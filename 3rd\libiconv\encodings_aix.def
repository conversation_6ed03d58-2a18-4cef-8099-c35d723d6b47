/* Copyright (C) 2000-2002, 2008 Free Software Foundation, Inc.
   This file is part of the GNU LIBICONV Library.

   The GNU LIBICONV Library is free software; you can redistribute it
   and/or modify it under the terms of the GNU Lesser General Public
   License as published by the Free Software Foundation; either version 2.1
   of the License, or (at your option) any later version.

   The GNU LIBICONV Library is distributed in the hope that it will be
   useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Lesser General Public License for more details.

   You should have received a copy of the GNU Lesser General Public
   License along with the GNU LIBICONV Library; see the file COPYING.LIB.
   If not, see <https://www.gnu.org/licenses/>.  */

/* Encodings used by system dependent locales on AIX. */

DEFENCODING(( "CP856",
            ),
            cp856,
            { cp856_mbtowc, NULL },       { cp856_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-856",                /* AIX */
            cp856)
#endif

DEFENCODING(( "CP922",
            ),
            cp922,
            { cp922_mbtowc, NULL },       { cp922_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-922",                /* AIX */
            cp922)
#endif

DEFENCODING(( "CP943",
            ),
            cp943,
            { cp943_mbtowc, NULL },       { cp943_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-943",                /* AIX */
            cp943)
#endif

DEFENCODING(( "CP1046",
            ),
            cp1046,
            { cp1046_mbtowc, NULL },      { cp1046_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-1046",               /* AIX */
            cp1046)
#endif

DEFENCODING(( "CP1124",
            ),
            cp1124,
            { cp1124_mbtowc, NULL },      { cp1124_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-1124",               /* AIX */
            cp1124)
#endif

DEFENCODING(( "CP1129",
            ),
            cp1129,
            { cp1129_mbtowc, NULL },      { cp1129_wctomb, NULL })
#ifdef USE_AIX_ALIASES
DEFALIAS(     "IBM-1129",               /* AIX */
            cp1129)
#endif

DEFENCODING(( "CP1161",
              "IBM1161",                /* glibc */
              "IBM-1161",               /* glibc */
              "csIBM1161",              /* glibc */
            ),
            cp1161,
            { cp1161_mbtowc, NULL },      { cp1161_wctomb, NULL })

DEFENCODING(( "CP1162",
              "IBM1162",                /* glibc */
              "IBM-1162",               /* glibc */
              "csIBM1162",              /* glibc */
            ),
            cp1162,
            { cp1162_mbtowc, NULL },      { cp1162_wctomb, NULL })

DEFENCODING(( "CP1163",
              "IBM1163",                /* glibc */
              "IBM-1163",               /* glibc */
              "csIBM1163",              /* glibc */
            ),
            cp1163,
            { cp1163_mbtowc, NULL },      { cp1163_wctomb, NULL })
