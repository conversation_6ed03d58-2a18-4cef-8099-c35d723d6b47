#include <stdio.h>
#include <string.h>
#include "common.h"
#include "buf.h"
#include "configfile.h"
#include "char_base.h"
#include "char_data.h"
#include "pet.h"
#include "util.h"
#include "jobs.h"
#include "skill.h"
#include "battle.h"
#include "battle_event.h"
#include "battle_command.h"
#include "battle_skill.h"
#include "item.h"
#include "language.h"
#include "tech.h"
#include "object.h"
#include "item_recipe.h"
#include "item_material.h"
#include "nrproto_serv.h"
#include "npcutil.h"
#include "tech_area.h"
#include "title.h"
#include "log.h"
#include "item_event.h"
#include "handletime.h"
#include "morph.h"
#include "npc_event.h"
#include "tech2.h"
#include "npc_lua.h"

#ifdef _FISHING_OPERATION
#include "fish_table.h"
#endif /* _FISHING_OPERATION */

#include "../puk2/puk2.h"

#ifdef MONSTER_HELP_TECH
#include "tech_pethelp.h"
#endif

#include "version.h"
#ifdef ANIT_CHEATING_SET
#include "db.h"		//用于封号
#endif

//#define TEST 0

// 胶懦 犬牢侩狼 哪颇老 胶拉摹
//#define	SKILL_TEST

#ifdef PUK2
// 歹固肺 倒妨林绰 NULL 器牢磐
const char DummyPointer2[1024] = {'\0'};
const char* DummyPointer = DummyPointer2;
#endif

#include "char_suit.h"
#include "../ext/itemExt.h"

/*========================================================================
 * 胶懦俊 措茄 家胶
 *========================================================================*/


static Tech	*TECH_tech;
static int  *TECH_techJumpTable;       // TECH_tech俊狼 抛捞喉.
static int   TECH_maxid;

static int		TECH_technum;

typedef struct tagTech_TechFunctionTable
{
	char			*functionname;		/*	汲沥 颇老俊 静绰 窃荐狼 捞抚 */
	TECH_CALLFUNC	func;				/*  角力肺 阂妨 啊绰 窃荐 */
	int				hash;				/*  hash */
	int				status;				/*  捞 目膏靛甫 局肯悼拱捞 瘤沥茄 钎 */
}TECH_TechFunctionTable;

/* 扁贱阑 疵府搁(磊) 咯扁俊 殿废窍绰 老 */
static TECH_TechFunctionTable TECH_functbl[] = {
	{ "TECH_None",				TECH_None,		0 },
	{ "TECH_NormalAttack",		TECH_NormalAttack,	0 },
	{ "TECH_NormalGuard",		TECH_NormalGuard,	0 },
	{ "TECH_ContinuationAttack", TECH_ContinuationAttack,	0 },
	{ "TECH_CrossCounter",		TECH_CrossCounter,	0 },
	{ "TECH_Parameter",			TECH_Parameter,	0 },
	{ "TECH_Taunt",			    TECH_Taunt,	0 },
	{ "TECH_SpiracleShot",		TECH_SpiracleShot,	0 },
	{ "TECH_Metamorphosis",		TECH_Metamorphosis,	0 },
	{ "TECH_Heal_Magic",		TECH_Heal_Magic,	0 },
	{ "TECH_Attack_Magic",		TECH_Attack_Magic,	0 },

	{ "TECH_Reflection_Physics", TECH_Reflection_Physics,	0 },
	{ "TECH_Absorb_Physics",	TECH_Absorb_Physics,	0 },
	{ "TECH_Ineffective_Physics", TECH_Ineffective_Physics,	0 },

	{ "TECH_Reflection_Magic",	TECH_Reflection_Magic,	0 },
	{ "TECH_Absorb_Magic",		TECH_Absorb_Magic,	0 },
	{ "TECH_Ineffective_Magic",	TECH_Ineffective_Magic,	0 },

	{ "TECH_BodyGuard",			TECH_BodyGuard,	0 },
	{ "TECH_Dorain",			TECH_Dorain,	0 },
	{ "TECH_LpRecovery",		TECH_LpRecovery, 0 },
	{ "TECH_StatusChange",		TECH_StatusChange , 0},
	{ "TECH_MergeItem",         TECH_MergeItem, 0},
	{ "TECH_JudgeItem",         TECH_JudgeItem, 0},
	{ "TECH_MarkItem",          TECH_MarkItem, 0},
	{ "TECH_RepairItem",        TECH_RepairItem, 0},
	{ "TECH_StatusRecover",     TECH_StatusRecover, 0},

	{ "TECH_NormalAttack",     	TECH_NormalAttack, 0},
	{ "TECH_NormalGuard",		TECH_NormalGuard, 0},
	{ "TECH_Revive",			TECH_Revive,	0},

	{ "TECH_Treat_Type",		TECH_Treat_Type,	0},
	{ "TECH_Reverse_Type",		TECH_Reverse_Type,	0},

	{ "TECH_SpecialGuard",		TECH_SpecialGuard,	0},
	{ "TECH_MagicGuard",		TECH_MagicGuard,	0},

	{ "TECH_Mikawashi",			TECH_Mikawashi,		0},
	{ "TECH_Consentration",		TECH_Consentration,	0},
	{ "TECH_Forcecut",			TECH_Forcecut,	0},

	{ "TECH_GuardBreak",		TECH_GuardBreak,	0 },
	{ "TECH_Steal",				TECH_Steal,			0 },
	{ "TECH_Allowance",			TECH_Allowance,			0 },
	{ "TECH_MedicalTreatment",	TECH_MedicalTreatment,	0 },

	{ "TECH_Death",				TECH_Death,	0 },
	{ "TECH_StatusAttack",		TECH_StatusAttack,	0 },
	{ "TECH_EquipBreakAttack",	TECH_EquipBreakAttack,	0 },
	{ "TECH_GoldAttack",		TECH_GoldAttack,	0 },
	{ "TECH_EnergyDrain",		TECH_EnergyDrain,	0 },
	{ "TECH_Bomb",				TECH_Bomb,	0 },
	{ "TECH_Sacrifice",			TECH_Sacrifice,	0 },

	{ "TECH_BloodAttack",		TECH_BloodAttack,	0 },
	{ "TECH_Summon",			TECH_Summon,	0 },

	{ "TECH_Position",			TECH_Position,	0 },

	{ "TECH_Lumb",			    TECH_Lumb,	0 },
	{ "TECH_Hunting",			TECH_Hunting,	0 },
	{ "TECH_Mining",			TECH_Mining,	0 },
#ifdef TECH_CUTTING
	/* 剪取技能  */
	{ "TECH_Cutting",			TECH_Cutting,	0 },
#endif
	{ "TECH_RandomShot",		TECH_RandomShot,	0 },

	{ "TECH_Assassin",			TECH_Assassin,	0 },
	{ "TECH_PanicAttack",		TECH_PanicAttack,	0 },
	{ "TECH_EarthQuake",		TECH_EarthQuake,	0 },
	{ "TECH_Dance",				TECH_Dance,		0 },

	{ "TECH_DefUp",				TECH_DefUp,		0 },
	{ "TECH_DefDown",			TECH_DefDown,	0 },

	{ "TECH_AtkUp",				TECH_AtkUp,		0 },
	{ "TECH_AtkDown",			TECH_AtkDown,	0 },

	{ "TECH_AglUp",				TECH_AglUp,		0 },
	{ "TECH_AglDown",			TECH_AglDown,	0 },
#ifdef	TECH_BLASTWAVE
//追月
	{ "TECH_BlastWave",			TECH_BlastWave,	0 },
#endif

//	{ "TECH_StatusResistance",	TECH_StatusResistance, 0},

#ifdef _FISHING_OPERATION
	{ "TECH_Fishing",			TECH_Fishing,		0 },
#endif /* _FISHING_OPERATION */

#ifdef _OPERATION_APPEND_JEWEL
	{ "TECH_AppendJewel",		TECH_AppendJewel,	0 },
#endif /* _OPERATION_APPEND_JEWEL */

#ifdef _TEST_TECH_YUK
	{ "TECH_Ranbu",		TECH_Ranbu,	0 },
	{ "TECH_AttackAll",	TECH_AttackAll, 0 },
	{ "TECH_DetectEnemy",	TECH_DetectEnemy,	0 },
	{ "TECH_UrgentAllowance",	TECH_UrgentAllowance,	0 },
	{ "TECH_UrgentMedic",	TECH_UrgentMedic,	0 },
	{ "TECH_IndirectStatusAttack",	TECH_IndirectStatusAttack,	0 },
	{ "TECH_AxeBomber",	TECH_AxeBomber,	0 },
	{ "TECH_UltimateAttack",	TECH_UltimateAttack,	0 },
	{ "TECH_PickPocket",	TECH_PickPocket,	0 },
	{ "TECH_RcvUp",	TECH_RcvUp,	0 },
	{ "TECH_Parrying",	TECH_Parrying,	0 },
#ifdef VERSION_TW
	//投掷取消主动释放
#else
	{ "TECH_ThrowItem",	TECH_ThrowItem,	0 },
#endif
	{ "TECH_Provocation", TECH_Provocation,	0 },
#endif /* _TEST_TECH_YUK */

#ifdef _OPERATION_REMAKE_ITEM
	{ "TECH_RemakeItem",	TECH_RemakeItem, 0 },
#endif /* _OPERATION_REMAKE_ITEM */

#if 0
	{ "TECH_ChargeAttack",		TECH_ChargeAttack,	0 },
	{ "TECH_Guardian",			TECH_Guardian,		0 },
	{ "TECH_PowerBalance",		TECH_PowerBalance,	0 },
	{ "TECH_Mighty",			TECH_Mighty,		0 },
	{ "TECH_EarthRound",		TECH_EarthRound,	0 },
	{ "TECH_Abduct",			TECH_Abduct,		0 },
	{ "TECH_Steal",				TECH_Steal,			0 },
	{ "TECH_Merge",				TECH_Merge,			0 },
	{ "TECH_NoGuard",			TECH_NoGuard,		0 },
#endif
#ifdef PUK2_SKILL
	{ "TECH_DelayAttack",		TECH_DelayAttack,	0 },
	{ "TECH_Wattack",			TECH_DoubleAttack,	0 },
	{ "TECH_FullBodyGuard",		TECH_FullBodyGuard,	0 },
	{ "TECH_FirstAttack",		TECH_FirstAttack,	0 },
	{ "TECH_Copy",				TECH_ShapeChange,	0 },
	{ "TECH_Madmax",			TECH_Retribution,	0 },
	{ "TECH_MadmaxEx",			TECH_RetributionEx,	0 },
	{ "TECH_Acid",				TECH_Acid,			0 },
	{ "TECH_MonWind",			TECH_MonWind,		0 },
	{ "TECH_Barrier",			TECH_Barrier,		0 },
#endif
#ifdef PET_RIDE
	{ "TECH_Rider",				TECH_Rider,			0 },
	{ "TECH_RiderPet",			TECH_RiderPet,		0 },
	{ "TECH_RiderBreak",		TECH_RiderBreak,	0 },
#endif
#ifdef MAGIC_MISSILE
	{ "TECH_MagicMissile",		TECH_MagicMissile,	0 },
#endif
#ifdef LEVEL_DOWN_SKILL
	{ "TECH_LvSet",			TECH_LevelDown,	0 },
	{ "TECH_LevelDown",			TECH_LevelDown,	0 },
#endif
    { "TECH_Lua",		TECH_Lua,	0 },
};
// 局肯悼拱 皋老狼 ID甸.
int TECH_PETMAIL_id_tbl[] = {
	TECH_ID_PETMAIL1,
	TECH_ID_PETMAIL2,
	TECH_ID_PETMAIL3,
	TECH_ID_PETMAIL4,
};
#ifdef PET_RIDE
RidePetData	*TECH_rideData;
int		TECH_rideDataNum;
#endif

#define C_STM	( ch->iu.player.Stamina )
#define C_DEX	( ch->iu.player.Dex )
#define C_INT	( ch->iu.player.Intelligence )

/*----------------------------------------------------------------------*/


/* 扁夯 眉农, 咀技胶 包拌 */
/*----------------------------------------------------------------------*/
INLINE BOOL TECH_CHECKINDEX( int index )
{
    if( TECH_technum<=index || index<0 ) return FALSE;
    return TRUE;
}
/*----------------------------------------------------------------------*/
static INLINE BOOL TECH_CHECKINTDATAINDEX( int index)
{
	if( TECH_DATAINTNUM <= index || index < 0 ) return FALSE;
	return TRUE;
}
/*----------------------------------------------------------------------*/
static INLINE BOOL TECH_CHECKCHARDATAINDEX( int index)
{
	if( TECH_DATACHARNUM <= index || index < 0 ) return FALSE;
	return TRUE;
}
/*----------------------------------------------------------------------*/
INLINE int TECH_getInt( int index, TECH_DATAINT element)
{
	if( TECH_CHECKINDEX(index) == FALSE ) return -1;
#if 1		// 馆靛矫 裹困 眉农窍档废(淀捞)
	if( TECH_CHECKINTDATAINDEX( element ) == FALSE ) return -1;
#endif
	return TECH_tech[index].intdata[element];
}
/*----------------------------------------------------------------------*/
INLINE int TECH_setInt( int index, TECH_DATAINT element, int data)
{
	int buf;
#if 1		// 馆靛矫 裹困 眉农窍档废(淀捞)
	if( TECH_CHECKINDEX( index ) == FALSE ) return -1;
	if( TECH_CHECKINTDATAINDEX( element ) == FALSE ) return -1;
#endif
	buf = TECH_tech[index].intdata[element];
	TECH_tech[index].intdata[element]=data;
	return buf;
}
/*----------------------------------------------------------------------*/
INLINE int TECH_getWorkInt( int index, TECH_WORKINT element)
{
#if 1		// 馆靛矫 裹困 眉农窍档废(淀捞)
	if( TECH_CHECKINDEX( index ) == FALSE ) return -1;
	if( element < 0 || element >= TECH_WORKINTDATANUM ) return -1;
#endif
	return TECH_tech[index].workintdata[element];
}
/*----------------------------------------------------------------------*/
INLINE int TECH_setWorkInt( int index, TECH_WORKINT element, int data)
{
	int buf;
#if 1		// 馆靛矫 裹困 眉农窍档废(淀捞)
	if( TECH_CHECKINDEX( index ) == FALSE ) return -1;
	if( element < 0 || element >= TECH_WORKINTDATANUM ) return -1;
#endif
	buf = TECH_tech[index].workintdata[element];
	TECH_tech[index].workintdata[element]=data;
	return buf;
}
/*----------------------------------------------------------------------*/
INLINE char* TECH_getChar( int index, TECH_DATACHAR element)
{
#ifdef PUK2
	if( ! TECH_CHECKINDEX( index)) return (char *) DummyPointer;
	if( ! TECH_CHECKCHARDATAINDEX( element)) return (char *) DummyPointer;
#else
	if( ! TECH_CHECKINDEX( index)) return NULL;
	if( ! TECH_CHECKCHARDATAINDEX( element)) return NULL;
#endif
	return TECH_tech[index].chardata[element].string;
}

/*----------------------------------------------------------------------*/
INLINE BOOL TECH_setChar( int index , TECH_DATACHAR element, char* new )
{
    if(! TECH_CHECKINDEX(index)) return FALSE;
    if(! TECH_CHECKCHARDATAINDEX(element)) return FALSE;
    STRCPY( TECH_tech[index].chardata[element].string, new );
    return TRUE;
}
/*----------------------------------------------------------------------
 * 局肯悼拱扁贱狼 荐甫 救促.
 *---------------------------------------------------------------------*/
int TECH_getTechNum( void)
{
	return TECH_technum;
}



// 眉农绰 1盒.
#define CHAR_OPECATECHECKTIME 60
// 11俺肺 酒眶
#define CHAR_OPECATECHECKCOUNT 11
//--------------------------------------------------------------------
//  坷欺饭捞记 剧侥狼 矫埃苞 雀荐肺 摹飘甫 眉农
//--------------------------------------------------------------------
int OpecateTimeCheck(
	Char *ch
){

	if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == TRUE ){
		// 叼滚芭绰 攫力唱 OK.
		return TRUE;
	}
#ifdef PUK2
	// 捞 眉农绰 秦力秦 滴绰 巴 鞍促(＇03.12/4)
	return TRUE;
#else
	// 眉农 矫埃焊促 傈狼 矫阿捞扼搁 檬扁拳促.
	if( ch->wu.player.OpecateFirstTime < NowTime.tv_sec - CHAR_OPECATECHECKTIME ){
		ch->wu.player.OpecateFirstTime = NowTime.tv_sec;
		ch->wu.player.OpecateCount = 1;
		// 贸澜篮 己傍.
		return TRUE;
	}

	// 墨款飘诀.
	ch->wu.player.OpecateCount ++;
	// 酒流 眉农 雀荐俊 给 固闷栏搁 亮促.
	if( ch->wu.player.OpecateCount < CHAR_OPECATECHECKCOUNT ){
		return TRUE;
	}

	// 瘤沥 矫埃 捞郴俊 11俺 捞惑 秒沁栏骨肺 捞惑窍促.
	print( "CHEAT!! Opecate Limit %s %s\n", ch->cu.player.CdKey, ch->c.Name );

	// 冻绢哆赴促搁 冻绢哆赴促.
	if( getUnFairUserDownFlg() == 1 ){
		Connect[ch->wu.player.Fd].closed = 1;
	}

	return FALSE;
#endif
}


//-------------------------------------------------------------------------
// 局肯悼拱 皋老狼 可记阑 欺胶 茄促.
//-------------------------------------------------------------------------
void TECH_PETMAIL_parseOption( int techindex)
{
	char buf[256];
	char *p;
	int ret;
	int value;
	if( ! TECH_CHECKINDEX( techindex)) {
		eprint( "err\n");
		return;
	}
	TECH_setWorkInt( techindex, TECH_PETMAIL_SPEED, 600);
	TECH_setWorkInt( techindex, TECH_PETMAIL_DROP, 1);
	TECH_setWorkInt( techindex, TECH_PETMAIL_MISTAKE, 1);
	p = TECH_getChar( techindex, TECH_OPTION);
	if( p == NULL ) return;
	ret = getStringFromIndexWithDelim( p,"|", 1, buf, sizeof( buf));
	if( ! ret ) return;
	value = atoi( buf);
	if( value < 0 ) value = 0;
	TECH_setWorkInt( techindex, TECH_PETMAIL_SPEED, value);

	ret = getStringFromIndexWithDelim( p,"|", 2, buf, sizeof( buf));
	if( ! ret ) return;
	value = atoi( buf);
	if( value < 0 ) value = 0;
	TECH_setWorkInt( techindex, TECH_PETMAIL_DROP, value);

	ret = getStringFromIndexWithDelim( p,"|", 2, buf, sizeof( buf));
	if( ! ret ) return;
	value = atoi( buf);
	if( value < 0 ) value = 0;
	TECH_setWorkInt( techindex, TECH_PETMAIL_MISTAKE, value);
}
//-------------------------------------------------------------------------
// 瘤沥等 扁贱 ID啊 局肯悼拱 皋老狼 辆幅牢啊 绢恫啊甫 炼荤茄促.
//-------------------------------------------------------------------------
BOOL TECH_PETMAIL_checkId( int id)
{
	int i;
	for( i = 0; i < arraysizeof( TECH_PETMAIL_id_tbl); i ++ ) {
		if( id == TECH_PETMAIL_id_tbl[i]) return TRUE;
	}
	return FALSE;
}
/*----------------------------------------------------------------------
 * 扁贱狼 汲沥 颇老阑 佬绰促
 *---------------------------------------------------------------------*/
BOOL TECH_initTech( char *filename)
{
    FILE*   f;
    char    line[256];
    int     linenum=0;
    int     tech_readlen=0;
	int		i, j;
	int jobsnum;

	TECH_maxid = 0;

	/* hash 狼 殿废 */
	for( i = 0; i < arraysizeof( TECH_functbl); i ++ ) {
		TECH_functbl[i].hash = hashpjw( TECH_functbl[i].functionname);
	}

    f = fopen(filename,"rb");
    if( f == NULL ){
        print( "file open error\n");
        return FALSE;
    }

    TECH_technum=0;

    /*  快急 蜡瓤茄 青捞 割青 乐阑瘤 绢冻瘤 炼荤茄促    */
    while( fgets( line, sizeof( line ), f ) ){
        linenum ++;
        if( line[0] == '#' ) continue;        /* comment */
        if( line[0] == '\n' ) continue;       /* none    */
        chomp( line );

        TECH_technum++;
    }

    if( fseek( f, 0, SEEK_SET ) == -1 ){
        eprint( "Seek Error\n" );
        fclose(f);
        return FALSE;
    }

    TECH_tech = allocateMemory( sizeof(struct tagTech)
                                   * TECH_technum );
    if( TECH_tech == NULL ){
    	// "皋葛府甫 犬焊 且 荐 绝菌嚼聪促. %d\n"
        print_tr(LANG_MSG_CHAR_SKILL_C_001 ,
                 (int)sizeof(struct tagTech) *TECH_technum);
        fclose( f );
        return FALSE;
    }
	jobsnum = JOBS_getJobNum();

	/* 檬扁拳 */
    for( i = 0; i < TECH_technum; i ++ ) {
    	for( j = 0; j < TECH_DATAINTNUM; j ++ ) {
    		TECH_setInt( i, j,-1);
    	}
    	for( j = 0; j < TECH_DATACHARNUM; j ++ ) {
    		TECH_setChar( i, j,"");
    	}
    	for( j = 0; j < TECH_WORKINTDATANUM; j ++ ) {
    		TECH_setWorkInt( i, j,-1);
    	}
		TECH_tech[i].skillindex = -1;
		TECH_tech[i].func = NULL;
    }

    /*  肚 促矫 佬绰促    */
    linenum = 0;
    while( fgets( line, sizeof( line ), f ) ){
        linenum ++;
        if( line[0] == '#' ) continue;        /* comment */
        if( line[0] == '\n' ) continue;       /* none    */
        chomp( line );
{
        char    token[256];
        int     ret;
		for( i = 0; i < TECH_DATACHARNUM; i ++ ) {

	        /*  某腐磐 扼牢侩 配奴阑 夯促    */
	        ret = getStringFromIndexWithDelim( line, CSV_DELIM,
	        									i + 1,
	        									token, sizeof(token));
	        if( ret==FALSE ){
				// "单捞磐狼 亲格捞 面盒窍瘤 臼疽嚼聪促.file:%s line:%d\n"
				print_warn();
				print_tr(LANG_MSG_CHAR_SKILL_C_002, filename, linenum);
	            break;
	        }
	        TECH_setChar( tech_readlen, i, token);
		}
        if( i < TECH_DATACHARNUM ) continue;
        /* 荐摹 单捞磐 */
//#define	TECH_STARTINTNUM		(TECH_DATACHARNUM+1)
        for( /*i = TECH_STARTINTNUM*/; i < TECH_DATACHARNUM + TECH_DATAINTNUM; i ++ ) {
            ret = getStringFromIndexWithDelim( line, CSV_DELIM, i+1, token,
                                               sizeof(token));
            if( ret==FALSE ){
				// "单捞磐狼 亲格捞 面盒窍瘤 臼疽嚼聪促.file:%s line:%d\n"
                print_tr(LANG_MSG_CHAR_SKILL_C_002, filename, linenum);
                break;
            }
            if( strlen( token) != 0 ) {
	            TECH_setInt( tech_readlen, i - TECH_DATACHARNUM, atoi( token));
			}
			// 力老 奴 ID牢啊 绢恫啊 炼荤茄促.
			if( i == TECH_DATACHARNUM+TECH_ID) {
				if( atoi( token) > TECH_maxid ) TECH_maxid = atoi( token);
			}
        }
        if( i < TECH_DATACHARNUM + TECH_DATAINTNUM ) continue;
		// 捞 扁贱捞 加窍绰 胶懦狼 梅磊甫 秒垫茄促.
		TECH_tech[tech_readlen].skillindex =
			SKILL_getSkillIndex( TECH_getInt( tech_readlen, TECH_SKILLID));
		// 捞 扁贱阑 贸府窍绰 窃荐俊狼 器牢磐甫 秒垫茄促.
		TECH_tech[tech_readlen].func = TECH_getTechFuncPointer(
                           			TECH_getChar( tech_readlen, TECH_FUNCNAME));

        tech_readlen ++;
}
    }
    fclose(f);

    TECH_technum = tech_readlen;
	if( TECH_maxid <= 0 ) {
		eprint( "id error maxid=%d\n", TECH_maxid);
	}
	else {
		// 痢橇 抛捞喉狼 累己.
		// TECH_ID肺何磐 梅磊甫 梆官肺 秒垫且 荐 乐档废(淀捞) 茄促.
		TECH_maxid++;
		TECH_techJumpTable = allocateMemory( sizeof( int) *TECH_maxid);
		if( TECH_techJumpTable == NULL ) {
			// "皋葛府甫 犬焊 且 荐 绝菌嚼聪促. %d\n"
			print_tr(LANG_MSG_CHAR_SKILL_C_001 ,
                     (int)sizeof(int) *TECH_maxid);
			freeMemory( TECH_tech);
			return FALSE;
		}
		for( i = 0; i < TECH_maxid; i ++ ) {
			TECH_techJumpTable[i] = -1;
		}
		for( i = 0; i < TECH_technum; i ++ ) {
			TECH_techJumpTable[TECH_tech[i].intdata[TECH_ID]] = i;
		}
	}

    //print( "Valid tech Num is %d\n", TECH_technum );
    print_t(LANG_MSG_TECH_C_201, TECH_technum );




#if 0
    for( i=0; i <TECH_technum ; i++ ){

        for( j = 0; j < TECH_DATACHARNUM; j ++ ) {
	        print( "%s ", TECH_getChar( i, j));
		}
		print( "\n");
        for( j = 0; j < TECH_DATAINTNUM; j ++ ) {
            print( "%d ", TECH_getInt( i, j));
        }
        print( "\n-------------------------------------------------\n");
    }
#endif
	for( i = 0; i < TECH_technum; i ++ ) {
		// 局肯悼拱 皋老狼 可记 欺胶 贸府
		if( TECH_PETMAIL_checkId( TECH_getInt( i, TECH_ID))) {
			TECH_PETMAIL_parseOption( i);
			/*
			print( "tech petmail work\n");
			print( "speed = %d ", TECH_getWorkInt( i, TECH_PETMAIL_SPEED));
			print( "drop = %d ", TECH_getWorkInt( i, TECH_PETMAIL_DROP));
			print( "mistake = %d\n", TECH_getWorkInt( i, TECH_PETMAIL_MISTAKE));
			*/
			print_t(LANG_MSG_TECH_C_202);
			print_t(LANG_MSG_TECH_C_203, TECH_getWorkInt( i, TECH_PETMAIL_SPEED));
			print_t(LANG_MSG_TECH_C_204, TECH_getWorkInt( i, TECH_PETMAIL_DROP));
			print_t(LANG_MSG_TECH_C_205, TECH_getWorkInt( i, TECH_PETMAIL_MISTAKE));

#if 1
#endif
		}
	}
    return TRUE;
}
#ifdef PET_RIDE
/*----------------------------------------------------------------------
 * 愁捞 扁备狼 汲沥 颇老阑 佬绢甸牢促
 *---------------------------------------------------------------------*/
BOOL TECH_initRide( char *filename )
{
    FILE*	f;
    char	line[256];
    int		linenum=0;
    int		readnum=0;

    f = fopen(filename,"rb");
    if( f == NULL ){
        print( "file open error\n");
        return FALSE;
    }

    /*  快急 蜡瓤茄 青捞 割青 乐阑瘤 绢冻瘤 炼荤茄促    */
    TECH_rideDataNum = 0;
    while( fgets( line, sizeof( line ), f ) ){
        linenum ++;
        if( line[0] == '#' )continue;        /* comment */
        if( line[0] == '\n' )continue;       /* none    */
        chomp( line );

        TECH_rideDataNum++;
    }

    if( fseek( f, 0, SEEK_SET ) == -1 ){
        eprint( "Seek Error\n" );
        fclose(f);
        return FALSE;
    }

	// 皋葛府 犬焊
    TECH_rideData = allocateMemory( sizeof( RidePetData ) * TECH_rideDataNum );
    if( TECH_rideData == NULL ){
    	// "皋葛府甫 犬焊 且 荐 绝菌嚼聪促. %d\n"
        print_tr(LANG_MSG_CHAR_SKILL_C_001 ,
                 (int)sizeof(struct tagTech)*TECH_technum);
        fclose( f );
        return FALSE;
    }

    /*  肚 促矫 佬绰促    */
    linenum = 0;
    while( fgets( line, sizeof( line ), f ) ){
        linenum ++;
        if( line[0] == '#' )continue;        /* comment */
        if( line[0] == '\n' )continue;       /* none    */
        chomp( line );
		{
        	char    token[256];
        	int     ret;

	        /* 弊贰侨 锅龋客 捞悼 加档甫 佬绢甸牢促 */
			// 弊贰侨 锅龋
	        ret = getStringFromIndexWithDelim( line, CSV_DELIM, 1,
	        								   token, sizeof( token ) );
	        if( ret == FALSE ){
				// "单捞磐狼 亲格捞 面盒窍瘤 臼疽嚼聪促.file:%s line:%d\n"
	            print_tr(LANG_MSG_CHAR_SKILL_C_002, filename, linenum );
	            continue;
	        }
			TECH_rideData[readnum].grano = atoi( token );
			// 捞悼 加档
	        ret = getStringFromIndexWithDelim( line, CSV_DELIM, 2,
	        								   token, sizeof( token ) );
	        if( ret == FALSE ){
				// "单捞磐狼 亲格捞 面盒窍瘤 臼疽嚼聪促.file:%s line:%d\n"
				print_warn();
				print_tr(LANG_MSG_CHAR_SKILL_C_002, filename, linenum );
	            continue;
	        }
			TECH_rideData[readnum].speed = atoi( token );
			// 弥绊加档
	        ret = getStringFromIndexWithDelim( line, CSV_DELIM, 3,
	        								   token, sizeof( token ) );
	        if( ret == FALSE ){
				// "单捞磐狼 亲格捞 面盒窍瘤 臼疽嚼聪促.file:%s line:%d\n"
	            print_tr(LANG_MSG_CHAR_SKILL_C_002, filename, linenum );
	            continue;
	        }
			TECH_rideData[readnum].maxspeed = atoi( token );

			readnum++;
		}
    }
    fclose(f);

    return TRUE;
}
#endif
/*------------------------------------------------------------------------
 * Tech狼 汲沥 颇老 促矫 佬绢
 *-----------------------------------------------------------------------*/
BOOL TECH_reinitTech( void )
{
	freeMemory( TECH_tech);
	return( TECH_initTech( config.techfile));
}
/*------------------------------------------------------------------------
 * TECH_ID(栏)肺何磐 梅磊甫 酒绰 窃荐
 * 倒酒啊 蔼
 * 己傍: 梅磊
 * 角菩: -1
 *-----------------------------------------------------------------------*/
int TECH_getTechIndex( int techid)
{
#if 1  // 痢橇 抛捞喉阑 荤侩茄 规侥
	if( techid < 0 || techid >= TECH_maxid ) return -1;
	return TECH_techJumpTable[techid];

#else  // 嘲篮 规侥
	int		i;
	for( i = 0; i < TECH_technum; i ++ ) {
		if( TECH_tech[i].intdata[TECH_ID] == techid ) {
			return i;
		}
	}
	return -1;
#endif
}
/*------------------------------------------------------------------------
 * 扁贱狼 梅磊肺何磐, 加窍绰 胶懦狼 梅磊甫 掘绰 窃荐
 * 倒酒啊 蔼
 * 己傍: 梅磊
 * 角菩: -1
 *-----------------------------------------------------------------------*/
int TECH_getSkillIndex( int techindex)
{
	if( ! TECH_CHECKINDEX(techindex)) return -1;
	return TECH_tech[techindex].skillindex;
}
/*------------------------------------------------------------
 * 扁贱狼 窃荐疙栏肺何磐 pointer甫 倒妨霖促
 * 牢荐
 *  name        char*       林贱狼 捞抚
 * 倒酒啊 蔼
 *  窃荐俊狼 器牢磐.绝绰 版快俊绰 NULL
 ------------------------------------------------------------*/
TECH_CALLFUNC TECH_getTechFuncPointer(char* name )
{
    int i;
    int hash = hashpjw( name );
    for( i = 0 ; i< arraysizeof( TECH_functbl) ; i++ ) {
        if( TECH_functbl[i].hash == hash ) {
        	if( strcmp( TECH_functbl[i].functionname, name ) == 0 )  {
	            return TECH_functbl[i].func;
			}
		}
	}
    return NULL;
}
/*------------------------------------------------------------
 * 扁贱狼 梅磊肺何磐 pointer甫 倒妨霖促
 * 牢荐
 *  techindex        int       扁贱狼 梅磊
 * 倒酒啊 蔼
 *  窃荐俊狼 器牢磐.绝绰 版快俊绰 NULL
 ------------------------------------------------------------*/
TECH_CALLFUNC TECH_getTechFuncPointerFromTechIndex( int techindex)
{
	if( ! TECH_CHECKINDEX( techindex)) return NULL;
	return TECH_tech[techindex].func;
}
#if 0
//-------------------------------------------------------------------------
// 秒垫窍绰 扁贱 版氰摹甫 拌魂茄促.
// 牢荐：
// ch         Char *    某腐磐 pointer
// skillarray int       胶懦狼 硅凯狼 梅磊
//-------------------------------------------------------------------------
int TECH_getTechExp( Char *ch, int techindex)
{
	int exp;
	// 拌魂侥 家厚 FP /10 * player lv * skilldifficulty * rank * rand( 0.95 - 1.05)

	exp = TECH_getInt( techindex, TECH_FORCEPOINT)
		/ 10
		* ch->i.Lv
		* (SKILL_getInt( TECH_getSkillIndex( techindex), SKILL_DIFFICULTY)/100.0)
		* 1.0 // 珐农
		* (RAND( 95,105) / 100.0);
	if( exp < 0 ) exp = 0;

	return exp;
}

//-------------------------------------------------------------------------
// 秒垫窍绰 扁贱 版氰摹甫 拌魂茄促.(傈捧 胶懦侩)
// 牢荐：
// ch         Char *    某腐磐 pointer
// skillarray int       胶懦狼 硅凯狼 梅磊
//-------------------------------------------------------------------------
int TECH_getBattleTechExp( Char *ch, int techindex, int mode)
{
	float  exp=0;
//	int eexp=500;
	float force = ((float) TECH_getInt( techindex, TECH_FORCEPOINT)/200);
	float force2 = (float) TECH_getInt( techindex, TECH_FORCEPOINT);
	float modskill = (float) SKILL_getInt(
						 TECH_getSkillIndex( techindex), SKILL_DIFFICULTY);


	//荤角篮 100硅 登绊 乐绰 葛剧捞瘤父 瘤陛篮 酒流
//	modskill= modskill /100;

	switch(mode){
	case BATTLE_SKILLEXP_NORMAL:
//		print("fp=%f skill=%f ran=%f ",
//		force,
//		modskill,
//		(float)(RAND( 95,105) / 100.0)
//		);

		// 拌魂侥 家厚 FP /200 * EnemyEXP * skilldifficulty * rand( 0.95 - 1.05)
		exp = force  * modskill
			* ((float) RAND( 95,105) / 100.0)
			* 1000;

		if( exp < 1000 ) exp = 0;

	break;

	case BATTLE_SKILLEXP_MAGIC:
		// 拌魂侥 家厚 FP /200 * EnemyEXP * skilldifficulty * rand( 0.95 - 1.05)
		exp =(float) force2 * (float) TECH_getInt(techindex, TECH_NECESSARYLV) / 20
			* modskill * (float)(RAND( 95,105) / 100.0);

		if( exp < 1 ) exp = 1;
	break;

	case 3:
		// 拌魂侥 家厚 FP /200 * EnemyEXP * skilldifficulty * rand( 0.95 - 1.05)
		exp =(float)(SKILL_getInt( TECH_getSkillIndex( techindex), SKILL_DIFFICULTY)/100.0)
			* (float)(RAND( 95,105) / 100.0)
			*1000;

		if( exp < 0 ) exp = 0;

	break;


	}

	return exp;
}
#endif

/*
 * 角力肺 扁贱捞 矫青登绰 巴阑 敬促
 */
/*----------------------------------------------------------------------
 *  扁贱阑 荤侩茄促.
 * 牢荐
 * ch				Char *  磊脚狼 某腐磐 pointer
 * jobindex			int		流诀(夯诀捞唱 何诀牢啊)
 * haveskill		int		割锅掳狼 胶懦阑 荤侩沁绰瘤
 * havetech			int		割锅掳狼 扁贱阑 荤侩沁绰瘤
 * toindex			int		穿备俊霸.
 * data				char *  何啊 单捞磐
 *---------------------------------------------------------------------*/
int TECH_Use(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	char *data
)
{
	int		array, techid;
	int		ret;
	TECH_CALLFUNC func;
#ifdef NEW_JOB_CHANGE
	int		techindex;
	int		skillindex;
	int		usablelv;
#endif

#ifdef PUK2
	// 胶懦裹困 锅龋啊 100阑 逞绊 乐阑 锭绰 府滚胶 荤侩
	ch->w.RebirthSkill = 0;
#if 1


#ifdef MONSTER_HELP_TECH
	ch->wu.player.PetHelpSkill =FALSE ;
	if( haveskill >= 200 )
	{
		haveskill %= 200;
		ch->wu.player.PetHelpSkill=TRUE;
	}
	else if( haveskill >= 100 )
	{
		haveskill %= 100;
		if( ch->w.RebirthLevel )
		{
			ch->w.RebirthSkill++;
		}
	}
#else
	if( haveskill >= 100 ){
		haveskill %= 100;
		if( ch->w.RebirthLevel ){
			ch->w.RebirthSkill++;
		}
	}
#endif //end of MONSTER_HELP_TECH


#else
	// 快急, 府滚胶 且 荐 乐促搁 馆靛矫 府滚胶
	// (抛胶飘侩)
		haveskill %= 100;
		if( ch->w.RebirthLevel ){
			ch->w.RebirthSkill++;
		}
#endif
#endif
	/* techindex 甫 掘绰促 */
	/* jobindex, haveskill狼 眉农绰 CHAR_getTechId() 吝(救)俊辑 窍绊 乐促 */
	techid = CHAR_getTechId( ch, haveskill, havetech);
	if( techid == -1 ) return FALSE;
	/* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
	array = TECH_getTechIndex( techid);
	if( array == -1 ) return FALSE;

#ifdef NEW_JOB_CHANGE	// 悼累捞 烹捞扼搁 PUK2(盒)祈俊档 馆康窃 促捧绢扼
	// 敲饭捞绢父 眉农
	if( ch->i.WhichType == CHAR_TYPEPLAYER ){
		// 荤侩且 荐 绝绰 珐农甫 荤侩窍绊 乐瘤 臼绰啊?
		techindex = TECH_getTechIndex( techid );
		skillindex = TECH_getSkillIndex( techindex );
		usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
		// 荤侩 啊瓷 饭骇阑 逞绊 乐阑鳖?
		if( TECH_getInt( techindex, TECH_NECESSARYLV ) > usablelv ){
			// 瘤陛狼 老磊府肺绰 荤侩且 荐 绝绰 珐农
			return FALSE;
		}
	}
#endif

#ifdef SUIT_SYSTEM	
	if(BattleSuitCommand(ch, techid, toindex) == FALSE)
	{
#endif
		func = TECH_getTechFuncPointerFromTechIndex( array);

		/* 窃荐肺 抄促 */
		if( func ) {
			ret = func( ch,  haveskill, havetech, toindex,  array, data );
		}
		else {
			ret = FALSE;
		}
		return ret;
#ifdef SUIT_SYSTEM	
	}
	return TRUE;
#endif
}

//*******************************************************
// 籍魄-- 措扁(酒公巴档 窍瘤 臼绰促)
//
int TECH_None(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data

)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_NONE,
						 toindex, -1 );
	return TRUE;
}

//*******************************************************
// 胶懦：拱府 馆荤
//
int TECH_Reflection_Physics(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_REFLECTION_PHYSICS,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 胶懦：拱府 软荐
//
int TECH_Absorb_Physics(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_ABSORB_PHYSICS,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 胶懦：拱府 公瓤
//
int TECH_Ineffective_Physics(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_INEFFECTIVE_PHYSICS,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}


//*******************************************************
// 胶懦：付过 馆荤
//
int TECH_Reflection_Magic(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_REFLECTION_MAGIC,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 胶懦：付过 软荐
//
int TECH_Absorb_Magic(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_ABSORB_MAGIC,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 胶懦：付过 公瓤
//
int TECH_Ineffective_Magic(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_INEFFECTIVE_MAGIC,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}




//*******************************************************
// 胶懦：雀汗拌
//
int TECH_Heal_Magic(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_HEAL,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}



//*******************************************************
// 扁贱：傍拜 付贱
//
int TECH_Attack_Magic(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_MAGIC,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：楷加 傍拜(敲饭捞绢)
//
int TECH_ContinuationAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_RENZOKU,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：农肺胶 墨款磐(敲饭捞绢)
//
int TECH_CrossCounter(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{


	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_CROSSCOUNTER,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：颇扼固磐拌(敲饭捞绢)
//
int TECH_Parameter(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_PARAMETER,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

int TECH_Taunt(
        Char *ch,
        int haveskill,
        int havetech,
        int toindex,
        int array,
        char *data
)
//
//*******************************************************
{
    //目膏靛甫 汲沥茄促
    BATTLE_ActionSelect(ch, BATTLE_COM_TAUNT, toindex, TECH_getInt(array, TECH_ID));
    return TRUE;
}

//*******************************************************
// 扁贱：扁傍藕
//
int TECH_SpiracleShot(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_SPIRACLESHOT,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

#ifdef	TECH_BLASTWAVE
//*******************************************************
// 追月
//
int TECH_BlastWave(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_BLASTWAVE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
#endif

//*******************************************************
// 扁贱：胶其既 啊捞靛
//
int TECH_SpecialGuard(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_SPECIALGARD,
						 toindex, TECH_getInt( array, TECH_ID) );

	return TRUE;
}

//*******************************************************
// 扁贱：概流 啊捞靛
//
int TECH_MagicGuard(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_MAGICGARD,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：固墨唱
//
int TECH_Mikawashi(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_DODGE,
						 toindex, TECH_getInt( array, TECH_ID) );

	return TRUE;
}





//*******************************************************
// 扁贱：龋困
//
int TECH_BodyGuard(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_BODYGUARD,
						 toindex, TECH_getInt( array, TECH_ID) );


	return TRUE;
}

//*******************************************************
// 扁贱：靛饭牢
//
int TECH_Dorain(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_DORAIN,
						 toindex, TECH_getInt( array, TECH_ID) );


	return TRUE;
}

//*******************************************************
// 扁贱：LP犁积 付过
//
int TECH_LpRecovery(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_LP_RECOVERY,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：胶抛捞磐胶 捞惑 傍拜
//
int TECH_StatusChange(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_STATUSCHANGE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}


//*******************************************************
// 扁贱：胶抛捞磐胶 捞惑 雀汗
//
int TECH_StatusRecover(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_STATUSRECOVER,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：胶抛捞磐胶 捞惑 雀汗
//
int TECH_Revive(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_REVIVE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：加己 快措
//
int TECH_Treat_Type(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_TREAT_TYPE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：加己 馆傈
//
int TECH_Reverse_Type(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_REVERSE_TYPE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：沥脚 笼吝
//
int TECH_Consentration(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_CONSENTRATION,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：器胶 钠
//
int TECH_Forcecut(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_FORCECUT,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：啊捞靛 宏饭捞农
//
int TECH_GuardBreak(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_GUARDBREAK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：DEFUP
//
int TECH_DefUp(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_DEFUP,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}
//*******************************************************
// 扁贱：DEFDOWN
//
int TECH_DefDown(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_DEFDOWN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：ATKUP
//
int TECH_AtkUp(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_ATKUP,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：ATKDOWN
//
int TECH_AtkDown(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_ATKDOWN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：AGLUP
//
int TECH_AglUp(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_AGLUP,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：AGLDOWN
//
int TECH_AglDown(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_AGLDOWN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}






//*******************************************************
// 扁贱：壬模促
//
int TECH_Steal(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_STEAL,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：溜荤
//
int TECH_Death(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_DEATH,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：胶抛捞磐胶 捞惑 傍拜
//
int TECH_StatusAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_STATUSATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：公扁 颇鲍 傍拜
//
int TECH_EquipBreakAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *dataITEMSET_MAXREMAIN
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_BREAKATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：榜靛 傍拜
//
int TECH_GoldAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_GOLDATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：俊呈瘤 靛饭牢
//
int TECH_EnergyDrain(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_ENERGYDRAIN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：磊气
//
int TECH_Bomb(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_BOMB,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：魂 力拱阑 官魔
//
int TECH_Sacrifice(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_SACRIFICE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：软趋 傍拜
//
int TECH_BloodAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_BLOODATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：家券
//
int TECH_Summon(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_SUMMON,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：背鄂 傍拜
//
int TECH_PanicAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_PANIC,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：去鄂荤
//
int TECH_RandomShot(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_RANDOMSHOT,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：鞠混
//
int TECH_Assassin(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_ASSASSIN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}

//*******************************************************
// 扁贱：酒胶农俊捞农
//
int TECH_EarthQuake(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_EARTHQUAKE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}


//*******************************************************
// 扁贱：勉
//
int TECH_Dance(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_P_DANCE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}




//*******************************************************
// 扁贱：器瘤记 捞悼
//
int TECH_Position(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_POSITION,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}





//*******************************************************
// 扁贱：烹惑 规绢
//
int TECH_NormalAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_ATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}


//*******************************************************
// 扁贱：烹惑 规绢
//
int TECH_NormalGuard(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_GUARD,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}


//******************************************************
//扁贱：炼背
//馆券蔼：柳沥茄 胶懦 家厚樊
//******************************************************
int TECH_Train( Char *pch)
{
	Char *ch;	//敲饭捞绢狼 器牢磐
	int techindex;
	char *option, *p;
	int  work = 1000;
	int i , j;
	int techId;
#ifdef NEW_JOB_CHANGE
	int		skillindex;
	int		usablelv;
#endif

	if(pch->i.WhichType != CHAR_TYPEPET) return 1000;

	ch = (Char *) pch->wu.pet.PlayerChar;

	//林牢狼 器牢磐绰 沥惑利牢啊?
	if( CHAR_CheckCharPointer( ch ) == FALSE) return 1000;

	//炼背狼 胶懦阑 啊瘤绊 乐阑鳖?
	//胶懦狼 厘家甫 夯促
    for (i = 0; i < CHAR_MAXSKILLHAVE; i++) {
        if (ch->player_addon->Skill[i].SkillId == BATTLE_PARTICULAR_SKILL_TRAIN) {
            break;
        }
    }

	if(i == CHAR_MAXSKILLHAVE) return 1000;

#ifdef NEW_JOB_CHANGE
	skillindex = SKILL_getSkillIndex( BATTLE_PARTICULAR_SKILL_TRAIN );
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
#endif

	//啊瘤绊 乐带, 扁贱狼 饭骇篮 倔付牢啊?
    for (j = CHAR_MAXTECHHAVE - 1; j > -1; j--) {//鞘夸 绝绢狼 巴老鳖 例措 0老瘤档
		techId = CHAR_getTechId( ch, i, j);
		if( techId == -1 ) continue;

		/* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
		techindex = TECH_getTechIndex( techId);
//		print("teckId=%d \n", techId );
		//格窍 乐绊 犬牢
        if (TECH_getInt(techindex, TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_TRAIN) continue;

        if (TECH_TechLevelCheck(ch, TECH_getInt(techindex, TECH_NECESSARYLV)) == 0) continue;
#ifdef NEW_JOB_CHANGE
        // 荤侩且 荐 乐绰 饭骇狼 扁贱捞唱 眉农
        if (TECH_getInt(techindex, TECH_NECESSARYLV) > usablelv) {
            // 荤侩且 荐 绝促
            continue;
        }
#endif

		option =  TECH_getChar( TECH_getTechIndex( techId), TECH_OPTION);
			//豪牢
		p = strstr(option, "AR:");
        if (p != NULL) {
            sscanf(p + 3, "%d", &work);
            work = CHARSUIT_getOptionValue(ch, techindex, "AR:", work);
            break;
        }
	}

	//FP家厚啦
//	work = work * 0.001

//	work  = (float) nowfp * work;

	return work;
}

#ifdef PUK2_SKILL
//*******************************************************
// 扁贱：拌背拌炼(青悼 词眠绢 困扁 诀)
//
int TECH_DelayAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//
#ifdef _DEBUG
	print( "\nDelay Attack!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_DELAYATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
//*******************************************************
// 扁贱：老籍捞炼
//
int TECH_DoubleAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//
#ifdef _DEBUG
	print( "\nDouble Attack!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_BILLIARD,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
//*******************************************************
// 扁贱：扁荤狼 抗
//
int TECH_FullBodyGuard(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{

	//
#ifdef _DEBUG
	print( "\nKnight Guard!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_KNIGHTGUARD,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
//*******************************************************
// 扁贱：脚加 苞窜(绊加 傍拜, 饶 1雀 绒老)
//
int TECH_FirstAttack(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nFirst Attack!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_FIRSTATTACK,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
//*******************************************************
// 扁贱：剧滴 俺绊扁(酒焙 某腐磐肺 函窍绰, 颇扼固磐甫 墨乔)
//
int TECH_ShapeChange(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nShape Change!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_COPY,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}
//*******************************************************
// 扁贱：牢苞览焊(何皋尔 傈侩, 磊脚俊霸档 单固瘤)
//
int TECH_Retribution(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nMad Max!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_RETRIBUTION,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

int TECH_RetributionEx(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_RETRIBUTION_EX,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;
}

//*******************************************************
// 扁贱：魂己厚
//
int TECH_Acid(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nAcid Rain!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_ACIDRAIN,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}
//*******************************************************
// 扁贱：倒浅
//
int TECH_MonWind(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nMonster Wind!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_TORNADE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}
//*******************************************************
// 扁贱：胶懦 官府绢
//
int TECH_Barrier(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nSkill Barrier!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	//目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_TORNADE,
						 toindex, TECH_getInt( array, TECH_ID) );
	return TRUE;

}
#endif
#ifdef PET_RIDE
//*******************************************************
// 扁贱：扼捞歹
//
int TECH_Rider(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nRider!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	// 傈捧吝?
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE ){
		// 傈捧吝篮 目膏靛 汲沥
		BATTLE_ActionSelect( ch, BATTLE_COM_RIDER,
							 toindex, TECH_getInt( array, TECH_ID ) );
	}else{
		int		techid = CHAR_getTechId( ch, haveskill, havetech );

		// 烹惑矫绰 局肯悼拱俊霸 铰窍瞒
		// 国结 愁捞 扁备 窍绊 乐绢?
		if( ch->w.walk.ridePet != -1 ){
			// 愁捞 扁备 辆丰
			PET_stopRidePet( ch );
		}else{
			int		fp;
			int		petindex = ch->iu.player.DefaultPet;

			/* 愁捞 扁备 胶懦阑 荤侩且 荐 乐阑瘤 绢冻瘤甫 眉农 */
			// 傈捧侩狼 窃荐甫 荤侩秦 家厚 FP甫 夸备茄促
			ch->w.BattleCom3 = techid;
			fp = BATTLE_SKILL_UseFp( ch );
			// FP甫 眉农
			if( ch->i.ForcePoint < fp) {
				// FP啊 何练窍促

				return FALSE;
			}
			// 愁捞 扁备 啊瓷茄啊 绢恫啊
			if( PET_enableRidePet( ch, petindex ) == FALSE ){
				// 愁捞 扁备 且 荐 绝促
				return FALSE;
			}

			/* 局肯悼拱 愁捞 扁备 汲沥 */
			// 愁捞 扁备狼 汲沥
			if( PET_ridePet( ch, ch->iu.player.DefaultPet, techid ) ){
#if 0
				int		techid, techindex;
				int		skillindex;
				int		maxlevel;

				/* 愁捞 扁备 己傍矫, 版氰摹甫 涝荐 */
				// 愁捞 扁备 胶懦狼 阿辆 荐摹甫 秒垫
				techid = CHAR_getTechId( ch, haveskill, havetech);
				techindex = TECH_getTechIndex( techid );
				skillindex = TECH_getSkillIndex( techindex );
				maxlevel = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
				// 荤侩且 荐 乐绰 弥措 饭骇俊 捞福绊 乐阑 锭绰, 版氰蔼篮 甸绢坷瘤 臼绰促
				if( maxlevel > ch->player_addon->Skill[haveskill].SkillLevel ){
					float	exp = 10.0;
					int		iexp;
					int		techlevel;
					int		nowlevel;
					int		st, de, in;

					// 版氰摹 拌魂
					techlevel = TECH_getInt( techindex, TECH_NECESSARYLV );
					nowlevel = ch->player_addon->Skill[haveskill].SkillLevel;
					exp = exp * ( (float) techlevel / nowlevel);
					//乔滚吝篮 2硅
					if(CHAR_IsFeverTime( ch ) == TRUE){
						exp = exp * 2;
					}	
					// 版氰摹 焊沥
					iexp = SKILLEXP_AmplifySkillExp( ch, skillindex, exp );
					// 版氰摹甫 啊魂(饭骇诀档 眉农)
					TECH_setSkillExp( ch, skillindex, haveskill, iexp, &st, &de, &in );
				}
#endif
				// FP甫 临牢促
				ch->i.ForcePoint -= fp;
				// 努扼捞攫飘俊 沥焊甫 价脚
				CHAR_send_CP_String( ch, ( CHAR_CP_FORCEPOINT | CHAR_CP_FAME ) );
				// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
				CHAR_sendPartyParam( ch );
			}
			// 局肯悼拱林狼 Dur 皑家
			// 捞巴篮 积魂拌 胶懦 荤侩矫父?
		}
	}
	return TRUE;
}
//*******************************************************
// 扁贱：俺府官
//
int TECH_RiderPet(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	Char	*player;
	//
#ifdef _DEBUG
	print( "\nGalliver!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif
	// 局肯悼拱牢 巴捞 傈力
	if( ch->i.WhichType == CHAR_TYPEPET ){
		// 咯矾 啊瘤 乐栏骨肺, 敲饭捞绢螟档 眉农
		player = (Char *) ch->wu.pet.PlayerChar;

		/* 目膏靛甫 汲沥茄促 */
		// 敲饭捞绢 惑怕甫 眉农
		if( CHAR_CheckCharPointer( player ) && player->w.walk.ridePet != -1 ){
			// 愁捞 扁备 窍绊 乐促绊(搁) 唱公绰, 愁捞 扁备 秦力
			BATTLE_ActionSelect( ch, BATTLE_COM_RIDER,
							 		toindex, TECH_getInt( array, TECH_ID ) );
		}else{
			// 弊 捞寇绰, 酒公巴档 窍瘤 臼绰阑 汲沥
			BATTLE_ActionSelect( ch, BATTLE_COM_NONE,
							 		toindex, TECH_getInt( array, TECH_ID ) );
		}
	}else{
		// 酒公巴档 窍瘤 臼绰阑 汲沥
		BATTLE_ActionSelect( ch, BATTLE_COM_NONE,
						 		toindex, TECH_getInt( array, TECH_ID ) );
	}

	return TRUE;
}
//*******************************************************
// 扁贱：扼捞歹 宏饭捞农(ATK, MND 傍侩)
//
int TECH_RiderBreak(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nRider Break!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	// 目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_RIDER_BREAK,
						 toindex, TECH_getInt( array, TECH_ID ) );
	return TRUE;
}
#endif
#ifdef MAGIC_MISSILE
//*******************************************************
// 扁贱：付过狼 拳混
//
int TECH_MagicMissile(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nMagic Missile!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	// 目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_MAGIC_MISSILE,
						 toindex, TECH_getInt( array, TECH_ID ) );
	return TRUE;
}
#endif
#ifdef LEVEL_DOWN_SKILL
//*******************************************************
// 扁贱：饭骇 促款
//
int TECH_LevelDown(
	Char *ch,
	int haveskill,
	int havetech,
	int toindex,
	int array,
	char *data
)
//
//*******************************************************
{
	//
#ifdef _DEBUG
	print( "\nLevel Down!!(%d, %d, %d, %d) \n",
		haveskill,
		havetech,
		toindex,
		array );
#endif

	// 目膏靛甫 汲沥茄促
	BATTLE_ActionSelect( ch, BATTLE_COM_M_LEVELDOWN,
						 toindex, TECH_getInt( array, TECH_ID ) );
	return TRUE;
}
#endif

int TECH_Lua(Char *ch, int haveskill, int havetech, int toindex, int array, char *data) {
    BATTLE_ActionSelect(ch, BATTLE_COM_LUA, toindex, TECH_getInt(array, TECH_ID));
    return TRUE;
}

//-------------------------------------------------------------------------
// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
//-------------------------------------------------------------------------
void TechParameterMsg( Char *ch, int stm, int dex, int intelligence ){
	char szBuffer[256];
	if( stm != 0 ){
		// "       饭骇诀秦 STM 啊 %-d 登菌嚼聪促."
		snprintf( szBuffer, sizeof( szBuffer ),
			TMSG(LANG_MSG_TECH_C_101), ch->c.Name, stm );
		SYSTEMMSG( ch, szBuffer );
	}
	if( dex != 0 ){
		// "       饭骇诀秦 DEX 啊 %-d 登菌嚼聪促."
		snprintf( szBuffer, sizeof( szBuffer ),
			TMSG(LANG_MSG_TECH_C_102), ch->c.Name, dex );
		SYSTEMMSG( ch, szBuffer );
	}
	if( intelligence != 0 ){
		// "       饭骇诀秦 CTYPE_INT 啊 %-d 登菌嚼聪促."
		snprintf( szBuffer, sizeof( szBuffer ),
			TMSG(LANG_MSG_TECH_C_103), ch->c.Name, intelligence );
		SYSTEMMSG( ch, szBuffer );
	}
}

//-------------------------------------------------------------------------
// 措惑狼 Char 器牢磐甫 秒垫茄促.
// 坷欺饭捞记 墨抛绊府 C狼 炼扒阑 盲快绊 乐阑鳖 眉农档 茄促.
//
//-------------------------------------------------------------------------
Char *TECH_getOperationC_Char( Char *ch, int toindex)
{
	Char *to_ch = NULL;
//	toindex = 1;
	if( toindex < 0 || toindex >= CHAR_WINDOWBUFSIZE) return NULL;
	to_ch = ch->wu.player.OperationC[toindex];
	// 磊脚狼 版快 畴眉农
	if( to_ch == ch ) return ch;
//	print("toindx=%d", toindex);
	if( ! CHAR_CheckCharPointer( to_ch)) return NULL;
//	print("toch=%s", to_ch->c.Name);
	// 颇萍俊 乐阑鳖?
	if( to_ch->i.WhichType == CHAR_TYPEPLAYER ){
		// 颇萍俊 乐阑鳖?
		if( ! CHAR_findPartyMember( ch, to_ch ) ){
			// 绝促搁 传菊俊 乐阑鳖?
			if( ! NPC_Util_charIsInFrontOfChar( ch, to_ch, 1 )){
				return NULL;
			}
			// 傈捧吝篮 救 凳
			if( to_ch->w.BattleMode != BATTLE_CHARMODE_NONE ){
				return NULL;
			}
#ifdef PUK3_VEHICLE
			// 况橇吝篮 救 凳
			if( CHAR_getWorkFlg( to_ch, CHAR_ISWARPING ) ){
				return NULL;
			}
#endif
		}
	}
	else if( to_ch->i.WhichType == CHAR_TYPEPET) {
		// 荤脐牢啊 绢恫啊
#ifdef PET_FIELD
		// 鞘靛摹 唱公绰 辨矾 局肯悼拱捞扼绊绰 焊瘤 臼绰促
		if( to_ch->wu.pet.PlayerChar
		 && to_ch->iu.pet.DepartureBattleStatus != CHAR_PET_FIELD )
#else
		if( to_ch->wu.pet.PlayerChar )
#endif
		{
			if( ch != to_ch->wu.pet.PlayerChar) {
				// 林牢捞 颇萍唱 局肯悼拱俊霸 绝栏搁 救 等促.
				// 颇萍俊 乐阑鳖?
				if( ! CHAR_findPartyMember( ch, to_ch->wu.pet.PlayerChar)) {
					// 传菊俊 乐阑鳖?
					if( ! NPC_Util_charIsInFrontOfChar( ch, to_ch->wu.pet.PlayerChar, 1 )){
						return NULL;
					}
				}
			}
		}
		// 传菊俊 乐阑鳖?
		else {
			if( ! NPC_Util_charIsInFrontOfChar( ch, to_ch, 1 )){
				return NULL;
			}
		}
	}else if(MORPH_CheckMonsterImageNumber( to_ch) == TRUE){
		if( ! NPC_Util_charIsInFrontOfChar( ch, to_ch, 1 )){
			return NULL;
		}
	}else {
		return NULL;
	}
	// 咯扁鳖瘤 坷搁(磊) 宝满酒.
	return to_ch;
}

//-------------------------------------------------------------------------
// 胶懦 版氰摹甫 技飘 茄促.饭骇诀 眉农扼电瘤档 茄促.
//-------------------------------------------------------------------------
BOOL TECH_setSkillExp( Char *ch,
							  int skillindex,
							  int haveskillindex,
							  int exp,
							  int *stm, // 饭骇诀沁阑 锭俊 掘绰 STM
							  int *dex, // 饭骇诀沁阑 锭俊 掘绰 DEX
							  int *intelligence) // 饭骇诀沁阑 锭俊 掘绰 INTELLIGENCE
{
	int skillexpid;
	int skillexpindex;
	int srclevel;
	int destexp;
	int maxlevel;
	int limit = 100;
	BOOL ret = FALSE;

	// 刘啊盒阑 檬扁拳
	*stm = 0;
	*dex = 0;
	*intelligence = 0;
	if( exp == 0 ) return FALSE;

	// 盔狼 饭骇 焊粮
	srclevel = CHAR_getSkillLevel( ch, haveskillindex);
	// 磊脚狼 茄拌 饭骇捞 登绊 乐栏搁(磊) 版氰蔼篮 甸绢坷瘤 臼绰促.
#if 1
	maxlevel = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
#else
	maxlevel = SKILL_getMaxLevel( SKILL_getInt( skillindex, SKILL_ID), ch->iu.player.Job);
#endif
	if( srclevel >= maxlevel ) return FALSE;

	// 版氰摹 敲矾胶
	destexp = CHAR_getSkillExp( ch, haveskillindex) +exp;
	CHAR_setSkillExp( ch, haveskillindex, destexp);

	//
	skillexpid = SKILL_getInt( skillindex, SKILL_S_TABLE);
	skillexpindex = SKILLEXP_getSkillExpIndex( skillexpid);
	if( skillexpindex == -1 ) {
		goto TECH_SETSKILLEXP_ENSURE;
	}


	// 饭骇篮1~10钎扁.弊犯瘤父 硅凯篮 0栏肺何磐捞聪鳖,+1饭骇篮 弊措肺 亮篮 巴捞促.
	if( srclevel >= cgmsvcf.maxskilllevel ){	// 饭骇 10捞惑篮 救 等促.
		goto TECH_SETSKILLEXP_ENSURE;
	}

	// 饭骇诀沁绰瘤?
	if( destexp >= SKILLEXP_getInt( skillexpindex, srclevel ) ) {
		// 沁促.
	}else{
		// 窍瘤 臼疽栏搁 弥饶俊
		goto TECH_SETSKILLEXP_ENSURE;
	}

	// 饭骇诀沁促.
	CHAR_setSkillLevel( ch, haveskillindex, srclevel+1);
	ret = TRUE;	// 饭骇诀 敲贰弊

	// 积魂拌 傈侩 颇扼固磐啊 坷福绰 巴篮 珐农 C啊 登绊 唱辑
	if( ch->iu.player.JobRank >= JOBS_RANK_C
	// 茄摸 歹 货酚霸 棵扼埃 饭骇捞 5捞惑捞扼搁 颇扼固磐啊 函拳茄促.
	&&  srclevel+1 >= 5
	){
		int stmWork, dexWork, intelWork;

		// 快急 况农 康开俊 刘啊 傈噶盒阑 持绰促.
		stmWork = SKILL_getInt( skillindex, SKILL_GETSTAMINA);
		dexWork = SKILL_getInt( skillindex, SKILL_GETDEX);
		intelWork = SKILL_getInt( skillindex, SKILL_GETINTELLIGENCE);

		// 阿阿阑 啊魂秦 惑茄阑 逞扁档 窍绊, 0 固父捞 等促搁 芭扁辑 肛冕促.
		if( C_STM + stmWork > limit ) stmWork = limit - C_STM;
		if( C_STM + stmWork < 0 ) stmWork = C_STM;

		if( C_DEX + dexWork > limit ) dexWork = limit - C_DEX;
		if( C_DEX + dexWork < 0 ) dexWork = C_DEX;
		if( C_INT + intelWork > limit ) intelWork = limit - C_INT;
		if( C_INT + intelWork < 0 ) intelWork = C_INT;
		// 积魂 颇扼固磐 函拳
		*stm = stmWork;
		*dex = dexWork;
		*intelligence = intelWork;
		C_STM += *stm;
		C_DEX += *dex;
		C_INT += *intelligence;

	}

	//胶懦 饭骇 UP捞骨肺 概仿阑 霖促
	ch->iu.player.Charm += CH_FIX_PLAYERSKILLUP;
	if(ch->iu.player.Charm > 100 ) 	ch->iu.player.Charm = 100;


	// 颇扼固磐 犁拌魂
	CHAR_complianceParameter( ch);

	CHAR_send_CP_String( ch, CHAR_CP_CHARM);
		CHAR_send_CP2_String( ch, CHAR_CP2_DUELPOINT);


	//扁贱 单捞磐 焊辰促
	CHAR_sendTechData( ch, haveskillindex);


TECH_SETSKILLEXP_ENSURE:
	CHAR_sendSkillDataOne( ch, haveskillindex);

	return ret;
}
//-------------------------------------------------------------------------
// 疙己阑 秒垫茄促.(啊傍, 炼府, 炼力)
// 坷欺饭捞记 墨抛绊府 A
//-------------------------------------------------------------------------
int TECH_getFame3( Char *ch, int skilllv, int itemrank, int result, int flg)
{

	int fame = 1;

	//公流篮 疙己摹 函拳 绝澜
	if(ch->iu.player.Job == 1) {
		return fame;
	}


	//厘厚前拌(啊傍)
	if(flg == 0){
		if( result ) {
			//疙己 惑茄摹狼 眉农
			if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
			if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

			//磊脚狼 胶懦 饭骇/2捞惑狼 酒捞袍阑 父甸菌促
			if( (skilllv / 2) <= itemrank) {
				fame = 60;
				ch->iu.player.Fame += fame;
				ch->iu.player.FameGetValue -= fame;

			}
			else if( (skilllv / 2) > itemrank) {
				fame = 30;
				ch->iu.player.Fame += fame;
				ch->iu.player.FameGetValue -= fame;

			}
		}

	}else{
	//炼力, 炼府
		if( result ) {
			//疙己 惑茄摹狼 眉农

			if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
			if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

			if( (skilllv / 2) <= itemrank) {
				fame = 20;
				ch->iu.player.Fame += fame;
				ch->iu.player.FameGetValue -= fame;

			}
			else if( (skilllv / 2) > itemrank) {
				fame = 10;
				ch->iu.player.Fame += fame;
				ch->iu.player.FameGetValue -= fame;
			}
		}
	}

	//弥措摹甫 逞瘤 臼霸
	if(ch->iu.player.Fame > CHAR_MAXFAME) ch->iu.player.Fame = CHAR_MAXFAME;
	if(ch->iu.player.FameGetValue < 0) ch->iu.player.FameGetValue =0;
	return fame;
}

//-------------------------------------------------------------------------
// 疙己阑 秒垫茄促.(荐府, 皑沥, 档厘)
// 坷欺饭捞记 墨抛绊府 B
//-------------------------------------------------------------------------
static int TECH_getFame1( Char *ch, int skilllv, int itemrank, int result)
{

	int fame = 1;

	//公流篮 疙己摹 函拳 绝澜
	if(ch->iu.player.Job == 1) {
		return fame;
	}


	if( result ) {
		if(ch->iu.player.JobAncestry == 390
		||ch->iu.player.JobAncestry == 400
		){
			//疙己 惑茄摹狼 眉农

			if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
			if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

			fame = 50;
			ch->iu.player.Fame += fame;
			ch->iu.player.FameGetValue -= fame;
		}else{
			//疙己 惑茄摹狼 眉农

			if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
			if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

			fame = 60;
			ch->iu.player.Fame += fame;
			ch->iu.player.FameGetValue -= fame;
		}
	}
	else {

	}


	//弥措摹甫 逞瘤 臼霸
	if(ch->iu.player.Fame > CHAR_MAXFAME) ch->iu.player.Fame = CHAR_MAXFAME;
	if(ch->iu.player.FameGetValue < 0) ch->iu.player.FameGetValue = 0;

	return fame;
}
//-------------------------------------------------------------------------
// 疙己阑 秒垫茄促.(览鞭 贸摹, 摹丰)
// 坷欺饭捞记 墨抛绊府 C
// int toskilllv : 惑措狼 饭骇
// int result : TRUE : 己傍锭 FALSE : 角菩锭
//-------------------------------------------------------------------------
static int TECH_getFame2( Char *ch, int skilllv, int maxskilllv, int result)
{
	int fame = 0;

	//公流篮 疙己摹 函拳 绝澜
	if(ch->iu.player.Job == 1) {
		return fame;
	}

	if( result == TRUE) {
		//疙己 惑茄摹狼 眉农
		if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
		if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

		//狼荤
		if(ch->iu.player.JobAncestry == 440){
			fame = 100;
			ch->iu.player.Fame += fame;
			ch->iu.player.FameGetValue -= fame;
		}else{
			//埃龋荤
			fame = 15;
			ch->iu.player.Fame += fame;
			ch->iu.player.FameGetValue -= fame;
		}
	}
	else {

	}

	//弥措摹甫 逞瘤 臼霸
	if(ch->iu.player.Fame > CHAR_MAXFAME) ch->iu.player.Fame = CHAR_MAXFAME;
	if(ch->iu.player.FameGetValue < 0) ch->iu.player.FameGetValue = 0;
	return fame;
}

//-------------------------------------------------------------------------
// 疙己阑 秒垫茄促.(函脚, 函厘)
// 坷欺饭捞记 墨抛绊府 C
// int toskilllv : 惑措狼 饭骇
// int result : TRUE : 己傍锭 FALSE : 角菩锭
//-------------------------------------------------------------------------
static int TECH_getFame4( Char *ch, int skilllv, int targetlv, int result)
{
	int fame = 0;

	//公流篮 疙己摹 函拳 绝澜
	if(ch->iu.player.Job == 1) {
		return fame;
	}


	if( result == TRUE) {
		//疙己 惑茄摹狼 眉农

		if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
		if(CHAR_FameLimitCheck( ch) == FALSE) return fame;
			fame = 10;
			ch->iu.player.Fame += fame;
			ch->iu.player.FameGetValue -= fame;
	}
	else {
	}

	//弥措摹甫 逞瘤 臼霸
	if(ch->iu.player.Fame > CHAR_MAXFAME) ch->iu.player.Fame = CHAR_MAXFAME;
	if(ch->iu.player.FameGetValue < 0) ch->iu.player.FameGetValue = 0;

	return fame;
}

//-------------------------------------------------------------------------
// 疙己阑 秒垫茄促.(国盲, 盲奔, 荐菲)
// 坷欺饭捞记 墨抛绊府 D
// int toskilllv : 惑措狼 饭骇
// int result : TRUE : 己傍锭 FALSE : 角菩锭
//-------------------------------------------------------------------------
static int TECH_getFame5( Char *ch, int result)
{
	int fame = 0;

	//公流篮 疙己摹 函拳 绝澜
	if(ch->iu.player.Job == 1) {
		return fame;
	}

	if( result == TRUE) {
		//疙己 惑茄摹狼 眉农
		if(CHAR_FameGetTimeCheck(ch) == FALSE) return fame;
		if(CHAR_FameLimitCheck( ch) == FALSE) return fame;

		// 疙己摹 10000 固父锭
		fame = 2;
		ch->iu.player.Fame += fame;
		ch->iu.player.FameGetValue -= fame;


	}
	//弥措摹甫 逞瘤 臼霸
	if(ch->iu.player.Fame > CHAR_MAXFAME) ch->iu.player.Fame = CHAR_MAXFAME;
	if(ch->iu.player.FameGetValue < 0) ch->iu.player.FameGetValue = 0;
	return fame;
}


static char sendDeleteAtomBuf[2048];
//-------------------------------------------------------------------------
// 酒捞袍 钦己俊 荤侩茄 家犁甫 瘤款促.
// 窍绰 辫俊, 瘤款 巴 府胶飘甫 累己茄促.
//-------------------------------------------------------------------------
static char *TECH_MergeItem_deleteAtom(
	Char *ch,			// 某腐磐狼 牢郸胶
	int haveitembindex, // 家犁 B甫 啊瘤绊 乐绰 厘家
	int itemb,			// 家犁 B狼 酒捞袍 牢郸胶
	int *mergehaveitemindex, 	// 钦己俊 荤侩窍绰 酒捞袍捞 啊瘤绊 乐绰 厘家 府胶飘
	int *mergeitemid,		// 钦己窍绰 酒捞袍狼 牢郸胶 府胶飘
	int mergeitemmax, 		// 弊 府胶飘俊 割俺 甸绢啊绊 乐绢.
	int recipeindex			// 饭矫乔牢璃胶.
)
{
	int i, j;
	char buf[256];
	char escape[128];

	// 瘤款促
	sendDeleteAtomBuf[0] = '\0';
	if( ITEM_CHECKINDEX( itemb)) {
		// 敲饭捞绢狼 家瘤前栏肺何磐 毒促.
        int remain = ITEM_getInt(itemb, ITEM_REMAIN);
        if (remain <= 1) {
            CHAR_unsetItem(ch, haveitembindex);
        } else {
            ITEM_setInt(itemb, ITEM_REMAIN, remain - 1);
        }
		// 毒 老阑 焊辰促.
		CHAR_sendItemDataOne( ch, haveitembindex);
		makeEscapeString( ITEM_getChar( itemb, ITEM_TRUENAME),
						  escape, sizeof( escape));
//        if (remain > 1) {
//            snprintf(sendDeleteAtomBuf, sizeof(sendDeleteAtomBuf), "%s%d|", escape, 1);
//        } else {
        snprintf(sendDeleteAtomBuf, sizeof(sendDeleteAtomBuf), "%s|", escape);
//		}
		// 酒捞袍阑 瘤款 肺弊甫 秒茄促.
		LogItem(
			ch->cu.player.CdKey,	// CD虐
			ch->c.Name, /* 某腐磐疙 */
			ITEM_getInt( itemb, ITEM_ID),  /* 酒捞袍 锅龋 */
			"MargeDelete",
			ch->i.MapId, ch->i.Floor, ch->i.X, ch->i.Y
		);
		// 富混.
        if (remain <= 1) {
            ITEM_endExistItemsOne(itemb);
        }
	}

	for( i = 0; i < ITEM_RECIPE_MAX; i ++ ) {
		for( j = 0; j < mergeitemmax; j ++ ) {
			if( ITEM_RECIPE_getInt( recipeindex, ITEM_RECIPE_A+i)
				== ITEM_getInt( mergeitemid[j], ITEM_ID) )
			{
				int require = ITEM_RECIPE_getInt( recipeindex, ITEM_RECIPE_REQUIRE_A+i);
				int remain;
				if( require < 1 ) require = 1;
				remain = ITEM_getInt( mergeitemid[j], ITEM_REMAIN) - require;
				// 价脚侩 某腐磐 扼牢 累己
				makeEscapeString( ITEM_getChar( mergeitemid[j], ITEM_TRUENAME),
								  escape, sizeof( escape));
				if( require > 1 ) {
					snprintf( buf, sizeof( buf),
							  "%s%d|",
							  escape, require);
				}
				else {
					snprintf( buf, sizeof( buf),
							  "%s|",
							  escape);
				}
				strcat( sendDeleteAtomBuf, buf);
				// 瘤款促
				if( remain <= 0 ) {
					// 唱赣瘤荐啊 捞力 绝栏搁 酒捞袍捞 荤扼柳促.
					// 酒捞袍阑 瘤款 肺弊甫 秒茄促.
					LogItem(
						ch->cu.player.CdKey,	// CD虐
						ch->c.Name, /* 某腐磐疙 */
						ITEM_getInt( mergeitemid[j], ITEM_ID ),  /* 酒捞袍 锅龋 */
						"MargeDelete",
						ch->i.MapId, ch->i.Floor, ch->i.X, ch->i.Y
					);
					// 酒捞袍阑 家蜡磊肺何磐 毒促.
					CHAR_unsetItem( ch, mergehaveitemindex[j]);
					// 家芭.
					ITEM_endExistItemsOne( mergeitemid[j]);
					// 瘤款 老阑 焊辰促.
					CHAR_sendItemDataOne( ch, mergehaveitemindex[j]);
				}
				// 唱赣瘤荐 临牢促
				else {
					ITEM_setInt( mergeitemid[j], ITEM_REMAIN, remain);
					CHAR_sendItemDataOne( ch, mergehaveitemindex[j]);
				}
				break;
			}
		}
	}
	return sendDeleteAtomBuf;
}
// 家犁 B抛捞喉阑 骂福绰 厘家
static int tech_mergeitem_itemelement[] = {
	ITEM_MATERIAL_WEAPON, 
	ITEM_MATERIAL_ARMOUR
#ifdef JEWLRY_ACCESSORY
/* 饰品制作增加宝石和装饰宝石 */
	,ITEM_MATERIAL_ACCESSORY
#endif
};
// 家犁 B狼 酒捞袍 瓤苞贸.ITEM_MATERIAL_* 客 钦窍绰 老.

static int tech_mergeitem_itemeffect[] = {
	ITEM_MAXDURABILITY,				/*  郴备仿 */
    ITEM_MODIFYATTACK,              /*  傍拜仿 函拳樊  */
    ITEM_MODIFYDEFENCE,             /*  规绢仿 函拳樊  */
    ITEM_MODIFYAGILITY,             /*  AGILITY 函拳樊  */
	ITEM_MODIFYMAGIC,				/*  MAGIC 函拳樊 */
	ITEM_MODIFYRECOVERY,			/*  雀汗仿 函拳樊 */
	ITEM_MODIFYCRITICAL,			/*  困扁 函拳樊 */
	ITEM_MODIFYCOUNTER,				/*  墨款磐 函拳樊 */
	ITEM_MODIFYHITRATE,				/*  疙吝啦 荐沥摹 */
    ITEM_MODIFYAVOID,               /*  雀乔啦 荐沥    */

    ITEM_MODIFYHP,                  /*  HP函拳樊    */
    ITEM_MODIFYFORCEPOINT,          /*  FP函拳樊    */
    ITEM_MODIFYLUCK,                /*  LUCK 函拳樊    */
    ITEM_MODIFYCHARISMA,            /*  CHARISMA 函拳樊    */
    ITEM_MODIFYCHARM,				/*  瘤硅仿 函拳樊 */
    /*  胶抛捞磐胶 荐沥摹.*/
    ITEM_POISON,					/* 刀 吧阑 锭付促 单固瘤          */
    ITEM_SLEEP,              		/* 泪.青悼且 荐 绝促            */
    ITEM_STONE,              		/* 倒.青悼且 荐 绝促              */
    ITEM_DRUNK,              		/* 秒茄促.疙吝啦捞 郴赴促     */
    ITEM_CONFUSION,          		/* 去鄂.傍拜 格钎甫 肋给茄促     */
    ITEM_AMNESIA,            		/* 扁撅惑角 郴己 */
	ITEM_MODIFYSTAMINA,             /* 胶怕固呈 荐沥摹 */
	ITEM_MODIFYDEX,
	ITEM_MODIFYINTELLIGENCE
};


// 己傍啦阑 拌魂茄促.
int TECH_MergeItem_CalcProb( Char *ch, int SkillId, int haveskill ){

	int  Dex, Int, Stm, failedprob = 0;
#ifdef PUK2
	int rate;
#endif

	Dex = C_DEX;
	Int = C_INT;
	Stm = C_STM;
#ifdef PUK2
	// 府滚胶矫俊绰 颇扼固磐甫 惑铰
	if( ch->w.RebirthSkill ){

		// 炼府·炼力绰 喊墨抛绊府 秒鞭
		if( SkillId == 215 || SkillId == 216 ){
			rate = SKILL_RebirthParam( REBIRTH_CATEGORY_MERGE, ch->w.RebirthLevel, REBIRTH_PARAM_RATE );
		}else{
			rate = SKILL_RebirthParam( REBIRTH_CATEGORY_CRAFT, ch->w.RebirthLevel, REBIRTH_PARAM_RATE );
		}

		Dex = ( Dex * rate + 50 ) / 100;
		Int = ( Int * rate + 50 ) / 100;
		Stm = ( Stm * rate + 50 ) / 100;
	}
#endif

	// 公郊 胶懦阑 荤侩沁绰瘤?

	if( 200 <= SkillId && SkillId <= 214 ){	// 公扁 规绢侩 扁备 累己狼 版快

		// 胶怕固呈唱 璃胶啊 50捞惑 乐促搁 角菩窍瘤 臼绰促.
		if( Dex > 50 || Stm > 50 ){
			failedprob = 0;
		}else{
			failedprob = ( 50 - Stm ) * 0.34 + ( 50 - Dex ) * 0.66;
		}
	}else
	if( 215 == SkillId ){	// 炼府狼 版快
		if( Dex > 50 || Int > 50 ){
			failedprob = 0;
		}else{
			failedprob = ( 50 - Dex ) * 0.5 + ( 50 - Int ) * 0.5;
		}

	}else
	if( 216 == SkillId ){	// 炼力狼 版快
		if( Dex > 50 || Int > 50 ){
			failedprob = 0;
		}else{
			failedprob = ( 50 - Dex ) * 0.34 + ( 50 - Int ) * 0.66;
		}
	}


	if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == TRUE ){
		// 犬伏 钎矫狼 叼滚弊 皋技瘤
		char buf[128];
		snprintf( buf, sizeof( buf),
				  TMSG(LANG_MSG_AUTOMATIC_358), failedprob );
        print("%s", buf);
	}

	// 厘厚 酒捞袍狼 己傍啦 函拳摹
	failedprob *= ( 100 - CHAR_getSkillSuccessProb( ch, haveskill)) * 0.01;

	// 弥厩 惑怕俊辑档 芭狼 角菩窍瘤 臼绰单.
	if( failedprob == 0 || RAND( 1, 1000 ) > failedprob ){
		// 己傍
		return  TRUE;
	}else{
		// 角菩
		return  FALSE;
	}
}

//-------------------------------------------------------------------------
// 积魂拌 颇扼固磐狼 瓤苞甫 辰促
//-------------------------------------------------------------------------
#ifdef MONSTER_HELP_TECH
BOOL TECH_MergeItem_addNeoParam( Char *ch, int SkillId, int itemindex, int checkflg )
#else
BOOL TECH_MergeItem_addNeoParam( Char *ch, int SkillId, int itemindex )
#endif
{
	int iMoto, Dex, Int, Stm, prob;
	float bonus = 1.0;
#ifdef PUK2
	int rate;
#endif


	Dex = C_DEX;
	Int = C_INT;
	Stm = C_STM;
#ifdef PUK2
	// 府滚胶矫俊绰 颇扼固磐甫 惑铰
	if( ch->w.RebirthSkill ){
		// 炼府·炼力绰 喊墨抛绊府 秒鞭
		if( SkillId == 215 || SkillId == 216 ){
			rate = SKILL_RebirthParam( REBIRTH_CATEGORY_MERGE, ch->w.RebirthLevel, REBIRTH_PARAM_RATE );
		}else{
			rate = SKILL_RebirthParam( REBIRTH_CATEGORY_CRAFT, ch->w.RebirthLevel, REBIRTH_PARAM_RATE );
		}

		Dex = ( Dex * rate + 50 ) / 100;
		Int = ( Int * rate + 50 ) / 100;
		Stm = ( Stm * rate + 50 ) / 100;
        print_t(LANG_MSG_INFOTYPE_INFO);
		print( "Rebirth Merge(%d/%d/%d)\n", Dex, Int, Stm );
	}
#endif

	// 公郊 胶懦阑 荤侩沁绰瘤?

	if( 200 <= SkillId && SkillId <= 206 ){	// 公扁狼 版快
		if( Stm < 50 || Dex < 50 ){
			bonus = 1.0;	// 胶怕固呈唱 璃胶啊 50 捞窍扼搁 1硅
		}else{
			bonus = 1.0 + ( ( Stm - 50 ) * 0.34 + ( Dex - 50 ) * 0.66 ) * 0.001;
		}
		// 颇况诀 矫挪促.
		iMoto = ITEM_getInt( itemindex, ITEM_MODIFYATTACK );
		iMoto *= bonus;
		ITEM_setInt( itemindex, ITEM_MODIFYATTACK, iMoto );

        print_t(LANG_MSG_INFOTYPE_INFO);
		print( "Bonus Rate(%d)\n", (int)( bonus * 100 ) );
	}else
	if( 207 <= SkillId && SkillId <= 214 ){	// 规绢侩 扁备 啊傍狼 版快

		if( Stm < 50 || Dex < 50 ){
			bonus = 1.0;	// 胶怕固呈唱 璃胶啊 50 捞窍扼搁 1硅
		}else{
			bonus = 1.0 + ( ( Stm - 50 ) * 0.34 + ( Dex - 50 ) * 0.66 ) * 0.001;
		}
		// 颇况诀 矫挪促.
		iMoto = ITEM_getInt( itemindex, ITEM_MODIFYDEFENCE );
		iMoto *= bonus;
		ITEM_setInt( itemindex, ITEM_MODIFYDEFENCE, iMoto );

	}else
	if( 216 == SkillId ){	// 炼力
		if( Int < 50 || Dex < 50 ){
			prob = 0;
		}else{
			prob = ( Dex - 50 ) * 0.34 + ( Int - 50 ) * 0.66 ;
#ifdef MONSTER_HELP_TECH
			if(checkflg)
			{
			//	prob+=(prob*MERGE_RATE);
				prob+=MERGE_ADDPOINT;
			}
#endif

		}

		if( RAND( 1, 1000 ) <= prob ){
			// 己傍捞促.2俺肺 茄促.
			iMoto = ITEM_getInt( itemindex, ITEM_REMAIN );
			iMoto += 1;
			// 促父 弥措荐甫 逞绰 版快绰 疵府瘤 臼绰促.
			if( ITEM_getInt( itemindex, ITEM_MAXREMAIN ) >= iMoto ){
				ITEM_setInt( itemindex, ITEM_REMAIN, iMoto );
			}
		}
	}else
	if( 215 == SkillId ){	// 炼府
		if( Int < 50 || Dex < 50 ){
			prob = 0;
		}else{
			prob = ( Dex - 50 ) * 0.5 + ( Int - 50 ) * 0.5 ;
#ifdef MONSTER_HELP_TECH
			if(checkflg)
			{
				//prob+=(prob*MERGE_RATE);
				prob+=MERGE_ADDPOINT;
			}
#endif
		}
		if( RAND( 1, 1000 ) <= prob ){
			// 己傍捞促.2俺肺 茄促.
			iMoto = ITEM_getInt( itemindex, ITEM_REMAIN );
			iMoto += 1;
			// 促父 弥措荐甫 逞绰 版快绰 疵府瘤 臼绰促.
			if( ITEM_getInt( itemindex, ITEM_MAXREMAIN ) >= iMoto ){
				ITEM_setInt( itemindex, ITEM_REMAIN, iMoto );
			}
		}
	}

	return TRUE;

}
//-------------------------------------------------------------------------
// 酒捞袍 B狼 瓤苞甫 辰促.
//-------------------------------------------------------------------------
#ifdef PUK2
BOOL TECH_MergeItem_addItemB( Char *ch, int itemindex, int itemb, int checkflg)
#else
BOOL TECH_MergeItem_addItemB( int itemindex, int itemb)
#endif
{
    int materialId = -1;
	int materialindex;
	int i, iItemType;
	int array = -1;
#ifdef PUK2
	int rate;
#endif

	if( ! ITEM_CHECKINDEX( itemindex)) return FALSE;
	if( ! ITEM_CHECKINDEX( itemb)) return FALSE;

    // 酒捞袍狼 辆幅
    iItemType = ITEM_getInt(itemindex, ITEM_TYPE);
    int idb = ITEM_getInt(itemb, ITEM_ID);
    materialId = ITEM_MATERIAL_findMaterialId(iItemType, idb);
    if (materialId < 0) {
        if (iItemType >= ITEM_PET_CRYSTAL && iItemType <= ITEM_PET_AMULET2) return FALSE;
        // 公扁扼搁
        if ((ITEM_SWORD <= iItemType && iItemType <= ITEM_BOOMERANG) || ITEM_IsExtItemWeapon(iItemType)) {
            array = 0;
        } else
            // 规绢侩 扁备扼搁
        if ((ITEM_SHIELD <= iItemType && iItemType <= ITEM_SHOES) || ITEM_getEquipPlace(iItemType) < CHAR_DECORATION1) {
            array = 1;
#ifdef JEWLRY_ACCESSORY
/* 饰品制作增加宝石和装饰宝石 */
        } else if ((ITEM_BRACELET <= iItemType && iItemType <= ITEM_AMULET) ||
                   ITEM_getEquipPlace(iItemType) < CHAR_CRYSTAL) {
            array = 2;
#endif
        } else {

            return FALSE;
        }

        materialId = ITEM_getInt(itemb, tech_mergeitem_itemelement[array]);
    }
    materialindex = ITEM_MATERIAL_getIndex(materialId);
	if( ! ITEM_MATERIAL_CHECKINDEX( materialindex)) return FALSE;

	// 阿阿 刘啊窍绰 镑阑 疵赴促.
	for( i = 0; i < arraysizeof( tech_mergeitem_itemeffect); i ++ ) {
		int addflg = ITEM_MATERIAL_getInt( materialindex, i*3+1);
		int from = ITEM_MATERIAL_getInt( materialindex, i*3+2);
		int to = ITEM_MATERIAL_getInt( materialindex, i*3+3);
		int value;
		if( from > to ) {
			int tmp;
			tmp = from;
			from = to;
			to = tmp;
		}
		value = RAND( from , to);
#ifdef PUK2
		// 焊籍父 钦己窍绊 乐阑鳖 眉农
		if( checkflg ){
			// 府滚胶矫俊绰 颇扼固磐甫 惑铰
			if( ch->w.RebirthSkill ){
				rate = SKILL_RebirthParam( 0, ch->w.RebirthLevel, REBIRTH_PARAM_RATE );
#ifdef REBIRTH_EFFECT
				value = ( value * rate + 50 ) / 100;
#else
				value = ( value * rate ) / 100;
#endif
			}
		}
#endif
		ITEM_setInt( itemindex, tech_mergeitem_itemeffect[i],
					   (ITEM_getInt( itemindex, tech_mergeitem_itemeffect[i])
					   + ( addflg ?  value: 0)) * (addflg ?  1.0 : (100+value)*0.01));

	}
	// 郴备档绰 MAX俊 嘎冕促.
	ITEM_setInt( itemindex, ITEM_DURABILITY,
		ITEM_getInt( itemindex, ITEM_MAXDURABILITY ) );

	return TRUE;
}

//-------------------------------------------------------------------------
// 炼府牢侩狼 漂荐 酒捞袍阑 厘厚 窍绊 乐阑鳖?
//-------------------------------------------------------------------------
#define ITEM_ID_AJINOMOTO 18628
//#define ITEM_ID_CHOURIKIGU 18218
#define ITEM_ID_CHOURIKIGU 18629
BOOL TECH_MergeItem_CheckAjinomoto( Char *ch )
{
	int i, itemindex, iRet = 0;
	// 厘厚前父 眉农
	for( i = 0; i < CHAR_EQUIPPLACENUM; i ++ ){
		//
		itemindex = CHAR_getItemIndex( ch, i );
		if( ITEM_CHECKINDEX( itemindex ) == FALSE ) continue;
		// 酒瘤畴葛配?
		if( ITEM_getInt( itemindex, ITEM_ID ) == ITEM_ID_AJINOMOTO ){
			char szBuffer[256];
			iRet |= TECH_EXTRA_AJINOMOTO;	// 酒瘤畴葛配 厘厚 窍绊 乐菌促.
				// 肺弊甫 秒茄促.
				LogItem(
					ch->cu.player.CdKey,	// CD虐
					ch->c.Name, /* 某腐磐疙 */
					ITEM_getInt( itemindex, ITEM_ID ),  /* 酒捞袍 锅龋 */
					"ExtraDelete",
					ch->i.MapId,
					ch->i.Floor,
					ch->i.X, ch->i.Y
				);
			// 瘤款 老阑 焊辰促.
			snprintf( szBuffer, sizeof( szBuffer ),
				TMSG(LANG_MSG_EVENT_004), ITEM_getUseName(itemindex ) );
			SYSTEMMSG( ch, szBuffer );
			// 酒捞袍阑 家蜡磊肺何磐 毒促.
			CHAR_unsetItem( ch, i );
			// 家芭.
			ITEM_endExistItemsOne( itemindex );
			// 瘤款 老阑 焊辰促.
			CHAR_sendItemDataOne( ch, i );
			return TRUE;
		}
	}
	return FALSE;
}

BOOL TECH_MergeItem_KiguCheck( Char *ch )
{
	char szBuffer[256];
	int i, itemindex, iRet = 0;
	// 厘厚前父 眉农
	for( i = 0; i < CHAR_EQUIPPLACENUM; i ++ ){
		//
		itemindex = CHAR_getItemIndex( ch, i );
		if( ITEM_CHECKINDEX( itemindex ) == FALSE ) continue;
//		print( "\n%d---%d\n", ITEM_ID_CHOURIKIGU, ITEM_getInt( itemindex, ITEM_ID ) );
		// FP 利绢 亮篮 扁备?
		if( ITEM_getInt( itemindex, ITEM_ID ) == ITEM_ID_CHOURIKIGU ){
			int dur;
			iRet |= TECH_EXTRA_CHOURIKIGU;	// 炼府 扁备甫 荤侩窍绊 乐菌促.
//			print( "扁备惯\n" );
			// DUR甫 临牢促
			dur = ITEM_getInt( itemindex, ITEM_DURABILITY) -1;
			ITEM_setInt( itemindex, ITEM_DURABILITY, dur);
			if( dur <= 0 ){
				// 肺弊甫 秒茄促.
				LogItem(
					ch->cu.player.CdKey,	// CD虐
					ch->c.Name, /* 某腐磐疙 */
					ITEM_getInt( itemindex, ITEM_ID ),  /* 酒捞袍 锅龋 */
					"ExtraDelete",
					ch->i.MapId,
					ch->i.Floor,
					ch->i.X, ch->i.Y
				);
				// 瘤款 老阑 焊辰促.
				snprintf( szBuffer, sizeof( szBuffer ),
					TMSG(LANG_MSG_EVENT_004), ITEM_getUseName(itemindex ) );
				SYSTEMMSG( ch, szBuffer );
				// 酒捞袍阑 家蜡磊肺何磐 毒促.
				CHAR_unsetItem( ch, i );
				// 家芭.
				ITEM_endExistItemsOne( itemindex );
				// 瘤款 老阑 焊辰促.
				CHAR_sendItemDataOne( ch, i );
			}else{
				// DUR甫 临咯 敲饭捞绢俊 焊辰促.
				CHAR_sendItemDataOne( ch, i );
			}
			return TRUE;
		}
	}
	return FALSE;
}

// 酒瘤畴葛配狼 瓤苞甫 辰促.
int TECH_MergeItem_Ajinomoto( int newitemindex ){
	char szBuffer[256];
	char szBuffer2[256];
	char *pszArg;
	int iFp;
	// 盔捞 静绊 乐绰 蔼阑 墨乔
	pszArg = ITEM_getChar( newitemindex, ITEM_ARGUMENT );

	if( sscanf( pszArg, "%s", szBuffer2 ) != 1 ){
		return FALSE;
	}
	// FP狼 虐况靛啊 弥檬肺 乐阑鳖?
	if( szBuffer2[0] == 'F' && szBuffer2[1] == 'P' ){
	}else{
		return FALSE;
	}
	// 雀汗樊捞 结 乐阑鳖?
	if( sscanf( szBuffer2+2, "%d", &iFp ) != 1 ){
		return FALSE;
	}

	// 绢蠢 10 敲矾胶 窍绊 促矫 敬促
	iFp += 10;
	sprintf( szBuffer, "FP%d", iFp );
	ITEM_setChar( newitemindex, ITEM_ARGUMENT, szBuffer );

	return TRUE;
}

#ifdef PUK2
#include "tech_rebirth.c"
#include "../ext/techEx.h"

#endif

//-------------------------------------------------------------------------
// 酒捞袍 钦己(啊傍)
//-------------------------------------------------------------------------
int TECH_MergeItem( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	int mergehaveitemindex[ITEM_RECIPE_MAX];
	int mergeitemid[ITEM_RECIPE_MAX];
	int itemb = -1;
	int haveitembindex = -1;
	int mergeitemmax=0;
	char buf[128];
	int i, j;
	int recipeid;         // 饭矫乔狼 ID
	int recipeindex = -1;      // 饭矫乔狼 抛捞喉狼 index
	int skillid = -1;          // 某腐磐狼 skillid
	int *recipelist;      // 饭矫乔.
	int recipelen;              // 饭矫乔狼 酒捞袍狼 荐.
	int skilllv = -1;          //
	int itemrank = 0;         // destitem 狼 酒捞袍 珐农
	int destitemid = -1;
	int emptyhaveitemindex;
	int recipeskillindex;
	int skillindex = -1;
	int amount_fp = 0;
	int exp = 0; // 裙垫 胶懦 版氰摹
	int newitemindex = -1;
	char *deleteatomstring = NULL;
	int fd;
	int fame = 0;
	BOOL levelup = FALSE;
	BOOL injuryflg = FALSE;  // 惑贸车绰瘤 敲贰弊

	BOOL flg = FALSE; // 角菩牢啊 己傍牢啊.
	int stm = 0, dex = 0, intelligence = 0;
#ifdef PUK2
	int rate;
#endif
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
#ifdef SKILL_LOG
	int		before, after;
#endif

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_MERGEITEM_ENSURE;

    // 酒捞袍鄂狼 后 镑篮 乐阑鳖?
	emptyhaveitemindex = CHAR_findEmptyItemBox( ch);
	// 1雀肺 狐瘤绰 风橇
	while( 1 ) {
		int ret = getStringFromIndexWithDelim( data, "|", 1, buf, sizeof( buf));
		int consumptionrate;

		if( ret == FALSE) break;
		skillid = CHAR_getSkillId( ch, haveskill);
		skillindex = SKILL_getSkillIndex(skillid);
		if( ! SKILL_CHECKINDEX( skillindex)) break;
		skilllv = CHAR_getSkillLevel( ch, haveskill);
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
		// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
		usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
		// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
		if( skilllv > usablelv){
			skilllv = usablelv;
		}
#endif 
		// 府滚胶 窍绊 乐阑鳖?
		if( ch->w.RebirthSkill ){
			skilllv++;
			// 老窜 弥措 10 饭骇肺 肛冕促"
			if( skilllv > SKILL_LEVEL_MAX_CRAFT ){
				skilllv = SKILL_LEVEL_MAX_CRAFT;
			}
		}
#endif
		// 酒捞袍饭矫乔狼 index甫 救促
		recipeid = atoi( buf);
#ifdef PUK2
		// 饭矫乔 扁撅窍绊 乐阑鳖?
		if( ! CHAR_checkRecipeFlg( ch, recipeid) ){
			// 飘罚胶吝?
			if( ch->w.RebirthSkill ){
				// 飘罚胶吝捞扼搁, 刘啊茄 父怒档 眉农茄促
				if( ! CHAR_checkTranceRecipeFlg( skillid, skilllv, recipeid ) ){
					break;
				}
			}else{
				break;
			}
		}
#else
		// 饭矫乔 扁撅窍绊 乐阑鳖?
		if( ! CHAR_checkRecipeFlg( ch, recipeid)) break;
#endif
		recipeindex = ITEM_RECIPE_getIndex( recipeid);
		if( ! ITEM_RECIPE_CHECKINDEX(recipeindex)) break;
		destitemid = ITEM_RECIPE_getInt( recipeindex, ITEM_RECIPE_ITEMID);
		if( ! ITEM_CHECKITEMTABLE( destitemid)) break;

		// 胶懦 lv啊 格利狼 酒捞袍 饭骇焊促 何练窍搁 父甸 荐 绝促
		itemrank = ITEM_getlevelFromITEMtabl( destitemid);
		if( skilllv < itemrank) break;
		// FP 拌魂
		// fp 家厚啦
		consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
		if( consumptionrate <= 0 ) consumptionrate = 1;
//		amount_fp = (( 11 - skilllv)*10 + itemrank*10) * (consumptionrate * 0.01);

		// FP家厚樊篮 捞 窃荐肺何磐 秒秦 柯促.
		amount_fp = ITEM_CalcRecipiFp( itemrank, skilllv );
//		itemrank * 20 * ( 1.00 - (skilllv-1)*0.05) * (consumptionrate * 0.01);
		amount_fp *= consumptionrate * 0.01;

		// 夸府肺
		if( skillid == 215 ){
			// FP 利绢 亮篮 巴阑 厘厚 窍绊 乐阑鳖?
			if( TECH_MergeItem_KiguCheck( ch ) ){
				print( "moto= %d\n", amount_fp );
				amount_fp *= 0.9;
				print( "aoto= %d\n", amount_fp );
			}
		}
		if( amount_fp <= 0 ) amount_fp = 1;
#ifdef PUK2
		// 府滚胶矫俊绰 FP家厚甫 临牢促
		if( ch->w.RebirthSkill ){
			// 炼府·炼力绰 喊墨抛绊府 秒鞭
			if( skillid == 215 || skillid == 216 ){
				rate = SKILL_RebirthParam( REBIRTH_CATEGORY_MERGE, ch->w.RebirthLevel, REBIRTH_FP_RATE );
			}else{
				rate = SKILL_RebirthParam( REBIRTH_CATEGORY_CRAFT, ch->w.RebirthLevel, REBIRTH_FP_RATE );
			}
			amount_fp = ( amount_fp * rate + 50 ) / 100;
			// burst 矫埃狼 唱赣瘤 犬牢
			if( CHAR_FeverHaveTime(ch) <= 0 ) {
				// burst 鸥烙 何练且 锭绰 溜return
				goto TECH_MERGEITEM_ENSURE;
			}
		}
#endif
		if( ch->i.ForcePoint < amount_fp) {
			// FP 何练茄 版快绰 溜return
			goto TECH_MERGEITEM_ENSURE;
		}
		// 弊 胶懦俊 加窍绰 饭矫乔牢啊 绢恫啊甫 炼荤茄促
		// 胶懦肺何磐 酒捞袍饭矫乔狼 胶懦付促 抛捞喉狼 index甫 救促
		recipeskillindex = ITEM_RECIPE_getIndexBySkillIdOnSkillTbl( skillid);
		if( recipeskillindex == -1 ) break;
		ITEM_RECIPE_getRecipeIdOnSkillTbl( recipeskillindex, &recipelist, &recipelen);
		for( i = 0; i < recipelen; i ++ ) {
			// 老摹沁促.弊 胶懦俊 加窍绰 饭矫乔看促.
			if( recipelist[i] == recipeindex ) {
				break;
			}
		}
		if( i == recipelen ) break;

	// data 肺何磐 单捞磐甫 眠免茄促.
	// 啊傍狼 版快绰, data俊绰 recipe|item1|item2...|item5|itemB|
	// (客)苞 鞍捞 甸绢啊 乐促.itemB绰 绢叼俊 甸绢啊 乐阑鳖绰 葛弗促.
		for( i = 1; i <= ITEM_RECIPE_MAX+1; i ++ ) {
			int itemindex;
			int val;
			int ret = getStringFromIndexWithDelim( data, "|", i+1, buf, sizeof( buf));
			if( ret == FALSE ) break;
            val = atoi(buf) + ch->bagPage * CHAR_ITEMPAGENUM;
			if( val < CHAR_EQUIPPLACENUM || val >= CHAR_MAXITEMHAVE ) continue;
			itemindex = CHAR_getItemIndex( ch, val);
			if( ! ITEM_CHECKINDEX( itemindex)) continue;
			// 皑沥窍绊 乐绢 眉农
			if( ! ITEM_getInt( itemindex, ITEM_LEAKLEVEL)) continue;

			// 家犁 B牢啊 炼荤茄促
//			if( ITEM_MATERIAL_getIndex( ITEM_getInt( itemindex, ITEM_MATERIAL_WEAPON)) != -1 ||
//				ITEM_MATERIAL_getIndex( ITEM_getInt( itemindex, ITEM_MATERIAL_ARMOUR)) != -1 ||
//				ITEM_MATERIAL_getIndex( ITEM_getInt( itemindex, ITEM_MATERIAL_ACCESSORY)) != -1)
			if( ITEM_getInt( itemindex, ITEM_TYPE) == ITEM_JEWEL ||
				ITEM_getInt( itemindex, ITEM_TYPE) == ITEM_MATERIAL_B)
			{
				itemb = itemindex;
				haveitembindex = val;
			}
			else {
				// itemB啊 乐栏骨肺, ITEM_RECIPE_MAX啊 登绢 乐阑瘤档 葛福促.
				if( mergeitemmax < ITEM_RECIPE_MAX ) {
					mergehaveitemindex[mergeitemmax] = val;
					mergeitemid[mergeitemmax++] = itemindex;
				}
			}
		}
		{
			BOOL judgeflg = TRUE;
			// 力傍等 酒捞袍捞 饭矫乔客 老摹且瘤 绢冻瘤 炼荤茄促.
			for( i = 0; i < ITEM_RECIPE_MAX; i ++) {
				int atomitemid = ITEM_RECIPE_getInt( recipeindex, ITEM_RECIPE_A+i);
				if( ! ITEM_CHECKITEMTABLE( atomitemid)) continue;
//				print( "atomid=%d\n", atomitemid);
				for( j = 0; j < mergeitemmax; j ++ ) {
					int atomitemrequire;
					atomitemrequire = ITEM_RECIPE_getInt( recipeindex, ITEM_RECIPE_REQUIRE_A+i);

//					print( "id = %d\n", ITEM_getInt( mergeitemid[j], ITEM_ID));
					// 酒捞袍 ID啊 老摹 肚茄 荐樊阑 盲快绊 乐阑鳖
					if( ITEM_getInt( mergeitemid[j], ITEM_ID) == atomitemid &&
						ITEM_getInt( mergeitemid[j], ITEM_REMAIN) >= atomitemrequire )
					{
						// 傈俊 炼荤沁阑 锭俊 酒捞袍鄂狼 后 镑捞 绝菌带 锭.
						// 鞘夸 荐樊栏肺 啊瘤绊 乐绰 荐啊 窃膊扼搁, 后 镑捞 登骨肺,
						// 弊 磊府家甫 扁撅秦 敌促.
						if( emptyhaveitemindex == -1
							&& ITEM_getInt( mergeitemid[j], ITEM_REMAIN) == atomitemrequire)
						{
							emptyhaveitemindex = mergehaveitemindex[j];
						}
						break;
					}
				}
				if( j == mergeitemmax) {
					judgeflg = FALSE;
					break;
				}
			}
			if( ! judgeflg) break;
			// 酒捞袍狼 后 镑捞 绝菌促.捞巴栏肺 酒捞袍捞扼电瘤啊 绝绢瘤搁(磊) 阂街秦辑
			// 酒公巴档 窍瘤 臼绊 return 茄促.
			if( emptyhaveitemindex < 0 ) {
				char szBuffer[256];
				// "酒捞袍狼 鄂狼 后 镑捞 绝扁 锭巩俊 钦己 饶狼 酒捞袍阑 啊龙 荐 绝嚼聪促."
				translate_format(szBuffer, 0, LANG_MSG_TECH_C_019 );
				SYSTEMMSG( ch, szBuffer );
				goto TECH_MERGEITEM_ENSURE;
			}
#ifdef ANIT_CHEATING_SET
			if(cgmsvcf.anitcheatingflg != 0){
				if ( CHECK_ProduceTime(ch) == TRUE )
					goto TECH_MERGEITEM_ENSURE;
			}
#endif
		}
		// 捞巴栏肺 弥历 炼扒篮 爱眠绢脸促绊 秦, 拌魂俊 甸绢埃促.
		// 己傍 犬伏 拌魂规过篮 矫规辑甫 曼绊狼 老.

		flg = TECH_MergeItem_CalcProb( ch, skillid, haveskill );

#if 0	// 烤 己傍啦
		prob = skilllv *10 +10 +
			C_DEX * 0.1 +
			C_STM/15 +
			C_INT * 0.05 +
			( skilllv - itemrank)*10 +
			( (itemb != -1 ? ITEM_getInt( itemb, ITEM_RANK):0) / skilllv) * -10 +
			ch->i.Injury*-1 +
			ITEM_getBaseFailedProbFromITEMtabl( destitemid) +
			CHAR_getSkillSuccessProb( ch, haveskill)
			;
		if( prob<= 0 ) break;
		// 己傍
		if( prob >= 100 || RAND(1,100) <= prob ) {
			flg = TRUE;
			break;
		}
#endif
		break;
	}

	// 己傍
	if( flg) {
		// 酒捞袍 累己.
		newitemindex = ITEM_makeItemAndRegist( destitemid);
		// 家犁 B狼 瓤苞甫 辰促
#ifdef PUK2
		// 府滚胶狼 瓤苞甫 歹窍瘤 臼绊 焊籍阑 钦己
		TECH_MergeItem_addItemB( ch, newitemindex, itemb, 0);
#else
		TECH_MergeItem_addItemB( newitemindex, itemb);
#endif

#ifdef MONSTER_HELP_TECH
		{
			Char *pethelp=NULL;
			//局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促	
			pethelp=TECH_CheckPetHelp(ch, itemrank, PETHELP_MERGE);

			//捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
			if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
			{
				//局肯悼拱 秋橇俊 狼茄 何啊啊摹, 阁农府狼 郴备档狼 拌魂阑 角矫茄促
				TECH_MergeItem_addNeoParam( ch, skillid, newitemindex, TRUE );
				if(skillid==215||skillid==216)
				{
					TECH_PetHelpForCooking(ch, newitemindex, pethelp);
				}
				else
				{
					TECH_PetHelpForMerge(ch, newitemindex, pethelp);
				}
			}
			else
			{
				TECH_MergeItem_addNeoParam( ch, skillid, newitemindex, FALSE );
			}
		}
#else
		// 茄摸 歹 胶怕固呈俊 狼秦 炼陛 己瓷捞 坷甫鳖?
		TECH_MergeItem_addNeoParam( ch, skillid, newitemindex );
#endif

#ifdef PUK2
		// 府滚胶 窍绊 乐绢?
		if( ch->w.RebirthSkill ){
			// 府滚胶 焊沥阑 歹茄促
			TECH_MergeItem_RebirthParam( newitemindex, ch->w.RebirthLevel );
		}
#endif
		// 夸府狼 版快绰 漂荐 瓤苞
		if( skillid == 215 ){
			// 酒瘤畴葛配 厘厚 窍绊 乐绢?
			if( TECH_MergeItem_CheckAjinomoto( ch ) == TRUE ){
				// FP雀汗樊 疵赴促
				TECH_MergeItem_Ajinomoto( newitemindex );
			}
#ifdef PUK2
//			// 府滚胶 窍绊 乐绢?
//			if( ch->w.RebirthSkill ){
//				// 府滚胶 焊沥阑 歹茄促
//				TECH_MergeItem_RebirthParam( newitemindex, ch->w.RebirthLevel );
//			}
#endif
		}

        int itembNo = -1;
		// 100%皑沥登绊 乐促.
		ITEM_setInt( newitemindex, ITEM_LEAKLEVEL, 1);
#ifdef _OPERATION_APPEND_JEWEL
		// 焊籍 眠啊 啊瓷 敲贰弊甫 技飘 茄促
		if( ! ITEM_CHECKINDEX( itemb)){
			ITEM_setInt( newitemindex, ITEM_OTHERFLG,
				( ITEM_getInt( newitemindex, ITEM_OTHERFLG) | ITEM_OTHERFLG_CANADDJEWEL));
		} else {
            itembNo = ITEM_getInt(itemb, ITEM_ID);
        }
#endif /* _OPERATION_APPEND_JEWEL */


		// 家犁 酒捞袍 瘤款促
		deleteatomstring =
			TECH_MergeItem_deleteAtom(
				ch,
				haveitembindex,
				itemb,
				mergehaveitemindex,
				mergeitemid,
				mergeitemmax,
				recipeindex);

		// 酒捞袍 殿废
		if( ITEM_CHECKINDEX( newitemindex)) {
			ITEM_setInt( newitemindex, ITEM_MERGEFLG, TRUE);
			CHAR_setItemIndex( ch, emptyhaveitemindex, newitemindex);
			CHAR_sendItemDataOne( ch, emptyhaveitemindex);
			// 酒捞袍阑 涝荐茄 肺弊甫 秒茄促.
			LogItem(
				ch->cu.player.CdKey,	// CD虐
				ch->c.Name, /* 某腐磐疙 */
				destitemid,  /* 酒捞袍 锅龋 */
				"MargeCreate",
				ch->i.MapId, ch->i.Floor, ch->i.X, ch->i.Y
			);
			if(NL_DATA_EXTEND.use[NL_MERGEITEMFUNC])
				NPC_Lua_NL_MergeItemEventCallBack(ch, skillid, skilllv,newitemindex, itembNo);

		}
		// 胶懦 版氰摹 秒垫
		exp = SKILLEXP_getProductSkillExp( ch, skillindex, recipeindex,
											ch->player_addon->Skill[haveskill].SkillLevel,
											ch->iu.player.Job
											);
#ifdef SKILL_LOG
		// 荤侩傈狼 版氰摹
		before = CHAR_getSkillExp( ch, haveskill );
		// 积魂拌狼 胶懦 版氰摹俊 啊魂茄促
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp, &stm, &dex, &intelligence );
		// 荤侩饶狼 版氰摹
		after = CHAR_getSkillExp( ch, haveskill );
		// 胶懦 荤侩矫狼 肺弊甫 免仿
		LogSkill(
			ch->cu.player.CdKey, ch->iu.player.RegistNumber, ch->c.Name,
			skillid, before, after, exp );
#else
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp, &stm, &dex, &intelligence );
#endif
	}
	// 角菩扼搁 家犁甫 酪阑瘤档 葛弗促.
	else {
#if 0
		int prob;
		// 家角啦 拌魂
		prob = ITEM_getBaseFailedProbFromITEMtabl( destitemid)*-1 + skilllv *5*-1;
		print( "del prob= %d\n", prob);
		// 绝绢柳促.
		if( prob > 0 && prob <= RAND( 1,100)) {
			// 家犁 酒捞袍 瘤款促
			deleteatomstring = TECH_MergeItem_deleteAtom(
					ch, 	// 某腐磐狼 器牢磐.
					haveitembindex,	// 家犁 B狼 酒捞袍阑 持绊 乐绰 厘家.
					itemb,			// 家犁 B狼 酒捞袍狼 牢郸胶.
					mergehaveitemindex, // 钦眉 矫虐绰 酒捞袍阑 持绊 乐绰 厘家 府胶飘
					mergeitemid,	// 弊 酒捞袍狼 牢郸胶 府胶飘
					mergeitemmax, 	// 弊 酒捞袍捞 割俺 乐阑鳖.
					recipeindex		// 饭矫乔抛捞阂狼 牢郸胶.
				);
		}
#endif
	}
	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE)
	{
	// 疙己摹 拌魂

		//撅瘤肺 ID甫 炼荤茄促
		if(skillid == 215 || skillid == 216){
			//炼府, 炼力
			fame = TECH_getFame3( ch, skilllv, itemrank, flg, 1);
		}else{
			//啊傍
			fame = TECH_getFame3( ch, skilllv, itemrank, flg, 0);
		}

		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}

	CHAR_complianceParameter( ch);
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;

		// 夸府, 炼力绰 喊档 扁霖栏肺 皑家 矫埃阑 拌魂
		if(skillid == 215 || skillid == 216){
			time = SKILL_RebirthParam( REBIRTH_CATEGORY_MERGE, ch->w.RebirthLevel, REBIRTH_TIME );
		}else{
			time = SKILL_RebirthParam( REBIRTH_CATEGORY_CRAFT, ch->w.RebirthLevel, REBIRTH_TIME );
		}
		CHAR_FeverTimeConsume( ch, time );
	}
#endif

	// 惑贸 眉农 // 胶懦 饭骇狼 馆狼 犬伏肺 惑贸甫 涝绰促.
	if( skilllv >= 2 && RAND( 1,100000 ) <= cgmsvcf.mergeInjuryRate ) {
		char szBuffer[256];
		int iWork = skilllv*10;	// 胶懦 饭骇*10鳖瘤狼 惑贸甫 涝绰促.
		if( iWork >= 50 ) iWork = 50;		// 促父 50鳖瘤狼 惑贸绰 窍瘤 臼绰促.
		ch->i.Injury += RAND( 1, iWork );	// 己傍窍绊 乐促搁＋1?
		if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
		CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
		injuryflg = TRUE;
		translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
		SYSTEMMSG( ch, szBuffer );
#ifdef PUK2
		// 惑贸 惑怕甫 林困俊 舅赴促
		CHAR_sendInjuryEffect( ch );
#endif
	}

	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif

	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%d|%s",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg,
				  (newitemindex == -1 ?  -1: ITEM_getInt( newitemindex, ITEM_BASEIMAGENUMBER)),
				  (deleteatomstring == NULL ?  "": deleteatomstring));
		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return TRUE;
 TECH_MERGEITEM_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%d|%s",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg,
				  (newitemindex == -1 ?  -1: ITEM_getInt( newitemindex, ITEM_BASEIMAGENUMBER)),
				  (deleteatomstring == NULL ?  "": deleteatomstring));
		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return FALSE;
}

//-------------------------------------------------------------------------
// 酒捞袍 皑沥
//-------------------------------------------------------------------------
int TECH_JudgeItem( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	int skilllv;
	int itemindex;
	int prob;
#ifdef TEST
	char buf[256];
#endif
	int itemrank;
	int skillindex;
	int amount_fp = 0;  // 家厚 FP
	int consumptionrate;
	int fd;
	int fame = 0;
	int exp = 0;
	BOOL injuryflg = FALSE;

	BOOL levelup = FALSE;
	BOOL flg = FALSE;
	int stm = 0, dex = 0, intelligence = 0;
#ifdef PUK2
	int rate;
#endif
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
#ifdef SKILL_LOG
	int		before, after;
#endif

	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ) return FALSE;
	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_JUDGEITEM_ENSURE;
	skilllv = CHAR_getSkillLevel( ch, haveskill);
	if( skilllv == -1 ) goto TECH_JUDGEITEM_ENSURE;
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
#ifdef PUK2
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX_CRAFT ){
			skilllv = SKILL_LEVEL_MAX_CRAFT;
		}
	}
#endif

	// toindex俊 haveitemindex啊 甸绢啊 乐促.
    toindex += ch->bagPage * CHAR_ITEMPAGENUM;
	itemindex = CHAR_getItemIndex( ch, toindex);
	if( ! ITEM_CHECKINDEX( itemindex)) goto TECH_JUDGEITEM_ENSURE;
	if( ITEM_getInt( itemindex, ITEM_LEAKLEVEL)) {
		// "弊 酒捞袍篮 国结 皑沥登绊 乐嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_004));
		goto TECH_JUDGEITEM_ENSURE;
	}
	itemrank = ITEM_getInt( itemindex, ITEM_LEVEL);
	// FP 拌魂
	// fp 家厚啦
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;
	// 角力狼 FP
	amount_fp = (itemrank*10) * (consumptionrate * 0.01);

	if( amount_fp <= 0 ) amount_fp = 1;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_JUDGE, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_JUDGEITEM_ENSURE;
		}
	}
#endif
	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_JUDGEITEM_ENSURE;
	}
#ifdef ANIT_CHEATING_SET
	if(cgmsvcf.anitcheatingflg != 0){
		if ( CHECK_ProduceTime(ch) == TRUE )
			goto TECH_JUDGEITEM_ENSURE;
	}
#endif
	if( skilllv >= itemrank ){
		// 犬伏
		prob = 50 + 10*skilllv - 10*itemrank + CHAR_getSkillSuccessProb( ch, haveskill);
		prob *= 100;	// 1父 盒狼 1窜困肺 窍骨肺 捞犯霸 茄促.
		// 焊沥摹甫 拌魂秦 敲矾胶 茄促.
		prob += ( ( C_DEX - 50) * 0.16 + ( C_INT - 50 ) * 0.84 ) * 0.1;
		// 己傍
		if( RAND( 1,100*100) < prob) {
			flg = TRUE;
		}
	}

	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE)
	{
		// 疙己摹 拌魂
		fame = TECH_getFame1( ch, skilllv, itemrank, flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}

	CHAR_complianceParameter( ch);


	if( flg ) { // 己傍窍绊 乐栏搁(磊).
		ITEM_setInt( itemindex, ITEM_LEAKLEVEL, 1);
		CHAR_sendItemDataOne( ch, toindex);
		// 家瘤 吝蜡瓤苞 酒捞袍捞扼搁, 康氢阑 罐霸 茄促
		if( ITEM_getInt( itemindex, ITEM_ABLEEFFECTBETWEENHAVE)) {
			/*  颇扼固磐 瓤苞甫 辰促    */
			CHAR_send_CP_String( ch,
								 CHAR_CP_HP|CHAR_CP_MAXHP|
								 CHAR_CP_FORCEPOINT|CHAR_CP_MAXFORCEPOINT|
								 CHAR_CP_ATK|CHAR_CP_DEF|
								CHAR_CP_AGILITY|CHAR_CP_MAGICPOWER|
								 CHAR_CP_RECOVERY|CHAR_CP_CHARM|
								 CHAR_CP_STAMINA|CHAR_CP_DEX|
								 CHAR_CP_INTELLIGENCE|
								 CHAR_CP_FAME|
								 CHAR_CP_EARTH|
								 CHAR_CP_WATER|CHAR_CP_FIRE|
								 CHAR_CP_WIND
				);
			CHAR_send_CP2_String( ch, CHAR_CP2_ALL);
		}
		// 胶懦 版氰摹 秒垫
		exp = SKILLEXP_getProductSkillExp( ch , skillindex, itemrank,
											ch->player_addon->Skill[haveskill].SkillLevel,
											ch->iu.player.Job
											);
#ifdef SKILL_LOG
		// 荤侩傈狼 版氰摹
		before = CHAR_getSkillExp( ch, haveskill );
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
		// 荤侩饶狼 版氰摹
		after = CHAR_getSkillExp( ch, haveskill );
		// 胶懦 荤侩矫狼 肺弊甫 免仿
		LogSkill(
			ch->cu.player.CdKey, ch->iu.player.RegistNumber, ch->c.Name,
			CHAR_getSkillId( ch, haveskill), before, after, exp );
#else
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
#endif
	}
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_JUDGE, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif
	// 惑贸 眉农
	if(  RAND( 1,100) < 1/(skilllv*3+2)) {
		char szBuffer[256];
		if( flg) ch->i.Injury += 1;
		else ch->i.Injury += 5;
		if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
		CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
		injuryflg = TRUE;
		translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
		SYSTEMMSG( ch, szBuffer );
#ifdef PUK2
		// 惑贸 惑怕甫 林困俊 舅赴促
		CHAR_sendInjuryEffect( ch );
#endif
	}


#ifdef MONSTER_HELP_TECH
	{
		Char *pethelp=NULL;
		//局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促	
		pethelp=TECH_CheckPetHelp(ch, itemrank, PETHELP_REPAIR);

		//捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
		if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
		{
			//农府胶呕狼 郴己摹 拌魂阑 角矫茄促
			TECH_PetHelpForJudgeItem(ch, itemindex, pethelp);

			//2004-11-20 -->局肯悼拱 秋橇 窍磊 函版俊 狼茄促
			//TECH_PetHelpForRepair(ch, itemindex, pethelp);
		}
	}
#endif


	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif


	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

/*  酒捞袍狼 皑沥 饶俊 搬苞 焊绊窍绰 巴篮 弊促瘤 秦搬捞 登瘤 臼扁 锭巩俊 弊父敌促.
	{
		char szMessage[256];
		if( flg ){
			// "%s 绰 %s 狼 皑沥俊 己傍沁嚼聪促."
			snprintf( szMessage, sizeof( szMessage ),
				TMSG(LANG_MSG_CHAR_SKILL_C_013),
				ch->c.Name, ITEM_getAppropriateName( itemindex) );
			// 林困俊 矫胶袍 皋技瘤
			SYETEMMSG_ToArround( szMessage, ch->i.MapId, ch->i.Floor,
				ch->i.X, ch->i.Y, 1 );
		}else{
			// "%s 绰 %s 狼 皑沥俊 角菩沁嚼聪促."
			snprintf( szMessage, sizeof( szMessage ),
				TMSG(LANG_MSG_CHAR_SKILL_C_014),
				ch->c.Name, ITEM_getAppropriateName( itemindex) );
			// 林困俊 矫胶袍 皋技瘤
			SYETEMMSG_ToArround( szMessage, ch->i.MapId, ch->i.Floor,
				ch->i.X, ch->i.Y, 1 );
		}
	}

*/

	return TRUE;
 TECH_JUDGEITEM_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);

	}
	return FALSE;
}
//-------------------------------------------------------------------------
// 阿牢狼 胶懦
//-------------------------------------------------------------------------
int TECH_MarkItem( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	int skilllv;
	int itemindex;
//	int prob;
#ifdef TEST
	char buf[256];
#endif
//	int itemrank;
	int skillindex;
	BOOL flg = FALSE;
//	int place;
	int amount_fp;
//	int consumptionrate;
	int fd;
	int fame=0;
	int exp = 0;
	BOOL levelup = FALSE;
	int stm = 0, dex = 0, intelligence = 0;
	int otherflg = 0;
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
	int rate;
#endif


	BOOL injuryflg = FALSE;

	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ) return FALSE;

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_MARKITEM_ENSURE;
	skilllv = CHAR_getSkillLevel( ch, haveskill);
	if( skilllv == -1 ) goto TECH_MARKITEM_ENSURE;
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX ){
			skilllv = SKILL_LEVEL_MAX;
		}
	}
#endif
    toindex += ch->bagPage * CHAR_ITEMPAGENUM;

	// toindex俊 haveitemindex啊 甸绢啊 乐促.
	itemindex = CHAR_getItemIndex( ch, toindex);
	if( ! ITEM_CHECKINDEX( itemindex)) goto TECH_MARKITEM_ENSURE;

	//阿牢捞 场抄 惑怕
	if( (ITEM_getInt(itemindex, ITEM_OTHERFLG) & ITEM_OTHERFLG_INCUSE) &&
		strlen( ITEM_getChar( itemindex, ITEM_MEMO)) != 0)
	{
		//磊脚捞 穿弗 巴捞扼搁 OK
		if(strcmp( ITEM_getChar(itemindex, ITEM_RENAMECDKEY) , ch->cu.player.CdKey) != 0){
			// "弊 酒捞袍篮 国结 阿牢 登绊 乐嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_005));
			goto TECH_MARKITEM_ENSURE;
		}
	}


	if( ! ITEM_getInt( itemindex, ITEM_LEAKLEVEL)) {
		// "皑沥登绢 乐瘤 臼篮 酒捞袍俊绰 阿牢 且 荐 绝嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_006));
		goto TECH_MARKITEM_ENSURE;
	}

	if(ITEM_getInt(itemindex, ITEM_MAXREMAIN) > 1)
	{
		// "弊 酒捞袍俊绰 阿牢 且 荐 绝嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_007));
		goto TECH_MARKITEM_ENSURE;
	}


#if 0
	place = ITEM_getEquipPlace( itemindex);
	if( place == CHAR_DECORATION1 || place == -1 ) {
		// "弊 酒捞袍俊绰 阿牢 且 荐 绝嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_007));
		goto TECH_MARKITEM_ENSURE;
	}
#endif


#if 0
	itemrank = ITEM_getInt( itemindex, ITEM_LEVEL);
	// FP 拌魂
	// fp 家厚啦
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;
	amount_fp = (( 11 - skilllv)*10 + itemrank*10) * (consumptionrate * 0.01);
	if( amount_fp <= 0 ) amount_fp = 1;
#endif
	//FP绰 10 绊沥
	amount_fp = 10;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_MARK, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		// burst 矫埃狼 唱赣瘤 犬牢
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_MARKITEM_ENSURE;
		}
	}
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_MARKITEM_ENSURE;
	}

#if 0
	if( skilllv >= itemrank ) {

		// 犬伏 拌魂
		prob = skilllv *10+10 +
			( skilllv - ITEM_getInt( itemindex, ITEM_LEVEL))*10 +
			+ ITEM_getInt( itemindex, ITEM_BASEFAILEDPROB)
			+ CHAR_getSkillSuccessProb( ch, haveskill)
			;

		prob *= 100;	// 1父 盒狼 1窜困肺 窍骨肺 捞犯霸 茄促.
		// 焊沥摹甫 拌魂秦 敲矾胶 茄促.
		prob += ( ( C_DEX - 50) * 0.84 + ( C_INT - 50 ) * 0.16 ) * 0.1;

		// 己傍
		if( RAND( 1,100*100) < prob) {
			flg = TRUE;
		}
	}
#endif

#if 0
	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE)
	{
		// 疙己摹 拌魂
		fame = TECH_getFame1( ch, skilllv, itemrank, flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}
#endif
	CHAR_complianceParameter( ch);

	//例措 己傍
	flg = TRUE;
	if( flg) {
		//被捞 捞风绢扼搁 阿牢疙阑 瘤款促
		if(strcmp(data ,"") == 0){
			//捞抚阑 瘤款促
			ITEM_setChar( itemindex, ITEM_MEMO, "");
			//敲贰弊甫 瘤款促
			otherflg = ITEM_getInt(itemindex, ITEM_OTHERFLG);
			otherflg = otherflg & ~ ITEM_OTHERFLG_INCUSE;
			ITEM_setInt(itemindex, ITEM_OTHERFLG, otherflg);

			//茄 凰郴哥 磊何窃 贰弊绰 瘤款促
			otherflg = otherflg &~(ITEM_OTHERFLG_HANKO);
			ITEM_setInt(itemindex, ITEM_OTHERFLG, otherflg);

			//CDKEY甫 技飘 茄促
			ITEM_setChar( itemindex, ITEM_RENAMECDKEY, "");

		}else{
			ITEM_setChar( itemindex, ITEM_MEMO, data);
			//阿牢 敲贰弊甫 技款促
			otherflg = ITEM_getInt(itemindex, ITEM_OTHERFLG);
			otherflg |= ITEM_OTHERFLG_INCUSE;
			//茄 凰郴哥 磊何窃 贰弊绰 瘤款促
			otherflg = otherflg &~(ITEM_OTHERFLG_HANKO);
			ITEM_setInt(itemindex, ITEM_OTHERFLG, otherflg);
			//CDKEY甫 技飘 茄促
			ITEM_setChar( itemindex, ITEM_RENAMECDKEY, ch->cu.player.CdKey);


		}
//		CHAR_sendItemDataOne( ch, toindex);
		// 胶懦 版氰摹 秒垫
#if 0
		exp = SKILLEXP_getProductSkillExp( ch, skillindex, itemrank,
										ch->player_addon->Skill[haveskill].SkillLevel,
										ch->iu.player.Job
										);
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
//		exp = SKILLEXP_AmplifySkillExp( ch  , skillindex, exp);
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
//		CHAR_setSkillExp( ch, haveskill, CHAR_getSkillExp( ch, haveskill) + exp);
#endif


		CHAR_sendItemDataOne( ch, toindex);
#ifdef TEST
		{
			char buf[128];
			translate_format(buf, sizeof( buf), LANG_MSG_AUTOMATIC_359, exp);
			SYSTEMMSG( ch, buf);
		}
#endif
	}
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_MARK, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif

#if 0
	// 惑贸 眉农
	if(  RAND( 1,100) < 1/(skilllv*3+2)) {
		char szBuffer[256];
		if( flg) ch->i.Injury += 1;
		else ch->i.Injury += 5;
		if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
		CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
		injuryflg = TRUE;
		translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
		SYSTEMMSG( ch, szBuffer );

	}
#endif

	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif

	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);

	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return TRUE;
 TECH_MARKITEM_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return FALSE;
}
//-------------------------------------------------------------------------
// 荐府狼 胶懦
//-------------------------------------------------------------------------
int TECH_RepairItem( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	int skilllv;
	int itemindex;
	int prob;
#ifdef TEST
	char buf[256];
#endif
	int itemrank;
	int skillindex;
	int techindex;
	int amount_fp;
	int consumptionrate;
	BOOL flg = FALSE;
	int place;
	int option;
	int maxdur, dur;
	int fd;
	int fame=0;
	int exp = 0;
	int iItemType = 0;
	BOOL levelup = FALSE;
	BOOL injuryflg = FALSE;
	int stm = 0, dex = 0, intelligence = 0;
#ifdef PUK2
	int rate;
#endif
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
#ifdef SKILL_LOG
	int		before, after;
#endif

#ifdef MONSTER_HELP_TECH
	Char *pethelp=NULL;
#endif

	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ) return FALSE;

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_REPAIRITEM_ENSURE;
	skilllv = CHAR_getSkillLevel( ch, haveskill);
	if( skilllv == -1 ) goto TECH_REPAIRITEM_ENSURE;
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX_CRAFT ){
			skilllv = SKILL_LEVEL_MAX_CRAFT;
		}
	}
#endif
    toindex += ch->bagPage * CHAR_ITEMPAGENUM;

	// toindex俊 haveitemindex啊 甸绢啊 乐促.
	itemindex = CHAR_getItemIndex( ch, toindex);
	if( ! ITEM_CHECKINDEX( itemindex)) goto TECH_REPAIRITEM_ENSURE;
	techindex = TECH_getTechIndex( CHAR_getTechId( ch, haveskill, havetech));
	if( techindex == -1 ) goto TECH_REPAIRITEM_ENSURE;

	if( ! ITEM_getInt( itemindex, ITEM_LEAKLEVEL)) {
		// "皑沥登绢 乐瘤 臼篮 酒捞袍篮 荐府 且 荐 绝嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_008));
		goto TECH_REPAIRITEM_ENSURE;
	}
	// 酒捞袍狼 厘厚 俺家狼 辆幅甫 炼荤茄促.
	place = ITEM_getEquipPlace( itemindex);
	if( place == CHAR_DECORATION1 || place == -1 ) {
		// "弊 酒捞袍篮 荐府 且 荐 绝嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_009));
		goto TECH_REPAIRITEM_ENSURE;
	}
	// 可记阑 炼荤茄促.公扁 荐府唱 规绢侩 扁备 荐府牢啊.0捞 公扁.1 捞 规绢侩 扁备.
	option = atoi( TECH_getChar( techindex, TECH_OPTION));
	// 酒捞袍狼 辆幅
	iItemType = ITEM_getInt( itemindex, ITEM_TYPE );
	// 公扁 荐府锭.
	if( option == 0 ) {
		// 公扁扼搁 Ok.
        if ((ITEM_SWORD <= iItemType && iItemType <= ITEM_BOOMERANG) || ITEM_IsExtItemWeapon(iItemType)) {
        } else {
            // 公扁 捞寇扼搁 救蹬
            // "捞 胶懦俊辑绰 荐府 且 荐 绝嚼聪促."
            SYSTEMMSG(ch, TMSG(LANG_MSG_CHAR_SKILL_C_010));
            goto TECH_REPAIRITEM_ENSURE;
        }
	}
	// 规绢侩 扁备 荐府锭.
	else if( option == 1 ) {
		// 规绢侩 扁备 捞寇扼搁 救蹬.
        if ((ITEM_SHIELD <= iItemType && iItemType <= ITEM_SHOES) || ITEM_getEquipPlace(iItemType) < CHAR_DECORATION1) {
        } else {
            // "捞 胶懦俊辑绰 荐府 且 荐 绝嚼聪促."
            SYSTEMMSG(ch, TMSG(LANG_MSG_CHAR_SKILL_C_010));
            goto TECH_REPAIRITEM_ENSURE;
        }
	}
	maxdur = ITEM_getInt( itemindex, ITEM_MAXDURABILITY);
	dur = ITEM_getInt( itemindex, ITEM_DURABILITY);
	if( maxdur == dur ) {
		// "捞 酒捞袍篮 颊惑窍绊 乐嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_011));
		goto TECH_REPAIRITEM_ENSURE;
	}
	itemrank = ITEM_getInt( itemindex, ITEM_LEVEL);
	// FP 拌魂
	// fp 家厚啦
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;
	// 角力狼 家厚樊
	amount_fp = itemrank*10 * (consumptionrate * 0.01);
	if( amount_fp <= 0 ) amount_fp = 1;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_REPAIR, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		// burst 矫埃狼 唱赣瘤 犬牢
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_REPAIRITEM_ENSURE;
		}
	}
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_REPAIRITEM_ENSURE;
	}
#ifdef ANIT_CHEATING_SET
	if(cgmsvcf.anitcheatingflg != 0){
		if ( CHECK_ProduceTime(ch) == TRUE )
			goto TECH_REPAIRITEM_ENSURE;
	}
#endif
	if( skilllv >= itemrank ) {
		// 犬伏
		prob = 50 + 10*skilllv - 10*itemrank + CHAR_getSkillSuccessProb( ch, haveskill);
		prob *= 100;	// 1父 盒狼 1窜困肺 窍骨肺 捞犯霸 茄促.
		prob += ( ( C_STM - 50) * 0.5 + ( C_DEX - 50 ) * 0.5 ) * 0.1;

#ifdef MONSTER_HELP_TECH
		//局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促
		pethelp=TECH_CheckPetHelp(ch, itemrank, PETHELP_REPAIR);
		if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
		{
			//己傍啦俊 颊夯促
			prob +=REPAIR_ADDPOINT; 
		}
#endif
		// 己傍
		if( RAND( 1,100*100) < prob) {
			flg = TRUE;
		}
	}
	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE)
	{
		// 疙己摹 拌魂
		fame = TECH_getFame1( ch, skilllv, itemrank, flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}
	CHAR_complianceParameter( ch);
	if( flg) {
#ifdef TEST
		{
			char buf[128];
			snprintf( buf, sizeof( buf), "(dur(%d) - maxdur(%d))/2 + dur(%d)", dur, maxdur, dur);
			SYSTEMMSG( ch, buf);
		}
#endif


#ifdef MONSTER_HELP_TECH
		//捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
		if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
		{
			//农府胶呕狼 郴己摹 拌魂阑 角矫茄促
			TECH_PetHelpForRepair(ch, itemindex, pethelp);

			//DUR狼 临绢靛绰 规过俊 颊夯促
			maxdur = (dur+dur+maxdur+maxdur+maxdur)*0.2;
		}
		else
		{
			maxdur = (maxdur - dur)*0.5 + dur;
		}
#else
		maxdur = (maxdur - dur)*0.5 + dur;
#endif

        maxdur = max(1, maxdur);

		ITEM_setInt( itemindex, ITEM_MAXDURABILITY, maxdur);
		ITEM_setInt( itemindex, ITEM_DURABILITY, maxdur);

		CHAR_sendItemDataOne( ch, toindex);
		// 胶懦 版氰摹 秒垫
		exp = SKILLEXP_getProductSkillExp( ch, skillindex, itemrank,
											ch->player_addon->Skill[haveskill].SkillLevel,
											ch->iu.player.Job
											);
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
#ifdef SKILL_LOG
		// 荤侩傈狼 版氰摹
		before = CHAR_getSkillExp( ch, haveskill );
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
		// 荤侩饶狼 版氰摹
		after = CHAR_getSkillExp( ch, haveskill );
		// 胶懦 荤侩矫狼 肺弊甫 免仿
		LogSkill(
			ch->cu.player.CdKey, ch->iu.player.RegistNumber, ch->c.Name,
			CHAR_getSkillId( ch, haveskill), before, after, exp );
#else
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
#endif
#ifdef TEST
		{
			char buf[128];
			translate_format(buf, sizeof( buf), LANG_MSG_AUTOMATIC_359, exp);
			SYSTEMMSG( ch, buf);
		}
#endif


	}
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_REPAIR, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif

	// 惑贸 眉农
	// 惑贸 眉农 // 胶懦 饭骇狼 馆狼 犬伏肺 惑贸甫 涝绰促.
	if(  RAND( 1,100) < (skilllv/2 ) ) {
		char szBuffer[256];
		if( flg) ch->i.Injury += 1;
		else ch->i.Injury += 5;
		if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
		CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
		injuryflg = TRUE;
		translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
		SYSTEMMSG( ch, szBuffer );
#ifdef PUK2
		// 惑贸 惑怕甫 林困俊 舅赴促
		CHAR_sendInjuryEffect( ch );
#endif
	}


	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif


	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return TRUE;

 TECH_REPAIRITEM_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return FALSE;
}
//-------------------------------------------------------------------------
//坷欺饭捞记 剧侥 C狼 FP家厚樊阑 拌魂(扁贱 单捞磐 价脚侩)
//钎矫锭 拌魂 饶狼 FP甫 钎矫窍骨肺 拌魂
//-------------------------------------------------------------------------
int TECH_getModifiFp(Char *ch, int haveskillindex, int techindex, int fp)
{
	int techlv;
	int skillid;

	skillid = CHAR_getSkillId( ch, haveskillindex );

	if( skillid == -1 ) return fp;
	if( skillid < 221 || skillid > 224) return fp;

	techlv = TECH_getInt( techindex, TECH_NECESSARYLV );

	switch( skillid){
		case 221:
			//函厘
//			fp = techlv * 10;
		break;

		case 222:
			//函脚
//			fp = techlv * 10;
		break;

		case 223:
			//览鞭 贸摹扼绊
//			fp = 60 - (ch->wu.player.ModIntelligence / 2) + techlv * 4;
			//モ函版
//			fp = techlv * 10;  捞巴篮 辆丰

		break;

		case 224:
			//摹丰
			//	fp = 60 - (ch->wu.player.ModIntelligence / 2) + techlv * 4;
		break;

	}


	return fp;



}
//胶懦 饭骇俊 狼茄 函脚 啊瓷 辆练
int MorphTribe[10][2] =
{
	{1, CHAR_TRIBE_HUMAN},
	{2, CHAR_TRIBE_BEAST},
	{3, CHAR_TRIBE_UNDEAD},
	{4, CHAR_TRIBE_FLY},
	{5, CHAR_TRIBE_INSECT},
	{6, CHAR_TRIBE_PLANT},
	{7, CHAR_TRIBE_AMORPHAS},
	{8, CHAR_TRIBE_METAL},
	{9, CHAR_TRIBE_DRAGON},
	{10, CHAR_TRIBE_EVIL}
};


//角菩 犬伏
#ifdef PUK2
static int Failure[11]={50,45,40,35,30,25,20,15,10,5,1};
#else
static int Failure[10]={50,45,40,35,30,25,20,15,10,5};
#endif

//-------------------------------------------------------------------------
// 函脚, 函厘狼 胶懦
//
//-------------------------------------------------------------------------
int TECH_Metamorphosis( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	Char *target;
	int action ;  // 0: 函厘 1: 函脚
	int skilllv = -1;          //
	int skillindex;
	int prob = 0;
	BOOL levelup = FALSE;
	int fame=0;
	int exp = 0;
	int stm = 0, dex = 0, intelligence = 0;
	int fd;
	int amount_fp;
	int techindex;
	int techid;
	int maxskilllv = 1;
	int i;
	int targetlv = 100;
	int work;
	int ver = 0;
	int tribe = 0;

#ifdef TEST
	char buf[256];
#endif
	BOOL flg = FALSE;
	
	int char_action = ch->w.Action;
#ifdef PUK2
	int rate;
#endif
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
#ifdef SKILL_LOG
	int		before, after;
#endif

	techid = CHAR_getTechId( ch, haveskill, havetech);
	techindex = TECH_getTechIndex( techid);
	if( ! TECH_CHECKINDEX( techindex)) return -1;
	skilllv = TECH_getInt( techindex, TECH_NECESSARYLV ) ;
	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ) return -1;
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
#ifdef PUK2
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX ){
			skilllv = SKILL_LEVEL_MAX;
		}
	}
#endif
	//MAX 胶懦 眉农
	if(maxskilllv < skilllv){
		maxskilllv = skilllv;
	}

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_METAMORPHOSIS_ENSURE;

    // 贸澜狼 角青锭俊绰 牢荐甫 欺胶 茄促.
	// TECH_WORKDATAINT1 俊 狼秦 檬扁拳登绊 乐绢 魄窜茄促.
	if( TECH_getWorkInt( array, TECH_WORKINTDATA1) == -1 ) {
		char *optionstring;
		optionstring = TECH_getChar( array, TECH_OPTION);
		action = atoi( optionstring);
		if( action != 0 ) action = 1;
		TECH_setWorkInt( array, TECH_METAMO_ID, atoi( optionstring));
	}
	action = TECH_getWorkInt( array, TECH_METAMO_ID);
    // 措惑狼 Char 器牢磐甫 秒垫茄促.
	// 惑措祈捞, 坷欺饭捞记 墨抛绊府 C狼 炼扒阑 盲快绊 乐阑鳖 犁眉农茄促.
	// 咯扁俊 坷扁鳖瘤, 惑怕啊 函拳窍绊 乐阑 啊瓷己捞 乐栏骨肺.
	target = TECH_getOperationC_Char( ch, toindex);
	if( target == NULL ) {
		goto TECH_METAMORPHOSIS_ENSURE;
	}
	// 磊扁 磊脚档 救蹬
	if( target == ch) goto TECH_METAMORPHOSIS_ENSURE;
	// 函厘锭狼 眉农.
	if( action == 0 ) {
#ifdef GUISE_DEBUG
		// 敲饭捞绢啊 酒聪搁 救蹬.
		if( target->i.WhichType != CHAR_TYPEPLAYER ) {
			goto TECH_METAMORPHOSIS_ENSURE;
		}
#endif
	}
	// 函脚锭狼 眉农
	else {
		if( target->i.WhichType != CHAR_TYPEPLAYER )
//		if( target->i.WhichType == CHAR_TYPEPET
//		|| target->i.WhichType == CHAR_TYPESTANDENEMY)
		{
			//拳惑 锅龋绰 monster狼 巴牢啊
			if(MORPH_CheckMonsterImageNumber( target) == FALSE){
				goto TECH_METAMORPHOSIS_ENSURE;	
			}
		}else{
			goto TECH_METAMORPHOSIS_ENSURE;
		}
	}

	//家厚 FP绰 10 绊沥
	amount_fp = 10;
	if( amount_fp <= 0 ) amount_fp = 1;
	// 厘厚 酒捞袍狼 FP家厚 版皑摹
	amount_fp *= ( 100 - CHAR_getSkillConsumptionRate( ch, haveskill)) * 0.01;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_METAMORPH, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		// burst 矫埃狼 唱赣瘤 犬牢
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_METAMORPHOSIS_ENSURE;
		}
	}
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_METAMORPHOSIS_ENSURE;
	}

	// 犬伏 拌魂
	prob = 100 - Failure[ skilllv - 1];

#ifdef SKILL_TEST
	prob = 100;
#endif
	// 厘厚 酒捞袍狼 己傍啦 函拳摹
	prob += CHAR_getSkillSuccessProb( ch, haveskill);

	// 己傍
	if( RAND( 0,99) < prob) {
		flg = TRUE;
	}

	if(debug_morph == 1) {
		//例措 己傍 葛靛
		flg = TRUE;
	}

	//函厘
	if(action == 0){
		//鸥百狼 某扼弊扼啊 橇饭捞具弊扼牢啊
		if(GUISE_GetGraNo( target) == FALSE){
	//		print("函厘 角菩");
			flg = FALSE;
		}
	}else{
		//函脚
		tribe = target->i.Tribe;

		//辆练阑 汲沥(绊沥利侩)
		if(target->i.WhichType != CHAR_TYPEPLAYER 
		&& target->i.WhichType != CHAR_TYPEPET
		)
		{
			tribe = MORPH_GetTribe(target, target->i.BaseImageNumber);
	//		print("辆练 = %d \n", tribe);
		}

		//胶懦 饭骇俊 狼秦 函脚且 荐 乐绰 辆练狼 荐啊 促福促
		for(i = 0;i < CHAR_TRIBENUM ; i++)
		{
			if(tribe == MorphTribe[i][1])
			{
	//			print("Morph=%d \n", MorphTribe[i][0]);
				targetlv = MorphTribe[i][0];
				break;
			}
		}

		//函脚 啊瓷 monster牢啊?
		if( targetlv > skilllv ) {
			flg = FALSE;
	//		print("辆练 促福促");
		}
		//欧飘坊飘绰 函脚且 荐 绝促
		if(target->i.BaseBaseImageNumber == 101406)
		{
			flg = FALSE;
		}
	}


	//咯扁辑 滚怜 眉农(XG2 捞寇绰 荤侩且 荐 绝促)(函脚, 函厘 傍烹)
	if(MORPH_CgVersionCheck( ch) == FALSE){
		flg = FALSE;
		ver = 1;
	}

	if(SKILL_ElationCheck(ch, skillindex) == TRUE){
		// 疙己摹 拌魂
		fame = TECH_getFame4( ch, maxskilllv, target->i.Lv, flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}

	if( flg) {

		ch->i.BaseBaseImageNumber = target->i.BaseBaseImageNumber;
		//蜡瓤 焊荐绰 100焊
		ch->wu.player.Metamo_Count = 100;
		// 胶懦 版氰摹 秒垫
		exp = SKILLEXP_getProductSkillExp( ch, skillindex, skilllv,
											ch->player_addon->Skill[haveskill].SkillLevel,
											ch->iu.player.Job
											);
#ifdef SKILL_LOG
		// 荤侩傈狼 版氰摹
		before = CHAR_getSkillExp( ch, haveskill );
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
		// 荤侩饶狼 版氰摹
		after = CHAR_getSkillExp( ch, haveskill );
		// 胶懦 荤侩矫狼 肺弊甫 免仿
		LogSkill(
			ch->cu.player.CdKey, ch->iu.player.RegistNumber, ch->c.Name,
			CHAR_getSkillId( ch, haveskill), before, after, exp );
#else
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
#endif
		ch->iu.player.OtherFlg &= ~CHAR_METAMO_NPC;
		CHAR_complianceParameter( ch);

		//葛嚼捞 敲饭捞绢 捞寇扼搁 「辑」肺 汲沥
		if(MORPH_CheckImageNumber( ch) == FALSE){
			char_action = CHAR_ACTACTIONSTAND;
		}

		CHAR_sendWatchEvent( ch->w.ObjIndex, char_action, NULL, 0, TRUE);
		ch->w.Action = char_action;		
		
		CHAR_sendCToArroundCharacter( ch->w.ObjIndex);
	}
	else {
		int ran = RAND(0,99);
		//盲泼 付过栏肺 角菩 汲沥捞扼搁 角菩矫挪促

		if(debug_morph == 1) {
			//例措 己傍 葛靛
			ran = 1;
		}else if(debug_morph == 2) {
			//罚待 角菩
			ran = 79;
		}else if(debug_morph == 3) {
			//肯傈 角菩
			ran = 99;
		}
		
		
		//角菩狼 版快
		// 函厘锭狼 眉农.
		if( action == 0 ) {
//			print("角菩 =%d ran=%d", MORPH_getGraNo( skilllv), ran);
			//滚怜 瞒捞绰 泅惑 蜡瘤
			if(ver == 1) ran = 0;

			if(ran < 50 ){
				//泅惑 蜡瘤
			}else if(ran < 80){
				//罚待 函厘
				work = GUISE_GetRandomGraNo(ch, skilllv);
				if(work != -1){
					 ch->i.BaseBaseImageNumber =  work;
					//蜡瓤 焊荐绰 100焊
					ch->wu.player.Metamo_Count = 100;
					ch->iu.player.OtherFlg &= ~CHAR_METAMO_NPC;
				}
			}else{
				//角菩 函脚
				work = GUISE_getGraNo( skilllv);
				if(work != -1){
					 ch->i.BaseBaseImageNumber =  work;
					//蜡瓤 焊荐绰 100焊
					ch->wu.player.Metamo_Count = 100;
					ch->iu.player.OtherFlg |= CHAR_METAMO_NPC;

				}
			}
		}else{
//			print("角菩 =%d ran=%d", MORPH_getGraNo( skilllv), ran);

			//滚怜 瞒捞绰 泅惑 蜡瘤
			if(ver == 1) ran = 0;

			if(ran < 50 ){
				//泅惑 蜡瘤
			}else if(ran < 80)
			{
				//罚待 函脚
				work = MORPH_GetRandomGraNo(ch, skilllv);
				if(work != -1){
					 ch->i.BaseBaseImageNumber =  work;
					//蜡瓤 焊荐绰 100焊
					ch->wu.player.Metamo_Count = 100;
					ch->iu.player.OtherFlg &= ~CHAR_METAMO_NPC;
				}
			}else{
				//角菩 函脚
				work = MORPH_getGraNo( skilllv);
				if(work != -1){
					 ch->i.BaseBaseImageNumber =  work;
					//蜡瓤 焊荐绰 100焊
					ch->wu.player.Metamo_Count = 100;
					ch->iu.player.OtherFlg |= CHAR_METAMO_NPC;

				}
			}
		}

		//葛嚼捞 敲饭捞绢 捞寇扼搁 「辑」肺 汲沥
		if(MORPH_CheckImageNumber( ch) == FALSE){
			char_action = CHAR_ACTACTIONSTAND;
		}

		CHAR_sendWatchEvent( ch->w.ObjIndex, char_action, NULL, 0, TRUE);
		ch->w.Action = char_action;		

		CHAR_complianceParameter( ch);
		CHAR_sendCToArroundCharacter( ch->w.ObjIndex);
	}
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_METAMORPH, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif
#ifdef PET_RIDE
	// 愁捞 扁备吝?
	if( ch->w.walk.ridePet != -1 ){
		// 函脚沁绰瘤? (Metamo_count 肺 魄沥)
		if( ch->wu.player.Metamo_Count > 0 ){
			// 愁捞 扁备甫 碍力 辆丰
			PET_stopRidePet( ch );
		}
	}
#endif

	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 ));
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif

	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;


	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, 100);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return TRUE;

TECH_METAMORPHOSIS_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, 100);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return FALSE;
}

//-----------------------------------------------------------------
// 览鞭 贸摹
//-----------------------------------------------------------------
// 埃龋荤 捞寇
int aiOukyuu_Prob_Normal_Tbl[] =   { 50,45,40,35,30,25,20,15,10,5 };
// 埃龋荤
int aiOukyuu_Prob_Nourse_Tbl[] =   { 75,69,63,57,51,45,39,33,27,21 };
// 埃龋荤＋酒捞袍狼 己傍啦
int aiOukyuu_Prob_Plus_Tbl[] = { 99,92,85,78,71,64,57,50,43,33 };
// 家厚 FP
//int aiOukyuu_Fp_Tbl[] = { 17,24,20,35,40,40,45,45,50 };
// 雀汗樊
int aiOukyuu_Value_Tbl[] = { 30,60,100,100,200,268,312,432,580,900 };


//-----------------------------------------------------------------
// 摹丰
//-----------------------------------------------------------------
// 狼荤 捞寇狼 厩拳啦
int aiChiryou_Prob_Normal_Tbl[][10] = {
	{ 50,45,40,35,30,25,20,15,10,5  },	// 闰祸(惑贸 饭骇 1锭)
	{ 85,75,70,67,63,60,57,45,35,20 },	// 畴尔(惑贸 饭骇 2锭)
	{ 99,84,74,70,66,62,58,54,50,45 },	// 焊扼祸(惑贸 饭骇 3锭)
	{ 99,99,99,99,99,90,85,82,79,70 },	// 弧碍(惑贸 饭骇 4锭)
};
// 狼荤狼 己傍啦
int aiChiryou_Prob_Doctor_Tbl[][10] = {
	{ 35,30,25,20,15,10, 5, 1, 1, 1 },	// 闰祸(惑贸 饭骇 1锭)
	{ 55,45,40,35,32,29,26,20,15, 5 },	// 畴尔(惑贸 饭骇 2锭)
	{ 80,65,55,52,49,46,43,40,30,20 },	// 焊扼祸(惑贸 饭骇 3锭)
	{ 90,80,76,72,68,64,60,56,50,40 },	// 弧碍(惑贸 饭骇 4锭)
};
// 狼荤＋酒捞袍狼 己傍啦
int aiChiryou_Prob_Plus_Tbl[][10] = {
	{ 15,10, 5, 1, 1, 1, 1, 1, 0, 0 },	// 闰祸(惑贸 饭骇 1锭)
	{ 30,25,21,18,16,13, 9, 5, 1, 1 },	// 畴尔(惑贸 饭骇 2锭)
	{ 55,50,45,40,35,33,31,29,20,10 },	// 焊扼祸(惑贸 饭骇 3锭)
	{ 80,70,65,60,55,50,45,40,30,20 },	// 弧碍(惑贸 饭骇 4锭)
};

// 雀汗樊
int aiChiryou_Value_Tbl[] = { 25,30,35,40,45,50,55,60,65,100 };

// 瘤沥 酒捞袍 ID狼 酒捞袍阑 厘厚 窍绊 乐绰瘤 眉农茄促
BOOL TECH_checkEquipItem( Char *ch, int itemid)
{
	int i;
	int itemindex;

	for( i = 0; i < CHAR_EQUIPPLACENUM; i++){
		itemindex = CHAR_getItemIndex( ch, i);
		if( itemindex < 0) continue;
		if( ITEM_getInt( itemindex, ITEM_ID) == itemid) return TRUE;
	}

	return FALSE;
}


//-------------------------------------------------------------------------
// 急救、治疗的技能
// 动作和条件都是一样的，只是恢复不同，所以我总结在这里。
// int mode 0：急救 1：治疗
//-------------------------------------------------------------------------
int TECH_Recovery_sub( Char *ch, int haveskill, int havetech, int toindex, int mode)
{
	Char *target;
	int i;
	int skilllv = -1;          //
	int skillindex;
	int prob = 0;
	BOOL levelup = FALSE;
	int fame=0;
	int exp = 0;
	int stm = 0, dex = 0, intelligence = 0;
	int fd;
	int amount_fp, amount_fp_moto;
	int consumptionrate;
	int techindex;
	int techid;
	int KegaLevel = 0;
	int maxskilllv = 0;

#ifdef TEST
	char buf[256];
#endif
	BOOL flg = FALSE;
	int value = 0;
#ifdef PUK2
	int rate;
#endif
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
#ifdef SKILL_LOG
	int		before, after;
#endif
	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ) return FALSE;

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE) goto TECH_RECOVERY_SUB_ENSURE;

//	skilllv = CHAR_getSkillLevel( ch, haveskill);

	//泅犁狼 MAX 胶懦阑 掘绰促
	for(i = 0; i < CHAR_MAXTECHHAVE ; i++)
	{
		techid = CHAR_getTechId( ch, haveskill, i);
		techindex = TECH_getTechIndex( techid);
		if( ! TECH_CHECKINDEX( techindex)) continue;
		skilllv = TECH_getInt( techindex, TECH_NECESSARYLV ) ;
#ifdef PUK2
		// 府滚胶 窍绊 乐阑鳖?
		if( ch->w.RebirthSkill ){
			skilllv++;
			// 老窜 弥措 10 饭骇肺 肛冕促
			if( skilllv > SKILL_LEVEL_MAX ){
				skilllv = SKILL_LEVEL_MAX;
			}
		}
#endif
		//MAX 胶懦 眉农
		if(maxskilllv < skilllv){
			maxskilllv = skilllv;
		}
	}


	techid = CHAR_getTechId( ch, haveskill, havetech);
	techindex = TECH_getTechIndex( techid);
	if( ! TECH_CHECKINDEX( techindex)) return FALSE;
	//胶懦苞 咆捞 肋给窍绊 乐绢档
	skilllv = TECH_getInt( techindex, TECH_NECESSARYLV ) ;

	if( skilllv == -1 ) goto TECH_RECOVERY_SUB_ENSURE;
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
#ifdef PUK2
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX ){
			skilllv = SKILL_LEVEL_MAX;
		}
	}
#endif

    // 措惑狼 Char 器牢磐甫 秒垫茄促.
	// 惑措祈捞, 坷欺饭捞记 墨抛绊府 C狼 炼扒阑 盲快绊 乐阑鳖 犁眉农茄促.
	// 咯扁俊 坷扁鳖瘤, 惑怕啊 函拳窍绊 乐阑 啊瓷己捞 乐栏骨肺.
	target = TECH_getOperationC_Char( ch, toindex);
	if( target == NULL ) {
		goto TECH_RECOVERY_SUB_ENSURE;
	}

	// 摹丰, 摹丰狼 鞘夸啊 绝绰 版快甫 眉农.
	if( mode) {
		//
		if( target->i.Injury == 0 ){
			value = 0;	// 鞘夸绝澜.
			goto TECH_RECOVERY_SUB_ENSURE;
		}
	}
	else {
		if( target->i.Hp == target->w.MaxHp ){
			value = 0;	// 鞘夸绝澜.
			goto TECH_RECOVERY_SUB_ENSURE;
		}
	}


	//-------------------------------------------------
	// fp 家厚啦
	//-------------------------------------------------
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;


//	techid = CHAR_getTechId( ch, haveskill, havetech);
//	techindex = TECH_getTechIndex( techid);
//	if( ! TECH_CHECKINDEX( techindex)) return FALSE;

	//-------------------------------------------------
	// FP家厚摹 拌魂.
	//-------------------------------------------------
	if(mode == 1){
		// 摹丰狼 版快 胶懦 饭骇 x 30
		// 家厚 FP甫 秒秦 柯促.
		amount_fp_moto = TECH_getInt( techindex, TECH_FORCEPOINT );
	}else{
		// 览鞭 贸摹狼 版快
		if( skilllv < 0 ) skilllv = 1;
		if( skilllv > SKILL_LEVEL_MAX ) skilllv = SKILL_LEVEL_MAX;
		// 家厚 FP甫 秒秦 柯促.
		amount_fp_moto = TECH_getInt( techindex, TECH_FORCEPOINT );
	}

	amount_fp = amount_fp_moto;	// 盔狼 家厚 FP甫 扁撅茄促.
	// 函拳摹啊 乐栏搁
	if( consumptionrate > 0 ){
		amount_fp *= consumptionrate * 0.01;
	}
	if( amount_fp <= 0 ) amount_fp = 1;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_RECOVER, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		// burst 矫埃狼 唱赣瘤 犬牢
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_RECOVERY_SUB_ENSURE;
		}
	}
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_RECOVERY_SUB_ENSURE;
	}

	// 己傍狼 犬伏阑 拌魂.
	if( mode == 1 ){
		if( 0 <= target->i.Injury && target->i.Injury <= 25 ){
			KegaLevel = 1;
		}else
		if( 26 <= target->i.Injury && target->i.Injury <= 50 ){
			KegaLevel = 2;
		}else
		if( 51 <= target->i.Injury && target->i.Injury <= 75 ){
			KegaLevel = 3;
		}else{
			KegaLevel = 4;
		}
        char buf[8];
        int *rateList = aiChiryou_Prob_Normal_Tbl[KegaLevel - 1];
        // 摹丰.
        // 狼荤扼搁
        if (hashseti_has(cgmsvcf.doctorJobs, ch->iu.player.JobAncestry) ||
            hashseti_has(cgmsvcf.doctorJobs, ch->iu.player.Job)) {
            // 瘤沥 酒捞袍 ID狼 酒捞袍阑 厘厚 窍绊 乐阑鳖 炼荤茄促
            if (TECH_checkEquipItem(ch, 18246)) {
                //***** 捞 抛捞喉篮 角菩 犬伏捞骨肺, 100栏肺何磐 猾促.*****
                // 档备器窃狼 抛捞喉俊辑 秒茄促.
                sprintf(buf, "RP%d:", KegaLevel);
                rateList = aiChiryou_Prob_Plus_Tbl[KegaLevel - 1];
            } else {
                // 酒捞袍狼 己傍啦 函拳摹甫 啊魂茄促
                sprintf(buf, "RD%d:", KegaLevel);
                rateList = aiChiryou_Prob_Doctor_Tbl[KegaLevel - 1];
            }
        } else {
            // 狼荤 捞寇狼 叼弃飘 抛捞喉俊辑 秒茄促.
            sprintf(buf, "RN%d:", KegaLevel);
            rateList = aiChiryou_Prob_Normal_Tbl[KegaLevel - 1];
        }
        prob = Tech_getOptionByIndex(techindex, buf, -1);
        if (prob >= 0) {
            prob = CHARSUIT_getOptionValue(ch, techindex, buf, prob);
        }
        if (prob < 0) {
            prob = 100 - rateList[min(skilllv - 1, 9)];
        }
        if (buf[1] == 'D') {
            prob += CHAR_getSkillSuccessProb(ch, haveskill);
        }
#ifdef MONSTER_HELP_TECH
        {
            Char *pethelp = NULL;
            //局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促
            pethelp = TECH_CheckPetHelp(ch, skilllv, PETHELP_RECOVERY);
            //捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
            if (pethelp != NULL && ch->wu.player.PetHelpSkill) {
                //农府胶呕狼 郴己摹 拌魂阑 角矫茄促
                TECH_PetHelpForRecovery(ch, skilllv, pethelp);
                //己傍啦阑 UP
                //prob+=(prob*RECOVERY_RATE);
                prob += RECOVERY_ADDPOINT;
            }
        }
#endif
        prob *= 100;    // 1父 盒狼 1 罚待栏肺 窍扁 (困秦)锭巩俊
        // 角菩 焊沥阑 拌魂
        if (C_STM < 50 && C_INT < 50) {
            prob -= ((50 - C_STM) * 0.16 + (50 - C_INT) * 0.84) * 0.1;
        }

		if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == TRUE ){
            print_t(LANG_MSG_AUTOMATIC_485, skilllv, prob / 100.0, amount_fp, KegaLevel);
		}
	}else{
        char buf[8];
        int *rateList = aiOukyuu_Prob_Normal_Tbl;
		// 览鞭 贸摹.
        // 埃龋荤扼搁
        if (hashseti_has(cgmsvcf.nurseJobs, ch->iu.player.JobAncestry) ||
            hashseti_has(cgmsvcf.nurseJobs, ch->iu.player.Job)) {
            // 瘤沥 酒捞袍 ID狼 酒捞袍阑 厘厚 窍绊 乐阑鳖 炼荤茄促
            if (TECH_checkEquipItem(ch, 18245)) {
                // 葛磊器窃狼 抛捞喉俊辑 秒茄促.
                sprintf(buf, "RP:");
                rateList = aiOukyuu_Prob_Plus_Tbl;
            } else {
                // 埃龋荤 叼弃飘 抛捞喉俊辑 秒茄促.
                // 酒捞袍狼 己傍啦 函拳摹甫 啊魂茄促
                sprintf(buf, "RD:");
                rateList = aiOukyuu_Prob_Nourse_Tbl;
            }
        } else {
            // 览鞭 贸摹狼 叼弃飘 抛捞喉俊辑 秒茄促.
            sprintf(buf, "RN:");
            rateList = aiOukyuu_Prob_Normal_Tbl;
        }
        prob = Tech_getOptionByIndex(techindex, buf, -1);
        if (prob >= 0) {
            prob = CHARSUIT_getOptionValue(ch, techindex, buf, prob);
        }
        if (prob < 0) {
            prob = rateList[min(skilllv - 1, 9)];
        }
        if (buf[1] == 'D') {
            prob += CHAR_getSkillSuccessProb(ch, haveskill);
        }
#ifdef MONSTER_HELP_TECH
        {
            Char *pethelp = NULL;
            //局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促
            pethelp = TECH_CheckPetHelp(ch, skilllv, PETHELP_RECOVERY);
            //捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
            if (pethelp != NULL && ch->wu.player.PetHelpSkill) {
                //农府胶呕狼 郴己摹 拌魂阑 角矫茄促
                TECH_PetHelpForFirstaid(ch, skilllv, pethelp);

                //2004-11-20 局肯悼拱 秋橇 荤剧 函版俊 狼茄促
                //TECH_PetHelpForRecovery(ch, skilllv, pethelp);
                //己傍啦阑 UP
                //prob+=(prob*RECOVERY_RATE);
                prob += RECOVERY_ADDPOINT;
            }
        }
#endif
		prob *= 100;	// 1父 盒狼 1 罚待栏肺 窍扁 (困秦)锭巩俊
		// 角菩 焊沥阑 拌魂
        if (C_STM < 50 && C_INT < 50) {
            prob -= ((50 - C_STM) * 0.5 + (50 - C_INT) * 0.5) * 0.1;
        }

        if (CHAR_getWorkFlg(ch, CHAR_ISDEBUG) == TRUE) {
            print_t(LANG_MSG_AUTOMATIC_486, skilllv, prob / 100.0, amount_fp, aiOukyuu_Value_Tbl[skilllv - 1]);
        }
	}


	// 己傍且鳖?
	if( RAND( 1,10000) < prob) {

		// 摹丰
		if( mode ) {
			int itmp;
            itmp = Tech_getOptionByIndex(techindex, "AR:", -1);
            if (itmp >= 0) {
                itmp = CHARSUIT_getOptionValue(ch, techindex, "AR:", itmp);
            }
            if (itmp < 0) {
                // 雀汗樊 拌魂.抛捞喉俊辑 海鸥俊 秒秦 柯促.
                itmp = aiChiryou_Value_Tbl[min(9, skilllv - 1)];
            }
			// 焊沥摹甫 拌魂
            if (C_STM > 50 && C_INT > 50) {
                itmp *= 1.0 + ((C_STM - 50) * 0.16 + (C_INT - 50) * 0.84) * 0.001;
            }
			target->i.Injury -= itmp;
			if( target->i.Injury < 0 ) target->i.Injury = 0;
			if( target->i.Injury <= 0 ){
				value = 4;	// 肯蔫 窍搁(磊) 4
			}else{
				value = 1;	// 摹丰狼 版快绰 咯扁绰 1.促父 厩拳登搁(磊) 2
			}
			if( target->i.WhichType == CHAR_TYPEPLAYER ) {
				// 摹丰茄 惑措俊霸 弊 蔼阑 价脚.
				CHAR_send_CP2_String( target, CHAR_CP2_INJURY);
#ifdef PUK2
				// 惑贸 惑怕甫 林困俊 舅赴促
				CHAR_sendInjuryEffect( target );
#endif
			}
			// 局肯悼拱阑 雀汗沁促.
			else if( target->i.WhichType == CHAR_TYPEPET){
				Char *owner = target->wu.pet.PlayerChar;
				if( CHAR_CheckCharPointer( owner) && owner->i.WhichType == CHAR_TYPEPLAYER) {
					int i;
					for( i = 0; i < CHAR_MAXPETHAVE; i ++ ) {
						// CHAR_getHavePetCharPointer()甫 荤侩窍瘤 臼绰 巴篮,
						// 盒疙窍霸 焊刘登绊 乐扁 锭巩俊.(havepetindex, whichtype啊)
						if( (Char *) owner->player_addon->PetPointer[i] == target) {
							CHAR_send_KP_String( owner, i, CHAR_KP_INJURY);
#ifdef PET_RIDE
							// 愁捞 扁备 脐阑 摹丰沁绢?
							if( owner->w.walk.ridePet == i ){
								// 惑贸 惑怕甫 林困俊 舅赴促
								CHAR_sendInjuryEffect( owner );
							}
#endif
#ifdef PUK2
							// 惑贸 惑怕甫 林困俊 舅赴促
							CHAR_sendPetInjuryEffect( owner, i );
#endif
							break;
						}
					}
				}
			}

			{
				char szMsgBuffer[256];
				if( target->i.WhichType == CHAR_TYPEPET){
					Char *work;
					work = target->wu.pet.PlayerChar;
					if(work != ch){
						if( value == 4 ){
							//"%s甫%s俊 摹丰甫 罐酒, 惑贸啊 肯蔫 沁促.",
							snprintf( szMsgBuffer, sizeof(szMsgBuffer),
										TMSG(LANG_MSG_TECH_C_016),
										CHAR_getUseName( target ),
										ch->c.Name
									 );
						}else{
							//"%s甫%s俊 摹丰甫 罐酒, 惑贸啊 炼陛 雀汗沁促.",
							snprintf( szMsgBuffer, sizeof(szMsgBuffer),
										TMSG(LANG_MSG_TECH_C_009),
										CHAR_getUseName( target ),
										ch->c.Name
									 );
						}
						SYSTEMMSG( work, szMsgBuffer );
					}
				}else{
					if(ch != target){
						if( value == 4 ){
							//"%s俊 摹丰甫 罐酒, 惑贸啊 肯蔫 沁促"
							snprintf( szMsgBuffer, sizeof(szMsgBuffer),
									TMSG(LANG_MSG_TECH_C_015)	, ch->c.Name );
						}else{
							//"%s俊 摹丰甫 罐酒, 惑贸啊 炼陛 雀汗沁促"
							snprintf( szMsgBuffer, sizeof(szMsgBuffer),
									TMSG(LANG_MSG_TECH_C_010)	, ch->c.Name );

						}
						SYSTEMMSG( target, szMsgBuffer );
					}
				}
			}
			// 颇扼固磐甫 努扼捞攫飘俊 焊辰促.
			CHAR_complianceParameter( target );
			CHAR_send_CP_String( target , CHAR_CP_ALL );
		} else {
            value = Tech_getOptionByIndex(techindex, "AR:", -1);
            if (value >= 0) {
                value = CHARSUIT_getOptionValue(ch, techindex, "AR:", value);
            }
            if (value < 0) {
                // 雀汗樊 拌魂.抛捞喉俊辑 海鸥俊 秒秦 柯促.
                value = aiOukyuu_Value_Tbl[min(9, skilllv - 1)];
            }
            // 焊沥摹甫 拌魂
            if (C_STM > 50 && C_INT > 50) {
                value *= 1.0 + ((C_STM - 50) * 0.5 + (C_INT - 50) * 0.5) * 0.001;
            }

			// MAX甫 坷滚窍绰 巴 鞍栏搁 焊沥.
			if(value > (target->w.MaxHp - target->i.Hp) ) value = target->w.MaxHp - target->i.Hp;
			if( value < 0 ) value = 0;
			// 啊魂.
			target->i.Hp += value;
			// 茄锅 歹 MAX 眉农
			target->i.Hp = min( target->i.Hp, target->w.MaxHp);

			if( target->i.WhichType == CHAR_TYPEPLAYER ) {
				// 摹丰茄 荤恩俊霸 弊 蔼阑 价脚.
				CHAR_send_CP_String( target, CHAR_CP_HP);
			}else
			if( target->i.WhichType == CHAR_TYPEPET){
				Char *owner = target->wu.pet.PlayerChar;
				if( CHAR_CheckCharPointer( owner) && owner->i.WhichType == CHAR_TYPEPLAYER) {
					int i;
					for( i = 0; i < CHAR_MAXPETHAVE; i ++ ) {
						// CHAR_getHavePetCharPointer()甫 荤侩窍瘤 臼绰 巴篮,
						// 盒疙窍霸 焊刘登绊 乐扁 锭巩俊.(havepetindex, whichtype啊)
						if( (Char *) owner->player_addon->PetPointer[i] == target) {
							CHAR_send_KP_String( owner, i, CHAR_KP_HP);
							break;
						}
					}
				}
			}


			{
				char szMsgBuffer[256];
				if( target->i.WhichType == CHAR_TYPEPET){
					Char *work;
					work = target->wu.pet.PlayerChar;
					if(work != ch){
//						"%s俊 览鞭 贸摹甫 罐酒%s狼 LP啊%d雀汗沁促"
						snprintf( szMsgBuffer, sizeof(szMsgBuffer),
									TMSG(LANG_MSG_TECH_C_011),
									ch->c.Name,
									CHAR_getUseName( target ),
									value

								 );
						SYSTEMMSG( work, szMsgBuffer );
					}
				}else{
					if(ch != target){
						//"%s俊 览鞭 贸摹甫 罐酒 LP啊%d雀汗茄",
						snprintf( szMsgBuffer, sizeof(szMsgBuffer),
								TMSG(LANG_MSG_TECH_C_012),
								 ch->c.Name, value
								 );

						SYSTEMMSG( target, szMsgBuffer );
					}
				}
			}
		}
		// 胶懦 版氰摹 秒垫
		exp = SKILLEXP_getProductSkillExp( ch, skillindex, skilllv,
										ch->player_addon->Skill[haveskill].SkillLevel,
										ch->iu.player.Job
										);
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
#ifdef SKILL_LOG
		// 荤侩傈狼 版氰摹
		before = CHAR_getSkillExp( ch, haveskill );
		//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
		// 荤侩饶狼 版氰摹
		after = CHAR_getSkillExp( ch, haveskill );
		// 胶懦 荤侩矫狼 肺弊甫 免仿
		LogSkill(
			ch->cu.player.CdKey, ch->iu.player.RegistNumber, ch->c.Name,
			CHAR_getSkillId( ch, haveskill), before, after, exp );
#else
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp , &stm, &dex, &intelligence);
#endif
		// 己傍茄 敲贰弊.
		flg = TRUE;

	} else{
		// 角菩
		if( mode ){
			int  iAkka = 0;	// 厩拳登搁(磊) 1.
			char szMsgBuffer[256];
			// 摹丰 角菩.100盒狼 1狼 犬伏肺 惑贸啊 厩拳
			if( RAND( 1, 100 ) <= 1 ){
#ifndef PUK2
				target->i.Hp -= 20;		// 单固瘤甫 罐绰促
				// 弥历 HP绰 1
				if( target->i.Hp <= 0 ){
					target->i.Hp = 1;
				}
				// 惑贸啊 厩拳等 荤恩俊霸 弊 蔼阑 价脚.
				CHAR_send_CP_String( target, CHAR_CP_HP);
				// 颇萍 傈眉俊档 啊福模促
				CHAR_sendPartyParam( target );
				// 摹丰窍绊 乐绰 (盒)祈捞 促模促
				ch->i.Injury += 10;		// 绊沥摹肺 10 惑铰
				if( ch->i.Injury >= 100 ){
					ch->i.Injury = 100;
				}
				// 惑贸 惑怕甫 价脚
				CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
				// 惑贸 惑怕甫 林困俊 舅赴促
				CHAR_sendInjuryEffect( ch );
#else
				target->i.Injury += 25;	// 厩拳矫挪促.
#endif
				iAkka = 1;	// 厩拳等 钎
				value = 2;	// 摹丰狼 角菩狼 版快绰 1
						// 惑贸狼 厩拳绰 2.康去捞 狐瘤搁(磊) 3.
			}else{
				value = 1;	// 摹丰狼 角菩狼 版快绰 1
						// 惑贸狼 厩拳绰 2.康去捞 狐瘤搁(磊) 3.
			}
			if( target->i.Injury < 0 ) target->i.Injury = 0;
			if( target->i.Injury > 100 ) target->i.Injury = 100;
			if( target->i.WhichType == CHAR_TYPEPLAYER ) {
				// PUK2俊辑绰 措惑狼 惑贸啊 厩拳登瘤 臼霸 茄促
				// 馆措肺 摹丰螟篮 惑贸唱档废(淀捞)
#ifndef PUK2
				// 惑贸 饭骇 4扼搁 50%狼 犬伏肺, 康去捞 狐柳促.
				if( KegaLevel >= 4 	// 饭骇篮 4捞瘤父 箭磊绰 3捞促.
				&& iAkka == 1 		// 厩拳登绊 乐栏搁(磊).
				&& target->iu.player.Penalty < 5 // 康去 肺胶飘啊 5俺 捞窍.
				&& target->i.Lv >= 10 // 饭骇捞 10捞惑捞扼搁.
				&& RAND( 1, 100 ) < 50 			// 2盒狼 1栏肺
				){
					value = 3;	// 康去 狐柳 钎.努扼捞攫飘俊 焊辰促.
					// 康去 狐脸促.w
					target->iu.player.Penalty += 1;
					//努扼捞攫飘俊 价脚
					CHAR_complianceParameter( target );
					CHAR_send_CP2_String( target , CHAR_CP2_PENALTY );
					CHAR_send_CP_String( target , CHAR_CP_ALL );
					// 皋技瘤.摹丰俊 角菩秦 %s 绰 康去阑 酪菌促.
					snprintf( szMsgBuffer, sizeof(szMsgBuffer),
						TMSG(LANG_MSG_TECH_C_006), target->c.Name );

					SYSTEMMSG( target, szMsgBuffer );
				}else
#endif
				if( iAkka == 1 ){
#ifndef PUK2
					// 皋技瘤.%s 绰 累诀吝俊 促闷促.
					snprintf( szMsgBuffer, sizeof(szMsgBuffer),
						TMSG(LANG_MSG_TECH_C_014), ch->c.Name );

					SYSTEMMSG( ch, szMsgBuffer );
#endif
					// 皋技瘤.摹丰俊 角菩秦 %s 狼 惑贸啊 厩拳登菌促.
					snprintf( szMsgBuffer, sizeof(szMsgBuffer),
						TMSG(LANG_MSG_TECH_C_007), target->c.Name );

					SYSTEMMSG( target, szMsgBuffer );
				}else{
					// 皋技瘤.摹丰俊 角菩沁嚼聪促.
					translate_format(szMsgBuffer, sizeof(szMsgBuffer), LANG_MSG_TECH_C_008 );

					SYSTEMMSG( target, szMsgBuffer );
				}
				// 摹丰茄 惑措俊霸 弊 蔼阑 价脚.
				CHAR_send_CP2_String( target, CHAR_CP2_INJURY);
#ifdef PUK2
				// 惑贸 惑怕甫 林困俊 舅赴促
				CHAR_sendInjuryEffect( target );
#endif
			}
			// 局肯悼拱阑 厩拳矫淖促.
			else if( target->i.WhichType == CHAR_TYPEPET){
				Char *owner = target->wu.pet.PlayerChar;
				if( CHAR_CheckCharPointer( owner) && owner->i.WhichType == CHAR_TYPEPLAYER) {
					int i;
					for( i = 0; i < CHAR_MAXPETHAVE; i ++ ) {
						// CHAR_getHavePetCharPointer()甫 荤侩窍瘤 臼绰 巴篮,
						// 盒疙窍霸 焊刘登绊 乐扁 锭巩俊.(havepetindex, whichtype啊)
						if( (Char *) owner->player_addon->PetPointer[i] == target) {
							CHAR_send_KP_String( owner, i, CHAR_KP_INJURY);
#ifdef PET_RIDE
							// 愁捞 扁备 脐捞 厩拳登菌绢?
							if( owner->w.walk.ridePet == i ){
								// 惑贸 惑怕甫 林困俊 舅赴促
								CHAR_sendInjuryEffect( owner );
							}
#endif
#ifdef PUK2
							// 惑贸 惑怕甫 林困俊 舅赴促
							CHAR_sendPetInjuryEffect( owner, i );
#endif
							// 皋技瘤.摹丰俊 角菩秦 %s 狼 惑贸啊 厩拳登菌促.
							snprintf( szMsgBuffer, sizeof(szMsgBuffer),
								TMSG(LANG_MSG_TECH_C_007), target->c.Name );
							SYSTEMMSG( target, szMsgBuffer );
							break;
						}
					}
				}
			}
		}else{
			value = 1;	// 公均牢啊 蔼阑 持绢 敌促.绝菌栏搁 鞘夸绝菌带 巴捞 登扁 锭巩俊.

			if(ch != target)
			{
				char szMsgBuffer[256];
				//"%s俊 览鞭 贸摹甫 罐疽瘤父 肋 登瘤 臼疽促"
				snprintf( szMsgBuffer, sizeof(szMsgBuffer),
							TMSG(LANG_MSG_TECH_C_013)
							, ch->c.Name

							 );
				if( target->i.WhichType == CHAR_TYPEPET){
					Char *work;
					work = target->wu.pet.PlayerChar;
					SYSTEMMSG( work, szMsgBuffer );
				}else{
					SYSTEMMSG( target, szMsgBuffer );
				}
			}
		}

	}

	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE){
		// 疙己摹 拌魂
		fame = TECH_getFame2( ch, skilllv, maxskilllv, flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}

	CHAR_complianceParameter( ch);
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_RECOVER, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif

	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
	// 雀汗茄 惑措狼 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( target );
#endif

	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, value);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);



	}

	return TRUE;

 TECH_RECOVERY_SUB_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence, value);

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return FALSE;
}
//-------------------------------------------------------------------------
// 览鞭 贸摹狼 胶懦
//-------------------------------------------------------------------------
int TECH_Allowance( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_Recovery_sub( ch, haveskill, havetech, toindex, 0);
}
//-------------------------------------------------------------------------
// 摹丰狼 胶懦
//-------------------------------------------------------------------------
int TECH_MedicalTreatment( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_Recovery_sub( ch, haveskill, havetech, toindex, 1);
}
//-------------------------------------------------------------------------
// 好食便当
//-------------------------------------------------------------------------
#ifdef ITEM_NEW
BOOL CheckAutoFeddItem( Char *ch ){
	int i, itemindex, remain, workfp, prefp;
	
	if( (CHAR_CheckCharPointer( ch) == FALSE) || ch->i.WhichType != CHAR_TYPEPLAYER ) return FALSE;
	
	for( i=CHAR_STARTITEMARRAY ; i < CHAR_MAXITEMHAVE ; i++ ) { 	   
		itemindex = CHAR_getItemIndex( ch, i);
		workfp = (ITEM_getInt(itemindex, ITEM_SPECIALEFFECTVALUE));
		if( ITEM_CHECKINDEX( itemindex ) 
		&&	ITEM_getInt(itemindex, ITEM_LEAKLEVEL) 
		&& ITEM_getInt(itemindex, ITEM_SPECIALEFFECT) == ITEM_SPECIALEFFECT_AUTO_FEED
		&& workfp != 0 ){
			remain = ITEM_getInt(itemindex, ITEM_REMAIN) - 1;
			prefp = ch->i.ForcePoint;
			ch->i.ForcePoint += workfp;
			if( ch->i.ForcePoint > ch->w.MaxForcePoint)
				ch->i.ForcePoint = ch->w.MaxForcePoint;
			char szBuffer[256];
			translate_format(szBuffer, sizeof(szBuffer), LANG_MSG_TECH_C_020, ITEM_getChar( itemindex, ITEM_TRUENAME ), ch->i.ForcePoint - prefp);
			SYSTEMMSG(ch, szBuffer);
			if( remain > 0)
				ITEM_setInt(itemindex, ITEM_REMAIN, remain);
			else{
				CHAR_unsetItem( ch, i );
				ITEM_endExistItemsOne( itemindex );
			}
			CHAR_sendItemDataOne(ch, i);
			return TRUE;
		}
	}
	return FALSE;
}
#endif
//-------------------------------------------------------------------------
// 坷欺饭捞记 墨抛绊府 D.国盲, 盲奔, 荐菲栏肺何磐 阂妨 埃促.
// 牢荐 :int mode
//              0: 国盲
//              1: 荐菲
//              2: 盲奔
//-------------------------------------------------------------------------
int TECH_use_D( Char *ch, int haveskill, int havetech, int toindex, int mode)
{
	int skilllv = -1;          //
	int skillindex;
	int skillid;
	int fd;
	int prob = 0;
	BOOL levelup = FALSE;
	BOOL injuryflg = FALSE;
	int fame=0;
	int exp = 0;
	int stm = 0, dex = 0, intelligence = 0;
	int consumptionrate;
	int failedprob = 0;
	int amount_fp = 1;
	BOOL flg = FALSE;
//	char buf[256];
	int emptyhaveitemindex;
	int techareaindex, aTechAreaIndex[16];
	int newitemindex = -1, iAreaCount = -1, iTail = -1, imagenumber = -1;
	char itemname[128];
	int area_failedprob, iHit = -1;
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
	int rate;
	int rankdis = 0;
#endif
    int luaHooked = 0;
    int luaItemId = -1;

#ifdef MONSTER_HELP_TECH
	BOOL phflg=FALSE;
	Char *pethelp=NULL;
#endif



	itemname[0] = '\0';

	skillindex = SKILL_getSkillIndex(CHAR_getSkillId( ch, haveskill));
	if( skillindex == -1 ){
		 goto TECH_USE_D_ENSURE;
	}

	skillid = SKILL_getInt( skillindex, SKILL_ID);

	// 傈捧吝篮 救蹬.
	if( ch->w.BattleMode != BATTLE_CHARMODE_NONE){
		 goto TECH_USE_D_ENSURE;
	}
	skilllv = CHAR_getSkillLevel( ch, haveskill);
	if( skilllv == -1 ){
		 goto TECH_USE_D_ENSURE;
	}
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
	// 飘罚胶吝?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促
		if( skilllv > SKILL_LEVEL_MAX_CRAFT ){
			skilllv = SKILL_LEVEL_MAX_CRAFT;
		}
	}
#endif

    // 酒捞袍鄂狼 后 镑篮 乐阑鳖?
	emptyhaveitemindex = CHAR_findEmptyItemBox( ch);
	if( emptyhaveitemindex == -1 ) {
		SYSTEMMSG( ch , TMSG(LANG_MSG_TECH_C_005));
		goto TECH_USE_D_ENSURE;
	}

	// 坷欺饭捞记 剧侥 D眉农.瘤沥狼 雀荐 捞惑 窍搁(磊) 冻绢柳促.
	OpecateTimeCheck( ch );

	// FP 拌魂
	// fp 家厚啦
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;
	switch( mode) {
	case 0:
		amount_fp = 1;
		break;
	case 1:
		amount_fp = 1;
		break;
	case 2:
		amount_fp = 1;
		break;

#ifdef _FISHING_OPERATION
	case 3:
		amount_fp = 1;
		break;
#endif /* _FISHING_OPERATION */

#ifdef TECH_CUTTING
	case 4:
		amount_fp = 1;
		break;

	default:
		print( "err_6 :%s:%s:%d   \n", ch->cu.player.CdKey, ch->c.Name, mode);
		break;
#else
	default:
		print( "err\n");
		break;
#endif
	}
	amount_fp *= consumptionrate * 0.01;
	if( amount_fp <= 0 ) amount_fp = 1;
#ifdef PUK2
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_GATHER, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
		// burst 矫埃狼 唱赣瘤 犬牢
		if( CHAR_FeverHaveTime(ch) <= 0 ) {
			// "burst 鸥烙捞 面盒窍瘤 臼嚼聪促."
			SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_015));
			// burst 鸥烙 何练且 锭绰 溜return
			goto TECH_USE_D_ENSURE;
		}
	}
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_USE_D_ENSURE;
	}

	// 磊脚捞 乐绰 厘家肺何磐, 秒垫且 荐 乐绰 酒捞袍捞 乐绰 抛捞喉阑 秒垫茄促.
	iAreaCount = TECH_AREA_getIndexFromCoordinate(
		skillid, ch->i.MapId, ch->i.Floor, ch->i.X, ch->i.Y,
		aTechAreaIndex,  arraysizeof( aTechAreaIndex ),
		&techareaindex, &iTail );

	if( iTail == -1 || iAreaCount <= 0 ) {
        if (!luaHooked) {
            luaItemId = EmitGatherItemEvent(ch, skillid, skilllv, -1);
            luaHooked = 1;
            if (luaItemId >= 0) {
                goto LUA_HOOKED_TECHAREA;
            }
        }
		goto TECH_USE_D_ENSURE;
	}

	// 盖 关狼 扁夯 角菩 犬伏阑 秒秦 柯促.
	area_failedprob = TECH_AREA_getInt( iTail, TECH_AREA_FAILEDPROB);
	if( area_failedprob < 0 ) area_failedprob = 0;

	// 扁夯 角菩啦阑 拌魂.惑贸唱绊 乐栏搁(磊) 犬伏 临牢促.
	failedprob =  area_failedprob;
/*
	// 扁夯 角菩 犬伏阑 拌魂茄促.
	switch( mode ) {
		// 国盲
	case 0:
		// 犬伏 拌魂
		failedprob =  area_failedprob *
			(( 400 - CHAR_getSkillSuccessProb( ch, haveskill))*0.0025) *
			((500 - C_INT - C_STM)*0.002)
		;
#ifdef TEST
		snprintf( buf, sizeof( buf),
				   "%d(fail) * (400 -  %d(item 胶懦 荐沥))/400 * (500 - %d(int) - %d(stm))/500 = %d"
				  ,
				  area_failedprob,
				  CHAR_getSkillSuccessProb( ch, haveskill),
				  C_INT,
				  C_STM,
				  failedprob);
#endif
		break;
		// 荐菲
	case 1:
		// 犬伏 拌魂
		failedprob = area_failedprob *
			(( 400 - CHAR_getSkillSuccessProb( ch, haveskill))*0.0025) *
			((500 - C_INT - C_DEX)*0.002)
		;
#ifdef TEST
		snprintf( buf, sizeof( buf),
				   "%d(fail) * (400 -  %d(item 胶懦 荐沥))/400 * (500 - %d(int) - %d(dex))/500 = %d"
				  ,
				  area_failedprob,
				  CHAR_getSkillSuccessProb( ch, haveskill),
				  C_INT,
				  C_DEX,
				  failedprob);
#endif
		break;
		// 盲奔
	case 2:
		// 犬伏 拌魂
		failedprob = area_failedprob *
			(( 400 - CHAR_getSkillSuccessProb( ch, haveskill))*0.0025) *
			((500 - C_INT - C_DEX)*0.002)
		;
#ifdef TEST
		snprintf( buf, sizeof( buf),
				   "%d(fail) * (400 -  %d(item 胶懦 荐沥))/400 * (500 - %d(Dex) - %d(Stm))/500 = %d"
				  ,
				  area_failedprob,
				  CHAR_getSkillSuccessProb( ch, haveskill),
				  C_DEX,
				  C_STM,
				  failedprob);
#endif
		break;

	default:
		print( "err\n");
		break;
	}
*/
#ifdef TEST

	SYSTEMMSG( ch, buf);

#endif
	// 免泅 酒捞袍栏肺何磐 犬伏阑 拌魂.
	{
#define TECH_AREA_DUPLICATE_ITEM_NUM	32
		int cnt = 0;
		int itemprob[TECH_AREA_DUPLICATE_ITEM_NUM];
		int itemid[TECH_AREA_DUPLICATE_ITEM_NUM];
		int sumitemprob = 0;
		int value;
		int i, j, iNowAreaIndex, iNeedItem, k;
		int r;
		// 茫酒辰 俊府绢 牢郸胶狼 焙窜俊辑 促矫 茄锅 风橇.
		for( j = 0; j < iAreaCount; j ++ ) {
			iNowAreaIndex = aTechAreaIndex[j];	// 捞锅 牢郸胶.

			if( iNowAreaIndex < 0 ) break;		// 付捞呈胶扼搁 咯扁辑 场唱.

			// 捞 咆 俊府绢俊 鞘夸茄 酒捞袍捞 汲沥登绢 乐阑鳖?
			iNeedItem = TECH_AREA_getInt( iNowAreaIndex, TECH_AREA_NEED_ITEM );

			// 汲沥登菌促.捞巴阑 啊瘤绊 乐绢? 啊瘤瘤 臼疽栏搁 捞 俊府绢绰 公瓤
			if( iNeedItem > 0 ){
				int needitemindex;
				// 啊瘤绊 乐绰 酒捞袍 傈何肺何磐 八祸
				for( k = 0; k < CHAR_MAXITEMHAVE ; k ++ ){
					// 酒捞袍 牢郸胶甫 掘绰促.
					needitemindex = CHAR_getItemIndex( ch, k );
					if( ITEM_CHECKINDEX( needitemindex ) == FALSE ) continue;
					// 鞘夸茄 酒捞袍捞菌促.OK.
					if( ITEM_getInt( needitemindex, ITEM_ID ) == iNeedItem ){
						break;
					}
				}
				// 啊瘤瘤 臼疽栏搁.捞 俊府绢绰 公瓤
				if( k >= CHAR_MAXITEMHAVE ) continue;
			}

			for( i = 0; i < TECH_AREA_ITEMIDNUM; i ++ ) {
				value = TECH_AREA_getInt( iNowAreaIndex, TECH_AREA_ITEMID1+i);
				// 酒捞袍 ID啊 蜡瓤窍搁
				if( value != -1 && ITEM_CHECKITEMTABLE( value)) {
					int tmp, itemrank;

					itemrank = ITEM_getlevelFromITEMtabl( value );
					// 酒捞袍 珐农啊 0 捞窍? 俊矾促.
					if( itemrank <= 0 ) continue;
#ifdef _FISHING_OPERATION
					// 酒捞袍 珐农啊 臭疽栏搁 饶焊俊 持瘤 臼绰促.
					if( mode != 3){
						if( itemrank > skilllv ) continue;
					}
					// 超矫狼 版快
					else {
						int itemindex;
						int id;

						// 冈捞 酒捞袍阑 眉农
						itemindex = CHAR_getItemIndex( ch, toindex);
						// 公瓤牢 酒捞袍捞 瘤沥登绊 乐促
						if( itemindex == -1) {
							goto TECH_USE_D_ENSURE;
						}
						// 皑沥 敲贰弊甫 眉农
						if( ! ITEM_getInt( itemindex, ITEM_LEAKLEVEL)){
							goto TECH_USE_D_ENSURE;
						}
						// 酒捞袍 ID甫 秒垫
						id = ITEM_getInt( itemindex, ITEM_ID);
						// 超矫锭绰 荤侩且 荐 乐绰 冈捞狼 珐农甫 眉农
						if( ITEM_getlevelFromITEMtabl( id) > skilllv) continue;
						// 瘤沥等 冈捞肺 棱鳃瘤 绢冻瘤 眉农
						if( ! FISHTABLE_checkFishAndFeed( value, id)){
							// 棱洒瘤 臼绰促
							continue;
						}
						// 棱鳃 锭绰 饶焊俊 持绰促
					}
#else
					// 酒捞袍 珐农啊 臭疽栏搁 饶焊俊 持瘤 臼绰促.
					if( itemrank > skilllv ) continue;
#endif /* _FISHING_OPERATION */
					itemid[cnt] = value;	// 酒捞袍 ID甫 拜吵.
					tmp = TECH_AREA_getInt( iNowAreaIndex, TECH_AREA_ITEMPROB1+i);
//					print( "CNT = %d ID %d Prob %d\n", cnt, value, tmp );
					sumitemprob += tmp;
					itemprob[cnt] = sumitemprob;// 犬伏阑 持绰促.
					cnt ++;
					// 捞巴栏肺 啊垫钦聪促.
					if( cnt >= TECH_AREA_DUPLICATE_ITEM_NUM ) break;
/*
					tmp = TECH_AREA_getInt( iNowAreaIndex, TECH_AREA_ITEMPROB1+i);
					if( cnt == 0 ) {
						itemprob[cnt] = tmp;// 犬伏阑 持绰促.
					}
					else {
						itemprob[cnt] = tmp + itemprob[cnt-1];	// 钦拌甫 敲矾胶 秦 唱埃促.
					}
					sumitemprob += tmp;	// 钦拌甫 敲矾胶 秦 唱埃促.
					cnt ++;
*/
				}
			}
		}

		// 酒捞袍狼 汲沥捞 绝菌促
		if( cnt == 0 ) {
            if (!luaHooked) {
                luaItemId = EmitGatherItemEvent(ch, skillid, skilllv, -1);
                luaHooked = 1;
                if (luaItemId >= 0) {
                    goto LUA_HOOKED_TECHAREA;
                }
            }
			goto TECH_USE_D_ENSURE;
		}

		// 罚待狼 裹困绰 0 ~傈酒捞袍 犬伏狼 钦拌＋角菩 犬伏
		prob = sumitemprob + failedprob;
		// 惑贸甫 涝绊 乐栏搁(磊) 角菩啦阑 啊魂.惑贸狼 蔼阑 犬伏肺 官槽促.
		// 溜 prob 啊 2000 栏肺 惑贸 10捞扼搁 2000*10*0.01 10%促.
 		failedprob += prob * ch->i.Injury * 0.01;

		// 胶怕固呈殿栏肺 角菩啦阑 历窍矫挪促.
		if( mode == 0 ){	// 国盲
			failedprob *= ((500 - C_INT - C_STM)*0.002);
		}else
		if( mode == 1 ){	// 荐菲
			failedprob *= ((500 - C_INT - C_DEX)*0.002);
		}else
		if( mode == 2 ){	// 盲奔
			failedprob *= ((500 - C_DEX - C_INT)*0.002);
		}
#ifdef _FISHING_OPERATION
		else
		if( mode == 3 ){	// 超矫
			failedprob *= ((500 - C_INT - C_DEX)*0.002);
		}
#endif /* _FISHING_OPERATION */
#ifdef TECH_CUTTING
		else
		if( mode == 4 ){	//  剪取
			failedprob *= ((500 - C_INT - C_DEX)*0.002);
		}
#endif /* TECH_CUTTING */

		// 厘厚 酒捞袍狼 己傍啦 函拳摹
		failedprob *= ( 100 - CHAR_getSkillConsumptionRate( ch, haveskill)) * 0.01;


#ifdef MONSTER_HELP_TECH
		//局肯悼拱 秋橇 惯悼 犬牢
		pethelp=TECH_CheckPetHelpForUseD(ch);
		if(pethelp!=NULL)
		{
			failedprob-=failedprob*FAILED_RATE;
		}
#endif

		// 罚待阑 辰促.
		r = RAND( 1, (prob-1));

		if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == TRUE ){	// 犬伏 钎矫狼 叼滚弊 皋技瘤
			int tmpprob = 0;
			print( "\n" );
			for( i = 0; i < cnt; i ++ ){
				print( "(%s):(%d) \n", ITEM_getNameFromNumber( itemid[i] ), itemprob[i]-tmpprob );
				tmpprob = itemprob[i] ;
			}
			print( "fail prob %d/%d\n", failedprob, prob );
		}

		if( r <= failedprob ){	// 角菩 犬伏俊 秦寸窍搁(磊) 角菩.
			iHit = -1;	// 角菩.
		}else{
			// 公攫啊俊 HIT 沁栏骨肺, 罚待摹肺何磐 角菩盒阑 临牢促.
			r -= (failedprob );
			// 绢蠢 巴栏肺 嘎疽绰瘤.
			for( i = 0; i < cnt; i ++ ) {
				if( r < itemprob[i] ) {
					iHit = i;	// 捞巴捞促.
					break;
				}
			}
		}

#ifdef _FISHING_OPERATION
		// 冈捞 酒捞袍阑 临牢促
		if( mode == 3){
			int itemindex;
			int remain;

			// 冈捞 酒捞袍阑 眉农
			itemindex = CHAR_getItemIndex( ch, toindex);
			// 公瓤牢 酒捞袍捞 瘤沥登绊 乐促
			if( itemindex == -1) {
				goto TECH_USE_D_ENSURE;
			}
			remain = ITEM_getInt( itemindex, ITEM_REMAIN);
			// 冈捞 酒捞袍阑 临牢促
			if( remain >= 2){
				ITEM_setInt( itemindex, ITEM_REMAIN, remain-1);
				CHAR_sendItemDataOne( ch, toindex );
			}
			// 胶琶荐啊 1狼 版快
			else {
				// 酒捞袍阑 哈变促.
				CHAR_unsetItem( ch, toindex );
				// 酒捞袍阑 瘤款促
				ITEM_endExistItemsOne( itemindex );
				// 努扼捞攫飘俊 烹瘤
				CHAR_sendItemDataOne( ch, toindex );
			}
		}
#endif /* _FISHING_OPERATION */

        if (iHit < 0) {
            if (!luaHooked) {
                luaItemId = EmitGatherItemEvent(ch, skillid, skilllv, -1);
                luaHooked = 1;
                if (luaItemId >= 0) {
                    goto LUA_HOOKED_TECHAREA;
                }
            }
        }
		// 嘎疽阑 版快.
		if( iHit >= 0 ) {
			int itemrank = ITEM_getlevelFromITEMtabl( itemid[iHit]);

			// 酒捞袍狼 珐农啊 磊脚狼 胶懦 饭骇 捞窍扼搁 秒垫且 荐 乐促.
#ifdef _FISHING_OPERATION
			if(  itemrank <= skilllv || mode == 3) {
#else
            if (itemrank > skilllv) {
                if (!luaHooked) {
                    luaItemId = EmitGatherItemEvent(ch, skillid, skilllv, -1);
                    luaHooked = 1;
                    if (luaItemId >= 0) {
                        goto LUA_HOOKED_TECHAREA;
                    }
                }
            }
			if(  itemrank <= skilllv) {
                luaItemId = EmitGatherItemEvent(ch, skillid, skilllv, itemid[iHit]);
                luaHooked = 1;
                if (luaItemId < 0) {
                    goto TECH_USE_D_ENSURE;
                }
                LUA_HOOKED_TECHAREA:
                if (luaHooked && luaItemId >= 0) {
                    iHit = 0;
                    itemid[0] = luaItemId;
                    itemrank = ITEM_getlevelFromITEMtabl(itemid[iHit]);
                }
#endif /* _FISHING_OPERATION */
				// 酒捞袍 累己.
				newitemindex = ITEM_makeItemAndRegist( itemid[iHit]);
//TODO 咯扁辑, 秋橇狼 汲沥阑 且鳖.
#ifdef MONSTER_HELP_TECH
				if(pethelp!=NULL)
				{
					int itemlevel=ITEM_getInt( newitemindex, ITEM_LEVEL);
					if(itemlevel>0)
					{
						phflg=TECH_PetHelpForProducer(ch, itemlevel, pethelp);
					}
				}

				/*2004-11-16　贸府 老何 函版俊 狼秦 昏力
				//局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促	
				pethelp=TECH_CheckPetHelp(ch, itemrank, PETHELP_PRODUCER);

				//捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
				//if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
				if(pethelp!=NULL)
				{
					//农府胶呕狼 郴己摹 拌魂阑 角矫茄促
					TECH_PetHelpForProducer(ch, newitemindex, pethelp);

					//局肯悼拱 秋橇 己傍 敲贰弊甫 汲沥
					phflg=TRUE;
				}
				*/
#endif

#ifdef _FISHING_OPERATION
				// 超矫狼 版快, 超酒 棵赴 酒捞袍狼 可记苞 窃荐甫 眉农秦,
				if( mode == 3){
					// USEFUNC俊 ITEM_useMystery啊 甸绢啊 乐阑 锭绰 磊悼利栏肺 角青茄促
					// 弊 锭俊 酒捞袍篮 敲饭捞绢俊 扒匙林扁 傈俊 家戈窍骨肺 林狼啊 鞘夸
					if( ITEM_getFunctionPointer( newitemindex, ITEM_USEFUNC) == ITEM_useMystery){
						char *filename;

						//颇老疙 GET
						filename = ITEM_getChar( newitemindex, ITEM_ARGUMENT);
						ITEM_endExistItemsOne( newitemindex);
						//胶农赋飘 秦籍
						read_command_fileItem( ch, filename, "", 0 , 0);

						return FALSE;
					}
				}
#endif /* _FISHING_OPERATION */

				// 咯扁辑 积魂拌 颇扼固磐俊 狼秦, 俺荐啊 刘啊窍芭唱 临绢甸芭唱.
				if( C_STM > 50 && C_DEX > 50 ){	// 刘啊窍绰 版快
					int irem;
					int itmp = ( ( C_STM - 50 ) * 0.84 + ( C_DEX - 50 ) * 0.16 );
					if( itmp >= RAND( 1, 10000 ) ){
						// 疵赴促
						irem = ITEM_getInt( newitemindex, ITEM_REMAIN ) + 1;
						print_t(LANG_MSG_AUTOMATIC_361, ITEM_getChar( newitemindex, ITEM_TRUENAME ) );
						// 弥措甫 逞绢 绝菌栏搁
						if( ITEM_getInt( newitemindex, ITEM_MAXREMAIN ) >= irem ){
							// 啊魂
							ITEM_setInt( newitemindex, ITEM_REMAIN, irem );
						}
					}
				}else
				if( C_STM < 50 && C_DEX < 50 ){	// 临绢靛绰 版快
					int irem;
					int itmp = ( ( 50 - C_STM ) * 0.84 + ( 50 - C_DEX ) * 0.16 );
					if( itmp <= RAND( 1, 10000 ) ){
						// 疵赴促
						irem = ITEM_getInt( newitemindex, ITEM_REMAIN ) - 1;
						// 1 固父俊绰 窍瘤 臼绰促
						if( irem >= 1 ){
							// 临牢促
							ITEM_setInt( newitemindex, ITEM_REMAIN, irem );
						}
					}
				}
				// 酒捞袍 殿废
				if( ITEM_CHECKINDEX( newitemindex)) {
					// 酒捞袍阑 涝荐茄 肺弊甫 秒茄促.
					LogItem(
						ch->cu.player.CdKey,	// CD虐
						ch->c.Name, /* 某腐磐疙 */
						itemid[iHit],  /* 酒捞袍 锅龋 */
						"Tech_Use_D",
						ch->i.MapId, ch->i.Floor, ch->i.X, ch->i.Y
					);
#if 0
	// 磊悼 胶琶 茄促搁 咯扁绰 角青窍瘤 臼霸 茄促.
					CHAR_setItemIndex( ch, emptyhaveitemindex, newitemindex);
					CHAR_sendItemDataOne( ch, emptyhaveitemindex);
#endif
				}else{
					// 控瘤 角菩
					goto TECH_USE_D_ENSURE;
				}
//台服是日志记录 封禁另行处理 所以检测处理在此函数尾部
#ifdef ANIT_CHEATING_SET
				if(cgmsvcf.anitcheatingflg != 0){
					if( CHECK_WorkTime(ch) == TRUE)
						goto TECH_USE_D_ENSURE;
				}
#endif 
//ANIT_CHEATING_SET
				// 胶懦 版氰摹 秒垫
				exp = SKILLEXP_getProductSkillExp( ch, skillindex, itemrank,
											ch->player_addon->Skill[haveskill].SkillLevel,
											ch->iu.player.Job
											);
				//积魂拌狼 胶懦 版氰摹甫 刘气茄促.
//				exp = SKILLEXP_AmplifySkillExp( ch , skillindex, exp);
				levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp, &stm, &dex, &intelligence );
				// 捞抚 某腐磐 扼牢 累己.
				makeEscapeString( ITEM_getAppropriateName( newitemindex), itemname, sizeof( itemname));

				flg = TRUE;
				// 酒捞袍狼 捞抚阑 扁撅秦 敌促
				imagenumber = ITEM_getInt( newitemindex, ITEM_BASEIMAGENUMBER );
#if 1
				// 酒捞袍阑 胶琶 矫难档 俺.
				// 弊 锭狼 烹脚 包拌绰 秦 滚赴促
				if( CHAR_setStackItem( ch, newitemindex	) < 0 ){
					// 胶琶 秦 盔贰绰 绝绢脸促.
					newitemindex = -1;
				}
				// ¤狼 窃荐甫 何福搁(磊), 胶琶狼 包拌肺 newitemindex 啊 绝绢瘤骨肺,
				// 捞 捞饶绰 newitemindex 绰 咀技胶 窍瘤 臼绰 巴.
#endif
#ifdef PUK2
				// 秒垫茄 酒捞袍狼 珐农客 胶懦 饭骇狼 瞒捞甫 夸备茄促
				rankdis = ( itemrank * 2 - 1 ) - skilllv;
#endif
			}
		}
	}

	// 惑贸 眉农 // 胶懦 饭骇狼 馆狼 犬伏肺 惑贸甫 涝绰促.
#ifndef PUK2
	//  酒捞袍 珐农客狼 瞒捞啊 乐绰 版快俊 促模促
	if( rankdis > 0 && RAND( 1,10000 ) <= 20 )
#else
	if( skilllv >= 2 && RAND( 1,100000 ) <= cgmsvcf.gatherInjuryRate )
#endif
	{
		char szBuffer[256];
#ifndef PUK2
		int iWork = rankdis*5;	// 珐农瞒捞*5鳖瘤啊 促模促
#else
		int iWork = skilllv*5;	// 胶懦 饭骇*5鳖瘤狼 惑贸甫 涝绰促.
#endif
		int iHavePos = -1, itemindex = -1;

		// 惑贸车促.措开阑 厘厚 窍绊 乐绢?
		itemindex = CHAR_HaveItemInitFuncCheck(
			ch,
			ITEM_InitSacrifice, // 措开 瘤厘 瘤沥
			1,			//1捞扼搁 厘厚前父.
			&iHavePos	// 啊瘤绊 乐带 厘家
		);
		// 酒捞袍阑 啊瘤绊 乐菌促.弊 唱赣瘤 措开 雀荐档 乐菌促.
		if( itemindex >= 0 ){
			int iMigawariCount = 0;

			// 措开栏肺 登菌促.
			// "%s 绰 促磨 巴 鞍霸 登菌瘤父, %s 狼 塞栏肺 瘤难龙 荐 乐菌促"
			translate_format(szBuffer, 0, LANG_MSG_TECH_C_017, ch->c.Name,
				ITEM_getAppropriateName( itemindex ) );
			SYSTEMMSG( ch, szBuffer );
			// 措开 雀荐甫 临牢促.
			iMigawariCount = ITEM_getInt( itemindex, ITEM_VAR1 ) -1;
			if( iMigawariCount <= 0 ){	// 绝绢廉 噶啊脸促.
				// "%s 绰 噶啊廉 滚啡促"
				translate_format(szBuffer, 0, LANG_MSG_TECH_C_018,
					ITEM_getAppropriateName( itemindex ) );
				SYSTEMMSG( ch, szBuffer );
				// 酒捞袍阑 哈变促.
				CHAR_unsetItem( ch, iHavePos );
				// 肺弊甫 秒茄促.
				LogItem(
					ch->cu.player.CdKey,	// CD虐
					ch->c.Name, /* 某腐磐疙 */
					ITEM_getInt( itemindex, ITEM_ID ),  /* 酒捞袍 锅龋 */
					"SACRIFICE_BREAK",
					ch->i.MapId,
					ch->i.Floor,
					ch->i.X, ch->i.Y
				);
				// 何鉴促.
				ITEM_endExistItemsOne( itemindex );
				// 努扼捞攫飘俊 烹瘤
				CHAR_sendItemDataOne( ch, iHavePos );
				// 颇扼固磐 犁拌魂
				CHAR_complianceParameter( ch);
				CHAR_send_CP_String( ch, CHAR_CP_ALL );
			}else{
				// 雀荐 临牢 巴阑 扁撅.
				ITEM_setInt( itemindex, ITEM_VAR1, iMigawariCount );
				//add by moonboy 显示剩余次数更新
				CHAR_sendItemDataOne( ch, iHavePos );
			}
		}else{
			// 措开捞 绝扁 锭巩俊 惑贸
			if( iWork >= 50 ) iWork = 50;		// 促父 50鳖瘤狼 惑贸绰 窍瘤 臼绰促.
			ch->i.Injury += RAND( 1, iWork );	// 己傍窍绊 乐促搁＋1?
			if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
			CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
			injuryflg = TRUE;
			translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
			SYSTEMMSG( ch, szBuffer );
#ifdef PUK2
			// 惑贸 惑怕甫 林困俊 舅赴促
			CHAR_sendInjuryEffect( ch );
#endif
		}
	}

	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE){
		// 疙己摹 拌魂
		fame = TECH_getFame5( ch,  flg);
		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}


#ifdef ITEM_NEW
/* 好食便当 */
	if( ch->i.ForcePoint == amount_fp )
		CheckAutoFeddItem(ch);
#endif
	// fp 家厚
	ch->i.ForcePoint -= amount_fp;
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_GATHER, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif
	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif


	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];

#ifdef MONSTER_HELP_TECH
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%s|%d",
				  exp, fame, levelup, stm, dex, intelligence,
				  ( imagenumber == -1 ?  -1 : imagenumber ), itemname, phflg );
#else
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%s",
				  exp, fame, levelup, stm, dex, intelligence,
				  ( imagenumber == -1 ?  -1 : imagenumber ), itemname );

#endif

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return TRUE;

 TECH_USE_D_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%d",
				  exp, fame, levelup, stm, dex, intelligence,-1, 0 );

		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}

	return FALSE;

}
//-------------------------------------------------------------------------
// 国盲狼 胶懦
//-------------------------------------------------------------------------
int TECH_Lumb( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_use_D( ch, haveskill, havetech, toindex, 0);
}
//-------------------------------------------------------------------------
// 荐菲狼 胶懦
//-------------------------------------------------------------------------
int TECH_Hunting( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_use_D( ch, haveskill, havetech, toindex, 1);
}
//-------------------------------------------------------------------------
// 盲奔狼 胶懦
//-------------------------------------------------------------------------
int TECH_Mining( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_use_D( ch, haveskill, havetech, toindex, 2);
}

#ifdef _FISHING_OPERATION
//-------------------------------------------------------------------------
// 超矫狼 胶懦
//-------------------------------------------------------------------------
int TECH_Fishing( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
//	int itemindex;

/*	print( "%d", toindex + CHAR_STARTITEMARRAY);
	itemindex = CHAR_getItemIndex( ch, toindex + CHAR_STARTITEMARRAY);
	if( itemindex < 0) return FALSE;
	print( "%s", ITEM_getIncuseName( itemindex));

	return TRUE;
*/
/*	itemindex = CHAR_getItemIndex( ch, toindex + CHAR_STARTITEMARRAY);
	if( itemindex < 0){ // 何沥茄 酒捞袍捞 瘤沥登绊 乐促
		// 何沥 蜡历 措氓
		if( getUnFairUserDownFlg() == 1 ){
			Connect[ch->wu.player.Fd].closed = 1;
		}
		return FALSE;
	}*/

	return TECH_use_D( ch, haveskill, havetech, toindex + CHAR_STARTITEMARRAY, 3);
}
#endif /* _FISHING_OPERATION */

#ifdef TECH_CUTTING
/* 剪取技能  */
int TECH_Cutting( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	return TECH_use_D( ch, haveskill, havetech, toindex, 4);
}
#endif

//-------------------------------------------------------------------------
//鞘夸 饭骇捞 酒聪搁 胶懦阑 荤侩且 荐 绝促
//馆券蔼：
//	0：荤侩且 荐 绝促
//　1：荤侩且 荐 乐促
//-------------------------------------------------------------------------
int TECH_TechLevelCheck(Char *ch, int level)
{
	int plv;
//	int lv_tbl[10]={1,10,20,30,40,50,60,70,80,90};

	level--;
	//敲饭捞绢 饭骇
	plv = ch->i.Lv;
#ifdef LEVEL_CONTROLL
	// 敲饭捞绢?
    if (ch->i.WhichType == CHAR_TYPEPLAYER) {
        // 饭骇 力绢 酒捞袍 厘厚吝?
        if (ch->wu.player.EffectiveLevel < plv) {
            // 瓤苞 饭骇阑 敲饭捞绢 饭骇肺辑 秒鞭茄促
            plv = ch->wu.player.EffectiveLevel;
        }
    }
#endif

    if (plv >= level * 10) {
        return 1;
    }

	return 0;

}

#ifdef _OPERATION_APPEND_JEWEL
// 焊籍炼涝俊 狼茄 郴备仿 历窍啦苞 弊 犬伏
int AppendJewel_durDownTbl[4][2] = {
	{  15, 75},
	{  55, 80},
	{  85, 85},
	{ 100, 90}
};

//-------------------------------------------------------------------------
// 焊籍 眠啊 胶懦
//-------------------------------------------------------------------------
int TECH_AppendJewel( Char *ch, int haveskill, int havetech, int toindex, int array, char *data)
{
	char buf[256];
	int i, j;
	int fd;
	int skillid;
	int skillindex = -1;
	int skilllv = 1;
	int itemrank = 1;
	int haveitemindex[2];
	int newitemindex = -1;
	int itembindex = -1;
	int exp = 0;
	int fame = 0;
	int levelup = 0;
	int stm = 0;
	int dex = 0;
	int intelligence = 0;
	int injuryflg = 0;
	int flg = FALSE;
	int *recipe;
	int recipenum;
	int amount_fp;
	int techid;
	int olddur, oldmaxdur;
	int newdur, newmaxdur;
	float durrate;
	char deleteatomstring[256];
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	int usablelv;
#endif
	int consumptionrate;
	int rate;
#endif

	deleteatomstring[0] = '\0';

	// 胶懦 ID肺何磐 胶懦 牢郸胶 秒垫
	skillid = CHAR_getSkillId( ch, haveskill);
	skillindex = SKILL_getSkillIndex(skillid);
	if( ! SKILL_CHECKINDEX( skillindex)) goto TECH_APPENDJEWEL_ENSURE;
	skilllv = CHAR_getSkillLevel( ch, haveskill);
#ifdef PUK2
#ifdef NEW_JOB_CHANGE
	// 泅犁狼 老磊府肺 荤侩且 荐 乐绰 胶懦 饭骇阑 夸备茄促
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
	// 胶懦 饭骇苞 厚背秦 撤篮 (盒)祈鳖瘤 荤侩 啊瓷
	if( skilllv > usablelv){
		skilllv = usablelv;
	}
#endif 
	// 府滚胶 窍绊 乐阑鳖?
	if( ch->w.RebirthSkill ){
		skilllv++;
		// 老窜 弥措 10 饭骇肺 肛冕促"
		if( skilllv > SKILL_LEVEL_MAX ){
			skilllv = SKILL_LEVEL_MAX;
		}
	}
#endif

	// 弥檬狼 牢荐俊绰 焊籍阑 眠啊窍绊 酵篮 厘厚啊 乐绰 厘家啊 甸绢啊 乐促
	getStringFromIndexWithDelim( data, "|", 1, buf, sizeof( buf));
	haveitemindex[0] = atoi(buf) + ch->bagPage * CHAR_ITEMPAGENUM;
	newitemindex = CHAR_getItemIndex( ch, haveitemindex[0]);
	if( newitemindex <= 0) goto TECH_APPENDJEWEL_ENSURE;
	itemrank = ITEM_getlevelFromITEMtabl( ITEM_getInt( newitemindex, ITEM_ID));

	// 皑沥登绊 乐阑鳖?
	if( ! ITEM_getInt( newitemindex, ITEM_LEAKLEVEL)){
		goto TECH_APPENDJEWEL_ENSURE;
	}
	// 棵官弗 酒捞袍牢啊 绢恫啊 眉农茄促
	if( ! ( ITEM_getInt( newitemindex, ITEM_OTHERFLG) & ITEM_OTHERFLG_CANADDJEWEL)){
		// 焊籍 眠啊 啊瓷 敲贰弊啊 ON肺 登绢 乐瘤 臼促.
		goto TECH_APPENDJEWEL_ENSURE;
	}
	// 饭矫乔甫 眉农茄促
	for( i = 0; i < CHAR_MAXSKILLHAVE; i++){
		int index;
#ifdef JEWLRY_ACCESSORY
		/* 饰品制作增加宝石和装饰宝石 */
		if( ( ch->player_addon->Skill[i].SkillId < 200 || ch->player_addon->Skill[i].SkillId > 214 ) && ch->player_addon->Skill[i].SkillId != 2004) continue;
#else
		if( ch->player_addon->Skill[i].SkillId < 200 || ch->player_addon->Skill[i].SkillId > 214 ) continue;
#endif
		// 饭矫乔狼 胶懦 抛捞喉阑 秒垫
		index = ITEM_RECIPE_getIndexBySkillIdOnSkillTbl( ch->player_addon->Skill[i].SkillId);
		// 饭矫乔 ID狼 老恩阑 秒垫
		ITEM_RECIPE_getRecipeIdOnSkillTbl( index, &recipe, &recipenum);
		for( j = 0; j < recipenum; j++){
			// 饭矫乔狼 酒捞袍 ID啊 老摹且鳖
			if( ITEM_RECIPE_getInt( recipe[j], ITEM_RECIPE_ITEMID) == ITEM_getInt( newitemindex, ITEM_ID)){
				// 弊 饭矫乔甫 扁撅窍绊 乐阑鳖
				if( CHAR_checkRecipeFlg( ch, ITEM_RECIPE_getInt( recipe[j], ITEM_RECIPE_ID))){
					break;
				}
			}
		}
		if( j < recipenum) break;
	}
	if( i >= CHAR_MAXSKILLHAVE) goto TECH_APPENDJEWEL_ENSURE;

	// fp 家厚
	techid = TECH_getTechIndex( ch->player_addon->Skill[haveskill].Technique[havetech]);
	amount_fp = TECH_getInt( techid, TECH_FORCEPOINT);
#ifdef PUK2
	// 家厚 FP俊 焊沥阑 歹茄促
	consumptionrate = 100 - CHAR_getSkillConsumptionRate( ch, haveskill);
	if( consumptionrate <= 0 ) consumptionrate = 1;
	amount_fp = ( amount_fp * (consumptionrate * 0.01) );
#endif

	if( ch->i.ForcePoint < amount_fp) {
		// "FP啊 面盒窍瘤 臼嚼聪促."
		SYSTEMMSG( ch, TMSG(LANG_MSG_CHAR_SKILL_C_012));
		// FP 何练茄 版快绰 溜return
		goto TECH_APPENDJEWEL_ENSURE;
	}

	// 2锅掳狼 牢荐俊绰 眠啊窍绰 焊籍捞 乐绰 厘家啊 甸绢啊 乐促
	getStringFromIndexWithDelim( data, "|", 2, buf, sizeof( buf));
	haveitemindex[1] = atoi(buf) + ch->bagPage * CHAR_ITEMPAGENUM;
	itembindex = CHAR_getItemIndex( ch, haveitemindex[1]);
	if( itembindex <= 0) goto TECH_APPENDJEWEL_ENSURE;

	// 皑沥登绊 乐阑鳖?
	if( ! ITEM_getInt( itembindex, ITEM_LEAKLEVEL)){
		goto TECH_APPENDJEWEL_ENSURE;
	}
	// 家犁 B牢啊 绢恫啊 眉农茄促
	if( ITEM_getInt( itembindex, ITEM_TYPE) != ITEM_JEWEL &&
		ITEM_getInt( itembindex, ITEM_TYPE) != ITEM_MATERIAL_B)
	{
		goto TECH_APPENDJEWEL_ENSURE;
	}

	// 己傍啦 魄沥
	if( RAND( 0, 1000)){
		levelup = 1;
		flg = TRUE;
		strcpy( deleteatomstring, ITEM_getIncuseName( itembindex));
		if(SKILL_getMaxLevel( skillindex, ch->iu.player.Job) <= ch->player_addon->Skill[haveskill].SkillLevel){
			exp = 0;
		}
		else {
			exp = 10;
		}
		levelup = TECH_setSkillExp( ch, skillindex, haveskill, exp, &stm, &dex, &intelligence );
	}
	else {
		//injuryflg = 1;
		flg = FALSE;
		strcpy( deleteatomstring, ITEM_getIncuseName( itembindex));
		goto TECH_APPENDJEWEL_ENSURE;
	}


#ifdef MONSTER_HELP_TECH
	{
		Char *pethelp=NULL;
		int itemrank=0;
		itemrank=ITEM_getInt( itembindex, ITEM_LEVEL);

		//局肯悼拱 秋橇甫 捞侩 啊瓷茄啊 绢恫啊狼 眉农甫 角矫茄促
		pethelp=TECH_CheckPetHelp(ch, itemrank, PETHELP_APPNDJEWEL);
		//捞侩 啊瓷窍搁, 捞侩 啊瓷 monster狼 器牢磐啊 倒酒柯促
		if(pethelp!=NULL&&ch->wu.player.PetHelpSkill)
		{
			//农府胶呕狼 郴己摹 拌魂阑 角矫茄促
			TECH_PetHelpForAppendjewel(ch, itemrank, pethelp);
		}
	}
#endif

	// 函拳傈狼 郴备摹甫 焊粮茄促
	olddur = ITEM_getInt( newitemindex, ITEM_DURABILITY);
	oldmaxdur = ITEM_getInt( newitemindex, ITEM_MAXDURABILITY);

	// 家犁 B狼 瓤苞甫 馆康矫挪促
#ifdef PUK2
	// 府滚胶狼 瓤苞甫 歹秦 焊籍阑 钦己
	TECH_MergeItem_addItemB( ch, newitemindex, itembindex, 1);
#else
	TECH_MergeItem_addItemB( newitemindex, itembindex);
#endif

	// 函拳 饶狼 弥措 郴备摹肺何磐 厚啦阑 夸备茄促
	newmaxdur = ITEM_getInt( newitemindex, ITEM_MAXDURABILITY);
	durrate = (float)(newmaxdur) / (float)(oldmaxdur);
	print_t(LANG_MSG_AUTOMATIC_362, durrate);
	if( olddur == oldmaxdur) newdur = newmaxdur;
	else newdur = olddur * durrate;

	// 货肺款 郴备摹甫 技飘 茄促
	if( newdur >= newmaxdur) newdur = newmaxdur;
	ITEM_setInt( newitemindex, ITEM_DURABILITY, newdur);

	// 焊籍炼涝俊 狼茄 郴备摹狼 历窍
	{
		int rate = RAND(0, 100);
#if 1
		print( "\nAppendJewel Rate( " );
#endif
		for( i = 0; i < 4; i++){
#if 1
			print( "%d:%d, %d ", i, AppendJewel_durDownTbl[i][0], AppendJewel_durDownTbl[i][1] );
#endif
			if( rate <= AppendJewel_durDownTbl[i][0]){
				int dur = ITEM_getInt( newitemindex, ITEM_MAXDURABILITY);
				dur = (dur * AppendJewel_durDownTbl[i][1]) / 100;
				if( dur < 1) dur = 1;
				ITEM_setInt( newitemindex, ITEM_MAXDURABILITY, dur);
				dur = ITEM_getInt( newitemindex, ITEM_DURABILITY);
				dur = (dur * AppendJewel_durDownTbl[i][1]) / 100;
				if( dur < 1) dur = 1;
				ITEM_setInt( newitemindex, ITEM_DURABILITY, dur);
				print_t(LANG_MSG_AUTOMATIC_363, i, AppendJewel_durDownTbl[i][1]);
				break;
			}
		}
#if 1
		print( ") \n" );
#endif
	}
    int itembNo = ITEM_getInt(itembindex, ITEM_ID);

    int remain = ITEM_getInt(itembindex, ITEM_REMAIN);
    if (remain > 1) {
        remain -= 1;
        ITEM_setInt(itembindex, ITEM_REMAIN, remain);
    } else {
        // 家犁 B甫 家芭茄促
        CHAR_unsetItem(ch, haveitemindex[1]);
        ITEM_endExistItemsOne(itembindex);
    }

	// 焊籍 眠啊 啊瓷 敲贰弊甫 瘤款促
	ITEM_setInt( newitemindex, ITEM_OTHERFLG,
	( ITEM_getInt( newitemindex, ITEM_OTHERFLG) & ~ITEM_OTHERFLG_CANADDJEWEL));
    if (NL_DATA_EXTEND.use[NL_MERGEITEMFUNC]) {
        NPC_Lua_NL_MergeItemEventCallBack(ch, skillid, skilllv, newitemindex, itembNo);
    }
	// 酒捞袍 沥焊甫 价脚
	CHAR_sendItemDataOne( ch, haveitemindex[0]);
	CHAR_sendItemDataOne( ch, haveitemindex[1]);

	//流诀苞 胶懦狼 包拌绰 乐绊 乐绢
	if(SKILL_ElationCheck(ch, skillindex) == TRUE)
	{
	// 疙己摹 拌魂

		//撅瘤肺 ID甫 炼荤茄促
		if(skillid == 215 || skillid == 216){
			//炼府, 炼力
			fame = TECH_getFame3( ch, skilllv, itemrank, flg, 1);
		}else{
			//啊傍
			fame = TECH_getFame3( ch, skilllv, itemrank, flg, 0);
		}

		if( TITLE_TitleCheck( ch, TITLE_CHECK_FAMEEVENT)){
			CHAR_sendTitle( ch);
		}
	}

	CHAR_complianceParameter( ch);
	// fp 家厚
#ifdef PUK2
	// 扁贱 单捞磐肺何磐 家厚 FP甫 啊廉柯促
	// 国结 荤傈俊 拌魂捞 场抄 惑怕
//	amount_fp = TECH_getInt( techid, TECH_FORCEPOINT);
	// 府滚胶矫俊绰 FP家厚甫 临牢促
	if( ch->w.RebirthSkill ){
		rate = SKILL_RebirthParam( REBIRTH_CATEGORY_APPEND, ch->w.RebirthLevel, REBIRTH_FP_RATE );
		amount_fp = ( amount_fp * rate + 50 ) / 100;
	}
	ch->i.ForcePoint -= amount_fp;
#else
	ch->i.ForcePoint -= 50;
#endif
#ifdef PUK2
	// 府滚胶矫俊绰 burst 鸥烙阑 临牢促
	if( ch->w.RebirthSkill ){
		int		time;
		time = SKILL_RebirthParam( REBIRTH_CATEGORY_APPEND, ch->w.RebirthLevel, REBIRTH_TIME );
		CHAR_FeverTimeConsume( ch, time );
	}
#endif

#if 0
	// 惑贸 眉农 // 胶懦 饭骇狼 馆狼 犬伏肺 惑贸甫 涝绰促.
	if( skilllv >= 2 && RAND( 1,1000 ) <= 1 ) {
		char szBuffer[256];
		int iWork = skilllv*10;	// 胶懦 饭骇*10鳖瘤狼 惑贸甫 涝绰促.
		if( iWork >= 50 ) iWork = 50;		// 促父 50鳖瘤狼 惑贸绰 窍瘤 臼绰促.
		ch->i.Injury += RAND( 1, iWork );	// 己傍窍绊 乐促搁＋1?
		if( ch->i.Injury >= 100 ) ch->i.Injury = 100;
		CHAR_send_CP2_String( ch, CHAR_CP2_INJURY);
		injuryflg = TRUE;
		translate_format(szBuffer, 0, LANG_MSG_TECH_C_014, ch->c.Name );
		SYSTEMMSG( ch, szBuffer );
	}
#endif

	CHAR_send_CP_String( ch, CHAR_CP_FORCEPOINT|CHAR_CP_FAME|
						 (stm != 0 ?  CHAR_CP_STAMINA : 0 ) |
						 (dex != 0 ?  CHAR_CP_DEX : 0 ) |
						 (intelligence != 0 ?  CHAR_CP_INTELLIGENCE : 0 )
		);
#ifdef PUK2
	// 颇萍 糕滚俊霸档 沥焊甫 焊辰促
	CHAR_sendPartyParam( ch );
#endif


	// 积魂拌狼 饭骇诀 颇扼固磐 皋技瘤
	TechParameterMsg( ch, stm, dex, intelligence );
	// 扩档快俊绰 钎矫窍瘤 臼霸 窍磊.
	stm = dex = intelligence = 0;

	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%d|%s",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg,
				  (newitemindex == -1 ?  -1: ITEM_getInt( newitemindex, ITEM_BASEIMAGENUMBER)),
				  deleteatomstring);
		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return TRUE;

TECH_APPENDJEWEL_ENSURE:
	fd = getfdFromChar( ch);
	if( fd != -1 ) {
		char sendbuf[2048];
		snprintf( sendbuf, sizeof( sendbuf),
				  "%d|%d|%d|%d|%d|%d|%d|%d|%s",
				  exp, fame, levelup, stm, dex, intelligence, injuryflg,
				  (newitemindex == -1 ?  -1: ITEM_getInt( newitemindex, ITEM_BASEIMAGENUMBER)),
				  deleteatomstring);
		nrproto_TU_send( fd, flg,
						 SKILL_getInt( skillindex, SKILL_OPERATION)+1,
						 sendbuf);
	}
	return FALSE;
}
#endif /* _OPERATION_APPEND_JEWEL */

#ifdef ANIT_CHEATING_SET
//高速采集检测
int CHECK_WorkTime( Char *ch)
{
	int fd;

	fd = getfdFromChar( ch);
	if( fd <= -1 )
		return FALSE;

	if ( time_diff_sec( NowTime, Connect[fd].lastworktime) > 3 ){	//默认正常不低于3秒
		Connect[fd].lastworktime = NowTime;
		return FALSE;
	}
	++Connect[fd].errornum;
	Connect[fd].closed = 1;


    if (cgmsvcf.highspeedblock == 1) {
        CHAR_Ban(Connect[fd].ch);
//		if(setdb(Connect[fd].ch,"tbl_user","EnableFlg",0))
        eprint("Player(%s) over speed,block! \n", Connect[fd].ch->c.Name);
    }

	return TRUE;
}


//高速生产检测
#ifdef VERSION_TW	//这是台服才有的函数 客户端有对应的函数
int CHECK_ProduceTime( Char *ch)
{
	int fd;
	double producetime;
	
	fd = getfdFromChar( ch);
	if( fd <= -1 )
		return FALSE;

	producetime = time_diff_sec( NowTime, Connect[fd].producetimestart);
	if ( producetime >= Connect[fd].producetimerecv - 3 && producetime > 5 && Connect[fd].producetimerecv ) //默认正常不低于5秒
		return FALSE;	//时间正常
	//JPCG_SendWarningCode(v1, 7, 0);	//还没开始写
	Connect[fd].producetimerecv = 0;
	Connect[fd].producetimestart.tv_sec = 0;
	Connect[fd].producetimestart.tv_usec = 0;
	++Connect[fd].errornum;
	Connect[fd].closed = 1;
    if (cgmsvcf.highspeedblock == 1) {
        CHAR_Ban(Connect[fd].ch);
//		if(setdb(Connect[fd].ch,"tbl_user","EnableFlg",0))
        eprint("Player(%s) over speed,block! \n", Connect[fd].ch->c.Name);
    }
	return TRUE;
}
#else //VERSION_TW
//韩服版高速生产检测
int CHECK_ProduceTime( Char *ch)
{
	int fd;
	
	fd = getfdFromChar( ch);
	if( fd <= -1 )
		return FALSE;
	
	if ( time_diff_sec( NowTime, Connect[fd].lastproducetime) > 5 ){
		Connect[sockfd].lastproducetime = NowTime;
		return FALSE;	//时间正常
	}
	//JPCG_SendWarningCode(v1, 7, 0);	//还没开始写
	Connect[sockfd].lastproducetime = NowTime;
	++Connect[fd].errornum;
	Connect[fd].closed = 1;
	if(cgmsvcf.highspeedblock == 1){
		if(setdb(Connect[fd].ch,"tbl_user","EnableFlg",0))
			printf("Player(%s) over speed,block! \n",Connect[fd].ch->c.Name);
	}
	return TRUE;
}

#endif //VERSION_TW
#endif //ANIT_CHEATING_SET
