/*
 * Copyright (C) 1999-2001, 2012, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * JISX0212.1990-0
 */

static const unsigned short jisx0212_2uni_page22[81] = {
  /* 0x22 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x02d8, 0x02c7,
  0x00b8, 0x02d9, 0x02dd, 0x00af, 0x02db, 0x02da, 0xff5e, 0x0384,
  0x0385, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x00a1, 0x00a6, 0x00bf, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0x00ba, 0x00aa, 0x00a9, 0x00ae, 0x2122, 0x00a4,
  0x2116,
};
static const unsigned short jisx0212_2uni_page26[188] = {
  /* 0x26 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x0386, 0x0388, 0x0389, 0x038a, 0x03aa, 0xfffd, 0x038c, 0xfffd,
  0x038e, 0x03ab, 0xfffd, 0x038f, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x03ac, 0x03ad, 0x03ae, 0x03af, 0x03ca, 0x0390, 0x03cc, 0x03c2,
  0x03cd, 0x03cb, 0x03b0, 0x03ce, 0xfffd, 0xfffd,
  /* 0x27 */
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x0402, 0x0403, 0x0404, 0x0405, 0x0406, 0x0407, 0x0408,
  0x0409, 0x040a, 0x040b, 0x040c, 0x040e, 0x040f, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0x0452, 0x0453, 0x0454, 0x0455, 0x0456, 0x0457, 0x0458,
  0x0459, 0x045a, 0x045b, 0x045c, 0x045e, 0x045f,
};
static const unsigned short jisx0212_2uni_page29[275] = {
  /* 0x29 */
  0x00c6, 0x0110, 0xfffd, 0x0126, 0xfffd, 0x0132, 0xfffd, 0x0141,
  0x013f, 0xfffd, 0x014a, 0x00d8, 0x0152, 0xfffd, 0x0166, 0x00de,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0x00e6, 0x0111, 0x00f0, 0x0127, 0x0131, 0x0133, 0x0138, 0x0142,
  0x0140, 0x0149, 0x014b, 0x00f8, 0x0153, 0x00df, 0x0167, 0x00fe,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x2a */
  0x00c1, 0x00c0, 0x00c4, 0x00c2, 0x0102, 0x01cd, 0x0100, 0x0104,
  0x00c5, 0x00c3, 0x0106, 0x0108, 0x010c, 0x00c7, 0x010a, 0x010e,
  0x00c9, 0x00c8, 0x00cb, 0x00ca, 0x011a, 0x0116, 0x0112, 0x0118,
  0xfffd, 0x011c, 0x011e, 0x0122, 0x0120, 0x0124, 0x00cd, 0x00cc,
  0x00cf, 0x00ce, 0x01cf, 0x0130, 0x012a, 0x012e, 0x0128, 0x0134,
  0x0136, 0x0139, 0x013d, 0x013b, 0x0143, 0x0147, 0x0145, 0x00d1,
  0x00d3, 0x00d2, 0x00d6, 0x00d4, 0x01d1, 0x0150, 0x014c, 0x00d5,
  0x0154, 0x0158, 0x0156, 0x015a, 0x015c, 0x0160, 0x015e, 0x0164,
  0x0162, 0x00da, 0x00d9, 0x00dc, 0x00db, 0x016c, 0x01d3, 0x0170,
  0x016a, 0x0172, 0x016e, 0x0168, 0x01d7, 0x01db, 0x01d9, 0x01d5,
  0x0174, 0x00dd, 0x0178, 0x0176, 0x0179, 0x017d, 0x017b, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
  /* 0x2b */
  0x00e1, 0x00e0, 0x00e4, 0x00e2, 0x0103, 0x01ce, 0x0101, 0x0105,
  0x00e5, 0x00e3, 0x0107, 0x0109, 0x010d, 0x00e7, 0x010b, 0x010f,
  0x00e9, 0x00e8, 0x00eb, 0x00ea, 0x011b, 0x0117, 0x0113, 0x0119,
  0x01f5, 0x011d, 0x011f, 0xfffd, 0x0121, 0x0125, 0x00ed, 0x00ec,
  0x00ef, 0x00ee, 0x01d0, 0xfffd, 0x012b, 0x012f, 0x0129, 0x0135,
  0x0137, 0x013a, 0x013e, 0x013c, 0x0144, 0x0148, 0x0146, 0x00f1,
  0x00f3, 0x00f2, 0x00f6, 0x00f4, 0x01d2, 0x0151, 0x014d, 0x00f5,
  0x0155, 0x0159, 0x0157, 0x015b, 0x015d, 0x0161, 0x015f, 0x0165,
  0x0163, 0x00fa, 0x00f9, 0x00fc, 0x00fb, 0x016d, 0x01d4, 0x0171,
  0x016b, 0x0173, 0x016f, 0x0169, 0x01d8, 0x01dc, 0x01da, 0x01d6,
  0x0175, 0x00fd, 0x00ff, 0x0177, 0x017a, 0x017e, 0x017c,
};
static const unsigned short jisx0212_2uni_page30[5801] = {
  /* 0x30 */
  0x4e02, 0x4e04, 0x4e05, 0x4e0c, 0x4e12, 0x4e1f, 0x4e23, 0x4e24,
  0x4e28, 0x4e2b, 0x4e2e, 0x4e2f, 0x4e30, 0x4e35, 0x4e40, 0x4e41,
  0x4e44, 0x4e47, 0x4e51, 0x4e5a, 0x4e5c, 0x4e63, 0x4e68, 0x4e69,
  0x4e74, 0x4e75, 0x4e79, 0x4e7f, 0x4e8d, 0x4e96, 0x4e97, 0x4e9d,
  0x4eaf, 0x4eb9, 0x4ec3, 0x4ed0, 0x4eda, 0x4edb, 0x4ee0, 0x4ee1,
  0x4ee2, 0x4ee8, 0x4eef, 0x4ef1, 0x4ef3, 0x4ef5, 0x4efd, 0x4efe,
  0x4eff, 0x4f00, 0x4f02, 0x4f03, 0x4f08, 0x4f0b, 0x4f0c, 0x4f12,
  0x4f15, 0x4f16, 0x4f17, 0x4f19, 0x4f2e, 0x4f31, 0x4f60, 0x4f33,
  0x4f35, 0x4f37, 0x4f39, 0x4f3b, 0x4f3e, 0x4f40, 0x4f42, 0x4f48,
  0x4f49, 0x4f4b, 0x4f4c, 0x4f52, 0x4f54, 0x4f56, 0x4f58, 0x4f5f,
  0x4f63, 0x4f6a, 0x4f6c, 0x4f6e, 0x4f71, 0x4f77, 0x4f78, 0x4f79,
  0x4f7a, 0x4f7d, 0x4f7e, 0x4f81, 0x4f82, 0x4f84,
  /* 0x31 */
  0x4f85, 0x4f89, 0x4f8a, 0x4f8c, 0x4f8e, 0x4f90, 0x4f92, 0x4f93,
  0x4f94, 0x4f97, 0x4f99, 0x4f9a, 0x4f9e, 0x4f9f, 0x4fb2, 0x4fb7,
  0x4fb9, 0x4fbb, 0x4fbc, 0x4fbd, 0x4fbe, 0x4fc0, 0x4fc1, 0x4fc5,
  0x4fc6, 0x4fc8, 0x4fc9, 0x4fcb, 0x4fcc, 0x4fcd, 0x4fcf, 0x4fd2,
  0x4fdc, 0x4fe0, 0x4fe2, 0x4ff0, 0x4ff2, 0x4ffc, 0x4ffd, 0x4fff,
  0x5000, 0x5001, 0x5004, 0x5007, 0x500a, 0x500c, 0x500e, 0x5010,
  0x5013, 0x5017, 0x5018, 0x501b, 0x501c, 0x501d, 0x501e, 0x5022,
  0x5027, 0x502e, 0x5030, 0x5032, 0x5033, 0x5035, 0x5040, 0x5041,
  0x5042, 0x5045, 0x5046, 0x504a, 0x504c, 0x504e, 0x5051, 0x5052,
  0x5053, 0x5057, 0x5059, 0x505f, 0x5060, 0x5062, 0x5063, 0x5066,
  0x5067, 0x506a, 0x506d, 0x5070, 0x5071, 0x503b, 0x5081, 0x5083,
  0x5084, 0x5086, 0x508a, 0x508e, 0x508f, 0x5090,
  /* 0x32 */
  0x5092, 0x5093, 0x5094, 0x5096, 0x509b, 0x509c, 0x509e, 0x509f,
  0x50a0, 0x50a1, 0x50a2, 0x50aa, 0x50af, 0x50b0, 0x50b9, 0x50ba,
  0x50bd, 0x50c0, 0x50c3, 0x50c4, 0x50c7, 0x50cc, 0x50ce, 0x50d0,
  0x50d3, 0x50d4, 0x50d8, 0x50dc, 0x50dd, 0x50df, 0x50e2, 0x50e4,
  0x50e6, 0x50e8, 0x50e9, 0x50ef, 0x50f1, 0x50f6, 0x50fa, 0x50fe,
  0x5103, 0x5106, 0x5107, 0x5108, 0x510b, 0x510c, 0x510d, 0x510e,
  0x50f2, 0x5110, 0x5117, 0x5119, 0x511b, 0x511c, 0x511d, 0x511e,
  0x5123, 0x5127, 0x5128, 0x512c, 0x512d, 0x512f, 0x5131, 0x5133,
  0x5134, 0x5135, 0x5138, 0x5139, 0x5142, 0x514a, 0x514f, 0x5153,
  0x5155, 0x5157, 0x5158, 0x515f, 0x5164, 0x5166, 0x517e, 0x5183,
  0x5184, 0x518b, 0x518e, 0x5198, 0x519d, 0x51a1, 0x51a3, 0x51ad,
  0x51b8, 0x51ba, 0x51bc, 0x51be, 0x51bf, 0x51c2,
  /* 0x33 */
  0x51c8, 0x51cf, 0x51d1, 0x51d2, 0x51d3, 0x51d5, 0x51d8, 0x51de,
  0x51e2, 0x51e5, 0x51ee, 0x51f2, 0x51f3, 0x51f4, 0x51f7, 0x5201,
  0x5202, 0x5205, 0x5212, 0x5213, 0x5215, 0x5216, 0x5218, 0x5222,
  0x5228, 0x5231, 0x5232, 0x5235, 0x523c, 0x5245, 0x5249, 0x5255,
  0x5257, 0x5258, 0x525a, 0x525c, 0x525f, 0x5260, 0x5261, 0x5266,
  0x526e, 0x5277, 0x5278, 0x5279, 0x5280, 0x5282, 0x5285, 0x528a,
  0x528c, 0x5293, 0x5295, 0x5296, 0x5297, 0x5298, 0x529a, 0x529c,
  0x52a4, 0x52a5, 0x52a6, 0x52a7, 0x52af, 0x52b0, 0x52b6, 0x52b7,
  0x52b8, 0x52ba, 0x52bb, 0x52bd, 0x52c0, 0x52c4, 0x52c6, 0x52c8,
  0x52cc, 0x52cf, 0x52d1, 0x52d4, 0x52d6, 0x52db, 0x52dc, 0x52e1,
  0x52e5, 0x52e8, 0x52e9, 0x52ea, 0x52ec, 0x52f0, 0x52f1, 0x52f4,
  0x52f6, 0x52f7, 0x5300, 0x5303, 0x530a, 0x530b,
  /* 0x34 */
  0x530c, 0x5311, 0x5313, 0x5318, 0x531b, 0x531c, 0x531e, 0x531f,
  0x5325, 0x5327, 0x5328, 0x5329, 0x532b, 0x532c, 0x532d, 0x5330,
  0x5332, 0x5335, 0x533c, 0x533d, 0x533e, 0x5342, 0x534c, 0x534b,
  0x5359, 0x535b, 0x5361, 0x5363, 0x5365, 0x536c, 0x536d, 0x5372,
  0x5379, 0x537e, 0x5383, 0x5387, 0x5388, 0x538e, 0x5393, 0x5394,
  0x5399, 0x539d, 0x53a1, 0x53a4, 0x53aa, 0x53ab, 0x53af, 0x53b2,
  0x53b4, 0x53b5, 0x53b7, 0x53b8, 0x53ba, 0x53bd, 0x53c0, 0x53c5,
  0x53cf, 0x53d2, 0x53d3, 0x53d5, 0x53da, 0x53dd, 0x53de, 0x53e0,
  0x53e6, 0x53e7, 0x53f5, 0x5402, 0x5413, 0x541a, 0x5421, 0x5427,
  0x5428, 0x542a, 0x542f, 0x5431, 0x5434, 0x5435, 0x5443, 0x5444,
  0x5447, 0x544d, 0x544f, 0x545e, 0x5462, 0x5464, 0x5466, 0x5467,
  0x5469, 0x546b, 0x546d, 0x546e, 0x5474, 0x547f,
  /* 0x35 */
  0x5481, 0x5483, 0x5485, 0x5488, 0x5489, 0x548d, 0x5491, 0x5495,
  0x5496, 0x549c, 0x549f, 0x54a1, 0x54a6, 0x54a7, 0x54a9, 0x54aa,
  0x54ad, 0x54ae, 0x54b1, 0x54b7, 0x54b9, 0x54ba, 0x54bb, 0x54bf,
  0x54c6, 0x54ca, 0x54cd, 0x54ce, 0x54e0, 0x54ea, 0x54ec, 0x54ef,
  0x54f6, 0x54fc, 0x54fe, 0x54ff, 0x5500, 0x5501, 0x5505, 0x5508,
  0x5509, 0x550c, 0x550d, 0x550e, 0x5515, 0x552a, 0x552b, 0x5532,
  0x5535, 0x5536, 0x553b, 0x553c, 0x553d, 0x5541, 0x5547, 0x5549,
  0x554a, 0x554d, 0x5550, 0x5551, 0x5558, 0x555a, 0x555b, 0x555e,
  0x5560, 0x5561, 0x5564, 0x5566, 0x557f, 0x5581, 0x5582, 0x5586,
  0x5588, 0x558e, 0x558f, 0x5591, 0x5592, 0x5593, 0x5594, 0x5597,
  0x55a3, 0x55a4, 0x55ad, 0x55b2, 0x55bf, 0x55c1, 0x55c3, 0x55c6,
  0x55c9, 0x55cb, 0x55cc, 0x55ce, 0x55d1, 0x55d2,
  /* 0x36 */
  0x55d3, 0x55d7, 0x55d8, 0x55db, 0x55de, 0x55e2, 0x55e9, 0x55f6,
  0x55ff, 0x5605, 0x5608, 0x560a, 0x560d, 0x560e, 0x560f, 0x5610,
  0x5611, 0x5612, 0x5619, 0x562c, 0x5630, 0x5633, 0x5635, 0x5637,
  0x5639, 0x563b, 0x563c, 0x563d, 0x563f, 0x5640, 0x5641, 0x5643,
  0x5644, 0x5646, 0x5649, 0x564b, 0x564d, 0x564f, 0x5654, 0x565e,
  0x5660, 0x5661, 0x5662, 0x5663, 0x5666, 0x5669, 0x566d, 0x566f,
  0x5671, 0x5672, 0x5675, 0x5684, 0x5685, 0x5688, 0x568b, 0x568c,
  0x5695, 0x5699, 0x569a, 0x569d, 0x569e, 0x569f, 0x56a6, 0x56a7,
  0x56a8, 0x56a9, 0x56ab, 0x56ac, 0x56ad, 0x56b1, 0x56b3, 0x56b7,
  0x56be, 0x56c5, 0x56c9, 0x56ca, 0x56cb, 0x56cf, 0x56d0, 0x56cc,
  0x56cd, 0x56d9, 0x56dc, 0x56dd, 0x56df, 0x56e1, 0x56e4, 0x56e5,
  0x56e6, 0x56e7, 0x56e8, 0x56f1, 0x56eb, 0x56ed,
  /* 0x37 */
  0x56f6, 0x56f7, 0x5701, 0x5702, 0x5707, 0x570a, 0x570c, 0x5711,
  0x5715, 0x571a, 0x571b, 0x571d, 0x5720, 0x5722, 0x5723, 0x5724,
  0x5725, 0x5729, 0x572a, 0x572c, 0x572e, 0x572f, 0x5733, 0x5734,
  0x573d, 0x573e, 0x573f, 0x5745, 0x5746, 0x574c, 0x574d, 0x5752,
  0x5762, 0x5765, 0x5767, 0x5768, 0x576b, 0x576d, 0x576e, 0x576f,
  0x5770, 0x5771, 0x5773, 0x5774, 0x5775, 0x5777, 0x5779, 0x577a,
  0x577b, 0x577c, 0x577e, 0x5781, 0x5783, 0x578c, 0x5794, 0x5797,
  0x5799, 0x579a, 0x579c, 0x579d, 0x579e, 0x579f, 0x57a1, 0x5795,
  0x57a7, 0x57a8, 0x57a9, 0x57ac, 0x57b8, 0x57bd, 0x57c7, 0x57c8,
  0x57cc, 0x57cf, 0x57d5, 0x57dd, 0x57de, 0x57e4, 0x57e6, 0x57e7,
  0x57e9, 0x57ed, 0x57f0, 0x57f5, 0x57f6, 0x57f8, 0x57fd, 0x57fe,
  0x57ff, 0x5803, 0x5804, 0x5808, 0x5809, 0x57e1,
  /* 0x38 */
  0x580c, 0x580d, 0x581b, 0x581e, 0x581f, 0x5820, 0x5826, 0x5827,
  0x582d, 0x5832, 0x5839, 0x583f, 0x5849, 0x584c, 0x584d, 0x584f,
  0x5850, 0x5855, 0x585f, 0x5861, 0x5864, 0x5867, 0x5868, 0x5878,
  0x587c, 0x587f, 0x5880, 0x5881, 0x5887, 0x5888, 0x5889, 0x588a,
  0x588c, 0x588d, 0x588f, 0x5890, 0x5894, 0x5896, 0x589d, 0x58a0,
  0x58a1, 0x58a2, 0x58a6, 0x58a9, 0x58b1, 0x58b2, 0x58c4, 0x58bc,
  0x58c2, 0x58c8, 0x58cd, 0x58ce, 0x58d0, 0x58d2, 0x58d4, 0x58d6,
  0x58da, 0x58dd, 0x58e1, 0x58e2, 0x58e9, 0x58f3, 0x5905, 0x5906,
  0x590b, 0x590c, 0x5912, 0x5913, 0x5914, 0x8641, 0x591d, 0x5921,
  0x5923, 0x5924, 0x5928, 0x592f, 0x5930, 0x5933, 0x5935, 0x5936,
  0x593f, 0x5943, 0x5946, 0x5952, 0x5953, 0x5959, 0x595b, 0x595d,
  0x595e, 0x595f, 0x5961, 0x5963, 0x596b, 0x596d,
  /* 0x39 */
  0x596f, 0x5972, 0x5975, 0x5976, 0x5979, 0x597b, 0x597c, 0x598b,
  0x598c, 0x598e, 0x5992, 0x5995, 0x5997, 0x599f, 0x59a4, 0x59a7,
  0x59ad, 0x59ae, 0x59af, 0x59b0, 0x59b3, 0x59b7, 0x59ba, 0x59bc,
  0x59c1, 0x59c3, 0x59c4, 0x59c8, 0x59ca, 0x59cd, 0x59d2, 0x59dd,
  0x59de, 0x59df, 0x59e3, 0x59e4, 0x59e7, 0x59ee, 0x59ef, 0x59f1,
  0x59f2, 0x59f4, 0x59f7, 0x5a00, 0x5a04, 0x5a0c, 0x5a0d, 0x5a0e,
  0x5a12, 0x5a13, 0x5a1e, 0x5a23, 0x5a24, 0x5a27, 0x5a28, 0x5a2a,
  0x5a2d, 0x5a30, 0x5a44, 0x5a45, 0x5a47, 0x5a48, 0x5a4c, 0x5a50,
  0x5a55, 0x5a5e, 0x5a63, 0x5a65, 0x5a67, 0x5a6d, 0x5a77, 0x5a7a,
  0x5a7b, 0x5a7e, 0x5a8b, 0x5a90, 0x5a93, 0x5a96, 0x5a99, 0x5a9c,
  0x5a9e, 0x5a9f, 0x5aa0, 0x5aa2, 0x5aa7, 0x5aac, 0x5ab1, 0x5ab2,
  0x5ab3, 0x5ab5, 0x5ab8, 0x5aba, 0x5abb, 0x5abf,
  /* 0x3a */
  0x5ac4, 0x5ac6, 0x5ac8, 0x5acf, 0x5ada, 0x5adc, 0x5ae0, 0x5ae5,
  0x5aea, 0x5aee, 0x5af5, 0x5af6, 0x5afd, 0x5b00, 0x5b01, 0x5b08,
  0x5b17, 0x5b34, 0x5b19, 0x5b1b, 0x5b1d, 0x5b21, 0x5b25, 0x5b2d,
  0x5b38, 0x5b41, 0x5b4b, 0x5b4c, 0x5b52, 0x5b56, 0x5b5e, 0x5b68,
  0x5b6e, 0x5b6f, 0x5b7c, 0x5b7d, 0x5b7e, 0x5b7f, 0x5b81, 0x5b84,
  0x5b86, 0x5b8a, 0x5b8e, 0x5b90, 0x5b91, 0x5b93, 0x5b94, 0x5b96,
  0x5ba8, 0x5ba9, 0x5bac, 0x5bad, 0x5baf, 0x5bb1, 0x5bb2, 0x5bb7,
  0x5bba, 0x5bbc, 0x5bc0, 0x5bc1, 0x5bcd, 0x5bcf, 0x5bd6, 0x5bd7,
  0x5bd8, 0x5bd9, 0x5bda, 0x5be0, 0x5bef, 0x5bf1, 0x5bf4, 0x5bfd,
  0x5c0c, 0x5c17, 0x5c1e, 0x5c1f, 0x5c23, 0x5c26, 0x5c29, 0x5c2b,
  0x5c2c, 0x5c2e, 0x5c30, 0x5c32, 0x5c35, 0x5c36, 0x5c59, 0x5c5a,
  0x5c5c, 0x5c62, 0x5c63, 0x5c67, 0x5c68, 0x5c69,
  /* 0x3b */
  0x5c6d, 0x5c70, 0x5c74, 0x5c75, 0x5c7a, 0x5c7b, 0x5c7c, 0x5c7d,
  0x5c87, 0x5c88, 0x5c8a, 0x5c8f, 0x5c92, 0x5c9d, 0x5c9f, 0x5ca0,
  0x5ca2, 0x5ca3, 0x5ca6, 0x5caa, 0x5cb2, 0x5cb4, 0x5cb5, 0x5cba,
  0x5cc9, 0x5ccb, 0x5cd2, 0x5cdd, 0x5cd7, 0x5cee, 0x5cf1, 0x5cf2,
  0x5cf4, 0x5d01, 0x5d06, 0x5d0d, 0x5d12, 0x5d2b, 0x5d23, 0x5d24,
  0x5d26, 0x5d27, 0x5d31, 0x5d34, 0x5d39, 0x5d3d, 0x5d3f, 0x5d42,
  0x5d43, 0x5d46, 0x5d48, 0x5d55, 0x5d51, 0x5d59, 0x5d4a, 0x5d5f,
  0x5d60, 0x5d61, 0x5d62, 0x5d64, 0x5d6a, 0x5d6d, 0x5d70, 0x5d79,
  0x5d7a, 0x5d7e, 0x5d7f, 0x5d81, 0x5d83, 0x5d88, 0x5d8a, 0x5d92,
  0x5d93, 0x5d94, 0x5d95, 0x5d99, 0x5d9b, 0x5d9f, 0x5da0, 0x5da7,
  0x5dab, 0x5db0, 0x5db4, 0x5db8, 0x5db9, 0x5dc3, 0x5dc7, 0x5dcb,
  0x5dd0, 0x5dce, 0x5dd8, 0x5dd9, 0x5de0, 0x5de4,
  /* 0x3c */
  0x5de9, 0x5df8, 0x5df9, 0x5e00, 0x5e07, 0x5e0d, 0x5e12, 0x5e14,
  0x5e15, 0x5e18, 0x5e1f, 0x5e20, 0x5e2e, 0x5e28, 0x5e32, 0x5e35,
  0x5e3e, 0x5e4b, 0x5e50, 0x5e49, 0x5e51, 0x5e56, 0x5e58, 0x5e5b,
  0x5e5c, 0x5e5e, 0x5e68, 0x5e6a, 0x5e6b, 0x5e6c, 0x5e6d, 0x5e6e,
  0x5e70, 0x5e80, 0x5e8b, 0x5e8e, 0x5ea2, 0x5ea4, 0x5ea5, 0x5ea8,
  0x5eaa, 0x5eac, 0x5eb1, 0x5eb3, 0x5ebd, 0x5ebe, 0x5ebf, 0x5ec6,
  0x5ecc, 0x5ecb, 0x5ece, 0x5ed1, 0x5ed2, 0x5ed4, 0x5ed5, 0x5edc,
  0x5ede, 0x5ee5, 0x5eeb, 0x5f02, 0x5f06, 0x5f07, 0x5f08, 0x5f0e,
  0x5f19, 0x5f1c, 0x5f1d, 0x5f21, 0x5f22, 0x5f23, 0x5f24, 0x5f28,
  0x5f2b, 0x5f2c, 0x5f2e, 0x5f30, 0x5f34, 0x5f36, 0x5f3b, 0x5f3d,
  0x5f3f, 0x5f40, 0x5f44, 0x5f45, 0x5f47, 0x5f4d, 0x5f50, 0x5f54,
  0x5f58, 0x5f5b, 0x5f60, 0x5f63, 0x5f64, 0x5f67,
  /* 0x3d */
  0x5f6f, 0x5f72, 0x5f74, 0x5f75, 0x5f78, 0x5f7a, 0x5f7d, 0x5f7e,
  0x5f89, 0x5f8d, 0x5f8f, 0x5f96, 0x5f9c, 0x5f9d, 0x5fa2, 0x5fa7,
  0x5fab, 0x5fa4, 0x5fac, 0x5faf, 0x5fb0, 0x5fb1, 0x5fb8, 0x5fc4,
  0x5fc7, 0x5fc8, 0x5fc9, 0x5fcb, 0x5fd0, 0x5fd1, 0x5fd2, 0x5fd3,
  0x5fd4, 0x5fde, 0x5fe1, 0x5fe2, 0x5fe8, 0x5fe9, 0x5fea, 0x5fec,
  0x5fed, 0x5fee, 0x5fef, 0x5ff2, 0x5ff3, 0x5ff6, 0x5ffa, 0x5ffc,
  0x6007, 0x600a, 0x600d, 0x6013, 0x6014, 0x6017, 0x6018, 0x601a,
  0x601f, 0x6024, 0x602d, 0x6033, 0x6035, 0x6040, 0x6047, 0x6048,
  0x6049, 0x604c, 0x6051, 0x6054, 0x6056, 0x6057, 0x605d, 0x6061,
  0x6067, 0x6071, 0x607e, 0x607f, 0x6082, 0x6086, 0x6088, 0x608a,
  0x608e, 0x6091, 0x6093, 0x6095, 0x6098, 0x609d, 0x609e, 0x60a2,
  0x60a4, 0x60a5, 0x60a8, 0x60b0, 0x60b1, 0x60b7,
  /* 0x3e */
  0x60bb, 0x60be, 0x60c2, 0x60c4, 0x60c8, 0x60c9, 0x60ca, 0x60cb,
  0x60ce, 0x60cf, 0x60d4, 0x60d5, 0x60d9, 0x60db, 0x60dd, 0x60de,
  0x60e2, 0x60e5, 0x60f2, 0x60f5, 0x60f8, 0x60fc, 0x60fd, 0x6102,
  0x6107, 0x610a, 0x610c, 0x6110, 0x6111, 0x6112, 0x6113, 0x6114,
  0x6116, 0x6117, 0x6119, 0x611c, 0x611e, 0x6122, 0x612a, 0x612b,
  0x6130, 0x6131, 0x6135, 0x6136, 0x6137, 0x6139, 0x6141, 0x6145,
  0x6146, 0x6149, 0x615e, 0x6160, 0x616c, 0x6172, 0x6178, 0x617b,
  0x617c, 0x617f, 0x6180, 0x6181, 0x6183, 0x6184, 0x618b, 0x618d,
  0x6192, 0x6193, 0x6197, 0x6198, 0x619c, 0x619d, 0x619f, 0x61a0,
  0x61a5, 0x61a8, 0x61aa, 0x61ad, 0x61b8, 0x61b9, 0x61bc, 0x61c0,
  0x61c1, 0x61c2, 0x61ce, 0x61cf, 0x61d5, 0x61dc, 0x61dd, 0x61de,
  0x61df, 0x61e1, 0x61e2, 0x61e7, 0x61e9, 0x61e5,
  /* 0x3f */
  0x61ec, 0x61ed, 0x61ef, 0x6201, 0x6203, 0x6204, 0x6207, 0x6213,
  0x6215, 0x621c, 0x6220, 0x6222, 0x6223, 0x6227, 0x6229, 0x622b,
  0x6239, 0x623d, 0x6242, 0x6243, 0x6244, 0x6246, 0x624c, 0x6250,
  0x6251, 0x6252, 0x6254, 0x6256, 0x625a, 0x625c, 0x6264, 0x626d,
  0x626f, 0x6273, 0x627a, 0x627d, 0x628d, 0x628e, 0x628f, 0x6290,
  0x62a6, 0x62a8, 0x62b3, 0x62b6, 0x62b7, 0x62ba, 0x62be, 0x62bf,
  0x62c4, 0x62ce, 0x62d5, 0x62d6, 0x62da, 0x62ea, 0x62f2, 0x62f4,
  0x62fc, 0x62fd, 0x6303, 0x6304, 0x630a, 0x630b, 0x630d, 0x6310,
  0x6313, 0x6316, 0x6318, 0x6329, 0x632a, 0x632d, 0x6335, 0x6336,
  0x6339, 0x633c, 0x6341, 0x6342, 0x6343, 0x6344, 0x6346, 0x634a,
  0x634b, 0x634e, 0x6352, 0x6353, 0x6354, 0x6358, 0x635b, 0x6365,
  0x6366, 0x636c, 0x636d, 0x6371, 0x6374, 0x6375,
  /* 0x40 */
  0x6378, 0x637c, 0x637d, 0x637f, 0x6382, 0x6384, 0x6387, 0x638a,
  0x6390, 0x6394, 0x6395, 0x6399, 0x639a, 0x639e, 0x63a4, 0x63a6,
  0x63ad, 0x63ae, 0x63af, 0x63bd, 0x63c1, 0x63c5, 0x63c8, 0x63ce,
  0x63d1, 0x63d3, 0x63d4, 0x63d5, 0x63dc, 0x63e0, 0x63e5, 0x63ea,
  0x63ec, 0x63f2, 0x63f3, 0x63f5, 0x63f8, 0x63f9, 0x6409, 0x640a,
  0x6410, 0x6412, 0x6414, 0x6418, 0x641e, 0x6420, 0x6422, 0x6424,
  0x6425, 0x6429, 0x642a, 0x642f, 0x6430, 0x6435, 0x643d, 0x643f,
  0x644b, 0x644f, 0x6451, 0x6452, 0x6453, 0x6454, 0x645a, 0x645b,
  0x645c, 0x645d, 0x645f, 0x6460, 0x6461, 0x6463, 0x646d, 0x6473,
  0x6474, 0x647b, 0x647d, 0x6485, 0x6487, 0x648f, 0x6490, 0x6491,
  0x6498, 0x6499, 0x649b, 0x649d, 0x649f, 0x64a1, 0x64a3, 0x64a6,
  0x64a8, 0x64ac, 0x64b3, 0x64bd, 0x64be, 0x64bf,
  /* 0x41 */
  0x64c4, 0x64c9, 0x64ca, 0x64cb, 0x64cc, 0x64ce, 0x64d0, 0x64d1,
  0x64d5, 0x64d7, 0x64e4, 0x64e5, 0x64e9, 0x64ea, 0x64ed, 0x64f0,
  0x64f5, 0x64f7, 0x64fb, 0x64ff, 0x6501, 0x6504, 0x6508, 0x6509,
  0x650a, 0x650f, 0x6513, 0x6514, 0x6516, 0x6519, 0x651b, 0x651e,
  0x651f, 0x6522, 0x6526, 0x6529, 0x652e, 0x6531, 0x653a, 0x653c,
  0x653d, 0x6543, 0x6547, 0x6549, 0x6550, 0x6552, 0x6554, 0x655f,
  0x6560, 0x6567, 0x656b, 0x657a, 0x657d, 0x6581, 0x6585, 0x658a,
  0x6592, 0x6595, 0x6598, 0x659d, 0x65a0, 0x65a3, 0x65a6, 0x65ae,
  0x65b2, 0x65b3, 0x65b4, 0x65bf, 0x65c2, 0x65c8, 0x65c9, 0x65ce,
  0x65d0, 0x65d4, 0x65d6, 0x65d8, 0x65df, 0x65f0, 0x65f2, 0x65f4,
  0x65f5, 0x65f9, 0x65fe, 0x65ff, 0x6600, 0x6604, 0x6608, 0x6609,
  0x660d, 0x6611, 0x6612, 0x6615, 0x6616, 0x661d,
  /* 0x42 */
  0x661e, 0x6621, 0x6622, 0x6623, 0x6624, 0x6626, 0x6629, 0x662a,
  0x662b, 0x662c, 0x662e, 0x6630, 0x6631, 0x6633, 0x6639, 0x6637,
  0x6640, 0x6645, 0x6646, 0x664a, 0x664c, 0x6651, 0x664e, 0x6657,
  0x6658, 0x6659, 0x665b, 0x665c, 0x6660, 0x6661, 0x66fb, 0x666a,
  0x666b, 0x666c, 0x667e, 0x6673, 0x6675, 0x667f, 0x6677, 0x6678,
  0x6679, 0x667b, 0x6680, 0x667c, 0x668b, 0x668c, 0x668d, 0x6690,
  0x6692, 0x6699, 0x669a, 0x669b, 0x669c, 0x669f, 0x66a0, 0x66a4,
  0x66ad, 0x66b1, 0x66b2, 0x66b5, 0x66bb, 0x66bf, 0x66c0, 0x66c2,
  0x66c3, 0x66c8, 0x66cc, 0x66ce, 0x66cf, 0x66d4, 0x66db, 0x66df,
  0x66e8, 0x66eb, 0x66ec, 0x66ee, 0x66fa, 0x6705, 0x6707, 0x670e,
  0x6713, 0x6719, 0x671c, 0x6720, 0x6722, 0x6733, 0x673e, 0x6745,
  0x6747, 0x6748, 0x674c, 0x6754, 0x6755, 0x675d,
  /* 0x43 */
  0x6766, 0x676c, 0x676e, 0x6774, 0x6776, 0x677b, 0x6781, 0x6784,
  0x678e, 0x678f, 0x6791, 0x6793, 0x6796, 0x6798, 0x6799, 0x679b,
  0x67b0, 0x67b1, 0x67b2, 0x67b5, 0x67bb, 0x67bc, 0x67bd, 0x67f9,
  0x67c0, 0x67c2, 0x67c3, 0x67c5, 0x67c8, 0x67c9, 0x67d2, 0x67d7,
  0x67d9, 0x67dc, 0x67e1, 0x67e6, 0x67f0, 0x67f2, 0x67f6, 0x67f7,
  0x6852, 0x6814, 0x6819, 0x681d, 0x681f, 0x6828, 0x6827, 0x682c,
  0x682d, 0x682f, 0x6830, 0x6831, 0x6833, 0x683b, 0x683f, 0x6844,
  0x6845, 0x684a, 0x684c, 0x6855, 0x6857, 0x6858, 0x685b, 0x686b,
  0x686e, 0x686f, 0x6870, 0x6871, 0x6872, 0x6875, 0x6879, 0x687a,
  0x687b, 0x687c, 0x6882, 0x6884, 0x6886, 0x6888, 0x6896, 0x6898,
  0x689a, 0x689c, 0x68a1, 0x68a3, 0x68a5, 0x68a9, 0x68aa, 0x68ae,
  0x68b2, 0x68bb, 0x68c5, 0x68c8, 0x68cc, 0x68cf,
  /* 0x44 */
  0x68d0, 0x68d1, 0x68d3, 0x68d6, 0x68d9, 0x68dc, 0x68dd, 0x68e5,
  0x68e8, 0x68ea, 0x68eb, 0x68ec, 0x68ed, 0x68f0, 0x68f1, 0x68f5,
  0x68f6, 0x68fb, 0x68fc, 0x68fd, 0x6906, 0x6909, 0x690a, 0x6910,
  0x6911, 0x6913, 0x6916, 0x6917, 0x6931, 0x6933, 0x6935, 0x6938,
  0x693b, 0x6942, 0x6945, 0x6949, 0x694e, 0x6957, 0x695b, 0x6963,
  0x6964, 0x6965, 0x6966, 0x6968, 0x6969, 0x696c, 0x6970, 0x6971,
  0x6972, 0x697a, 0x697b, 0x697f, 0x6980, 0x698d, 0x6992, 0x6996,
  0x6998, 0x69a1, 0x69a5, 0x69a6, 0x69a8, 0x69ab, 0x69ad, 0x69af,
  0x69b7, 0x69b8, 0x69ba, 0x69bc, 0x69c5, 0x69c8, 0x69d1, 0x69d6,
  0x69d7, 0x69e2, 0x69e5, 0x69ee, 0x69ef, 0x69f1, 0x69f3, 0x69f5,
  0x69fe, 0x6a00, 0x6a01, 0x6a03, 0x6a0f, 0x6a11, 0x6a15, 0x6a1a,
  0x6a1d, 0x6a20, 0x6a24, 0x6a28, 0x6a30, 0x6a32,
  /* 0x45 */
  0x6a34, 0x6a37, 0x6a3b, 0x6a3e, 0x6a3f, 0x6a45, 0x6a46, 0x6a49,
  0x6a4a, 0x6a4e, 0x6a50, 0x6a51, 0x6a52, 0x6a55, 0x6a56, 0x6a5b,
  0x6a64, 0x6a67, 0x6a6a, 0x6a71, 0x6a73, 0x6a7e, 0x6a81, 0x6a83,
  0x6a86, 0x6a87, 0x6a89, 0x6a8b, 0x6a91, 0x6a9b, 0x6a9d, 0x6a9e,
  0x6a9f, 0x6aa5, 0x6aab, 0x6aaf, 0x6ab0, 0x6ab1, 0x6ab4, 0x6abd,
  0x6abe, 0x6abf, 0x6ac6, 0x6ac9, 0x6ac8, 0x6acc, 0x6ad0, 0x6ad4,
  0x6ad5, 0x6ad6, 0x6adc, 0x6add, 0x6ae4, 0x6ae7, 0x6aec, 0x6af0,
  0x6af1, 0x6af2, 0x6afc, 0x6afd, 0x6b02, 0x6b03, 0x6b06, 0x6b07,
  0x6b09, 0x6b0f, 0x6b10, 0x6b11, 0x6b17, 0x6b1b, 0x6b1e, 0x6b24,
  0x6b28, 0x6b2b, 0x6b2c, 0x6b2f, 0x6b35, 0x6b36, 0x6b3b, 0x6b3f,
  0x6b46, 0x6b4a, 0x6b4d, 0x6b52, 0x6b56, 0x6b58, 0x6b5d, 0x6b60,
  0x6b67, 0x6b6b, 0x6b6e, 0x6b70, 0x6b75, 0x6b7d,
  /* 0x46 */
  0x6b7e, 0x6b82, 0x6b85, 0x6b97, 0x6b9b, 0x6b9f, 0x6ba0, 0x6ba2,
  0x6ba3, 0x6ba8, 0x6ba9, 0x6bac, 0x6bad, 0x6bae, 0x6bb0, 0x6bb8,
  0x6bb9, 0x6bbd, 0x6bbe, 0x6bc3, 0x6bc4, 0x6bc9, 0x6bcc, 0x6bd6,
  0x6bda, 0x6be1, 0x6be3, 0x6be6, 0x6be7, 0x6bee, 0x6bf1, 0x6bf7,
  0x6bf9, 0x6bff, 0x6c02, 0x6c04, 0x6c05, 0x6c09, 0x6c0d, 0x6c0e,
  0x6c10, 0x6c12, 0x6c19, 0x6c1f, 0x6c26, 0x6c27, 0x6c28, 0x6c2c,
  0x6c2e, 0x6c33, 0x6c35, 0x6c36, 0x6c3a, 0x6c3b, 0x6c3f, 0x6c4a,
  0x6c4b, 0x6c4d, 0x6c4f, 0x6c52, 0x6c54, 0x6c59, 0x6c5b, 0x6c5c,
  0x6c6b, 0x6c6d, 0x6c6f, 0x6c74, 0x6c76, 0x6c78, 0x6c79, 0x6c7b,
  0x6c85, 0x6c86, 0x6c87, 0x6c89, 0x6c94, 0x6c95, 0x6c97, 0x6c98,
  0x6c9c, 0x6c9f, 0x6cb0, 0x6cb2, 0x6cb4, 0x6cc2, 0x6cc6, 0x6ccd,
  0x6ccf, 0x6cd0, 0x6cd1, 0x6cd2, 0x6cd4, 0x6cd6,
  /* 0x47 */
  0x6cda, 0x6cdc, 0x6ce0, 0x6ce7, 0x6ce9, 0x6ceb, 0x6cec, 0x6cee,
  0x6cf2, 0x6cf4, 0x6d04, 0x6d07, 0x6d0a, 0x6d0e, 0x6d0f, 0x6d11,
  0x6d13, 0x6d1a, 0x6d26, 0x6d27, 0x6d28, 0x6c67, 0x6d2e, 0x6d2f,
  0x6d31, 0x6d39, 0x6d3c, 0x6d3f, 0x6d57, 0x6d5e, 0x6d5f, 0x6d61,
  0x6d65, 0x6d67, 0x6d6f, 0x6d70, 0x6d7c, 0x6d82, 0x6d87, 0x6d91,
  0x6d92, 0x6d94, 0x6d96, 0x6d97, 0x6d98, 0x6daa, 0x6dac, 0x6db4,
  0x6db7, 0x6db9, 0x6dbd, 0x6dbf, 0x6dc4, 0x6dc8, 0x6dca, 0x6dce,
  0x6dcf, 0x6dd6, 0x6ddb, 0x6ddd, 0x6ddf, 0x6de0, 0x6de2, 0x6de5,
  0x6de9, 0x6def, 0x6df0, 0x6df4, 0x6df6, 0x6dfc, 0x6e00, 0x6e04,
  0x6e1e, 0x6e22, 0x6e27, 0x6e32, 0x6e36, 0x6e39, 0x6e3b, 0x6e3c,
  0x6e44, 0x6e45, 0x6e48, 0x6e49, 0x6e4b, 0x6e4f, 0x6e51, 0x6e52,
  0x6e53, 0x6e54, 0x6e57, 0x6e5c, 0x6e5d, 0x6e5e,
  /* 0x48 */
  0x6e62, 0x6e63, 0x6e68, 0x6e73, 0x6e7b, 0x6e7d, 0x6e8d, 0x6e93,
  0x6e99, 0x6ea0, 0x6ea7, 0x6ead, 0x6eae, 0x6eb1, 0x6eb3, 0x6ebb,
  0x6ebf, 0x6ec0, 0x6ec1, 0x6ec3, 0x6ec7, 0x6ec8, 0x6eca, 0x6ecd,
  0x6ece, 0x6ecf, 0x6eeb, 0x6eed, 0x6eee, 0x6ef9, 0x6efb, 0x6efd,
  0x6f04, 0x6f08, 0x6f0a, 0x6f0c, 0x6f0d, 0x6f16, 0x6f18, 0x6f1a,
  0x6f1b, 0x6f26, 0x6f29, 0x6f2a, 0x6f2f, 0x6f30, 0x6f33, 0x6f36,
  0x6f3b, 0x6f3c, 0x6f2d, 0x6f4f, 0x6f51, 0x6f52, 0x6f53, 0x6f57,
  0x6f59, 0x6f5a, 0x6f5d, 0x6f5e, 0x6f61, 0x6f62, 0x6f68, 0x6f6c,
  0x6f7d, 0x6f7e, 0x6f83, 0x6f87, 0x6f88, 0x6f8b, 0x6f8c, 0x6f8d,
  0x6f90, 0x6f92, 0x6f93, 0x6f94, 0x6f96, 0x6f9a, 0x6f9f, 0x6fa0,
  0x6fa5, 0x6fa6, 0x6fa7, 0x6fa8, 0x6fae, 0x6faf, 0x6fb0, 0x6fb5,
  0x6fb6, 0x6fbc, 0x6fc5, 0x6fc7, 0x6fc8, 0x6fca,
  /* 0x49 */
  0x6fda, 0x6fde, 0x6fe8, 0x6fe9, 0x6ff0, 0x6ff5, 0x6ff9, 0x6ffc,
  0x6ffd, 0x7000, 0x7005, 0x7006, 0x7007, 0x700d, 0x7017, 0x7020,
  0x7023, 0x702f, 0x7034, 0x7037, 0x7039, 0x703c, 0x7043, 0x7044,
  0x7048, 0x7049, 0x704a, 0x704b, 0x7054, 0x7055, 0x705d, 0x705e,
  0x704e, 0x7064, 0x7065, 0x706c, 0x706e, 0x7075, 0x7076, 0x707e,
  0x7081, 0x7085, 0x7086, 0x7094, 0x7095, 0x7096, 0x7097, 0x7098,
  0x709b, 0x70a4, 0x70ab, 0x70b0, 0x70b1, 0x70b4, 0x70b7, 0x70ca,
  0x70d1, 0x70d3, 0x70d4, 0x70d5, 0x70d6, 0x70d8, 0x70dc, 0x70e4,
  0x70fa, 0x7103, 0x7104, 0x7105, 0x7106, 0x7107, 0x710b, 0x710c,
  0x710f, 0x711e, 0x7120, 0x712b, 0x712d, 0x712f, 0x7130, 0x7131,
  0x7138, 0x7141, 0x7145, 0x7146, 0x7147, 0x714a, 0x714b, 0x7150,
  0x7152, 0x7157, 0x715a, 0x715c, 0x715e, 0x7160,
  /* 0x4a */
  0x7168, 0x7179, 0x7180, 0x7185, 0x7187, 0x718c, 0x7192, 0x719a,
  0x719b, 0x71a0, 0x71a2, 0x71af, 0x71b0, 0x71b2, 0x71b3, 0x71ba,
  0x71bf, 0x71c0, 0x71c1, 0x71c4, 0x71cb, 0x71cc, 0x71d3, 0x71d6,
  0x71d9, 0x71da, 0x71dc, 0x71f8, 0x71fe, 0x7200, 0x7207, 0x7208,
  0x7209, 0x7213, 0x7217, 0x721a, 0x721d, 0x721f, 0x7224, 0x722b,
  0x722f, 0x7234, 0x7238, 0x7239, 0x7241, 0x7242, 0x7243, 0x7245,
  0x724e, 0x724f, 0x7250, 0x7253, 0x7255, 0x7256, 0x725a, 0x725c,
  0x725e, 0x7260, 0x7263, 0x7268, 0x726b, 0x726e, 0x726f, 0x7271,
  0x7277, 0x7278, 0x727b, 0x727c, 0x727f, 0x7284, 0x7289, 0x728d,
  0x728e, 0x7293, 0x729b, 0x72a8, 0x72ad, 0x72ae, 0x72b1, 0x72b4,
  0x72be, 0x72c1, 0x72c7, 0x72c9, 0x72cc, 0x72d5, 0x72d6, 0x72d8,
  0x72df, 0x72e5, 0x72f3, 0x72f4, 0x72fa, 0x72fb,
  /* 0x4b */
  0x72fe, 0x7302, 0x7304, 0x7305, 0x7307, 0x730b, 0x730d, 0x7312,
  0x7313, 0x7318, 0x7319, 0x731e, 0x7322, 0x7324, 0x7327, 0x7328,
  0x732c, 0x7331, 0x7332, 0x7335, 0x733a, 0x733b, 0x733d, 0x7343,
  0x734d, 0x7350, 0x7352, 0x7356, 0x7358, 0x735d, 0x735e, 0x735f,
  0x7360, 0x7366, 0x7367, 0x7369, 0x736b, 0x736c, 0x736e, 0x736f,
  0x7371, 0x7377, 0x7379, 0x737c, 0x7380, 0x7381, 0x7383, 0x7385,
  0x7386, 0x738e, 0x7390, 0x7393, 0x7395, 0x7397, 0x7398, 0x739c,
  0x739e, 0x739f, 0x73a0, 0x73a2, 0x73a5, 0x73a6, 0x73aa, 0x73ab,
  0x73ad, 0x73b5, 0x73b7, 0x73b9, 0x73bc, 0x73bd, 0x73bf, 0x73c5,
  0x73c6, 0x73c9, 0x73cb, 0x73cc, 0x73cf, 0x73d2, 0x73d3, 0x73d6,
  0x73d9, 0x73dd, 0x73e1, 0x73e3, 0x73e6, 0x73e7, 0x73e9, 0x73f4,
  0x73f5, 0x73f7, 0x73f9, 0x73fa, 0x73fb, 0x73fd,
  /* 0x4c */
  0x73ff, 0x7400, 0x7401, 0x7404, 0x7407, 0x740a, 0x7411, 0x741a,
  0x741b, 0x7424, 0x7426, 0x7428, 0x7429, 0x742a, 0x742b, 0x742c,
  0x742d, 0x742e, 0x742f, 0x7430, 0x7431, 0x7439, 0x7440, 0x7443,
  0x7444, 0x7446, 0x7447, 0x744b, 0x744d, 0x7451, 0x7452, 0x7457,
  0x745d, 0x7462, 0x7466, 0x7467, 0x7468, 0x746b, 0x746d, 0x746e,
  0x7471, 0x7472, 0x7480, 0x7481, 0x7485, 0x7486, 0x7487, 0x7489,
  0x748f, 0x7490, 0x7491, 0x7492, 0x7498, 0x7499, 0x749a, 0x749c,
  0x749f, 0x74a0, 0x74a1, 0x74a3, 0x74a6, 0x74a8, 0x74a9, 0x74aa,
  0x74ab, 0x74ae, 0x74af, 0x74b1, 0x74b2, 0x74b5, 0x74b9, 0x74bb,
  0x74bf, 0x74c8, 0x74c9, 0x74cc, 0x74d0, 0x74d3, 0x74d8, 0x74da,
  0x74db, 0x74de, 0x74df, 0x74e4, 0x74e8, 0x74ea, 0x74eb, 0x74ef,
  0x74f4, 0x74fa, 0x74fb, 0x74fc, 0x74ff, 0x7506,
  /* 0x4d */
  0x7512, 0x7516, 0x7517, 0x7520, 0x7521, 0x7524, 0x7527, 0x7529,
  0x752a, 0x752f, 0x7536, 0x7539, 0x753d, 0x753e, 0x753f, 0x7540,
  0x7543, 0x7547, 0x7548, 0x754e, 0x7550, 0x7552, 0x7557, 0x755e,
  0x755f, 0x7561, 0x756f, 0x7571, 0x7579, 0x757a, 0x757b, 0x757c,
  0x757d, 0x757e, 0x7581, 0x7585, 0x7590, 0x7592, 0x7593, 0x7595,
  0x7599, 0x759c, 0x75a2, 0x75a4, 0x75b4, 0x75ba, 0x75bf, 0x75c0,
  0x75c1, 0x75c4, 0x75c6, 0x75cc, 0x75ce, 0x75cf, 0x75d7, 0x75dc,
  0x75df, 0x75e0, 0x75e1, 0x75e4, 0x75e7, 0x75ec, 0x75ee, 0x75ef,
  0x75f1, 0x75f9, 0x7600, 0x7602, 0x7603, 0x7604, 0x7607, 0x7608,
  0x760a, 0x760c, 0x760f, 0x7612, 0x7613, 0x7615, 0x7616, 0x7619,
  0x761b, 0x761c, 0x761d, 0x761e, 0x7623, 0x7625, 0x7626, 0x7629,
  0x762d, 0x7632, 0x7633, 0x7635, 0x7638, 0x7639,
  /* 0x4e */
  0x763a, 0x763c, 0x764a, 0x7640, 0x7641, 0x7643, 0x7644, 0x7645,
  0x7649, 0x764b, 0x7655, 0x7659, 0x765f, 0x7664, 0x7665, 0x766d,
  0x766e, 0x766f, 0x7671, 0x7674, 0x7681, 0x7685, 0x768c, 0x768d,
  0x7695, 0x769b, 0x769c, 0x769d, 0x769f, 0x76a0, 0x76a2, 0x76a3,
  0x76a4, 0x76a5, 0x76a6, 0x76a7, 0x76a8, 0x76aa, 0x76ad, 0x76bd,
  0x76c1, 0x76c5, 0x76c9, 0x76cb, 0x76cc, 0x76ce, 0x76d4, 0x76d9,
  0x76e0, 0x76e6, 0x76e8, 0x76ec, 0x76f0, 0x76f1, 0x76f6, 0x76f9,
  0x76fc, 0x7700, 0x7706, 0x770a, 0x770e, 0x7712, 0x7714, 0x7715,
  0x7717, 0x7719, 0x771a, 0x771c, 0x7722, 0x7728, 0x772d, 0x772e,
  0x772f, 0x7734, 0x7735, 0x7736, 0x7739, 0x773d, 0x773e, 0x7742,
  0x7745, 0x7746, 0x774a, 0x774d, 0x774e, 0x774f, 0x7752, 0x7756,
  0x7757, 0x775c, 0x775e, 0x775f, 0x7760, 0x7762,
  /* 0x4f */
  0x7764, 0x7767, 0x776a, 0x776c, 0x7770, 0x7772, 0x7773, 0x7774,
  0x777a, 0x777d, 0x7780, 0x7784, 0x778c, 0x778d, 0x7794, 0x7795,
  0x7796, 0x779a, 0x779f, 0x77a2, 0x77a7, 0x77aa, 0x77ae, 0x77af,
  0x77b1, 0x77b5, 0x77be, 0x77c3, 0x77c9, 0x77d1, 0x77d2, 0x77d5,
  0x77d9, 0x77de, 0x77df, 0x77e0, 0x77e4, 0x77e6, 0x77ea, 0x77ec,
  0x77f0, 0x77f1, 0x77f4, 0x77f8, 0x77fb, 0x7805, 0x7806, 0x7809,
  0x780d, 0x780e, 0x7811, 0x781d, 0x7821, 0x7822, 0x7823, 0x782d,
  0x782e, 0x7830, 0x7835, 0x7837, 0x7843, 0x7844, 0x7847, 0x7848,
  0x784c, 0x784e, 0x7852, 0x785c, 0x785e, 0x7860, 0x7861, 0x7863,
  0x7864, 0x7868, 0x786a, 0x786e, 0x787a, 0x787e, 0x788a, 0x788f,
  0x7894, 0x7898, 0x78a1, 0x789d, 0x789e, 0x789f, 0x78a4, 0x78a8,
  0x78ac, 0x78ad, 0x78b0, 0x78b1, 0x78b2, 0x78b3,
  /* 0x50 */
  0x78bb, 0x78bd, 0x78bf, 0x78c7, 0x78c8, 0x78c9, 0x78cc, 0x78ce,
  0x78d2, 0x78d3, 0x78d5, 0x78d6, 0x78e4, 0x78db, 0x78df, 0x78e0,
  0x78e1, 0x78e6, 0x78ea, 0x78f2, 0x78f3, 0x7900, 0x78f6, 0x78f7,
  0x78fa, 0x78fb, 0x78ff, 0x7906, 0x790c, 0x7910, 0x791a, 0x791c,
  0x791e, 0x791f, 0x7920, 0x7925, 0x7927, 0x7929, 0x792d, 0x7931,
  0x7934, 0x7935, 0x793b, 0x793d, 0x793f, 0x7944, 0x7945, 0x7946,
  0x794a, 0x794b, 0x794f, 0x7951, 0x7954, 0x7958, 0x795b, 0x795c,
  0x7967, 0x7969, 0x796b, 0x7972, 0x7979, 0x797b, 0x797c, 0x797e,
  0x798b, 0x798c, 0x7991, 0x7993, 0x7994, 0x7995, 0x7996, 0x7998,
  0x799b, 0x799c, 0x79a1, 0x79a8, 0x79a9, 0x79ab, 0x79af, 0x79b1,
  0x79b4, 0x79b8, 0x79bb, 0x79c2, 0x79c4, 0x79c7, 0x79c8, 0x79ca,
  0x79cf, 0x79d4, 0x79d6, 0x79da, 0x79dd, 0x79de,
  /* 0x51 */
  0x79e0, 0x79e2, 0x79e5, 0x79ea, 0x79eb, 0x79ed, 0x79f1, 0x79f8,
  0x79fc, 0x7a02, 0x7a03, 0x7a07, 0x7a09, 0x7a0a, 0x7a0c, 0x7a11,
  0x7a15, 0x7a1b, 0x7a1e, 0x7a21, 0x7a27, 0x7a2b, 0x7a2d, 0x7a2f,
  0x7a30, 0x7a34, 0x7a35, 0x7a38, 0x7a39, 0x7a3a, 0x7a44, 0x7a45,
  0x7a47, 0x7a48, 0x7a4c, 0x7a55, 0x7a56, 0x7a59, 0x7a5c, 0x7a5d,
  0x7a5f, 0x7a60, 0x7a65, 0x7a67, 0x7a6a, 0x7a6d, 0x7a75, 0x7a78,
  0x7a7e, 0x7a80, 0x7a82, 0x7a85, 0x7a86, 0x7a8a, 0x7a8b, 0x7a90,
  0x7a91, 0x7a94, 0x7a9e, 0x7aa0, 0x7aa3, 0x7aac, 0x7ab3, 0x7ab5,
  0x7ab9, 0x7abb, 0x7abc, 0x7ac6, 0x7ac9, 0x7acc, 0x7ace, 0x7ad1,
  0x7adb, 0x7ae8, 0x7ae9, 0x7aeb, 0x7aec, 0x7af1, 0x7af4, 0x7afb,
  0x7afd, 0x7afe, 0x7b07, 0x7b14, 0x7b1f, 0x7b23, 0x7b27, 0x7b29,
  0x7b2a, 0x7b2b, 0x7b2d, 0x7b2e, 0x7b2f, 0x7b30,
  /* 0x52 */
  0x7b31, 0x7b34, 0x7b3d, 0x7b3f, 0x7b40, 0x7b41, 0x7b47, 0x7b4e,
  0x7b55, 0x7b60, 0x7b64, 0x7b66, 0x7b69, 0x7b6a, 0x7b6d, 0x7b6f,
  0x7b72, 0x7b73, 0x7b77, 0x7b84, 0x7b89, 0x7b8e, 0x7b90, 0x7b91,
  0x7b96, 0x7b9b, 0x7b9e, 0x7ba0, 0x7ba5, 0x7bac, 0x7baf, 0x7bb0,
  0x7bb2, 0x7bb5, 0x7bb6, 0x7bba, 0x7bbb, 0x7bbc, 0x7bbd, 0x7bc2,
  0x7bc5, 0x7bc8, 0x7bca, 0x7bd4, 0x7bd6, 0x7bd7, 0x7bd9, 0x7bda,
  0x7bdb, 0x7be8, 0x7bea, 0x7bf2, 0x7bf4, 0x7bf5, 0x7bf8, 0x7bf9,
  0x7bfa, 0x7bfc, 0x7bfe, 0x7c01, 0x7c02, 0x7c03, 0x7c04, 0x7c06,
  0x7c09, 0x7c0b, 0x7c0c, 0x7c0e, 0x7c0f, 0x7c19, 0x7c1b, 0x7c20,
  0x7c25, 0x7c26, 0x7c28, 0x7c2c, 0x7c31, 0x7c33, 0x7c34, 0x7c36,
  0x7c39, 0x7c3a, 0x7c46, 0x7c4a, 0x7c55, 0x7c51, 0x7c52, 0x7c53,
  0x7c59, 0x7c5a, 0x7c5b, 0x7c5c, 0x7c5d, 0x7c5e,
  /* 0x53 */
  0x7c61, 0x7c63, 0x7c67, 0x7c69, 0x7c6d, 0x7c6e, 0x7c70, 0x7c72,
  0x7c79, 0x7c7c, 0x7c7d, 0x7c86, 0x7c87, 0x7c8f, 0x7c94, 0x7c9e,
  0x7ca0, 0x7ca6, 0x7cb0, 0x7cb6, 0x7cb7, 0x7cba, 0x7cbb, 0x7cbc,
  0x7cbf, 0x7cc4, 0x7cc7, 0x7cc8, 0x7cc9, 0x7ccd, 0x7ccf, 0x7cd3,
  0x7cd4, 0x7cd5, 0x7cd7, 0x7cd9, 0x7cda, 0x7cdd, 0x7ce6, 0x7ce9,
  0x7ceb, 0x7cf5, 0x7d03, 0x7d07, 0x7d08, 0x7d09, 0x7d0f, 0x7d11,
  0x7d12, 0x7d13, 0x7d16, 0x7d1d, 0x7d1e, 0x7d23, 0x7d26, 0x7d2a,
  0x7d2d, 0x7d31, 0x7d3c, 0x7d3d, 0x7d3e, 0x7d40, 0x7d41, 0x7d47,
  0x7d48, 0x7d4d, 0x7d51, 0x7d53, 0x7d57, 0x7d59, 0x7d5a, 0x7d5c,
  0x7d5d, 0x7d65, 0x7d67, 0x7d6a, 0x7d70, 0x7d78, 0x7d7a, 0x7d7b,
  0x7d7f, 0x7d81, 0x7d82, 0x7d83, 0x7d85, 0x7d86, 0x7d88, 0x7d8b,
  0x7d8c, 0x7d8d, 0x7d91, 0x7d96, 0x7d97, 0x7d9d,
  /* 0x54 */
  0x7d9e, 0x7da6, 0x7da7, 0x7daa, 0x7db3, 0x7db6, 0x7db7, 0x7db9,
  0x7dc2, 0x7dc3, 0x7dc4, 0x7dc5, 0x7dc6, 0x7dcc, 0x7dcd, 0x7dce,
  0x7dd7, 0x7dd9, 0x7e00, 0x7de2, 0x7de5, 0x7de6, 0x7dea, 0x7deb,
  0x7ded, 0x7df1, 0x7df5, 0x7df6, 0x7df9, 0x7dfa, 0x7e08, 0x7e10,
  0x7e11, 0x7e15, 0x7e17, 0x7e1c, 0x7e1d, 0x7e20, 0x7e27, 0x7e28,
  0x7e2c, 0x7e2d, 0x7e2f, 0x7e33, 0x7e36, 0x7e3f, 0x7e44, 0x7e45,
  0x7e47, 0x7e4e, 0x7e50, 0x7e52, 0x7e58, 0x7e5f, 0x7e61, 0x7e62,
  0x7e65, 0x7e6b, 0x7e6e, 0x7e6f, 0x7e73, 0x7e78, 0x7e7e, 0x7e81,
  0x7e86, 0x7e87, 0x7e8a, 0x7e8d, 0x7e91, 0x7e95, 0x7e98, 0x7e9a,
  0x7e9d, 0x7e9e, 0x7f3c, 0x7f3b, 0x7f3d, 0x7f3e, 0x7f3f, 0x7f43,
  0x7f44, 0x7f47, 0x7f4f, 0x7f52, 0x7f53, 0x7f5b, 0x7f5c, 0x7f5d,
  0x7f61, 0x7f63, 0x7f64, 0x7f65, 0x7f66, 0x7f6d,
  /* 0x55 */
  0x7f71, 0x7f7d, 0x7f7e, 0x7f7f, 0x7f80, 0x7f8b, 0x7f8d, 0x7f8f,
  0x7f90, 0x7f91, 0x7f96, 0x7f97, 0x7f9c, 0x7fa1, 0x7fa2, 0x7fa6,
  0x7faa, 0x7fad, 0x7fb4, 0x7fbc, 0x7fbf, 0x7fc0, 0x7fc3, 0x7fc8,
  0x7fce, 0x7fcf, 0x7fdb, 0x7fdf, 0x7fe3, 0x7fe5, 0x7fe8, 0x7fec,
  0x7fee, 0x7fef, 0x7ff2, 0x7ffa, 0x7ffd, 0x7ffe, 0x7fff, 0x8007,
  0x8008, 0x800a, 0x800d, 0x800e, 0x800f, 0x8011, 0x8013, 0x8014,
  0x8016, 0x801d, 0x801e, 0x801f, 0x8020, 0x8024, 0x8026, 0x802c,
  0x802e, 0x8030, 0x8034, 0x8035, 0x8037, 0x8039, 0x803a, 0x803c,
  0x803e, 0x8040, 0x8044, 0x8060, 0x8064, 0x8066, 0x806d, 0x8071,
  0x8075, 0x8081, 0x8088, 0x808e, 0x809c, 0x809e, 0x80a6, 0x80a7,
  0x80ab, 0x80b8, 0x80b9, 0x80c8, 0x80cd, 0x80cf, 0x80d2, 0x80d4,
  0x80d5, 0x80d7, 0x80d8, 0x80e0, 0x80ed, 0x80ee,
  /* 0x56 */
  0x80f0, 0x80f2, 0x80f3, 0x80f6, 0x80f9, 0x80fa, 0x80fe, 0x8103,
  0x810b, 0x8116, 0x8117, 0x8118, 0x811c, 0x811e, 0x8120, 0x8124,
  0x8127, 0x812c, 0x8130, 0x8135, 0x813a, 0x813c, 0x8145, 0x8147,
  0x814a, 0x814c, 0x8152, 0x8157, 0x8160, 0x8161, 0x8167, 0x8168,
  0x8169, 0x816d, 0x816f, 0x8177, 0x8181, 0x8190, 0x8184, 0x8185,
  0x8186, 0x818b, 0x818e, 0x8196, 0x8198, 0x819b, 0x819e, 0x81a2,
  0x81ae, 0x81b2, 0x81b4, 0x81bb, 0x81cb, 0x81c3, 0x81c5, 0x81ca,
  0x81ce, 0x81cf, 0x81d5, 0x81d7, 0x81db, 0x81dd, 0x81de, 0x81e1,
  0x81e4, 0x81eb, 0x81ec, 0x81f0, 0x81f1, 0x81f2, 0x81f5, 0x81f6,
  0x81f8, 0x81f9, 0x81fd, 0x81ff, 0x8200, 0x8203, 0x820f, 0x8213,
  0x8214, 0x8219, 0x821a, 0x821d, 0x8221, 0x8222, 0x8228, 0x8232,
  0x8234, 0x823a, 0x8243, 0x8244, 0x8245, 0x8246,
  /* 0x57 */
  0x824b, 0x824e, 0x824f, 0x8251, 0x8256, 0x825c, 0x8260, 0x8263,
  0x8267, 0x826d, 0x8274, 0x827b, 0x827d, 0x827f, 0x8280, 0x8281,
  0x8283, 0x8284, 0x8287, 0x8289, 0x828a, 0x828e, 0x8291, 0x8294,
  0x8296, 0x8298, 0x829a, 0x829b, 0x82a0, 0x82a1, 0x82a3, 0x82a4,
  0x82a7, 0x82a8, 0x82a9, 0x82aa, 0x82ae, 0x82b0, 0x82b2, 0x82b4,
  0x82b7, 0x82ba, 0x82bc, 0x82be, 0x82bf, 0x82c6, 0x82d0, 0x82d5,
  0x82da, 0x82e0, 0x82e2, 0x82e4, 0x82e8, 0x82ea, 0x82ed, 0x82ef,
  0x82f6, 0x82f7, 0x82fd, 0x82fe, 0x8300, 0x8301, 0x8307, 0x8308,
  0x830a, 0x830b, 0x8354, 0x831b, 0x831d, 0x831e, 0x831f, 0x8321,
  0x8322, 0x832c, 0x832d, 0x832e, 0x8330, 0x8333, 0x8337, 0x833a,
  0x833c, 0x833d, 0x8342, 0x8343, 0x8344, 0x8347, 0x834d, 0x834e,
  0x8351, 0x8355, 0x8356, 0x8357, 0x8370, 0x8378,
  /* 0x58 */
  0x837d, 0x837f, 0x8380, 0x8382, 0x8384, 0x8386, 0x838d, 0x8392,
  0x8394, 0x8395, 0x8398, 0x8399, 0x839b, 0x839c, 0x839d, 0x83a6,
  0x83a7, 0x83a9, 0x83ac, 0x83be, 0x83bf, 0x83c0, 0x83c7, 0x83c9,
  0x83cf, 0x83d0, 0x83d1, 0x83d4, 0x83dd, 0x8353, 0x83e8, 0x83ea,
  0x83f6, 0x83f8, 0x83f9, 0x83fc, 0x8401, 0x8406, 0x840a, 0x840f,
  0x8411, 0x8415, 0x8419, 0x83ad, 0x842f, 0x8439, 0x8445, 0x8447,
  0x8448, 0x844a, 0x844d, 0x844f, 0x8451, 0x8452, 0x8456, 0x8458,
  0x8459, 0x845a, 0x845c, 0x8460, 0x8464, 0x8465, 0x8467, 0x846a,
  0x8470, 0x8473, 0x8474, 0x8476, 0x8478, 0x847c, 0x847d, 0x8481,
  0x8485, 0x8492, 0x8493, 0x8495, 0x849e, 0x84a6, 0x84a8, 0x84a9,
  0x84aa, 0x84af, 0x84b1, 0x84b4, 0x84ba, 0x84bd, 0x84be, 0x84c0,
  0x84c2, 0x84c7, 0x84c8, 0x84cc, 0x84cf, 0x84d3,
  /* 0x59 */
  0x84dc, 0x84e7, 0x84ea, 0x84ef, 0x84f0, 0x84f1, 0x84f2, 0x84f7,
  0x8532, 0x84fa, 0x84fb, 0x84fd, 0x8502, 0x8503, 0x8507, 0x850c,
  0x850e, 0x8510, 0x851c, 0x851e, 0x8522, 0x8523, 0x8524, 0x8525,
  0x8527, 0x852a, 0x852b, 0x852f, 0x8533, 0x8534, 0x8536, 0x853f,
  0x8546, 0x854f, 0x8550, 0x8551, 0x8552, 0x8553, 0x8556, 0x8559,
  0x855c, 0x855d, 0x855e, 0x855f, 0x8560, 0x8561, 0x8562, 0x8564,
  0x856b, 0x856f, 0x8579, 0x857a, 0x857b, 0x857d, 0x857f, 0x8581,
  0x8585, 0x8586, 0x8589, 0x858b, 0x858c, 0x858f, 0x8593, 0x8598,
  0x859d, 0x859f, 0x85a0, 0x85a2, 0x85a5, 0x85a7, 0x85b4, 0x85b6,
  0x85b7, 0x85b8, 0x85bc, 0x85bd, 0x85be, 0x85bf, 0x85c2, 0x85c7,
  0x85ca, 0x85cb, 0x85ce, 0x85ad, 0x85d8, 0x85da, 0x85df, 0x85e0,
  0x85e6, 0x85e8, 0x85ed, 0x85f3, 0x85f6, 0x85fc,
  /* 0x5a */
  0x85ff, 0x8600, 0x8604, 0x8605, 0x860d, 0x860e, 0x8610, 0x8611,
  0x8612, 0x8618, 0x8619, 0x861b, 0x861e, 0x8621, 0x8627, 0x8629,
  0x8636, 0x8638, 0x863a, 0x863c, 0x863d, 0x8640, 0x8642, 0x8646,
  0x8652, 0x8653, 0x8656, 0x8657, 0x8658, 0x8659, 0x865d, 0x8660,
  0x8661, 0x8662, 0x8663, 0x8664, 0x8669, 0x866c, 0x866f, 0x8675,
  0x8676, 0x8677, 0x867a, 0x868d, 0x8691, 0x8696, 0x8698, 0x869a,
  0x869c, 0x86a1, 0x86a6, 0x86a7, 0x86a8, 0x86ad, 0x86b1, 0x86b3,
  0x86b4, 0x86b5, 0x86b7, 0x86b8, 0x86b9, 0x86bf, 0x86c0, 0x86c1,
  0x86c3, 0x86c5, 0x86d1, 0x86d2, 0x86d5, 0x86d7, 0x86da, 0x86dc,
  0x86e0, 0x86e3, 0x86e5, 0x86e7, 0x8688, 0x86fa, 0x86fc, 0x86fd,
  0x8704, 0x8705, 0x8707, 0x870b, 0x870e, 0x870f, 0x8710, 0x8713,
  0x8714, 0x8719, 0x871e, 0x871f, 0x8721, 0x8723,
  /* 0x5b */
  0x8728, 0x872e, 0x872f, 0x8731, 0x8732, 0x8739, 0x873a, 0x873c,
  0x873d, 0x873e, 0x8740, 0x8743, 0x8745, 0x874d, 0x8758, 0x875d,
  0x8761, 0x8764, 0x8765, 0x876f, 0x8771, 0x8772, 0x877b, 0x8783,
  0x8784, 0x8785, 0x8786, 0x8787, 0x8788, 0x8789, 0x878b, 0x878c,
  0x8790, 0x8793, 0x8795, 0x8797, 0x8798, 0x8799, 0x879e, 0x87a0,
  0x87a3, 0x87a7, 0x87ac, 0x87ad, 0x87ae, 0x87b1, 0x87b5, 0x87be,
  0x87bf, 0x87c1, 0x87c8, 0x87c9, 0x87ca, 0x87ce, 0x87d5, 0x87d6,
  0x87d9, 0x87da, 0x87dc, 0x87df, 0x87e2, 0x87e3, 0x87e4, 0x87ea,
  0x87eb, 0x87ed, 0x87f1, 0x87f3, 0x87f8, 0x87fa, 0x87ff, 0x8801,
  0x8803, 0x8806, 0x8809, 0x880a, 0x880b, 0x8810, 0x8819, 0x8812,
  0x8813, 0x8814, 0x8818, 0x881a, 0x881b, 0x881c, 0x881e, 0x881f,
  0x8828, 0x882d, 0x882e, 0x8830, 0x8832, 0x8835,
  /* 0x5c */
  0x883a, 0x883c, 0x8841, 0x8843, 0x8845, 0x8848, 0x8849, 0x884a,
  0x884b, 0x884e, 0x8851, 0x8855, 0x8856, 0x8858, 0x885a, 0x885c,
  0x885f, 0x8860, 0x8864, 0x8869, 0x8871, 0x8879, 0x887b, 0x8880,
  0x8898, 0x889a, 0x889b, 0x889c, 0x889f, 0x88a0, 0x88a8, 0x88aa,
  0x88ba, 0x88bd, 0x88be, 0x88c0, 0x88ca, 0x88cb, 0x88cc, 0x88cd,
  0x88ce, 0x88d1, 0x88d2, 0x88d3, 0x88db, 0x88de, 0x88e7, 0x88ef,
  0x88f0, 0x88f1, 0x88f5, 0x88f7, 0x8901, 0x8906, 0x890d, 0x890e,
  0x890f, 0x8915, 0x8916, 0x8918, 0x8919, 0x891a, 0x891c, 0x8920,
  0x8926, 0x8927, 0x8928, 0x8930, 0x8931, 0x8932, 0x8935, 0x8939,
  0x893a, 0x893e, 0x8940, 0x8942, 0x8945, 0x8946, 0x8949, 0x894f,
  0x8952, 0x8957, 0x895a, 0x895b, 0x895c, 0x8961, 0x8962, 0x8963,
  0x896b, 0x896e, 0x8970, 0x8973, 0x8975, 0x897a,
  /* 0x5d */
  0x897b, 0x897c, 0x897d, 0x8989, 0x898d, 0x8990, 0x8994, 0x8995,
  0x899b, 0x899c, 0x899f, 0x89a0, 0x89a5, 0x89b0, 0x89b4, 0x89b5,
  0x89b6, 0x89b7, 0x89bc, 0x89d4, 0x89d5, 0x89d6, 0x89d7, 0x89d8,
  0x89e5, 0x89e9, 0x89eb, 0x89ed, 0x89f1, 0x89f3, 0x89f6, 0x89f9,
  0x89fd, 0x89ff, 0x8a04, 0x8a05, 0x8a07, 0x8a0f, 0x8a11, 0x8a12,
  0x8a14, 0x8a15, 0x8a1e, 0x8a20, 0x8a22, 0x8a24, 0x8a26, 0x8a2b,
  0x8a2c, 0x8a2f, 0x8a35, 0x8a37, 0x8a3d, 0x8a3e, 0x8a40, 0x8a43,
  0x8a45, 0x8a47, 0x8a49, 0x8a4d, 0x8a4e, 0x8a53, 0x8a56, 0x8a57,
  0x8a58, 0x8a5c, 0x8a5d, 0x8a61, 0x8a65, 0x8a67, 0x8a75, 0x8a76,
  0x8a77, 0x8a79, 0x8a7a, 0x8a7b, 0x8a7e, 0x8a7f, 0x8a80, 0x8a83,
  0x8a86, 0x8a8b, 0x8a8f, 0x8a90, 0x8a92, 0x8a96, 0x8a97, 0x8a99,
  0x8a9f, 0x8aa7, 0x8aa9, 0x8aae, 0x8aaf, 0x8ab3,
  /* 0x5e */
  0x8ab6, 0x8ab7, 0x8abb, 0x8abe, 0x8ac3, 0x8ac6, 0x8ac8, 0x8ac9,
  0x8aca, 0x8ad1, 0x8ad3, 0x8ad4, 0x8ad5, 0x8ad7, 0x8add, 0x8adf,
  0x8aec, 0x8af0, 0x8af4, 0x8af5, 0x8af6, 0x8afc, 0x8aff, 0x8b05,
  0x8b06, 0x8b0b, 0x8b11, 0x8b1c, 0x8b1e, 0x8b1f, 0x8b0a, 0x8b2d,
  0x8b30, 0x8b37, 0x8b3c, 0x8b42, 0x8b43, 0x8b44, 0x8b45, 0x8b46,
  0x8b48, 0x8b52, 0x8b53, 0x8b54, 0x8b59, 0x8b4d, 0x8b5e, 0x8b63,
  0x8b6d, 0x8b76, 0x8b78, 0x8b79, 0x8b7c, 0x8b7e, 0x8b81, 0x8b84,
  0x8b85, 0x8b8b, 0x8b8d, 0x8b8f, 0x8b94, 0x8b95, 0x8b9c, 0x8b9e,
  0x8b9f, 0x8c38, 0x8c39, 0x8c3d, 0x8c3e, 0x8c45, 0x8c47, 0x8c49,
  0x8c4b, 0x8c4f, 0x8c51, 0x8c53, 0x8c54, 0x8c57, 0x8c58, 0x8c5b,
  0x8c5d, 0x8c59, 0x8c63, 0x8c64, 0x8c66, 0x8c68, 0x8c69, 0x8c6d,
  0x8c73, 0x8c75, 0x8c76, 0x8c7b, 0x8c7e, 0x8c86,
  /* 0x5f */
  0x8c87, 0x8c8b, 0x8c90, 0x8c92, 0x8c93, 0x8c99, 0x8c9b, 0x8c9c,
  0x8ca4, 0x8cb9, 0x8cba, 0x8cc5, 0x8cc6, 0x8cc9, 0x8ccb, 0x8ccf,
  0x8cd6, 0x8cd5, 0x8cd9, 0x8cdd, 0x8ce1, 0x8ce8, 0x8cec, 0x8cef,
  0x8cf0, 0x8cf2, 0x8cf5, 0x8cf7, 0x8cf8, 0x8cfe, 0x8cff, 0x8d01,
  0x8d03, 0x8d09, 0x8d12, 0x8d17, 0x8d1b, 0x8d65, 0x8d69, 0x8d6c,
  0x8d6e, 0x8d7f, 0x8d82, 0x8d84, 0x8d88, 0x8d8d, 0x8d90, 0x8d91,
  0x8d95, 0x8d9e, 0x8d9f, 0x8da0, 0x8da6, 0x8dab, 0x8dac, 0x8daf,
  0x8db2, 0x8db5, 0x8db7, 0x8db9, 0x8dbb, 0x8dc0, 0x8dc5, 0x8dc6,
  0x8dc7, 0x8dc8, 0x8dca, 0x8dce, 0x8dd1, 0x8dd4, 0x8dd5, 0x8dd7,
  0x8dd9, 0x8de4, 0x8de5, 0x8de7, 0x8dec, 0x8df0, 0x8dbc, 0x8df1,
  0x8df2, 0x8df4, 0x8dfd, 0x8e01, 0x8e04, 0x8e05, 0x8e06, 0x8e0b,
  0x8e11, 0x8e14, 0x8e16, 0x8e20, 0x8e21, 0x8e22,
  /* 0x60 */
  0x8e23, 0x8e26, 0x8e27, 0x8e31, 0x8e33, 0x8e36, 0x8e37, 0x8e38,
  0x8e39, 0x8e3d, 0x8e40, 0x8e41, 0x8e4b, 0x8e4d, 0x8e4e, 0x8e4f,
  0x8e54, 0x8e5b, 0x8e5c, 0x8e5d, 0x8e5e, 0x8e61, 0x8e62, 0x8e69,
  0x8e6c, 0x8e6d, 0x8e6f, 0x8e70, 0x8e71, 0x8e79, 0x8e7a, 0x8e7b,
  0x8e82, 0x8e83, 0x8e89, 0x8e90, 0x8e92, 0x8e95, 0x8e9a, 0x8e9b,
  0x8e9d, 0x8e9e, 0x8ea2, 0x8ea7, 0x8ea9, 0x8ead, 0x8eae, 0x8eb3,
  0x8eb5, 0x8eba, 0x8ebb, 0x8ec0, 0x8ec1, 0x8ec3, 0x8ec4, 0x8ec7,
  0x8ecf, 0x8ed1, 0x8ed4, 0x8edc, 0x8ee8, 0x8eee, 0x8ef0, 0x8ef1,
  0x8ef7, 0x8ef9, 0x8efa, 0x8eed, 0x8f00, 0x8f02, 0x8f07, 0x8f08,
  0x8f0f, 0x8f10, 0x8f16, 0x8f17, 0x8f18, 0x8f1e, 0x8f20, 0x8f21,
  0x8f23, 0x8f25, 0x8f27, 0x8f28, 0x8f2c, 0x8f2d, 0x8f2e, 0x8f34,
  0x8f35, 0x8f36, 0x8f37, 0x8f3a, 0x8f40, 0x8f41,
  /* 0x61 */
  0x8f43, 0x8f47, 0x8f4f, 0x8f51, 0x8f52, 0x8f53, 0x8f54, 0x8f55,
  0x8f58, 0x8f5d, 0x8f5e, 0x8f65, 0x8f9d, 0x8fa0, 0x8fa1, 0x8fa4,
  0x8fa5, 0x8fa6, 0x8fb5, 0x8fb6, 0x8fb8, 0x8fbe, 0x8fc0, 0x8fc1,
  0x8fc6, 0x8fca, 0x8fcb, 0x8fcd, 0x8fd0, 0x8fd2, 0x8fd3, 0x8fd5,
  0x8fe0, 0x8fe3, 0x8fe4, 0x8fe8, 0x8fee, 0x8ff1, 0x8ff5, 0x8ff6,
  0x8ffb, 0x8ffe, 0x9002, 0x9004, 0x9008, 0x900c, 0x9018, 0x901b,
  0x9028, 0x9029, 0x902f, 0x902a, 0x902c, 0x902d, 0x9033, 0x9034,
  0x9037, 0x903f, 0x9043, 0x9044, 0x904c, 0x905b, 0x905d, 0x9062,
  0x9066, 0x9067, 0x906c, 0x9070, 0x9074, 0x9079, 0x9085, 0x9088,
  0x908b, 0x908c, 0x908e, 0x9090, 0x9095, 0x9097, 0x9098, 0x9099,
  0x909b, 0x90a0, 0x90a1, 0x90a2, 0x90a5, 0x90b0, 0x90b2, 0x90b3,
  0x90b4, 0x90b6, 0x90bd, 0x90cc, 0x90be, 0x90c3,
  /* 0x62 */
  0x90c4, 0x90c5, 0x90c7, 0x90c8, 0x90d5, 0x90d7, 0x90d8, 0x90d9,
  0x90dc, 0x90dd, 0x90df, 0x90e5, 0x90d2, 0x90f6, 0x90eb, 0x90ef,
  0x90f0, 0x90f4, 0x90fe, 0x90ff, 0x9100, 0x9104, 0x9105, 0x9106,
  0x9108, 0x910d, 0x9110, 0x9114, 0x9116, 0x9117, 0x9118, 0x911a,
  0x911c, 0x911e, 0x9120, 0x9125, 0x9122, 0x9123, 0x9127, 0x9129,
  0x912e, 0x912f, 0x9131, 0x9134, 0x9136, 0x9137, 0x9139, 0x913a,
  0x913c, 0x913d, 0x9143, 0x9147, 0x9148, 0x914f, 0x9153, 0x9157,
  0x9159, 0x915a, 0x915b, 0x9161, 0x9164, 0x9167, 0x916d, 0x9174,
  0x9179, 0x917a, 0x917b, 0x9181, 0x9183, 0x9185, 0x9186, 0x918a,
  0x918e, 0x9191, 0x9193, 0x9194, 0x9195, 0x9198, 0x919e, 0x91a1,
  0x91a6, 0x91a8, 0x91ac, 0x91ad, 0x91ae, 0x91b0, 0x91b1, 0x91b2,
  0x91b3, 0x91b6, 0x91bb, 0x91bc, 0x91bd, 0x91bf,
  /* 0x63 */
  0x91c2, 0x91c3, 0x91c5, 0x91d3, 0x91d4, 0x91d7, 0x91d9, 0x91da,
  0x91de, 0x91e4, 0x91e5, 0x91e9, 0x91ea, 0x91ec, 0x91ed, 0x91ee,
  0x91ef, 0x91f0, 0x91f1, 0x91f7, 0x91f9, 0x91fb, 0x91fd, 0x9200,
  0x9201, 0x9204, 0x9205, 0x9206, 0x9207, 0x9209, 0x920a, 0x920c,
  0x9210, 0x9212, 0x9213, 0x9216, 0x9218, 0x921c, 0x921d, 0x9223,
  0x9224, 0x9225, 0x9226, 0x9228, 0x922e, 0x922f, 0x9230, 0x9233,
  0x9235, 0x9236, 0x9238, 0x9239, 0x923a, 0x923c, 0x923e, 0x9240,
  0x9242, 0x9243, 0x9246, 0x9247, 0x924a, 0x924d, 0x924e, 0x924f,
  0x9251, 0x9258, 0x9259, 0x925c, 0x925d, 0x9260, 0x9261, 0x9265,
  0x9267, 0x9268, 0x9269, 0x926e, 0x926f, 0x9270, 0x9275, 0x9276,
  0x9277, 0x9278, 0x9279, 0x927b, 0x927c, 0x927d, 0x927f, 0x9288,
  0x9289, 0x928a, 0x928d, 0x928e, 0x9292, 0x9297,
  /* 0x64 */
  0x9299, 0x929f, 0x92a0, 0x92a4, 0x92a5, 0x92a7, 0x92a8, 0x92ab,
  0x92af, 0x92b2, 0x92b6, 0x92b8, 0x92ba, 0x92bb, 0x92bc, 0x92bd,
  0x92bf, 0x92c0, 0x92c1, 0x92c2, 0x92c3, 0x92c5, 0x92c6, 0x92c7,
  0x92c8, 0x92cb, 0x92cc, 0x92cd, 0x92ce, 0x92d0, 0x92d3, 0x92d5,
  0x92d7, 0x92d8, 0x92d9, 0x92dc, 0x92dd, 0x92df, 0x92e0, 0x92e1,
  0x92e3, 0x92e5, 0x92e7, 0x92e8, 0x92ec, 0x92ee, 0x92f0, 0x92f9,
  0x92fb, 0x92ff, 0x9300, 0x9302, 0x9308, 0x930d, 0x9311, 0x9314,
  0x9315, 0x931c, 0x931d, 0x931e, 0x931f, 0x9321, 0x9324, 0x9325,
  0x9327, 0x9329, 0x932a, 0x9333, 0x9334, 0x9336, 0x9337, 0x9347,
  0x9348, 0x9349, 0x9350, 0x9351, 0x9352, 0x9355, 0x9357, 0x9358,
  0x935a, 0x935e, 0x9364, 0x9365, 0x9367, 0x9369, 0x936a, 0x936d,
  0x936f, 0x9370, 0x9371, 0x9373, 0x9374, 0x9376,
  /* 0x65 */
  0x937a, 0x937d, 0x937f, 0x9380, 0x9381, 0x9382, 0x9388, 0x938a,
  0x938b, 0x938d, 0x938f, 0x9392, 0x9395, 0x9398, 0x939b, 0x939e,
  0x93a1, 0x93a3, 0x93a4, 0x93a6, 0x93a8, 0x93ab, 0x93b4, 0x93b5,
  0x93b6, 0x93ba, 0x93a9, 0x93c1, 0x93c4, 0x93c5, 0x93c6, 0x93c7,
  0x93c9, 0x93ca, 0x93cb, 0x93cc, 0x93cd, 0x93d3, 0x93d9, 0x93dc,
  0x93de, 0x93df, 0x93e2, 0x93e6, 0x93e7, 0x93f9, 0x93f7, 0x93f8,
  0x93fa, 0x93fb, 0x93fd, 0x9401, 0x9402, 0x9404, 0x9408, 0x9409,
  0x940d, 0x940e, 0x940f, 0x9415, 0x9416, 0x9417, 0x941f, 0x942e,
  0x942f, 0x9431, 0x9432, 0x9433, 0x9434, 0x943b, 0x943f, 0x943d,
  0x9443, 0x9445, 0x9448, 0x944a, 0x944c, 0x9455, 0x9459, 0x945c,
  0x945f, 0x9461, 0x9463, 0x9468, 0x946b, 0x946d, 0x946e, 0x946f,
  0x9471, 0x9472, 0x9484, 0x9483, 0x9578, 0x9579,
  /* 0x66 */
  0x957e, 0x9584, 0x9588, 0x958c, 0x958d, 0x958e, 0x959d, 0x959e,
  0x959f, 0x95a1, 0x95a6, 0x95a9, 0x95ab, 0x95ac, 0x95b4, 0x95b6,
  0x95ba, 0x95bd, 0x95bf, 0x95c6, 0x95c8, 0x95c9, 0x95cb, 0x95d0,
  0x95d1, 0x95d2, 0x95d3, 0x95d9, 0x95da, 0x95dd, 0x95de, 0x95df,
  0x95e0, 0x95e4, 0x95e6, 0x961d, 0x961e, 0x9622, 0x9624, 0x9625,
  0x9626, 0x962c, 0x9631, 0x9633, 0x9637, 0x9638, 0x9639, 0x963a,
  0x963c, 0x963d, 0x9641, 0x9652, 0x9654, 0x9656, 0x9657, 0x9658,
  0x9661, 0x966e, 0x9674, 0x967b, 0x967c, 0x967e, 0x967f, 0x9681,
  0x9682, 0x9683, 0x9684, 0x9689, 0x9691, 0x9696, 0x969a, 0x969d,
  0x969f, 0x96a4, 0x96a5, 0x96a6, 0x96a9, 0x96ae, 0x96af, 0x96b3,
  0x96ba, 0x96ca, 0x96d2, 0x5db2, 0x96d8, 0x96da, 0x96dd, 0x96de,
  0x96df, 0x96e9, 0x96ef, 0x96f1, 0x96fa, 0x9702,
  /* 0x67 */
  0x9703, 0x9705, 0x9709, 0x971a, 0x971b, 0x971d, 0x9721, 0x9722,
  0x9723, 0x9728, 0x9731, 0x9733, 0x9741, 0x9743, 0x974a, 0x974e,
  0x974f, 0x9755, 0x9757, 0x9758, 0x975a, 0x975b, 0x9763, 0x9767,
  0x976a, 0x976e, 0x9773, 0x9776, 0x9777, 0x9778, 0x977b, 0x977d,
  0x977f, 0x9780, 0x9789, 0x9795, 0x9796, 0x9797, 0x9799, 0x979a,
  0x979e, 0x979f, 0x97a2, 0x97ac, 0x97ae, 0x97b1, 0x97b2, 0x97b5,
  0x97b6, 0x97b8, 0x97b9, 0x97ba, 0x97bc, 0x97be, 0x97bf, 0x97c1,
  0x97c4, 0x97c5, 0x97c7, 0x97c9, 0x97ca, 0x97cc, 0x97cd, 0x97ce,
  0x97d0, 0x97d1, 0x97d4, 0x97d7, 0x97d8, 0x97d9, 0x97dd, 0x97de,
  0x97e0, 0x97db, 0x97e1, 0x97e4, 0x97ef, 0x97f1, 0x97f4, 0x97f7,
  0x97f8, 0x97fa, 0x9807, 0x980a, 0x9819, 0x980d, 0x980e, 0x9814,
  0x9816, 0x981c, 0x981e, 0x9820, 0x9823, 0x9826,
  /* 0x68 */
  0x982b, 0x982e, 0x982f, 0x9830, 0x9832, 0x9833, 0x9835, 0x9825,
  0x983e, 0x9844, 0x9847, 0x984a, 0x9851, 0x9852, 0x9853, 0x9856,
  0x9857, 0x9859, 0x985a, 0x9862, 0x9863, 0x9865, 0x9866, 0x986a,
  0x986c, 0x98ab, 0x98ad, 0x98ae, 0x98b0, 0x98b4, 0x98b7, 0x98b8,
  0x98ba, 0x98bb, 0x98bf, 0x98c2, 0x98c5, 0x98c8, 0x98cc, 0x98e1,
  0x98e3, 0x98e5, 0x98e6, 0x98e7, 0x98ea, 0x98f3, 0x98f6, 0x9902,
  0x9907, 0x9908, 0x9911, 0x9915, 0x9916, 0x9917, 0x991a, 0x991b,
  0x991c, 0x991f, 0x9922, 0x9926, 0x9927, 0x992b, 0x9931, 0x9932,
  0x9933, 0x9934, 0x9935, 0x9939, 0x993a, 0x993b, 0x993c, 0x9940,
  0x9941, 0x9946, 0x9947, 0x9948, 0x994d, 0x994e, 0x9954, 0x9958,
  0x9959, 0x995b, 0x995c, 0x995e, 0x995f, 0x9960, 0x999b, 0x999d,
  0x999f, 0x99a6, 0x99b0, 0x99b1, 0x99b2, 0x99b5,
  /* 0x69 */
  0x99b9, 0x99ba, 0x99bd, 0x99bf, 0x99c3, 0x99c9, 0x99d3, 0x99d4,
  0x99d9, 0x99da, 0x99dc, 0x99de, 0x99e7, 0x99ea, 0x99eb, 0x99ec,
  0x99f0, 0x99f4, 0x99f5, 0x99f9, 0x99fd, 0x99fe, 0x9a02, 0x9a03,
  0x9a04, 0x9a0b, 0x9a0c, 0x9a10, 0x9a11, 0x9a16, 0x9a1e, 0x9a20,
  0x9a22, 0x9a23, 0x9a24, 0x9a27, 0x9a2d, 0x9a2e, 0x9a33, 0x9a35,
  0x9a36, 0x9a38, 0x9a47, 0x9a41, 0x9a44, 0x9a4a, 0x9a4b, 0x9a4c,
  0x9a4e, 0x9a51, 0x9a54, 0x9a56, 0x9a5d, 0x9aaa, 0x9aac, 0x9aae,
  0x9aaf, 0x9ab2, 0x9ab4, 0x9ab5, 0x9ab6, 0x9ab9, 0x9abb, 0x9abe,
  0x9abf, 0x9ac1, 0x9ac3, 0x9ac6, 0x9ac8, 0x9ace, 0x9ad0, 0x9ad2,
  0x9ad5, 0x9ad6, 0x9ad7, 0x9adb, 0x9adc, 0x9ae0, 0x9ae4, 0x9ae5,
  0x9ae7, 0x9ae9, 0x9aec, 0x9af2, 0x9af3, 0x9af5, 0x9af9, 0x9afa,
  0x9afd, 0x9aff, 0x9b00, 0x9b01, 0x9b02, 0x9b03,
  /* 0x6a */
  0x9b04, 0x9b05, 0x9b08, 0x9b09, 0x9b0b, 0x9b0c, 0x9b0d, 0x9b0e,
  0x9b10, 0x9b12, 0x9b16, 0x9b19, 0x9b1b, 0x9b1c, 0x9b20, 0x9b26,
  0x9b2b, 0x9b2d, 0x9b33, 0x9b34, 0x9b35, 0x9b37, 0x9b39, 0x9b3a,
  0x9b3d, 0x9b48, 0x9b4b, 0x9b4c, 0x9b55, 0x9b56, 0x9b57, 0x9b5b,
  0x9b5e, 0x9b61, 0x9b63, 0x9b65, 0x9b66, 0x9b68, 0x9b6a, 0x9b6b,
  0x9b6c, 0x9b6d, 0x9b6e, 0x9b73, 0x9b75, 0x9b77, 0x9b78, 0x9b79,
  0x9b7f, 0x9b80, 0x9b84, 0x9b85, 0x9b86, 0x9b87, 0x9b89, 0x9b8a,
  0x9b8b, 0x9b8d, 0x9b8f, 0x9b90, 0x9b94, 0x9b9a, 0x9b9d, 0x9b9e,
  0x9ba6, 0x9ba7, 0x9ba9, 0x9bac, 0x9bb0, 0x9bb1, 0x9bb2, 0x9bb7,
  0x9bb8, 0x9bbb, 0x9bbc, 0x9bbe, 0x9bbf, 0x9bc1, 0x9bc7, 0x9bc8,
  0x9bce, 0x9bd0, 0x9bd7, 0x9bd8, 0x9bdd, 0x9bdf, 0x9be5, 0x9be7,
  0x9bea, 0x9beb, 0x9bef, 0x9bf3, 0x9bf7, 0x9bf8,
  /* 0x6b */
  0x9bf9, 0x9bfa, 0x9bfd, 0x9bff, 0x9c00, 0x9c02, 0x9c0b, 0x9c0f,
  0x9c11, 0x9c16, 0x9c18, 0x9c19, 0x9c1a, 0x9c1c, 0x9c1e, 0x9c22,
  0x9c23, 0x9c26, 0x9c27, 0x9c28, 0x9c29, 0x9c2a, 0x9c31, 0x9c35,
  0x9c36, 0x9c37, 0x9c3d, 0x9c41, 0x9c43, 0x9c44, 0x9c45, 0x9c49,
  0x9c4a, 0x9c4e, 0x9c4f, 0x9c50, 0x9c53, 0x9c54, 0x9c56, 0x9c58,
  0x9c5b, 0x9c5d, 0x9c5e, 0x9c5f, 0x9c63, 0x9c69, 0x9c6a, 0x9c5c,
  0x9c6b, 0x9c68, 0x9c6e, 0x9c70, 0x9c72, 0x9c75, 0x9c77, 0x9c7b,
  0x9ce6, 0x9cf2, 0x9cf7, 0x9cf9, 0x9d0b, 0x9d02, 0x9d11, 0x9d17,
  0x9d18, 0x9d1c, 0x9d1d, 0x9d1e, 0x9d2f, 0x9d30, 0x9d32, 0x9d33,
  0x9d34, 0x9d3a, 0x9d3c, 0x9d45, 0x9d3d, 0x9d42, 0x9d43, 0x9d47,
  0x9d4a, 0x9d53, 0x9d54, 0x9d5f, 0x9d63, 0x9d62, 0x9d65, 0x9d69,
  0x9d6a, 0x9d6b, 0x9d70, 0x9d76, 0x9d77, 0x9d7b,
  /* 0x6c */
  0x9d7c, 0x9d7e, 0x9d83, 0x9d84, 0x9d86, 0x9d8a, 0x9d8d, 0x9d8e,
  0x9d92, 0x9d93, 0x9d95, 0x9d96, 0x9d97, 0x9d98, 0x9da1, 0x9daa,
  0x9dac, 0x9dae, 0x9db1, 0x9db5, 0x9db9, 0x9dbc, 0x9dbf, 0x9dc3,
  0x9dc7, 0x9dc9, 0x9dca, 0x9dd4, 0x9dd5, 0x9dd6, 0x9dd7, 0x9dda,
  0x9dde, 0x9ddf, 0x9de0, 0x9de5, 0x9de7, 0x9de9, 0x9deb, 0x9dee,
  0x9df0, 0x9df3, 0x9df4, 0x9dfe, 0x9e0a, 0x9e02, 0x9e07, 0x9e0e,
  0x9e10, 0x9e11, 0x9e12, 0x9e15, 0x9e16, 0x9e19, 0x9e1c, 0x9e1d,
  0x9e7a, 0x9e7b, 0x9e7c, 0x9e80, 0x9e82, 0x9e83, 0x9e84, 0x9e85,
  0x9e87, 0x9e8e, 0x9e8f, 0x9e96, 0x9e98, 0x9e9b, 0x9e9e, 0x9ea4,
  0x9ea8, 0x9eac, 0x9eae, 0x9eaf, 0x9eb0, 0x9eb3, 0x9eb4, 0x9eb5,
  0x9ec6, 0x9ec8, 0x9ecb, 0x9ed5, 0x9edf, 0x9ee4, 0x9ee7, 0x9eec,
  0x9eed, 0x9eee, 0x9ef0, 0x9ef1, 0x9ef2, 0x9ef5,
  /* 0x6d */
  0x9ef8, 0x9eff, 0x9f02, 0x9f03, 0x9f09, 0x9f0f, 0x9f10, 0x9f11,
  0x9f12, 0x9f14, 0x9f16, 0x9f17, 0x9f19, 0x9f1a, 0x9f1b, 0x9f1f,
  0x9f22, 0x9f26, 0x9f2a, 0x9f2b, 0x9f2f, 0x9f31, 0x9f32, 0x9f34,
  0x9f37, 0x9f39, 0x9f3a, 0x9f3c, 0x9f3d, 0x9f3f, 0x9f41, 0x9f43,
  0x9f44, 0x9f45, 0x9f46, 0x9f47, 0x9f53, 0x9f55, 0x9f56, 0x9f57,
  0x9f58, 0x9f5a, 0x9f5d, 0x9f5e, 0x9f68, 0x9f69, 0x9f6d, 0x9f6e,
  0x9f6f, 0x9f70, 0x9f71, 0x9f73, 0x9f75, 0x9f7a, 0x9f7d, 0x9f8f,
  0x9f90, 0x9f91, 0x9f92, 0x9f94, 0x9f96, 0x9f97, 0x9f9e, 0x9fa1,
  0x9fa2, 0x9fa3, 0x9fa5,
};

static int
jisx0212_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 == 0x22) || (c1 >= 0x26 && c1 <= 0x27) || (c1 >= 0x29 && c1 <= 0x2b) || (c1 >= 0x30 && c1 <= 0x6d)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        if (i < 470) {
          if (i < 175)
            wc = jisx0212_2uni_page22[i-94];
        } else if (i < 752) {
          if (i < 658)
            wc = jisx0212_2uni_page26[i-470];
        } else if (i < 1410) {
          if (i < 1027)
            wc = jisx0212_2uni_page29[i-752];
        } else {
          if (i < 7211)
            wc = jisx0212_2uni_page30[i-1410];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

static const unsigned short jisx0212_2charset[6067] = {
  0x2242, 0x2270, 0x2243, 0x226d, 0x226c, 0x226e, 0x2234, 0x2231,
  0x226b, 0x2244, 0x2a22, 0x2a21, 0x2a24, 0x2a2a, 0x2a23, 0x2a29,
  0x2921, 0x2a2e, 0x2a32, 0x2a31, 0x2a34, 0x2a33, 0x2a40, 0x2a3f,
  0x2a42, 0x2a41, 0x2a50, 0x2a52, 0x2a51, 0x2a54, 0x2a58, 0x2a53,
  0x292c, 0x2a63, 0x2a62, 0x2a65, 0x2a64, 0x2a72, 0x2930, 0x294e,
  0x2b22, 0x2b21, 0x2b24, 0x2b2a, 0x2b23, 0x2b29, 0x2941, 0x2b2e,
  0x2b32, 0x2b31, 0x2b34, 0x2b33, 0x2b40, 0x2b3f, 0x2b42, 0x2b41,
  0x2943, 0x2b50, 0x2b52, 0x2b51, 0x2b54, 0x2b58, 0x2b53, 0x294c,
  0x2b63, 0x2b62, 0x2b65, 0x2b64, 0x2b72, 0x2950, 0x2b73, 0x2a27,
  0x2b27, 0x2a25, 0x2b25, 0x2a28, 0x2b28, 0x2a2b, 0x2b2b, 0x2a2c,
  0x2b2c, 0x2a2f, 0x2b2f, 0x2a2d, 0x2b2d, 0x2a30, 0x2b30, 0x2922,
  0x2942, 0x2a37, 0x2b37, 0x2a36, 0x2b36, 0x2a38, 0x2b38, 0x2a35,
  0x2b35, 0x2a3a, 0x2b3a, 0x2a3b, 0x2b3b, 0x2a3d, 0x2b3d, 0x2a3c,
  0x2a3e, 0x2b3e, 0x2924, 0x2944, 0x2a47, 0x2b47, 0x2a45, 0x2b45,
  0x2a46, 0x2b46, 0x2a44, 0x2945, 0x2926, 0x2946, 0x2a48, 0x2b48,
  0x2a49, 0x2b49, 0x2947, 0x2a4a, 0x2b4a, 0x2a4c, 0x2b4c, 0x2a4b,
  0x2b4b, 0x2929, 0x2949, 0x2928, 0x2948, 0x2a4d, 0x2b4d, 0x2a4f,
  0x2b4f, 0x2a4e, 0x2b4e, 0x294a, 0x292b, 0x294b, 0x2a57, 0x2b57,
  0x2a56, 0x2b56, 0x292d, 0x294d, 0x2a59, 0x2b59, 0x2a5b, 0x2b5b,
  0x2a5a, 0x2b5a, 0x2a5c, 0x2b5c, 0x2a5d, 0x2b5d, 0x2a5f, 0x2b5f,
  0x2a5e, 0x2b5e, 0x2a61, 0x2b61, 0x2a60, 0x2b60, 0x292f, 0x294f,
  0x2a6c, 0x2b6c, 0x2a69, 0x2b69, 0x2a66, 0x2b66, 0x2a6b, 0x2b6b,
  0x2a68, 0x2b68, 0x2a6a, 0x2b6a, 0x2a71, 0x2b71, 0x2a74, 0x2b74,
  0x2a73, 0x2a75, 0x2b75, 0x2a77, 0x2b77, 0x2a76, 0x2b76, 0x2a26,
  0x2b26, 0x2a43, 0x2b43, 0x2a55, 0x2b55, 0x2a67, 0x2b67, 0x2a70,
  0x2b70, 0x2a6d, 0x2b6d, 0x2a6f, 0x2b6f, 0x2a6e, 0x2b6e, 0x2b39,
  0x2230, 0x222f, 0x2232, 0x2236, 0x2235, 0x2233, 0x2238, 0x2239,
  0x2661, 0x2662, 0x2663, 0x2664, 0x2667, 0x2669, 0x266c, 0x2676,
  0x2665, 0x266a, 0x2671, 0x2672, 0x2673, 0x2674, 0x267b, 0x2678,
  0x2675, 0x267a, 0x2677, 0x2679, 0x267c, 0x2742, 0x2743, 0x2744,
  0x2745, 0x2746, 0x2747, 0x2748, 0x2749, 0x274a, 0x274b, 0x274c,
  0x274d, 0x274e, 0x2772, 0x2773, 0x2774, 0x2775, 0x2776, 0x2777,
  0x2778, 0x2779, 0x277a, 0x277b, 0x277c, 0x277d, 0x277e, 0x2271,
  0x226f, 0x3021, 0x3022, 0x3023, 0x3024, 0x3025, 0x3026, 0x3027,
  0x3028, 0x3029, 0x302a, 0x302b, 0x302c, 0x302d, 0x302e, 0x302f,
  0x3030, 0x3031, 0x3032, 0x3033, 0x3034, 0x3035, 0x3036, 0x3037,
  0x3038, 0x3039, 0x303a, 0x303b, 0x303c, 0x303d, 0x303e, 0x303f,
  0x3040, 0x3041, 0x3042, 0x3043, 0x3044, 0x3045, 0x3046, 0x3047,
  0x3048, 0x3049, 0x304a, 0x304b, 0x304c, 0x304d, 0x304e, 0x304f,
  0x3050, 0x3051, 0x3052, 0x3053, 0x3054, 0x3055, 0x3056, 0x3057,
  0x3058, 0x3059, 0x305a, 0x305b, 0x305c, 0x305d, 0x305e, 0x3060,
  0x3061, 0x3062, 0x3063, 0x3064, 0x3065, 0x3066, 0x3067, 0x3068,
  0x3069, 0x306a, 0x306b, 0x306c, 0x306d, 0x306e, 0x306f, 0x3070,
  0x305f, 0x3071, 0x3072, 0x3073, 0x3074, 0x3075, 0x3076, 0x3077,
  0x3078, 0x3079, 0x307a, 0x307b, 0x307c, 0x307d, 0x307e, 0x3121,
  0x3122, 0x3123, 0x3124, 0x3125, 0x3126, 0x3127, 0x3128, 0x3129,
  0x312a, 0x312b, 0x312c, 0x312d, 0x312e, 0x312f, 0x3130, 0x3131,
  0x3132, 0x3133, 0x3134, 0x3135, 0x3136, 0x3137, 0x3138, 0x3139,
  0x313a, 0x313b, 0x313c, 0x313d, 0x313e, 0x313f, 0x3140, 0x3141,
  0x3142, 0x3143, 0x3144, 0x3145, 0x3146, 0x3147, 0x3148, 0x3149,
  0x314a, 0x314b, 0x314c, 0x314d, 0x314e, 0x314f, 0x3150, 0x3151,
  0x3152, 0x3153, 0x3154, 0x3155, 0x3156, 0x3157, 0x3158, 0x3159,
  0x315a, 0x315b, 0x315c, 0x315d, 0x315e, 0x3176, 0x315f, 0x3160,
  0x3161, 0x3162, 0x3163, 0x3164, 0x3165, 0x3166, 0x3167, 0x3168,
  0x3169, 0x316a, 0x316b, 0x316c, 0x316d, 0x316e, 0x316f, 0x3170,
  0x3171, 0x3172, 0x3173, 0x3174, 0x3175, 0x3177, 0x3178, 0x3179,
  0x317a, 0x317b, 0x317c, 0x317d, 0x317e, 0x3221, 0x3222, 0x3223,
  0x3224, 0x3225, 0x3226, 0x3227, 0x3228, 0x3229, 0x322a, 0x322b,
  0x322c, 0x322d, 0x322e, 0x322f, 0x3230, 0x3231, 0x3232, 0x3233,
  0x3234, 0x3235, 0x3236, 0x3237, 0x3238, 0x3239, 0x323a, 0x323b,
  0x323c, 0x323d, 0x323e, 0x323f, 0x3240, 0x3241, 0x3242, 0x3243,
  0x3244, 0x3245, 0x3251, 0x3246, 0x3247, 0x3248, 0x3249, 0x324a,
  0x324b, 0x324c, 0x324d, 0x324e, 0x324f, 0x3250, 0x3252, 0x3253,
  0x3254, 0x3255, 0x3256, 0x3257, 0x3258, 0x3259, 0x325a, 0x325b,
  0x325c, 0x325d, 0x325e, 0x325f, 0x3260, 0x3261, 0x3262, 0x3263,
  0x3264, 0x3265, 0x3266, 0x3267, 0x3268, 0x3269, 0x326a, 0x326b,
  0x326c, 0x326d, 0x326e, 0x326f, 0x3270, 0x3271, 0x3272, 0x3273,
  0x3274, 0x3275, 0x3276, 0x3277, 0x3278, 0x3279, 0x327a, 0x327b,
  0x327c, 0x327d, 0x327e, 0x3321, 0x3322, 0x3323, 0x3324, 0x3325,
  0x3326, 0x3327, 0x3328, 0x3329, 0x332a, 0x332b, 0x332c, 0x332d,
  0x332e, 0x332f, 0x3330, 0x3331, 0x3332, 0x3333, 0x3334, 0x3335,
  0x3336, 0x3337, 0x3338, 0x3339, 0x333a, 0x333b, 0x333c, 0x333d,
  0x333e, 0x333f, 0x3340, 0x3341, 0x3342, 0x3343, 0x3344, 0x3345,
  0x3346, 0x3347, 0x3348, 0x3349, 0x334a, 0x334b, 0x334c, 0x334d,
  0x334e, 0x334f, 0x3350, 0x3351, 0x3352, 0x3353, 0x3354, 0x3355,
  0x3356, 0x3357, 0x3358, 0x3359, 0x335a, 0x335b, 0x335c, 0x335d,
  0x335e, 0x335f, 0x3360, 0x3361, 0x3362, 0x3363, 0x3364, 0x3365,
  0x3366, 0x3367, 0x3368, 0x3369, 0x336a, 0x336b, 0x336c, 0x336d,
  0x336e, 0x336f, 0x3370, 0x3371, 0x3372, 0x3373, 0x3374, 0x3375,
  0x3376, 0x3377, 0x3378, 0x3379, 0x337a, 0x337b, 0x337c, 0x337d,
  0x337e, 0x3421, 0x3422, 0x3423, 0x3424, 0x3425, 0x3426, 0x3427,
  0x3428, 0x3429, 0x342a, 0x342b, 0x342c, 0x342d, 0x342e, 0x342f,
  0x3430, 0x3431, 0x3432, 0x3433, 0x3434, 0x3435, 0x3436, 0x3438,
  0x3437, 0x3439, 0x343a, 0x343b, 0x343c, 0x343d, 0x343e, 0x343f,
  0x3440, 0x3441, 0x3442, 0x3443, 0x3444, 0x3445, 0x3446, 0x3447,
  0x3448, 0x3449, 0x344a, 0x344b, 0x344c, 0x344d, 0x344e, 0x344f,
  0x3450, 0x3451, 0x3452, 0x3453, 0x3454, 0x3455, 0x3456, 0x3457,
  0x3458, 0x3459, 0x345a, 0x345b, 0x345c, 0x345d, 0x345e, 0x345f,
  0x3460, 0x3461, 0x3462, 0x3463, 0x3464, 0x3465, 0x3466, 0x3467,
  0x3468, 0x3469, 0x346a, 0x346b, 0x346c, 0x346d, 0x346e, 0x346f,
  0x3470, 0x3471, 0x3472, 0x3473, 0x3474, 0x3475, 0x3476, 0x3477,
  0x3478, 0x3479, 0x347a, 0x347b, 0x347c, 0x347d, 0x347e, 0x3521,
  0x3522, 0x3523, 0x3524, 0x3525, 0x3526, 0x3527, 0x3528, 0x3529,
  0x352a, 0x352b, 0x352c, 0x352d, 0x352e, 0x352f, 0x3530, 0x3531,
  0x3532, 0x3533, 0x3534, 0x3535, 0x3536, 0x3537, 0x3538, 0x3539,
  0x353a, 0x353b, 0x353c, 0x353d, 0x353e, 0x353f, 0x3540, 0x3541,
  0x3542, 0x3543, 0x3544, 0x3545, 0x3546, 0x3547, 0x3548, 0x3549,
  0x354a, 0x354b, 0x354c, 0x354d, 0x354e, 0x354f, 0x3550, 0x3551,
  0x3552, 0x3553, 0x3554, 0x3555, 0x3556, 0x3557, 0x3558, 0x3559,
  0x355a, 0x355b, 0x355c, 0x355d, 0x355e, 0x355f, 0x3560, 0x3561,
  0x3562, 0x3563, 0x3564, 0x3565, 0x3566, 0x3567, 0x3568, 0x3569,
  0x356a, 0x356b, 0x356c, 0x356d, 0x356e, 0x356f, 0x3570, 0x3571,
  0x3572, 0x3573, 0x3574, 0x3575, 0x3576, 0x3577, 0x3578, 0x3579,
  0x357a, 0x357b, 0x357c, 0x357d, 0x357e, 0x3621, 0x3622, 0x3623,
  0x3624, 0x3625, 0x3626, 0x3627, 0x3628, 0x3629, 0x362a, 0x362b,
  0x362c, 0x362d, 0x362e, 0x362f, 0x3630, 0x3631, 0x3632, 0x3633,
  0x3634, 0x3635, 0x3636, 0x3637, 0x3638, 0x3639, 0x363a, 0x363b,
  0x363c, 0x363d, 0x363e, 0x363f, 0x3640, 0x3641, 0x3642, 0x3643,
  0x3644, 0x3645, 0x3646, 0x3647, 0x3648, 0x3649, 0x364a, 0x364b,
  0x364c, 0x364d, 0x364e, 0x364f, 0x3650, 0x3651, 0x3652, 0x3653,
  0x3654, 0x3655, 0x3656, 0x3657, 0x3658, 0x3659, 0x365a, 0x365b,
  0x365c, 0x365d, 0x365e, 0x365f, 0x3660, 0x3661, 0x3662, 0x3663,
  0x3664, 0x3665, 0x3666, 0x3667, 0x3668, 0x3669, 0x366a, 0x366b,
  0x366c, 0x366d, 0x3670, 0x3671, 0x366e, 0x366f, 0x3672, 0x3673,
  0x3674, 0x3675, 0x3676, 0x3677, 0x3678, 0x3679, 0x367a, 0x367b,
  0x367d, 0x367e, 0x367c, 0x3721, 0x3722, 0x3723, 0x3724, 0x3725,
  0x3726, 0x3727, 0x3728, 0x3729, 0x372a, 0x372b, 0x372c, 0x372d,
  0x372e, 0x372f, 0x3730, 0x3731, 0x3732, 0x3733, 0x3734, 0x3735,
  0x3736, 0x3737, 0x3738, 0x3739, 0x373a, 0x373b, 0x373c, 0x373d,
  0x373e, 0x373f, 0x3740, 0x3741, 0x3742, 0x3743, 0x3744, 0x3745,
  0x3746, 0x3747, 0x3748, 0x3749, 0x374a, 0x374b, 0x374c, 0x374d,
  0x374e, 0x374f, 0x3750, 0x3751, 0x3752, 0x3753, 0x3754, 0x3755,
  0x3756, 0x3757, 0x3760, 0x3758, 0x3759, 0x375a, 0x375b, 0x375c,
  0x375d, 0x375e, 0x375f, 0x3761, 0x3762, 0x3763, 0x3764, 0x3765,
  0x3766, 0x3767, 0x3768, 0x3769, 0x376a, 0x376b, 0x376c, 0x376d,
  0x377e, 0x376e, 0x376f, 0x3770, 0x3771, 0x3772, 0x3773, 0x3774,
  0x3775, 0x3776, 0x3777, 0x3778, 0x3779, 0x377a, 0x377b, 0x377c,
  0x377d, 0x3821, 0x3822, 0x3823, 0x3824, 0x3825, 0x3826, 0x3827,
  0x3828, 0x3829, 0x382a, 0x382b, 0x382c, 0x382d, 0x382e, 0x382f,
  0x3830, 0x3831, 0x3832, 0x3833, 0x3834, 0x3835, 0x3836, 0x3837,
  0x3838, 0x3839, 0x383a, 0x383b, 0x383c, 0x383d, 0x383e, 0x383f,
  0x3840, 0x3841, 0x3842, 0x3843, 0x3844, 0x3845, 0x3846, 0x3847,
  0x3848, 0x3849, 0x384a, 0x384b, 0x384c, 0x384d, 0x384e, 0x3850,
  0x3851, 0x384f, 0x3852, 0x3853, 0x3854, 0x3855, 0x3856, 0x3857,
  0x3858, 0x3859, 0x385a, 0x385b, 0x385c, 0x385d, 0x385e, 0x385f,
  0x3860, 0x3861, 0x3862, 0x3863, 0x3864, 0x3865, 0x3867, 0x3868,
  0x3869, 0x386a, 0x386b, 0x386c, 0x386d, 0x386e, 0x386f, 0x3870,
  0x3871, 0x3872, 0x3873, 0x3874, 0x3875, 0x3876, 0x3877, 0x3878,
  0x3879, 0x387a, 0x387b, 0x387c, 0x387d, 0x387e, 0x3921, 0x3922,
  0x3923, 0x3924, 0x3925, 0x3926, 0x3927, 0x3928, 0x3929, 0x392a,
  0x392b, 0x392c, 0x392d, 0x392e, 0x392f, 0x3930, 0x3931, 0x3932,
  0x3933, 0x3934, 0x3935, 0x3936, 0x3937, 0x3938, 0x3939, 0x393a,
  0x393b, 0x393c, 0x393d, 0x393e, 0x393f, 0x3940, 0x3941, 0x3942,
  0x3943, 0x3944, 0x3945, 0x3946, 0x3947, 0x3948, 0x3949, 0x394a,
  0x394b, 0x394c, 0x394d, 0x394e, 0x394f, 0x3950, 0x3951, 0x3952,
  0x3953, 0x3954, 0x3955, 0x3956, 0x3957, 0x3958, 0x3959, 0x395a,
  0x395b, 0x395c, 0x395d, 0x395e, 0x395f, 0x3960, 0x3961, 0x3962,
  0x3963, 0x3964, 0x3965, 0x3966, 0x3967, 0x3968, 0x3969, 0x396a,
  0x396b, 0x396c, 0x396d, 0x396e, 0x396f, 0x3970, 0x3971, 0x3972,
  0x3973, 0x3974, 0x3975, 0x3976, 0x3977, 0x3978, 0x3979, 0x397a,
  0x397b, 0x397c, 0x397d, 0x397e, 0x3a21, 0x3a22, 0x3a23, 0x3a24,
  0x3a25, 0x3a26, 0x3a27, 0x3a28, 0x3a29, 0x3a2a, 0x3a2b, 0x3a2c,
  0x3a2d, 0x3a2e, 0x3a2f, 0x3a30, 0x3a31, 0x3a33, 0x3a34, 0x3a35,
  0x3a36, 0x3a37, 0x3a38, 0x3a32, 0x3a39, 0x3a3a, 0x3a3b, 0x3a3c,
  0x3a3d, 0x3a3e, 0x3a3f, 0x3a40, 0x3a41, 0x3a42, 0x3a43, 0x3a44,
  0x3a45, 0x3a46, 0x3a47, 0x3a48, 0x3a49, 0x3a4a, 0x3a4b, 0x3a4c,
  0x3a4d, 0x3a4e, 0x3a4f, 0x3a50, 0x3a51, 0x3a52, 0x3a53, 0x3a54,
  0x3a55, 0x3a56, 0x3a57, 0x3a58, 0x3a59, 0x3a5a, 0x3a5b, 0x3a5c,
  0x3a5d, 0x3a5e, 0x3a5f, 0x3a60, 0x3a61, 0x3a62, 0x3a63, 0x3a64,
  0x3a65, 0x3a66, 0x3a67, 0x3a68, 0x3a69, 0x3a6a, 0x3a6b, 0x3a6c,
  0x3a6d, 0x3a6e, 0x3a6f, 0x3a70, 0x3a71, 0x3a72, 0x3a73, 0x3a74,
  0x3a75, 0x3a76, 0x3a77, 0x3a78, 0x3a79, 0x3a7a, 0x3a7b, 0x3a7c,
  0x3a7d, 0x3a7e, 0x3b21, 0x3b22, 0x3b23, 0x3b24, 0x3b25, 0x3b26,
  0x3b27, 0x3b28, 0x3b29, 0x3b2a, 0x3b2b, 0x3b2c, 0x3b2d, 0x3b2e,
  0x3b2f, 0x3b30, 0x3b31, 0x3b32, 0x3b33, 0x3b34, 0x3b35, 0x3b36,
  0x3b37, 0x3b38, 0x3b39, 0x3b3a, 0x3b3b, 0x3b3d, 0x3b3c, 0x3b3e,
  0x3b3f, 0x3b40, 0x3b41, 0x3b42, 0x3b43, 0x3b44, 0x3b45, 0x3b47,
  0x3b48, 0x3b49, 0x3b4a, 0x3b46, 0x3b4b, 0x3b4c, 0x3b4d, 0x3b4e,
  0x3b4f, 0x3b50, 0x3b51, 0x3b52, 0x3b53, 0x3b57, 0x3b55, 0x3b54,
  0x3b56, 0x3b58, 0x3b59, 0x3b5a, 0x3b5b, 0x3b5c, 0x3b5d, 0x3b5e,
  0x3b5f, 0x3b60, 0x3b61, 0x3b62, 0x3b63, 0x3b64, 0x3b65, 0x3b66,
  0x3b67, 0x3b68, 0x3b69, 0x3b6a, 0x3b6b, 0x3b6c, 0x3b6d, 0x3b6e,
  0x3b6f, 0x3b70, 0x3b71, 0x3b72, 0x6674, 0x3b73, 0x3b74, 0x3b75,
  0x3b76, 0x3b77, 0x3b78, 0x3b7a, 0x3b79, 0x3b7b, 0x3b7c, 0x3b7d,
  0x3b7e, 0x3c21, 0x3c22, 0x3c23, 0x3c24, 0x3c25, 0x3c26, 0x3c27,
  0x3c28, 0x3c29, 0x3c2a, 0x3c2b, 0x3c2c, 0x3c2e, 0x3c2d, 0x3c2f,
  0x3c30, 0x3c31, 0x3c34, 0x3c32, 0x3c33, 0x3c35, 0x3c36, 0x3c37,
  0x3c38, 0x3c39, 0x3c3a, 0x3c3b, 0x3c3c, 0x3c3d, 0x3c3e, 0x3c3f,
  0x3c40, 0x3c41, 0x3c42, 0x3c43, 0x3c44, 0x3c45, 0x3c46, 0x3c47,
  0x3c48, 0x3c49, 0x3c4a, 0x3c4b, 0x3c4c, 0x3c4d, 0x3c4e, 0x3c4f,
  0x3c50, 0x3c52, 0x3c51, 0x3c53, 0x3c54, 0x3c55, 0x3c56, 0x3c57,
  0x3c58, 0x3c59, 0x3c5a, 0x3c5b, 0x3c5c, 0x3c5d, 0x3c5e, 0x3c5f,
  0x3c60, 0x3c61, 0x3c62, 0x3c63, 0x3c64, 0x3c65, 0x3c66, 0x3c67,
  0x3c68, 0x3c69, 0x3c6a, 0x3c6b, 0x3c6c, 0x3c6d, 0x3c6e, 0x3c6f,
  0x3c70, 0x3c71, 0x3c72, 0x3c73, 0x3c74, 0x3c75, 0x3c76, 0x3c77,
  0x3c78, 0x3c79, 0x3c7a, 0x3c7b, 0x3c7c, 0x3c7d, 0x3c7e, 0x3d21,
  0x3d22, 0x3d23, 0x3d24, 0x3d25, 0x3d26, 0x3d27, 0x3d28, 0x3d29,
  0x3d2a, 0x3d2b, 0x3d2c, 0x3d2d, 0x3d2e, 0x3d2f, 0x3d32, 0x3d30,
  0x3d31, 0x3d33, 0x3d34, 0x3d35, 0x3d36, 0x3d37, 0x3d38, 0x3d39,
  0x3d3a, 0x3d3b, 0x3d3c, 0x3d3d, 0x3d3e, 0x3d3f, 0x3d40, 0x3d41,
  0x3d42, 0x3d43, 0x3d44, 0x3d45, 0x3d46, 0x3d47, 0x3d48, 0x3d49,
  0x3d4a, 0x3d4b, 0x3d4c, 0x3d4d, 0x3d4e, 0x3d4f, 0x3d50, 0x3d51,
  0x3d52, 0x3d53, 0x3d54, 0x3d55, 0x3d56, 0x3d57, 0x3d58, 0x3d59,
  0x3d5a, 0x3d5b, 0x3d5c, 0x3d5d, 0x3d5e, 0x3d5f, 0x3d60, 0x3d61,
  0x3d62, 0x3d63, 0x3d64, 0x3d65, 0x3d66, 0x3d67, 0x3d68, 0x3d69,
  0x3d6a, 0x3d6b, 0x3d6c, 0x3d6d, 0x3d6e, 0x3d6f, 0x3d70, 0x3d71,
  0x3d72, 0x3d73, 0x3d74, 0x3d75, 0x3d76, 0x3d77, 0x3d78, 0x3d79,
  0x3d7a, 0x3d7b, 0x3d7c, 0x3d7d, 0x3d7e, 0x3e21, 0x3e22, 0x3e23,
  0x3e24, 0x3e25, 0x3e26, 0x3e27, 0x3e28, 0x3e29, 0x3e2a, 0x3e2b,
  0x3e2c, 0x3e2d, 0x3e2e, 0x3e2f, 0x3e30, 0x3e31, 0x3e32, 0x3e33,
  0x3e34, 0x3e35, 0x3e36, 0x3e37, 0x3e38, 0x3e39, 0x3e3a, 0x3e3b,
  0x3e3c, 0x3e3d, 0x3e3e, 0x3e3f, 0x3e40, 0x3e41, 0x3e42, 0x3e43,
  0x3e44, 0x3e45, 0x3e46, 0x3e47, 0x3e48, 0x3e49, 0x3e4a, 0x3e4b,
  0x3e4c, 0x3e4d, 0x3e4e, 0x3e4f, 0x3e50, 0x3e51, 0x3e52, 0x3e53,
  0x3e54, 0x3e55, 0x3e56, 0x3e57, 0x3e58, 0x3e59, 0x3e5a, 0x3e5b,
  0x3e5c, 0x3e5d, 0x3e5e, 0x3e5f, 0x3e60, 0x3e61, 0x3e62, 0x3e63,
  0x3e64, 0x3e65, 0x3e66, 0x3e67, 0x3e68, 0x3e69, 0x3e6a, 0x3e6b,
  0x3e6c, 0x3e6d, 0x3e6e, 0x3e6f, 0x3e70, 0x3e71, 0x3e72, 0x3e73,
  0x3e74, 0x3e75, 0x3e76, 0x3e77, 0x3e78, 0x3e79, 0x3e7a, 0x3e7b,
  0x3e7e, 0x3e7c, 0x3e7d, 0x3f21, 0x3f22, 0x3f23, 0x3f24, 0x3f25,
  0x3f26, 0x3f27, 0x3f28, 0x3f29, 0x3f2a, 0x3f2b, 0x3f2c, 0x3f2d,
  0x3f2e, 0x3f2f, 0x3f30, 0x3f31, 0x3f32, 0x3f33, 0x3f34, 0x3f35,
  0x3f36, 0x3f37, 0x3f38, 0x3f39, 0x3f3a, 0x3f3b, 0x3f3c, 0x3f3d,
  0x3f3e, 0x3f3f, 0x3f40, 0x3f41, 0x3f42, 0x3f43, 0x3f44, 0x3f45,
  0x3f46, 0x3f47, 0x3f48, 0x3f49, 0x3f4a, 0x3f4b, 0x3f4c, 0x3f4d,
  0x3f4e, 0x3f4f, 0x3f50, 0x3f51, 0x3f52, 0x3f53, 0x3f54, 0x3f55,
  0x3f56, 0x3f57, 0x3f58, 0x3f59, 0x3f5a, 0x3f5b, 0x3f5c, 0x3f5d,
  0x3f5e, 0x3f5f, 0x3f60, 0x3f61, 0x3f62, 0x3f63, 0x3f64, 0x3f65,
  0x3f66, 0x3f67, 0x3f68, 0x3f69, 0x3f6a, 0x3f6b, 0x3f6c, 0x3f6d,
  0x3f6e, 0x3f6f, 0x3f70, 0x3f71, 0x3f72, 0x3f73, 0x3f74, 0x3f75,
  0x3f76, 0x3f77, 0x3f78, 0x3f79, 0x3f7a, 0x3f7b, 0x3f7c, 0x3f7d,
  0x3f7e, 0x4021, 0x4022, 0x4023, 0x4024, 0x4025, 0x4026, 0x4027,
  0x4028, 0x4029, 0x402a, 0x402b, 0x402c, 0x402d, 0x402e, 0x402f,
  0x4030, 0x4031, 0x4032, 0x4033, 0x4034, 0x4035, 0x4036, 0x4037,
  0x4038, 0x4039, 0x403a, 0x403b, 0x403c, 0x403d, 0x403e, 0x403f,
  0x4040, 0x4041, 0x4042, 0x4043, 0x4044, 0x4045, 0x4046, 0x4047,
  0x4048, 0x4049, 0x404a, 0x404b, 0x404c, 0x404d, 0x404e, 0x404f,
  0x4050, 0x4051, 0x4052, 0x4053, 0x4054, 0x4055, 0x4056, 0x4057,
  0x4058, 0x4059, 0x405a, 0x405b, 0x405c, 0x405d, 0x405e, 0x405f,
  0x4060, 0x4061, 0x4062, 0x4063, 0x4064, 0x4065, 0x4066, 0x4067,
  0x4068, 0x4069, 0x406a, 0x406b, 0x406c, 0x406d, 0x406e, 0x406f,
  0x4070, 0x4071, 0x4072, 0x4073, 0x4074, 0x4075, 0x4076, 0x4077,
  0x4078, 0x4079, 0x407a, 0x407b, 0x407c, 0x407d, 0x407e, 0x4121,
  0x4122, 0x4123, 0x4124, 0x4125, 0x4126, 0x4127, 0x4128, 0x4129,
  0x412a, 0x412b, 0x412c, 0x412d, 0x412e, 0x412f, 0x4130, 0x4131,
  0x4132, 0x4133, 0x4134, 0x4135, 0x4136, 0x4137, 0x4138, 0x4139,
  0x413a, 0x413b, 0x413c, 0x413d, 0x413e, 0x413f, 0x4140, 0x4141,
  0x4142, 0x4143, 0x4144, 0x4145, 0x4146, 0x4147, 0x4148, 0x4149,
  0x414a, 0x414b, 0x414c, 0x414d, 0x414e, 0x414f, 0x4150, 0x4151,
  0x4152, 0x4153, 0x4154, 0x4155, 0x4156, 0x4157, 0x4158, 0x4159,
  0x415a, 0x415b, 0x415c, 0x415d, 0x415e, 0x415f, 0x4160, 0x4161,
  0x4162, 0x4163, 0x4164, 0x4165, 0x4166, 0x4167, 0x4168, 0x4169,
  0x416a, 0x416b, 0x416c, 0x416d, 0x416e, 0x416f, 0x4170, 0x4171,
  0x4172, 0x4173, 0x4174, 0x4175, 0x4176, 0x4177, 0x4178, 0x4179,
  0x417a, 0x417b, 0x417c, 0x417d, 0x417e, 0x4221, 0x4222, 0x4223,
  0x4224, 0x4225, 0x4226, 0x4227, 0x4228, 0x4229, 0x422a, 0x422b,
  0x422c, 0x422d, 0x422e, 0x4230, 0x422f, 0x4231, 0x4232, 0x4233,
  0x4234, 0x4235, 0x4237, 0x4236, 0x4238, 0x4239, 0x423a, 0x423b,
  0x423c, 0x423d, 0x423e, 0x4240, 0x4241, 0x4242, 0x4244, 0x4245,
  0x4247, 0x4248, 0x4249, 0x424a, 0x424c, 0x4243, 0x4246, 0x424b,
  0x424d, 0x424e, 0x424f, 0x4250, 0x4251, 0x4252, 0x4253, 0x4254,
  0x4255, 0x4256, 0x4257, 0x4258, 0x4259, 0x425a, 0x425b, 0x425c,
  0x425d, 0x425e, 0x425f, 0x4260, 0x4261, 0x4262, 0x4263, 0x4264,
  0x4265, 0x4266, 0x4267, 0x4268, 0x4269, 0x426a, 0x426b, 0x426c,
  0x426d, 0x423f, 0x426e, 0x426f, 0x4270, 0x4271, 0x4272, 0x4273,
  0x4274, 0x4275, 0x4276, 0x4277, 0x4278, 0x4279, 0x427a, 0x427b,
  0x427c, 0x427d, 0x427e, 0x4321, 0x4322, 0x4323, 0x4324, 0x4325,
  0x4326, 0x4327, 0x4328, 0x4329, 0x432a, 0x432b, 0x432c, 0x432d,
  0x432e, 0x432f, 0x4330, 0x4331, 0x4332, 0x4333, 0x4334, 0x4335,
  0x4336, 0x4337, 0x4339, 0x433a, 0x433b, 0x433c, 0x433d, 0x433e,
  0x433f, 0x4340, 0x4341, 0x4342, 0x4343, 0x4344, 0x4345, 0x4346,
  0x4347, 0x4348, 0x4338, 0x434a, 0x434b, 0x434c, 0x434d, 0x434f,
  0x434e, 0x4350, 0x4351, 0x4352, 0x4353, 0x4354, 0x4355, 0x4356,
  0x4357, 0x4358, 0x4359, 0x435a, 0x435b, 0x4349, 0x435c, 0x435d,
  0x435e, 0x435f, 0x4360, 0x4361, 0x4362, 0x4363, 0x4364, 0x4365,
  0x4366, 0x4367, 0x4368, 0x4369, 0x436a, 0x436b, 0x436c, 0x436d,
  0x436e, 0x436f, 0x4370, 0x4371, 0x4372, 0x4373, 0x4374, 0x4375,
  0x4376, 0x4377, 0x4378, 0x4379, 0x437a, 0x437b, 0x437c, 0x437d,
  0x437e, 0x4421, 0x4422, 0x4423, 0x4424, 0x4425, 0x4426, 0x4427,
  0x4428, 0x4429, 0x442a, 0x442b, 0x442c, 0x442d, 0x442e, 0x442f,
  0x4430, 0x4431, 0x4432, 0x4433, 0x4434, 0x4435, 0x4436, 0x4437,
  0x4438, 0x4439, 0x443a, 0x443b, 0x443c, 0x443d, 0x443e, 0x443f,
  0x4440, 0x4441, 0x4442, 0x4443, 0x4444, 0x4445, 0x4446, 0x4447,
  0x4448, 0x4449, 0x444a, 0x444b, 0x444c, 0x444d, 0x444e, 0x444f,
  0x4450, 0x4451, 0x4452, 0x4453, 0x4454, 0x4455, 0x4456, 0x4457,
  0x4458, 0x4459, 0x445a, 0x445b, 0x445c, 0x445d, 0x445e, 0x445f,
  0x4460, 0x4461, 0x4462, 0x4463, 0x4464, 0x4465, 0x4466, 0x4467,
  0x4468, 0x4469, 0x446a, 0x446b, 0x446c, 0x446d, 0x446e, 0x446f,
  0x4470, 0x4471, 0x4472, 0x4473, 0x4474, 0x4475, 0x4476, 0x4477,
  0x4478, 0x4479, 0x447a, 0x447b, 0x447c, 0x447d, 0x447e, 0x4521,
  0x4522, 0x4523, 0x4524, 0x4525, 0x4526, 0x4527, 0x4528, 0x4529,
  0x452a, 0x452b, 0x452c, 0x452d, 0x452e, 0x452f, 0x4530, 0x4531,
  0x4532, 0x4533, 0x4534, 0x4535, 0x4536, 0x4537, 0x4538, 0x4539,
  0x453a, 0x453b, 0x453c, 0x453d, 0x453e, 0x453f, 0x4540, 0x4541,
  0x4542, 0x4543, 0x4544, 0x4545, 0x4546, 0x4547, 0x4548, 0x4549,
  0x454a, 0x454b, 0x454d, 0x454c, 0x454e, 0x454f, 0x4550, 0x4551,
  0x4552, 0x4553, 0x4554, 0x4555, 0x4556, 0x4557, 0x4558, 0x4559,
  0x455a, 0x455b, 0x455c, 0x455d, 0x455e, 0x455f, 0x4560, 0x4561,
  0x4562, 0x4563, 0x4564, 0x4565, 0x4566, 0x4567, 0x4568, 0x4569,
  0x456a, 0x456b, 0x456c, 0x456d, 0x456e, 0x456f, 0x4570, 0x4571,
  0x4572, 0x4573, 0x4574, 0x4575, 0x4576, 0x4577, 0x4578, 0x4579,
  0x457a, 0x457b, 0x457c, 0x457d, 0x457e, 0x4621, 0x4622, 0x4623,
  0x4624, 0x4625, 0x4626, 0x4627, 0x4628, 0x4629, 0x462a, 0x462b,
  0x462c, 0x462d, 0x462e, 0x462f, 0x4630, 0x4631, 0x4632, 0x4633,
  0x4634, 0x4635, 0x4636, 0x4637, 0x4638, 0x4639, 0x463a, 0x463b,
  0x463c, 0x463d, 0x463e, 0x463f, 0x4640, 0x4641, 0x4642, 0x4643,
  0x4644, 0x4645, 0x4646, 0x4647, 0x4648, 0x4649, 0x464a, 0x464b,
  0x464c, 0x464d, 0x464e, 0x464f, 0x4650, 0x4651, 0x4652, 0x4653,
  0x4654, 0x4655, 0x4656, 0x4657, 0x4658, 0x4659, 0x465a, 0x465b,
  0x465c, 0x465d, 0x465e, 0x465f, 0x4660, 0x4736, 0x4661, 0x4662,
  0x4663, 0x4664, 0x4665, 0x4666, 0x4667, 0x4668, 0x4669, 0x466a,
  0x466b, 0x466c, 0x466d, 0x466e, 0x466f, 0x4670, 0x4671, 0x4672,
  0x4673, 0x4674, 0x4675, 0x4676, 0x4677, 0x4678, 0x4679, 0x467a,
  0x467b, 0x467c, 0x467d, 0x467e, 0x4721, 0x4722, 0x4723, 0x4724,
  0x4725, 0x4726, 0x4727, 0x4728, 0x4729, 0x472a, 0x472b, 0x472c,
  0x472d, 0x472e, 0x472f, 0x4730, 0x4731, 0x4732, 0x4733, 0x4734,
  0x4735, 0x4737, 0x4738, 0x4739, 0x473a, 0x473b, 0x473c, 0x473d,
  0x473e, 0x473f, 0x4740, 0x4741, 0x4742, 0x4743, 0x4744, 0x4745,
  0x4746, 0x4747, 0x4748, 0x4749, 0x474a, 0x474b, 0x474c, 0x474d,
  0x474e, 0x474f, 0x4750, 0x4751, 0x4752, 0x4753, 0x4754, 0x4755,
  0x4756, 0x4757, 0x4758, 0x4759, 0x475a, 0x475b, 0x475c, 0x475d,
  0x475e, 0x475f, 0x4760, 0x4761, 0x4762, 0x4763, 0x4764, 0x4765,
  0x4766, 0x4767, 0x4768, 0x4769, 0x476a, 0x476b, 0x476c, 0x476d,
  0x476e, 0x476f, 0x4770, 0x4771, 0x4772, 0x4773, 0x4774, 0x4775,
  0x4776, 0x4777, 0x4778, 0x4779, 0x477a, 0x477b, 0x477c, 0x477d,
  0x477e, 0x4821, 0x4822, 0x4823, 0x4824, 0x4825, 0x4826, 0x4827,
  0x4828, 0x4829, 0x482a, 0x482b, 0x482c, 0x482d, 0x482e, 0x482f,
  0x4830, 0x4831, 0x4832, 0x4833, 0x4834, 0x4835, 0x4836, 0x4837,
  0x4838, 0x4839, 0x483a, 0x483b, 0x483c, 0x483d, 0x483e, 0x483f,
  0x4840, 0x4841, 0x4842, 0x4843, 0x4844, 0x4845, 0x4846, 0x4847,
  0x4848, 0x4849, 0x484a, 0x484b, 0x484c, 0x4853, 0x484d, 0x484e,
  0x484f, 0x4850, 0x4851, 0x4852, 0x4854, 0x4855, 0x4856, 0x4857,
  0x4858, 0x4859, 0x485a, 0x485b, 0x485c, 0x485d, 0x485e, 0x485f,
  0x4860, 0x4861, 0x4862, 0x4863, 0x4864, 0x4865, 0x4866, 0x4867,
  0x4868, 0x4869, 0x486a, 0x486b, 0x486c, 0x486d, 0x486e, 0x486f,
  0x4870, 0x4871, 0x4872, 0x4873, 0x4874, 0x4875, 0x4876, 0x4877,
  0x4878, 0x4879, 0x487a, 0x487b, 0x487c, 0x487d, 0x487e, 0x4921,
  0x4922, 0x4923, 0x4924, 0x4925, 0x4926, 0x4927, 0x4928, 0x4929,
  0x492a, 0x492b, 0x492c, 0x492d, 0x492e, 0x492f, 0x4930, 0x4931,
  0x4932, 0x4933, 0x4934, 0x4935, 0x4936, 0x4937, 0x4938, 0x4939,
  0x493a, 0x493b, 0x493c, 0x4941, 0x493d, 0x493e, 0x493f, 0x4940,
  0x4942, 0x4943, 0x4944, 0x4945, 0x4946, 0x4947, 0x4948, 0x4949,
  0x494a, 0x494b, 0x494c, 0x494d, 0x494e, 0x494f, 0x4950, 0x4951,
  0x4952, 0x4953, 0x4954, 0x4955, 0x4956, 0x4957, 0x4958, 0x4959,
  0x495a, 0x495b, 0x495c, 0x495d, 0x495e, 0x495f, 0x4960, 0x4961,
  0x4962, 0x4963, 0x4964, 0x4965, 0x4966, 0x4967, 0x4968, 0x4969,
  0x496a, 0x496b, 0x496c, 0x496d, 0x496e, 0x496f, 0x4970, 0x4971,
  0x4972, 0x4973, 0x4974, 0x4975, 0x4976, 0x4977, 0x4978, 0x4979,
  0x497a, 0x497b, 0x497c, 0x497d, 0x497e, 0x4a21, 0x4a22, 0x4a23,
  0x4a24, 0x4a25, 0x4a26, 0x4a27, 0x4a28, 0x4a29, 0x4a2a, 0x4a2b,
  0x4a2c, 0x4a2d, 0x4a2e, 0x4a2f, 0x4a30, 0x4a31, 0x4a32, 0x4a33,
  0x4a34, 0x4a35, 0x4a36, 0x4a37, 0x4a38, 0x4a39, 0x4a3a, 0x4a3b,
  0x4a3c, 0x4a3d, 0x4a3e, 0x4a3f, 0x4a40, 0x4a41, 0x4a42, 0x4a43,
  0x4a44, 0x4a45, 0x4a46, 0x4a47, 0x4a48, 0x4a49, 0x4a4a, 0x4a4b,
  0x4a4c, 0x4a4d, 0x4a4e, 0x4a4f, 0x4a50, 0x4a51, 0x4a52, 0x4a53,
  0x4a54, 0x4a55, 0x4a56, 0x4a57, 0x4a58, 0x4a59, 0x4a5a, 0x4a5b,
  0x4a5c, 0x4a5d, 0x4a5e, 0x4a5f, 0x4a60, 0x4a61, 0x4a62, 0x4a63,
  0x4a64, 0x4a65, 0x4a66, 0x4a67, 0x4a68, 0x4a69, 0x4a6a, 0x4a6b,
  0x4a6c, 0x4a6d, 0x4a6e, 0x4a6f, 0x4a70, 0x4a71, 0x4a72, 0x4a73,
  0x4a74, 0x4a75, 0x4a76, 0x4a77, 0x4a78, 0x4a79, 0x4a7a, 0x4a7b,
  0x4a7c, 0x4a7d, 0x4a7e, 0x4b21, 0x4b22, 0x4b23, 0x4b24, 0x4b25,
  0x4b26, 0x4b27, 0x4b28, 0x4b29, 0x4b2a, 0x4b2b, 0x4b2c, 0x4b2d,
  0x4b2e, 0x4b2f, 0x4b30, 0x4b31, 0x4b32, 0x4b33, 0x4b34, 0x4b35,
  0x4b36, 0x4b37, 0x4b38, 0x4b39, 0x4b3a, 0x4b3b, 0x4b3c, 0x4b3d,
  0x4b3e, 0x4b3f, 0x4b40, 0x4b41, 0x4b42, 0x4b43, 0x4b44, 0x4b45,
  0x4b46, 0x4b47, 0x4b48, 0x4b49, 0x4b4a, 0x4b4b, 0x4b4c, 0x4b4d,
  0x4b4e, 0x4b4f, 0x4b50, 0x4b51, 0x4b52, 0x4b53, 0x4b54, 0x4b55,
  0x4b56, 0x4b57, 0x4b58, 0x4b59, 0x4b5a, 0x4b5b, 0x4b5c, 0x4b5d,
  0x4b5e, 0x4b5f, 0x4b60, 0x4b61, 0x4b62, 0x4b63, 0x4b64, 0x4b65,
  0x4b66, 0x4b67, 0x4b68, 0x4b69, 0x4b6a, 0x4b6b, 0x4b6c, 0x4b6d,
  0x4b6e, 0x4b6f, 0x4b70, 0x4b71, 0x4b72, 0x4b73, 0x4b74, 0x4b75,
  0x4b76, 0x4b77, 0x4b78, 0x4b79, 0x4b7a, 0x4b7b, 0x4b7c, 0x4b7d,
  0x4b7e, 0x4c21, 0x4c22, 0x4c23, 0x4c24, 0x4c25, 0x4c26, 0x4c27,
  0x4c28, 0x4c29, 0x4c2a, 0x4c2b, 0x4c2c, 0x4c2d, 0x4c2e, 0x4c2f,
  0x4c30, 0x4c31, 0x4c32, 0x4c33, 0x4c34, 0x4c35, 0x4c36, 0x4c37,
  0x4c38, 0x4c39, 0x4c3a, 0x4c3b, 0x4c3c, 0x4c3d, 0x4c3e, 0x4c3f,
  0x4c40, 0x4c41, 0x4c42, 0x4c43, 0x4c44, 0x4c45, 0x4c46, 0x4c47,
  0x4c48, 0x4c49, 0x4c4a, 0x4c4b, 0x4c4c, 0x4c4d, 0x4c4e, 0x4c4f,
  0x4c50, 0x4c51, 0x4c52, 0x4c53, 0x4c54, 0x4c55, 0x4c56, 0x4c57,
  0x4c58, 0x4c59, 0x4c5a, 0x4c5b, 0x4c5c, 0x4c5d, 0x4c5e, 0x4c5f,
  0x4c60, 0x4c61, 0x4c62, 0x4c63, 0x4c64, 0x4c65, 0x4c66, 0x4c67,
  0x4c68, 0x4c69, 0x4c6a, 0x4c6b, 0x4c6c, 0x4c6d, 0x4c6e, 0x4c6f,
  0x4c70, 0x4c71, 0x4c72, 0x4c73, 0x4c74, 0x4c75, 0x4c76, 0x4c77,
  0x4c78, 0x4c79, 0x4c7a, 0x4c7b, 0x4c7c, 0x4c7d, 0x4c7e, 0x4d21,
  0x4d22, 0x4d23, 0x4d24, 0x4d25, 0x4d26, 0x4d27, 0x4d28, 0x4d29,
  0x4d2a, 0x4d2b, 0x4d2c, 0x4d2d, 0x4d2e, 0x4d2f, 0x4d30, 0x4d31,
  0x4d32, 0x4d33, 0x4d34, 0x4d35, 0x4d36, 0x4d37, 0x4d38, 0x4d39,
  0x4d3a, 0x4d3b, 0x4d3c, 0x4d3d, 0x4d3e, 0x4d3f, 0x4d40, 0x4d41,
  0x4d42, 0x4d43, 0x4d44, 0x4d45, 0x4d46, 0x4d47, 0x4d48, 0x4d49,
  0x4d4a, 0x4d4b, 0x4d4c, 0x4d4d, 0x4d4e, 0x4d4f, 0x4d50, 0x4d51,
  0x4d52, 0x4d53, 0x4d54, 0x4d55, 0x4d56, 0x4d57, 0x4d58, 0x4d59,
  0x4d5a, 0x4d5b, 0x4d5c, 0x4d5d, 0x4d5e, 0x4d5f, 0x4d60, 0x4d61,
  0x4d62, 0x4d63, 0x4d64, 0x4d65, 0x4d66, 0x4d67, 0x4d68, 0x4d69,
  0x4d6a, 0x4d6b, 0x4d6c, 0x4d6d, 0x4d6e, 0x4d6f, 0x4d70, 0x4d71,
  0x4d72, 0x4d73, 0x4d74, 0x4d75, 0x4d76, 0x4d77, 0x4d78, 0x4d79,
  0x4d7a, 0x4d7b, 0x4d7c, 0x4d7d, 0x4d7e, 0x4e21, 0x4e22, 0x4e24,
  0x4e25, 0x4e26, 0x4e27, 0x4e28, 0x4e29, 0x4e23, 0x4e2a, 0x4e2b,
  0x4e2c, 0x4e2d, 0x4e2e, 0x4e2f, 0x4e30, 0x4e31, 0x4e32, 0x4e33,
  0x4e34, 0x4e35, 0x4e36, 0x4e37, 0x4e38, 0x4e39, 0x4e3a, 0x4e3b,
  0x4e3c, 0x4e3d, 0x4e3e, 0x4e3f, 0x4e40, 0x4e41, 0x4e42, 0x4e43,
  0x4e44, 0x4e45, 0x4e46, 0x4e47, 0x4e48, 0x4e49, 0x4e4a, 0x4e4b,
  0x4e4c, 0x4e4d, 0x4e4e, 0x4e4f, 0x4e50, 0x4e51, 0x4e52, 0x4e53,
  0x4e54, 0x4e55, 0x4e56, 0x4e57, 0x4e58, 0x4e59, 0x4e5a, 0x4e5b,
  0x4e5c, 0x4e5d, 0x4e5e, 0x4e5f, 0x4e60, 0x4e61, 0x4e62, 0x4e63,
  0x4e64, 0x4e65, 0x4e66, 0x4e67, 0x4e68, 0x4e69, 0x4e6a, 0x4e6b,
  0x4e6c, 0x4e6d, 0x4e6e, 0x4e6f, 0x4e70, 0x4e71, 0x4e72, 0x4e73,
  0x4e74, 0x4e75, 0x4e76, 0x4e77, 0x4e78, 0x4e79, 0x4e7a, 0x4e7b,
  0x4e7c, 0x4e7d, 0x4e7e, 0x4f21, 0x4f22, 0x4f23, 0x4f24, 0x4f25,
  0x4f26, 0x4f27, 0x4f28, 0x4f29, 0x4f2a, 0x4f2b, 0x4f2c, 0x4f2d,
  0x4f2e, 0x4f2f, 0x4f30, 0x4f31, 0x4f32, 0x4f33, 0x4f34, 0x4f35,
  0x4f36, 0x4f37, 0x4f38, 0x4f39, 0x4f3a, 0x4f3b, 0x4f3c, 0x4f3d,
  0x4f3e, 0x4f3f, 0x4f40, 0x4f41, 0x4f42, 0x4f43, 0x4f44, 0x4f45,
  0x4f46, 0x4f47, 0x4f48, 0x4f49, 0x4f4a, 0x4f4b, 0x4f4c, 0x4f4d,
  0x4f4e, 0x4f4f, 0x4f50, 0x4f51, 0x4f52, 0x4f53, 0x4f54, 0x4f55,
  0x4f56, 0x4f57, 0x4f58, 0x4f59, 0x4f5a, 0x4f5b, 0x4f5c, 0x4f5d,
  0x4f5e, 0x4f5f, 0x4f60, 0x4f61, 0x4f62, 0x4f63, 0x4f64, 0x4f65,
  0x4f66, 0x4f67, 0x4f68, 0x4f69, 0x4f6a, 0x4f6b, 0x4f6c, 0x4f6d,
  0x4f6e, 0x4f6f, 0x4f70, 0x4f71, 0x4f72, 0x4f74, 0x4f75, 0x4f76,
  0x4f73, 0x4f77, 0x4f78, 0x4f79, 0x4f7a, 0x4f7b, 0x4f7c, 0x4f7d,
  0x4f7e, 0x5021, 0x5022, 0x5023, 0x5024, 0x5025, 0x5026, 0x5027,
  0x5028, 0x5029, 0x502a, 0x502b, 0x502c, 0x502e, 0x502f, 0x5030,
  0x5031, 0x502d, 0x5032, 0x5033, 0x5034, 0x5035, 0x5037, 0x5038,
  0x5039, 0x503a, 0x503b, 0x5036, 0x503c, 0x503d, 0x503e, 0x503f,
  0x5040, 0x5041, 0x5042, 0x5043, 0x5044, 0x5045, 0x5046, 0x5047,
  0x5048, 0x5049, 0x504a, 0x504b, 0x504c, 0x504d, 0x504e, 0x504f,
  0x5050, 0x5051, 0x5052, 0x5053, 0x5054, 0x5055, 0x5056, 0x5057,
  0x5058, 0x5059, 0x505a, 0x505b, 0x505c, 0x505d, 0x505e, 0x505f,
  0x5060, 0x5061, 0x5062, 0x5063, 0x5064, 0x5065, 0x5066, 0x5067,
  0x5068, 0x5069, 0x506a, 0x506b, 0x506c, 0x506d, 0x506e, 0x506f,
  0x5070, 0x5071, 0x5072, 0x5073, 0x5074, 0x5075, 0x5076, 0x5077,
  0x5078, 0x5079, 0x507a, 0x507b, 0x507c, 0x507d, 0x507e, 0x5121,
  0x5122, 0x5123, 0x5124, 0x5125, 0x5126, 0x5127, 0x5128, 0x5129,
  0x512a, 0x512b, 0x512c, 0x512d, 0x512e, 0x512f, 0x5130, 0x5131,
  0x5132, 0x5133, 0x5134, 0x5135, 0x5136, 0x5137, 0x5138, 0x5139,
  0x513a, 0x513b, 0x513c, 0x513d, 0x513e, 0x513f, 0x5140, 0x5141,
  0x5142, 0x5143, 0x5144, 0x5145, 0x5146, 0x5147, 0x5148, 0x5149,
  0x514a, 0x514b, 0x514c, 0x514d, 0x514e, 0x514f, 0x5150, 0x5151,
  0x5152, 0x5153, 0x5154, 0x5155, 0x5156, 0x5157, 0x5158, 0x5159,
  0x515a, 0x515b, 0x515c, 0x515d, 0x515e, 0x515f, 0x5160, 0x5161,
  0x5162, 0x5163, 0x5164, 0x5165, 0x5166, 0x5167, 0x5168, 0x5169,
  0x516a, 0x516b, 0x516c, 0x516d, 0x516e, 0x516f, 0x5170, 0x5171,
  0x5172, 0x5173, 0x5174, 0x5175, 0x5176, 0x5177, 0x5178, 0x5179,
  0x517a, 0x517b, 0x517c, 0x517d, 0x517e, 0x5221, 0x5222, 0x5223,
  0x5224, 0x5225, 0x5226, 0x5227, 0x5228, 0x5229, 0x522a, 0x522b,
  0x522c, 0x522d, 0x522e, 0x522f, 0x5230, 0x5231, 0x5232, 0x5233,
  0x5234, 0x5235, 0x5236, 0x5237, 0x5238, 0x5239, 0x523a, 0x523b,
  0x523c, 0x523d, 0x523e, 0x523f, 0x5240, 0x5241, 0x5242, 0x5243,
  0x5244, 0x5245, 0x5246, 0x5247, 0x5248, 0x5249, 0x524a, 0x524b,
  0x524c, 0x524d, 0x524e, 0x524f, 0x5250, 0x5251, 0x5252, 0x5253,
  0x5254, 0x5255, 0x5256, 0x5257, 0x5258, 0x5259, 0x525a, 0x525b,
  0x525c, 0x525d, 0x525e, 0x525f, 0x5260, 0x5261, 0x5262, 0x5263,
  0x5264, 0x5265, 0x5266, 0x5267, 0x5268, 0x5269, 0x526a, 0x526b,
  0x526c, 0x526d, 0x526e, 0x526f, 0x5270, 0x5271, 0x5272, 0x5273,
  0x5274, 0x5276, 0x5277, 0x5278, 0x5275, 0x5279, 0x527a, 0x527b,
  0x527c, 0x527d, 0x527e, 0x5321, 0x5322, 0x5323, 0x5324, 0x5325,
  0x5326, 0x5327, 0x5328, 0x5329, 0x532a, 0x532b, 0x532c, 0x532d,
  0x532e, 0x532f, 0x5330, 0x5331, 0x5332, 0x5333, 0x5334, 0x5335,
  0x5336, 0x5337, 0x5338, 0x5339, 0x533a, 0x533b, 0x533c, 0x533d,
  0x533e, 0x533f, 0x5340, 0x5341, 0x5342, 0x5343, 0x5344, 0x5345,
  0x5346, 0x5347, 0x5348, 0x5349, 0x534a, 0x534b, 0x534c, 0x534d,
  0x534e, 0x534f, 0x5350, 0x5351, 0x5352, 0x5353, 0x5354, 0x5355,
  0x5356, 0x5357, 0x5358, 0x5359, 0x535a, 0x535b, 0x535c, 0x535d,
  0x535e, 0x535f, 0x5360, 0x5361, 0x5362, 0x5363, 0x5364, 0x5365,
  0x5366, 0x5367, 0x5368, 0x5369, 0x536a, 0x536b, 0x536c, 0x536d,
  0x536e, 0x536f, 0x5370, 0x5371, 0x5372, 0x5373, 0x5374, 0x5375,
  0x5376, 0x5377, 0x5378, 0x5379, 0x537a, 0x537b, 0x537c, 0x537d,
  0x537e, 0x5421, 0x5422, 0x5423, 0x5424, 0x5425, 0x5426, 0x5427,
  0x5428, 0x5429, 0x542a, 0x542b, 0x542c, 0x542d, 0x542e, 0x542f,
  0x5430, 0x5431, 0x5432, 0x5434, 0x5435, 0x5436, 0x5437, 0x5438,
  0x5439, 0x543a, 0x543b, 0x543c, 0x543d, 0x543e, 0x5433, 0x543f,
  0x5440, 0x5441, 0x5442, 0x5443, 0x5444, 0x5445, 0x5446, 0x5447,
  0x5448, 0x5449, 0x544a, 0x544b, 0x544c, 0x544d, 0x544e, 0x544f,
  0x5450, 0x5451, 0x5452, 0x5453, 0x5454, 0x5455, 0x5456, 0x5457,
  0x5458, 0x5459, 0x545a, 0x545b, 0x545c, 0x545d, 0x545e, 0x545f,
  0x5460, 0x5461, 0x5462, 0x5463, 0x5464, 0x5465, 0x5466, 0x5467,
  0x5468, 0x5469, 0x546a, 0x546c, 0x546b, 0x546d, 0x546e, 0x546f,
  0x5470, 0x5471, 0x5472, 0x5473, 0x5474, 0x5475, 0x5476, 0x5477,
  0x5478, 0x5479, 0x547a, 0x547b, 0x547c, 0x547d, 0x547e, 0x5521,
  0x5522, 0x5523, 0x5524, 0x5525, 0x5526, 0x5527, 0x5528, 0x5529,
  0x552a, 0x552b, 0x552c, 0x552d, 0x552e, 0x552f, 0x5530, 0x5531,
  0x5532, 0x5533, 0x5534, 0x5535, 0x5536, 0x5537, 0x5538, 0x5539,
  0x553a, 0x553b, 0x553c, 0x553d, 0x553e, 0x553f, 0x5540, 0x5541,
  0x5542, 0x5543, 0x5544, 0x5545, 0x5546, 0x5547, 0x5548, 0x5549,
  0x554a, 0x554b, 0x554c, 0x554d, 0x554e, 0x554f, 0x5550, 0x5551,
  0x5552, 0x5553, 0x5554, 0x5555, 0x5556, 0x5557, 0x5558, 0x5559,
  0x555a, 0x555b, 0x555c, 0x555d, 0x555e, 0x555f, 0x5560, 0x5561,
  0x5562, 0x5563, 0x5564, 0x5565, 0x5566, 0x5567, 0x5568, 0x5569,
  0x556a, 0x556b, 0x556c, 0x556d, 0x556e, 0x556f, 0x5570, 0x5571,
  0x5572, 0x5573, 0x5574, 0x5575, 0x5576, 0x5577, 0x5578, 0x5579,
  0x557a, 0x557b, 0x557c, 0x557d, 0x557e, 0x5621, 0x5622, 0x5623,
  0x5624, 0x5625, 0x5626, 0x5627, 0x5628, 0x5629, 0x562a, 0x562b,
  0x562c, 0x562d, 0x562e, 0x562f, 0x5630, 0x5631, 0x5632, 0x5633,
  0x5634, 0x5635, 0x5636, 0x5637, 0x5638, 0x5639, 0x563a, 0x563b,
  0x563c, 0x563d, 0x563e, 0x563f, 0x5640, 0x5641, 0x5642, 0x5643,
  0x5644, 0x5645, 0x5647, 0x5648, 0x5649, 0x564a, 0x564b, 0x5646,
  0x564c, 0x564d, 0x564e, 0x564f, 0x5650, 0x5651, 0x5652, 0x5653,
  0x5654, 0x5656, 0x5657, 0x5658, 0x5655, 0x5659, 0x565a, 0x565b,
  0x565c, 0x565d, 0x565e, 0x565f, 0x5660, 0x5661, 0x5662, 0x5663,
  0x5664, 0x5665, 0x5666, 0x5667, 0x5668, 0x5669, 0x566a, 0x566b,
  0x566c, 0x566d, 0x566e, 0x566f, 0x5670, 0x5671, 0x5672, 0x5673,
  0x5674, 0x5675, 0x5676, 0x5677, 0x5678, 0x5679, 0x567a, 0x567b,
  0x567c, 0x567d, 0x567e, 0x5721, 0x5722, 0x5723, 0x5724, 0x5725,
  0x5726, 0x5727, 0x5728, 0x5729, 0x572a, 0x572b, 0x572c, 0x572d,
  0x572e, 0x572f, 0x5730, 0x5731, 0x5732, 0x5733, 0x5734, 0x5735,
  0x5736, 0x5737, 0x5738, 0x5739, 0x573a, 0x573b, 0x573c, 0x573d,
  0x573e, 0x573f, 0x5740, 0x5741, 0x5742, 0x5743, 0x5744, 0x5745,
  0x5746, 0x5747, 0x5748, 0x5749, 0x574a, 0x574b, 0x574c, 0x574d,
  0x574e, 0x574f, 0x5750, 0x5751, 0x5752, 0x5753, 0x5754, 0x5755,
  0x5756, 0x5757, 0x5758, 0x5759, 0x575a, 0x575b, 0x575c, 0x575d,
  0x575e, 0x575f, 0x5760, 0x5761, 0x5762, 0x5764, 0x5765, 0x5766,
  0x5767, 0x5768, 0x5769, 0x576a, 0x576b, 0x576c, 0x576d, 0x576e,
  0x576f, 0x5770, 0x5771, 0x5772, 0x5773, 0x5774, 0x5775, 0x5776,
  0x5777, 0x5778, 0x5779, 0x583e, 0x5763, 0x577a, 0x577b, 0x577c,
  0x577d, 0x577e, 0x5821, 0x5822, 0x5823, 0x5824, 0x5825, 0x5826,
  0x5827, 0x5828, 0x5829, 0x582a, 0x582b, 0x582c, 0x582d, 0x582e,
  0x582f, 0x5830, 0x5831, 0x5832, 0x5833, 0x584c, 0x5834, 0x5835,
  0x5836, 0x5837, 0x5838, 0x5839, 0x583a, 0x583b, 0x583c, 0x583d,
  0x583f, 0x5840, 0x5841, 0x5842, 0x5843, 0x5844, 0x5845, 0x5846,
  0x5847, 0x5848, 0x5849, 0x584a, 0x584b, 0x584d, 0x584e, 0x584f,
  0x5850, 0x5851, 0x5852, 0x5853, 0x5854, 0x5855, 0x5856, 0x5857,
  0x5858, 0x5859, 0x585a, 0x585b, 0x585c, 0x585d, 0x585e, 0x585f,
  0x5860, 0x5861, 0x5862, 0x5863, 0x5864, 0x5865, 0x5866, 0x5867,
  0x5868, 0x5869, 0x586a, 0x586b, 0x586c, 0x586d, 0x586e, 0x586f,
  0x5870, 0x5871, 0x5872, 0x5873, 0x5874, 0x5875, 0x5876, 0x5877,
  0x5878, 0x5879, 0x587a, 0x587b, 0x587c, 0x587d, 0x587e, 0x5921,
  0x5922, 0x5923, 0x5924, 0x5925, 0x5926, 0x5927, 0x5928, 0x592a,
  0x592b, 0x592c, 0x592d, 0x592e, 0x592f, 0x5930, 0x5931, 0x5932,
  0x5933, 0x5934, 0x5935, 0x5936, 0x5937, 0x5938, 0x5939, 0x593a,
  0x593b, 0x593c, 0x5929, 0x593d, 0x593e, 0x593f, 0x5940, 0x5941,
  0x5942, 0x5943, 0x5944, 0x5945, 0x5946, 0x5947, 0x5948, 0x5949,
  0x594a, 0x594b, 0x594c, 0x594d, 0x594e, 0x594f, 0x5950, 0x5951,
  0x5952, 0x5953, 0x5954, 0x5955, 0x5956, 0x5957, 0x5958, 0x5959,
  0x595a, 0x595b, 0x595c, 0x595d, 0x595e, 0x595f, 0x5960, 0x5961,
  0x5962, 0x5963, 0x5964, 0x5965, 0x5966, 0x5974, 0x5967, 0x5968,
  0x5969, 0x596a, 0x596b, 0x596c, 0x596d, 0x596e, 0x596f, 0x5970,
  0x5971, 0x5972, 0x5973, 0x5975, 0x5976, 0x5977, 0x5978, 0x5979,
  0x597a, 0x597b, 0x597c, 0x597d, 0x597e, 0x5a21, 0x5a22, 0x5a23,
  0x5a24, 0x5a25, 0x5a26, 0x5a27, 0x5a28, 0x5a29, 0x5a2a, 0x5a2b,
  0x5a2c, 0x5a2d, 0x5a2e, 0x5a2f, 0x5a30, 0x5a31, 0x5a32, 0x5a33,
  0x5a34, 0x5a35, 0x5a36, 0x3866, 0x5a37, 0x5a38, 0x5a39, 0x5a3a,
  0x5a3b, 0x5a3c, 0x5a3d, 0x5a3e, 0x5a3f, 0x5a40, 0x5a41, 0x5a42,
  0x5a43, 0x5a44, 0x5a45, 0x5a46, 0x5a47, 0x5a48, 0x5a49, 0x5a4a,
  0x5a4b, 0x5a6d, 0x5a4c, 0x5a4d, 0x5a4e, 0x5a4f, 0x5a50, 0x5a51,
  0x5a52, 0x5a53, 0x5a54, 0x5a55, 0x5a56, 0x5a57, 0x5a58, 0x5a59,
  0x5a5a, 0x5a5b, 0x5a5c, 0x5a5d, 0x5a5e, 0x5a5f, 0x5a60, 0x5a61,
  0x5a62, 0x5a63, 0x5a64, 0x5a65, 0x5a66, 0x5a67, 0x5a68, 0x5a69,
  0x5a6a, 0x5a6b, 0x5a6c, 0x5a6e, 0x5a6f, 0x5a70, 0x5a71, 0x5a72,
  0x5a73, 0x5a74, 0x5a75, 0x5a76, 0x5a77, 0x5a78, 0x5a79, 0x5a7a,
  0x5a7b, 0x5a7c, 0x5a7d, 0x5a7e, 0x5b21, 0x5b22, 0x5b23, 0x5b24,
  0x5b25, 0x5b26, 0x5b27, 0x5b28, 0x5b29, 0x5b2a, 0x5b2b, 0x5b2c,
  0x5b2d, 0x5b2e, 0x5b2f, 0x5b30, 0x5b31, 0x5b32, 0x5b33, 0x5b34,
  0x5b35, 0x5b36, 0x5b37, 0x5b38, 0x5b39, 0x5b3a, 0x5b3b, 0x5b3c,
  0x5b3d, 0x5b3e, 0x5b3f, 0x5b40, 0x5b41, 0x5b42, 0x5b43, 0x5b44,
  0x5b45, 0x5b46, 0x5b47, 0x5b48, 0x5b49, 0x5b4a, 0x5b4b, 0x5b4c,
  0x5b4d, 0x5b4e, 0x5b4f, 0x5b50, 0x5b51, 0x5b52, 0x5b53, 0x5b54,
  0x5b55, 0x5b56, 0x5b57, 0x5b58, 0x5b59, 0x5b5a, 0x5b5b, 0x5b5c,
  0x5b5d, 0x5b5e, 0x5b5f, 0x5b60, 0x5b61, 0x5b62, 0x5b63, 0x5b64,
  0x5b65, 0x5b66, 0x5b67, 0x5b68, 0x5b69, 0x5b6a, 0x5b6b, 0x5b6c,
  0x5b6d, 0x5b6e, 0x5b70, 0x5b71, 0x5b72, 0x5b73, 0x5b6f, 0x5b74,
  0x5b75, 0x5b76, 0x5b77, 0x5b78, 0x5b79, 0x5b7a, 0x5b7b, 0x5b7c,
  0x5b7d, 0x5b7e, 0x5c21, 0x5c22, 0x5c23, 0x5c24, 0x5c25, 0x5c26,
  0x5c27, 0x5c28, 0x5c29, 0x5c2a, 0x5c2b, 0x5c2c, 0x5c2d, 0x5c2e,
  0x5c2f, 0x5c30, 0x5c31, 0x5c32, 0x5c33, 0x5c34, 0x5c35, 0x5c36,
  0x5c37, 0x5c38, 0x5c39, 0x5c3a, 0x5c3b, 0x5c3c, 0x5c3d, 0x5c3e,
  0x5c3f, 0x5c40, 0x5c41, 0x5c42, 0x5c43, 0x5c44, 0x5c45, 0x5c46,
  0x5c47, 0x5c48, 0x5c49, 0x5c4a, 0x5c4b, 0x5c4c, 0x5c4d, 0x5c4e,
  0x5c4f, 0x5c50, 0x5c51, 0x5c52, 0x5c53, 0x5c54, 0x5c55, 0x5c56,
  0x5c57, 0x5c58, 0x5c59, 0x5c5a, 0x5c5b, 0x5c5c, 0x5c5d, 0x5c5e,
  0x5c5f, 0x5c60, 0x5c61, 0x5c62, 0x5c63, 0x5c64, 0x5c65, 0x5c66,
  0x5c67, 0x5c68, 0x5c69, 0x5c6a, 0x5c6b, 0x5c6c, 0x5c6d, 0x5c6e,
  0x5c6f, 0x5c70, 0x5c71, 0x5c72, 0x5c73, 0x5c74, 0x5c75, 0x5c76,
  0x5c77, 0x5c78, 0x5c79, 0x5c7a, 0x5c7b, 0x5c7c, 0x5c7d, 0x5c7e,
  0x5d21, 0x5d22, 0x5d23, 0x5d24, 0x5d25, 0x5d26, 0x5d27, 0x5d28,
  0x5d29, 0x5d2a, 0x5d2b, 0x5d2c, 0x5d2d, 0x5d2e, 0x5d2f, 0x5d30,
  0x5d31, 0x5d32, 0x5d33, 0x5d34, 0x5d35, 0x5d36, 0x5d37, 0x5d38,
  0x5d39, 0x5d3a, 0x5d3b, 0x5d3c, 0x5d3d, 0x5d3e, 0x5d3f, 0x5d40,
  0x5d41, 0x5d42, 0x5d43, 0x5d44, 0x5d45, 0x5d46, 0x5d47, 0x5d48,
  0x5d49, 0x5d4a, 0x5d4b, 0x5d4c, 0x5d4d, 0x5d4e, 0x5d4f, 0x5d50,
  0x5d51, 0x5d52, 0x5d53, 0x5d54, 0x5d55, 0x5d56, 0x5d57, 0x5d58,
  0x5d59, 0x5d5a, 0x5d5b, 0x5d5c, 0x5d5d, 0x5d5e, 0x5d5f, 0x5d60,
  0x5d61, 0x5d62, 0x5d63, 0x5d64, 0x5d65, 0x5d66, 0x5d67, 0x5d68,
  0x5d69, 0x5d6a, 0x5d6b, 0x5d6c, 0x5d6d, 0x5d6e, 0x5d6f, 0x5d70,
  0x5d71, 0x5d72, 0x5d73, 0x5d74, 0x5d75, 0x5d76, 0x5d77, 0x5d78,
  0x5d79, 0x5d7a, 0x5d7b, 0x5d7c, 0x5d7d, 0x5d7e, 0x5e21, 0x5e22,
  0x5e23, 0x5e24, 0x5e25, 0x5e26, 0x5e27, 0x5e28, 0x5e29, 0x5e2a,
  0x5e2b, 0x5e2c, 0x5e2d, 0x5e2e, 0x5e2f, 0x5e30, 0x5e31, 0x5e32,
  0x5e33, 0x5e34, 0x5e35, 0x5e36, 0x5e37, 0x5e38, 0x5e39, 0x5e3f,
  0x5e3a, 0x5e3b, 0x5e3c, 0x5e3d, 0x5e3e, 0x5e40, 0x5e41, 0x5e42,
  0x5e43, 0x5e44, 0x5e45, 0x5e46, 0x5e47, 0x5e48, 0x5e49, 0x5e4e,
  0x5e4a, 0x5e4b, 0x5e4c, 0x5e4d, 0x5e4f, 0x5e50, 0x5e51, 0x5e52,
  0x5e53, 0x5e54, 0x5e55, 0x5e56, 0x5e57, 0x5e58, 0x5e59, 0x5e5a,
  0x5e5b, 0x5e5c, 0x5e5d, 0x5e5e, 0x5e5f, 0x5e60, 0x5e61, 0x5e62,
  0x5e63, 0x5e64, 0x5e65, 0x5e66, 0x5e67, 0x5e68, 0x5e69, 0x5e6a,
  0x5e6b, 0x5e6c, 0x5e6d, 0x5e6e, 0x5e6f, 0x5e72, 0x5e70, 0x5e71,
  0x5e73, 0x5e74, 0x5e75, 0x5e76, 0x5e77, 0x5e78, 0x5e79, 0x5e7a,
  0x5e7b, 0x5e7c, 0x5e7d, 0x5e7e, 0x5f21, 0x5f22, 0x5f23, 0x5f24,
  0x5f25, 0x5f26, 0x5f27, 0x5f28, 0x5f29, 0x5f2a, 0x5f2b, 0x5f2c,
  0x5f2d, 0x5f2e, 0x5f2f, 0x5f30, 0x5f32, 0x5f31, 0x5f33, 0x5f34,
  0x5f35, 0x5f36, 0x5f37, 0x5f38, 0x5f39, 0x5f3a, 0x5f3b, 0x5f3c,
  0x5f3d, 0x5f3e, 0x5f3f, 0x5f40, 0x5f41, 0x5f42, 0x5f43, 0x5f44,
  0x5f45, 0x5f46, 0x5f47, 0x5f48, 0x5f49, 0x5f4a, 0x5f4b, 0x5f4c,
  0x5f4d, 0x5f4e, 0x5f4f, 0x5f50, 0x5f51, 0x5f52, 0x5f53, 0x5f54,
  0x5f55, 0x5f56, 0x5f57, 0x5f58, 0x5f59, 0x5f5a, 0x5f5b, 0x5f5c,
  0x5f5d, 0x5f6f, 0x5f5e, 0x5f5f, 0x5f60, 0x5f61, 0x5f62, 0x5f63,
  0x5f64, 0x5f65, 0x5f66, 0x5f67, 0x5f68, 0x5f69, 0x5f6a, 0x5f6b,
  0x5f6c, 0x5f6d, 0x5f6e, 0x5f70, 0x5f71, 0x5f72, 0x5f73, 0x5f74,
  0x5f75, 0x5f76, 0x5f77, 0x5f78, 0x5f79, 0x5f7a, 0x5f7b, 0x5f7c,
  0x5f7d, 0x5f7e, 0x6021, 0x6022, 0x6023, 0x6024, 0x6025, 0x6026,
  0x6027, 0x6028, 0x6029, 0x602a, 0x602b, 0x602c, 0x602d, 0x602e,
  0x602f, 0x6030, 0x6031, 0x6032, 0x6033, 0x6034, 0x6035, 0x6036,
  0x6037, 0x6038, 0x6039, 0x603a, 0x603b, 0x603c, 0x603d, 0x603e,
  0x603f, 0x6040, 0x6041, 0x6042, 0x6043, 0x6044, 0x6045, 0x6046,
  0x6047, 0x6048, 0x6049, 0x604a, 0x604b, 0x604c, 0x604d, 0x604e,
  0x604f, 0x6050, 0x6051, 0x6052, 0x6053, 0x6054, 0x6055, 0x6056,
  0x6057, 0x6058, 0x6059, 0x605a, 0x605b, 0x605c, 0x605d, 0x6064,
  0x605e, 0x605f, 0x6060, 0x6061, 0x6062, 0x6063, 0x6065, 0x6066,
  0x6067, 0x6068, 0x6069, 0x606a, 0x606b, 0x606c, 0x606d, 0x606e,
  0x606f, 0x6070, 0x6071, 0x6072, 0x6073, 0x6074, 0x6075, 0x6076,
  0x6077, 0x6078, 0x6079, 0x607a, 0x607b, 0x607c, 0x607d, 0x607e,
  0x6121, 0x6122, 0x6123, 0x6124, 0x6125, 0x6126, 0x6127, 0x6128,
  0x6129, 0x612a, 0x612b, 0x612c, 0x612d, 0x612e, 0x612f, 0x6130,
  0x6131, 0x6132, 0x6133, 0x6134, 0x6135, 0x6136, 0x6137, 0x6138,
  0x6139, 0x613a, 0x613b, 0x613c, 0x613d, 0x613e, 0x613f, 0x6140,
  0x6141, 0x6142, 0x6143, 0x6144, 0x6145, 0x6146, 0x6147, 0x6148,
  0x6149, 0x614a, 0x614b, 0x614c, 0x614d, 0x614e, 0x614f, 0x6150,
  0x6151, 0x6152, 0x6154, 0x6155, 0x6156, 0x6153, 0x6157, 0x6158,
  0x6159, 0x615a, 0x615b, 0x615c, 0x615d, 0x615e, 0x615f, 0x6160,
  0x6161, 0x6162, 0x6163, 0x6164, 0x6165, 0x6166, 0x6167, 0x6168,
  0x6169, 0x616a, 0x616b, 0x616c, 0x616d, 0x616e, 0x616f, 0x6170,
  0x6171, 0x6172, 0x6173, 0x6174, 0x6175, 0x6176, 0x6177, 0x6178,
  0x6179, 0x617a, 0x617b, 0x617d, 0x617e, 0x6221, 0x6222, 0x6223,
  0x6224, 0x617c, 0x622d, 0x6225, 0x6226, 0x6227, 0x6228, 0x6229,
  0x622a, 0x622b, 0x622c, 0x622f, 0x6230, 0x6231, 0x6232, 0x622e,
  0x6233, 0x6234, 0x6235, 0x6236, 0x6237, 0x6238, 0x6239, 0x623a,
  0x623b, 0x623c, 0x623d, 0x623e, 0x623f, 0x6240, 0x6241, 0x6242,
  0x6243, 0x6245, 0x6246, 0x6244, 0x6247, 0x6248, 0x6249, 0x624a,
  0x624b, 0x624c, 0x624d, 0x624e, 0x624f, 0x6250, 0x6251, 0x6252,
  0x6253, 0x6254, 0x6255, 0x6256, 0x6257, 0x6258, 0x6259, 0x625a,
  0x625b, 0x625c, 0x625d, 0x625e, 0x625f, 0x6260, 0x6261, 0x6262,
  0x6263, 0x6264, 0x6265, 0x6266, 0x6267, 0x6268, 0x6269, 0x626a,
  0x626b, 0x626c, 0x626d, 0x626e, 0x626f, 0x6270, 0x6271, 0x6272,
  0x6273, 0x6274, 0x6275, 0x6276, 0x6277, 0x6278, 0x6279, 0x627a,
  0x627b, 0x627c, 0x627d, 0x627e, 0x6321, 0x6322, 0x6323, 0x6324,
  0x6325, 0x6326, 0x6327, 0x6328, 0x6329, 0x632a, 0x632b, 0x632c,
  0x632d, 0x632e, 0x632f, 0x6330, 0x6331, 0x6332, 0x6333, 0x6334,
  0x6335, 0x6336, 0x6337, 0x6338, 0x6339, 0x633a, 0x633b, 0x633c,
  0x633d, 0x633e, 0x633f, 0x6340, 0x6341, 0x6342, 0x6343, 0x6344,
  0x6345, 0x6346, 0x6347, 0x6348, 0x6349, 0x634a, 0x634b, 0x634c,
  0x634d, 0x634e, 0x634f, 0x6350, 0x6351, 0x6352, 0x6353, 0x6354,
  0x6355, 0x6356, 0x6357, 0x6358, 0x6359, 0x635a, 0x635b, 0x635c,
  0x635d, 0x635e, 0x635f, 0x6360, 0x6361, 0x6362, 0x6363, 0x6364,
  0x6365, 0x6366, 0x6367, 0x6368, 0x6369, 0x636a, 0x636b, 0x636c,
  0x636d, 0x636e, 0x636f, 0x6370, 0x6371, 0x6372, 0x6373, 0x6374,
  0x6375, 0x6376, 0x6377, 0x6378, 0x6379, 0x637a, 0x637b, 0x637c,
  0x637d, 0x637e, 0x6421, 0x6422, 0x6423, 0x6424, 0x6425, 0x6426,
  0x6427, 0x6428, 0x6429, 0x642a, 0x642b, 0x642c, 0x642d, 0x642e,
  0x642f, 0x6430, 0x6431, 0x6432, 0x6433, 0x6434, 0x6435, 0x6436,
  0x6437, 0x6438, 0x6439, 0x643a, 0x643b, 0x643c, 0x643d, 0x643e,
  0x643f, 0x6440, 0x6441, 0x6442, 0x6443, 0x6444, 0x6445, 0x6446,
  0x6447, 0x6448, 0x6449, 0x644a, 0x644b, 0x644c, 0x644d, 0x644e,
  0x644f, 0x6450, 0x6451, 0x6452, 0x6453, 0x6454, 0x6455, 0x6456,
  0x6457, 0x6458, 0x6459, 0x645a, 0x645b, 0x645c, 0x645d, 0x645e,
  0x645f, 0x6460, 0x6461, 0x6462, 0x6463, 0x6464, 0x6465, 0x6466,
  0x6467, 0x6468, 0x6469, 0x646a, 0x646b, 0x646c, 0x646d, 0x646e,
  0x646f, 0x6470, 0x6471, 0x6472, 0x6473, 0x6474, 0x6475, 0x6476,
  0x6477, 0x6478, 0x6479, 0x647a, 0x647b, 0x647c, 0x647d, 0x647e,
  0x6521, 0x6522, 0x6523, 0x6524, 0x6525, 0x6526, 0x6527, 0x6528,
  0x6529, 0x652a, 0x652b, 0x652c, 0x652d, 0x652e, 0x652f, 0x6530,
  0x6531, 0x6532, 0x6533, 0x6534, 0x6535, 0x653b, 0x6536, 0x6537,
  0x6538, 0x6539, 0x653a, 0x653c, 0x653d, 0x653e, 0x653f, 0x6540,
  0x6541, 0x6542, 0x6543, 0x6544, 0x6545, 0x6546, 0x6547, 0x6548,
  0x6549, 0x654a, 0x654b, 0x654c, 0x654d, 0x654f, 0x6550, 0x654e,
  0x6551, 0x6552, 0x6553, 0x6554, 0x6555, 0x6556, 0x6557, 0x6558,
  0x6559, 0x655a, 0x655b, 0x655c, 0x655d, 0x655e, 0x655f, 0x6560,
  0x6561, 0x6562, 0x6563, 0x6564, 0x6565, 0x6566, 0x6568, 0x6567,
  0x6569, 0x656a, 0x656b, 0x656c, 0x656d, 0x656e, 0x656f, 0x6570,
  0x6571, 0x6572, 0x6573, 0x6574, 0x6575, 0x6576, 0x6577, 0x6578,
  0x6579, 0x657a, 0x657c, 0x657b, 0x657d, 0x657e, 0x6621, 0x6622,
  0x6623, 0x6624, 0x6625, 0x6626, 0x6627, 0x6628, 0x6629, 0x662a,
  0x662b, 0x662c, 0x662d, 0x662e, 0x662f, 0x6630, 0x6631, 0x6632,
  0x6633, 0x6634, 0x6635, 0x6636, 0x6637, 0x6638, 0x6639, 0x663a,
  0x663b, 0x663c, 0x663d, 0x663e, 0x663f, 0x6640, 0x6641, 0x6642,
  0x6643, 0x6644, 0x6645, 0x6646, 0x6647, 0x6648, 0x6649, 0x664a,
  0x664b, 0x664c, 0x664d, 0x664e, 0x664f, 0x6650, 0x6651, 0x6652,
  0x6653, 0x6654, 0x6655, 0x6656, 0x6657, 0x6658, 0x6659, 0x665a,
  0x665b, 0x665c, 0x665d, 0x665e, 0x665f, 0x6660, 0x6661, 0x6662,
  0x6663, 0x6664, 0x6665, 0x6666, 0x6667, 0x6668, 0x6669, 0x666a,
  0x666b, 0x666c, 0x666d, 0x666e, 0x666f, 0x6670, 0x6671, 0x6672,
  0x6673, 0x6675, 0x6676, 0x6677, 0x6678, 0x6679, 0x667a, 0x667b,
  0x667c, 0x667d, 0x667e, 0x6721, 0x6722, 0x6723, 0x6724, 0x6725,
  0x6726, 0x6727, 0x6728, 0x6729, 0x672a, 0x672b, 0x672c, 0x672d,
  0x672e, 0x672f, 0x6730, 0x6731, 0x6732, 0x6733, 0x6734, 0x6735,
  0x6736, 0x6737, 0x6738, 0x6739, 0x673a, 0x673b, 0x673c, 0x673d,
  0x673e, 0x673f, 0x6740, 0x6741, 0x6742, 0x6743, 0x6744, 0x6745,
  0x6746, 0x6747, 0x6748, 0x6749, 0x674a, 0x674b, 0x674c, 0x674d,
  0x674e, 0x674f, 0x6750, 0x6751, 0x6752, 0x6753, 0x6754, 0x6755,
  0x6756, 0x6757, 0x6758, 0x6759, 0x675a, 0x675b, 0x675c, 0x675d,
  0x675e, 0x675f, 0x6760, 0x6761, 0x6762, 0x6763, 0x6764, 0x6765,
  0x6766, 0x676a, 0x6767, 0x6768, 0x6769, 0x676b, 0x676c, 0x676d,
  0x676e, 0x676f, 0x6770, 0x6771, 0x6772, 0x6773, 0x6774, 0x6776,
  0x6777, 0x6778, 0x6779, 0x6775, 0x677a, 0x677b, 0x677c, 0x677d,
  0x6828, 0x677e, 0x6821, 0x6822, 0x6823, 0x6824, 0x6825, 0x6826,
  0x6827, 0x6829, 0x682a, 0x682b, 0x682c, 0x682d, 0x682e, 0x682f,
  0x6830, 0x6831, 0x6832, 0x6833, 0x6834, 0x6835, 0x6836, 0x6837,
  0x6838, 0x6839, 0x683a, 0x683b, 0x683c, 0x683d, 0x683e, 0x683f,
  0x6840, 0x6841, 0x6842, 0x6843, 0x6844, 0x6845, 0x6846, 0x6847,
  0x6848, 0x6849, 0x684a, 0x684b, 0x684c, 0x684d, 0x684e, 0x684f,
  0x6850, 0x6851, 0x6852, 0x6853, 0x6854, 0x6855, 0x6856, 0x6857,
  0x6858, 0x6859, 0x685a, 0x685b, 0x685c, 0x685d, 0x685e, 0x685f,
  0x6860, 0x6861, 0x6862, 0x6863, 0x6864, 0x6865, 0x6866, 0x6867,
  0x6868, 0x6869, 0x686a, 0x686b, 0x686c, 0x686d, 0x686e, 0x686f,
  0x6870, 0x6871, 0x6872, 0x6873, 0x6874, 0x6875, 0x6876, 0x6877,
  0x6878, 0x6879, 0x687a, 0x687b, 0x687c, 0x687d, 0x687e, 0x6921,
  0x6922, 0x6923, 0x6924, 0x6925, 0x6926, 0x6927, 0x6928, 0x6929,
  0x692a, 0x692b, 0x692c, 0x692d, 0x692e, 0x692f, 0x6930, 0x6931,
  0x6932, 0x6933, 0x6934, 0x6935, 0x6936, 0x6937, 0x6938, 0x6939,
  0x693a, 0x693b, 0x693c, 0x693d, 0x693e, 0x693f, 0x6940, 0x6941,
  0x6942, 0x6943, 0x6944, 0x6945, 0x6946, 0x6947, 0x6948, 0x6949,
  0x694a, 0x694c, 0x694d, 0x694b, 0x694e, 0x694f, 0x6950, 0x6951,
  0x6952, 0x6953, 0x6954, 0x6955, 0x6956, 0x6957, 0x6958, 0x6959,
  0x695a, 0x695b, 0x695c, 0x695d, 0x695e, 0x695f, 0x6960, 0x6961,
  0x6962, 0x6963, 0x6964, 0x6965, 0x6966, 0x6967, 0x6968, 0x6969,
  0x696a, 0x696b, 0x696c, 0x696d, 0x696e, 0x696f, 0x6970, 0x6971,
  0x6972, 0x6973, 0x6974, 0x6975, 0x6976, 0x6977, 0x6978, 0x6979,
  0x697a, 0x697b, 0x697c, 0x697d, 0x697e, 0x6a21, 0x6a22, 0x6a23,
  0x6a24, 0x6a25, 0x6a26, 0x6a27, 0x6a28, 0x6a29, 0x6a2a, 0x6a2b,
  0x6a2c, 0x6a2d, 0x6a2e, 0x6a2f, 0x6a30, 0x6a31, 0x6a32, 0x6a33,
  0x6a34, 0x6a35, 0x6a36, 0x6a37, 0x6a38, 0x6a39, 0x6a3a, 0x6a3b,
  0x6a3c, 0x6a3d, 0x6a3e, 0x6a3f, 0x6a40, 0x6a41, 0x6a42, 0x6a43,
  0x6a44, 0x6a45, 0x6a46, 0x6a47, 0x6a48, 0x6a49, 0x6a4a, 0x6a4b,
  0x6a4c, 0x6a4d, 0x6a4e, 0x6a4f, 0x6a50, 0x6a51, 0x6a52, 0x6a53,
  0x6a54, 0x6a55, 0x6a56, 0x6a57, 0x6a58, 0x6a59, 0x6a5a, 0x6a5b,
  0x6a5c, 0x6a5d, 0x6a5e, 0x6a5f, 0x6a60, 0x6a61, 0x6a62, 0x6a63,
  0x6a64, 0x6a65, 0x6a66, 0x6a67, 0x6a68, 0x6a69, 0x6a6a, 0x6a6b,
  0x6a6c, 0x6a6d, 0x6a6e, 0x6a6f, 0x6a70, 0x6a71, 0x6a72, 0x6a73,
  0x6a74, 0x6a75, 0x6a76, 0x6a77, 0x6a78, 0x6a79, 0x6a7a, 0x6a7b,
  0x6a7c, 0x6a7d, 0x6a7e, 0x6b21, 0x6b22, 0x6b23, 0x6b24, 0x6b25,
  0x6b26, 0x6b27, 0x6b28, 0x6b29, 0x6b2a, 0x6b2b, 0x6b2c, 0x6b2d,
  0x6b2e, 0x6b2f, 0x6b30, 0x6b31, 0x6b32, 0x6b33, 0x6b34, 0x6b35,
  0x6b36, 0x6b37, 0x6b38, 0x6b39, 0x6b3a, 0x6b3b, 0x6b3c, 0x6b3d,
  0x6b3e, 0x6b3f, 0x6b40, 0x6b41, 0x6b42, 0x6b43, 0x6b44, 0x6b45,
  0x6b46, 0x6b47, 0x6b48, 0x6b49, 0x6b50, 0x6b4a, 0x6b4b, 0x6b4c,
  0x6b4d, 0x6b52, 0x6b4e, 0x6b4f, 0x6b51, 0x6b53, 0x6b54, 0x6b55,
  0x6b56, 0x6b57, 0x6b58, 0x6b59, 0x6b5a, 0x6b5b, 0x6b5c, 0x6b5e,
  0x6b5d, 0x6b5f, 0x6b60, 0x6b61, 0x6b62, 0x6b63, 0x6b64, 0x6b65,
  0x6b66, 0x6b67, 0x6b68, 0x6b69, 0x6b6a, 0x6b6b, 0x6b6d, 0x6b6e,
  0x6b6f, 0x6b6c, 0x6b70, 0x6b71, 0x6b72, 0x6b73, 0x6b74, 0x6b76,
  0x6b75, 0x6b77, 0x6b78, 0x6b79, 0x6b7a, 0x6b7b, 0x6b7c, 0x6b7d,
  0x6b7e, 0x6c21, 0x6c22, 0x6c23, 0x6c24, 0x6c25, 0x6c26, 0x6c27,
  0x6c28, 0x6c29, 0x6c2a, 0x6c2b, 0x6c2c, 0x6c2d, 0x6c2e, 0x6c2f,
  0x6c30, 0x6c31, 0x6c32, 0x6c33, 0x6c34, 0x6c35, 0x6c36, 0x6c37,
  0x6c38, 0x6c39, 0x6c3a, 0x6c3b, 0x6c3c, 0x6c3d, 0x6c3e, 0x6c3f,
  0x6c40, 0x6c41, 0x6c42, 0x6c43, 0x6c44, 0x6c45, 0x6c46, 0x6c47,
  0x6c48, 0x6c49, 0x6c4a, 0x6c4b, 0x6c4c, 0x6c4e, 0x6c4f, 0x6c4d,
  0x6c50, 0x6c51, 0x6c52, 0x6c53, 0x6c54, 0x6c55, 0x6c56, 0x6c57,
  0x6c58, 0x6c59, 0x6c5a, 0x6c5b, 0x6c5c, 0x6c5d, 0x6c5e, 0x6c5f,
  0x6c60, 0x6c61, 0x6c62, 0x6c63, 0x6c64, 0x6c65, 0x6c66, 0x6c67,
  0x6c68, 0x6c69, 0x6c6a, 0x6c6b, 0x6c6c, 0x6c6d, 0x6c6e, 0x6c6f,
  0x6c70, 0x6c71, 0x6c72, 0x6c73, 0x6c74, 0x6c75, 0x6c76, 0x6c77,
  0x6c78, 0x6c79, 0x6c7a, 0x6c7b, 0x6c7c, 0x6c7d, 0x6c7e, 0x6d21,
  0x6d22, 0x6d23, 0x6d24, 0x6d25, 0x6d26, 0x6d27, 0x6d28, 0x6d29,
  0x6d2a, 0x6d2b, 0x6d2c, 0x6d2d, 0x6d2e, 0x6d2f, 0x6d30, 0x6d31,
  0x6d32, 0x6d33, 0x6d34, 0x6d35, 0x6d36, 0x6d37, 0x6d38, 0x6d39,
  0x6d3a, 0x6d3b, 0x6d3c, 0x6d3d, 0x6d3e, 0x6d3f, 0x6d40, 0x6d41,
  0x6d42, 0x6d43, 0x6d44, 0x6d45, 0x6d46, 0x6d47, 0x6d48, 0x6d49,
  0x6d4a, 0x6d4b, 0x6d4c, 0x6d4d, 0x6d4e, 0x6d4f, 0x6d50, 0x6d51,
  0x6d52, 0x6d53, 0x6d54, 0x6d55, 0x6d56, 0x6d57, 0x6d58, 0x6d59,
  0x6d5a, 0x6d5b, 0x6d5c, 0x6d5d, 0x6d5e, 0x6d5f, 0x6d60, 0x6d61,
  0x6d62, 0x6d63, 0x2237,
};

static const Summary16 jisx0212_uni2indx_page00[70] = {
  /* 0x0000 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0x0000 },
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0xc652 }, {    7, 0x8500 },
  {   10, 0xffff }, {   26, 0xff7e }, {   40, 0xffff }, {   56, 0xff7f },
  /* 0x0100 */
  {   71, 0xffff }, {   87, 0xffcf }, {  101, 0xcff7 }, {  114, 0xffff },
  {  130, 0x3fff }, {  144, 0xffff }, {  160, 0xffff }, {  176, 0x7fff },
  {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 }, {  191, 0x0000 },
  {  191, 0xe000 }, {  194, 0x1fff }, {  207, 0x0000 }, {  207, 0x0020 },
  /* 0x0200 */
  {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 },
  {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 },
  {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 }, {  208, 0x0000 },
  {  208, 0x0080 }, {  209, 0x2f00 }, {  214, 0x0000 }, {  214, 0x0000 },
  /* 0x0300 */
  {  214, 0x0000 }, {  214, 0x0000 }, {  214, 0x0000 }, {  214, 0x0000 },
  {  214, 0x0000 }, {  214, 0x0000 }, {  214, 0x0000 }, {  214, 0x0000 },
  {  214, 0xd770 }, {  223, 0x0001 }, {  224, 0xfc00 }, {  230, 0x0001 },
  {  231, 0x7c04 }, {  237, 0x0000 }, {  237, 0x0000 }, {  237, 0x0000 },
  /* 0x0400 */
  {  237, 0xdffc }, {  250, 0x0000 }, {  250, 0x0000 }, {  250, 0x0000 },
  {  250, 0x0000 }, {  250, 0xdffc },
};
static const Summary16 jisx0212_uni2indx_page21[3] = {
  /* 0x2100 */
  {  263, 0x0000 }, {  263, 0x0040 }, {  264, 0x0004 },
};
static const Summary16 jisx0212_uni2indx_page4e[1307] = {
  /* 0x4e00 */
  {  265, 0x1034 }, {  269, 0x8004 }, {  271, 0xc918 }, {  277, 0x0021 },
  {  279, 0x0093 }, {  283, 0x1402 }, {  286, 0x0308 }, {  289, 0x8230 },
  {  293, 0x2000 }, {  294, 0x20c0 }, {  297, 0x8000 }, {  298, 0x0200 },
  {  299, 0x0008 }, {  300, 0x0c01 }, {  303, 0x8107 }, {  308, 0xe02a },
  /* 0x4f00 */
  {  314, 0x190d }, {  320, 0x02e4 }, {  325, 0x4000 }, {  326, 0x4aaa },
  {  333, 0x1b05 }, {  339, 0x8154 }, {  344, 0x5409 }, {  349, 0x6782 },
  {  356, 0x5636 }, {  364, 0xc69d }, {  373, 0x0000 }, {  373, 0x7a84 },
  {  380, 0xbb63 }, {  390, 0x1004 }, {  392, 0x0005 }, {  394, 0xb005 },
  /* 0x5000 */
  {  399, 0x5493 }, {  406, 0x7989 }, {  414, 0x4084 }, {  417, 0x082d },
  {  422, 0x5467 }, {  430, 0x828e }, {  436, 0x24cd }, {  443, 0x0003 },
  {  445, 0xc45a }, {  452, 0xd85d }, {  461, 0x8407 }, {  466, 0x2601 },
  {  470, 0x5099 }, {  476, 0xb119 }, {  483, 0x8354 }, {  489, 0x4446 },
  /* 0x5100 */
  {  494, 0x79c8 }, {  502, 0x7a81 }, {  509, 0xb188 }, {  515, 0x033a },
  {  521, 0x8404 }, {  524, 0x81a8 }, {  529, 0x0050 }, {  531, 0x4000 },
  {  532, 0x4818 }, {  536, 0x2100 }, {  538, 0x200a }, {  541, 0xd500 },
  {  546, 0x8104 }, {  549, 0x412e }, {  555, 0x4024 }, {  558, 0x009c },
  /* 0x5200 */
  {  562, 0x0026 }, {  565, 0x016c }, {  570, 0x0104 }, {  572, 0x1026 },
  {  576, 0x0220 }, {  578, 0x95a0 }, {  584, 0x4043 }, {  588, 0x0380 },
  {  591, 0x1425 }, {  596, 0x15e8 }, {  603, 0x80f0 }, {  608, 0x2dc1 },
  {  615, 0x9151 }, {  621, 0x1852 }, {  626, 0x1722 }, {  632, 0x00d3 },
  /* 0x5300 */
  {  637, 0x1c09 }, {  642, 0xd90a }, {  649, 0x3ba0 }, {  656, 0x7025 },
  {  662, 0x1804 }, {  665, 0x0a00 }, {  667, 0x302a }, {  672, 0x4204 },
  {  675, 0x4188 }, {  679, 0x2218 }, {  683, 0x8c12 }, {  688, 0x25b4 },
  {  695, 0x8021 }, {  698, 0x642c }, {  704, 0x00c1 }, {  707, 0x0020 },
  /* 0x5400 */
  {  708, 0x0004 }, {  709, 0x0408 }, {  711, 0x8582 }, {  716, 0x0032 },
  {  719, 0xa098 }, {  724, 0x4000 }, {  725, 0x6ad4 }, {  733, 0x8010 },
  {  735, 0x232a }, {  741, 0x9062 }, {  746, 0x66c2 }, {  753, 0x8e82 },
  {  759, 0x6440 }, {  763, 0x0000 }, {  763, 0x9401 }, {  767, 0xd040 },
  /* 0x5500 */
  {  771, 0x7323 }, {  779, 0x0020 }, {  780, 0x0c00 }, {  782, 0x3864 },
  {  788, 0x2682 }, {  793, 0x4d03 }, {  799, 0x0053 }, {  803, 0x8000 },
  {  804, 0xc146 }, {  810, 0x009e }, {  815, 0x2018 }, {  818, 0x8004 },
  {  820, 0x5a4a }, {  827, 0x498e }, {  834, 0x0204 }, {  836, 0x8040 },
  /* 0x5600 */
  {  838, 0xe520 }, {  844, 0x0207 }, {  848, 0x1000 }, {  849, 0xbaa9 },
  {  858, 0xaa5b }, {  867, 0x4010 }, {  869, 0xa24f }, {  877, 0x0026 },
  {  880, 0x1930 }, {  885, 0xe620 }, {  891, 0x3bc0 }, {  898, 0x408a },
  {  902, 0xbe20 }, {  909, 0xb201 }, {  914, 0x29f2 }, {  922, 0x00c2 },
  /* 0x5700 */
  {  925, 0x1486 }, {  930, 0x2c22 }, {  935, 0xd63d }, {  945, 0xe018 },
  {  950, 0x3060 }, {  954, 0x0004 }, {  955, 0xe9a4 }, {  963, 0x5ebb },
  {  974, 0x100a }, {  977, 0xf6b0 }, {  986, 0x1382 }, {  991, 0x2100 },
  {  993, 0x9180 }, {  997, 0x6020 }, { 1000, 0x22d2 }, { 1006, 0xe161 },
  /* 0x5800 */
  { 1013, 0x3318 }, { 1019, 0xc800 }, { 1022, 0x20c1 }, { 1026, 0x8204 },
  { 1029, 0xb200 }, { 1033, 0x8021 }, { 1036, 0x0192 }, { 1040, 0x9100 },
  { 1043, 0xb783 }, { 1052, 0x2051 }, { 1056, 0x0247 }, { 1061, 0x1006 },
  { 1064, 0x6114 }, { 1069, 0x2455 }, { 1075, 0x0206 }, { 1078, 0x0008 },
  /* 0x5900 */
  { 1079, 0x1860 }, { 1083, 0x201c }, { 1087, 0x811a }, { 1092, 0x8069 },
  { 1097, 0x0048 }, { 1099, 0xea0c }, { 1106, 0xa80a }, { 1111, 0x1a64 },
  { 1117, 0x5800 }, { 1120, 0x80a4 }, { 1124, 0xe090 }, { 1129, 0x1489 },
  { 1134, 0x251a }, { 1140, 0xe004 }, { 1144, 0xc098 }, { 1149, 0x0096 },
  /* 0x5a00 */
  { 1153, 0x7011 }, { 1158, 0x400c }, { 1161, 0x2598 }, { 1167, 0x0001 },
  { 1168, 0x11b0 }, { 1173, 0x4021 }, { 1176, 0x20a8 }, { 1180, 0x4c80 },
  { 1184, 0x0800 }, { 1185, 0xd249 }, { 1192, 0x1085 }, { 1196, 0x8d2e },
  { 1204, 0x8150 }, { 1208, 0x1400 }, { 1210, 0x4421 }, { 1214, 0x2060 },
  /* 0x5b00 */
  { 1217, 0x0103 }, { 1220, 0x2a80 }, { 1224, 0x2022 }, { 1227, 0x0110 },
  { 1229, 0x1802 }, { 1232, 0x4044 }, { 1235, 0xc100 }, { 1238, 0xf000 },
  { 1242, 0x4452 }, { 1247, 0x005b }, { 1252, 0xb300 }, { 1257, 0x1486 },
  { 1262, 0xa003 }, { 1266, 0x07c0 }, { 1271, 0x8001 }, { 1273, 0x2012 },
  /* 0x5c00 */
  { 1276, 0x1000 }, { 1277, 0xc080 }, { 1280, 0x5a48 }, { 1286, 0x0065 },
  { 1290, 0x0000 }, { 1290, 0x1600 }, { 1293, 0x238c }, { 1299, 0x3c31 },
  { 1306, 0x8580 }, { 1310, 0xa004 }, { 1313, 0x044d }, { 1318, 0x0434 },
  { 1322, 0x0a00 }, { 1324, 0x2084 }, { 1327, 0x4000 }, { 1328, 0x0016 },
  /* 0x5d00 */
  { 1331, 0x2042 }, { 1334, 0x0004 }, { 1335, 0x08d8 }, { 1340, 0xa212 },
  { 1345, 0x054c }, { 1350, 0x8222 }, { 1354, 0x2417 }, { 1360, 0xc601 },
  { 1365, 0x050a }, { 1369, 0x8a3c }, { 1376, 0x0881 }, { 1379, 0x0315 },
  { 1384, 0x4888 }, { 1388, 0x0301 }, { 1391, 0x0211 }, { 1394, 0x0300 },
  /* 0x5e00 */
  { 1396, 0x2081 }, { 1399, 0x8134 }, { 1404, 0x4101 }, { 1407, 0x4024 },
  { 1410, 0x0a00 }, { 1412, 0x5943 }, { 1419, 0x7d00 }, { 1425, 0x0001 },
  { 1426, 0x4801 }, { 1429, 0x0000 }, { 1429, 0x1534 }, { 1435, 0xe00a },
  { 1440, 0x5840 }, { 1444, 0x5036 }, { 1450, 0x0820 }, { 1452, 0x0000 },
  /* 0x5f00 */
  { 1452, 0x41c4 }, { 1457, 0x3200 }, { 1460, 0x591e }, { 1468, 0xa851 },
  { 1474, 0x20b1 }, { 1479, 0x0911 }, { 1483, 0x8099 }, { 1488, 0x6534 },
  { 1495, 0xa200 }, { 1498, 0x3040 }, { 1501, 0x9894 }, { 1507, 0x0103 },
  { 1510, 0x0b90 }, { 1515, 0x401f }, { 1521, 0xf706 }, { 1530, 0x144c },
  /* 0x6000 */
  { 1535, 0x2480 }, { 1538, 0x8598 }, { 1544, 0x2010 }, { 1546, 0x0028 },
  { 1548, 0x1381 }, { 1553, 0x20d2 }, { 1558, 0x0082 }, { 1560, 0xc002 },
  { 1563, 0x4544 }, { 1568, 0x612a }, { 1574, 0x0134 }, { 1578, 0x4883 },
  { 1583, 0xcf14 }, { 1591, 0x6a30 }, { 1597, 0x0024 }, { 1599, 0x3124 },
  /* 0x6100 */
  { 1604, 0x1484 }, { 1608, 0x52df }, { 1618, 0x0c04 }, { 1621, 0x02e3 },
  { 1627, 0x0262 }, { 1631, 0x4000 }, { 1632, 0x1001 }, { 1634, 0x9904 },
  { 1639, 0x281b }, { 1645, 0xb18c }, { 1652, 0x2521 }, { 1657, 0x1300 },
  { 1660, 0xc007 }, { 1665, 0xf020 }, { 1670, 0xb2a6 }, { 1678, 0x0000 },
  /* 0x6200 */
  { 1678, 0x009a }, { 1682, 0x1028 }, { 1685, 0x0a8d }, { 1691, 0x2200 },
  { 1693, 0x105c }, { 1698, 0x1457 }, { 1705, 0xa010 }, { 1708, 0x2408 },
  { 1711, 0xe000 }, { 1714, 0x0001 }, { 1715, 0x0140 }, { 1717, 0xc4c8 },
  { 1723, 0x4010 }, { 1725, 0x0460 }, { 1728, 0x0400 }, { 1729, 0x3014 },
  /* 0x6300 */
  { 1733, 0x2c18 }, { 1738, 0x0149 }, { 1742, 0x2600 }, { 1745, 0x1260 },
  { 1749, 0x4c5e }, { 1757, 0x091c }, { 1762, 0x3060 }, { 1766, 0xb132 },
  { 1773, 0x0494 }, { 1777, 0x4631 }, { 1783, 0xe050 }, { 1788, 0x2000 },
  { 1789, 0x4122 }, { 1793, 0x103a }, { 1798, 0x1421 }, { 1802, 0x032c },
  /* 0x6400 */
  { 1807, 0x0600 }, { 1809, 0x4115 }, { 1814, 0x8635 }, { 1821, 0xa021 },
  { 1825, 0x8800 }, { 1827, 0xbc1e }, { 1836, 0x200b }, { 1840, 0x2818 },
  { 1844, 0x80a0 }, { 1847, 0xab03 }, { 1854, 0x114a }, { 1859, 0xe008 },
  { 1863, 0x5e10 }, { 1869, 0x00a3 }, { 1873, 0x2630 }, { 1878, 0x88a1 },
  /* 0x6500 */
  { 1883, 0x8712 }, { 1889, 0xca58 }, { 1896, 0x4244 }, { 1900, 0x3402 },
  { 1904, 0x0288 }, { 1907, 0x8015 }, { 1911, 0x0881 }, { 1914, 0x2400 },
  { 1916, 0x0422 }, { 1919, 0x2124 }, { 1923, 0x4049 }, { 1927, 0x801c },
  { 1931, 0x4304 }, { 1935, 0x8151 }, { 1940, 0x0000 }, { 1940, 0xc235 },
  /* 0x6600 */
  { 1947, 0x2311 }, { 1952, 0x6066 }, { 1958, 0x5e5e }, { 1968, 0x028b },
  { 1973, 0x5461 }, { 1979, 0x1b82 }, { 1985, 0x1c03 }, { 1990, 0xdba8 },
  { 1999, 0x3801 }, { 2003, 0x9e05 }, { 2010, 0x2011 }, { 2013, 0x8826 },
  { 2018, 0xd10d }, { 2025, 0x8810 }, { 2028, 0x5900 }, { 2032, 0x0c00 },
  /* 0x6700 */
  { 2034, 0x40a0 }, { 2037, 0x1208 }, { 2040, 0x0005 }, { 2042, 0x4008 },
  { 2044, 0x11a0 }, { 2048, 0x2030 }, { 2051, 0x5040 }, { 2054, 0x0850 },
  { 2057, 0xc012 }, { 2061, 0x0b4a }, { 2067, 0x0000 }, { 2067, 0x3827 },
  { 2074, 0x032d }, { 2080, 0x1284 }, { 2084, 0x0042 }, { 2086, 0x02c5 },
  /* 0x6800 */
  { 2091, 0x0000 }, { 2091, 0xa210 }, { 2095, 0xb180 }, { 2100, 0x880b },
  { 2105, 0x1430 }, { 2109, 0x09a4 }, { 2114, 0xc800 }, { 2117, 0x1e27 },
  { 2125, 0x0154 }, { 2129, 0x1540 }, { 2133, 0x462a }, { 2139, 0x0804 },
  { 2141, 0x9120 }, { 2145, 0x324b }, { 2152, 0x3d20 }, { 2158, 0x3863 },
  /* 0x6900 */
  { 2165, 0x0640 }, { 2168, 0x00cb }, { 2173, 0x0000 }, { 2173, 0x092a },
  { 2178, 0x4224 }, { 2182, 0x0880 }, { 2184, 0x1378 }, { 2191, 0x8c07 },
  { 2197, 0x2001 }, { 2199, 0x0144 }, { 2202, 0xa962 }, { 2209, 0x1580 },
  { 2213, 0x0120 }, { 2215, 0x00c2 }, { 2218, 0xc024 }, { 2222, 0x402a },
  /* 0x6a00 */
  { 2226, 0x800b }, { 2230, 0x2422 }, { 2234, 0x0111 }, { 2237, 0xc895 },
  { 2244, 0x4660 }, { 2249, 0x0867 }, { 2255, 0x0490 }, { 2258, 0x400a },
  { 2261, 0x0aca }, { 2267, 0xe802 }, { 2272, 0x8820 }, { 2275, 0xe013 },
  { 2281, 0x1340 }, { 2285, 0x3071 }, { 2291, 0x1090 }, { 2294, 0x3007 },
  /* 0x6b00 */
  { 2299, 0x82cc }, { 2305, 0x4883 }, { 2310, 0x9910 }, { 2315, 0x8860 },
  { 2319, 0x2440 }, { 2322, 0x2144 }, { 2326, 0x4881 }, { 2330, 0x6021 },
  { 2334, 0x0024 }, { 2336, 0x8880 }, { 2339, 0x730d }, { 2347, 0x6301 },
  { 2352, 0x1218 }, { 2356, 0x0440 }, { 2358, 0x40ca }, { 2363, 0x8282 },
  /* 0x6c00 */
  { 2367, 0x6234 }, { 2373, 0x8205 }, { 2377, 0x51c0 }, { 2382, 0x8c68 },
  { 2388, 0xac00 }, { 2392, 0x1a14 }, { 2397, 0xa880 }, { 2401, 0x0b50 },
  { 2406, 0x02e0 }, { 2410, 0x91b0 }, { 2416, 0x0000 }, { 2416, 0x0015 },
  { 2419, 0xa044 }, { 2423, 0x1457 }, { 2430, 0x5a81 }, { 2436, 0x0014 },
  /* 0x6d00 */
  { 2438, 0xc490 }, { 2443, 0x040a }, { 2446, 0xc1c0 }, { 2451, 0x9202 },
  { 2455, 0x0000 }, { 2455, 0xc080 }, { 2458, 0x80a2 }, { 2462, 0x1001 },
  { 2464, 0x0084 }, { 2466, 0x01d6 }, { 2472, 0x1400 }, { 2474, 0xa290 },
  { 2479, 0xc510 }, { 2484, 0xa840 }, { 2488, 0x8225 }, { 2493, 0x1051 },
  /* 0x6e00 */
  { 2497, 0x0011 }, { 2499, 0x4000 }, { 2500, 0x0084 }, { 2502, 0x1a44 },
  { 2507, 0x8b30 }, { 2513, 0x709e }, { 2521, 0x010c }, { 2524, 0x2808 },
  { 2527, 0x2000 }, { 2528, 0x0208 }, { 2530, 0x6081 }, { 2534, 0x880a },
  { 2538, 0xe58b }, { 2547, 0x0000 }, { 2547, 0x6800 }, { 2550, 0x2a00 },
  /* 0x6f00 */
  { 2553, 0x3510 }, { 2558, 0x0d40 }, { 2562, 0xa640 }, { 2567, 0x1849 },
  { 2572, 0x8000 }, { 2573, 0x668e }, { 2581, 0x1106 }, { 2585, 0x6000 },
  { 2587, 0x3988 }, { 2593, 0x845d }, { 2600, 0xc1e1 }, { 2607, 0x1061 },
  { 2611, 0x05a0 }, { 2615, 0x4400 }, { 2617, 0x0300 }, { 2619, 0x3221 },
  /* 0x7000 */
  { 2624, 0x20e1 }, { 2629, 0x0080 }, { 2630, 0x8009 }, { 2633, 0x1290 },
  { 2637, 0x4f18 }, { 2644, 0x6030 }, { 2648, 0x5030 }, { 2652, 0x4060 },
  { 2655, 0x0062 }, { 2658, 0x09f0 }, { 2664, 0x0810 }, { 2666, 0x0093 },
  { 2670, 0x0400 }, { 2671, 0x117a }, { 2678, 0x0010 }, { 2679, 0x0400 },
  /* 0x7100 */
  { 2680, 0x98f8 }, { 2688, 0x4000 }, { 2689, 0xa801 }, { 2693, 0x0103 },
  { 2696, 0x0ce2 }, { 2702, 0x5485 }, { 2708, 0x0101 }, { 2710, 0x0200 },
  { 2711, 0x10a1 }, { 2715, 0x0c04 }, { 2718, 0x8005 }, { 2721, 0x840d },
  { 2726, 0x1813 }, { 2731, 0x1648 }, { 2736, 0x0000 }, { 2736, 0x4100 },
  /* 0x7200 */
  { 2738, 0x0381 }, { 2742, 0xa488 }, { 2747, 0x8810 }, { 2750, 0x0310 },
  { 2753, 0xc02e }, { 2759, 0x5469 }, { 2766, 0xc909 }, { 2772, 0x9982 },
  { 2778, 0x6210 }, { 2782, 0x0808 }, { 2784, 0x6100 }, { 2787, 0x4012 },
  { 2790, 0x1282 }, { 2794, 0x8160 }, { 2798, 0x0020 }, { 2799, 0x4c18 },
  /* 0x7300 */
  { 2804, 0x28b4 }, { 2810, 0x430c }, { 2815, 0x1194 }, { 2820, 0x2c26 },
  { 2826, 0x2008 }, { 2828, 0xe145 }, { 2835, 0xdac1 }, { 2843, 0x1282 },
  { 2847, 0x406b }, { 2853, 0xd1a9 }, { 2861, 0x2c65 }, { 2868, 0xb2a0 },
  { 2874, 0x9a60 }, { 2880, 0x224c }, { 2885, 0x02ca }, { 2890, 0xaeb0 },
  /* 0x7400 */
  { 2898, 0x0493 }, { 2903, 0x0c02 }, { 2906, 0xff50 }, { 2916, 0x0203 },
  { 2919, 0x28d9 }, { 2926, 0x2086 }, { 2930, 0x69c4 }, { 2937, 0x0006 },
  { 2939, 0x82e3 }, { 2946, 0x9707 }, { 2954, 0xcf4b }, { 2964, 0x8a26 },
  { 2970, 0x1300 }, { 2973, 0xcd09 }, { 2980, 0x8d10 }, { 2985, 0x9c10 },
  /* 0x7500 */
  { 2990, 0x0040 }, { 2991, 0x00c4 }, { 2994, 0x8693 }, { 3001, 0xe240 },
  { 3006, 0x4189 }, { 3011, 0xc085 }, { 3016, 0x8002 }, { 3018, 0x7e02 },
  { 3025, 0x0022 }, { 3027, 0x122d }, { 3033, 0x0014 }, { 3035, 0x8410 },
  { 3038, 0xd053 }, { 3045, 0x9080 }, { 3048, 0xd093 }, { 3055, 0x0202 },
  /* 0x7600 */
  { 3057, 0x959d }, { 3066, 0x7a6c }, { 3075, 0x2268 }, { 3080, 0x172c },
  { 3087, 0x0e3b }, { 3095, 0x8220 }, { 3098, 0xe030 }, { 3103, 0x0012 },
  { 3105, 0x3022 }, { 3109, 0xb820 }, { 3114, 0x25fd }, { 3124, 0x2000 },
  { 3125, 0x5a22 }, { 3131, 0x0210 }, { 3133, 0x1141 }, { 3137, 0x1243 },
  /* 0x7700 */
  { 3142, 0x4441 }, { 3146, 0x16b4 }, { 3153, 0xe104 }, { 3158, 0x6270 },
  { 3164, 0xe464 }, { 3171, 0xd0c4 }, { 3177, 0x1495 }, { 3183, 0x241d },
  { 3189, 0x3011 }, { 3193, 0x8470 }, { 3198, 0xc484 }, { 3203, 0x4022 },
  { 3206, 0x0208 }, { 3208, 0xc226 }, { 3214, 0x1451 }, { 3219, 0x0913 },
  /* 0x7800 */
  { 3224, 0x6260 }, { 3229, 0x2002 }, { 3231, 0x600e }, { 3236, 0x00a1 },
  { 3239, 0x5198 }, { 3245, 0x5004 }, { 3248, 0x451b }, { 3255, 0x4400 },
  { 3257, 0x8400 }, { 3259, 0xe110 }, { 3264, 0x3112 }, { 3269, 0xa80f },
  { 3276, 0x5380 }, { 3281, 0x886c }, { 3287, 0x0453 }, { 3292, 0x8ccc },
  /* 0x7900 */
  { 3299, 0x1041 }, { 3302, 0xd401 }, { 3307, 0x22a1 }, { 3312, 0xa832 },
  { 3318, 0x8c70 }, { 3324, 0x1912 }, { 3329, 0x0a80 }, { 3332, 0x5a04 },
  { 3337, 0x1800 }, { 3339, 0x197a }, { 3347, 0x8b02 }, { 3352, 0x0912 },
  { 3356, 0x8594 }, { 3362, 0x6450 }, { 3367, 0x2c25 }, { 3373, 0x1102 },
  /* 0x7a00 */
  { 3376, 0x168c }, { 3382, 0x4822 }, { 3386, 0xa882 }, { 3391, 0x0731 },
  { 3397, 0x11b0 }, { 3402, 0xb260 }, { 3408, 0x24a1 }, { 3413, 0x4120 },
  { 3416, 0x0c65 }, { 3422, 0x4013 }, { 3426, 0x1009 }, { 3429, 0x1a28 },
  { 3434, 0x5240 }, { 3438, 0x0802 }, { 3440, 0x1b00 }, { 3444, 0x6812 },
  /* 0x7b00 */
  { 3449, 0x0080 }, { 3450, 0x8010 }, { 3452, 0xee88 }, { 3460, 0xa013 },
  { 3465, 0x4083 }, { 3469, 0x0020 }, { 3470, 0xa651 }, { 3477, 0x008c },
  { 3480, 0x4210 }, { 3483, 0x4843 }, { 3488, 0x9021 }, { 3492, 0x3c65 },
  { 3500, 0x0524 }, { 3504, 0x0ed0 }, { 3510, 0x0500 }, { 3512, 0x5734 },
  /* 0x7c00 */
  { 3520, 0xda5e }, { 3530, 0x0a00 }, { 3532, 0x1161 }, { 3537, 0x065a },
  { 3543, 0x0440 }, { 3545, 0x7e2e }, { 3555, 0x628a }, { 3561, 0x3205 },
  { 3566, 0x80c0 }, { 3569, 0x4010 }, { 3571, 0x0041 }, { 3573, 0x9cc1 },
  { 3580, 0xa390 }, { 3586, 0x26b8 }, { 3593, 0x0a40 }, { 3596, 0x0020 },
  /* 0x7d00 */
  { 3597, 0x8388 }, { 3602, 0x604e }, { 3608, 0x2448 }, { 3612, 0x7002 },
  { 3616, 0x2183 }, { 3621, 0x368a }, { 3628, 0x04a0 }, { 3631, 0x8d01 },
  { 3636, 0x396e }, { 3645, 0x60c2 }, { 3650, 0x04c0 }, { 3653, 0x02c8 },
  { 3657, 0x707c }, { 3665, 0x0280 }, { 3667, 0x2c64 }, { 3673, 0x0662 },
  /* 0x7e00 */
  { 3678, 0x0101 }, { 3680, 0x30a3 }, { 3686, 0xb181 }, { 3692, 0x8048 },
  { 3695, 0x40b0 }, { 3699, 0x8105 }, { 3703, 0xc826 }, { 3709, 0x4108 },
  { 3712, 0x24c2 }, { 3717, 0x6522 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 },
  /* 0x7f00 */
  { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0x0000 }, { 3723, 0xf800 },
  { 3728, 0x8098 }, { 3732, 0x380c }, { 3737, 0x207a }, { 3743, 0xe002 },
  { 3747, 0xa801 }, { 3751, 0x10c3 }, { 3756, 0x2446 }, { 3761, 0x9010 },
  { 3764, 0xc109 }, { 3769, 0x8800 }, { 3771, 0xd128 }, { 3777, 0xe404 },
  /* 0x8000 */
  { 3782, 0xe580 }, { 3788, 0xe05a }, { 3795, 0x5051 }, { 3800, 0x56b1 },
  { 3808, 0x0011 }, { 3810, 0x0000 }, { 3810, 0x2051 }, { 3814, 0x0022 },
  { 3816, 0x4102 }, { 3819, 0x5000 }, { 3821, 0x08c0 }, { 3824, 0x0300 },
  { 3826, 0xa100 }, { 3829, 0x01b4 }, { 3834, 0x6001 }, { 3837, 0x464d },
  /* 0x8100 */
  { 3844, 0x0808 }, { 3846, 0x51c0 }, { 3851, 0x1091 }, { 3855, 0x1421 },
  { 3859, 0x14a0 }, { 3863, 0x0084 }, { 3865, 0xa383 }, { 3872, 0x0080 },
  { 3873, 0x4872 }, { 3879, 0x4941 }, { 3884, 0x4004 }, { 3886, 0x0814 },
  { 3889, 0xcc28 }, { 3895, 0x68a0 }, { 3900, 0x1812 }, { 3904, 0xa367 },
  /* 0x8200 */
  { 3913, 0x8009 }, { 3916, 0x2618 }, { 3921, 0x0106 }, { 3924, 0x0414 },
  { 3927, 0xc878 }, { 3934, 0x1042 }, { 3937, 0x2089 }, { 3941, 0xa810 },
  { 3945, 0x469b }, { 3953, 0x0d52 }, { 3959, 0x479b }, { 3968, 0xd495 },
  { 3976, 0x0040 }, { 3977, 0x0421 }, { 3980, 0xa515 }, { 3987, 0x60c0 },
  /* 0x8300 */
  { 3991, 0x0d83 }, { 3997, 0xe800 }, { 4001, 0x7006 }, { 4006, 0x3489 },
  { 4012, 0x609c }, { 4018, 0x00fa }, { 4024, 0x0000 }, { 4024, 0xa101 },
  { 4028, 0x2055 }, { 4033, 0x3b34 }, { 4041, 0x32c0 }, { 4046, 0xc000 },
  { 4048, 0x8281 }, { 4052, 0x2013 }, { 4056, 0x0500 }, { 4058, 0x1340 },
  /* 0x8400 */
  { 4062, 0x8442 }, { 4066, 0x0222 }, { 4069, 0x8000 }, { 4070, 0x0200 },
  { 4071, 0xa5a0 }, { 4077, 0x1746 }, { 4084, 0x04b1 }, { 4089, 0x3159 },
  { 4096, 0x0022 }, { 4098, 0x402c }, { 4102, 0x8740 }, { 4107, 0x6412 },
  { 4112, 0x9185 }, { 4118, 0x1008 }, { 4120, 0x8480 }, { 4123, 0x2c87 },
  /* 0x8500 */
  { 4130, 0x508c }, { 4135, 0x5001 }, { 4138, 0x8cbc }, { 4146, 0x805c },
  { 4151, 0x8040 }, { 4153, 0xf24f }, { 4163, 0x8817 }, { 4169, 0xae00 },
  { 4174, 0x9a62 }, { 4181, 0xa108 }, { 4185, 0x20a5 }, { 4190, 0xf1d0 },
  { 4198, 0x4c84 }, { 4203, 0x8500 }, { 4206, 0x2141 }, { 4210, 0x9048 },
  /* 0x8600 */
  { 4214, 0x6031 }, { 4219, 0x4b07 }, { 4226, 0x0282 }, { 4229, 0x3540 },
  { 4234, 0x0047 }, { 4238, 0x23cc }, { 4245, 0x921f }, { 4253, 0x04e0 },
  { 4257, 0x2100 }, { 4259, 0x1542 }, { 4264, 0x21c2 }, { 4269, 0x83ba },
  { 4277, 0x002b }, { 4281, 0x14a6 }, { 4287, 0x00a9 }, { 4291, 0x3400 },
  /* 0x8700 */
  { 4294, 0xc8b0 }, { 4300, 0xc219 }, { 4306, 0xc10a }, { 4311, 0x7606 },
  { 4318, 0x2029 }, { 4322, 0x2100 }, { 4324, 0x8032 }, { 4328, 0x0806 },
  { 4331, 0x1bf8 }, { 4340, 0x43a9 }, { 4347, 0x7089 }, { 4353, 0xc022 },
  { 4357, 0x4702 }, { 4362, 0x9660 }, { 4368, 0x2c1c }, { 4374, 0x850a },
  /* 0x8800 */
  { 4379, 0x0e4a }, { 4385, 0xdf1d }, { 4396, 0x6100 }, { 4399, 0x1425 },
  { 4404, 0x4f2a }, { 4412, 0x9562 }, { 4419, 0x0211 }, { 4422, 0x0a02 },
  { 4425, 0x0001 }, { 4426, 0x9d00 }, { 4431, 0x0501 }, { 4434, 0x6400 },
  { 4437, 0x7c01 }, { 4443, 0x480e }, { 4448, 0x8080 }, { 4450, 0x00a3 },
  /* 0x8900 */
  { 4454, 0xe042 }, { 4459, 0x1760 }, { 4465, 0x01c1 }, { 4469, 0x4627 },
  { 4476, 0x8265 }, { 4482, 0x1c84 }, { 4487, 0x480e }, { 4492, 0x3c29 },
  { 4499, 0x2200 }, { 4501, 0x9831 }, { 4507, 0x0021 }, { 4509, 0x10f1 },
  { 4515, 0x0000 }, { 4515, 0x01f0 }, { 4520, 0x2a20 }, { 4524, 0xa24a },
  /* 0x8a00 */
  { 4530, 0x80b0 }, { 4534, 0x4036 }, { 4539, 0x9855 }, { 4546, 0x60a0 },
  { 4550, 0x62a9 }, { 4557, 0x31c8 }, { 4563, 0x00a2 }, { 4566, 0xcee0 },
  { 4574, 0x8849 }, { 4579, 0x82c5 }, { 4585, 0xc280 }, { 4589, 0x48c8 },
  { 4594, 0x0748 }, { 4599, 0xa0ba }, { 4606, 0x1000 }, { 4607, 0x9071 },
  /* 0x8b00 */
  { 4613, 0x0c60 }, { 4617, 0xd002 }, { 4621, 0x2000 }, { 4622, 0x1081 },
  { 4625, 0x217c }, { 4632, 0x421c }, { 4637, 0x2008 }, { 4639, 0x5340 },
  { 4644, 0xa832 }, { 4650, 0xd030 }, { 4655, 0x0000 }, { 4655, 0x0000 },
  { 4655, 0x0000 }, { 4655, 0x0000 }, { 4655, 0x0000 }, { 4655, 0x0000 },
  /* 0x8c00 */
  { 4655, 0x0000 }, { 4655, 0x0000 }, { 4655, 0x0000 }, { 4655, 0x6300 },
  { 4659, 0x8aa0 }, { 4664, 0x2b9a }, { 4672, 0x2358 }, { 4678, 0x4868 },
  { 4683, 0x08c0 }, { 4686, 0x1a0d }, { 4692, 0x0010 }, { 4693, 0x0600 },
  { 4695, 0x8a60 }, { 4700, 0x2260 }, { 4704, 0x9102 }, { 4708, 0xc1a5 },
  /* 0x8d00 */
  { 4715, 0x020a }, { 4718, 0x0884 }, { 4721, 0x0000 }, { 4721, 0x0000 },
  { 4721, 0x0000 }, { 4721, 0x0000 }, { 4721, 0x5220 }, { 4725, 0x8000 },
  { 4726, 0x2114 }, { 4730, 0xc023 }, { 4735, 0x9841 }, { 4740, 0x1aa4 },
  { 4746, 0x45e1 }, { 4753, 0x02b2 }, { 4758, 0x10b0 }, { 4762, 0x2017 },
  /* 0x8e00 */
  { 4767, 0x0872 }, { 4772, 0x0052 }, { 4775, 0x00cf }, { 4781, 0x23ca },
  { 4788, 0xe803 }, { 4794, 0x7810 }, { 4799, 0xb206 }, { 4805, 0x0e03 },
  { 4810, 0x020c }, { 4813, 0x6c25 }, { 4820, 0x6284 }, { 4825, 0x0c28 },
  { 4829, 0x809b }, { 4835, 0x1012 }, { 4838, 0x6100 }, { 4841, 0x0683 },
  /* 0x8f00 */
  { 4846, 0x8185 }, { 4851, 0x41c1 }, { 4856, 0x71ab }, { 4865, 0x04f0 },
  { 4870, 0x808b }, { 4875, 0x613e }, { 4883, 0x0020 }, { 4884, 0x0000 },
  { 4884, 0x0000 }, { 4884, 0x2000 }, { 4885, 0x0073 }, { 4890, 0x4160 },
  { 4894, 0x2c43 }, { 4900, 0x002d }, { 4904, 0x4119 }, { 4909, 0x4862 },
  /* 0x9000 */
  { 4914, 0x1114 }, { 4918, 0x0900 }, { 4920, 0xb700 }, { 4926, 0x8098 },
  { 4930, 0x1018 }, { 4933, 0x2800 }, { 4935, 0x10c4 }, { 4939, 0x0211 },
  { 4942, 0x5920 }, { 4947, 0x0ba1 }, { 4953, 0x0027 }, { 4957, 0x605d },
  { 4964, 0x11b8 }, { 4970, 0xb3a4 }, { 4978, 0x8820 }, { 4981, 0xc051 },
  /* 0x9100 */
  { 4986, 0x2171 }, { 4992, 0x55d1 }, { 5000, 0xc2ad }, { 5008, 0x36d2 },
  { 5016, 0x8188 }, { 5020, 0x0e88 }, { 5025, 0x2092 }, { 5029, 0x0e10 },
  { 5033, 0x446a }, { 5039, 0x413a }, { 5045, 0x7142 }, { 5051, 0xb84f },
  { 5060, 0x002c }, { 5063, 0x4698 }, { 5069, 0xf630 }, { 5077, 0x2a83 },
  /* 0x9200 */
  { 5083, 0x16f3 }, { 5092, 0x314d }, { 5099, 0xc178 }, { 5106, 0x5769 },
  { 5115, 0xe4cd }, { 5124, 0x3302 }, { 5129, 0xc3a3 }, { 5137, 0xbbe1 },
  { 5147, 0x6700 }, { 5152, 0x8284 }, { 5156, 0x89b1 }, { 5163, 0xbd44 },
  { 5171, 0x79ef }, { 5183, 0xb3a9 }, { 5192, 0x51ab }, { 5200, 0x8a01 },
  /* 0x9300 */
  { 5204, 0x2105 }, { 5208, 0xf032 }, { 5215, 0x06b2 }, { 5221, 0x00d8 },
  { 5225, 0x0380 }, { 5228, 0x45a7 }, { 5236, 0xa6b0 }, { 5243, 0xa45b },
  { 5251, 0xad07 }, { 5259, 0x4924 }, { 5264, 0x0b5a }, { 5271, 0x0470 },
  { 5275, 0x3ef2 }, { 5285, 0xd208 }, { 5290, 0x00c4 }, { 5293, 0x2f80 },
  /* 0x9400 */
  { 5299, 0xe316 }, { 5307, 0x80e0 }, { 5311, 0xc000 }, { 5313, 0xa81e },
  { 5320, 0x1528 }, { 5325, 0x9220 }, { 5329, 0xe90a }, { 5336, 0x0006 },
  { 5338, 0x0018 }, { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 },
  { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 },
  /* 0x9500 */
  { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 },
  { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x0000 }, { 5340, 0x4300 },
  { 5343, 0x7110 }, { 5348, 0xe000 }, { 5351, 0x1a42 }, { 5356, 0xa450 },
  { 5361, 0x0b40 }, { 5365, 0xe60f }, { 5374, 0x0051 }, { 5377, 0x0000 },
  /* 0x9600 */
  { 5377, 0x0000 }, { 5377, 0x6000 }, { 5379, 0x1074 }, { 5384, 0x378a },
  { 5392, 0x0002 }, { 5393, 0x01d4 }, { 5398, 0x4002 }, { 5400, 0xd810 },
  { 5405, 0x021e }, { 5410, 0xa442 }, { 5415, 0xc270 }, { 5421, 0x0408 },
  { 5423, 0x0400 }, { 5424, 0xe504 }, { 5430, 0x8200 }, { 5432, 0x0402 },
  /* 0x9700 */
  { 5434, 0x022c }, { 5438, 0x2c00 }, { 5441, 0x010e }, { 5445, 0x000a },
  { 5447, 0xc40a }, { 5452, 0x0da0 }, { 5457, 0x4488 }, { 5461, 0xa9c8 },
  { 5468, 0x0201 }, { 5470, 0xc6e0 }, { 5477, 0x5004 }, { 5480, 0xd766 },
  { 5490, 0x76b2 }, { 5499, 0x6b93 }, { 5508, 0x8013 }, { 5512, 0x0592 },
  /* 0x9800 */
  { 5517, 0x6480 }, { 5521, 0x5250 }, { 5526, 0xc869 }, { 5533, 0x402d },
  { 5538, 0x0490 }, { 5541, 0x06ce }, { 5548, 0x146c }, { 5554, 0x0000 },
  { 5554, 0x0000 }, { 5554, 0x0000 }, { 5554, 0x6800 }, { 5557, 0x8d91 },
  { 5564, 0x1124 }, { 5568, 0x0000 }, { 5568, 0x04ea }, { 5574, 0x0048 },
  /* 0x9900 */
  { 5576, 0x0184 }, { 5579, 0x9ce2 }, { 5587, 0x08c4 }, { 5591, 0x1e3e },
  { 5600, 0x61c3 }, { 5607, 0xdb10 }, { 5614, 0x0001 }, { 5615, 0x0000 },
  { 5615, 0x0000 }, { 5615, 0xa800 }, { 5618, 0x0040 }, { 5619, 0xa627 },
  { 5627, 0x0208 }, { 5629, 0x5618 }, { 5635, 0x1c80 }, { 5639, 0x6231 },
  /* 0x9a00 */
  { 5645, 0x181c }, { 5650, 0x4043 }, { 5654, 0x609d }, { 5661, 0x0168 },
  { 5665, 0x5c92 }, { 5672, 0x2052 }, { 5676, 0x0000 }, { 5676, 0x0000 },
  { 5676, 0x0000 }, { 5676, 0x0000 }, { 5676, 0xd400 }, { 5680, 0xca74 },
  { 5688, 0x414a }, { 5693, 0x18e5 }, { 5700, 0x12b1 }, { 5706, 0xa62c },
  /* 0x9b00 */
  { 5713, 0x7b3f }, { 5725, 0x1a45 }, { 5731, 0x2841 }, { 5735, 0x26b8 },
  { 5742, 0x1900 }, { 5745, 0x48e0 }, { 5750, 0x7d6a }, { 5760, 0x83a8 },
  { 5766, 0xaef1 }, { 5776, 0x6411 }, { 5781, 0x12c0 }, { 5785, 0xd987 },
  { 5794, 0x4182 }, { 5798, 0xa181 }, { 5803, 0x8ca0 }, { 5808, 0xa788 },
  /* 0x9c00 */
  { 5815, 0x8805 }, { 5819, 0x5742 }, { 5826, 0x07cc }, { 5833, 0x20e2 },
  { 5838, 0xc63a }, { 5846, 0xf959 }, { 5856, 0x4f08 }, { 5862, 0x08a5 },
  { 5867, 0x0000 }, { 5867, 0x0000 }, { 5867, 0x0000 }, { 5867, 0x0000 },
  { 5867, 0x0000 }, { 5867, 0x0000 }, { 5867, 0x0040 }, { 5868, 0x0284 },
  /* 0x9d00 */
  { 5871, 0x0804 }, { 5873, 0x7182 }, { 5879, 0x8000 }, { 5880, 0x341d },
  { 5887, 0x04ac }, { 5892, 0x8018 }, { 5895, 0x0e2c }, { 5901, 0x58c1 },
  { 5907, 0x6458 }, { 5913, 0x01ec }, { 5919, 0x5402 }, { 5923, 0x9222 },
  { 5928, 0x0688 }, { 5932, 0xc4f0 }, { 5939, 0x4aa1 }, { 5945, 0x4019 },
  /* 0x9e00 */
  { 5949, 0x4484 }, { 5953, 0x3267 }, { 5961, 0x0000 }, { 5961, 0x0000 },
  { 5961, 0x0000 }, { 5961, 0x0000 }, { 5961, 0x0000 }, { 5961, 0x1c00 },
  { 5964, 0xc0bd }, { 5972, 0x4940 }, { 5976, 0xd110 }, { 5981, 0x0039 },
  { 5985, 0x0940 }, { 5988, 0x8020 }, { 5990, 0x7090 }, { 5995, 0x8127 },
  /* 0x9f00 */
  { 6001, 0x820c }, { 6005, 0x8ed7 }, { 6015, 0x8c44 }, { 6020, 0xb696 },
  { 6029, 0x00fa }, { 6035, 0x65e8 }, { 6043, 0xe300 }, { 6048, 0x242b },
  { 6054, 0x8000 }, { 6055, 0x40d7 }, { 6062, 0x002e },
};
static const Summary16 jisx0212_uni2indx_pageff[6] = {
  /* 0xff00 */
  { 6066, 0x0000 }, { 6066, 0x0000 }, { 6066, 0x0000 }, { 6066, 0x0000 },
  { 6066, 0x0000 }, { 6066, 0x4000 },
};

static int
jisx0212_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, size_t n)
{
  if (n >= 2) {
    const Summary16 *summary = NULL;
    if (wc >= 0x0000 && wc < 0x0460)
      summary = &jisx0212_uni2indx_page00[(wc>>4)];
    else if (wc >= 0x2100 && wc < 0x2130)
      summary = &jisx0212_uni2indx_page21[(wc>>4)-0x210];
    else if (wc >= 0x4e00 && wc < 0x9fb0)
      summary = &jisx0212_uni2indx_page4e[(wc>>4)-0x4e0];
    else if (wc >= 0xff00 && wc < 0xff60)
      summary = &jisx0212_uni2indx_pageff[(wc>>4)-0xff0];
    if (summary) {
      unsigned short used = summary->used;
      unsigned int i = wc & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        unsigned short c;
        /* Keep in 'used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add 'summary->indx' and the number of bits set in 'used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        c = jisx0212_2charset[summary->indx + used];
        r[0] = (c >> 8); r[1] = (c & 0xff);
        return 2;
      }
    }
    return RET_ILUNI;
  }
  return RET_TOOSMALL;
}
