# The following variable is specific to this backend and its correct
# values might depend on your environment - feel free to set it accordingly.

SQLITE3INCLUDEDIR = -I/usr/include
SQLITE3LIBDIR = -L/usr/lib
SQLITE3LIBS = -lsqlite3

# The rest of the Makefile is indepentent of the target environment.

COMPILER = g++
CXXFLAGS = -Wall -pedantic -Wno-long-long
SHARED_CXXFLAGS = ${CXXFLAGS} -fPIC
INCLUDEDIRS = -I../../../include -I../../../include/private ${SQLITE3INCLUDEDIR}

SHARED_LIBDIRS = ${SQLITE3LIBDIR}
SHARED_LIBS = ${SQLITE3LIBS} ../../core/libsoci_core.a

UNAME = $(shell uname)
ifeq ($(UNAME),Darwin)
	SHARED_LINK_FLAGS = -dynamiclib -flat_namespace -undefined suppress
else
	SHARED_LINK_FLAGS = -shared
endif


OBJECTS = blob.o error.o factory.o row-id.o session.o standard-into-type.o \
	standard-use-type.o statement.o vector-into-type.o vector-use-type.o \
	common.o

SHARED_OBJECTS = blob-s.o factory-s.o row-id-s.o session-s.o \
	standard-into-type-s.o standard-use-type-s.o statement-s.o \
	vector-into-type-s.o vector-use-type-s.o common-s.o


libsoci_sqlite3.a : ${OBJECTS}
	ar rv $@ $?
	rm *.o


blob.o : blob.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

error.o : error.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

common.o : common.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

factory.o : factory.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

row-id.o : row-id.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

session.o : session.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

standard-into-type.o : standard-into-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

standard-use-type.o : standard-use-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

statement.o : statement.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

vector-into-type.o : vector-into-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

vector-use-type.o : vector-use-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}


shared : ${SHARED_OBJECTS}
	${COMPILER} ${SHARED_LINK_FLAGS} -o libsoci_sqlite3.so \
        	${SHARED_OBJECTS} ${SHARED_LIBDIRS} ${SHARED_LIBS}
	rm *.o

blob-s.o : blob.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

error-s.o : error.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGS} ${INCLUDEDIRS}

common-s.o : common.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

factory-s.o : factory.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

row-id-s.o : row-id.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

session-s.o : session.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

standard-into-type-s.o : standard-into-type.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

standard-use-type-s.o : standard-use-type.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

statement-s.o : statement.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

vector-into-type-s.o : vector-into-type.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}

vector-use-type-s.o : vector-use-type.cpp
	${COMPILER} -c -o $@ $? ${SHARED_CXXFLAGS} ${INCLUDEDIRS}


clean :
	rm -f libsoci_sqlite3.a libsoci_sqlite3.so
