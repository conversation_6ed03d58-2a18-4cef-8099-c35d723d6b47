/*
 * Copyright (C) 1999-2001 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CJK variants table
 */

static const unsigned short cjk_variants[12038] = {
  0x9e44, 0x1e2a, 0x200b, 0xcb87, 0xaf0c, 0x9e0a, 0x9e0b, 0xd42c,
  0x23c1, 0xaf0e, 0x9e04, 0x9e05, 0xa176, 0xd207, 0x2303, 0xa304,
  0x1e12, 0x619c, 0xeb57, 0x9e11, 0x2c02, 0xac08, 0x1e17, 0xa34b,
  0x1e16, 0xa34b, 0x1e20, 0xa775, 0xb96d, 0x23e2, 0x3a37, 0x3b09,
  0xd5c2, 0xb771, 0x4cf8, 0xcd72, 0x1e22, 0xa3be, 0x1e18, 0xa775,
  0x1e24, 0xa169, 0x1e1f, 0xa3be, 0xe149, 0x1e21, 0xa169, 0x23b3,
  0xa6b4, 0x20a1, 0x2e76, 0xcadd, 0xa5aa, 0x00f6, 0x200b, 0xcb87,
  0x3792, 0x3860, 0xb90f, 0xc23f, 0x5c4a, 0x5c50, 0x673b, 0x674a,
  0x68a8, 0xe8ce, 0x9e33, 0x9e32, 0xd1e8, 0x40ba, 0xc232, 0x2a6f,
  0xee97, 0x3319, 0x34e7, 0xd209, 0xa3ca, 0x2208, 0xd2c5, 0x2efc,
  0xdffa, 0x0006, 0x9e94, 0x1e46, 0xe579, 0x1e45, 0xe579, 0x2c1b,
  0x2e7a, 0x2e85, 0x6ebc, 0xeebd, 0xcfa9, 0xc0cf, 0x397d, 0xba02,
  0x1f17, 0x3500, 0x473e, 0xd846, 0xa5ac, 0x564e, 0xd65d, 0x1e58,
  0xb909, 0x1e57, 0xb909, 0x6690, 0x66a0, 0xe6b1, 0xcfd2, 0x60f7,
  0x6109, 0x610a, 0xe115, 0xb6f8, 0x2158, 0xa9cb, 0xdcb7, 0x1e82,
  0xe1e0, 0x1e7e, 0x1e81, 0xbf27, 0x1e79, 0x1e81, 0x2e72, 0xbf27,
  0x6f9c, 0xef9f, 0x1e79, 0x1e7e, 0xbf27, 0x1e71, 0xe1e0, 0x36b8,
  0xc7ad, 0xdc6b, 0xc22d, 0x1e8b, 0xa3d3, 0x1e8a, 0xa3d3, 0xaf0d,
  0x1e8f, 0x1e90, 0xb5bc, 0x1e8e, 0x1e90, 0x35bc, 0xd667, 0x1e8e,
  0x9e8f, 0x1f1d, 0xe6f2, 0x9e44, 0xa6db, 0x2c81, 0x2d57, 0x3b72,
  0xbb73, 0x9e99, 0x9e98, 0x1e9c, 0x9e9e, 0x1e9a, 0x9e9e, 0x3589,
  0x358a, 0x6f4a, 0xef50, 0x1e9a, 0x9e9c, 0xc10f, 0x1ebe, 0xa166,
  0x4522, 0xc523, 0x4546, 0x4552, 0x455d, 0xc55e, 0x9eaf, 0x9eb0,
  0x9eab, 0x9eac, 0xa91c, 0xd9aa, 0xd93b, 0xa6b2, 0x9ebb, 0x9eba,
  0x1ea1, 0xa166, 0xa104, 0xafce, 0x20cd, 0x22a8, 0xa2d5, 0x1ed8,
  0xa0c5, 0xa0d5, 0xdb8e, 0xa0df, 0x21ab, 0x21b0, 0xbc37, 0x2f93,
  0xaf9e, 0x9f5b, 0x1f1e, 0xa098, 0x1f96, 0x2d18, 0xad19, 0x1efa,
  0xa009, 0x9ec5, 0x20ca, 0xa0f2, 0xa40c, 0x9eed, 0xaf74, 0x214f,
  0x6577, 0x6578, 0xe57f, 0xa100, 0xa011, 0x9ede, 0x2047, 0xa3cd,
  0x1fa1, 0xa0f9, 0x1ed3, 0xa009, 0x9fc7, 0x2f6c, 0xb58c, 0x9f4e,
  0x2023, 0x2f77, 0x2fac, 0x4706, 0xeae3, 0x20a0, 0xc26b, 0xae11,
  0x1e51, 0x473e, 0xd846, 0xa12a, 0xa925, 0xb703, 0xa0b4, 0xa005,
  0x1e91, 0x1f20, 0xa0b3, 0x1ed0, 0x2098, 0xce56, 0xa049, 0x1f1d,
  0xa0b3, 0x9fe5, 0x9fd4, 0xa0b7, 0xa000, 0xa02b, 0xa096, 0x207d,
  0xa0de, 0x1f47, 0xcada, 0xa2aa, 0x1f32, 0x1f60, 0xa11e, 0x1f31,
  0x1f60, 0xa11e, 0x9f90, 0xd0c4, 0x9f40, 0x9f3c, 0xb014, 0x9f2b,
  0xae03, 0x9efe, 0x4b28, 0x5eb0, 0x5ec6, 0x6ab5, 0xead4, 0xa360,
  0x6918, 0xe980, 0x1ecf, 0x2f7f, 0xeaf4, 0xa05a, 0x9fab, 0x1f31,
  0x1f32, 0x211e, 0xa9b3, 0xa0ad, 0xa0c9, 0xaf8a, 0x3cd5, 0xc04b,
  0x2002, 0xae76, 0xaf88, 0xa058, 0x29b7, 0xa9ea, 0xb765, 0x6625,
  0x6634, 0x6670, 0xe682, 0xb549, 0x9f35, 0x9ed1, 0x5350, 0xd5a6,
  0x9fe0, 0x1ef7, 0xa0f9, 0x9fb6, 0xa0e5, 0x2075, 0xe049, 0xa074,
  0xa0d1, 0xa108, 0xa115, 0x9f5e, 0xa102, 0x2118, 0xac3d, 0xc7e6,
  0x9fa3, 0xac40, 0xc537, 0x9fe3, 0xccfb, 0x9efc, 0xa101, 0xdf14,
  0xc23c, 0x1f23, 0xb093, 0xa16a, 0xcae2, 0x9fa0, 0xda2b, 0x9fee,
  0x9fc1, 0x9f21, 0xa114, 0xa13c, 0xa006, 0xa137, 0xa008, 0x2039,
  0xa109, 0x1fe2, 0xd129, 0xa036, 0x209a, 0xb548, 0x2079, 0x2099,
  0x20c3, 0xa907, 0x4ca5, 0xeb3b, 0x30de, 0xbb23, 0x9f25, 0x1f75,
  0xae76, 0x207a, 0x24b1, 0xa592, 0x9f1c, 0x9fe9, 0x9feb, 0x1ed3,
  0x9efa, 0xa06c, 0x00f6, 0x1e2a, 0xcb87, 0xd146, 0xa010, 0xa00f,
  0x9eec, 0xae78, 0xa0b9, 0x2fa3, 0xd5c9, 0x1eff, 0xc706, 0xa03c,
  0x22b5, 0xa2cc, 0x9f26, 0xa25a, 0x9ff1, 0x1fed, 0xa109, 0xa0b5,
  0xa024, 0xa0be, 0xd2f1, 0xdcf0, 0x9eee, 0x9f1f, 0x5d0b, 0x5d17,
  0xdd5d, 0x9f83, 0xa0c0, 0x9f5c, 0xafa4, 0xe03c, 0x200a, 0xa0af,
  0x9fa7, 0x1fa6, 0xe049, 0xa078, 0xa077, 0x1ffb, 0x2099, 0x20c3,
  0xa907, 0x2003, 0x24b1, 0xa592, 0xa0c2, 0x1f2a, 0xa0de, 0xa0e8,
  0xa11f, 0x4f75, 0x69e1, 0xea82, 0xafac, 0xb770, 0x9f27, 0x1ed0,
  0x1f1e, 0xce56, 0x1ffb, 0x2079, 0x20c3, 0xa907, 0x1ff2, 0xb548,
  0xafad, 0x1f10, 0xc26b, 0x1e26, 0x2e76, 0xcadd, 0xabb6, 0xa10e,
  0xa13b, 0xa110, 0xa132, 0xa13a, 0x9f63, 0xa06c, 0x1f1d, 0x9f20,
  0x9f1b, 0xa03a, 0x9f24, 0xa01e, 0xa03e, 0xa059, 0xa07b, 0x1ffb,
  0x2079, 0x2099, 0xa907, 0xafb1, 0x9ec5, 0x9f65, 0x9ed9, 0x1ec2,
  0x22a8, 0xa2d5, 0x9fa8, 0x9ec6, 0xb55e, 0xc67b, 0x1f2a, 0xa07d,
  0x9ec9, 0xa0ed, 0x9fa5, 0xa07e, 0xa0f4, 0x3076, 0x30aa, 0xb0e1,
  0xa0e3, 0xe6c7, 0x9ed9, 0xa0e9, 0x1ef7, 0x9fa1, 0x9eea, 0x9fca,
  0x9fac, 0x9ebf, 0x9fa9, 0x1fed, 0xa039, 0xa0a4, 0xa0a7, 0x9fe6,
  0x9faa, 0x1fad, 0xac3d, 0x1f31, 0x1f32, 0x9f60, 0xa07f, 0xa139,
  0x9f18, 0xa0a8, 0x9fea, 0xa127, 0xa0a9, 0xa0a5, 0x9fe8, 0x2150,
  0xa152, 0xcc2a, 0xa1f6, 0x406e, 0xc097, 0x214c, 0xa151, 0x224b,
  0x22ca, 0xac05, 0x214a, 0xa151, 0xa154, 0x1ee7, 0x6577, 0x6578,
  0xe57f, 0x213f, 0xa152, 0x214a, 0xa14c, 0x213f, 0xa150, 0xa14e,
  0xa157, 0xa156, 0x1e68, 0xa9cb, 0xeee8, 0xa160, 0xa15c, 0xb5f7,
  0x1ea1, 0x9ebe, 0xa185, 0x1e21, 0x9e24, 0x9fde, 0xd62d, 0x2929,
  0x6754, 0xe75d, 0x24b2, 0x4b11, 0x65a2, 0x65d7, 0xe5dc, 0xd208,
  0x9e0c, 0x530a, 0xd332, 0x4faa, 0xe90a, 0x4363, 0xc378, 0xa180,
  0xa17e, 0xa6c5, 0xa18b, 0xae3d, 0xa189, 0xa167, 0x2706, 0x270e,
  0xa713, 0xaca1, 0xa184, 0x218c, 0xcb27, 0xa182, 0x218a, 0xcb27,
  0x226e, 0xa3e7, 0xa6e7, 0xa192, 0xd0c4, 0xa190, 0xab82, 0x21a9,
  0xabeb, 0xdecd, 0xdfb2, 0x2b90, 0xab9c, 0xa85a, 0xb700, 0xabc3,
  0x2bbc, 0xabc7, 0xabcc, 0x2199, 0xabeb, 0x2e42, 0xcf83, 0x1ecc,
  0x21b0, 0xbc37, 0x3638, 0xef15, 0xe9ae, 0x1ecc, 0x21ab, 0xbc37,
  0xa1b4, 0x3c96, 0xd85d, 0xbc7a, 0xa1b1, 0xbcc1, 0xbcef, 0xa1cd,
  0xbd0c, 0x21c8, 0xbde8, 0xbd7c, 0xbd82, 0x3053, 0x30bd, 0xbdd2,
  0xbdb8, 0x21d6, 0xbe96, 0xa1c0, 0xbdbc, 0xa1bb, 0xbe1b, 0xbe4a,
  0xc69a, 0xb817, 0x21c6, 0xbe96, 0x21dc, 0xbf9f, 0x21db, 0xbf9f,
  0x4108, 0x4155, 0x4188, 0xc199, 0x2e7e, 0xb73a, 0x21e2, 0xa1e3,
  0x21e1, 0xa1e3, 0x21e1, 0xa1e2, 0xecf3, 0xac45, 0x2904, 0xd655,
  0x21ec, 0x21ee, 0x68a8, 0x68cc, 0xe8ce, 0xece7, 0x21e8, 0x21ee,
  0x68a8, 0x68cc, 0xe8ce, 0x21f4, 0x317f, 0xb191, 0x21e8, 0x21ec,
  0x68a8, 0x68cc, 0xe8ce, 0xa1f1, 0xa1ef, 0x2c32, 0x2c34, 0x2c36,
  0x2c37, 0xeb50, 0xbac8, 0x21ed, 0x317f, 0xb191, 0xa147, 0x2757,
  0x284a, 0xa8a4, 0x2c80, 0xef63, 0x3483, 0xb4ca, 0xa1fe, 0xa1fd,
  0xe47f, 0x2202, 0xe1d6, 0x2200, 0xe1d6, 0xa204, 0xa203, 0x221b,
  0xa275, 0x1e42, 0xd2c5, 0xb81e, 0xd2bb, 0x2283, 0x2290, 0xc53b,
  0xa6ed, 0xb289, 0xa289, 0xa247, 0xa25b, 0x2205, 0x2231, 0x224f,
  0x2259, 0xa275, 0xa22a, 0xa22b, 0x2227, 0x223c, 0xa2ab, 0x2226,
  0x223c, 0xa2ab, 0x624b, 0x6464, 0xe4c7, 0xa220, 0x2225, 0xaf46,
  0x2257, 0x2277, 0x63df, 0xe4f2, 0xa244, 0xe8b3, 0x221b, 0x224f,
  0x2259, 0xa275, 0xa241, 0xd8fd, 0xa24e, 0xa23e, 0x2226, 0x2227,
  0xa2ab, 0xa28a, 0xa23a, 0xa28c, 0xa274, 0xa234, 0x2264, 0xa291,
  0xeb00, 0xa22d, 0xa219, 0x214b, 0x22ca, 0xac05, 0xbe82, 0xbb6c,
  0xa239, 0x221b, 0x2231, 0x2259, 0xa275, 0x226e, 0xa3e7, 0x2263,
  0x2271, 0x228d, 0x228e, 0x2292, 0x2294, 0x61f0, 0xe1fc, 0x222c,
  0x2277, 0x63df, 0xe4f2, 0x221b, 0x2231, 0x224f, 0xa275, 0xa033,
  0xa21a, 0xa265, 0x2251, 0x2271, 0x228d, 0x228e, 0x2292, 0x2294,
  0x61f0, 0xe1fc, 0x2242, 0xa291, 0xa25d, 0x65b9, 0xe609, 0xa287,
  0xa270, 0x218e, 0x2250, 0xa3e7, 0xa269, 0x2251, 0x2263, 0x228d,
  0x228e, 0x2292, 0x2294, 0x61f0, 0xe1fc, 0xb72d, 0xa240, 0x2205,
  0x221b, 0x2231, 0x224f, 0xa259, 0x222c, 0x2257, 0x63df, 0xe4f2,
  0xb22e, 0xa2e6, 0x2212, 0x2290, 0xc53b, 0xcb9a, 0xa267, 0xa218,
  0xa23d, 0xa23f, 0x2251, 0x2263, 0x2271, 0x228e, 0x2292, 0x2294,
  0x61f0, 0xe1fc, 0x2251, 0x2263, 0x2271, 0x228d, 0x2292, 0x2294,
  0x61f0, 0xe1fc, 0x2212, 0x2283, 0xc53b, 0x2242, 0xa264, 0x2251,
  0x2263, 0x2271, 0x228d, 0x228e, 0x2294, 0x61f0, 0xe1fc, 0x2251,
  0x2263, 0x2271, 0x228d, 0x228e, 0x2292, 0x61f0, 0xe1fc, 0x5cad,
  0xdcea, 0x22e7, 0xa2f8, 0xdfa6, 0x4cfc, 0xccff, 0xa2d9, 0xa2f1,
  0x1ec2, 0x20cd, 0xa2d5, 0x9f2e, 0x2226, 0x2227, 0xa23c, 0xa2f5,
  0xa2c1, 0x22b4, 0x22de, 0xb1a5, 0x22b3, 0x22de, 0xb1a5, 0x2026,
  0xa2cc, 0xb548, 0xa2e2, 0xa2b2, 0xb54f, 0x22d1, 0xb555, 0xa2c8,
  0xa2c7, 0x214b, 0x224b, 0xac05, 0x22db, 0x22f2, 0xa2f3, 0x2026,
  0xa2b5, 0x22c5, 0x2f95, 0x2fa0, 0xb555, 0x1ec2, 0x20cd, 0xa2a8,
  0x22d7, 0xb702, 0x22d6, 0xb702, 0xa2a1, 0xa2e9, 0x22cb, 0x22f2,
  0xa2f3, 0xd0dc, 0x22b3, 0x22b4, 0xb1a5, 0xb22e, 0xa2bf, 0xb1c3,
  0xa27f, 0x229d, 0xa2f8, 0xa2da, 0xa2a2, 0x22cb, 0x22db, 0xa2f3,
  0x22cb, 0x22db, 0xa2f2, 0xa2b1, 0x229d, 0xa2e7, 0xa300, 0xa2fb,
  0x1e10, 0xa304, 0x1e10, 0xa303, 0xeead, 0x2fe9, 0x3031, 0xb0a4,
  0xb3ac, 0x5133, 0xd166, 0xa338, 0x37e9, 0xb7fe, 0x2e00, 0xdfca,
  0xa320, 0xa31e, 0xa329, 0xa32d, 0xcbcb, 0x5535, 0xd5cf, 0xa321,
  0xa326, 0xa331, 0x3c47, 0xbed9, 0xa32e, 0x2333, 0x2941, 0xa969,
  0x2332, 0x2941, 0xa969, 0xa31a, 0xa340, 0x3bc9, 0xe1ab, 0xa33a,
  0xe7c6, 0xa352, 0x3607, 0x36fb, 0x6629, 0xe65e, 0xd294, 0x1e16,
  0x9e17, 0xa350, 0x2d0b, 0x53ef, 0xd550, 0x2354, 0x23f6, 0xb5ea,
  0xa34d, 0xa346, 0x384c, 0xb9d5, 0x234f, 0x23f6, 0xb5ea, 0x2358,
  0xa5ae, 0x28f2, 0xdce3, 0x2355, 0xa5ae, 0xb13d, 0xc387, 0x53d4,
  0xd514, 0x9f54, 0xc6e7, 0x2365, 0x2877, 0x3ef7, 0x3f9b, 0x4002,
  0x597f, 0xee75, 0x2364, 0xd97f, 0xd1e5, 0x585b, 0xd85e, 0xadf5,
  0x2918, 0xb23c, 0xae01, 0x237d, 0xc68d, 0xa37b, 0x2dfb, 0xb372,
  0xcf37, 0x3064, 0xdcc9, 0xa374, 0x2373, 0xc68d, 0x2388, 0x23b0,
  0xaee0, 0xb239, 0x2ef0, 0xaef3, 0x23a4, 0x23af, 0x36a6, 0x36c6,
  0x3b74, 0xbb77, 0xc8d4, 0xa382, 0xa3b2, 0x2727, 0xa8d3, 0xa3ad,
  0xa399, 0xc825, 0xef8e, 0xad16, 0x23a0, 0xaec1, 0x28e5, 0x2edb,
  0x613d, 0xe1d0, 0xa38d, 0xa795, 0xaef3, 0xa3a1, 0x2395, 0xaec1,
  0xa39f, 0xaec2, 0xa3b4, 0x2386, 0xb6c6, 0xaec8, 0xaeda, 0x2ecf,
  0xaed0, 0xa38c, 0xaedd, 0x2386, 0x36a6, 0x36c6, 0x3b74, 0xbb77,
  0x2382, 0xaee0, 0xa389, 0x1e25, 0xa6b4, 0xa3a3, 0xbe90, 0xc9c1,
  0xd0b1, 0x60bb, 0x6130, 0xe6a3, 0xa3bb, 0xa3ba, 0x1e1f, 0x9e22,
  0x470c, 0xce23, 0x1e09, 0xaf0e, 0x23c3, 0x23c4, 0xa3c5, 0x23c2,
  0x23c4, 0xa3c5, 0x23c2, 0x23c3, 0xa3c5, 0x23c2, 0x23c3, 0xa3c4,
  0xe749, 0xe746, 0xa3f9, 0xb268, 0x9e41, 0xe6d9, 0x1eee, 0x23db,
  0xc08d, 0xb536, 0xa92c, 0xc2ae, 0x467a, 0x467c, 0x6aea, 0xeaee,
  0x1e8a, 0x9e8b, 0xcdb4, 0x2909, 0xdb8a, 0x354d, 0xb558, 0x23cd,
  0xc08d, 0xac09, 0x4573, 0x4582, 0x4589, 0xc58a, 0x28e1, 0xc77f,
  0x1e1b, 0x3a37, 0x3b09, 0xd5c2, 0x218e, 0x2250, 0xa26e, 0x49d6,
  0x5879, 0xe6bb, 0xa44c, 0x276e, 0x37b1, 0x3aaf, 0x51fa, 0xe8b1,
  0x234f, 0x2354, 0x35ea, 0xd449, 0xd65f, 0x23c8, 0x2606, 0xbb4e,
  0xa630, 0x24a9, 0xa4f6, 0x4c72, 0xefa5, 0x25ab, 0xa644, 0xe5a4,
  0xaf14, 0x1edd, 0xd855, 0xaf8c, 0x26ae, 0xb6cf, 0xa687, 0xa442,
  0xa5ce, 0xb061, 0xa451, 0x252b, 0x2656, 0xda21, 0xa678, 0x5074,
  0xd07d, 0x2518, 0x2553, 0x2554, 0x255f, 0x5aec, 0xe5d9, 0x2434,
  0xa449, 0x2433, 0xa449, 0xa450, 0xd117, 0xa44d, 0xa44a, 0xa415,
  0x3282, 0xc2c2, 0x2433, 0xa434, 0xa43f, 0xa3eb, 0xa43d, 0xa436,
  0xa41e, 0xa638, 0xa6c8, 0xa614, 0xa6a6, 0xa504, 0x24e1, 0x5c9f,
  0xdd20, 0x24bc, 0xa58e, 0xa4fa, 0xa5c6, 0xa5da, 0xda46, 0xe031,
  0xa5dc, 0x2492, 0xda4b, 0xda4d, 0x2611, 0x5656, 0xdb3c, 0xa4e3,
  0x248c, 0x30d2, 0xefa2, 0x248a, 0x30d2, 0xefa2, 0xda60, 0x246a,
  0xda4b, 0xa6a8, 0xa680, 0xa4a4, 0xa65d, 0xa49c, 0x2400, 0xa4f6,
  0xef69, 0x2580, 0xd849, 0x2003, 0x207a, 0xa592, 0x2173, 0xcb11,
  0x618e, 0xee79, 0x2459, 0xa58e, 0xa6a5, 0x65a7, 0xeb28, 0xe7ff,
  0x2516, 0x255e, 0x45d6, 0xc602, 0xa660, 0xa635, 0xa5f6, 0xa666,
  0x2629, 0xdb41, 0xa672, 0xa68c, 0xa665, 0xa5b2, 0x2458, 0x5c9f,
  0xdd20, 0xa485, 0xda36, 0x2560, 0x2586, 0x269e, 0x27d1, 0xb08a,
  0x2400, 0xa4a9, 0xa45a, 0x5a98, 0xdbf1, 0xa457, 0xd123, 0xd39e,
  0xa57a, 0xa523, 0x24d1, 0x255e, 0x45d6, 0xc602, 0x242f, 0x2553,
  0x2554, 0x255f, 0x5aec, 0xe5d9, 0xa61c, 0xa5ca, 0xa62e, 0xa5e9,
  0xa515, 0xa59a, 0xa59e, 0xa41f, 0xdaaf, 0xaff5, 0xa60a, 0xdb2a,
  0xe5ee, 0x242f, 0x2518, 0x2554, 0x255f, 0x5aec, 0xe5d9, 0x242f,
  0x2518, 0x2553, 0x255f, 0x5aec, 0xe5d9, 0xa649, 0xa649, 0x24d1,
  0x2516, 0x45d6, 0xc602, 0x242f, 0x2518, 0x2553, 0x2554, 0x5aec,
  0xe5d9, 0x24f2, 0x2586, 0x269e, 0x27d1, 0xb08a, 0x5854, 0x5858,
  0xe29c, 0xa616, 0xa5c7, 0xa6c0, 0x265b, 0x2699, 0x26d3, 0xef67,
  0xa613, 0x26c9, 0xcf57, 0xa63d, 0xa62f, 0xa510, 0x24af, 0xd849,
  0x6927, 0xe935, 0xdb71, 0x24f2, 0x2560, 0x269e, 0x27d1, 0xb08a,
  0x2459, 0xa4bc, 0x267e, 0xc616, 0x2003, 0x207a, 0xa4b1, 0xa524,
  0x3198, 0x3199, 0xbb56, 0xa527, 0xa5bb, 0x9e27, 0x2403, 0xa644,
  0x9e54, 0x2355, 0xa358, 0x25b7, 0xa674, 0x68df, 0x68e0, 0xe963,
  0xa4df, 0x41df, 0xd425, 0x25af, 0xa674, 0xa5a9, 0xa60d, 0xa6b3,
  0xef45, 0xa45b, 0xa56c, 0xa51d, 0xa417, 0xa45c, 0xa469, 0xe16a,
  0xab60, 0xa637, 0xa522, 0xa6c1, 0xa66f, 0xa4d4, 0xdb30, 0x23f9,
  0xbb4e, 0xa540, 0xa5bd, 0x247c, 0xdb3c, 0xa56f, 0xa455, 0xa567,
  0x2690, 0x2c1d, 0xc51e, 0xa653, 0xa51b, 0xa6b6, 0xa62f, 0x24d7,
  0xdb41, 0xa520, 0x2578, 0xa628, 0xa3fd, 0xa6d1, 0xa4d3, 0xa5e5,
  0xa452, 0xa574, 0xb076, 0xdb50, 0x2403, 0xa5ab, 0xa655, 0x2556,
  0xa557, 0xa668, 0xa618, 0xa645, 0x241f, 0xda21, 0x256e, 0xa699,
  0xa695, 0xa49d, 0xa4d2, 0xdb6b, 0xa4dd, 0xa4d5, 0xa650, 0xdb5f,
  0xa5f3, 0xa4d9, 0x25af, 0xa5b7, 0x585c, 0x585f, 0xe053, 0xa428,
  0xaf53, 0xa591, 0xa49b, 0xa413, 0xa4dc, 0xa694, 0x2617, 0x2c1d,
  0xc51e, 0xa68f, 0xa65c, 0x256e, 0x265b, 0x26d3, 0xef67, 0x24f2,
  0x2560, 0x2586, 0x27d1, 0xb08a, 0xbd4f, 0xa6ca, 0xa6c2, 0xa4bd,
  0xa456, 0xa499, 0xa411, 0x9eb8, 0xa5be, 0x1e25, 0xa3b3, 0xa624,
  0xa6c2, 0xa56d, 0xa5eb, 0x26a3, 0xa6bb, 0xa181, 0xa453, 0x2570,
  0xcf57, 0xa6a2, 0xd2cf, 0xd271, 0xa631, 0x256e, 0x2699, 0xef67,
  0x26f2, 0x26f4, 0xa70d, 0x26de, 0x26ec, 0x2efb, 0x2efd, 0x5ff4,
  0xe025, 0xa6e0, 0x9e96, 0x26d8, 0x26ec, 0x2efb, 0x2efd, 0x5ff4,
  0xe025, 0xe856, 0xa6d9, 0x26e3, 0x2718, 0xccf0, 0x26e2, 0x2718,
  0xccf0, 0x3df5, 0x3e0a, 0xbe15, 0xa18f, 0xa6f1, 0x26d8, 0x26de,
  0x2efb, 0x2efd, 0x5ff4, 0xe025, 0x2213, 0x2712, 0xd597, 0x26f6,
  0x26fb, 0x26fd, 0x2700, 0xa70b, 0xc74f, 0xa6ea, 0x26d7, 0x26f4,
  0xa70d, 0x26fe, 0x2716, 0xa717, 0x26d7, 0x26f2, 0xa70d, 0xa707,
  0x26ef, 0x26fb, 0x26fd, 0x2700, 0xa70b, 0xb5e5, 0x26ef, 0x26f6,
  0x26fd, 0x2700, 0xa70b, 0x26ef, 0x26f6, 0x26fb, 0x2700, 0xa70b,
  0x26f3, 0x2716, 0xa717, 0x26ef, 0x26f6, 0x26fb, 0x26fd, 0xa70b,
  0xa711, 0x2186, 0x270e, 0xa713, 0xa6f5, 0xa70f, 0x26ef, 0x26f6,
  0x26fb, 0x26fd, 0xa700, 0x26d7, 0x26f2, 0xa6f4, 0x2186, 0x2706,
  0xa713, 0xa708, 0xa703, 0x26ed, 0xd597, 0x2186, 0x2706, 0xa70e,
  0x26f3, 0x26fe, 0xa717, 0x26f3, 0x26fe, 0xa716, 0x26e2, 0xa6e3,
  0x383e, 0xbb12, 0xa721, 0xa71f, 0x453a, 0xc53c, 0xd056, 0x238b,
  0xa8d3, 0xb257, 0xb747, 0xc3ea, 0x2754, 0x27ca, 0xadb3, 0xa8d9,
  0x2834, 0xa872, 0x3b7e, 0x3b7f, 0x3b81, 0xbca1, 0xe62f, 0x2c85,
  0xe62a, 0x276f, 0x28ca, 0x28de, 0xe62b, 0xaea7, 0xe62c, 0xa78b,
  0x2730, 0x27ca, 0xadb3, 0x21f7, 0x284a, 0xa8a4, 0xade0, 0xa805,
  0x28b0, 0x28b5, 0x28c7, 0x28dc, 0x4f48, 0xcf4e, 0xa8e2, 0x27bb,
  0xa8e9, 0x2862, 0xe696, 0x28b3, 0xe6ab, 0x289c, 0xc908, 0xa802,
  0xa803, 0xb1bb, 0xa88c, 0x6640, 0xe641, 0xbce5, 0x23f0, 0xd1fa,
  0x274f, 0xe62b, 0xa7a7, 0xa787, 0x1e18, 0x9e20, 0xa7a2, 0xa8a2,
  0xa7c0, 0xa8df, 0xa8e0, 0xa8da, 0xa773, 0xa753, 0x2841, 0xa8d8,
  0xc561, 0xe67b, 0xa39a, 0xa806, 0x282f, 0x2c27, 0xac2d, 0xa79c,
  0xa79b, 0xa8a2, 0xa778, 0xa8be, 0xa770, 0xab88, 0x280a, 0xd056,
  0xa88a, 0xa7e1, 0xa8b6, 0xa8cb, 0xa84f, 0xa816, 0x275d, 0xa8e9,
  0xa782, 0xc86e, 0xe656, 0x2730, 0x2754, 0xadb3, 0x24f2, 0x2560,
  0x2586, 0x269e, 0xb08a, 0xa7d3, 0xa7d2, 0xa852, 0x2864, 0xa8ce,
  0xa81d, 0x28c4, 0xe1ce, 0x2824, 0xe684, 0xa7ad, 0xc897, 0xb267,
  0x2d0e, 0x2d5c, 0xc895, 0xb383, 0xa763, 0xa764, 0xa75a, 0xa796,
  0xd3eb, 0xa7a9, 0xa879, 0x28ae, 0xa8af, 0xa7b4, 0xe67b, 0xa7da,
  0x27de, 0xe684, 0x6636, 0xe68e, 0x279a, 0x2c27, 0xac2d, 0x2895,
  0xe681, 0xb2a5, 0x273a, 0xa872, 0x454c, 0xc54d, 0x2792, 0xa8d8,
  0xa8ea, 0x21f7, 0x2757, 0xa8a4, 0xd314, 0xc57b, 0xa7b2, 0xa851,
  0xa850, 0xa7d8, 0xa896, 0xbd82, 0xa8a7, 0xa1a2, 0xd46c, 0xa86b,
  0xa75e, 0x27d9, 0xa8ce, 0xe663, 0x46d0, 0xee7d, 0xa861, 0x273a,
  0xa834, 0xac18, 0x2364, 0xee75, 0x2811, 0xa8cd, 0x450e, 0x4816,
  0xc8da, 0xc245, 0xa7ab, 0xa767, 0x2830, 0xe681, 0xa854, 0xa89e,
  0x28bb, 0xc246, 0x2760, 0xc908, 0xa897, 0x277a, 0xa7a1, 0x21f7,
  0x2757, 0xa84a, 0xa859, 0x2815, 0xa8af, 0x2815, 0xa8ae, 0x275b,
  0x28b5, 0x28dc, 0x4f48, 0xcf4e, 0x275f, 0xe6ab, 0x275b, 0x28b0,
  0x28dc, 0x4f48, 0xcf4e, 0xa7af, 0xde87, 0xa899, 0xa7a6, 0x27dc,
  0xe1ce, 0xa75b, 0x274f, 0xa8de, 0xa7b1, 0xa8e4, 0xa879, 0x27d9,
  0xa864, 0xc4bd, 0x238b, 0xa727, 0x2792, 0xa841, 0xa739, 0xa786,
  0x275b, 0x28b0, 0x28b5, 0x4f48, 0xcf4e, 0x274f, 0xa8ca, 0xa784,
  0xa785, 0x23e1, 0xc77f, 0xa75c, 0xa8cc, 0x2398, 0x2edb, 0xe13d,
  0x275d, 0xa7bb, 0xa846, 0x28ef, 0xa8f5, 0x28ee, 0xa8f5, 0xd072,
  0xa8f9, 0x2356, 0xdce3, 0x3bbb, 0xbbbc, 0x28ee, 0xa8ef, 0x28f7,
  0xa8fa, 0x28f6, 0xa8fa, 0xa8fc, 0xa8f1, 0x28f6, 0xa8f7, 0x2a7f,
  0xd05f, 0xa8f8, 0x2900, 0xabff, 0x28fd, 0xabff, 0xa90a, 0x21e6,
  0xd655, 0xe64d, 0x1ffb, 0x2079, 0x2099, 0xa0c3, 0x23d8, 0xdb8a,
  0xa902, 0x2fa9, 0x5907, 0xd986, 0xa913, 0xa90f, 0xd641, 0x236f,
  0xb23c, 0xa91b, 0xa91a, 0x9eb1, 0xa920, 0xa91f, 0x2923, 0xb8a6,
  0x2922, 0xb8a6, 0x9f19, 0x2172, 0x6754, 0xe75d, 0xa3cf, 0xbb80,
  0x372c, 0xb94d, 0xbcf0, 0xe82d, 0xbbd4, 0xda87, 0xa93e, 0xa96a,
  0xa939, 0x2332, 0x2333, 0xa969, 0xa950, 0xcad2, 0xb7f0, 0xa96e,
  0xeede, 0xa942, 0x4287, 0xe029, 0x2968, 0x296c, 0xc34e, 0xaf09,
  0xc55a, 0x2967, 0xadb4, 0x2965, 0xadb4, 0x2956, 0x296c, 0xc34e,
  0x2332, 0x2333, 0xa941, 0xa93a, 0x2956, 0x2968, 0xc34e, 0xa94b,
  0x29b3, 0xab2d, 0x29e6, 0xa9e7, 0x299d, 0x2a24, 0x4ca7, 0xccda,
  0x2a66, 0xaa8d, 0xaabd, 0xa9d9, 0x29f8, 0xd505, 0xa9ac, 0xc945,
  0xc385, 0xaab8, 0x2986, 0x2a24, 0x4ca7, 0xccda, 0xaaf5, 0xaad7,
  0x2aaf, 0xab00, 0xa992, 0x1f60, 0x2976, 0xab2d, 0x1f84, 0xa9ea,
  0x2a3f, 0xaa40, 0xaa12, 0xa9ca, 0xa9c9, 0x1e68, 0xa158, 0xa9d7,
  0xaa8e, 0xaa30, 0xa9cd, 0xa98a, 0xd591, 0xaa1f, 0x2978, 0xa9e7,
  0x2978, 0xa9e6, 0x1f84, 0xa9b7, 0xa9ec, 0xa9eb, 0xaae6, 0x298d,
  0xd505, 0xaa63, 0xaa41, 0xaa6d, 0xab08, 0xab0c, 0xab4c, 0xa9c6,
  0x2b22, 0xab43, 0x2a2f, 0xaa31, 0xa9e2, 0x2986, 0x299d, 0x4ca7,
  0xccda, 0xab2a, 0xab09, 0x2a1b, 0xaa31, 0xa9d2, 0x2a1b, 0xaa2f,
  0xaaa7, 0x2afa, 0xaafb, 0x29b8, 0xaa40, 0x29b8, 0x2a3f, 0xaa50,
  0xaa04, 0xaa40, 0xa9fb, 0x2987, 0xaa8d, 0xbdeb, 0xaa05, 0x1e3d,
  0xee97, 0xaaff, 0x2b30, 0x2b46, 0xab7e, 0xab0b, 0xab38, 0x28fb,
  0xd05f, 0x2987, 0xaa66, 0xa9d0, 0xaa32, 0xaabc, 0xab03, 0x29ab,
  0xab00, 0xa99b, 0xaaaa, 0xa988, 0x3127, 0xd06d, 0x2b1d, 0xd885,
  0xab32, 0xab21, 0xab2a, 0xa9aa, 0xa9ee, 0xab19, 0xa9a9, 0x3194,
  0xc644, 0x2a34, 0xaafb, 0x2a34, 0xaafa, 0xaa73, 0x29ab, 0xaaaf,
  0xaaad, 0xaa06, 0xaa2d, 0xaa75, 0xaa07, 0xaaf1, 0x2acb, 0xd885,
  0xaad2, 0x2a18, 0xab43, 0xab37, 0x2a26, 0xaad4, 0x2976, 0xa9b3,
  0x2a74, 0x2b46, 0xab7e, 0xaad0, 0xab24, 0xaa76, 0xb1d2, 0x2a18,
  0xab22, 0x2a74, 0x2b30, 0xab7e, 0xaa08, 0xab53, 0xab52, 0xab6b,
  0xa5e3, 0x2b78, 0x3569, 0x3585, 0x3586, 0xb588, 0xab7f, 0xab59,
  0x2b66, 0x3569, 0x3585, 0x3586, 0xb588, 0xab7d, 0xab7c, 0x2a74,
  0x2b30, 0xab46, 0xab6a, 0x2bcd, 0x2bd5, 0x2bd7, 0x2bdc, 0x2be7,
  0xc52f, 0xa197, 0x4260, 0x5675, 0xd6c7, 0xca79, 0xa7a8, 0xca81,
  0xd089, 0x219d, 0xab9c, 0x219d, 0xab90, 0x2bda, 0x2bf3, 0x2bf6,
  0xc3e4, 0x2b9f, 0xabe6, 0x2b9e, 0xabe6, 0xabf5, 0x2bb7, 0xabe9,
  0xac4e, 0xb1b2, 0xabae, 0xabab, 0x5b8c, 0xe1bc, 0xa0a2, 0x2ba1,
  0xabe9, 0xabc6, 0x21a6, 0xabc7, 0x2bdb, 0xabec, 0x5cd3, 0xdcd4,
  0xe1c7, 0xa1a4, 0xabbb, 0x21a6, 0xabbc, 0x6751, 0xe752, 0x66ba,
  0x674d, 0x674e, 0x674f, 0x6db4, 0x6e16, 0xee64, 0xa1a8, 0x2b81,
  0x2bd5, 0x2bd7, 0x2bdc, 0x2be7, 0xc52f, 0xaebd, 0x2b81, 0x2bcd,
  0x2bd7, 0x2bdc, 0x2be7, 0xc52f, 0x2b81, 0x2bcd, 0x2bd5, 0x2bdc,
  0x2be7, 0xc52f, 0x2b9d, 0x2bf3, 0x2bf6, 0xc3e4, 0x2bbd, 0xabec,
  0x2b81, 0x2bcd, 0x2bd5, 0x2bd7, 0x2be7, 0xc52f, 0xabe2, 0xda67,
  0x4aad, 0xcab6, 0xabdd, 0x2b9e, 0xab9f, 0x2b81, 0x2bcd, 0x2bd5,
  0x2bd7, 0x2bdc, 0xc52f, 0x2ba1, 0xabb7, 0x2199, 0xa1a9, 0x2bbd,
  0xabdb, 0x2b9d, 0x2bda, 0x2bf6, 0xc3e4, 0xaba0, 0x2b9d, 0x2bda,
  0x2bf3, 0xc3e4, 0x2bfe, 0xac0d, 0xac0b, 0xac0e, 0x2bf9, 0xac0d,
  0x28fd, 0xa900, 0x1e13, 0xac08, 0x214b, 0x224b, 0xa2ca, 0xac07,
  0xac06, 0x1e13, 0xac02, 0xa3de, 0xabfb, 0x2bf9, 0xabfe, 0xabfc,
  0x2c13, 0x2c14, 0xc23e, 0x2c12, 0x2c14, 0xc23e, 0x2c12, 0x2c13,
  0xc23e, 0xa875, 0xac1a, 0xac19, 0x1e48, 0x2e85, 0x6ebc, 0xeebd,
  0x2617, 0x2690, 0xc51e, 0xc1ce, 0x2c20, 0x5d7b, 0x6bae, 0x6c7b,
  0xec9c, 0x2c1f, 0x5d7b, 0x6bae, 0x6c7b, 0xec9c, 0x2c23, 0x2c24,
  0xb023, 0xac22, 0x2c22, 0xb023, 0xac26, 0xac25, 0x279a, 0x282f,
  0xac2d, 0xac2b, 0xac29, 0xeb40, 0x279a, 0x282f, 0xac27, 0x21f2,
  0x2c34, 0x2c36, 0x2c37, 0xeb50, 0x21f2, 0x2c32, 0x2c36, 0x2c37,
  0xeb50, 0x21f2, 0x2c32, 0x2c34, 0x2c37, 0xeb50, 0x21f2, 0x2c32,
  0x2c34, 0x2c36, 0xeb50, 0xac4d, 0x1fad, 0x2118, 0xc6e1, 0x1fb7,
  0xddfc, 0xac64, 0xac53, 0xa1e5, 0xac4a, 0xac46, 0x6ce5, 0xee1f,
  0xac38, 0xaba9, 0x2c5b, 0xae48, 0xac43, 0x2c4f, 0xae48, 0xac6c,
  0xac62, 0xac61, 0xac42, 0xac68, 0xac66, 0xac5e, 0xadbc, 0xa1fa,
  0x1e97, 0x2d57, 0x3b72, 0xbb73, 0xdc48, 0x2742, 0xe62a, 0xdc3a,
  0xad87, 0xad17, 0xacf4, 0xad50, 0x2cf6, 0x2d8b, 0x2d8c, 0x2db9,
  0x6666, 0x669d, 0xe6af, 0xa188, 0x2d52, 0x2d53, 0x2dcc, 0x2dd6,
  0x2dd7, 0xc89e, 0xadba, 0xadbd, 0xad87, 0xad2c, 0xadcb, 0xada8,
  0xada7, 0xacdd, 0xacd2, 0xacfd, 0xada2, 0xada0, 0xad22, 0xadd2,
  0xace9, 0xace8, 0xacf0, 0xacef, 0xac98, 0x2d58, 0x2db8, 0xadc6,
  0x2c9b, 0x2d8b, 0x2d8c, 0x2db9, 0x6666, 0x669d, 0xe6af, 0xace1,
  0xad97, 0xad0d, 0xadae, 0xad08, 0xad07, 0x234e, 0xd3ef, 0xad03,
  0x27fc, 0x2d5c, 0xc895, 0xa393, 0xac97, 0x1ed1, 0xad19, 0x1ed1,
  0xad18, 0xace5, 0xacbd, 0x2d83, 0xad84, 0xcbcf, 0xac9a, 0x2ca9,
  0x2d53, 0xc89e, 0x2ca9, 0x2d52, 0xc89e, 0x1e97, 0x2c81, 0x3b72,
  0xbb73, 0x2cf5, 0x2db8, 0xadc6, 0xad94, 0x27fc, 0x2d0e, 0xc895,
  0xad81, 0x3e13, 0xbeaa, 0xad73, 0xad6f, 0xad5d, 0xad2d, 0xad2d,
  0x2c96, 0xacb4, 0x2c9b, 0x2cf6, 0x2d8c, 0x2db9, 0x6666, 0x669d,
  0xe6af, 0x2c9b, 0x2cf6, 0x2d8b, 0x2db9, 0x6666, 0x669d, 0xe6af,
  0xad5a, 0xad02, 0xace4, 0xace3, 0xacc4, 0xacc3, 0xad04, 0xe6df,
  0x2730, 0x2754, 0xa7ca, 0x2965, 0xa967, 0x2cf5, 0x2d58, 0xadc6,
  0x2c9b, 0x2cf6, 0x2d8b, 0x2d8c, 0x6666, 0x669d, 0xe6af, 0xacad,
  0xac7f, 0xacb3, 0x2dd3, 0xadd4, 0x2cf5, 0x2d58, 0xadb8, 0xacbf,
  0x2ca9, 0x2dd6, 0xadd7, 0xace6, 0xadc5, 0xadc5, 0x2ca9, 0x2dcc,
  0xadd7, 0x2ca9, 0x2dcc, 0xadd6, 0xaddd, 0xaddb, 0xa759, 0xade3,
  0xade2, 0xe78f, 0xadf0, 0xadef, 0xa36e, 0xd856, 0xadfd, 0x2377,
  0xb372, 0xadfa, 0x67cd, 0xe7e8, 0x231d, 0xdfca, 0x2370, 0x2e63,
  0xae64, 0x9f48, 0xae25, 0xae2b, 0xcd19, 0xae43, 0xae33, 0x9f16,
  0xd88b, 0xcd48, 0xcc3e, 0xcb92, 0x2e5f, 0xb5d8, 0xae05, 0x2e2f,
  0xae36, 0xae40, 0xae08, 0xd4c6, 0x2e47, 0x2e5a, 0xae6b, 0x2e26,
  0xae36, 0x2f52, 0x3b78, 0xc688, 0xae6c, 0xae10, 0x2e26, 0xae2f,
  0xae58, 0xae57, 0xa183, 0xae27, 0x21aa, 0xcf83, 0xae0f, 0x2e2e,
  0x2e5a, 0xae6b, 0x2c4f, 0xac5b, 0xce28, 0xae3c, 0xae3b, 0x2e2e,
  0x2e47, 0xae6b, 0x2e1c, 0xb5d8, 0xb5d9, 0x2e01, 0xae64, 0x2e01,
  0xae63, 0x2e2e, 0x2e47, 0xae5a, 0xae31, 0x1e7e, 0x2e79, 0xb9a6,
  0xc9ca, 0x1e26, 0x1f75, 0x2002, 0x20a1, 0x2e77, 0xcadd, 0xae76,
  0xa016, 0x2e72, 0xb9a6, 0x9e48, 0xa1e0, 0x2e83, 0x2eb5, 0xaee3,
  0x2ef0, 0xaef3, 0x2e7f, 0xaee3, 0x5358, 0xd38a, 0x1e48, 0x2c1b,
  0x6ebc, 0xeebd, 0xb176, 0xc240, 0xaeec, 0xaee1, 0xaeab, 0x2fdc,
  0xb1c9, 0xaedf, 0xef90, 0x2ec3, 0xaee2, 0xa750, 0xae93, 0x2e7f,
  0x53f4, 0x544a, 0xd4ed, 0xaebb, 0x377e, 0x3780, 0x37a9, 0x37d7,
  0xb8a5, 0xaeb6, 0xaece, 0xabd3, 0xaedf, 0x2395, 0xa3a0, 0xa3a2,
  0x2e9f, 0xaee2, 0xa3a6, 0xaebc, 0x23a9, 0xaed0, 0x23a9, 0xaecf,
  0xa3a8, 0x2398, 0x28e5, 0xe13d, 0xa3ae, 0x2e99, 0xaebf, 0x2382,
  0xa3b0, 0xae91, 0x2e9f, 0xaec3, 0x2e7f, 0xae83, 0xaeea, 0xaee9,
  0xae90, 0x2385, 0x2e81, 0xaef3, 0x2385, 0x239b, 0x2e81, 0xaef0,
  0xdfea, 0x26d8, 0x26de, 0x26ec, 0x2efd, 0x5ff4, 0xe025, 0x1e43,
  0xdffa, 0x26d8, 0x26de, 0x26ec, 0x2efb, 0x5ff4, 0xe025, 0xe58b,
  0x44e3, 0x598d, 0x5fa1, 0x5fa7, 0x5fa8, 0x5fa9, 0xdfaf, 0xc570,
  0xb8c4, 0x330a, 0xb335, 0xa958, 0x9e00, 0x9e8c, 0x1e09, 0xa3c1,
  0x5cae, 0x5cb3, 0xdd30, 0xaf12, 0xaf11, 0xa40a, 0xaf35, 0x2f4c,
  0xc030, 0xcd43, 0xaf33, 0xaf4e, 0xaf2a, 0xaf20, 0xaf3a, 0x2f3e,
  0xaf48, 0xaf37, 0xaf3c, 0xaf3b, 0x2f39, 0xaf48, 0xa22b, 0x2f39,
  0xaf3e, 0xaf25, 0xaf2f, 0xaf51, 0xaf50, 0x2e30, 0x3b78, 0xc688,
  0x2679, 0xc576, 0xaf55, 0x2f54, 0x6304, 0xe332, 0xcbf2, 0x2f5a,
  0xbc47, 0x2f59, 0xbc47, 0x2f5c, 0x2f5d, 0xaf5e, 0x2f5b, 0x2f5d,
  0xaf5e, 0x2f5b, 0x2f5c, 0xaf5e, 0x2f5b, 0x2f5c, 0xaf5d, 0xaf60,
  0x2f5f, 0xd4a6, 0xaf66, 0xaf65, 0xaf72, 0xcdb5, 0xe6d5, 0x1efd,
  0xb58c, 0xaf68, 0x9ee2, 0x1eff, 0x2fac, 0xeae3, 0xafb9, 0x1f5b,
  0xeaf4, 0x2f83, 0xdfec, 0x2fb0, 0x2fb4, 0xafb5, 0xaf80, 0x2f91,
  0x5ff3, 0xe015, 0xc2e5, 0x9f77, 0x9f6a, 0xa40e, 0xe65f, 0x2f84,
  0x5ff3, 0xe015, 0x1ece, 0xaf9e, 0x22d1, 0xafa0, 0x1ece, 0xaf93,
  0x22d1, 0xaf95, 0xc9a6, 0x201f, 0xd5c9, 0xa065, 0x290d, 0xd986,
  0x1eff, 0x208d, 0xaf77, 0xa09c, 0xaf81, 0xa0c4, 0x2fb7, 0x3074,
  0xb0b3, 0x2f81, 0xafb5, 0x2f81, 0xafb4, 0x2fb3, 0x3074, 0xb0b3,
  0xaf7b, 0xafc4, 0xafc3, 0xb1b6, 0x9ec1, 0x31f4, 0xb1fa, 0xda8c,
  0xb07e, 0x2e94, 0xb1c9, 0xb09f, 0xb025, 0xb182, 0x2306, 0x3031,
  0xb0a4, 0xb0b4, 0xa538, 0xb13e, 0x31d0, 0xb1f7, 0xb14b, 0xb16b,
  0xb1ae, 0xb16a, 0xb0b5, 0xb134, 0x9f42, 0xb091, 0xb190, 0xb056,
  0x2c22, 0xac24, 0xafe3, 0xb060, 0x2306, 0x2fe9, 0xb0a4, 0x30e3,
  0x3374, 0x4dcf, 0x4deb, 0xce3d, 0xb1df, 0xb1cc, 0xb052, 0xb200,
  0xb046, 0x21c4, 0xb0bd, 0xb01d, 0xb02a, 0x241d, 0xb08b, 0x2379,
  0xdcc9, 0xd03b, 0xb119, 0x3085, 0xb0a6, 0xb1c7, 0x2fb3, 0x2fb7,
  0xb0b3, 0xb0e0, 0x20eb, 0x2641, 0x30aa, 0xb0e1, 0xb15f, 0xb1e8,
  0xb137, 0xb0fb, 0x30a9, 0xb0f1, 0xb0f2, 0xafd9, 0x30e5, 0xb142,
  0x3111, 0xb139, 0x3071, 0xb0a6, 0x24f2, 0x2560, 0x2586, 0x269e,
  0xa7d1, 0xb061, 0xc302, 0xb016, 0x9fd4, 0x3102, 0xda96, 0xdbef,
  0xafe2, 0xb0ca, 0x2306, 0x2fe9, 0xb031, 0x3071, 0xb085, 0x307c,
  0xb0f1, 0x20eb, 0x3076, 0xb0e1, 0xb164, 0xb1f8, 0xb173, 0xb1ab,
  0x2fb3, 0x2fb7, 0xb074, 0xaff0, 0xb005, 0xe5f7, 0x21c4, 0xb053,
  0x30a2, 0xea5a, 0x248a, 0x248c, 0xefa2, 0x1ffd, 0xbb23, 0xb075,
  0x20eb, 0x3076, 0xb0aa, 0xb03b, 0x307f, 0xb142, 0xb1fc, 0xb158,
  0xb1f2, 0xb18a, 0xb11c, 0x3159, 0xb15a, 0xb19a, 0xb163, 0x307c,
  0xb0a9, 0xb07d, 0xd822, 0xb07b, 0xd90a, 0x3096, 0xda96, 0x4609,
  0xc652, 0x3080, 0xb139, 0xb06a, 0xc231, 0xb0ec, 0xb14d, 0xb181,
  0xb1a4, 0xb192, 0x2abf, 0xd06d, 0xb1fc, 0xb006, 0xb07a, 0x3080,
  0xb111, 0xb14e, 0xa35a, 0xaffe, 0xe858, 0x307f, 0xb0e5, 0xb817,
  0xbbb7, 0xb001, 0xb120, 0x313c, 0x361a, 0xc718, 0x3174, 0xb1fe,
  0xb0e8, 0x30ed, 0xb15a, 0x30ed, 0xb159, 0x3078, 0xb185, 0xb0ef,
  0xb0ab, 0xb004, 0xb002, 0xb196, 0xd651, 0xb0ad, 0xb151, 0xae86,
  0x317d, 0xb21a, 0x317c, 0xb21a, 0xbb32, 0x21ed, 0x21f4, 0xb191,
  0xb121, 0xafe7, 0xb15f, 0xb1a9, 0xb0eb, 0xb01c, 0x21ed, 0x21f4,
  0xb17f, 0xb126, 0x2af6, 0xc644, 0x316d, 0xb197, 0xb196, 0x259c,
  0x3199, 0xbb56, 0x259c, 0x3198, 0xbb56, 0xb0ee, 0xb124, 0x22b3,
  0x22b4, 0xa2de, 0xb187, 0xb0af, 0xb003, 0xabaa, 0xafc6, 0xa766,
  0xa2e4, 0xb073, 0x2e94, 0xafdc, 0xb03f, 0xb1d4, 0x3000, 0xb1f7,
  0xb1e3, 0x2b3e, 0xb1f6, 0xb1cd, 0xb1f5, 0xd499, 0xb03c, 0xb1d1,
  0xb1e7, 0xb1e6, 0xb079, 0xb0e9, 0x2fcf, 0xb1ff, 0xb1dc, 0xb1d2,
  0x3000, 0xb1d0, 0xb0ac, 0xafcf, 0x30e7, 0xb133, 0xbb22, 0xb151,
  0xb1f4, 0xb04b, 0xb207, 0xb206, 0xb214, 0x322f, 0xb232, 0xb20b,
  0xb227, 0x3226, 0xb230, 0x317c, 0xb17d, 0xb21e, 0x5ca1, 0xdd22,
  0xb21b, 0x3218, 0xb230, 0xb217, 0xb22c, 0xb229, 0x2279, 0xa2e0,
  0x320f, 0xb232, 0x3218, 0xb226, 0x320f, 0xb22f, 0x3237, 0xb238,
  0x3236, 0xb238, 0x3236, 0xb237, 0xa384, 0xb23e, 0x236f, 0xa918,
  0xb23b, 0xb24c, 0xb24b, 0xce94, 0x33e0, 0xcd2e, 0x34b2, 0xb4c8,
  0xb2c2, 0xa728, 0x5a17, 0xdbac, 0xb34d, 0xb268, 0xe1e6, 0xa7f7,
  0x23c9, 0xb260, 0x32e1, 0xb4f4, 0xb36b, 0xb383, 0xb3da, 0xb4a6,
  0xb4fe, 0xb5bc, 0x2448, 0xc2c2, 0xa214, 0xb4ab, 0xb2cb, 0xb2d4,
  0xb2d7, 0x32e9, 0xb4c7, 0xb476, 0xb473, 0xb384, 0xb436, 0xdb77,
  0xa831, 0xb4e1, 0x32de, 0x4274, 0xd9dd, 0xb255, 0xb4d4, 0xb49a,
  0xb29b, 0xb2ff, 0x37b4, 0xb7fa, 0xb428, 0xb29c, 0xb2d6, 0xb2d5,
  0xb29d, 0xb2dd, 0xb2dc, 0xb2b5, 0xb4ec, 0x336e, 0xb4da, 0x3269,
  0x3304, 0xb4f4, 0xb50f, 0xb3c0, 0xb4c1, 0xb514, 0xb4f0, 0x34a5,
  0xc67a, 0x329e, 0xb4c7, 0xb2cf, 0xb39b, 0x32e1, 0xb4f4, 0x2f04,
  0xb335, 0x1e3e, 0x34e7, 0xd209, 0xb46f, 0xb523, 0xb397, 0xb4be,
  0xb4bb, 0xb33e, 0xb493, 0xb4cb, 0xb49f, 0xb399, 0xb4e0, 0xb3ee,
  0xb48f, 0x2f04, 0xb30a, 0xb412, 0xdf13, 0xb31f, 0xb3d2, 0x3405,
  0x34b9, 0xb52a, 0x38b1, 0x4975, 0xcd91, 0xb25e, 0xb3d1, 0xd379,
  0xb41c, 0xb488, 0xb40d, 0xb4bf, 0xb3db, 0x3417, 0xb4e3, 0xd20d,
  0xb26a, 0x32e0, 0xb4da, 0x2377, 0xadfb, 0xb03b, 0x3816, 0xb8f2,
  0x27fd, 0xb26b, 0xb2a1, 0xb31c, 0xb323, 0xb302, 0xe1c7, 0xb4d1,
  0xa30a, 0xb3ed, 0xb4c4, 0xb451, 0xb4f2, 0xb4a3, 0xb47b, 0xb414,
  0xb45c, 0xc8b0, 0xb2e3, 0xb34f, 0xb33f, 0xb26c, 0xb362, 0xb24e,
  0xb3b2, 0xb325, 0xd0cc, 0x3416, 0xb447, 0xb52c, 0xb4b3, 0xb519,
  0xb4f1, 0xb45f, 0xce3d, 0x3341, 0x34b9, 0xb52a, 0xb784, 0xb35f,
  0xb337, 0xb3bb, 0x33fa, 0xb447, 0xb363, 0x335c, 0xd490, 0xb2d3,
  0xb498, 0xb528, 0xb2a2, 0x34d5, 0xb51c, 0xb9a8, 0x3444, 0xb51d,
  0x3442, 0xb51d, 0xb504, 0x34fa, 0xd96c, 0x33fa, 0xb416, 0xb4ef,
  0xb524, 0xb3b4, 0xb3bc, 0xb402, 0x4dcf, 0x4deb, 0xce3d, 0xb4f5,
  0xb31a, 0xb2a0, 0xb29f, 0xb3ba, 0x21fb, 0xb4ca, 0xb516, 0xb35e,
  0xb326, 0xb491, 0xb490, 0xb320, 0xb42d, 0xb2c8, 0xb322, 0xb4cd,
  0xb3b8, 0x32e8, 0xc67a, 0xb26f, 0x4d55, 0x4d76, 0xcedd, 0xb29a,
  0x3251, 0xb4c8, 0xb3ff, 0xb506, 0xb4f7, 0xb4fc, 0x3341, 0x3405,
  0xb52a, 0xb51b, 0xb31e, 0xb31d, 0xb361, 0xb2e5, 0xb3b3, 0x329e,
  0xb2e9, 0x3251, 0xb4b2, 0x21fb, 0xb483, 0xb321, 0xb4a1, 0xb3a5,
  0xb2c5, 0x343a, 0xb51c, 0x32e0, 0xb36e, 0xb4fb, 0xb324, 0xb2ac,
  0xb363, 0xb52c, 0x1e3e, 0x3319, 0xd209, 0xb2df, 0xb448, 0xb2e7,
  0xb401, 0xb3b7, 0x3269, 0x32e1, 0xb304, 0xb469, 0x34b7, 0xd96d,
  0xb446, 0xb4de, 0xb4b8, 0xb50a, 0xb270, 0x9e51, 0xb445, 0xb522,
  0xb4b5, 0xb51f, 0xb4fd, 0xb2e2, 0xb522, 0xb2e6, 0xb484, 0xb400,
  0xb4ba, 0x343a, 0xb4d5, 0x3442, 0xb444, 0xb508, 0x3505, 0xb512,
  0xb31b, 0xb44a, 0xb432, 0x3341, 0x3405, 0xb4b9, 0xc234, 0x33fd,
  0xb4e5, 0xb535, 0xb534, 0xa3ce, 0xd003, 0x4b27, 0x4b56, 0x4b5e,
  0xcba3, 0x1ff2, 0x209a, 0xa2b9, 0x9f8e, 0xb575, 0x23d9, 0xb558,
  0xb559, 0xa2c4, 0x22c5, 0xa2d1, 0x5d01, 0xdd25, 0x23d9, 0xb54d,
  0xb54e, 0x3582, 0xbb5b, 0xa0d8, 0x2b66, 0x2b78, 0x3585, 0x3586,
  0xb588, 0xb578, 0xb54c, 0xb5c9, 0xb570, 0x69c6, 0x69c8, 0x6a45,
  0xea71, 0xbb5d, 0x355b, 0xbb5b, 0xbbd9, 0x2b66, 0x2b78, 0x3569,
  0x3586, 0xb588, 0x2b66, 0x2b78, 0x3569, 0x3585, 0xb588, 0x2b66,
  0x2b78, 0x3569, 0x3585, 0xb586, 0x1e9d, 0x358a, 0x6f4a, 0xef50,
  0x1e9d, 0x3589, 0x6f4a, 0xef50, 0x358e, 0xef4b, 0x1efd, 0xaf6c,
  0xd9ba, 0x358b, 0xef4b, 0xb595, 0x359e, 0xd7a4, 0xb593, 0x65d8,
  0x6b25, 0x6b26, 0x6b2a, 0xeb2d, 0x3594, 0xd7a4, 0xe159, 0xb5ac,
  0xb5a9, 0xb5b7, 0xb5b5, 0xb5b2, 0xb5ad, 0x1e8e, 0x1e8f, 0xb275,
  0xb5d2, 0xb577, 0xb5c8, 0x4c31, 0xcc4f, 0x2e1c, 0xae5f, 0x2e61,
  0xb5db, 0xb5d9, 0xc121, 0xa6f8, 0xd20a, 0x234f, 0x2354, 0xa3f6,
  0x35f9, 0xb642, 0x2164, 0x363f, 0xb6e0, 0xb698, 0x35f6, 0xb642,
  0xb680, 0xb625, 0xb664, 0xb63b, 0x2347, 0xb6fb, 0xb626, 0xb719,
  0xb62c, 0xb6c7, 0xb14e, 0xc6a8, 0xb5fe, 0xb60a, 0xb60f, 0xc0a4,
  0xb630, 0xb62f, 0xa1ac, 0xb602, 0x365d, 0xb6f8, 0xb6e8, 0x6855,
  0xe86f, 0x35f7, 0xb6e0, 0x35f6, 0xb5f9, 0xb644, 0xb643, 0xb64b,
  0xb649, 0xb6ec, 0x3681, 0xb6c9, 0xb6c4, 0xb688, 0xb689, 0xb669,
  0x363c, 0xb6f8, 0xb660, 0xb65f, 0xb5ff, 0xb6df, 0xb65a, 0xb69c,
  0x3692, 0xc520, 0xb697, 0xb5fa, 0x3653, 0xb6c9, 0xb6ab, 0x4f6a,
  0xdfa0, 0xb655, 0xb656, 0xb674, 0x4156, 0xc157, 0x367b, 0x5af3,
  0x5c19, 0x65c7, 0xe68c, 0xb5f8, 0xb66e, 0xc545, 0x36ad, 0x36cd,
  0xc6a5, 0x2386, 0x23af, 0xb6c6, 0xb6d6, 0xb6c1, 0xb682, 0x36a4,
  0xc6a5, 0x1e86, 0xc7ad, 0xb6a8, 0xb654, 0x2386, 0x23a4, 0x23af,
  0xb6a6, 0xb619, 0x3653, 0xb681, 0x40a4, 0x4167, 0x41f3, 0xc7be,
  0xb6a4, 0xa411, 0xb6a7, 0xb668, 0x35f7, 0xb63f, 0xb63d, 0xb652,
  0x3d40, 0x4cac, 0x6eaf, 0x6eb4, 0xeeb9, 0xb6f5, 0xb6f3, 0x1e66,
  0x363c, 0xb65d, 0xb6fa, 0xb6f9, 0x2347, 0xb607, 0xb6fe, 0xb6fd,
  0xa1a3, 0x22d6, 0xa2d7, 0x9f1a, 0xd8e8, 0xb717, 0xb716, 0xb60e,
  0xb722, 0xb71f, 0xb71e, 0xb71b, 0xd0e7, 0x2932, 0xb94d, 0xa273,
  0xb72f, 0x372e, 0xd853, 0xc843, 0x3a38, 0xba8f, 0xb736, 0xb735,
  0x21e0, 0xba5f, 0xbbba, 0x594d, 0x66d1, 0xe6dc, 0x3a29, 0xbb0a,
  0xb87f, 0xa72c, 0x3766, 0x3919, 0xba86, 0xe0a8, 0xb9d3, 0xb89d,
  0xb803, 0x9f86, 0xb749, 0xb94a, 0xb9aa, 0x386e, 0xc6c3, 0xa091,
  0x9e1c, 0x2eba, 0x3780, 0x37a9, 0x37d7, 0x38a5, 0xeb06, 0xe5c6,
  0x2eba, 0x377e, 0x37a9, 0x37d7, 0xb8a5, 0xb975, 0x3406, 0xb9cb,
  0xb960, 0x1e2b, 0x3860, 0x390f, 0xb930, 0x5edb, 0x5ef6, 0xdf6d,
  0xd3d3, 0xba05, 0xb85d, 0xba1e, 0xb8d7, 0xbaea, 0xb80c, 0xb898,
  0xb8d6, 0x2eba, 0x377e, 0x3780, 0x37d7, 0xb8a5, 0x39cd, 0x6397,
  0xe453, 0xb953, 0xb89f, 0x23f0, 0x3aaf, 0xd01c, 0x32d0, 0xb7fa,
  0xb827, 0xd235, 0xb822, 0xb959, 0x2eba, 0x377e, 0x3780, 0x37a9,
  0xb8a5, 0xbac3, 0xbab8, 0xb7fb, 0x231b, 0xb7fe, 0xa948, 0x3801,
  0x387a, 0xba6e, 0xb805, 0xb7ff, 0x32d0, 0xb7b4, 0xb7e5, 0xba89,
  0x231b, 0xb7e9, 0xb7f9, 0xb894, 0x37f3, 0x387a, 0xba6e, 0x3764,
  0xbad4, 0x39ae, 0xd363, 0xb7f5, 0xba19, 0x385f, 0xb8e7, 0xbadb,
  0x39de, 0xbaf3, 0xb8df, 0x37a6, 0xbae8, 0x3aaa, 0xbadf, 0x3b04,
  0xbb17, 0xba39, 0x337f, 0xb8f2, 0x21d3, 0xb144, 0xa20a, 0xb7cf,
  0xb7bb, 0x3a43, 0xcb4f, 0x39d8, 0xba23, 0xd988, 0x271d, 0xbb12,
  0xb97f, 0x2353, 0xb9d5, 0xb852, 0xb851, 0xb90e, 0x3a31, 0xbafb,
  0xb7a1, 0x3808, 0xb8e7, 0x1e2b, 0x3792, 0xb90f, 0xba48, 0xb968,
  0xba94, 0xb9bf, 0x39d7, 0xba4b, 0xba3a, 0xba9c, 0xb9f3, 0xba01,
  0xba33, 0x376f, 0xc6c3, 0xba03, 0x37f3, 0x3801, 0xba6e, 0xbf06,
  0xb746, 0x3a11, 0xcc17, 0xb9f1, 0x3973, 0xb9d1, 0x4681, 0xc682,
  0xb800, 0xb7a7, 0xb761, 0xb7ad, 0x2eba, 0x377e, 0x3780, 0x37a9,
  0xb7d7, 0x2922, 0xa923, 0x3346, 0xcd91, 0xbaae, 0xb8f6, 0xb9e4,
  0x391c, 0xbaa2, 0xbb1e, 0xaf03, 0x38cb, 0x3ab1, 0x3ac0, 0xc881,
  0x38ca, 0x3ab1, 0x3ac0, 0xc881, 0xb936, 0xb7a8, 0xb7a3, 0xb80b,
  0xba0a, 0x3808, 0xb85f, 0xca1c, 0x337f, 0xb816, 0xb8be, 0xbac2,
  0xc897, 0xb9e8, 0x1e57, 0x9e58, 0xb858, 0x1e2b, 0x3792, 0xb860,
  0x47f4, 0xc887, 0xb95a, 0xb749, 0x38c0, 0xbaa2, 0xbadd, 0xb9e7,
  0xb9f6, 0xbb0f, 0xba3f, 0xba62, 0xb792, 0xd46e, 0xb8d5, 0xd45a,
  0xb768, 0x2932, 0xb72c, 0xb7ab, 0xba62, 0xb7d5, 0xb918, 0xb78f,
  0xb986, 0xb966, 0xb965, 0xb862, 0xc243, 0xba9d, 0x9e1a, 0x3885,
  0xb9d1, 0xb781, 0xba13, 0x1e50, 0xba02, 0xb842, 0xbb16, 0xb9b2,
  0xb961, 0xbaec, 0xbada, 0x3af8, 0xbb05, 0xc253, 0x2e72, 0xae79,
  0xb43e, 0xb769, 0x3804, 0xd363, 0xb985, 0xba4a, 0xe198, 0xb864,
  0xb9d9, 0x5028, 0xe392, 0xb784, 0xb7aa, 0x3885, 0xb973, 0xb760,
  0x39f9, 0xba70, 0x2353, 0xb84c, 0x3865, 0xba4b, 0x3837, 0xba23,
  0xb9c7, 0xba9f, 0x3abb, 0xce7f, 0xb80a, 0xbab3, 0xbae7, 0xb8bf,
  0xb920, 0xb901, 0xb884, 0xb868, 0xb922, 0xb9d4, 0xb869, 0x1e50,
  0xb97d, 0xb879, 0xb79e, 0xb8e5, 0xbad3, 0x3881, 0xcc17, 0xbac1,
  0xb97c, 0xb807, 0xb7a2, 0x3837, 0xb9d8, 0x3743, 0xbb0a, 0x3a6b,
  0xbace, 0x3aa3, 0xd262, 0x385c, 0xbafb, 0xb86a, 0x1e1b, 0x23e2,
  0x3b09, 0xd5c2, 0x3734, 0xba8f, 0xb811, 0xb866, 0xcf47, 0xb92b,
  0x3830, 0xcb4f, 0xb861, 0xb9b4, 0x3865, 0xb9d7, 0xb73a, 0x392d,
  0xb955, 0x3a2a, 0xbace, 0x37f3, 0x3801, 0xb87a, 0xb9d4, 0xbae5,
  0x3ad3, 0x5263, 0xd26a, 0xbade, 0xbaa9, 0xbafd, 0xb749, 0xb7fd,
  0x3734, 0xba38, 0xcc37, 0xb863, 0x3a98, 0xd617, 0xba97, 0xb867,
  0xb96b, 0xb9da, 0x38c0, 0xb91c, 0xba2f, 0xba81, 0x380e, 0xbadf,
  0xb8bc, 0x23f0, 0xb7b1, 0x38ca, 0x38cb, 0x3ac0, 0xc881, 0xb9df,
  0xb7e0, 0xb9db, 0x38ca, 0x38cb, 0x3ab1, 0xc881, 0xba12, 0xb8f9,
  0xb7dc, 0xa1f3, 0xd030, 0x3a2a, 0xba6b, 0x3a10, 0xba79, 0xb803,
  0xbb11, 0xb988, 0xb809, 0xb91f, 0xba7c, 0x380e, 0xbaaa, 0xba71,
  0xb9e0, 0xb80c, 0xb7a5, 0xb987, 0xb80a, 0xb989, 0xbb1e, 0x385c,
  0xba31, 0xba83, 0x380f, 0xbb17, 0xb989, 0x1e1b, 0x23e2, 0x3a37,
  0xd5c2, 0x3743, 0xba29, 0xb924, 0xbad5, 0x271d, 0xb83e, 0xb984,
  0x380f, 0xbb04, 0x60c1, 0x6b30, 0xeb31, 0x38c2, 0xbafa, 0x4f3a,
  0xcf3c, 0x31fd, 0x3b53, 0x3b61, 0xea69, 0x1ffd, 0x30de, 0xda22,
  0xbb5f, 0xbb50, 0xb17e, 0xbb3e, 0xbb58, 0xe4a6, 0xbb35, 0xdb0c,
  0x23f9, 0xa606, 0xbb27, 0x3b22, 0xbb61, 0x259c, 0x3198, 0xb199,
  0xbb3b, 0x355b, 0xb582, 0xb581, 0xbb24, 0x3b22, 0xbb53, 0x3b68,
  0xbb69, 0x3b65, 0xbb69, 0x3b65, 0xbb68, 0xa24d, 0x3e0b, 0x3f80,
  0xbf81, 0x6f52, 0xef7f, 0x1e97, 0x2c81, 0x2d57, 0xbb73, 0x1e97,
  0x2c81, 0x2d57, 0xbb72, 0x2386, 0x23af, 0xbb77, 0x2386, 0x23af,
  0xbb74, 0x2e30, 0x2f52, 0xc688, 0xbb7a, 0xbb79, 0x3bb1, 0xbbb2,
  0x273d, 0x3b7f, 0x3b81, 0xbca1, 0x273d, 0x3b7e, 0x3b81, 0xbca1,
  0xa92d, 0x273d, 0x3b7e, 0x3b7f, 0xbca1, 0xbba4, 0xbb98, 0xbb9e,
  0xbbae, 0xbb8b, 0xbbab, 0xbb92, 0xbbaf, 0xbb87, 0xbb9a, 0xbb93,
  0xbba1, 0x3b7c, 0xbbb2, 0x3b7c, 0xbbb1, 0xbbc6, 0xb147, 0xb740,
  0x28f3, 0xbbbc, 0x28f3, 0xbbbb, 0xbbc1, 0x3bc0, 0x41ec, 0xdb6d,
  0xdf42, 0xbbb4, 0x233b, 0xe1ab, 0xbbcf, 0xbbce, 0x50b2, 0xd8ac,
  0xa936, 0xc562, 0xbbd8, 0xbbd7, 0xb583, 0xbbee, 0xbc08, 0xc403,
  0xbbdf, 0xbbff, 0xbbf5, 0x4266, 0xc29b, 0xbc0c, 0x3be1, 0xbc0a,
  0xbc08, 0xbc07, 0x3c17, 0x3c23, 0xc081, 0x3c14, 0x3c23, 0xc081,
  0xe6f0, 0xbc2b, 0x3c14, 0x3c17, 0xc081, 0xbc2c, 0xbc22, 0xbc29,
  0xbc33, 0xbc32, 0x3c35, 0xbc3a, 0x3c34, 0xbc3a, 0x1ecc, 0x21ab,
  0xa1b0, 0x3c34, 0xbc35, 0x232f, 0x2f59, 0x2f5a, 0xbed9, 0xbf22,
  0xbcdb, 0x3c5a, 0xbc61, 0x3c59, 0xbc61, 0x3c59, 0xbc5a, 0xbe6f,
  0xbc74, 0xbc73, 0xbd36, 0xa1b3, 0xbef8, 0x3c89, 0xc00b, 0xbc88,
  0xbca1, 0xa1b2, 0xbdfb, 0xbe9d, 0x273d, 0x3b7e, 0x3b7f, 0x3b81,
  0xbc92, 0x3cfd, 0xbfa4, 0xc043, 0xbf1a, 0xc01d, 0xbdea, 0xbec4,
  0xbe22, 0xbf59, 0x3eec, 0xcc04, 0xe63b, 0xbcb2, 0xbcb1, 0xbfd4,
  0xe711, 0xa1b5, 0x1f71, 0xc04b, 0xbc4e, 0xbeaf, 0xbfd8, 0xa76d,
  0xda3b, 0x3d99, 0xbdda, 0xa1ba, 0xa933, 0xbfa9, 0x3edd, 0xc027,
  0xc018, 0xbffc, 0xc009, 0x3e8c, 0x3f51, 0xc67a, 0x3ca2, 0xbfa4,
  0xbd87, 0xbf54, 0xa1bd, 0xc051, 0xbf54, 0xbc79, 0xcaaa, 0xb6f2,
  0xbd79, 0xbde8, 0xbdfa, 0xbf3f, 0xbf86, 0xbe5e, 0xbeae, 0xbfc1,
  0xbe2c, 0xbfae, 0x3e08, 0xbfdf, 0x26a0, 0xc00f, 0xbefb, 0xbe3e,
  0xbef8, 0xbfc3, 0xbf6f, 0xbfdc, 0x3ee8, 0x3ff1, 0xbff5, 0xbfa3,
  0x3f82, 0x3f84, 0xc013, 0xbf94, 0xbd43, 0xa1c2, 0x21c3, 0xa857,
  0xbe7c, 0xbcfe, 0xbe09, 0xbe67, 0x5385, 0xd49e, 0x3cea, 0xbdda,
  0xbfe4, 0x3e0e, 0xc006, 0xbf87, 0xbdf6, 0xbf23, 0xbf7f, 0xbe26,
  0xbeb3, 0xbe19, 0xbecc, 0xbf64, 0x3f97, 0xc900, 0xbf32, 0x3e0b,
  0x3f80, 0x3f81, 0xbfc7, 0xbf04, 0xa1c5, 0xa1c9, 0xbfb1, 0xbe7d,
  0xc5f3, 0xa1c4, 0x3cea, 0xbd99, 0xbe0c, 0x21c0, 0xbd44, 0xbca6,
  0x2a6c, 0xbedb, 0xbe7b, 0x26e6, 0x3e0a, 0xbe15, 0xbd9e, 0xbe05,
  0xbd45, 0xbc97, 0xbdf8, 0xbe34, 0x3d4e, 0xbfdf, 0xbd89, 0x26e6,
  0x3df5, 0xbe15, 0x3b6e, 0x3da9, 0x3f80, 0xbf81, 0xbde5, 0xbf2c,
  0x3d9c, 0xc006, 0xbf38, 0xbfa0, 0x2d60, 0x3eaa, 0x5c3f, 0xdc40,
  0xbf01, 0x26e6, 0x3df5, 0xbe0a, 0xc00b, 0xbef2, 0xbda3, 0xa1cf,
  0xbca8, 0xbda1, 0xbeab, 0xbd4b, 0xbe07, 0x6030, 0xe04a, 0xbd51,
  0xa1d1, 0xbd48, 0xbd8c, 0xbc64, 0xbdf3, 0xbd85, 0xbdc4, 0xc063,
  0x3ebc, 0xbfd5, 0x3ee1, 0xbeff, 0xbfda, 0xa24c, 0xbf70, 0xbffa,
  0xbf0a, 0xbf11, 0x3cfc, 0x3f51, 0xc67a, 0xa3b5, 0x3fdb, 0xd499,
  0x21c6, 0xa1d6, 0xbf91, 0xbc9f, 0x2d60, 0x3e13, 0x5c3f, 0xdc40,
  0xbe29, 0xbd49, 0x3cdd, 0xe061, 0xbda2, 0x4194, 0xe394, 0x3e7f,
  0xbfd5, 0xbca7, 0xc06d, 0xbda4, 0xd365, 0xbf77, 0x232f, 0xbc47,
  0xbefe, 0xbdeb, 0x3cf7, 0xc027, 0xbeef, 0x404e, 0x4054, 0xc067,
  0xc044, 0x3e80, 0xbeff, 0xc005, 0xbffe, 0xbfeb, 0xc064, 0x3d5c,
  0x3ff1, 0xbff5, 0xc058, 0xbfa6, 0x3caa, 0xcc04, 0xbede, 0xbe17,
  0x2364, 0x3f9b, 0xc002, 0x3c7b, 0xbd52, 0xbd50, 0xbeda, 0x3e80,
  0xbee1, 0xbe14, 0xbdaf, 0xb87c, 0xbe87, 0xbe89, 0xc055, 0xbca4,
  0xbc49, 0xbd9f, 0x1e79, 0x1e7e, 0x9e81, 0xc1b3, 0xbe0d, 0xbf44,
  0xbda8, 0xbe10, 0xbd46, 0xe88d, 0xbf31, 0xc04c, 0xc020, 0xc01f,
  0xc032, 0xbff0, 0x3cfc, 0x3e8c, 0xc67a, 0x3d01, 0xbd2f, 0xbca9,
  0x3f5c, 0xbff3, 0x3f5b, 0xbff3, 0xbda6, 0xbd54, 0xbe83, 0xc026,
  0xbed7, 0xbda0, 0x3b6e, 0x3da9, 0x3e0b, 0xbf81, 0x3b6e, 0x3da9,
  0x3e0b, 0xbf80, 0x3d67, 0x3f84, 0xc013, 0x3d67, 0x3f82, 0xc013,
  0xbd47, 0xbd9d, 0xbe9c, 0xbd69, 0xbda7, 0xbfb3, 0x2364, 0x3ef7,
  0xc002, 0xc03e, 0x21db, 0xa1dc, 0xbe11, 0xbd63, 0x3ca2, 0xbcfd,
  0xbeea, 0xbcf6, 0xbd4d, 0xc032, 0xbdc0, 0xbf9a, 0xbd4a, 0xbd53,
  0xbda9, 0x4028, 0xc02c, 0xc015, 0xbcb5, 0x3e7f, 0xbebc, 0xbcde,
  0x3e81, 0xc02f, 0x3e95, 0xd499, 0xbd55, 0x3d4e, 0xbe08, 0xbd9b,
  0xbee5, 0xbf4d, 0x3d5c, 0x3ee8, 0xbff5, 0x3f5b, 0xbf5c, 0x3d5c,
  0x3ee8, 0xbff1, 0x65ca, 0xe614, 0xbe85, 0xbcfa, 0xbee4, 0x2364,
  0x3ef7, 0xbf9b, 0xbee2, 0x3d9c, 0xbe0e, 0xbcfb, 0x3c88, 0xbe16,
  0xbd4f, 0x3d67, 0x3f82, 0xbf84, 0xbfd2, 0xbcf8, 0xbca5, 0xbf47,
  0xbf46, 0xbf74, 0x3cf7, 0xbedd, 0x3fd1, 0xc02c, 0x3fd1, 0xc028,
  0xbfda, 0xaf25, 0x3f4b, 0xbfb0, 0xbf9c, 0xbca3, 0xbee0, 0x1f71,
  0xbcd5, 0xbf45, 0x3edf, 0xc054, 0xc05d, 0xbd12, 0x3edf, 0xc04e,
  0xbf13, 0xbee9, 0xc04f, 0xbe7e, 0xbee6, 0xbedf, 0xbec5, 0x2149,
  0xc097, 0xc1c8, 0x670a, 0x671b, 0xe748, 0x4ac3, 0xcac8, 0x40fe,
  0xdd64, 0x407e, 0xc0d6, 0x407d, 0xc0d6, 0xc1e6, 0xc16c, 0x3c14,
  0x3c17, 0xbc23, 0x4210, 0x6229, 0xe46a, 0x23cd, 0xa3db, 0xc08f,
  0xc08e, 0x2149, 0xc06e, 0xc152, 0xc197, 0x362d, 0x36cc, 0x4167,
  0x41f3, 0xc7be, 0x4832, 0x491f, 0xc92e, 0xc0f1, 0xcf39, 0xeede,
  0x1e3a, 0xc232, 0x4149, 0x632c, 0xe34a, 0xc1be, 0xc20d, 0x41d7,
  0x421b, 0xc224, 0xc0f4, 0xc16c, 0x9e4c, 0x407d, 0xc07e, 0xc1ed,
  0xc159, 0xc169, 0x413c, 0xc1d2, 0x41c1, 0xc217, 0xc1f4, 0xc1d9,
  0xc1fc, 0xc1b1, 0xc0af, 0xc0c3, 0xc1a2, 0x407b, 0xdd64, 0xe2b7,
  0x21de, 0x4155, 0x4188, 0xc199, 0xe2b2, 0x9e9f, 0x4130, 0xc1c4,
  0xc165, 0xc1dc, 0xc1fe, 0x41cc, 0xc1d3, 0xb5e0, 0xc133, 0xc162,
  0x4114, 0xc1c4, 0xc126, 0x40e7, 0xc1d2, 0xe35b, 0x40bc, 0x632c,
  0xe34a, 0xc16e, 0xc09c, 0x21de, 0x4108, 0x4188, 0xc199, 0xb696,
  0xb696, 0xc0df, 0x412d, 0xd315, 0xc115, 0x36cc, 0x40a4, 0x41f3,
  0xc7be, 0xc0e6, 0x4080, 0xc0ca, 0xc151, 0x417e, 0xc185, 0x4174,
  0xc185, 0x4174, 0xc17e, 0x21de, 0x4108, 0x4155, 0xc199, 0xc1fb,
  0xd367, 0x3eb6, 0xe394, 0xc09d, 0x21de, 0x4108, 0x4155, 0xc188,
  0xc0fd, 0xc0ed, 0xe88e, 0xbf2b, 0xc1ba, 0xc1b9, 0xc0bd, 0x40e8,
  0xc217, 0x4114, 0xc130, 0xc06f, 0x411a, 0xc1d3, 0xac1e, 0x48f7,
  0xcca6, 0x40e7, 0xc13c, 0x411a, 0xc1cc, 0xedf0, 0x40c2, 0x421b,
  0xc224, 0xc0eb, 0xc116, 0x25b6, 0xd425, 0xc07f, 0xbbc1, 0xc0db,
  0x36cc, 0x40a4, 0x4167, 0xc7be, 0xc0e9, 0xc18f, 0xc0ec, 0xc118,
  0xd000, 0xc0c1, 0xc089, 0x40e8, 0xc1c1, 0x40c2, 0x41d7, 0xc224,
  0x40c2, 0x41d7, 0xc21b, 0x9e89, 0xb11b, 0x1e3a, 0xc0ba, 0xb52b,
  0xc23a, 0xc237, 0x9fce, 0x2c12, 0x2c13, 0xac14, 0x9e2c, 0xae8a,
  0xb96a, 0xa889, 0xa899, 0xcb3a, 0xc258, 0x4255, 0x4a93, 0x4a97,
  0xcabb, 0xb99c, 0x424e, 0x4a93, 0x4a97, 0xcabb, 0xc24d, 0xc25c,
  0xc25b, 0xab83, 0x3c02, 0xc29b, 0x1f10, 0xa0a0, 0xb2b5, 0xc27d,
  0x42a0, 0xc2a7, 0xc275, 0xc282, 0xc281, 0xa954, 0xc2a2, 0xd366,
  0x3c02, 0xc266, 0x427a, 0xc2a7, 0xc28a, 0x427a, 0xc2a0, 0xc2ad,
  0xc2ac, 0xa3d0, 0xdc7a, 0x5c7b, 0xdc8b, 0xc2c0, 0xc377, 0xc341,
  0xc336, 0xc2b6, 0x2448, 0xb282, 0xc2fd, 0xdc7e, 0xc329, 0xc36e,
  0xc370, 0xdc89, 0xaf87, 0xc368, 0xc2f9, 0xc345, 0xc36a, 0xc319,
  0xc344, 0xc33b, 0x430d, 0xdc8d, 0xc2ed, 0xc2c8, 0xc318, 0xb08d,
  0xc36b, 0xd653, 0xdc8e, 0x42f8, 0xdc8d, 0x431f, 0xc375, 0xc37c,
  0xc2fe, 0xc2f0, 0x430e, 0xc375, 0xc380, 0xc33f, 0xc2cc, 0xdc6c,
  0xdc93, 0xd75f, 0xc37b, 0xdc92, 0xc366, 0xc371, 0xc2b9, 0xc2f2,
  0x4328, 0xd76f, 0xc2b8, 0xc2f1, 0xc2ee, 0xe3e1, 0x2956, 0x2968,
  0xa96c, 0xdc98, 0xee9e, 0xc359, 0xc358, 0x217d, 0xc378, 0xc332,
  0xc2ec, 0xc2ef, 0x4303, 0xc381, 0xc37a, 0xc2dd, 0xc2de, 0xc335,
  0xd3b7, 0x430e, 0xc31f, 0xc2b7, 0x217d, 0xc363, 0xc36d, 0xc32e,
  0xc315, 0xdc9b, 0xc321, 0xc36b, 0x583c, 0xdc9c, 0xa999, 0xa35b,
  0xc4a3, 0xc4b5, 0xc452, 0xc46a, 0x43cf, 0xc474, 0xcfeb, 0xc44b,
  0xc4b0, 0xc3fe, 0xc472, 0xc447, 0xc4bd, 0x4409, 0x4460, 0xc4a2,
  0xc3ce, 0xc3cd, 0x43a8, 0xc474, 0xc43a, 0xc4cf, 0x4434, 0xc439,
  0x2b9d, 0x2bda, 0x2bf3, 0xabf6, 0xa72d, 0xc4ab, 0x448e, 0xc4d4,
  0xc43f, 0xc894, 0xc3b0, 0xbbec, 0xc46f, 0x43cb, 0x4460, 0xc4a2,
  0x4483, 0xc4c8, 0xc4a1, 0xc489, 0x4411, 0xc463, 0x4410, 0xc463,
  0xe6d5, 0x43e1, 0xc439, 0x43e1, 0xc434, 0xc3d0, 0xc4ca, 0xc3f2,
  0xc3b3, 0xc3ae, 0xc39a, 0xc8af, 0x43cb, 0x4409, 0xc4a2, 0x4410,
  0xc411, 0xc476, 0xc4a1, 0xd3b9, 0xc39b, 0xc405, 0xc4cc, 0xc3b1,
  0x43a8, 0xc3cf, 0xc464, 0xc4a6, 0xc4b8, 0x440d, 0xc4c8, 0xc4bf,
  0xc40f, 0x43f1, 0xc4d4, 0x440e, 0xc468, 0x43cb, 0x4409, 0xc460,
  0xc391, 0xc477, 0xc3f0, 0xc3af, 0xc399, 0xc478, 0x28d0, 0xc3ba,
  0xc487, 0x440d, 0xc483, 0xc4da, 0xc43c, 0xc470, 0xc3d1, 0xc4da,
  0x43f1, 0xc48e, 0x44c9, 0xc4d2, 0xaf01, 0xcf38, 0xc515, 0xc50c,
  0x4501, 0xcf3e, 0xc506, 0x44f6, 0xcf3e, 0xc4f7, 0xc4ef, 0x287c,
  0x4816, 0xc8da, 0xc4ee, 0x4f42, 0xcf4c, 0x2617, 0x2690, 0xac1d,
  0xb674, 0x1ea7, 0xc523, 0x1ea7, 0xc522, 0x4a4c, 0x52cf, 0xd607,
  0x2b81, 0x2bcd, 0x2bd5, 0x2bd7, 0x2bdc, 0xabe7, 0xe6fb, 0x9fbd,
  0x2722, 0xc53c, 0x2212, 0x2283, 0x2290, 0x456b, 0xc575, 0x2722,
  0xc53a, 0xc54e, 0xc55d, 0x4559, 0xc571, 0xb6a2, 0x1ea9, 0x4552,
  0x455d, 0xc55e, 0xd015, 0x283a, 0xc54d, 0x283a, 0xc54c, 0xc53d,
  0xc560, 0x1ea9, 0x4546, 0x455d, 0xc55e, 0x4544, 0xc571, 0xa959,
  0x1ea9, 0x4542, 0x4546, 0x4552, 0xc55e, 0x1ea9, 0x4546, 0x4552,
  0xc55d, 0xc551, 0xa793, 0xbbd5, 0xc567, 0xc565, 0xc56a, 0xc568,
  0x453b, 0xc575, 0xaf02, 0x4544, 0xc559, 0x23e0, 0x4582, 0x4589,
  0xc58a, 0xc587, 0x453b, 0xc56b, 0xaf53, 0xc585, 0xa84d, 0x23e0,
  0x4573, 0x4589, 0xc58a, 0x457a, 0xc586, 0xc585, 0xc574, 0x23e0,
  0x4573, 0x4582, 0xc58a, 0x23e0, 0x4573, 0x4582, 0xc589, 0x458f,
  0xde08, 0x458e, 0xde08, 0xc664, 0xc642, 0xc627, 0xc658, 0xc60d,
  0xd0ac, 0xc667, 0xc632, 0xc621, 0xc60b, 0x46b0, 0xe764, 0xc5fe,
  0xc665, 0xc670, 0xc5d9, 0xc662, 0x24d1, 0x2516, 0x255e, 0xc602,
  0xc5c9, 0xc646, 0xc626, 0xc613, 0xc647, 0xd139, 0x45f3, 0xeebb,
  0x3dcb, 0x45f2, 0xeebb, 0xc661, 0xc5fa, 0xc5f9, 0xc5b4, 0x24d1,
  0x2516, 0x255e, 0xc5d6, 0xc649, 0xc62e, 0x3108, 0xc652, 0xc5af,
  0xc5a1, 0xc5ea, 0xa591, 0xc61e, 0xc63b, 0xc617, 0xc5ae, 0xc645,
  0xc5e9, 0xc59f, 0xc65f, 0xc671, 0xc606, 0xc5ad, 0xc63b, 0x4618,
  0xc63a, 0xc66e, 0xc66d, 0xc597, 0x2af6, 0xb194, 0xc624, 0xc5e8,
  0xc5eb, 0xc605, 0x3108, 0xc609, 0x4670, 0xd1c3, 0xc5a0, 0xc669,
  0xc62a, 0xc5f4, 0xc5d2, 0xc66c, 0xc596, 0xc5c7, 0xc5ac, 0xc65e,
  0xc672, 0xc663, 0xc63f, 0xc63e, 0xd1de, 0x45c8, 0x4655, 0xd1c3,
  0xc62b, 0xc66b, 0xc675, 0xc674, 0x23d1, 0x32e8, 0x34a5, 0x3cfc,
  0x3e8c, 0x3f51, 0x467c, 0x6166, 0x6197, 0xe1b1, 0xa0dc, 0x23d1,
  0xc67a, 0x388d, 0xc682, 0x388d, 0xc681, 0xdc8c, 0x2e30, 0x2f52,
  0xbb78, 0xc690, 0x2373, 0xa37d, 0xdccb, 0xc68b, 0xc69a, 0x21d2,
  0xc691, 0xc6a1, 0xc69e, 0x36a4, 0xb6ad, 0xb61f, 0x45b1, 0xe764,
  0xc6ba, 0x46b8, 0xc6b9, 0xef13, 0x46b2, 0xc6b9, 0x46b2, 0xc6b8,
  0xc6b1, 0xef13, 0xc6c8, 0x376f, 0xb86e, 0xc6cd, 0xc6c1, 0x4f3d,
  0x6262, 0xe4b5, 0xc897, 0xc6c7, 0xc6de, 0x2869, 0xee7d, 0x46e3,
  0xdb7c, 0x5462, 0xd4cb, 0xc6dc, 0xc6e4, 0xcc20, 0xc6d7, 0xc6cf,
  0xac3d, 0x46d1, 0xdb7c, 0xc6d8, 0xa362, 0x5361, 0xd62f, 0x1eff,
  0xa023, 0xc742, 0x23bf, 0xce23, 0xc798, 0x5996, 0xd9c6, 0xb14e,
  0xc71f, 0xc71e, 0xc726, 0xc725, 0xc72f, 0xc7d3, 0x472b, 0xc787,
  0xc760, 0x1e51, 0x1f17, 0xd846, 0xd457, 0xc75c, 0xc709, 0xa6f0,
  0xc75e, 0xc7bc, 0xc741, 0xc750, 0xc737, 0xc765, 0xc764, 0xd9a9,
  0x23e1, 0xa8e1, 0xc7c1, 0xc7b6, 0xc72f, 0xc79e, 0xcff3, 0xc70d,
  0xc792, 0xc7aa, 0xc7d2, 0xc7da, 0xc7a0, 0x1e86, 0xb6b8, 0xc7d9,
  0xc786, 0xc751, 0x36cc, 0x40a4, 0x4167, 0xc1f3, 0xc785, 0xd499,
  0xc7a2, 0xc72c, 0xc7b0, 0xc7a9, 0xcb36, 0x47e7, 0xda20, 0x9faf,
  0x47e4, 0xda20, 0xc7ef, 0xc7eb, 0x3917, 0x4887, 0xc8f8, 0xc8ef,
  0xc92c, 0x483f, 0x4926, 0x6271, 0x62db, 0xe45b, 0xc8ad, 0xc8bc,
  0xc8c7, 0xc88e, 0x287c, 0x450e, 0xc8da, 0xc868, 0xc86f, 0xc8b8,
  0xa38e, 0xc8aa, 0xc8d6, 0x40ae, 0x491f, 0xc92e, 0xc92a, 0xc931,
  0xc92b, 0x47ff, 0x4926, 0x6271, 0x62db, 0xe45b, 0xc90e, 0xc85c,
  0xb731, 0xc8a9, 0xc864, 0xc8fd, 0xc8d1, 0xc904, 0xc841, 0xc856,
  0xc817, 0xc912, 0x27c6, 0x48ba, 0xc8bb, 0xc81a, 0xc8e0, 0x6e78,
  0x6e7b, 0xee7c, 0x38ca, 0x38cb, 0x3ab1, 0xbac0, 0x3917, 0xc7f4,
  0xc919, 0xc815, 0xc3f7, 0x27fc, 0x2d0e, 0xad5c, 0x27e6, 0x3900,
  0xc6cc, 0xc8e7, 0xc8e3, 0x2ca9, 0x2d52, 0xad53, 0xe7a8, 0xc855,
  0xc827, 0xc800, 0xc459, 0x33bd, 0xde2b, 0x6e78, 0xee7c, 0xc81c,
  0x486e, 0xc8bb, 0x486e, 0xc8ba, 0xc801, 0xc920, 0xc812, 0xc859,
  0xe695, 0xa387, 0xc82c, 0x287c, 0x450e, 0xc816, 0xc875, 0xc89c,
  0xc89b, 0xc7f6, 0xc1d0, 0xc7f4, 0xeec4, 0xc857, 0xbda7, 0xc85a,
  0x6669, 0x667a, 0xe6aa, 0x2760, 0xa89c, 0xc840, 0xc86a, 0xc88d,
  0x40ae, 0x4832, 0xc92e, 0xc8c1, 0x47ff, 0x483f, 0x6271, 0x62db,
  0xe45b, 0xc83a, 0xc83e, 0xc7fe, 0x40ae, 0x4832, 0xc91f, 0xc83b,
  0xc93b, 0xc93a, 0xc9ae, 0xc9b4, 0xc9a9, 0xc9a1, 0xa996, 0xc995,
  0xc9d8, 0xc9b0, 0xc98e, 0xb346, 0xc9b1, 0xc98d, 0xc984, 0xca1f,
  0xc97f, 0xc9aa, 0xc978, 0xc96f, 0xc94e, 0xc943, 0xafa1, 0xc940,
  0xc985, 0xc93c, 0xc962, 0xc977, 0xc93f, 0xe6e2, 0xc9c3, 0xa3b6,
  0xc9bf, 0xca08, 0x527a, 0x52b8, 0x541f, 0x54fa, 0xd5dd, 0xcc7c,
  0xae74, 0x49cc, 0x4a50, 0x67a6, 0x67a7, 0xef9d, 0x49cb, 0x4a50,
  0xef9d, 0xca2e, 0xd018, 0x4a09, 0xccb3, 0xcc83, 0xa3ea, 0xe8eb,
  0xc955, 0xca4d, 0x4a31, 0xca6a, 0xca2d, 0xca62, 0xca60, 0x4a3e,
  0x4a3f, 0x55c1, 0xd5f3, 0xca0e, 0x4a5e, 0xca6d, 0xc9c6, 0x49d4,
  0xccb3, 0xca05, 0x4cba, 0xd5ad, 0x4a3a, 0xca49, 0xb8f1, 0xc980,
  0xca4c, 0xc9f8, 0xc9cd, 0x49f0, 0xca6a, 0xca3b, 0x4a4f, 0xca69,
  0x4a1a, 0xca49, 0xca32, 0x4a01, 0x4a3f, 0x55c1, 0xd5f3, 0x4a01,
  0x4a3e, 0x55c1, 0xd5f3, 0x4cd3, 0xdc37, 0xca57, 0x4c87, 0xcce0,
  0x4a1a, 0xca3a, 0x4526, 0x4a23, 0x52cf, 0xd607, 0xc9ef, 0x6834,
  0xe896, 0x4a33, 0xca69, 0x49cb, 0x49cc, 0xef9d, 0xca61, 0xca42,
  0x4a06, 0xca6d, 0xc9fe, 0xca51, 0xc9fd, 0xca70, 0x6839, 0x683a,
  0x683d, 0xe893, 0x4a33, 0xca4f, 0x49f0, 0xca31, 0xd3b7, 0x4a06,
  0xca5e, 0xca63, 0x4aae, 0xcac6, 0xab86, 0xe631, 0xab8a, 0xcaca,
  0xcab3, 0xcac5, 0xcab5, 0x4aaf, 0xcab0, 0x424e, 0x4255, 0x4a97,
  0xcabb, 0x424e, 0x4255, 0x4a93, 0xcabb, 0xcac4, 0xcaa9, 0xcaba,
  0xcac7, 0xca9d, 0xbd3c, 0xde30, 0x2be0, 0xcab6, 0x4a77, 0xcac6,
  0x4a91, 0xcab0, 0x4a91, 0xcaaf, 0xca8a, 0xca8e, 0x2be0, 0xcaad,
  0xcaa5, 0x424e, 0x4255, 0x4a93, 0xca97, 0x4076, 0xcac8, 0xca9c,
  0xca8d, 0x4a77, 0xcaae, 0xcaa6, 0x4076, 0xcac3, 0xca83, 0xa947,
  0x4aea, 0xdc4e, 0x9f2b, 0x6f8d, 0xef92, 0x1e26, 0x20a1, 0xae76,
  0x4af6, 0xcaf8, 0x9fdf, 0x5abc, 0xdc0a, 0x4ad6, 0xdc4e, 0x4ade,
  0xcaf8, 0x4ade, 0xcaf6, 0xcbea, 0xcbe4, 0x4b4d, 0xcbb0, 0x2173,
  0xa4b2, 0xcb46, 0xcb67, 0xcb8d, 0x218a, 0x218c, 0x3547, 0x4b56,
  0x4b5e, 0xcba3, 0x9f53, 0x4bc4, 0xd303, 0xc7e2, 0x424b, 0xcb8b,
  0x4bed, 0xcc60, 0xcc69, 0xcbb2, 0xcb14, 0xd9d4, 0x4b0b, 0xcbb0,
  0x3830, 0xba43, 0xcb7a, 0xcbc9, 0x3547, 0x4b27, 0x4b5e, 0xcba3,
  0xcc06, 0xcb82, 0xcbf3, 0xcbe9, 0xcc39, 0xcb8f, 0x3547, 0x4b27,
  0x4b56, 0xcba3, 0xcb15, 0xcbe0, 0xcc4c, 0xcb50, 0xcbd4, 0x4c3d,
  0x4c56, 0xcc64, 0xcbe0, 0xcc21, 0xcb59, 0xcbe6, 0x00f6, 0x1e2a,
  0xa00b, 0xcb3a, 0xcb1f, 0xcb5d, 0xae1a, 0xcc59, 0xa284, 0x3547,
  0x4b27, 0x4b56, 0xcb5e, 0x4c00, 0xd536, 0xcbcb, 0xcc5c, 0xcc6e,
  0xcc1e, 0xcc2b, 0xcbdb, 0x4b0b, 0xcb4d, 0xcb45, 0xd282, 0x4b35,
  0xd303, 0xd483, 0xcb51, 0x2327, 0xcba7, 0xad4c, 0xcc23, 0xcc0d,
  0xcb7c, 0xcc46, 0xcbac, 0x4b71, 0xcb7f, 0xcc12, 0xcb03, 0xcb86,
  0x4b5b, 0x4c01, 0xcc6d, 0xcafe, 0x4b3c, 0xcc60, 0xcc43, 0xcc5b,
  0xcc6c, 0xaf57, 0xcb5a, 0x4ba6, 0xd536, 0x4be9, 0xcc6d, 0x3caa,
  0xbeec, 0xcb58, 0xcbd3, 0x4c14, 0xd4d1, 0xcbe1, 0x4c11, 0xd4d1,
  0xcc6a, 0x3881, 0xba11, 0xcc2b, 0xcbaa, 0xc6d9, 0xcb80, 0xcbd1,
  0xa142, 0x4bab, 0xcc18, 0x35d7, 0xcc4f, 0xba90, 0xcb5c, 0xcb7e,
  0xae18, 0x4c52, 0xcc55, 0xcc5f, 0xcbee, 0xcbd7, 0xcb79, 0x35d7,
  0xcc31, 0xcc58, 0xcc40, 0x55ae, 0xd5ea, 0xcc40, 0x4b7e, 0xcc64,
  0x4c50, 0xd5e4, 0xcb93, 0xcbef, 0xcba8, 0xcc41, 0x4b3c, 0xcbed,
  0x4b7e, 0xcc56, 0xcb3e, 0xcc16, 0xcbf1, 0x4be9, 0xcc01, 0xcba9,
  0x2401, 0xefa5, 0xccf4, 0xe85e, 0xc9c8, 0xc9d5, 0x4a45, 0xcce0,
  0xccb9, 0xeea4, 0xeecf, 0x5083, 0xd085, 0xccf6, 0xccf2, 0xccb5,
  0x1ffc, 0xeb3b, 0xc1d0, 0x2986, 0x299d, 0x2a24, 0xccda, 0xccde,
  0x36f2, 0x6eaf, 0x6eb4, 0xeeb9, 0xcce7, 0x49d4, 0xca09, 0xcca4,
  0xcc8b, 0xca17, 0xccc9, 0xccdd, 0x6762, 0x6eaa, 0x6eab, 0x6eb5,
  0xeeba, 0xe931, 0xccbd, 0xe92c, 0x4a40, 0xdc37, 0xe93b, 0x2986,
  0x299d, 0x2a24, 0xcca7, 0xccc1, 0xccaa, 0x4a45, 0xcc87, 0xe94e,
  0xccae, 0x26e2, 0xa6e3, 0xcc9d, 0xcc74, 0xcc9c, 0x1e1d, 0x4cf9,
  0x4d72, 0xce9f, 0x4cf8, 0xce9f, 0xccfe, 0x1fc2, 0x4e18, 0x4e4b,
  0xce6b, 0x229f, 0xccff, 0x4cfa, 0xcea0, 0x229f, 0xccfc, 0xceaa,
  0xcea3, 0xcea6, 0xcea2, 0xcea1, 0xcea5, 0xcea8, 0xceab, 0xceb9,
  0xceb3, 0xcebd, 0xcebe, 0xceaf, 0xceb0, 0xcebc, 0xceb1, 0x4d8b,
  0xceae, 0x2e0b, 0xceb8, 0xcea7, 0xceb7, 0xcead, 0x4d4d, 0xceb4,
  0xceba, 0xcdb7, 0xcd2e, 0xcdca, 0x4da2, 0xcef8, 0x324e, 0xcd25,
  0x4e32, 0x4e8d, 0x4e9d, 0xcf27, 0xcec6, 0xcec2, 0xcec1, 0xcec5,
  0xcebb, 0xcecd, 0xcec0, 0xcecb, 0xced0, 0xcecc, 0xcec8, 0xaf26,
  0xcec4, 0xcd97, 0x4eca, 0xe77d, 0xae15, 0xcea9, 0x4d93, 0xcecf,
  0xcd1d, 0xced7, 0xcec1, 0xced3, 0x34a7, 0x4d76, 0xcedd, 0xce8a,
  0xcdea, 0x4e27, 0xcee6, 0x4ed4, 0xd8b4, 0xcede, 0xcedc, 0xceda,
  0x4dab, 0x4dda, 0x4ebf, 0xcf10, 0xd32f, 0xced9, 0xced2, 0xced6,
  0x4d82, 0xcedf, 0x1e1d, 0xccf8, 0xcedb, 0x4e6a, 0xced8, 0x34a7,
  0x4d55, 0xcedd, 0xce36, 0x4e6d, 0xd812, 0xcee2, 0xced1, 0x4d71,
  0xcedf, 0xcee1, 0xcee0, 0xcee8, 0xcee3, 0xcd18, 0xcee4, 0xcee5,
  0x3346, 0xb8b1, 0x4d4c, 0xcecf, 0xcd45, 0x4e7c, 0xcee7, 0x4e8c,
  0x4eed, 0xdce1, 0xcefc, 0xcf0d, 0x4dd1, 0xceff, 0x4d2c, 0xcef8,
  0xcefb, 0x4d64, 0x4dda, 0x4ebf, 0xcf10, 0xcef6, 0xcef4, 0xcef9,
  0xcefe, 0xceb2, 0xcf51, 0x4e43, 0xcef7, 0x23d5, 0xcf00, 0xaf69,
  0xcd23, 0xceb6, 0xcefa, 0xceee, 0xcefd, 0x4e5b, 0xcef0, 0xceeb,
  0xcef5, 0xcef2, 0xcf01, 0xcd27, 0xceef, 0xcde1, 0x303b, 0x3460,
  0x4deb, 0xce3d, 0xce41, 0x4da0, 0xceff, 0x4dd6, 0xceea, 0xcef1,
  0xce83, 0x4dd2, 0xceea, 0xcf03, 0xcf04, 0xcf02, 0x4d64, 0x4dab,
  0x4ebf, 0xcf10, 0xcef5, 0xcf09, 0xcf0e, 0xcf14, 0x4dcd, 0xcf17,
  0x4e01, 0xcf18, 0xd913, 0xcf0c, 0xcf16, 0xcf13, 0xcd5a, 0x303b,
  0x3460, 0x4dcf, 0xce3d, 0xcf05, 0xceac, 0xcf11, 0xcf08, 0xcec3,
  0xcf0f, 0xcf07, 0xd1f4, 0x4e15, 0xcf0a, 0x4de3, 0xcf18, 0x4e69,
  0xcef3, 0xd426, 0xcf19, 0xcf22, 0xcf12, 0xcec9, 0xcf23, 0x4dfc,
  0xcf0a, 0xcf1e, 0x4cfb, 0x4e4b, 0xce6b, 0xcf1a, 0xcf1c, 0xcf1f,
  0xcf1b, 0x23bf, 0xc70c, 0x4e31, 0xceb5, 0x4d5b, 0xcee6, 0xae4c,
  0xcf1d, 0xcf21, 0xcf29, 0x4e26, 0xceb5, 0x4d2f, 0x4e9d, 0xcf27,
  0xcea4, 0xcf26, 0x4d77, 0xe9bd, 0xcf15, 0xcf25, 0x303b, 0x3403,
  0x3460, 0x4dcf, 0xcdeb, 0xcee9, 0xcdd0, 0x4db3, 0xcef7, 0x4e70,
  0xcf2b, 0xcf2a, 0xce66, 0x4e8e, 0x4e96, 0xcea4, 0x4cfb, 0x4e18,
  0xce6b, 0x4e61, 0xcee3, 0xcf2f, 0xcec7, 0xcf2e, 0x1f1e, 0xa098,
  0xcffb, 0xcf2d, 0xcdbd, 0xced5, 0x4e4d, 0xcee3, 0xcf0b, 0xce82,
  0xce48, 0x4e04, 0xcef3, 0x4d75, 0xced8, 0x4cfb, 0x4e18, 0xce4b,
  0x4d78, 0x5327, 0xd812, 0x4f30, 0xe7c1, 0xcf33, 0x4e45, 0x4f2b,
  0xcf32, 0xcf34, 0xcece, 0x4d99, 0xcee7, 0xcf24, 0xcf31, 0xb9db,
  0xce64, 0xcdd5, 0xe8a3, 0xcf2c, 0x4e98, 0xcf35, 0x4d56, 0xcea9,
  0x4d9a, 0x4eed, 0xdce1, 0xcd2f, 0x4e4a, 0x4e96, 0xcea4, 0x4e92,
  0xcf20, 0x4e8f, 0xcf20, 0xcf28, 0xb24d, 0x4e4a, 0x4e8e, 0xcea4,
  0x4e89, 0xcf35, 0xcf06, 0x4d2f, 0x4e32, 0xcf27, 0x4cf8, 0xccf9,
  0xccfe, 0xcd06, 0xcd05, 0xcd02, 0x4e34, 0x4e4a, 0x4e8e, 0xce96,
  0xcd07, 0xcd04, 0xcd1a, 0xcd08, 0x4d4b, 0xce8a, 0xcd00, 0xcd09,
  0xcdef, 0xcd1c, 0xcd18, 0xcd14, 0xcd15, 0xcd17, 0xcdb1, 0xcd0d,
  0xcd1d, 0x4e26, 0xce31, 0xcdb8, 0xcd1b, 0xcd19, 0xcd0b, 0xcd21,
  0xcd35, 0xcd16, 0xcd10, 0xcd13, 0x4d64, 0x4dab, 0x4dda, 0xcf10,
  0xcd3a, 0x4d32, 0xcd4f, 0xcd31, 0xcdf4, 0xcd44, 0xcd33, 0xcd30,
  0xce54, 0xcd42, 0xce10, 0xcd46, 0xcd3c, 0xcd40, 0xcd39, 0xce79,
  0x4d4c, 0xcd93, 0xcd3f, 0xcd81, 0xcd68, 0xcd50, 0xcd5d, 0x4e5e,
  0xe076, 0xcd70, 0xcd4e, 0x4d75, 0xce6a, 0xcd66, 0xcd62, 0xcd73,
  0xcd61, 0x34a7, 0x4d55, 0xcd76, 0xcd5e, 0x4d71, 0xcd82, 0xcd86,
  0xcd83, 0xcd79, 0x4d89, 0x4e4d, 0xce61, 0xcd8c, 0xcd8f, 0x4d5b,
  0xce27, 0x4d99, 0xce7c, 0xcd88, 0xce3e, 0x4dd2, 0xcdd6, 0xcdbe,
  0x4d9a, 0xce8c, 0xcdba, 0xcdcb, 0xcdbd, 0xcdd4, 0xcdc4, 0x4e04,
  0xce69, 0xcdad, 0x4dbf, 0xcddc, 0xcdac, 0x4db3, 0xce43, 0x4d2c,
  0xcda2, 0xcdaf, 0xcdb9, 0xcda3, 0xcd9c, 0xcdbb, 0xcdb0, 0x4da0,
  0xcdd1, 0xcdb4, 0xcdc7, 0xcdd9, 0xcdd7, 0xcdd8, 0xcdec, 0xce9c,
  0xcdf9, 0xcdf2, 0xcddd, 0x4dfc, 0xce15, 0xce62, 0xcde6, 0xcd9e,
  0xcdde, 0xcdf6, 0x4d64, 0x4dab, 0x4dda, 0xcebf, 0xcdf1, 0xce0b,
  0xcde9, 0xcde0, 0xce37, 0xcde8, 0xcde1, 0x4de3, 0xce01, 0xce09,
  0xce1b, 0xce1f, 0xce1d, 0xce2b, 0xce17, 0xce1e, 0x4e8f, 0xce92,
  0xce2d, 0xce0a, 0xce11, 0xce7d, 0xce39, 0xce35, 0x4d2f, 0x4e32,
  0xce9d, 0xce93, 0xce2e, 0xce46, 0x4e45, 0xce70, 0xce88, 0xce5a,
  0xce55, 0xce52, 0x4e6e, 0xe7c1, 0xce7e, 0xce70, 0xce6f, 0xce73,
  0x4e89, 0xce98, 0x4f3b, 0x4f50, 0xe475, 0xa378, 0xc4e8, 0xc0b0,
  0x3b20, 0xcf3c, 0xcf36, 0x3b20, 0xcf3a, 0x46cb, 0x6262, 0xe4b5,
  0x44f6, 0xc501, 0x4516, 0xcf4c, 0xba3d, 0x275b, 0x28b0, 0x28b5,
  0x28dc, 0xcf4e, 0x4516, 0xcf42, 0x275b, 0x28b0, 0x28b5, 0x28dc,
  0xcf48, 0x4f36, 0xe475, 0x4db2, 0x4f52, 0xcf53, 0x4f51, 0xcf53,
  0x4f51, 0xcf52, 0x2570, 0x26c9, 0xcf85, 0x4f70, 0xcf78, 0xcf77,
  0x3683, 0xdfa0, 0x4f5a, 0xcf78, 0xcf86, 0x208c, 0x69e1, 0xea82,
  0xcf80, 0xcf62, 0x4f5a, 0xcf70, 0xcf76, 0x4f88, 0xd98a, 0x21aa,
  0xae42, 0xcf57, 0xcf74, 0x4f88, 0x5989, 0xd98a, 0x4f81, 0x4f87,
  0x5989, 0xd98a, 0xd288, 0xd288, 0xcf97, 0xcf91, 0xcf90, 0xcf8c,
  0xcfa5, 0xcfa8, 0xcfa4, 0xcfa3, 0xcf9f, 0xcfa1, 0x9e49, 0x217b,
  0xe90a, 0xcfb9, 0x4fb6, 0xd1bb, 0x4fb4, 0xd1bb, 0xcfae, 0x4fc5,
  0xcfe4, 0x4fc4, 0xcfe4, 0xcfe0, 0x9e60, 0xcff9, 0xcffd, 0xcfec,
  0xcfc6, 0x4fc4, 0xcfc5, 0xc3a9, 0xcfda, 0xcffa, 0xc796, 0xcfd8,
  0xcff1, 0x4e59, 0xe8dc, 0xcfd9, 0xc1ff, 0xb537, 0xd008, 0xd007,
  0xd00b, 0xd00a, 0xc54a, 0xc9d0, 0xb7b1, 0xe504, 0xd02e, 0xd5c9,
  0xd02c, 0x39c8, 0xe392, 0xd027, 0xd022, 0xbacc, 0xd073, 0xb065,
  0xd043, 0xdead, 0x504c, 0x5077, 0xdec4, 0xd076, 0xd03c, 0xd07e,
  0x5040, 0x5077, 0xdec4, 0xd079, 0x5068, 0xd06f, 0x2723, 0xa7a9,
  0xe5fb, 0x28fb, 0xaa7f, 0x5066, 0x506a, 0xd070, 0x5061, 0x506a,
  0xd070, 0x5054, 0xd06f, 0xd075, 0x5061, 0x5066, 0xd070, 0xd06f,
  0x2abf, 0xb127, 0xd06f, 0x5054, 0x5068, 0x506b, 0xd06e, 0x5061,
  0x5066, 0xd06a, 0xa8f0, 0xd038, 0x242c, 0xd07d, 0xd069, 0xd042,
  0x5040, 0x504c, 0xdec4, 0xd04d, 0x242c, 0xd074, 0xd04b, 0x4c9b,
  0xd085, 0x4c9b, 0xd083, 0xd088, 0xd087, 0xab8d, 0xd0af, 0x5178,
  0xd193, 0xd135, 0xd19a, 0xd0da, 0xc5a3, 0xeaaf, 0x508e, 0xd0bb,
  0xa3b7, 0x3bd3, 0xd8ac, 0xe91a, 0xd0af, 0xd14e, 0xd16b, 0xd139,
  0x5105, 0x5107, 0xd10b, 0x1f37, 0xa191, 0x51bb, 0xd1bd, 0xb3f9,
  0xd0a7, 0xa2dd, 0x585a, 0xeb0d, 0xb727, 0xd156, 0xd1da, 0x511b,
  0xde01, 0xd1d9, 0xd1a0, 0x5106, 0xd13a, 0x50c1, 0xd10b, 0x5103,
  0xd13a, 0xd0c1, 0xd109, 0xd108, 0x50c1, 0xd105, 0xd1be, 0x51d3,
  0x51df, 0xead2, 0xd1cd, 0x5133, 0xd166, 0xd1bf, 0xd1e0, 0xa43b,
  0x5173, 0xde0b, 0x50eb, 0xde01, 0xa507, 0x9fee, 0xd131, 0xd12b,
  0x2318, 0x5111, 0xd166, 0xd0a1, 0xd161, 0xd1c9, 0x45ee, 0xd0c0,
  0x5103, 0xd106, 0xd157, 0xa00e, 0x51c8, 0xd1d8, 0xe183, 0xd0be,
  0xd0e8, 0xd13e, 0xd195, 0xd1a3, 0xd136, 0x2318, 0x5111, 0xd133,
  0xd0bf, 0xef76, 0xe84b, 0x511a, 0xde0b, 0x50a0, 0xd193, 0xd1a9,
  0xd183, 0xea30, 0xeac8, 0xd190, 0xd17d, 0xd182, 0xd1cf, 0x50a0,
  0xd178, 0xd158, 0xd0a4, 0xd0f6, 0xd15f, 0xecd5, 0xd17b, 0xe94d,
  0x6ac4, 0xead3, 0x4fb4, 0x4fb6, 0x50c6, 0xd1bd, 0x50c6, 0xd1bb,
  0xd10d, 0xd113, 0xd1cb, 0x4655, 0xc670, 0x514a, 0xd1d8, 0xd138,
  0xd1c0, 0xd110, 0x5191, 0xead5, 0x510f, 0xd1df, 0x514a, 0xd1c8,
  0xd0ed, 0xd0ea, 0xd1e2, 0xc66f, 0x510f, 0xd1d3, 0xd114, 0xd1dc,
  0xa367, 0x9e34, 0xd1f0, 0xd1ed, 0xcdfb, 0x23f0, 0xa76e, 0xdf3f,
  0x9e0e, 0xa174, 0x1e3e, 0x3319, 0xb4e7, 0xb5e7, 0x3368, 0xd20e,
  0xd20d, 0xd213, 0xd210, 0x5217, 0x62ea, 0xe4fa, 0x5216, 0x62ea,
  0xe4fa, 0xe928, 0xd264, 0xd239, 0x523b, 0xd26b, 0xd266, 0xd259,
  0xb7c1, 0xd24a, 0xd229, 0x522e, 0xd26b, 0xd236, 0xd231, 0xba2f,
  0x3a79, 0xd26a, 0xd223, 0xd230, 0x3a79, 0xd263, 0x522e, 0xd23b,
  0xd271, 0x26cf, 0xd270, 0x5276, 0x5277, 0x5c53, 0xdc54, 0x5273,
  0x5277, 0x5c53, 0xdc54, 0x5273, 0x5276, 0x5c53, 0xdc54, 0xd279,
  0xd278, 0x49c7, 0x52b8, 0x541f, 0x54fa, 0xd5dd, 0xcbc0, 0x4f8a,
  0xcf8b, 0xd28c, 0xd28b, 0xa349, 0xd58c, 0xd56a, 0xd606, 0xd2b2,
  0xd2b1, 0x49c7, 0x527a, 0x541f, 0x54b7, 0x54fa, 0x5553, 0xd5dd,
  0xa20d, 0xd4ef, 0x1e42, 0xa208, 0xd466, 0xd5f6, 0xd3a7, 0xd407,
  0xd4bc, 0xd2e7, 0x26cc, 0x4526, 0x4a4c, 0x5607, 0xd613, 0xd351,
  0xd2e2, 0xd2e1, 0xd2ce, 0xa040, 0x55b2, 0xd60b, 0xd393, 0xd3f0,
  0x4b35, 0xcbc4, 0x2179, 0xd332, 0xd396, 0xd622, 0xd526, 0xa84b,
  0xc162, 0xd354, 0x4e6d, 0xd812, 0xcd65, 0xd438, 0x2179, 0xd30a,
  0xd34a, 0xd395, 0xd346, 0x1f9f, 0xd5a6, 0xd2d0, 0xd318, 0x2e84,
  0xd38a, 0xd598, 0xd3a2, 0xd558, 0xd4fd, 0xd434, 0xd54e, 0xd588,
  0x5415, 0xd5ba, 0x46ea, 0x5569, 0xd62f, 0x3804, 0xb9ae, 0xd477,
  0xbece, 0xc296, 0xc192, 0xd541, 0xd5ce, 0xd4c0, 0xd52d, 0xd552,
  0xd452, 0xd464, 0x546f, 0x55ac, 0xd5e5, 0xdc46, 0xb357, 0xd510,
  0x3d96, 0xd49e, 0x2e84, 0xd358, 0xd2fa, 0xd347, 0xd30e, 0xa50d,
  0xd35a, 0xd2cb, 0xd40a, 0xd4ee, 0xd494, 0xd435, 0xd3df, 0xd59f,
  0x4372, 0xca6b, 0xd555, 0xc469, 0x6d2c, 0x6daf, 0xee0e, 0x5493,
  0xd4f4, 0xd3be, 0xd3bd, 0xd458, 0xb79c, 0x235c, 0xd514, 0xd3b5,
  0xa807, 0x234e, 0x2d0b, 0xd550, 0xd2fd, 0xd506, 0x2eb5, 0x544a,
  0xd4ed, 0xd445, 0xd2cc, 0xd3b1, 0xd420, 0xd4f1, 0x5360, 0xd5ba,
  0xd600, 0xd63f, 0x49c7, 0x527a, 0x52b8, 0x54fa, 0xd5dd, 0xd40c,
  0x56cd, 0xd7a2, 0x25b6, 0xc1df, 0xce08, 0xd56d, 0xd5a9, 0x9e07,
  0x557f, 0x55fc, 0xd610, 0xd35d, 0xd3b4, 0xd330, 0xd55a, 0xd3f9,
  0xa3f6, 0x2eb5, 0x53f4, 0xd4ed, 0xd36d, 0xc740, 0xd3d1, 0xb939,
  0xd593, 0x46d6, 0xd4cb, 0xd36e, 0xd2c7, 0xd58a, 0xa85f, 0xb934,
  0xd36f, 0xd525, 0xd364, 0xd515, 0xcbc6, 0xd546, 0xd562, 0xd523,
  0xd51e, 0xb41c, 0x53bc, 0xd4f4, 0xd3b3, 0x31de, 0x3e95, 0x3fdb,
  0xc7c7, 0x3d96, 0xd385, 0xaf60, 0xd533, 0xd2b8, 0xd2cd, 0xd36a,
  0xae2d, 0x46d6, 0xd462, 0x4c11, 0xcc14, 0xd5cd, 0xd58a, 0xd63a,
  0xd577, 0xe3a3, 0xea40, 0x2eb5, 0x53f4, 0xd44a, 0xd3b2, 0xd2c1,
  0xd40d, 0x53bc, 0xd493, 0x49c7, 0x527a, 0x52b8, 0x541f, 0xd5dd,
  0xd35c, 0x298d, 0xa9f8, 0xd3f1, 0xd37b, 0xd84a, 0x235c, 0xd3d4,
  0xd482, 0xd48c, 0xd48b, 0xd471, 0xd311, 0xd36b, 0xd5bc, 0xd53b,
  0xd4a8, 0xeebb, 0x2328, 0xd5cf, 0x4ba6, 0xcc00, 0xd594, 0xd61e,
  0xd5fa, 0xd532, 0x55f9, 0xdb6a, 0xd368, 0xd487, 0x554b, 0xd602,
  0x554a, 0xd602, 0xd35e, 0x234e, 0xd3ef, 0xd36c, 0xd2b8, 0xd3b8,
  0xd35b, 0xd43c, 0xd489, 0xd361, 0xd29c, 0xd427, 0xd580, 0xd604,
  0x55f4, 0xd60a, 0xd4e3, 0x5431, 0x55fc, 0xd610, 0xd570, 0xd35f,
  0x546a, 0xd4df, 0xd297, 0xa9dc, 0xd460, 0xd537, 0x26ed, 0xa712,
  0xd359, 0xd3b6, 0x1f9f, 0xd350, 0xd428, 0x55b0, 0xd60d, 0x536f,
  0xd5e5, 0xca17, 0x4c54, 0xd5ea, 0xd5f7, 0x55ab, 0xd60d, 0x52f9,
  0xd60b, 0x5360, 0xd415, 0xd52f, 0x4a01, 0x4a3e, 0x4a3f, 0xd5f3,
  0x1e1b, 0x23e2, 0x3a37, 0xbb09, 0x201f, 0x2fa3, 0xd024, 0xd4dd,
  0xd369, 0x2328, 0xd535, 0xd61a, 0x49c7, 0x527a, 0x52b8, 0x541f,
  0xd4fa, 0x563d, 0xd646, 0xcc58, 0x536f, 0xd5ac, 0x4c54, 0xd5ae,
  0x4a01, 0x4a3e, 0x4a3f, 0xd5c1, 0x5574, 0xd60a, 0xd2c8, 0xd5af,
  0x553c, 0xdb6a, 0xd53a, 0x5431, 0x557f, 0xd610, 0xd41a, 0x554a,
  0xd54b, 0xd572, 0xd2a6, 0x4526, 0x4a4c, 0x52cf, 0xd613, 0x5574,
  0xd5f4, 0x52f9, 0xd5b2, 0x55ab, 0xd5b0, 0x6a65, 0xeaa5, 0x5431,
  0x557f, 0xd5fc, 0x52cf, 0xd607, 0xba97, 0xd5d3, 0xd539, 0xd30f,
  0xa170, 0x46ea, 0xd361, 0xd4e0, 0x55df, 0xd646, 0xd41d, 0xa914,
  0x55df, 0xd63d, 0x1e55, 0xd65d, 0xd65c, 0xb16e, 0xc307, 0x21e6,
  0xa904, 0xa47c, 0x565a, 0xd65b, 0x5657, 0xd65b, 0x5657, 0xd65a,
  0xd64f, 0x1e55, 0xd64e, 0xa3f7, 0xd666, 0xd665, 0x9e8f, 0x567a,
  0xd7f2, 0xd66f, 0xd7e3, 0xd66c, 0xd768, 0x2b83, 0xd6c7, 0xd66b,
  0xd771, 0xe6d6, 0xd766, 0xd806, 0xd755, 0xd7fb, 0xd79e, 0xd801,
  0x57a1, 0xd7c1, 0xd739, 0xd72f, 0x5745, 0x5836, 0xd83a, 0xd788,
  0xd6d4, 0xd814, 0xd6ba, 0xec8d, 0xd706, 0xd6d4, 0xd6a6, 0x2b83,
  0xd675, 0xd831, 0x5424, 0xd7a2, 0x5807, 0xd823, 0xd7f6, 0x5698,
  0x56b4, 0xd716, 0xef03, 0xd83b, 0xd7a7, 0xd7c4, 0xd6fa, 0xd7ef,
  0xd784, 0xd810, 0xd6f1, 0xd715, 0x5c9d, 0xdd1d, 0xd82d, 0xd6ac,
  0xd782, 0xd6fb, 0xd6d4, 0xd778, 0x574b, 0xd81f, 0xd68c, 0xd68b,
  0xeb4e, 0x5695, 0x5836, 0xd83a, 0x577f, 0xd805, 0xd7c8, 0xd7ec,
  0x5721, 0xd81f, 0xd80d, 0xd680, 0xc32c, 0xd815, 0xd67e, 0xd671,
  0xc33f, 0xd67b, 0xd717, 0xd7bb, 0xd811, 0x5747, 0xd805, 0xd7bf,
  0xd70b, 0xd6f3, 0xd696, 0xd78e, 0xd78d, 0xd7fb, 0x5827, 0xd839,
  0xd682, 0xd68a, 0x5424, 0xd6cd, 0x3594, 0xb59e, 0xd6ef, 0xd7ce,
  0xd77c, 0xd780, 0xd68a, 0xd6f0, 0xd7c7, 0xd7c6, 0xd748, 0xd7a8,
  0xd828, 0xd80e, 0xd66e, 0xd749, 0xd6f2, 0xd66b, 0xd6cf, 0xd821,
  0xd80f, 0x5681, 0xd798, 0xd683, 0x5747, 0xd77f, 0xd67f, 0x56ce,
  0xd823, 0xd74e, 0xd7d2, 0xd7f9, 0xd6f4, 0xd77e, 0x4d78, 0x4e6d,
  0xd327, 0xd69d, 0xd761, 0x5721, 0xd74b, 0xd7f8, 0xb0f7, 0x56ce,
  0xd807, 0x5799, 0xd839, 0xd7cf, 0xd702, 0xd6ca, 0x5695, 0x5745,
  0xd83a, 0x5799, 0xd827, 0x5695, 0x5745, 0xd836, 0xd6ee, 0x4383,
  0xdc9c, 0xd844, 0xd842, 0xe1c1, 0x1e51, 0x1f17, 0xc73e, 0x24af,
  0xa580, 0xd511, 0xb72f, 0x2563, 0x5858, 0xe29c, 0xa40c, 0xadf7,
  0x2563, 0x5854, 0xe29c, 0xd0e1, 0x236b, 0xd85e, 0x2675, 0x585f,
  0xe053, 0xa1b2, 0x236b, 0xd85b, 0x2675, 0x585c, 0xe053, 0xd864,
  0xd863, 0xd8dc, 0xe336, 0xd96f, 0xd89e, 0xa3ea, 0xd8b5, 0xd956,
  0x2acb, 0x2b1d, 0xd8ca, 0xd918, 0xae12, 0x596a, 0x67c8, 0xe7e4,
  0xd86e, 0x3bd3, 0xd0b2, 0xd972, 0xeef9, 0xd94f, 0x4d5d, 0xd8e4,
  0xd87d, 0xd8cc, 0xd8dd, 0xd960, 0xd90c, 0xd885, 0xd8b7, 0x58e1,
  0xe1cc, 0xd8e0, 0xd865, 0xd8c5, 0xd8d9, 0x58cf, 0xe1cc, 0xd933,
  0xd95d, 0x58b4, 0xd932, 0xd947, 0x5912, 0xd943, 0xb707, 0xd92b,
  0xd8f5, 0xd8f4, 0xa236, 0xa90d, 0xb0fc, 0xd8c8, 0xd90f, 0xd90e,
  0x58e6, 0xd943, 0xcde5, 0xd886, 0xd938, 0xd94c, 0xd8ed, 0xd8e4,
  0xd8e2, 0xd964, 0xd91b, 0x9eb5, 0x58e6, 0xd912, 0xd8e5, 0xd91d,
  0xb742, 0xd8af, 0xd974, 0xd884, 0xd8e3, 0xd8c6, 0xd934, 0xd89c,
  0xb446, 0xb4f7, 0xd86c, 0xd8ad, 0x5955, 0xd97d, 0xd974, 0x2364,
  0xa365, 0x290d, 0xafa9, 0xe738, 0xb838, 0x4f87, 0x4f88, 0xd98a,
  0x4f81, 0x4f87, 0x4f88, 0xd989, 0xd9c1, 0xaf01, 0xd9c3, 0xd9c4,
  0x599a, 0x59ba, 0xd9c9, 0x5994, 0xd9c5, 0x5993, 0xd9c5, 0x470e,
  0xd9c6, 0xd9c7, 0x5990, 0x59ba, 0xd9c9, 0xd9cb, 0xd9cd, 0xd9ce,
  0x59bd, 0xd9c8, 0xc779, 0x9eb2, 0xd9ca, 0xd9cf, 0xd9b7, 0xd9d0,
  0x59c0, 0xd9c2, 0xd9bc, 0x59b0, 0xd9d1, 0x358d, 0x5990, 0x599a,
  0xd9c9, 0xd9b6, 0x59a7, 0xd9c8, 0xd9cc, 0x59b3, 0xd9c2, 0xd98b,
  0x59b3, 0xd9c0, 0xd98e, 0xd98f, 0x5993, 0xd994, 0x470e, 0xd996,
  0xd998, 0x59a7, 0xd9bd, 0x5990, 0x599a, 0xd9ba, 0xd9ac, 0xd9a1,
  0xd9bf, 0xd9a5, 0xd9a6, 0xd9af, 0xd9b2, 0xd9b7, 0xcb4b, 0x59ef,
  0xd9f6, 0xb2b5, 0xd9f4, 0xd9e7, 0xd9f8, 0xd9e3, 0x59d7, 0xd9f6,
  0xd9de, 0x59d7, 0xd9ef, 0xd9e6, 0x59fd, 0xd9ff, 0x59f9, 0xd9ff,
  0x59f9, 0xd9fd, 0x5a01, 0xdba0, 0x5a00, 0xdba0, 0xdba2, 0xdba3,
  0xdba1, 0xdbaf, 0xdba7, 0xdba8, 0xdba6, 0xdbb1, 0xdbad, 0xdbaa,
  0xdbab, 0x3258, 0xdbac, 0xdbb0, 0xdabe, 0xdbb9, 0xdbb6, 0xdbbc,
  0x47e4, 0xc7e7, 0x241f, 0xa656, 0xbb23, 0xdbc0, 0xdbb7, 0xdbbb,
  0xdbbf, 0x9fe1, 0xdbbe, 0xdbb8, 0x5b6f, 0xdbd1, 0xdbc9, 0x24ec,
  0xdbc3, 0xdbca, 0xbce8, 0x5b49, 0xdbc1, 0xda6c, 0xdbc2, 0x2467,
  0xdbcb, 0x246a, 0xa492, 0xa46d, 0xdbb5, 0xdbc8, 0xdbd2, 0xdbcf,
  0xdbc4, 0xdbd0, 0xdbc7, 0xdbce, 0xdbc5, 0xdbcd, 0xdb8b, 0xa48f,
  0xdbe9, 0xdbe2, 0xdbe3, 0xdbd5, 0xabdf, 0xdbd7, 0xdbe7, 0x5a3d,
  0xdbdf, 0xdbe1, 0xdbe0, 0xdbd8, 0x5b6e, 0xdbdd, 0xdbe5, 0xdbe6,
  0xdbdc, 0xdbd9, 0xdbd6, 0xdbd4, 0xdbdb, 0xdbd3, 0xa938, 0xdb7d,
  0xdb04, 0xafd7, 0xdba4, 0xdbf3, 0xdbf6, 0xdbde, 0x3096, 0xb102,
  0x2500, 0xdbf1, 0xdbee, 0xdbed, 0xdbda, 0xdbeb, 0xdbec, 0xdbef,
  0xdbf0, 0xdbf5, 0xdbf2, 0x5aac, 0xdbf4, 0x5aaa, 0xdbf4, 0x5b80,
  0xdbfb, 0xa531, 0xdc01, 0xdbfe, 0xdc07, 0xdbfd, 0x4ae9, 0xdc0a,
  0xda1a, 0xdc03, 0xdc04, 0xdc06, 0xdc08, 0xdbff, 0xdbf7, 0x5aeb,
  0xdc0f, 0xdbe4, 0xdbf9, 0xdbfc, 0xdc05, 0xdbba, 0xdc02, 0xdc00,
  0xdc0d, 0xdc1e, 0xdc1d, 0x5afc, 0xdc16, 0x5b1a, 0xdc25, 0xdbe8,
  0x5b8d, 0xdc14, 0xdc1b, 0xdc10, 0xdb5c, 0x5acc, 0xdc0f, 0x242f,
  0x2518, 0x2553, 0x2554, 0x255f, 0xe5d9, 0xdc15, 0xdc18, 0xdbb3,
  0x3697, 0xdc19, 0xdc0c, 0x5bbd, 0xe8a8, 0xdbf8, 0xdc1a, 0x5ae0,
  0xdc16, 0xdbfa, 0xdc0b, 0xdc12, 0xdc13, 0xda8a, 0xdbcc, 0xdc0e,
  0xbb4c, 0xdc1c, 0xdc27, 0xdc11, 0xdc21, 0xdc24, 0xdc26, 0x5ae1,
  0xdc25, 0xdbb2, 0xdc22, 0x5b21, 0xdc23, 0x5b20, 0xdc23, 0xdc1f,
  0xa546, 0x5b81, 0xdc2a, 0xdc2c, 0x5b7e, 0xdc2b, 0xa5f9, 0xdbb4,
  0xdc28, 0x247c, 0xa611, 0xdc29, 0x24d7, 0xa629, 0x5a3c, 0xdbc1,
  0xdbb9, 0xdc32, 0xdba5, 0xa642, 0x5b5b, 0xdc2e, 0xdbc6, 0xdc2f,
  0xdc2d, 0x5b56, 0xdc2e, 0x5ae9, 0xdc31, 0xa66a, 0x553c, 0xd5f9,
  0x2661, 0xdc35, 0xbbc1, 0x5a71, 0xdbdd, 0x5a33, 0xdbd1, 0xdbae,
  0xa584, 0x5b93, 0xdba9, 0xdc34, 0xb2a4, 0xdbea, 0x46d1, 0xc6e3,
  0xda89, 0x5b2d, 0xdc2b, 0x5aad, 0xdbfb, 0x5b2b, 0xdc2a, 0x5b9a,
  0xdd5e, 0xdc09, 0x23d8, 0xa909, 0xda5f, 0x2bb4, 0xe1bc, 0x5ae4,
  0xdc14, 0x1ec7, 0x5b90, 0xe6e0, 0x5b8e, 0xe6e0, 0xdc17, 0x5b72,
  0xdba9, 0xdc30, 0xdc36, 0x5b83, 0xdd5e, 0xdc20, 0xdc33, 0x5a00,
  0xda01, 0xda08, 0xda02, 0xda03, 0xda8d, 0xdb4f, 0xda10, 0xda0c,
  0xda0e, 0x5b72, 0xdb93, 0xda15, 0xda16, 0x3258, 0xda17, 0xda13,
  0xdb70, 0xda0a, 0xda18, 0xda12, 0xdb1b, 0xdaf1, 0xdb33, 0xda4e,
  0xda1d, 0xda25, 0xda31, 0x5a1b, 0xdb4c, 0xdad6, 0xda29, 0xda1f,
  0x5af7, 0xe8a8, 0xda2d, 0xda2a, 0xda23, 0x5a3c, 0xdb49, 0xda41,
  0xda36, 0xda55, 0xda5b, 0xdb58, 0xda57, 0xda50, 0xda34, 0xda3a,
  0xda46, 0xdb05, 0xda5e, 0xda58, 0xda54, 0xda56, 0x5a33, 0xdb6f,
  0xda52, 0xda86, 0xda84, 0xda66, 0xda7f, 0xda69, 0xda70, 0xda7c,
  0xdaa0, 0xda85, 0xda75, 0x5a71, 0xdb6e, 0xda95, 0xda6c, 0xda6e,
  0xda6d, 0xda62, 0xda63, 0xdacd, 0xda72, 0xda73, 0xda6b, 0xdae2,
  0xda61, 0xdb78, 0xdaa1, 0xdaa3, 0xda9e, 0xda9a, 0x309e, 0xdaa4,
  0xdaa5, 0x2500, 0xda98, 0xdaa8, 0xda91, 0x5aaa, 0xdaac, 0xdaa6,
  0xda92, 0xdacb, 0xdaf8, 0xdacf, 0xdafe, 0x5aad, 0xdb80, 0xdad1,
  0xdab9, 0xdab2, 0xdac9, 0xdadb, 0xdab0, 0xdad7, 0xdabf, 0xdac2,
  0xdad2, 0xdac4, 0xdab6, 0xdac7, 0xdb85, 0x4ae9, 0xdabc, 0xdb00,
  0xdaf6, 0xdadc, 0xdb0a, 0x5acc, 0xdaeb, 0xdae7, 0xdb14, 0xdb01,
  0xdb02, 0x5ae4, 0xdb8d, 0xdaed, 0x5ae0, 0xdafc, 0xdb92, 0xdaee,
  0x3697, 0xdaf3, 0xdafa, 0xdae6, 0xdb0e, 0xdade, 0xdadd, 0xdb28,
  0xdb9c, 0xdb16, 0xdb1d, 0x5b20, 0xdb21, 0xdb17, 0x5ae1, 0xdb1a,
  0xdb19, 0xdb10, 0xdb39, 0xdb3e, 0x5b2b, 0xdb81, 0x5b2d, 0xdb7e,
  0xdb2c, 0xdb5a, 0x5b56, 0xdb5b, 0xdb59, 0xdb95, 0xdb5c, 0xdb4e,
  0xdb9e, 0xdb74, 0xdb6b, 0xdb96, 0x4a40, 0xccd3, 0xac88, 0x3e13,
  0x3eaa, 0xdc40, 0x3e13, 0x3eaa, 0xdc3f, 0xd373, 0xac82, 0x1e30,
  0x5c50, 0x673b, 0xe74a, 0x4ad6, 0xcaea, 0x1e30, 0x5c4a, 0x673b,
  0xe74a, 0x5273, 0x5276, 0x5277, 0xdc54, 0x5273, 0x5276, 0x5277,
  0xdc53, 0xdc5a, 0xdc58, 0xdc87, 0x9e88, 0xc32a, 0xdc76, 0xdc6e,
  0xc2b2, 0xc2b4, 0xdc94, 0xc2c9, 0xef26, 0xdc64, 0xc2e2, 0xc2b4,
  0xc683, 0x42f8, 0xc30d, 0xc30a, 0xc32f, 0xc32b, 0xdc7c, 0xc34f,
  0xc37e, 0x4383, 0xd83c, 0x56fd, 0xdd1d, 0xdd1e, 0x2458, 0x24e1,
  0xdd20, 0xdd1f, 0x321d, 0xdd22, 0xdd21, 0xdd2b, 0xdd27, 0xdd29,
  0xdd2a, 0xdd2f, 0xdd23, 0x2295, 0x5cea, 0xdd28, 0x2f10, 0x5cb3,
  0xdd30, 0xdd2e, 0xdd33, 0xdd40, 0x2f10, 0x5cae, 0xdd30, 0xdd35,
  0xdd2c, 0x9e70, 0xdd37, 0xdd36, 0xdd39, 0xdd34, 0xdd3b, 0xdd38,
  0xdd3a, 0xdd32, 0xdd42, 0xdd41, 0xdd3f, 0xdd45, 0xdd44, 0xdd3e,
  0x2379, 0xb064, 0xdd3c, 0xc68e, 0x5d13, 0xdd43, 0x5ce4, 0xdd31,
  0xdd48, 0xdd4a, 0x2bbe, 0xdcd4, 0x2bbe, 0xdcd3, 0xdd47, 0xdd4a,
  0xdd52, 0xdd49, 0x5d0a, 0xdd5e, 0xdd50, 0xdd4f, 0xdd54, 0x4d9a,
  0x4e8c, 0xdd53, 0x5d12, 0xdd24, 0x2356, 0xa8f2, 0x5cce, 0xdd31,
  0xdd4b, 0xdd55, 0x2295, 0x5cad, 0xdd28, 0xdd4d, 0xdd26, 0xdd4c,
  0xa046, 0x5d56, 0xe83c, 0xdd57, 0x5d4d, 0xef4e, 0xdd5a, 0xdd59,
  0xdd2d, 0xdd5b, 0xdd5c, 0x3557, 0xdd25, 0xdd3d, 0xdd58, 0xdd5f,
  0xdd60, 0x5cdb, 0xdd5e, 0x2050, 0x5d17, 0xdd5d, 0xdd61, 0xdd62,
  0xdd46, 0x5ce2, 0xdd24, 0x5ccd, 0xdd43, 0xdd51, 0xdd4e, 0x2050,
  0x5d0b, 0xdd5d, 0xdd63, 0xdd43, 0x56fd, 0xdc9d, 0xdc9e, 0xdca0,
  0x2458, 0x24e1, 0xdc9f, 0xdca2, 0x321d, 0xdca1, 0xdcac, 0x5ce2,
  0xdd12, 0x3557, 0xdd01, 0xdcec, 0xdca8, 0x5cad, 0xdcea, 0xdca9,
  0xdcaa, 0xdca7, 0xdcb6, 0xdcfc, 0xdcaf, 0xdcab, 0x2f10, 0x5cae,
  0xdcb3, 0x5cce, 0xdce4, 0xdcc1, 0xdcb0, 0xdcbc, 0xdcb4, 0xdcba,
  0xdcb8, 0xdcbf, 0xdcbb, 0xdcc0, 0xdcbd, 0xdcca, 0xdd04, 0xdcc8,
  0xdcc4, 0xdcb2, 0xdcc3, 0xdcc2, 0x5ccd, 0x5d13, 0xdd1c, 0xdcc7,
  0xdcc5, 0xdd10, 0xdcd5, 0xdcd1, 0xdcda, 0x5cd2, 0xdcd6, 0xdce6,
  0xdced, 0x5ceb, 0x5cf7, 0xef4e, 0xdd16, 0xdcde, 0xdcdc, 0xdd14,
  0xdcd9, 0xdce1, 0xdce0, 0xdce7, 0x5cf4, 0xe83c, 0xdcf5, 0xdd05,
  0xdcfb, 0xdcfa, 0xdcfd, 0xdcfe, 0x2050, 0x5d0b, 0xdd17, 0x5b83,
  0x5b9a, 0x5cdb, 0xdd0a, 0xdd07, 0xdd08, 0xdd0d, 0xdd0f, 0xdd1b,
  0x407b, 0xc0fe, 0xdd6c, 0xdd6a, 0xdd71, 0xdd70, 0xdd99, 0xdd95,
  0x2c1f, 0x2c20, 0x6bae, 0x6c7b, 0xec9c, 0xdfdf, 0xdd82, 0xdd81,
  0xdda8, 0xdda6, 0xdd76, 0xdd75, 0xdd91, 0xdd8b, 0xde7a, 0x5dc3,
  0xde8d, 0xddb2, 0xddb1, 0xde89, 0xde38, 0x5daf, 0xde8d, 0xde4c,
  0xde92, 0xdff9, 0xddfa, 0xde29, 0xde10, 0xde82, 0xde7a, 0xde55,
  0xde9a, 0xdde5, 0xde8b, 0xac40, 0x50eb, 0xd11b, 0x458e, 0xc58f,
  0xde34, 0x511a, 0xd173, 0xde8a, 0xddf5, 0xde64, 0xddf4, 0xde64,
  0xc8b0, 0xde93, 0xde91, 0xcaac, 0xde0a, 0xddbb, 0xde4f, 0x5e79,
  0xdea2, 0xddc4, 0xde44, 0xdea1, 0xde63, 0xddf8, 0xdff9, 0xde52,
  0x5e28, 0xde2a, 0x5e8a, 0xde95, 0x5e4b, 0xdea2, 0x5dac, 0xddf7,
  0xdea5, 0xddf6, 0xde84, 0xde83, 0xa8b8, 0xddb8, 0x5e0c, 0x5e70,
  0xde95, 0x5dfb, 0xe6ae, 0x5daf, 0xddc3, 0xdeaa, 0xde2f, 0xddde,
  0xde2c, 0x5e70, 0xde8a, 0xdeaa, 0xddf9, 0xdea6, 0xde51, 0x5e4b,
  0xde79, 0xde7f, 0xde9c, 0x5e8f, 0xde99, 0xdeb3, 0xd03d, 0xdec0,
  0x1f53, 0x5ec6, 0x6ab5, 0xead4, 0xdeb2, 0xdeb1, 0xdeac, 0xdeaf,
  0x5040, 0x504c, 0xd077, 0x1f53, 0x5eb0, 0x6ab5, 0xead4, 0xdf66,
  0xdf67, 0xdf68, 0xa19b, 0xdf6a, 0xdf69, 0xdf6b, 0x3799, 0x5ef6,
  0xdf6d, 0xdf6f, 0x5f49, 0xdf6c, 0x5f5f, 0xdf70, 0xdf77, 0xdf78,
  0x5f29, 0xdf88, 0xdf71, 0x3799, 0x5edb, 0xdf6d, 0xdf74, 0xdf75,
  0xdf7a, 0xdf72, 0xdf76, 0x5f15, 0xdf7b, 0xdf7c, 0xdf5c, 0xdf83,
  0xdf82, 0xdf81, 0xdf80, 0xdf7d, 0xdf7e, 0x5f1b, 0xdf86, 0xdf36,
  0x5f19, 0xdf84, 0xb33d, 0x1fcc, 0xdf85, 0x5efd, 0xdf7b, 0x5f12,
  0xdf84, 0x5f0c, 0xdf86, 0x5f3a, 0xdf8e, 0xdf89, 0xdf8b, 0xdf8d,
  0xdf57, 0xdf8a, 0xdf87, 0x5ef0, 0xdf88, 0xdf6e, 0xdf8c, 0xdf6f,
  0xdf91, 0xdf8f, 0xdf0f, 0xdf93, 0x5f1c, 0xdf8e, 0xdf90, 0xdf92,
  0xdf97, 0xd206, 0x67b1, 0x67dc, 0xe7ec, 0xbbc2, 0x5f96, 0xe38b,
  0xdf95, 0xdf98, 0x5ee2, 0xdf6c, 0xdf99, 0xdf7f, 0xdf9a, 0xdf21,
  0xdf00, 0x5ee3, 0xdf70, 0xdf94, 0xdf79, 0xdf73, 0xdeca, 0xdecb,
  0xdecc, 0xded2, 0xded1, 0xded4, 0x5ee2, 0xdf49, 0x3799, 0x5edb,
  0xdef6, 0xdf2a, 0x5edf, 0xdf2d, 0x5ee3, 0xdf5f, 0xdef2, 0xdefb,
  0xdf64, 0xdef8, 0xdef9, 0xdefc, 0xdee4, 0xdeeb, 0xdf62, 0xdefa,
  0x5efd, 0xdf15, 0xdefe, 0xdf09, 0xdf0a, 0xdf4e, 0xdf08, 0xdf07,
  0xdf05, 0xdf03, 0x5f12, 0xdf19, 0xdf14, 0x5f0c, 0xdf1b, 0xdf26,
  0x5ef0, 0xdf29, 0xdf1d, 0xdf25, 0xdf1e, 0xdf2c, 0xdf1f, 0x5f1c,
  0xdf3a, 0xdf33, 0xdf3b, 0xdf2f, 0xdf3c, 0xdf38, 0xdf61, 0xdf45,
  0xdf44, 0xdf3e, 0xdf46, 0xdf4d, 0xdf54, 0x5f9e, 0x5fa4, 0xdfad,
  0x5f9d, 0x5fa4, 0xdfad, 0xe5e2, 0x3683, 0xcf6a, 0x2f01, 0x5fa9,
  0xdfaf, 0x5f9d, 0x5f9e, 0xdfad, 0xa29e, 0x2f01, 0xdfa8, 0x2f01,
  0xdfa7, 0x2f01, 0x5fa1, 0xdfaf, 0xdfae, 0x5f9d, 0x5f9e, 0xdfa4,
  0xdfab, 0x2f01, 0x5fa1, 0xdfa9, 0xa19c, 0xdfb6, 0xdfb5, 0x5fba,
  0x6089, 0xe08a, 0x5fb9, 0x6089, 0xe08a, 0xe07c, 0x6039, 0xe054,
  0xe077, 0xdfc3, 0xdfc2, 0xdfe4, 0xe04e, 0xe081, 0x231d, 0xae00,
  0xe04b, 0xe084, 0xe019, 0xe032, 0xe060, 0xe055, 0xe023, 0x5d7f,
  0x6045, 0xe072, 0xdfc6, 0xe008, 0xe087, 0xaef8, 0xaf80, 0xe003,
  0x2f84, 0x2f91, 0xe015, 0x26d8, 0x26de, 0x26ec, 0x2efb, 0x2efd,
  0xe025, 0x5de1, 0xde5f, 0x1e43, 0xaefc, 0xe069, 0xdfef, 0xdfe5,
  0xe078, 0xe05c, 0xe052, 0x6013, 0xe05e, 0x6012, 0xe05e, 0x2f84,
  0x2f91, 0xdff3, 0xdfd9, 0xdfde, 0x26d8, 0x26de, 0x26ec, 0x2efb,
  0x2efd, 0xdff4, 0xe090, 0xa954, 0x3e38, 0xe04a, 0xa468, 0xdfdb,
  0x5fbe, 0xe054, 0xe060, 0xe08f, 0xa06a, 0xe06f, 0x5fdf, 0xe072,
  0x1fa6, 0xa075, 0x3e38, 0xe030, 0xdfd0, 0xdfc7, 0xe00e, 0x2675,
  0x585c, 0xd85f, 0x5fbe, 0xe039, 0xdfdd, 0xe07a, 0xe065, 0xe00a,
  0x6012, 0xe013, 0x5fdc, 0xe03a, 0xbeaf, 0xe059, 0xe002, 0xe041,
  0x5fdf, 0xe045, 0xced5, 0xdfc1, 0xe009, 0xe057, 0xdfbd, 0xdfc8,
  0xdfd8, 0xdfe9, 0x5fb9, 0x5fba, 0xe08a, 0x5fb9, 0x5fba, 0xe089,
  0xe03b, 0xe026, 0xe61d, 0xe127, 0xe13a, 0xe0ab, 0xb751, 0xe0a6,
  0xe114, 0xe0f5, 0xe112, 0xe134, 0x23b8, 0x6130, 0xe6a3, 0xe0cc,
  0x3b1d, 0x6b30, 0xeb31, 0xe0bd, 0x60d2, 0xe0de, 0xe0df, 0xe136,
  0xe12d, 0x60ce, 0xe0de, 0xe106, 0x60ce, 0xe0d2, 0xe0cf, 0xe148,
  0xe116, 0xe0f6, 0xe0ae, 0xe0e8, 0x1e61, 0x6109, 0x610a, 0xe115,
  0xe132, 0xe0d3, 0x1e61, 0x60f7, 0x610a, 0xe115, 0x1e61, 0x60f7,
  0x6109, 0xe115, 0xe0b9, 0xe0ac, 0x1e61, 0x60f7, 0x6109, 0xe10a,
  0xe0e7, 0xe093, 0xe0d1, 0x23b8, 0x60bb, 0xe6a3, 0xe0f8, 0xe0ba,
  0xe0d0, 0xe146, 0xe09d, 0xe147, 0x2398, 0x28e5, 0xaedb, 0xe147,
  0xe137, 0x613c, 0xe142, 0xe0e6, 0x9e23, 0xe194, 0xe189, 0xb59f,
  0x6196, 0xe19e, 0xe18b, 0x467a, 0x6197, 0xe1b1, 0x616c, 0xe1bb,
  0xa5e0, 0x6167, 0xe1bb, 0x61a4, 0xe1ac, 0xe1c5, 0xe1c3, 0x61b8,
  0xe1c0, 0xd14c, 0xe195, 0xe154, 0xe91f, 0xe162, 0x24b8, 0xee79,
  0xe14b, 0xe187, 0x615d, 0xe19e, 0x467a, 0x6166, 0xe1b1, 0xb9bc,
  0x1e11, 0xeb57, 0x615d, 0xe196, 0x6171, 0xe1ac, 0x233b, 0xbbc9,
  0x6171, 0xe1a4, 0x467a, 0x6166, 0xe197, 0x617f, 0xe1c0, 0x6167,
  0xe16c, 0x2bb4, 0xdb8c, 0x617f, 0xe1b8, 0xd845, 0xe17e, 0xe17d,
  0x2bc0, 0xb3a1, 0x61ca, 0xe1cb, 0x61c8, 0xe1cb, 0x61c8, 0xe1ca,
  0x58cf, 0xd8e1, 0x27dc, 0xa8c4, 0xa398, 0x61d2, 0xe485, 0x61d1,
  0xe485, 0x61da, 0x61fb, 0xe486, 0xe487, 0xe48c, 0x2200, 0xa202,
  0xe48a, 0xe489, 0xe48b, 0x61d3, 0xe1fb, 0xe1e1, 0xe488, 0x1e71,
  0x9e82, 0xe1dc, 0x621f, 0xe493, 0xe490, 0xb263, 0xe48f, 0xe492,
  0xe2e9, 0x2251, 0x2263, 0x2271, 0x228d, 0x228e, 0x2292, 0x2294,
  0xe1fc, 0xe497, 0xe48d, 0xe495, 0xe48e, 0x61d3, 0x61da, 0xe486,
  0x2251, 0x2263, 0x2271, 0x228d, 0x228e, 0x2292, 0x2294, 0xe1f0,
  0xe4af, 0xe4ab, 0xe498, 0xe4ad, 0xe4a5, 0x625b, 0xe4c5, 0xe49a,
  0xe4a0, 0xe49d, 0x6264, 0xe4a9, 0xe4a4, 0xe4a3, 0xe491, 0xe49e,
  0xe4ae, 0x629e, 0xe4a7, 0xe1e3, 0xe49f, 0xe499, 0xe4ac, 0xe49b,
  0xe4aa, 0xe3ba, 0x4089, 0xe46a, 0x6438, 0xe4ce, 0x6268, 0xe4cc,
  0xe4c8, 0xe4b6, 0xe4c3, 0xe4b4, 0xe4b9, 0xe4cd, 0xe4b0, 0xe4b8,
  0xe4c0, 0xe4bf, 0xe4be, 0x6295, 0x62e8, 0x6421, 0x6435, 0xe4c1,
  0xe49c, 0xe4bb, 0xe4ca, 0xe4ca, 0xe4c9, 0x2228, 0xe4c7, 0xe4cb,
  0xe4c2, 0xe4b7, 0xe4b3, 0xe4c6, 0x6206, 0xe4c5, 0xe4ba, 0xe348,
  0x46cb, 0x4f3d, 0xe4b5, 0x620e, 0xe4a9, 0xe4b2, 0x622e, 0xe4cc,
  0xe4bc, 0xe4bd, 0x47ff, 0x483f, 0x4926, 0x62db, 0xe45b, 0x6373,
  0x6451, 0x6452, 0xe46c, 0xe4cf, 0xe4f0, 0xe4d2, 0xe4ec, 0xe4ea,
  0xe4f6, 0xe4f3, 0xe4dc, 0xe4da, 0xe4e3, 0xe4e8, 0x6244, 0x62e8,
  0x6421, 0xe435, 0xe4e2, 0xe4ed, 0x6443, 0xe4eb, 0x62bd, 0xe4e6,
  0x2563, 0x5854, 0xd858, 0xe21e, 0xe4d1, 0xe4f7, 0xe4f1, 0xe4df,
  0xe4f5, 0xe4e5, 0xe4d5, 0xe4ef, 0xe4d0, 0x6322, 0xe4b1, 0xe47e,
  0xe4de, 0xc10a, 0x62ed, 0xe510, 0x4107, 0xe500, 0xe508, 0xe511,
  0xe509, 0xe29b, 0xe4dd, 0xe512, 0xe50c, 0xe4a1, 0xe4e4, 0xe4d7,
  0xe50b, 0xe4fb, 0x47ff, 0x483f, 0x4926, 0x6271, 0xe45b, 0xe50a,
  0x646f, 0xe513, 0x6381, 0xe4d8, 0xe504, 0xe503, 0xe514, 0x6244,
  0x6295, 0x6421, 0x6435, 0xe507, 0x61ef, 0xe4d3, 0x5216, 0x5217,
  0xe4fa, 0x62b3, 0xe510, 0xe4d6, 0xe506, 0xe502, 0xe4fd, 0x6444,
  0xe4f8, 0xe50d, 0xe52f, 0xe4a2, 0xe51e, 0x2f55, 0xe332, 0xe516,
  0xe52b, 0xe529, 0x641a, 0xe4d4, 0xe525, 0xe515, 0xe51f, 0xe524,
  0x637f, 0xe531, 0xe4ee, 0xe51b, 0xe52c, 0xe52d, 0xe51c, 0x62ad,
  0xe4b1, 0xe526, 0xe51a, 0xe520, 0xe521, 0x40bc, 0x4149, 0xe34a,
  0xe522, 0xe519, 0x2f55, 0xe304, 0xe530, 0xd868, 0xe486, 0xe4fc,
  0xe3e8, 0xe51d, 0xe528, 0xe52a, 0xe494, 0xe534, 0x6260, 0xe533,
  0x40bc, 0x4149, 0xe32c, 0xe505, 0xe540, 0xe537, 0xe4e1, 0xe496,
  0x4145, 0xe53b, 0xe4ff, 0x6445, 0xe53d, 0xe538, 0xe532, 0xe518,
  0xe539, 0x63d3, 0xe52a, 0xe53e, 0x6274, 0x6451, 0x6452, 0xe46c,
  0xe52e, 0xe536, 0xe517, 0x649f, 0xe53a, 0xe319, 0xe2e3, 0xe541,
  0xe53f, 0xe545, 0xe551, 0xdf44, 0x642e, 0xe570, 0x39c8, 0xd028,
  0x3eb6, 0xc194, 0x63c1, 0xe501, 0xb7aa, 0xe549, 0xe524, 0xe548,
  0x6543, 0xef12, 0xe4a8, 0xd4e5, 0x6402, 0xe54f, 0xe4e0, 0xe4e9,
  0xe53c, 0xe550, 0x63ae, 0xe547, 0x63ad, 0xe547, 0xe552, 0xe54d,
  0xe553, 0xe54c, 0xe228, 0xe54e, 0xe396, 0xe55e, 0xe55f, 0xe4fe,
  0xe546, 0xe559, 0xe560, 0xe55d, 0xe36f, 0xe4ff, 0xe535, 0xe557,
  0xe558, 0xe55b, 0x222c, 0x2257, 0x2277, 0xe4f2, 0x434d, 0xe55c,
  0xe556, 0xe542, 0x63fd, 0xe508, 0xe33e, 0xe55a, 0xe4e7, 0xe564,
  0xe56a, 0x63e5, 0xe508, 0x63a6, 0xe54f, 0xe4d9, 0xe4f4, 0xe563,
  0xe4f9, 0xe566, 0xe561, 0xe50f, 0xe49f, 0xe56b, 0x630f, 0xe4d4,
  0x6481, 0xe562, 0xe568, 0x6244, 0x6295, 0x62e8, 0x6435, 0xe4c1,
  0xe50e, 0xe50f, 0xe544, 0xe54c, 0x638c, 0xe570, 0xe56f, 0xe56d,
  0x6244, 0x6295, 0x62e8, 0x6421, 0xe4c1, 0xe56e, 0x622c, 0xe4ce,
  0xe4db, 0xe571, 0x629a, 0xe4eb, 0x62f3, 0xe4f8, 0xe360, 0xe56c,
  0xe554, 0x6274, 0x6373, 0x6452, 0xe46c, 0x6274, 0x6373, 0x6451,
  0xe46c, 0xb7aa, 0xe572, 0xe527, 0x647d, 0xe4bb, 0x47ff, 0x483f,
  0x4926, 0x6271, 0xe2db, 0x6909, 0xe95f, 0xe574, 0xe4c4, 0xe573,
  0xa228, 0xe565, 0x4089, 0xe229, 0x6274, 0x6373, 0x6451, 0xe452,
  0xe567, 0xe2df, 0xe4a5, 0xe575, 0xe576, 0x4f36, 0xcf50, 0xe54a,
  0xe569, 0xe523, 0x645a, 0xe4bb, 0xe2ae, 0xa1ff, 0x641d, 0xe562,
  0xe54b, 0x61d1, 0xe1d2, 0x61d3, 0x61fb, 0xe337, 0xe1d4, 0xe1dd,
  0xe1d8, 0xe1d7, 0xe1d9, 0xe1d5, 0xe1f7, 0xe1fa, 0xe1e7, 0xe1e4,
  0xe212, 0xe1e9, 0xe1e3, 0xe346, 0xe1f9, 0xe35a, 0xe1f5, 0xe203,
  0xe223, 0xe208, 0xe226, 0xe245, 0xe20d, 0xe214, 0x6221, 0x637e,
  0x6418, 0xe53a, 0xe209, 0xe2c7, 0xe2fc, 0xe211, 0xe210, 0x6205,
  0xe470, 0xbb3d, 0xe21e, 0xe3a2, 0x620e, 0xe264, 0xe227, 0xe201,
  0xe225, 0xe204, 0xe215, 0xe200, 0xe23a, 0x62ad, 0xe322, 0xe266,
  0xe257, 0xe237, 0x46cb, 0x4f3d, 0xe262, 0xe233, 0xe255, 0xe23d,
  0xe238, 0xe25e, 0x6246, 0x645a, 0xe47d, 0xe26c, 0xe26d, 0xe240,
  0xe23f, 0xe23e, 0x6244, 0x6421, 0xe435, 0xe251, 0xe234, 0xe460,
  0x6206, 0xe25b, 0xe25a, 0x2228, 0xe24b, 0xe230, 0xe249, 0x6247,
  0xe248, 0xe24d, 0x622e, 0xe268, 0xe239, 0x622c, 0xe438, 0xe276,
  0xe2ac, 0xe2a0, 0xe27a, 0xe2e9, 0x630f, 0xe41a, 0xe2aa, 0xe2ee,
  0xe2cf, 0xe2e3, 0xe403, 0xe28d, 0xe43a, 0xe285, 0xe2c1, 0xe2b1,
  0xe2a6, 0xe3a7, 0xe358, 0xe296, 0xe291, 0xe2cc, 0xe2a9, 0xe29b,
  0xe3f5, 0xe293, 0xe3a9, 0xe27f, 0x629a, 0xe443, 0xe27b, 0xe298,
  0xe31a, 0xe2ab, 0xe278, 0xe2a5, 0x222c, 0x2257, 0x2277, 0xe3df,
  0xe283, 0xe40b, 0xe2a8, 0xe280, 0xe2a3, 0x62f3, 0xe444, 0xe412,
  0x5216, 0x5217, 0xe2ea, 0xe2d9, 0xe338, 0xe2f1, 0xe3c8, 0x635e,
  0xe3d7, 0xe2b7, 0xe396, 0xe2f0, 0xe2e5, 0x5021, 0xe2e4, 0xe34b,
  0xe2ef, 0xe2e8, 0x62b9, 0x63e5, 0xe3fd, 0xe2bc, 0xe2dd, 0xe2d2,
  0xe2c5, 0xe2f6, 0xe426, 0x6417, 0xe427, 0x62b3, 0xe2ed, 0xe2bb,
  0xe2c3, 0xe2df, 0xe2e6, 0xe312, 0xe306, 0xe37a, 0xe369, 0xe32f,
  0xe328, 0xe31b, 0xe321, 0xe340, 0xe301, 0xe315, 0xe329, 0xe32b,
  0xe32e, 0xe47c, 0x6318, 0xe39a, 0xe310, 0xe326, 0xe455, 0xe341,
  0xe308, 0x6343, 0xe36f, 0xe307, 0xe31f, 0xe320, 0xe375, 0xe2f8,
  0xe333, 0xe319, 0xe365, 0xe348, 0xe347, 0xe3d8, 0xe376, 0xe354,
  0xe364, 0xe36c, 0x637e, 0xe49f, 0xe35b, 0xe3aa, 0xe360, 0xe370,
  0xe384, 0xe34d, 0xe382, 0xe3e4, 0xe3a1, 0xe428, 0xe387, 0xe3cc,
  0x63ad, 0xe3ae, 0xe39b, 0xe398, 0xe477, 0xe482, 0x63b8, 0xe42b,
  0xe3b3, 0xe3bf, 0x63a6, 0xe402, 0xe3ac, 0xe38a, 0xe3b0, 0xe3b5,
  0xe44c, 0xe3e2, 0xe3dc, 0xe3dd, 0xe3cd, 0xe3f0, 0xe3de, 0xe3e1,
  0xe3d1, 0xe3c3, 0xe3c7, 0xe3d0, 0xe414, 0x641d, 0xe481, 0xe410,
  0xe3f7, 0xe465, 0xe413, 0xe46d, 0xe420, 0xe479, 0xe3f9, 0xe419,
  0xe44a, 0xe433, 0xe436, 0xe432, 0x638c, 0xe42e, 0xe43f, 0xe454,
  0xe463, 0xe45e, 0xe471, 0xe472, 0x1ee7, 0x214f, 0x6578, 0xe57f,
  0x1ee7, 0x214f, 0x6577, 0xe57f, 0x1e45, 0x9e46, 0x1ee7, 0x214f,
  0x6577, 0xe578, 0xe5e8, 0xe5e9, 0xe5ea, 0xe5eb, 0x6589, 0xe5ed,
  0xe5ec, 0x6587, 0xe5ed, 0xaf00, 0xe5f6, 0xe5f3, 0x65a0, 0xe5f0,
  0x6592, 0xe5f2, 0x6591, 0xe5f2, 0xe5f4, 0xe5f5, 0xe5f8, 0x65f9,
  0xeb27, 0x658f, 0xe5f0, 0xe602, 0x2173, 0x65d7, 0xe5dc, 0xe601,
  0xa408, 0xe600, 0xa4c4, 0xe5fa, 0xe5fd, 0xe603, 0xe606, 0xe5fe,
  0x65b2, 0xe605, 0x65b1, 0xe605, 0x65c3, 0xe612, 0xe60a, 0x2266,
  0xe609, 0xe60e, 0xe60f, 0xe60d, 0xe608, 0xe60c, 0x65b4, 0xe612,
  0xb77f, 0xb697, 0xe5f1, 0x3ff6, 0xe614, 0xe615, 0xe611, 0xe607,
  0xe617, 0xe618, 0xe5ff, 0xe616, 0xe619, 0xe5ef, 0x2173, 0x65a2,
  0xe5dc, 0x3597, 0x6b25, 0x6b26, 0x6b2a, 0xeb2d, 0x242f, 0x2518,
  0x2553, 0x2554, 0x255f, 0xdaec, 0x2173, 0x65a2, 0xe5d7, 0xe61a,
  0xe613, 0xe610, 0xdf9f, 0xe61b, 0xe5fc, 0xe580, 0xe582, 0xe583,
  0xe586, 0xe588, 0x6587, 0xe589, 0xa54f, 0xe5d6, 0x658f, 0xe5a0,
  0xe5c8, 0x6591, 0xe592, 0xe58e, 0xe593, 0xe594, 0xe58c, 0xb0b6,
  0xe598, 0x6599, 0xeb27, 0xe5a8, 0xd05e, 0xe5e5, 0xe5a9, 0xe5ad,
  0xe5d3, 0xe5a5, 0xe5a3, 0xe5a1, 0xe5ab, 0xeb2e, 0x65b1, 0xe5b2,
  0xe5ac, 0xe5cd, 0xe5be, 0x2266, 0xe5b9, 0xe5b6, 0xeb29, 0xe5bf,
  0xe5bd, 0xe5bb, 0xe5bc, 0xe5e1, 0xe5cc, 0x65b4, 0xe5c3, 0xe5e0,
  0x3ff6, 0xe5ca, 0xe5cb, 0xe5d4, 0xe5d0, 0xe5d2, 0xe5d5, 0x65de,
  0xeb2b, 0xe5e4, 0xe61d, 0x6091, 0xe61c, 0xe68a, 0x1f8c, 0x6634,
  0x6670, 0xe682, 0x6633, 0xe67d, 0xe638, 0x2347, 0xe65e, 0x2742,
  0xac85, 0x274f, 0xa76f, 0xa751, 0xa740, 0xca7d, 0x6626, 0xe67d,
  0x1f8c, 0x6625, 0x6670, 0xe682, 0xe663, 0x2826, 0xe68e, 0xe628,
  0xbcae, 0x2768, 0xe641, 0x2768, 0xe640, 0xe69b, 0xe678, 0xe6b4,
  0xe673, 0xe658, 0xa905, 0x665c, 0x665d, 0xe67f, 0xa7c8, 0xe649,
  0x6655, 0xe67f, 0xe655, 0x2347, 0xe629, 0xaf8f, 0x2866, 0xe635,
  0xe677, 0x2c9b, 0x2cf6, 0x2d8b, 0x2d8c, 0x2db9, 0x669d, 0xe6af,
  0xe695, 0x4906, 0x667a, 0xe6aa, 0x1f8c, 0x6625, 0x6634, 0xe682,
  0xe648, 0xe665, 0xe646, 0x4906, 0x6669, 0xe6aa, 0x2794, 0xa819,
  0x6626, 0xe633, 0x6655, 0xe65c, 0x2830, 0xa895, 0x1f8c, 0x6625,
  0x6634, 0xe670, 0x27de, 0xa824, 0xe61f, 0xb697, 0x2826, 0xe636,
  0xe6a8, 0x1e5a, 0x66a0, 0xe6b1, 0x48d2, 0xe668, 0xa75e, 0xe69f,
  0xe645, 0x2c9b, 0x2cf6, 0x2d8b, 0x2d8c, 0x2db9, 0x6666, 0xe6af,
  0xe699, 0x1e5a, 0x6690, 0xe6b1, 0x23b8, 0x60bb, 0xe130, 0xe68f,
  0x4906, 0x6669, 0xe67a, 0x275f, 0xa8b3, 0xde8b, 0x2c9b, 0x2cf6,
  0x2d8b, 0x2d8c, 0x2db9, 0x6666, 0xe69d, 0x1e5a, 0x6690, 0xe6a0,
  0xea98, 0xe647, 0x66b7, 0xe6b8, 0x66b6, 0xe6b8, 0x66b6, 0xe6b7,
  0x2bc9, 0x674d, 0x674e, 0x674f, 0x6db4, 0x6e16, 0xee64, 0xa3ea,
  0xe6cb, 0xe6e3, 0x6ceb, 0xed08, 0x20f1, 0xecf8, 0xe6bd, 0xe6dd,
  0xe6db, 0x3742, 0xe6dc, 0x2f6b, 0x4431, 0xed70, 0xd67d, 0xa3cc,
  0xee1b, 0x66cf, 0xedb5, 0x3742, 0xe6d1, 0xe6cd, 0x6d8f, 0x6dc4,
  0xee21, 0xadb2, 0x5b8e, 0xdb90, 0xc9bb, 0xe6be, 0xbc1b, 0x9e91,
  0xe742, 0xe717, 0xe741, 0x671a, 0xe727, 0xc535, 0x671a, 0xe727,
  0xe73d, 0xeef4, 0x4075, 0x671b, 0xe748, 0xbcbe, 0xe6f6, 0x66fa,
  0x66fe, 0xe727, 0x4075, 0x670a, 0xe748, 0xe722, 0xe721, 0x66fa,
  0x66fe, 0xe71a, 0xe744, 0xd987, 0x1e30, 0x5c4a, 0x5c50, 0xe74a,
  0xe701, 0xe6f7, 0xe6f3, 0xe72d, 0xa3c7, 0x4075, 0x670a, 0xe71b,
  0xa3c6, 0x1e30, 0x5c4a, 0x5c50, 0xe73b, 0x2bc9, 0x66ba, 0x674e,
  0x674f, 0x6db4, 0x6e16, 0xee64, 0x2bc9, 0x66ba, 0x674d, 0x674f,
  0x6db4, 0x6e16, 0xee64, 0x2bc9, 0x66ba, 0x674d, 0x674e, 0x6db4,
  0x6e16, 0xee64, 0x2bc8, 0xe752, 0x2bc8, 0xe751, 0xe75a, 0x2172,
  0x2929, 0xe75d, 0xe75c, 0xe753, 0xe759, 0x2172, 0x2929, 0xe754,
  0x4cc6, 0x6763, 0x6eaa, 0x6eab, 0x6eb5, 0xeeba, 0xe762, 0x45b1,
  0xc6b0, 0xe768, 0xe82e, 0xe765, 0x676d, 0xe771, 0x676b, 0x6771,
  0xe7e7, 0x676b, 0x676d, 0xe7e7, 0xe7be, 0xcd46, 0x6789, 0xef17,
  0x6780, 0xef17, 0xe7b5, 0xe78d, 0xe78c, 0xade9, 0xe7c3, 0xe7bd,
  0xe7b9, 0x49cb, 0xe7a7, 0x49cb, 0xe7a6, 0xc89f, 0xe7c9, 0x5f41,
  0xe7dc, 0xe7dd, 0xe78b, 0xe79f, 0xe792, 0xe774, 0x4e6e, 0xcf30,
  0xe791, 0xa343, 0x589c, 0xe7e4, 0xe7af, 0xe7e6, 0xe7e7, 0x2dff,
  0xe7e8, 0xe7e9, 0xe7ea, 0x5f41, 0x67b1, 0xe7ec, 0xe7b2, 0xe7eb,
  0x589c, 0xe7c8, 0xe7cb, 0x676d, 0x6771, 0xe7cc, 0x2dff, 0xe7cd,
  0xe7d3, 0xe7d9, 0xe7de, 0x5f41, 0xe7dc, 0xe7ee, 0xe7ed, 0xe7f1,
  0xe7ef, 0x6f4f, 0xef51, 0xe7fb, 0xe7f5, 0xa4cd, 0xe875, 0xe876,
  0xe877, 0xe879, 0xe87a, 0xe878, 0xe87b, 0xe87c, 0xe882, 0xe880,
  0xe883, 0xe884, 0xe87d, 0xe881, 0xe87f, 0xe887, 0xe886, 0x6838,
  0xe888, 0xe88c, 0xe889, 0x6825, 0xe890, 0x6824, 0xe890, 0xe88f,
  0x6830, 0xe88a, 0xa934, 0x6767, 0xe892, 0x682c, 0xe88a, 0xe88b,
  0x4a4e, 0xe896, 0xe894, 0x681a, 0xe888, 0x4a68, 0x683a, 0x683d,
  0xe893, 0x4a68, 0x6839, 0x683d, 0xe893, 0xe891, 0x5cf4, 0xdd56,
  0x4a68, 0x6839, 0x683a, 0xe893, 0xeaed, 0xe897, 0xd16e, 0xe898,
  0xe89d, 0xe89a, 0x6854, 0xe89c, 0xe899, 0xe89b, 0x684f, 0xe89c,
  0x363e, 0xe86f, 0xa6df, 0xb13f, 0xe8a1, 0x685b, 0xe8a0, 0x685a,
  0xe8a0, 0xcc7b, 0xe89f, 0xe8a2, 0xe87e, 0xe8a4, 0xe8a5, 0x363e,
  0xe855, 0xe8a6, 0x6885, 0x6ad7, 0xee15, 0xe89e, 0xe8a7, 0xe801,
  0xe802, 0xe803, 0xe807, 0xe805, 0xe806, 0x6808, 0xeb1a, 0xe80a,
  0xe811, 0xe867, 0xe813, 0xe80e, 0xe812, 0xe80c, 0xe80f, 0xe810,
  0x6871, 0xead7, 0xe818, 0xe817, 0x681a, 0xe838, 0xe821, 0x682c,
  0xe830, 0xe832, 0xe81c, 0xbf41, 0xc1b2, 0xe826, 0x6824, 0xe825,
  0xe83b, 0xe82e, 0x4a68, 0x6839, 0x683a, 0xe83d, 0xe837, 0x4a4e,
  0xe834, 0xe846, 0xe84c, 0xe852, 0xe84e, 0xe853, 0x684f, 0xe854,
  0xe84d, 0xe873, 0xe862, 0x685a, 0xe85b, 0xe859, 0xe865, 0xce87,
  0xe86b, 0xe86c, 0xe870, 0xe874, 0x1e30, 0x21e8, 0x21ec, 0x21ee,
  0x5af7, 0x5bbd, 0x68cc, 0xe8ce, 0xe8d0, 0x68b7, 0x68c6, 0x68c8,
  0xe8d1, 0xe8d2, 0xa3f0, 0xa22e, 0xe8d3, 0x68ae, 0x68c6, 0xe8c8,
  0xe8d4, 0xe8cf, 0xe8d6, 0xe8d5, 0xe8d7, 0x68c4, 0xe8d8, 0x68c3,
  0xe8d8, 0x68ae, 0x68b7, 0x68c7, 0x68c8, 0xe8d9, 0xe8c6, 0x68ae,
  0x68b7, 0x68c6, 0xe8da, 0x21e8, 0x21ec, 0x21ee, 0x68a8, 0xe8ce,
  0x1e30, 0x21e8, 0x21ec, 0x21ee, 0x68a8, 0xe8cc, 0xe8ba, 0xe8ad,
  0xe8ae, 0xe8af, 0xe8b6, 0xe8b8, 0xe8bc, 0xe8bb, 0xe8c0, 0x68c3,
  0xe8c4, 0xe8c6, 0xe8c8, 0xe8de, 0xcffb, 0xe8db, 0x25b0, 0x68e0,
  0xe963, 0x25b0, 0x68df, 0xe963, 0xe910, 0xe965, 0xe964, 0xe966,
  0xe957, 0xe968, 0xe96a, 0x49d7, 0xe96b, 0xe90a, 0xe96c, 0x68f2,
  0xe96e, 0x68f0, 0xe96d, 0xe8ef, 0x68ee, 0xe96e, 0xe974, 0xe99d,
  0xe972, 0xe971, 0xe970, 0xe973, 0xe97a, 0xe978, 0x6920, 0xe97c,
  0x645c, 0x695f, 0xe977, 0x217b, 0x4faa, 0xe8ec, 0xe975, 0xe95c,
  0xe979, 0xe97b, 0xe8e1, 0xe97d, 0x6927, 0xe981, 0xe97f, 0xe982,
  0xe97e, 0x1f59, 0xe980, 0xd0b4, 0xe984, 0xe983, 0xe96f, 0xe18a,
  0x6905, 0xe97c, 0xe985, 0x2582, 0x6912, 0x6935, 0xe981, 0x5218,
  0xe986, 0xccca, 0xccc7, 0xe967, 0xe959, 0x2582, 0xe927, 0xe989,
  0xe987, 0xe967, 0xe98e, 0xccd5, 0xe969, 0xe988, 0xe98f, 0xe98a,
  0xe98c, 0xe98d, 0xe992, 0xe990, 0xe991, 0xe993, 0xe988, 0xe994,
  0xd1b3, 0xcce6, 0xe965, 0xe976, 0xe8e8, 0xe934, 0xe90d, 0xe98b,
  0x645c, 0x6909, 0xe977, 0xe995, 0x25b0, 0x68df, 0xe8e0, 0xe8e3,
  0x68e2, 0xe951, 0xe8e5, 0x6933, 0xe939, 0xe8e9, 0xe93c, 0xe8ea,
  0xe8eb, 0xe8ed, 0xe8ef, 0x68ee, 0xe8f2, 0xe91e, 0xe8fe, 0xe8fd,
  0xe8fc, 0xe8ff, 0xe8f4, 0xe90c, 0xe952, 0x6909, 0xe95f, 0xe904,
  0xe90e, 0xe903, 0xe90f, 0x6905, 0xe920, 0xe911, 0xe916, 0xe913,
  0x1f59, 0xe918, 0x6912, 0xe927, 0xe915, 0xe91c, 0xe91b, 0xe921,
  0xe928, 0xe937, 0x693d, 0xe94b, 0xe936, 0xe93f, 0xe95e, 0xe941,
  0xe943, 0xe93a, 0xe93e, 0xe948, 0xe949, 0xe945, 0xe94a, 0xe94c,
  0xe962, 0xe8f6, 0xe99f, 0xe99e, 0xea6c, 0xea6d, 0xa1af, 0x69c4,
  0xea6e, 0xea5d, 0xea70, 0xea6f, 0xea72, 0xce36, 0x6a62, 0xea74,
  0xea73, 0x69b1, 0xea6e, 0x6a5b, 0xea7f, 0x357a, 0x69c8, 0x6a45,
  0xea71, 0x357a, 0x69c6, 0x6a45, 0xea71, 0xea7b, 0xea7d, 0xea79,
  0xea75, 0xea7e, 0xea80, 0xea78, 0xea76, 0x69de, 0xea7c, 0x69dd,
  0xea7c, 0xea77, 0x208c, 0x4f75, 0xea82, 0xea88, 0xea87, 0xea73,
  0xea83, 0xea86, 0x6a2e, 0xea51, 0xea8e, 0xea8f, 0xea8b, 0xea8d,
  0xea93, 0xea92, 0xea91, 0xea90, 0x6a13, 0x6a57, 0xea8c, 0x6a37,
  0xea9a, 0x6a10, 0x6a57, 0xea8c, 0xea9b, 0xea97, 0xea99, 0xea52,
  0xea9e, 0xea98, 0x69f5, 0x6a51, 0xea9d, 0xd17e, 0xea7a, 0x6a12,
  0xea9a, 0xea9f, 0xea47, 0xeaa1, 0xd4e6, 0xea9c, 0xea96, 0xeaa0,
  0xeaa2, 0x357a, 0x69c6, 0x69c8, 0xea71, 0xea3a, 0xea85, 0xea95,
  0xea81, 0xeaa3, 0x69f5, 0x6a2e, 0xea9d, 0xea28, 0xea84, 0x6a10,
  0x6a13, 0xea8c, 0xb0ca, 0x69c5, 0xea7f, 0xe9b2, 0xeaa4, 0x69bf,
  0xea74, 0xeaa7, 0x560e, 0xeaa5, 0xeaa6, 0xbb22, 0xea8a, 0xea89,
  0xe9ac, 0xe9ad, 0x69b1, 0xe9c4, 0xe9b4, 0xe9b3, 0x357a, 0x69c6,
  0x69c8, 0xea45, 0xe9b9, 0x69c1, 0xe9ee, 0x69bf, 0xea62, 0xe9d4,
  0xe9db, 0xe9df, 0xe9d9, 0xe9d2, 0xea36, 0xe9d0, 0x69dd, 0xe9de,
  0xe9d1, 0xe9d5, 0x69c5, 0xea5b, 0xe9d8, 0xea4d, 0x208c, 0x4f75,
  0xe9e1, 0xe9f0, 0xea55, 0xea4a, 0xe9f1, 0xe9ed, 0xe9e2, 0xea6b,
  0xea6a, 0xea01, 0x6a10, 0x6a13, 0xea57, 0xea02, 0xe9f8, 0xe9ff,
  0xea0f, 0xea0e, 0xea0d, 0xea05, 0xea4c, 0xea42, 0xea19, 0x66b2,
  0xea2d, 0xea24, 0x6a12, 0xea37, 0xea16, 0xea41, 0x6a2e, 0xea51,
  0xea2b, 0xea38, 0xea43, 0xea3e, 0xea44, 0xea4f, 0xea5f, 0x560e,
  0xea65, 0xea66, 0xea64, 0xd0ae, 0x1f53, 0x5eb0, 0x5ec6, 0xead4,
  0xeca0, 0x51b8, 0xead3, 0xeacf, 0xd180, 0xead6, 0xead5, 0xeac5,
  0xd10f, 0x51b8, 0xeac4, 0x1f53, 0x5eb0, 0x5ec6, 0xeab5, 0x51cf,
  0xeacc, 0xeacb, 0x6871, 0x6885, 0xee15, 0xead9, 0xead8, 0x1eff,
  0xaf77, 0xeaf9, 0xeaef, 0x6b02, 0xeb22, 0x23d1, 0xeaee, 0xe83e,
  0x23d1, 0xeaea, 0xeae5, 0x1f5b, 0xaf7f, 0xeae4, 0xa243, 0x6ae9,
  0x6b13, 0xeb22, 0xb77e, 0xd0e1, 0x6b02, 0xeb22, 0xe87b, 0xeb23,
  0x6ae9, 0x6b02, 0xeb13, 0xeb1b, 0x3597, 0x65d8, 0x6b26, 0x6b2a,
  0xeb2d, 0x3597, 0x65d8, 0x6b25, 0x6b2a, 0xeb2d, 0x6599, 0xe5f9,
  0xa4c4, 0xe60b, 0x3597, 0x65d8, 0x6b25, 0x6b26, 0xeb2d, 0xe61a,
  0x3597, 0x65d8, 0x6b25, 0x6b26, 0xeb2a, 0xe604, 0x3b1d, 0x60c1,
  0xeb31, 0x3b1d, 0x60c1, 0xeb30, 0xeb39, 0xeb36, 0x1ffc, 0xcca5,
  0xac2c, 0xeb58, 0xeb4e, 0x573d, 0xeb49, 0x21f2, 0x2c32, 0x2c34,
  0x2c36, 0xac37, 0x1e11, 0xe19c, 0xeb47, 0xec7c, 0xec7d, 0xec7e,
  0xec6e, 0xebbb, 0xec80, 0xeeff, 0xec81, 0x6c78, 0xec88, 0xec24,
  0xec82, 0xec7f, 0xec84, 0xec85, 0xec86, 0x6bdb, 0xecb7, 0xec8c,
  0xec89, 0xec8f, 0xec87, 0xebf9, 0xec90, 0xec8d, 0xec8b, 0xec8a,
  0xec92, 0xec98, 0x6bd7, 0x6c76, 0xec9e, 0xec95, 0xec96, 0x6bb7,
  0xebf7, 0xec94, 0xec9b, 0xec91, 0x2c1f, 0x2c20, 0x5d7b, 0x6c7b,
  0xec9c, 0xec93, 0xecaa, 0x6ba7, 0xebf7, 0xec9d, 0xeb66, 0xebd8,
  0xeca7, 0xeca0, 0x6c00, 0xeca9, 0xeca4, 0x6bcb, 0xeca8, 0xebca,
  0xecac, 0xecbb, 0xecaf, 0xecad, 0x6b9d, 0xec76, 0xebbe, 0x6b89,
  0xecb7, 0xecb4, 0xecb1, 0xecb5, 0xecb2, 0xecb3, 0xecb8, 0xecae,
  0xecb0, 0xecb6, 0xecba, 0xec3a, 0x6ba7, 0x6bb7, 0xecc0, 0xeb8f,
  0xecab, 0xecca, 0x6bc7, 0xeca9, 0xecc8, 0xec97, 0xecc2, 0x6c28,
  0xecbd, 0xecc7, 0x6c0d, 0xecc5, 0x6c0c, 0xecc5, 0xecbe, 0x6c77,
  0xecc4, 0xecc6, 0xecc3, 0x6c2e, 0xecc1, 0xecd2, 0xecd1, 0xeccb,
  0xeca5, 0xeb73, 0x6c5e, 0xeccf, 0x6c08, 0xecce, 0xecd0, 0xeccd,
  0xec1b, 0xeca2, 0x6ccc, 0xef07, 0xecd3, 0xecd8, 0xeca6, 0xeca3,
  0x6bf5, 0xecb9, 0xecd7, 0xecdb, 0xecd4, 0xecc9, 0xecd9, 0xecd5,
  0x6cd6, 0x6f08, 0xef9e, 0xecdf, 0xecdd, 0xecdd, 0xecdc, 0xecde,
  0xec9f, 0xecbc, 0xec25, 0xec8e, 0xec99, 0xece3, 0xece1, 0xece2,
  0xecbf, 0xec9a, 0xeb63, 0xece0, 0x6b9d, 0x6bd7, 0xec9e, 0x6c10,
  0xecc4, 0x6b72, 0xec88, 0xeca1, 0x2c1f, 0x2c20, 0x5d7b, 0x6bae,
  0xec9c, 0xeb5a, 0xeb5b, 0xeb62, 0xeb77, 0xeb68, 0xeb6f, 0xeb74,
  0xeb7a, 0xeb81, 0xeb83, 0xeb8e, 0x6b72, 0xec78, 0xeb8b, 0xeb93,
  0xeb92, 0xeb8a, 0x56ab, 0xeb91, 0xec5f, 0xeb8d, 0xeb90, 0xebad,
  0xeb9a, 0xebb3, 0xebaa, 0xeb9e, 0xeba6, 0xec02, 0xeb9c, 0xec60,
  0xec6d, 0xebab, 0x2c1f, 0x2c20, 0x5d7b, 0x6bae, 0xec7b, 0xebba,
  0x6b9d, 0xec76, 0xec58, 0x6abe, 0xebc1, 0xec7a, 0xec31, 0xec39,
  0xebc9, 0xec23, 0xec37, 0xebc0, 0xebca, 0x6bc7, 0xec00, 0xebb6,
  0xebfd, 0xebd2, 0xebd6, 0xebea, 0xebd5, 0xebeb, 0xebe1, 0xebe4,
  0xebe7, 0xebdd, 0xebe2, 0xebf0, 0x6b89, 0xebdb, 0xebe8, 0xec3a,
  0xebf4, 0xebd4, 0xec5d, 0xec08, 0xec0f, 0xec68, 0xebf7, 0xec1b,
  0xec03, 0xec13, 0x6c10, 0xec77, 0x6c0c, 0xec0d, 0xec12, 0xec09,
  0xec01, 0xec42, 0xebff, 0xec20, 0x6c32, 0xef07, 0xec2d, 0xec28,
  0xec25, 0xec29, 0xec1f, 0xec1c, 0xec33, 0xec3e, 0x51a4, 0xec48,
  0x6c49, 0xef08, 0xec3b, 0xec35, 0xec45, 0xec3c, 0xec56, 0x6c53,
  0xec54, 0xec57, 0xec52, 0xec6f, 0xec64, 0xec67, 0xec63, 0x2c4c,
  0xee1f, 0x21eb, 0xecec, 0xee20, 0x66c1, 0xed08, 0xece7, 0xee24,
  0xa1e4, 0xee23, 0xee22, 0xe6c7, 0xee29, 0xee28, 0x66c1, 0xeceb,
  0x6d76, 0xee26, 0x6dd7, 0xee25, 0xee30, 0xee35, 0xee33, 0x6e1c,
  0xee32, 0xee2e, 0x6d44, 0xee31, 0xee2a, 0xee2f, 0xee2d, 0x53ba,
  0xedaf, 0xee38, 0xee39, 0xedc3, 0xee3b, 0xee3f, 0xedfa, 0xee3d,
  0xee3a, 0xee3c, 0xed1f, 0xee40, 0xee43, 0xee46, 0xee41, 0xedaa,
  0xee48, 0x6d5e, 0xee45, 0x6d5d, 0xee45, 0xee44, 0xee49, 0xee4c,
  0xee4f, 0xee50, 0xee4e, 0xe6d5, 0xee4a, 0x6d09, 0xee26, 0xee53,
  0x6da4, 0xee4d, 0x6dab, 0xee2b, 0xee51, 0xee52, 0x66de, 0x6dc4,
  0xee21, 0xee4b, 0xee59, 0xee55, 0xee57, 0xee56, 0xed7e, 0xee5b,
  0xee5c, 0xed59, 0x6d87, 0xee2b, 0xee27, 0x53ba, 0xed2c, 0xee5f,
  0x2bc9, 0x66ba, 0x674d, 0x674e, 0x674f, 0x6e16, 0xee64, 0xe6db,
  0xee60, 0xee61, 0xee58, 0xee63, 0xee5a, 0xee5a, 0xee62, 0xee5e,
  0xed33, 0x66de, 0x6d8f, 0xee21, 0xedcf, 0xedc9, 0xedc8, 0xee5d,
  0xedc6, 0xee67, 0xee65, 0x6d0e, 0xee25, 0xee37, 0xee68, 0xee36,
  0xee6a, 0xee54, 0xee69, 0xc1d5, 0xee6b, 0x6df4, 0xee47, 0x6df3,
  0xee47, 0xee6c, 0xee70, 0x6d3c, 0xee6d, 0xee34, 0xee0a, 0xee6f,
  0xedff, 0xee71, 0xd3ba, 0xee72, 0x6871, 0x6ad7, 0xee2c, 0x2bc9,
  0x66ba, 0x674d, 0x674e, 0x674f, 0x6db4, 0xee64, 0xee74, 0xee66,
  0x66da, 0xee73, 0xed1d, 0xee42, 0xee3e, 0x2c4c, 0xece5, 0xece9,
  0x66de, 0x6d8f, 0xedc4, 0xecf6, 0xecf4, 0xecf2, 0x6d0e, 0xedd7,
  0x6d09, 0xed76, 0xedac, 0xed07, 0xed06, 0xed23, 0x6d87, 0xedab,
  0xee15, 0xed28, 0xed1e, 0xed26, 0xed12, 0xed1f, 0xed1d, 0xed1b,
  0xedfd, 0xed15, 0xede5, 0xedd9, 0xed2f, 0xed30, 0xed42, 0xed34,
  0xed43, 0xed3f, 0xee1e, 0xed3b, 0xed50, 0xed53, 0xee1d, 0xed51,
  0xed60, 0x6d5d, 0xed5e, 0xed52, 0x6df3, 0xedf4, 0xed5c, 0xed61,
  0xed72, 0xed93, 0xed6a, 0xed7e, 0xed6f, 0xed6c, 0xed6e, 0xed89,
  0xed8a, 0xed77, 0xedeb, 0xed98, 0xeda1, 0xed9a, 0xedbb, 0xed96,
  0x6dbf, 0xedc0, 0xeda5, 0xeda9, 0xedca, 0xedc2, 0xedb2, 0xedb9,
  0xedba, 0xedc1, 0xedbc, 0x2bc9, 0x66ba, 0x674d, 0x674e, 0x674f,
  0x6db4, 0xee16, 0xedd6, 0xee1a, 0xedd3, 0xedda, 0xedef, 0xede6,
  0xedf2, 0xedf8, 0xedfa, 0xee07, 0xedf9, 0xee0c, 0xee0f, 0xee1b,
  0xee18, 0x2364, 0xa877, 0x4877, 0x48b1, 0xee7c, 0x24b8, 0xe18e,
  0xee7e, 0xc877, 0x4877, 0x48b1, 0xee78, 0x2869, 0xc6d0, 0xee7a,
  0xee85, 0xeea4, 0xee83, 0xee8f, 0xee87, 0x1e3d, 0xaa6f, 0xc350,
  0x4c97, 0xee84, 0xeea6, 0xeea5, 0x6eac, 0xeeb8, 0x4cc6, 0x6762,
  0x6eab, 0x6eb5, 0xeeba, 0x4cc6, 0x6762, 0x6eaa, 0x6eb5, 0xeeba,
  0x6ea9, 0xeeb8, 0xa305, 0x36f2, 0x4cac, 0x6eb4, 0xeeb9, 0x36f2,
  0x4cac, 0x6eaf, 0xeeb9, 0x4cc6, 0x6762, 0x6eaa, 0x6eab, 0xeeba,
  0x6ea9, 0xeeac, 0x36f2, 0x4cac, 0x6eaf, 0xeeb4, 0x4cc6, 0x6762,
  0x6eaa, 0x6eab, 0xeeb5, 0x45f2, 0x45f3, 0xd534, 0x1e48, 0x2c1b,
  0x2e85, 0xeebd, 0x1e48, 0x2c1b, 0x2e85, 0xeebc, 0xeec4, 0x48fa,
  0xeec3, 0xeecc, 0xeec9, 0xcc98, 0xeed2, 0xeed1, 0xeed9, 0xeed8,
  0xeef1, 0x294c, 0xc0b9, 0xeef6, 0xeeed, 0xa15a, 0xeef7, 0xeef2,
  0xeee4, 0xeedb, 0xeeea, 0xe709, 0xeee1, 0xeee9, 0xd8ae, 0xeefe,
  0xeefd, 0x6b6d, 0xef0b, 0xef0c, 0xd6d9, 0x6c32, 0xeccc, 0x6c49,
  0x6cd6, 0xef9e, 0xef0d, 0xeeff, 0xef02, 0xef09, 0xef11, 0xef0e,
  0xe3a1, 0x46b7, 0xc6bc, 0xa1ac, 0x6780, 0xe789, 0xef21, 0xef20,
  0xdc82, 0xef39, 0xef34, 0xa5c5, 0x1e9d, 0x3589, 0x358a, 0xef50,
  0x358b, 0xb58e, 0x5cf7, 0xdd4d, 0x67f2, 0xef51, 0x1e9d, 0x3589,
  0x358a, 0xef4a, 0x67f2, 0xef4f, 0x3b6f, 0xef7f, 0xef54, 0x6f53,
  0xef80, 0xef81, 0xef82, 0xef85, 0xef70, 0xef87, 0xef83, 0xef86,
  0x6f62, 0xef84, 0x6f61, 0xef84, 0xa1fa, 0xef88, 0x256e, 0x2699,
  0xa6d3, 0xa4ac, 0x6f71, 0xef8a, 0xef89, 0xef5a, 0xef6a, 0xef8b,
  0xd16d, 0xef8c, 0x3b6f, 0xef52, 0xef54, 0xef55, 0xef57, 0xef5f,
  0x6f61, 0xef62, 0xef59, 0xef60, 0xef5c, 0xef66, 0xef6c, 0xef6a,
  0xef72, 0xef77, 0x4adc, 0x6f92, 0xef99, 0xa390, 0xae9e, 0x4adc,
  0x6f8d, 0xef99, 0xef9a, 0xef9b, 0x6f8d, 0xef92, 0xef94, 0xef95,
  0x1e80, 0xef9f, 0x49cb, 0x49cc, 0xca50, 0x6c49, 0xef08, 0x1e80,
  0xef9c, 0x248a, 0x248c, 0xb0d2, 0x2401, 0xcc72,
};

static const short cjk_variants_indx[0x5200] = {
  /* 0x4e00 */
      4,    -1,    -1,    -1,     5,     6,    -1,     7,
     -1,     8,    10,    11,    12,    -1,    13,    -1,
     14,    16,    19,    20,    -1,    -1,    22,    24,
     26,    -1,    28,    29,    33,    34,    -1,    36,
     38,    40,    42,    44,    45,    47,    49,    52,
     -1,    -1,    53,    56,    59,    -1,    -1,    -1,
     60,    -1,    66,    67,    68,    -1,    -1,    -1,
     -1,    -1,    69,    -1,    -1,    71,    73,    -1,
     -1,    76,    77,    79,    81,    83,    85,    -1,
     87,    92,    -1,    -1,    93,    -1,    -1,    -1,
     94,    96,    -1,    -1,   100,   101,    -1,   103,
    105,    -1,   107,    -1,    -1,    -1,    -1,    -1,
    110,   111,    -1,    -1,    -1,    -1,   115,    -1,
    116,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
    118,   119,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,   121,    -1,    -1,    -1,    -1,   124,    -1,
    128,   130,   133,    -1,    -1,    -1,   135,    -1,
    137,   138,   139,   141,   143,    -1,   144,   147,
    151,   153,    -1,    -1,   155,    -1,   156,   157,
    161,   162,   163,    -1,   165,   167,   171,   173,
     -1,   174,    -1,    -1,    -1,    -1,    -1,   176,
     -1,   178,    -1,   182,   183,    -1,    -1,   184,
    185,   186,   187,    -1,    -1,   188,    -1,    -1,
    189,    -1,   190,   191,    -1,    -1,   192,   194,
     -1,   195,   196,    -1,    -1,   199,   201,   202,
     -1,   203,    -1,    -1,   204,    -1,   207,   209,
    210,   212,    -1,   215,    -1,    -1,    -1,    -1,
    217,   218,    -1,    -1,    -1,   220,   221,    -1,
     -1,    -1,   222,    -1,    -1,    -1,    -1,   223,
     -1,    -1,   227,    -1,   228,   229,   230,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,   232,
     -1,    -1,   234,    -1,   236,   237,   239,   240,
  /* 0x4f00 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
    245,    -1,    -1,    -1,    -1,    -1,   247,   248,
    251,   252,   253,   254,   255,   256,   259,   262,
    263,   265,    -1,   266,   267,   268,   269,   270,
     -1,    -1,   271,   273,    -1,    -1,   275,    -1,
     -1,   276,   279,    -1,    -1,   282,    -1,   283,
     -1,    -1,    -1,    -1,   284,    -1,    -1,    -1,
    285,    -1,   286,    -1,    -1,    -1,    -1,   287,
    288,    -1,    -1,    -1,    -1,    -1,   289,    -1,
     -1,    -1,    -1,   290,   295,    -1,    -1,    -1,
     -1,   296,    -1,   298,   301,    -1,   302,    -1,
    303,    -1,    -1,   307,    -1,   308,    -1,    -1,
     -1,    -1,   309,    -1,    -1,    -1,    -1,    -1,
     -1,   310,    -1,    -1,    -1,   312,    -1,   314,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,   315,   316,    -1,   318,    -1,
     -1,    -1,    -1,    -1,   319,    -1,   323,    -1,
    324,    -1,    -1,    -1,    -1,    -1,   325,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,   326,
    328,   329,    -1,   331,    -1,   332,   333,   335,
    336,   337,   338,   339,   340,   341,    -1,   343,
     -1,    -1,    -1,    -1,    -1,    -1,   344,   345,
     -1,    -1,    -1,    -1,    -1,   346,    -1,    -1,
     -1,   347,   348,    -1,    -1,    -1,    -1,   349,
     -1,    -1,   350,    -1,   351,    -1,   352,    -1,
     -1,    -1,    -1,    -1,   353,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,   355,   356,
    357,   358,   359,   360,    -1,   361,   362,    -1,
    363,   364,   365,   366,    -1,   367,   369,    -1,
     -1,   371,   372,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,   374,   378,   380,    -1,    -1,
  /* 0x5000 */
    382,    -1,   383,   385,    -1,   388,   389,    -1,
    390,   391,   393,   394,    -1,    -1,   397,   398,
    399,   400,    -1,    -1,    -1,    -1,   401,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,   402,   403,
     -1,    -1,    -1,   405,   407,    -1,   408,    -1,
     -1,    -1,    -1,   410,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,   411,    -1,    -1,   412,    -1,
     -1,   413,   415,    -1,   416,    -1,   417,    -1,
    418,    -1,    -1,    -1,    -1,    -1,   419,   420,
     -1,   421,    -1,    -1,    -1,    -1,    -1,    -1,
    422,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
    425,   426,   427,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,   428,    -1,    -1,
     -1,    -1,   429,    -1,   430,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,   432,   433,    -1,   435,
    436,   437,   441,   444,    -1,   445,   447,   448,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,   449,   452,    -1,    -1,
     -1,   453,    -1,    -1,    -1,    -1,   454,    -1,
    455,   458,   462,    -1,   464,    -1,    -1,    -1,
    465,   467,   470,    -1,   471,   472,    -1,   473,
    474,   475,    -1,    -1,    -1,   476,    -1,   477,
     -1,    -1,    -1,   478,   480,   481,    -1,   482,
     -1,   483,    -1,    -1,    -1,    -1,   484,    -1,
    485,    -1,   486,   487,   491,   492,    -1,    -1,
     -1,   493,   494,    -1,    -1,   495,    -1,    -1,
     -1,   498,    -1,    -1,    -1,   499,    -1,    -1,
    500,    -1,    -1,    -1,   501,    -1,   502,   504,
     -1,    -1,    -1,   505,    -1,   506,    -1,    -1,
    507,   508,    -1,   509,    -1,   512,    -1,    -1,
     -1,   513,   514,    -1,   515,    -1,    -1,    -1,
     -1,   516,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x5100 */
    518,   519,   520,    -1,   521,    -1,    -1,    -1,
    522,   523,    -1,    -1,    -1,    -1,   525,    -1,
    526,    -1,    -1,    -1,   527,   528,    -1,    -1,
    529,    -1,    -1,    -1,    -1,    -1,   531,   534,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,   535,
     -1,    -1,   536,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,   537,    -1,    -1,    -1,    -1,   538,
     -1,   539,   540,   541,   542,    -1,    -1,   543,
     -1,    -1,   545,    -1,    -1,    -1,    -1,   546,
     -1,   547,   549,   551,   554,    -1,   556,   557,
    561,   563,   565,    -1,   567,    -1,   568,   569,
    570,    -1,   572,    -1,   573,    -1,    -1,    -1,
    574,    -1,    -1,    -1,   575,    -1,   576,   578,
     -1,   579,   581,    -1,    -1,    -1,    -1,    -1,
    582,    -1,   583,   586,   591,    -1,   592,    -1,
     -1,   593,    -1,   595,    -1,   597,   599,    -1,
    600,   601,   602,   603,   604,   605,   606,    -1,
    609,   610,   611,   613,   614,    -1,   616,   618,
    619,   620,   621,    -1,    -1,    -1,    -1,   622,
     -1,   623,    -1,   625,   626,   627,    -1,    -1,
     -1,    -1,   629,   630,   631,    -1,   632,    -1,
    634,   635,   637,   639,   642,    -1,    -1,   644,
    645,   648,   649,   651,   652,   653,    -1,    -1,
     -1,    -1,   654,   655,    -1,   656,    -1,    -1,
    657,    -1,   659,   660,   661,   664,   665,    -1,
    667,   668,    -1,    -1,    -1,   669,    -1,   670,
     -1,   671,   672,   673,    -1,    -1,   674,    -1,
     -1,    -1,    -1,   676,   678,    -1,   680,    -1,
    684,   686,   688,   690,   692,   693,   694,    -1,
    696,    -1,    -1,   701,   702,   707,   710,   715,
     -1,   716,   717,   722,   723,    -1,   726,   727,
     -1,    -1,   730,   732,    -1,   734,   735,   736,
  /* 0x5200 */
    737,    -1,   739,   741,   742,   743,    -1,    -1,
    745,    -1,   747,    -1,    -1,   748,    -1,    -1,
     -1,    -1,   749,   752,   753,    -1,    -1,    -1,
    754,   755,   756,   757,    -1,    -1,    -1,    -1,
    762,    -1,    -1,    -1,    -1,   763,   764,   767,
    770,    -1,   773,   774,   776,   780,   781,    -1,
     -1,   782,    -1,    -1,   786,    -1,   787,    -1,
     -1,   788,   789,    -1,   790,   793,   794,   795,
    796,   797,   798,   800,   801,    -1,    -1,   802,
     -1,    -1,    -1,   803,   806,   807,   808,   809,
    813,   815,    -1,    -1,    -1,    -1,    -1,   823,
     -1,   827,   831,   832,    -1,   833,    -1,    -1,
     -1,    -1,    -1,   834,   842,   844,   845,   847,
     -1,   848,    -1,    -1,    -1,    -1,   849,    -1,
    852,   853,    -1,   861,   862,   863,    -1,   868,
     -1,   872,    -1,    -1,    -1,    -1,    -1,   873,
     -1,    -1,    -1,   874,   877,    -1,    -1,   878,
     -1,   879,   880,    -1,   881,   882,   890,    -1,
    898,   901,   903,    -1,   911,   919,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,   921,   923,   924,
     -1,   926,   927,    -1,    -1,    -1,    -1,    -1,
    928,    -1,   931,   932,    -1,    -1,    -1,    -1,
     -1,   935,   936,   937,   940,   943,    -1,    -1,
     -1,   945,    -1,    -1,    -1,    -1,    -1,   946,
     -1,   947,    -1,    -1,   948,   949,    -1,   951,
    952,    -1,   953,   956,   959,    -1,    -1,    -1,
     -1,   961,    -1,    -1,    -1,   965,   968,   970,
     -1,   972,   973,   974,    -1,   977,   978,    -1,
    981,    -1,   982,    -1,   983,    -1,   984,   985,
     -1,   987,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,   988,   989,   992,    -1,   995,    -1,    -1,
    996,    -1,    -1,   998,    -1,    -1,    -1,    -1,
  /* 0x5300 */
    999,    -1,    -1,  1000,  1002,  1004,  1005,    -1,
     -1,    -1,  1008,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   1009,    -1,  1011,  1012,    -1,  1014,  1016,    -1,
   1017,  1018,    -1,    -1,    -1,    -1,  1019,  1020,
   1021,  1023,    -1,    -1,    -1,  1024,  1025,  1026,
     -1,  1028,  1029,  1032,    -1,    -1,    -1,    -1,
   1035,    -1,  1036,  1037,    -1,    -1,    -1,    -1,
   1039,    -1,    -1,  1040,    -1,    -1,  1041,  1042,
     -1,  1046,    -1,  1047,    -1,  1049,  1050,  1053,
   1056,    -1,  1057,  1058,  1060,  1063,  1065,    -1,
   1067,    -1,  1069,  1070,  1071,    -1,    -1,    -1,
   1073,    -1,  1074,    -1,  1075,  1082,    -1,  1084,
     -1,    -1,    -1,  1085,    -1,    -1,  1087,  1088,
   1090,    -1,    -1,  1091,  1093,    -1,    -1,  1094,
   1096,  1097,    -1,  1099,    -1,  1100,    -1,    -1,
     -1,    -1,  1102,    -1,  1105,  1106,  1108,  1114,
   1115,  1116,    -1,  1117,  1119,  1120,  1121,    -1,
   1122,    -1,    -1,  1123,    -1,  1124,    -1,    -1,
   1126,  1130,  1131,  1132,    -1,    -1,    -1,  1133,
   1134,  1136,  1137,  1138,  1139,    -1,  1141,    -1,
   1142,  1143,    -1,    -1,    -1,  1145,  1146,  1147,
   1152,    -1,  1154,  1155,  1157,  1158,  1159,  1160,
   1161,    -1,  1164,  1165,    -1,    -1,  1166,  1168,
     -1,  1170,  1172,  1175,  1178,  1181,  1184,  1185,
   1186,  1187,  1188,    -1,  1189,  1190,  1193,  1194,
   1195,  1196,    -1,  1200,    -1,  1202,    -1,    -1,
   1203,  1205,    -1,  1207,    -1,    -1,  1209,    -1,
   1210,  1214,  1216,    -1,    -1,    -1,    -1,  1220,
     -1,    -1,  1223,  1226,    -1,    -1,    -1,    -1,
   1227,    -1,    -1,    -1,    -1,    -1,  1232,  1236,
     -1,  1237,    -1,    -1,    -1,  1240,    -1,    -1,
  /* 0x5400 */
   1241,  1243,    -1,  1245,    -1,    -1,    -1,    -1,
   1247,    -1,  1248,    -1,  1249,    -1,  1251,    -1,
     -1,  1252,    -1,  1254,    -1,  1255,    -1,  1256,
     -1,    -1,    -1,    -1,    -1,  1257,  1258,  1259,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   1262,    -1,    -1,    -1,  1263,    -1,    -1,  1265,
     -1,    -1,    -1,  1271,  1273,    -1,  1275,    -1,
     -1,    -1,    -1,  1276,    -1,  1277,    -1,  1278,
     -1,    -1,  1279,    -1,    -1,    -1,    -1,    -1,
   1280,  1282,  1284,    -1,  1285,  1286,    -1,    -1,
   1287,  1288,  1289,  1290,    -1,  1291,  1292,  1293,
   1294,  1297,  1299,  1300,  1301,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  1302,
   1303,  1304,  1305,    -1,    -1,  1307,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  1308,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  1311,    -1,    -1,
     -1,    -1,  1312,    -1,  1315,    -1,    -1,  1318,
     -1,    -1,  1319,    -1,    -1,    -1,    -1,    -1,
     -1,  1321,    -1,  1322,  1323,  1324,    -1,    -1,
     -1,    -1,    -1,    -1,  1325,    -1,    -1,    -1,
     -1,  1326,    -1,    -1,  1328,    -1,    -1,  1329,
     -1,  1331,  1334,    -1,    -1,    -1,    -1,    -1,
   1336,    -1,    -1,    -1,  1338,  1340,    -1,    -1,
     -1,    -1,    -1,    -1,  1341,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  1343,    -1,    -1,
     -1,  1344,  1348,  1349,  1350,  1351,    -1,  1352,
     -1,  1354,    -1,    -1,  1355,  1356,    -1,  1357,
     -1,  1358,    -1,  1361,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  1362,    -1,    -1,    -1,
     -1,    -1,  1363,    -1,    -1,    -1,  1368,    -1,
     -1,    -1,  1370,    -1,    -1,    -1,    -1,    -1,
  /* 0x5500 */
   1371,    -1,    -1,    -1,  1373,    -1,    -1,  1374,
     -1,    -1,    -1,    -1,    -1,  1375,    -1,    -1,
   1376,    -1,    -1,    -1,    -1,  1377,  1378,    -1,
   1382,    -1,    -1,  1388,    -1,  1389,    -1,    -1,
   1390,    -1,  1391,  1392,  1393,    -1,    -1,  1394,
     -1,    -1,    -1,  1395,    -1,    -1,    -1,    -1,
     -1,  1396,    -1,    -1,    -1,    -1,    -1,    -1,
   1397,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   1398,    -1,    -1,    -1,    -1,    -1,  1399,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  1400,
     -1,    -1,    -1,  1401,  1407,    -1,  1413,  1414,
     -1,    -1,    -1,    -1,    -1,    -1,  1415,  1419,
   1425,    -1,    -1,  1430,    -1,    -1,    -1,  1433,
     -1,    -1,    -1,    -1,  1434,  1435,  1436,  1440,
   1441,    -1,    -1,    -1,  1443,    -1,    -1,    -1,
   1444,    -1,  1445,    -1,    -1,    -1,    -1,    -1,
   1446,    -1,  1448,    -1,  1450,    -1,  1451,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  1456,    -1,
     -1,  1458,  1460,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  1463,    -1,  1464,    -1,  1467,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  1468,  1469,  1470,  1472,    -1,  1473,  1475,
   1477,    -1,  1480,    -1,    -1,    -1,  1481,  1483,
     -1,    -1,    -1,  1485,    -1,  1486,  1487,    -1,
     -1,    -1,    -1,    -1,    -1,  1488,  1489,  1490,
     -1,    -1,  1491,    -1,    -1,    -1,  1492,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  1493,    -1,  1494,    -1,    -1,    -1,
   1495,    -1,    -1,  1496,    -1,  1497,    -1,    -1,
     -1,  1498,    -1,  1499,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  1500,    -1,    -1,  1501,    -1,
     -1,  1502,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x5600 */
     -1,    -1,    -1,    -1,    -1,    -1,  1503,    -1,
     -1,    -1,  1505,    -1,    -1,  1506,    -1,    -1,
     -1,  1507,    -1,  1509,  1510,    -1,  1511,  1512,
   1515,    -1,    -1,    -1,  1516,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  1517,    -1,    -1,    -1,
   1518,  1519,    -1,    -1,    -1,    -1,  1521,  1522,
   1524,  1525,    -1,    -1,    -1,  1526,    -1,  1527,
   1528,    -1,    -1,    -1,    -1,  1529,    -1,    -1,
     -1,  1530,  1531,    -1,  1532,  1534,    -1,    -1,
     -1,  1535,    -1,    -1,    -1,    -1,    -1,    -1,
   1537,    -1,    -1,  1538,    -1,  1539,  1540,    -1,
     -1,    -1,    -1,  1542,  1544,  1545,    -1,    -1,
   1546,  1547,    -1,    -1,    -1,  1548,  1549,    -1,
   1550,    -1,  1551,    -1,    -1,    -1,    -1,  1552,
     -1,    -1,  1553,    -1,  1554,  1556,    -1,    -1,
   1559,  1560,    -1,    -1,    -1,    -1,  1561,    -1,
   1562,    -1,    -1,    -1,    -1,    -1,    -1,  1563,
     -1,    -1,    -1,    -1,  1564,    -1,    -1,  1565,
   1566,    -1,    -1,    -1,  1569,  1570,    -1,    -1,
     -1,  1571,    -1,    -1,    -1,    -1,  1575,    -1,
   1580,    -1,  1581,  1582,    -1,  1583,  1584,    -1,
   1585,    -1,    -1,    -1,    -1,    -1,  1586,    -1,
     -1,    -1,  1587,  1588,  1589,    -1,  1591,    -1,
     -1,    -1,    -1,  1592,    -1,    -1,    -1,    -1,
   1593,  1594,  1595,    -1,    -1,  1597,    -1,    -1,
   1598,  1599,  1601,    -1,  1602,    -1,    -1,  1603,
     -1,  1604,    -1,  1605,    -1,    -1,    -1,  1608,
   1611,  1617,    -1,  1618,    -1,    -1,  1619,  1625,
   1626,    -1,  1627,  1630,    -1,    -1,  1633,  1636,
     -1,    -1,  1637,    -1,  1638,  1644,    -1,  1647,
   1652,  1653,  1654,  1657,  1660,  1663,  1664,    -1,
   1669,    -1,    -1,  1670,    -1,  1675,  1680,    -1,
  /* 0x5700 */
   1683,    -1,    -1,  1688,    -1,    -1,  1689,  1692,
   1693,    -1,    -1,  1694,    -1,  1699,  1702,  1705,
     -1,  1706,  1707,  1709,    -1,    -1,  1712,  1715,
   1718,    -1,    -1,    -1,    -1,  1720,    -1,  1722,
     -1,  1723,  1724,  1726,    -1,    -1,    -1,  1727,
   1729,    -1,    -1,    -1,  1730,  1731,    -1,    -1,
   1732,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  1735,  1736,    -1,    -1,  1738,    -1,    -1,
   1742,    -1,  1743,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  1745,
   1749,  1750,    -1,  1751,  1752,    -1,    -1,  1755,
     -1,  1758,  1759,  1760,  1766,  1767,  1769,  1771,
   1773,    -1,    -1,  1775,  1776,    -1,  1777,  1778,
   1779,    -1,    -1,    -1,    -1,  1781,  1782,  1784,
   1786,    -1,    -1,  1787,    -1,  1788,    -1,    -1,
   1790,    -1,  1791,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  1792,    -1,  1793,  1794,  1795,  1796,
     -1,    -1,    -1,  1797,    -1,    -1,    -1,    -1,
     -1,    -1,  1798,  1800,  1801,  1802,  1803,    -1,
     -1,    -1,  1804,  1807,  1808,    -1,    -1,    -1,
     -1,  1809,  1810,    -1,    -1,    -1,  1811,  1812,
   1813,  1814,    -1,  1816,    -1,  1817,    -1,  1818,
     -1,  1819,  1820,    -1,  1821,    -1,    -1,    -1,
     -1,    -1,    -1,  1822,    -1,    -1,    -1,    -1,
   1824,    -1,    -1,    -1,    -1,    -1,  1825,    -1,
   1826,    -1,  1827,    -1,    -1,    -1,    -1,    -1,
     -1,  1830,  1835,  1836,    -1,    -1,    -1,    -1,
   1837,  1838,  1840,    -1,  1841,    -1,  1843,    -1,
     -1,  1845,    -1,    -1,    -1,    -1,  1846,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  1847,
     -1,    -1,    -1,    -1,  1848,  1851,    -1,    -1,
  /* 0x5800 */
     -1,    -1,  1852,  1853,    -1,  1854,  1855,  1856,
     -1,    -1,  1857,    -1,    -1,    -1,    -1,    -1,
     -1,  1858,    -1,    -1,    -1,  1859,  1861,    -1,
     -1,  1862,    -1,    -1,    -1,  1863,    -1,    -1,
     -1,    -1,    -1,    -1,  1864,    -1,  1866,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  1868,
   1871,  1873,    -1,    -1,  1874,    -1,    -1,    -1,
     -1,    -1,  1876,    -1,    -1,    -1,    -1,    -1,
     -1,  1878,    -1,    -1,    -1,    -1,  1880,    -1,
     -1,    -1,  1881,  1884,    -1,  1885,    -1,  1886,
   1887,  1888,  1889,    -1,  1890,    -1,    -1,  1891,
     -1,  1892,  1893,    -1,    -1,    -1,    -1,  1894,
     -1,  1895,  1896,    -1,  1897,    -1,  1899,    -1,
     -1,  1900,    -1,  1902,    -1,    -1,    -1,    -1,
     -1,    -1,  1903,    -1,    -1,  1905,    -1,  1906,
     -1,  1908,    -1,    -1,  1910,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  1913,  1914,    -1,  1915,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  1916,  1918,  1919,
     -1,  1920,    -1,    -1,  1922,    -1,  1924,    -1,
     -1,    -1,  1925,    -1,  1927,    -1,    -1,  1930,
     -1,    -1,    -1,    -1,    -1,    -1,  1931,  1933,
   1935,    -1,    -1,  1940,    -1,  1942,  1947,    -1,
   1948,    -1,    -1,  1949,    -1,    -1,  1950,    -1,
     -1,    -1,    -1,    -1,  1951,    -1,    -1,  1953,
     -1,    -1,  1954,  1956,  1957,  1958,  1959,    -1,
   1961,    -1,    -1,  1962,    -1,    -1,    -1,    -1,
   1964,  1966,  1967,    -1,  1968,    -1,  1973,  1975,
   1976,  1977,  1979,    -1,  1980,  1981,    -1,    -1,
     -1,  1984,  1986,    -1,    -1,    -1,  1987,  1989,
   1991,  1992,  1993,  1995,    -1,  1997,  1999,  2001,
   2003,  2004,  2005,  2007,  2009,  2010,    -1,    -1,
  /* 0x5900 */
   2012,    -1,  2014,    -1,  2015,  2017,    -1,  2018,
     -1,  2022,  2024,    -1,    -1,  2025,    -1,  2028,
     -1,    -1,    -1,  2029,  2030,    -1,    -1,    -1,
   2031,    -1,  2033,  2034,  2035,    -1,    -1,  2036,
   2037,    -1,  2038,  2040,    -1,  2042,    -1,    -1,
     -1,  2043,    -1,    -1,  2046,  2047,    -1,    -1,
     -1,    -1,  2048,  2050,  2051,    -1,  2052,    -1,
   2053,  2054,  2055,    -1,    -1,    -1,  2056,    -1,
     -1,  2057,  2060,    -1,    -1,    -1,    -1,  2061,
   2062,    -1,    -1,  2063,  2064,    -1,    -1,    -1,
   2065,    -1,    -1,    -1,  2066,    -1,  2068,    -1,
   2071,  2072,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2073,    -1,  2075,
   2077,  2080,  2083,    -1,  2084,    -1,  2087,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2088,    -1,
   2090,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2092,  2096,
   2098,    -1,  2099,    -1,    -1,  2100,    -1,    -1,
     -1,    -1,  2102,    -1,    -1,    -1,  2103,    -1,
     -1,  2104,    -1,  2105,    -1,  2106,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2110,  2111,  2112,  2114,    -1,    -1,    -1,
     -1,    -1,    -1,  2115,    -1,    -1,    -1,  2118,
   2120,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2122,    -1,
     -1,  2123,  2124,  2125,    -1,  2127,    -1,    -1,
   2128,    -1,  2129,    -1,    -1,    -1,    -1,  2130,
     -1,  2131,    -1,    -1,  2132,    -1,    -1,    -1,
     -1,    -1,  2133,    -1,    -1,    -1,  2134,  2136,
     -1,    -1,  2138,  2140,  2141,    -1,  2142,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2143,    -1,    -1,  2145,    -1,    -1,    -1,    -1,
  /* 0x5a00 */
     -1,    -1,    -1,    -1,  2146,  2147,  2148,  2149,
   2150,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  2151,    -1,    -1,    -1,    -1,    -1,
   2152,    -1,    -1,  2154,    -1,    -1,    -1,  2156,
     -1,    -1,    -1,    -1,  2157,    -1,  2161,    -1,
     -1,    -1,    -1,    -1,    -1,  2162,    -1,  2163,
   2165,  2166,  2168,    -1,  2169,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2171,
   2173,  2176,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2177,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  2178,    -1,    -1,  2179,    -1,
     -1,    -1,    -1,    -1,  2181,  2182,    -1,  2183,
     -1,    -1,    -1,  2185,  2186,  2189,  2190,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2191,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2193,  2195,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2196,
     -1,    -1,  2197,    -1,    -1,  2198,    -1,  2199,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2201,    -1,    -1,    -1,  2202,  2203,    -1,  2204,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  2206,    -1,    -1,    -1,    -1,
   2208,    -1,  2209,    -1,  2210,    -1,    -1,  2211,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2212,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2213,    -1,    -1,    -1,  2214,  2215,    -1,
     -1,    -1,  2217,  2219,    -1,    -1,    -1,  2221,
  /* 0x5b00 */
   2222,    -1,    -1,  2224,    -1,    -1,    -1,    -1,
   2225,  2226,    -1,  2227,  2228,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2229,    -1,    -1,    -1,  2230,    -1,    -1,
     -1,  2232,  2233,    -1,  2235,    -1,    -1,    -1,
     -1,    -1,  2236,    -1,    -1,  2238,    -1,    -1,
   2240,    -1,  2243,    -1,    -1,    -1,    -1,  2244,
   2245,    -1,    -1,    -1,    -1,    -1,  2246,    -1,
     -1,    -1,    -1,  2247,    -1,    -1,  2249,    -1,
     -1,    -1,    -1,    -1,  2252,    -1,    -1,    -1,
     -1,    -1,  2253,  2254,    -1,    -1,    -1,    -1,
     -1,  2255,    -1,    -1,    -1,    -1,    -1,    -1,
   2256,    -1,    -1,    -1,    -1,    -1,  2257,    -1,
     -1,    -1,  2262,  2263,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2264,    -1,    -1,    -1,  2269,  2270,  2271,  2274,
     -1,  2275,  2281,  2282,    -1,    -1,  2285,    -1,
   2286,    -1,  2287,    -1,    -1,  2288,    -1,    -1,
   2289,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  2291,  2293,  2297,  2299,
   2301,  2302,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2304,  2305,  2306,    -1,    -1,  2307,    -1,
     -1,    -1,    -1,    -1,  2308,    -1,  2310,  2311,
     -1,    -1,    -1,  2313,  2314,  2316,  2318,    -1,
   2320,    -1,    -1,  2321,    -1,    -1,  2322,  2323,
   2325,  2327,    -1,    -1,  2334,  2335,    -1,    -1,
     -1,    -1,    -1,  2341,    -1,  2342,    -1,  2348,
     -1,    -1,  2354,  2358,  2360,  2366,    -1,  2367,
   2368,    -1,  2370,    -1,    -1,    -1,  2371,  2373,
     -1,  2379,    -1,  2381,  2383,    -1,    -1,    -1,
     -1,    -1,    -1,  2385,    -1,  2389,  2390,    -1,
     -1,  2394,    -1,  2396,  2397,    -1,  2398,  2400,
  /* 0x5c00 */
     -1,    -1,  2402,    -1,    -1,  2404,  2407,  2408,
   2409,  2411,    -1,  2412,    -1,  2413,  2415,    -1,
     -1,    -1,  2416,  2419,  2422,    -1,    -1,    -1,
   2425,  2426,  2427,  2428,    -1,  2432,  2435,  2436,
   2441,    -1,  2446,  2449,  2450,  2452,  2453,  2454,
     -1,  2457,    -1,  2458,  2459,  2460,    -1,    -1,
     -1,    -1,  2463,    -1,  2468,    -1,  2473,  2478,
   2483,    -1,    -1,    -1,    -1,  2484,    -1,    -1,
   2487,    -1,  2489,  2490,    -1,  2491,  2492,    -1,
     -1,    -1,  2493,    -1,  2494,  2496,  2497,  2498,
     -1,    -1,    -1,  2500,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  2501,    -1,    -1,  2503,    -1,
     -1,  2504,  2505,    -1,  2506,    -1,  2507,    -1,
   2508,    -1,    -1,    -1,  2509,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2510,
   2511,  2512,  2516,    -1,    -1,  2517,    -1,    -1,
   2519,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2520,  2521,
   2522,    -1,  2523,  2524,    -1,    -1,    -1,    -1,
     -1,  2531,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2532,    -1,    -1,    -1,  2538,    -1,    -1,
     -1,    -1,    -1,  2539,  2540,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2541,    -1,  2542,
     -1,    -1,    -1,  2543,  2544,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  2545,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2546,    -1,    -1,
     -1,  2547,    -1,  2548,  2549,  2550,  2551,    -1,
   2552,  2553,    -1,    -1,    -1,    -1,    -1,  2554,
   2555,    -1,    -1,    -1,  2556,  2557,  2560,    -1,
     -1,    -1,    -1,    -1,    -1,  2567,    -1,    -1,
  /* 0x5d00 */
     -1,    -1,  2568,  2569,  2570,    -1,    -1,  2571,
   2572,    -1,    -1,  2573,    -1,  2575,  2576,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2579,  2580,
   2581,  2583,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  2585,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  2586,  2587,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  2589,    -1,    -1,    -1,
   2590,    -1,  2591,  2594,    -1,    -1,    -1,  2597,
   2601,    -1,  2604,    -1,  2605,  2608,    -1,    -1,
   2609,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2611,
     -1,    -1,    -1,  2612,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  2613,    -1,  2614,  2615,    -1,    -1,  2616,
     -1,    -1,    -1,  2618,  2625,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  2632,    -1,    -1,  2633,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2634,    -1,  2635,    -1,    -1,    -1,    -1,  2636,
   2637,    -1,    -1,    -1,    -1,    -1,  2638,    -1,
     -1,    -1,  2639,  2640,  2643,    -1,    -1,    -1,
   2645,  2648,  2655,    -1,  2656,  2657,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2658,  2660,    -1,
     -1,    -1,    -1,  2663,  2664,    -1,    -1,    -1,
     -1,    -1,  2667,  2668,  2669,    -1,  2670,  2673,
     -1,    -1,    -1,  2676,    -1,  2677,    -1,    -1,
   2678,    -1,  2679,  2680,    -1,    -1,    -1,    -1,
     -1,  2681,    -1,    -1,    -1,    -1,    -1,  2682,
   2683,    -1,    -1,    -1,    -1,  2684,    -1,  2685,
     -1,    -1,  2686,  2687,    -1,  2689,    -1,  2690,
  /* 0x5e00 */
   2692,  2694,    -1,  2697,    -1,  2698,    -1,    -1,
   2699,    -1,    -1,  2700,    -1,    -1,    -1,  2701,
   2702,  2703,  2704,    -1,    -1,  2705,    -1,    -1,
   2706,    -1,  2707,    -1,  2708,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2710,  2711,  2713,
     -1,    -1,    -1,  2714,    -1,  2715,  2716,  2719,
   2721,  2724,    -1,  2725,    -1,    -1,  2726,    -1,
     -1,    -1,    -1,  2728,  2729,  2730,    -1,    -1,
   2731,    -1,  2732,  2734,    -1,    -1,    -1,  2735,
   2738,    -1,    -1,    -1,  2740,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2741,
   2742,    -1,  2743,    -1,    -1,    -1,    -1,  2746,
     -1,  2748,    -1,  2749,  2751,    -1,    -1,    -1,
     -1,    -1,    -1,  2753,  2756,    -1,    -1,    -1,
     -1,    -1,  2757,    -1,  2760,    -1,  2761,  2767,
   2768,  2769,  2771,    -1,    -1,    -1,  2772,  2773,
     -1,  2776,    -1,  2778,  2780,  2782,  2786,    -1,
     -1,    -1,  2787,    -1,    -1,    -1,    -1,    -1,
   2788,  2789,    -1,  2790,  2791,    -1,    -1,    -1,
     -1,  2793,    -1,    -1,    -1,    -1,  2794,  2795,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  2797,
     -1,    -1,    -1,  2798,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  2799,  2803,    -1,
     -1,    -1,  2804,  2809,  2810,  2811,    -1,  2812,
     -1,  2813,  2815,  2816,    -1,    -1,    -1,    -1,
   2818,    -1,    -1,    -1,    -1,    -1,  2819,  2820,
   2822,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  2824,  2825,    -1,  2828,    -1,  2829,
   2831,  2833,  2834,  2836,    -1,    -1,    -1,    -1,
     -1,  2838,  2839,    -1,  2840,    -1,    -1,    -1,
   2841,    -1,    -1,  2844,    -1,    -1,    -1,    -1,
   2848,    -1,    -1,  2849,  2855,  2857,    -1,    -1,
  /* 0x5f00 */
   2863,  2864,  2871,  2872,  2873,    -1,    -1,    -1,
     -1,  2875,    -1,    -1,  2876,  2877,  2878,    -1,
   2880,  2883,  2884,    -1,  2885,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   2886,    -1,    -1,    -1,    -1,  2887,  2889,    -1,
     -1,    -1,  2890,    -1,    -1,    -1,    -1,  2891,
     -1,    -1,    -1,  2892,    -1,  2893,    -1,  2894,
     -1,  2895,  2897,  2898,  2899,    -1,  2900,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2902,    -1,
   2903,    -1,    -1,    -1,  2905,    -1,  2906,    -1,
   2907,  2908,  2909,  2912,  2914,  2915,    -1,  2918,
     -1,  2919,  2921,  2923,  2926,  2929,  2932,  2935,
   2936,    -1,    -1,    -1,    -1,  2938,  2939,    -1,
   2940,  2941,    -1,  2942,  2943,    -1,    -1,    -1,
     -1,    -1,  2945,    -1,  2946,    -1,    -1,  2947,
     -1,    -1,    -1,  2950,    -1,    -1,    -1,  2951,
   2953,  2955,    -1,  2958,  2959,    -1,    -1,  2962,
   2963,    -1,  2964,    -1,  2965,    -1,    -1,  2966,
     -1,  2967,    -1,  2970,    -1,  2972,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  2974,    -1,
   2976,  2978,    -1,  2979,  2981,    -1,    -1,    -1,
     -1,  2982,    -1,    -1,  2984,  2987,    -1,    -1,
   2988,  2989,    -1,  2990,  2993,  2995,    -1,  2997,
     -1,  3000,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3001,  3002,    -1,  3003,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3004,  3005,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3007,
     -1,  3008,    -1,    -1,  3009,    -1,    -1,    -1,
     -1,    -1,  3011,  3012,    -1,    -1,    -1,  3013,
     -1,  3014,    -1,    -1,    -1,    -1,    -1,    -1,
   3017,    -1,    -1,    -1,    -1,  3018,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3019,    -1,
  /* 0x6000 */
   3020,  3022,  3023,  3024,  3025,  3026,  3027,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  3028,    -1,  3029,    -1,
     -1,    -1,    -1,    -1,  3030,  3031,    -1,    -1,
     -1,    -1,    -1,  3032,    -1,  3034,    -1,    -1,
     -1,    -1,  3035,    -1,    -1,    -1,    -1,    -1,
     -1,  3036,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3039,  3044,    -1,    -1,  3045,
     -1,    -1,    -1,    -1,    -1,    -1,  3046,    -1,
     -1,    -1,    -1,  3047,    -1,    -1,    -1,    -1,
     -1,    -1,  3048,  3049,    -1,    -1,  3051,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3052,  3053,    -1,    -1,  3055,  3057,    -1,    -1,
     -1,    -1,  3058,    -1,    -1,    -1,    -1,    -1,
     -1,  3059,    -1,  3061,  3062,  3065,  3066,    -1,
   3070,  3071,  3072,  3073,  3074,  3076,  3077,  3078,
   3080,    -1,    -1,    -1,    -1,  3082,    -1,    -1,
     -1,    -1,  3084,  3089,    -1,  3090,    -1,    -1,
     -1,  3091,    -1,  3092,    -1,    -1,  3093,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3095,  3096,
     -1,    -1,  3097,    -1,  3098,    -1,  3101,    -1,
     -1,  3103,  3105,  3108,  3109,  3110,    -1,  3111,
     -1,    -1,    -1,  3112,  3115,  3116,  3117,    -1,
     -1,    -1,    -1,    -1,    -1,  3118,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3120,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3122,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3125,    -1,
   3127,  3128,    -1,  3131,    -1,  3132,    -1,  3134,
   3135,  3136,    -1,  3137,  3138,  3139,  3141,  3142,
     -1,  3143,  3145,    -1,    -1,    -1,    -1,  3146,
     -1,    -1,    -1,  3147,  3148,    -1,    -1,    -1,
  /* 0x6100 */
     -1,    -1,  3149,    -1,    -1,    -1,    -1,    -1,
   3151,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3153,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3155,    -1,  3156,  3157,    -1,    -1,    -1,
   3158,  3159,    -1,    -1,  3160,    -1,  3161,  3162,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3164,  3165,    -1,    -1,  3166,
     -1,  3167,    -1,    -1,  3169,  3170,  3171,  3172,
     -1,    -1,  3173,    -1,  3175,    -1,    -1,  3176,
     -1,    -1,    -1,  3177,    -1,  3178,  3179,    -1,
     -1,  3182,    -1,    -1,    -1,    -1,    -1,    -1,
   3184,  3185,  3187,    -1,    -1,    -1,    -1,  3189,
     -1,    -1,    -1,  3191,  3192,    -1,    -1,    -1,
     -1,    -1,  3193,  3194,    -1,  3195,  3196,    -1,
     -1,    -1,    -1,  3197,  3198,    -1,  3199,    -1,
     -1,    -1,    -1,    -1,  3200,  3202,  3204,  3205,
     -1,  3208,  3209,    -1,    -1,  3210,    -1,  3211,
     -1,    -1,  3212,    -1,    -1,    -1,    -1,    -1,
   3213,  3214,  3217,    -1,  3218,    -1,  3220,  3222,
   3223,  3226,  3229,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  3230,  3231,    -1,    -1,
     -1,  3234,    -1,  3235,    -1,    -1,  3236,    -1,
     -1,    -1,  3237,    -1,    -1,    -1,  3238,    -1,
     -1,    -1,    -1,  3239,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3240,    -1,    -1,    -1,  3241,
     -1,  3242,    -1,    -1,  3244,  3245,    -1,    -1,
   3246,  3248,  3249,    -1,  3251,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  3252,    -1,  3253,  3254,
     -1,    -1,    -1,  3255,    -1,    -1,  3256,  3257,
   3258,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3259,    -1,  3260,  3262,  3263,  3264,
   3266,    -1,  3267,    -1,  3268,  3270,  3271,  3272,
  /* 0x6200 */
   3273,    -1,    -1,    -1,    -1,    -1,  3274,  3275,
     -1,    -1,    -1,  3276,    -1,    -1,    -1,  3277,
     -1,    -1,    -1,    -1,  3279,    -1,    -1,  3280,
   3281,    -1,  3283,  3285,    -1,  3286,  3288,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3289,  3291,
     -1,  3292,    -1,    -1,  3293,    -1,  3294,  3296,
   3298,    -1,  3300,    -1,    -1,    -1,  3302,  3304,
   3306,  3308,    -1,  3309,  3310,    -1,  3312,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3313,  3314,  3315,  3316,    -1,
     -1,  3318,    -1,    -1,    -1,  3320,    -1,  3321,
   3322,    -1,    -1,    -1,    -1,    -1,  3324,    -1,
   3325,    -1,    -1,  3326,    -1,    -1,    -1,  3327,
   3328,  3330,  3332,  3333,  3334,    -1,    -1,  3335,
   3336,    -1,    -1,    -1,    -1,  3337,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3338,    -1,    -1,    -1,    -1,    -1,
     -1,  3340,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3341,  3342,  3343,  3344,  3345,  3347,
   3348,  3349,  3350,    -1,  3351,  3352,    -1,    -1,
     -1,    -1,    -1,    -1,  3353,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  3354,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3357,    -1,    -1,  3358,    -1,    -1,
   3359,    -1,    -1,  3360,    -1,    -1,    -1,  3361,
   3362,    -1,    -1,  3364,  3365,  3366,  3367,  3368,
     -1,    -1,    -1,    -1,  3369,  3370,  3371,  3372,
   3373,  3375,  3378,  3379,    -1,  3380,  3381,  3382,
   3383,  3385,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3387,
  /* 0x6300 */
     -1,    -1,  3388,    -1,  3389,    -1,    -1,    -1,
     -1,    -1,  3391,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3393,  3396,  3397,  3398,  3399,  3400,  3401,
   3402,  3403,  3404,  3405,  3406,  3407,  3408,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  3409,    -1,  3411,
     -1,    -1,    -1,    -1,    -1,  3412,  3413,  3414,
     -1,  3415,    -1,    -1,    -1,    -1,  3418,    -1,
     -1,    -1,    -1,    -1,    -1,  3421,    -1,  3422,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3423,
     -1,    -1,    -1,    -1,  3424,    -1,  3425,  3426,
     -1,  3427,  3428,  3429,    -1,    -1,    -1,    -1,
   3431,    -1,    -1,  3432,    -1,    -1,  3433,    -1,
     -1,    -1,  3435,    -1,  3437,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3438,
     -1,    -1,    -1,  3440,  3442,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3443,
     -1,  3444,    -1,  3445,    -1,    -1,    -1,    -1,
     -1,  3446,    -1,    -1,    -1,  3447,    -1,    -1,
     -1,    -1,    -1,    -1,  3448,    -1,    -1,    -1,
     -1,    -1,  3449,  3450,  3451,    -1,    -1,  3452,
   3453,    -1,  3454,  3455,  3456,  3457,    -1,    -1,
   3458,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3459,  3460,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3461,  3462,    -1,    -1,    -1,    -1,
   3463,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  3464,  3465,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3466,  3467,    -1,    -1,  3469,    -1,  3470,
  /* 0x6400 */
   3471,  3472,  3473,  3474,    -1,  3475,  3478,    -1,
     -1,    -1,    -1,    -1,    -1,  3479,    -1,    -1,
     -1,    -1,  3480,    -1,  3481,    -1,  3482,  3484,
     -1,    -1,    -1,    -1,  3485,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3487,    -1,    -1,    -1,    -1,  3488,    -1,    -1,
     -1,    -1,  3489,    -1,    -1,    -1,  3490,    -1,
     -1,    -1,  3491,    -1,    -1,    -1,  3493,    -1,
     -1,    -1,  3494,    -1,  3496,  3498,  3499,  3501,
   3503,    -1,  3504,    -1,    -1,    -1,    -1,    -1,
     -1,  3505,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  3506,    -1,    -1,  3507,
   3508,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3511,    -1,    -1,    -1,    -1,    -1,  3512,
     -1,    -1,    -1,  3513,    -1,    -1,  3514,    -1,
     -1,    -1,    -1,  3515,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  3516,  3518,    -1,    -1,    -1,
   3519,    -1,    -1,    -1,    -1,    -1,    -1,  3520,
   3521,  3522,    -1,  3523,    -1,    -1,    -1,    -1,
   3524,    -1,  3525,    -1,    -1,    -1,    -1,  3526,
     -1,  3527,    -1,  3528,    -1,  3529,  3531,  3532,
     -1,    -1,    -1,  3535,    -1,    -1,    -1,    -1,
     -1,    -1,  3536,  3538,    -1,  3539,    -1,  3540,
   3541,  3542,  3545,  3546,    -1,    -1,  3547,  3548,
     -1,  3549,    -1,    -1,  3550,    -1,    -1,  3551,
   3553,    -1,  3555,  3557,    -1,  3558,    -1,    -1,
     -1,  3559,    -1,    -1,  3560,  3561,    -1,    -1,
     -1,    -1,  3563,    -1,    -1,    -1,  3565,    -1,
   3566,  3567,    -1,  3568,    -1,  3569,    -1,  3570,
     -1,    -1,    -1,    -1,  3573,    -1,    -1,  3574,
   3575,  3576,  3577,    -1,  3578,  3581,    -1,  3582,
     -1,    -1,  3584,  3585,  3586,  3587,  3588,    -1,
  /* 0x6500 */
   3589,    -1,    -1,    -1,  3590,  3591,  3592,    -1,
   3593,    -1,  3594,    -1,    -1,    -1,    -1,  3595,
     -1,    -1,  3596,    -1,  3597,    -1,  3598,    -1,
     -1,  3599,    -1,  3600,  3601,  3603,    -1,  3605,
     -1,    -1,  3606,  3608,  3609,    -1,    -1,    -1,
   3610,    -1,  3611,  3614,  3615,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  3617,  3618,  3619,  3620,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3621,
   3625,  3628,    -1,    -1,  3629,  3630,  3632,  3633,
     -1,    -1,    -1,    -1,    -1,  3634,    -1,  3636,
   3638,  3640,    -1,  3641,    -1,    -1,  3643,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3644,    -1,    -1,    -1,    -1,    -1,    -1,
   3649,    -1,    -1,    -1,    -1,  3650,    -1,  3651,
   3652,    -1,  3653,    -1,    -1,    -1,    -1,    -1,
     -1,  3657,  3658,  3660,    -1,  3661,  3666,    -1,
   3671,  3676,  3680,  3684,  3686,  3688,  3689,    -1,
     -1,    -1,    -1,  3691,  3692,  3694,    -1,  3695,
     -1,    -1,    -1,    -1,    -1,    -1,  3700,  3702,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3703,    -1,    -1,  3704,  3705,    -1,    -1,
     -1,    -1,  3706,    -1,    -1,  3707,    -1,  3708,
     -1,    -1,    -1,    -1,  3709,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3712,  3713,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3714,    -1,    -1,    -1,    -1,  3715,
   3717,  3719,    -1,  3721,    -1,    -1,    -1,    -1,
   3722,    -1,    -1,    -1,    -1,  3723,    -1,  3724,
     -1,    -1,  3725,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3728,  3730,
   3733,  3734,  3736,    -1,    -1,    -1,  3737,  3738,
  /* 0x6600 */
     -1,    -1,  3739,    -1,    -1,    -1,    -1,  3740,
     -1,    -1,  3742,    -1,    -1,    -1,  3743,  3744,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3745,  3746,    -1,    -1,    -1,    -1,  3747,
     -1,    -1,    -1,    -1,    -1,  3748,  3749,    -1,
     -1,    -1,    -1,    -1,  3750,  3751,    -1,  3752,
   3753,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3754,    -1,    -1,  3755,  3756,  3758,  3759,  3761,
     -1,    -1,  3763,  3765,  3766,    -1,    -1,    -1,
     -1,  3767,    -1,  3768,    -1,    -1,    -1,    -1,
     -1,    -1,  3769,  3770,  3772,  3773,  3774,    -1,
     -1,    -1,  3775,    -1,    -1,  3776,    -1,  3778,
   3779,    -1,    -1,    -1,  3780,    -1,    -1,    -1,
   3781,  3782,    -1,    -1,    -1,    -1,  3783,    -1,
     -1,    -1,    -1,    -1,  3784,    -1,    -1,    -1,
     -1,    -1,    -1,  3786,    -1,    -1,    -1,    -1,
   3787,  3788,  3790,  3791,    -1,    -1,    -1,    -1,
   3793,  3794,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  3795,    -1,    -1,    -1,  3796,  3798,
   3803,    -1,    -1,    -1,  3804,    -1,    -1,    -1,
     -1,    -1,  3805,    -1,  3806,    -1,  3809,  3812,
   3813,    -1,    -1,  3814,    -1,  3815,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3817,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3819,    -1,    -1,  3820,    -1,  3821,  3825,
     -1,  3826,    -1,    -1,  3828,  3832,    -1,  3833,
     -1,    -1,    -1,    -1,    -1,    -1,  3834,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3835,
   3836,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3838,    -1,    -1,    -1,  3839,    -1,    -1,    -1,
     -1,    -1,  3840,  3845,    -1,  3846,    -1,    -1,
   3847,  3850,  3851,  3852,    -1,  3854,  3855,    -1,
  /* 0x6700 */
   3856,    -1,  3857,  3859,    -1,    -1,    -1,  3860,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3861,  3862,
     -1,  3863,    -1,  3864,    -1,    -1,  3865,  3866,
     -1,    -1,  3867,    -1,    -1,    -1,    -1,  3868,
     -1,    -1,    -1,    -1,  3869,  3871,  3872,  3873,
     -1,  3875,    -1,    -1,  3876,  3878,  3879,    -1,
     -1,    -1,  3880,    -1,    -1,    -1,    -1,    -1,
   3882,    -1,  3883,  3886,    -1,    -1,  3888,  3889,
     -1,  3890,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  3893,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   3894,  3895,    -1,    -1,  3896,  3897,  3898,    -1,
   3899,  3900,    -1,    -1,    -1,    -1,    -1,  3901,
   3903,  3904,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  3905,  3911,
   3912,  3917,    -1,    -1,  3918,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3920,
     -1,    -1,  3921,    -1,    -1,    -1,    -1,    -1,
     -1,  3925,    -1,    -1,  3928,    -1,  3929,    -1,
     -1,  3930,  3931,  3932,    -1,  3933,  3934,  3935,
   3936,  3937,  3942,  3945,    -1,  3946,    -1,    -1,
     -1,  3947,    -1,    -1,  3950,    -1,    -1,    -1,
     -1,    -1,    -1,  3952,    -1,    -1,    -1,    -1,
     -1,  3953,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  3954,
     -1,    -1,    -1,    -1,    -1,  3955,    -1,  3956,
     -1,    -1,    -1,    -1,  3961,    -1,    -1,    -1,
   3962,    -1,    -1,    -1,    -1,  3963,    -1,    -1,
     -1,  3964,    -1,    -1,    -1,    -1,    -1,    -1,
   3966,    -1,    -1,  3967,    -1,  3970,    -1,    -1,
     -1,  3971,  3972,  3974,    -1,  3975,  3976,  3978,
  /* 0x6800 */
   3979,  3980,    -1,  3983,  3985,  3987,    -1,  3988,
   3989,  3991,  3992,  3994,  3995,    -1,  3997,  3999,
     -1,  4001,    -1,    -1,    -1,    -1,  4002,  4004,
     -1,    -1,    -1,    -1,    -1,    -1,  4006,    -1,
     -1,    -1,  4007,    -1,    -1,    -1,    -1,  4008,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   4009,    -1,    -1,    -1,    -1,    -1,    -1,  4011,
   4013,    -1,    -1,    -1,    -1,    -1,  4014,    -1,
     -1,    -1,  4016,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4017,    -1,    -1,    -1,
     -1,  4019,  4020,    -1,    -1,    -1,    -1,    -1,
   4021,    -1,    -1,    -1,  4022,  4024,    -1,  4025,
   4027,  4030,  4031,  4032,  4033,  4034,  4036,  4037,
   4038,  4039,  4040,    -1,    -1,    -1,  4041,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4043,  4044,    -1,  4047,    -1,    -1,  4048,
     -1,  4049,    -1,    -1,  4051,  4052,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  4054,    -1,    -1,
     -1,    -1,    -1,    -1,  4056,    -1,    -1,    -1,
   4057,    -1,    -1,    -1,    -1,  4058,    -1,  4059,
     -1,    -1,    -1,    -1,    -1,  4060,  4065,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4067,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4069,    -1,  4070,  4071,
   4072,    -1,  4074,    -1,  4075,    -1,    -1,    -1,
     -1,    -1,  4076,  4080,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  4084,  4085,  4086,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4087,
     -1,    -1,    -1,    -1,    -1,  4088,    -1,  4089,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4091,  4092,    -1,    -1,    -1,  4094,    -1,
     -1,  4095,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x6900 */
   4096,  4097,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4098,    -1,    -1,    -1,    -1,  4100,  4101,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4104,
   4106,  4107,    -1,    -1,  4108,    -1,    -1,  4110,
   4111,    -1,  4112,    -1,  4113,    -1,    -1,    -1,
     -1,    -1,    -1,  4114,    -1,  4115,    -1,    -1,
   4116,    -1,    -1,    -1,  4117,    -1,  4118,    -1,
     -1,  4119,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4120,    -1,    -1,  4121,    -1,    -1,
     -1,    -1,    -1,  4123,    -1,  4124,    -1,    -1,
     -1,  4125,  4126,    -1,    -1,    -1,    -1,    -1,
   4127,  4128,    -1,    -1,    -1,  4129,  4130,    -1,
   4131,    -1,  4132,  4133,    -1,  4134,    -1,    -1,
     -1,    -1,    -1,  4135,    -1,  4137,    -1,    -1,
     -1,    -1,    -1,    -1,  4138,  4139,    -1,  4141,
     -1,    -1,    -1,    -1,  4142,  4143,  4144,  4145,
   4146,  4147,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4149,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4150,    -1,
   4152,    -1,  4153,    -1,    -1,    -1,  4154,    -1,
     -1,    -1,  4156,    -1,  4157,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4158,    -1,    -1,  4159,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4160,
   4161,    -1,    -1,  4163,    -1,  4164,    -1,    -1,
     -1,  4165,    -1,  4167,  4168,  4170,    -1,  4172,
   4174,  4176,  4177,  4178,    -1,    -1,  4180,  4181,
   4182,    -1,    -1,    -1,  4183,    -1,    -1,  4184,
   4185,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4186,    -1,  4187,    -1,    -1,  4188,    -1,
     -1,  4189,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x6a00 */
     -1,  4190,  4191,  4193,    -1,  4194,    -1,    -1,
     -1,    -1,  4195,    -1,    -1,    -1,    -1,    -1,
   4196,  4197,  4199,  4200,    -1,    -1,    -1,    -1,
     -1,  4201,    -1,    -1,    -1,    -1,  4202,    -1,
     -1,    -1,    -1,  4203,    -1,    -1,    -1,    -1,
     -1,  4205,  4207,    -1,    -1,    -1,    -1,  4209,
     -1,  4211,    -1,  4213,    -1,    -1,    -1,  4214,
   4218,  4220,  4221,    -1,    -1,  4222,    -1,  4223,
     -1,    -1,    -1,  4224,    -1,    -1,    -1,    -1,
   4226,    -1,  4227,  4228,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4230,
     -1,    -1,  4231,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  4233,    -1,    -1,  4235,    -1,
   4238,  4239,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4240,    -1,    -1,  4243,    -1,    -1,    -1,
     -1,  4244,    -1,  4245,    -1,    -1,  4246,    -1,
     -1,  4247,    -1,    -1,    -1,    -1,    -1,  4248,
   4250,    -1,    -1,    -1,  4251,    -1,    -1,  4252,
   4254,    -1,    -1,    -1,  4255,  4256,    -1,  4257,
     -1,    -1,  4258,  4260,    -1,    -1,    -1,    -1,
     -1,  4261,  4262,    -1,    -1,    -1,  4264,  4265,
     -1,  4267,    -1,  4271,    -1,    -1,    -1,    -1,
   4272,    -1,    -1,  4273,    -1,    -1,    -1,    -1,
   4274,  4278,  4279,  4280,    -1,    -1,    -1,    -1,
   4281,    -1,    -1,    -1,  4282,    -1,  4283,    -1,
     -1,    -1,    -1,  4285,  4287,  4288,    -1,    -1,
     -1,    -1,  4289,  4290,    -1,  4291,  4292,  4293,
     -1,    -1,    -1,    -1,    -1,  4295,    -1,  4296,
   4297,    -1,  4298,    -1,  4299,    -1,    -1,    -1,
     -1,    -1,    -1,  4300,    -1,    -1,    -1,    -1,
   4301,    -1,  4302,  4303,    -1,  4305,    -1,    -1,
  /* 0x6b00 */
     -1,    -1,    -1,    -1,  4306,  4308,    -1,    -1,
     -1,  4309,  4313,    -1,    -1,    -1,    -1,  4315,
     -1,  4316,  4317,    -1,    -1,    -1,  4319,  4320,
     -1,    -1,    -1,    -1,    -1,  4322,  4325,    -1,
   4327,    -1,  4329,  4333,  4336,    -1,    -1,  4337,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4338,    -1,    -1,  4339,    -1,    -1,
     -1,    -1,    -1,  4340,    -1,  4341,  4342,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4343,    -1,  4344,    -1,
   4346,    -1,    -1,  4347,    -1,    -1,  4349,    -1,
   4352,    -1,    -1,  4353,    -1,  4355,    -1,  4356,
     -1,  4357,    -1,    -1,    -1,  4359,    -1,    -1,
   4361,  4363,    -1,    -1,  4365,    -1,  4366,  4369,
     -1,    -1,  4371,  4375,  4379,    -1,    -1,  4382,
   4385,  4388,  4389,    -1,  4390,    -1,  4392,  4396,
   4400,  4401,    -1,    -1,    -1,    -1,    -1,  4405,
     -1,    -1,    -1,  4406,    -1,    -1,    -1,    -1,
     -1,    -1,  4407,  4408,    -1,    -1,    -1,    -1,
   4409,    -1,  4410,    -1,    -1,    -1,  4411,    -1,
     -1,  4412,    -1,    -1,  4413,    -1,    -1,    -1,
     -1,    -1,    -1,  4414,    -1,    -1,  4415,  4416,
     -1,  4417,  4419,    -1,  4421,    -1,    -1,  4422,
     -1,    -1,  4423,  4424,  4426,    -1,    -1,    -1,
   4428,  4429,  4432,    -1,    -1,    -1,  4433,    -1,
     -1,  4434,    -1,    -1,    -1,    -1,  4436,  4437,
     -1,    -1,    -1,  4438,  4440,  4441,    -1,  4442,
   4443,  4444,    -1,    -1,    -1,    -1,    -1,  4445,
     -1,  4446,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4447,    -1,  4448,    -1,
     -1,    -1,    -1,    -1,    -1,  4449,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4450,
  /* 0x6c00 */
     -1,    -1,  4451,    -1,    -1,    -1,    -1,  4453,
   4454,    -1,  4456,    -1,  4457,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4458,    -1,    -1,  4461,
     -1,    -1,    -1,  4464,    -1,    -1,    -1,    -1,
     -1,    -1,  4465,  4466,    -1,    -1,    -1,    -1,
     -1,  4469,    -1,  4470,  4471,    -1,    -1,    -1,
     -1,    -1,  4472,  4473,  4474,  4476,    -1,  4478,
     -1,    -1,  4481,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4483,
     -1,  4487,    -1,    -1,    -1,    -1,  4488,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4489,  4491,    -1,    -1,    -1,    -1,    -1,
     -1,  4493,    -1,    -1,  4495,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  4496,  4497,    -1,    -1,    -1,
     -1,  4498,  4499,  4500,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   4501,  4503,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4504,    -1,    -1,    -1,  4505,  4506,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4507,
     -1,  4508,  4513,  4515,  4516,  4517,  4518,  4519,
   4520,  4521,  4522,    -1,    -1,    -1,  4524,    -1,
     -1,  4525,  4526,    -1,    -1,  4527,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4528,    -1,
     -1,  4529,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  4530,    -1,    -1,
     -1,    -1,    -1,  4532,    -1,  4533,  4534,    -1,
     -1,    -1,    -1,    -1,    -1,  4535,    -1,    -1,
   4536,    -1,  4537,    -1,    -1,    -1,    -1,  4539,
   4540,    -1,    -1,    -1,    -1,    -1,  4541,  4542,
   4544,    -1,  4545,  4546,  4547,  4550,  4552,    -1,
  /* 0x6d00 */
     -1,  4553,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4554,    -1,    -1,    -1,
     -1,    -1,  4555,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4556,
     -1,    -1,    -1,    -1,    -1,    -1,  4557,    -1,
     -1,    -1,    -1,    -1,  4558,    -1,    -1,    -1,
   4559,    -1,    -1,  4560,  4561,  4562,  4563,  4564,
   4565,  4566,  4567,  4568,    -1,  4569,  4570,  4572,
   4574,  4575,  4576,  4577,  4578,  4579,    -1,    -1,
     -1,    -1,    -1,    -1,  4580,    -1,    -1,    -1,
     -1,    -1,    -1,  4583,    -1,    -1,    -1,  4584,
     -1,  4587,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4588,    -1,    -1,  4589,    -1,    -1,    -1,
     -1,    -1,  4590,    -1,    -1,  4592,    -1,  4593,
     -1,  4594,    -1,    -1,  4595,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4596,    -1,
     -1,  4598,    -1,  4600,  4601,  4603,  4604,  4605,
   4606,  4607,  4608,  4609,  4610,    -1,  4611,  4612,
   4614,  4615,    -1,    -1,    -1,    -1,    -1,  4619,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   4620,    -1,    -1,    -1,  4621,    -1,    -1,    -1,
   4622,    -1,    -1,    -1,  4623,    -1,    -1,    -1,
     -1,    -1,    -1,  4624,    -1,    -1,    -1,    -1,
     -1,    -1,  4625,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4626,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  4628,    -1,    -1,
   4629,    -1,  4631,  4632,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  4634,    -1,  4635,  4638,    -1,
   4639,    -1,  4640,  4641,    -1,    -1,    -1,    -1,
  /* 0x6e00 */
     -1,    -1,    -1,    -1,    -1,  4642,    -1,  4643,
   4644,  4646,  4647,  4650,  4654,  4655,  4656,    -1,
   4658,  4659,    -1,  4660,  4664,  4665,  4668,  4669,
     -1,  4670,    -1,  4671,    -1,    -1,    -1,    -1,
     -1,    -1,  4672,    -1,    -1,    -1,  4673,    -1,
     -1,  4674,    -1,    -1,  4675,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4676,    -1,    -1,    -1,
   4677,    -1,    -1,    -1,    -1,    -1,  4679,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4680,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4681,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4682,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4683,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  4684,  4685,  4686,  4687,  4688,
   4690,  4692,  4693,  4694,    -1,  4695,    -1,  4696,
     -1,  4697,    -1,    -1,  4698,    -1,    -1,    -1,
   4701,    -1,    -1,    -1,    -1,  4702,  4704,    -1,
     -1,    -1,    -1,    -1,  4706,  4707,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4708,  4712,    -1,    -1,  4713,  4714,
     -1,    -1,    -1,  4716,    -1,    -1,  4717,    -1,
     -1,    -1,    -1,    -1,  4719,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4721,  4722,    -1,    -1,
     -1,    -1,    -1,    -1,  4723,    -1,  4724,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4725,
     -1,  4726,  4728,  4729,    -1,  4730,  4732,  4733,
   4736,  4737,  4739,    -1,  4740,  4741,  4742,    -1,
   4743,  4746,  4747,    -1,  4748,    -1,    -1,  4750,
     -1,    -1,  4751,    -1,    -1,    -1,    -1,  4752,
   4755,    -1,    -1,  4757,    -1,    -1,  4758,  4759,
  /* 0x6f00 */
     -1,  4761,    -1,    -1,  4762,    -1,  4763,    -1,
     -1,    -1,  4764,    -1,    -1,    -1,    -1,    -1,
     -1,  4765,    -1,  4766,    -1,    -1,    -1,    -1,
     -1,    -1,  4767,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  4768,  4769,    -1,    -1,    -1,  4770,
     -1,    -1,    -1,  4773,  4774,    -1,    -1,    -1,
     -1,  4775,  4776,    -1,    -1,    -1,    -1,    -1,
   4777,    -1,    -1,    -1,    -1,    -1,    -1,  4778,
     -1,  4779,    -1,    -1,  4780,  4781,  4782,  4783,
     -1,    -1,    -1,  4784,    -1,  4785,    -1,    -1,
     -1,  4786,    -1,    -1,  4789,    -1,    -1,    -1,
     -1,  4791,    -1,  4792,  4794,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  4796,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4797,
   4798,    -1,    -1,    -1,  4799,    -1,    -1,  4800,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4801,
   4802,  4806,  4810,    -1,  4813,    -1,  4816,  4817,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4818,    -1,    -1,  4819,    -1,    -1,  4820,
     -1,    -1,  4821,  4822,  4825,    -1,    -1,  4826,
   4828,    -1,    -1,  4829,  4830,    -1,  4832,    -1,
     -1,  4833,    -1,    -1,    -1,    -1,  4834,    -1,
   4835,  4836,    -1,  4837,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4838,    -1,  4839,    -1,    -1,    -1,  4840,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4841,  4843,    -1,  4844,  4845,    -1,    -1,
   4847,    -1,  4848,  4850,  4852,    -1,    -1,  4853,
     -1,    -1,    -1,    -1,  4855,    -1,    -1,    -1,
     -1,    -1,    -1,  4856,    -1,    -1,    -1,    -1,
   4857,  4858,    -1,  4861,    -1,  4863,  4866,    -1,
     -1,    -1,  4868,    -1,  4869,    -1,  4870,    -1,
  /* 0x7000 */
     -1,    -1,  4871,    -1,    -1,  4874,  4875,    -1,
     -1,  4877,    -1,  4878,    -1,    -1,    -1,  4880,
     -1,    -1,    -1,  4881,    -1,  4884,    -1,    -1,
   4885,    -1,    -1,    -1,    -1,  4886,    -1,  4887,
   4888,    -1,    -1,    -1,    -1,    -1,  4889,  4890,
   4892,    -1,    -1,    -1,  4894,    -1,    -1,  4896,
   4897,    -1,  4898,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4900,    -1,
     -1,    -1,    -1,  4901,  4902,    -1,    -1,    -1,
     -1,    -1,    -1,  4903,  4905,    -1,  4906,  4908,
     -1,  4909,    -1,    -1,  4910,  4912,    -1,    -1,
   4913,    -1,    -1,    -1,    -1,  4914,    -1,    -1,
     -1,    -1,    -1,  4915,  4916,    -1,    -1,  4917,
     -1,    -1,    -1,    -1,    -1,  4918,  4919,  4921,
     -1,    -1,    -1,    -1,    -1,  4922,  4925,    -1,
     -1,    -1,    -1,  4927,    -1,  4929,  4931,  4933,
   4934,  4935,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4938,    -1,    -1,    -1,  4941,  4943,  4944,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4945,
     -1,    -1,    -1,    -1,  4947,  4948,    -1,    -1,
     -1,    -1,    -1,    -1,  4949,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  4954,  4957,
   4958,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  4959,  4960,    -1,  4962,  4965,    -1,    -1,
     -1,  4966,  4967,  4970,    -1,    -1,    -1,    -1,
     -1,    -1,  4971,    -1,    -1,    -1,    -1,  4972,
     -1,    -1,    -1,    -1,    -1,    -1,  4973,    -1,
     -1,    -1,    -1,  4975,    -1,    -1,    -1,  4976,
     -1,    -1,    -1,    -1,    -1,    -1,  4977,  4978,
   4980,  4982,    -1,  4983,  4984,  4985,    -1,    -1,
     -1,  4986,    -1,    -1,  4987,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  4988,  4989,    -1,
  /* 0x7100 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  4991,
   4992,    -1,  4996,    -1,    -1,    -1,    -1,  4997,
     -1,    -1,    -1,    -1,  4998,  5000,  5001,    -1,
   5002,    -1,  5003,    -1,    -1,    -1,    -1,    -1,
     -1,  5005,    -1,    -1,    -1,    -1,  5006,    -1,
     -1,    -1,    -1,    -1,    -1,  5007,    -1,    -1,
   5008,    -1,    -1,  5010,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5011,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5013,    -1,    -1,
     -1,  5014,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5017,  5018,    -1,    -1,  5019,  5023,  5024,
     -1,  5025,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5026,    -1,    -1,  5028,    -1,  5029,
     -1,  5033,    -1,    -1,  5034,    -1,  5036,    -1,
     -1,    -1,    -1,    -1,  5037,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  5039,    -1,
     -1,    -1,    -1,    -1,    -1,  5041,    -1,    -1,
   5043,    -1,    -1,    -1,    -1,    -1,    -1,  5047,
     -1,    -1,  5048,    -1,  5049,    -1,    -1,  5051,
     -1,  5052,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5056,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5057,  5058,  5059,    -1,    -1,    -1,    -1,
     -1,  5060,  5061,    -1,    -1,    -1,  5062,    -1,
     -1,  5063,    -1,    -1,  5065,    -1,    -1,    -1,
   5067,    -1,    -1,    -1,  5068,    -1,  5070,    -1,
   5071,    -1,  5073,  5075,    -1,  5077,    -1,  5078,
     -1,  5081,    -1,    -1,  5082,    -1,    -1,  5083,
     -1,    -1,    -1,    -1,    -1,    -1,  5085,    -1,
     -1,    -1,    -1,    -1,  5086,  5087,    -1,    -1,
     -1,    -1,    -1,  5088,  5092,    -1,    -1,    -1,
     -1,    -1,    -1,  5093,  5094,    -1,  5095,  5096,
  /* 0x7200 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5097,    -1,    -1,
   5098,    -1,    -1,    -1,    -1,    -1,    -1,  5099,
     -1,    -1,    -1,  5101,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5104,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5107,    -1,    -1,
     -1,  5108,  5109,    -1,  5111,    -1,    -1,  5112,
     -1,    -1,  5113,    -1,  5114,    -1,  5115,  5118,
   5119,    -1,    -1,  5120,    -1,  5121,  5122,    -1,
     -1,    -1,    -1,  5123,    -1,  5124,  5125,    -1,
     -1,    -1,    -1,  5129,    -1,  5130,    -1,    -1,
   5134,    -1,    -1,  5135,  5136,    -1,    -1,    -1,
   5137,    -1,    -1,    -1,    -1,    -1,  5138,    -1,
     -1,    -1,    -1,  5140,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5142,  5143,    -1,    -1,
     -1,    -1,  5144,    -1,    -1,  5146,    -1,    -1,
     -1,  5147,  5148,    -1,    -1,    -1,    -1,  5149,
     -1,    -1,  5150,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  5151,    -1,
     -1,    -1,    -1,  5152,    -1,    -1,    -1,    -1,
   5154,    -1,  5156,    -1,    -1,    -1,    -1,  5157,
     -1,    -1,    -1,    -1,  5159,  5160,  5161,    -1,
     -1,    -1,  5162,    -1,  5163,    -1,  5165,  5166,
   5167,  5168,    -1,    -1,    -1,    -1,    -1,    -1,
   5169,    -1,  5170,    -1,    -1,    -1,    -1,    -1,
   5172,  5173,    -1,    -1,  5174,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5175,  5176,    -1,
     -1,    -1,  5177,    -1,    -1,  5178,    -1,    -1,
     -1,    -1,    -1,    -1,  5179,  5180,  5181,  5182,
   5183,  5184,  5185,    -1,    -1,    -1,    -1,    -1,
   5186,  5188,    -1,    -1,    -1,  5189,  5190,    -1,
  /* 0x7300 */
     -1,    -1,  5191,  5192,    -1,    -1,    -1,  5193,
     -1,    -1,  5194,    -1,    -1,  5195,  5197,    -1,
     -1,    -1,    -1,    -1,    -1,  5199,    -1,    -1,
   5200,  5201,    -1,    -1,    -1,    -1,    -1,  5202,
     -1,  5204,    -1,    -1,    -1,    -1,    -1,    -1,
   5205,  5206,  5207,  5208,  5209,    -1,  5210,  5211,
     -1,    -1,  5212,    -1,    -1,  5213,  5214,    -1,
     -1,    -1,    -1,  5215,    -1,    -1,    -1,  5216,
     -1,  5218,    -1,    -1,  5219,  5220,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5221,  5222,  5225,
   5226,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5227,  5228,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  5229,    -1,    -1,  5231,    -1,
   5232,    -1,  5233,  5234,    -1,  5236,  5237,    -1,
   5238,  5239,  5240,    -1,    -1,  5241,    -1,  5243,
   5244,    -1,  5246,  5247,  5248,    -1,  5249,    -1,
   5250,  5251,    -1,  5252,    -1,  5254,    -1,  5255,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5256,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5257,  5258,  5259,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5260,  5262,    -1,    -1,    -1,    -1,  5263,  5264,
   5265,  5266,    -1,  5267,    -1,    -1,    -1,    -1,
     -1,    -1,  5268,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  5269,    -1,  5272,  5273,  5274,
   5276,  5277,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5278,    -1,    -1,  5280,    -1,    -1,    -1,
     -1,    -1,  5284,    -1,    -1,    -1,    -1,    -1,
   5285,  5286,  5288,    -1,    -1,    -1,    -1,  5289,
     -1,    -1,    -1,    -1,    -1,    -1,  5290,    -1,
  /* 0x7400 */
     -1,    -1,    -1,  5291,    -1,  5292,    -1,    -1,
     -1,  5293,    -1,    -1,    -1,  5296,  5298,  5299,
   5300,  5302,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5304,    -1,    -1,  5305,    -1,    -1,    -1,
     -1,  5307,  5309,    -1,  5310,    -1,    -1,  5311,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5312,
     -1,    -1,    -1,  5313,    -1,    -1,    -1,    -1,
     -1,    -1,  5314,    -1,    -1,    -1,    -1,    -1,
     -1,  5315,    -1,    -1,    -1,    -1,    -1,    -1,
   5316,    -1,    -1,  5319,  5321,    -1,    -1,    -1,
   5322,  5323,  5324,    -1,    -1,    -1,    -1,  5325,
   5326,    -1,  5327,    -1,  5328,    -1,  5330,  5331,
   5332,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  5333,    -1,    -1,    -1,  5335,
     -1,  5336,    -1,    -1,    -1,    -1,  5337,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5339,  5341,  5344,    -1,    -1,  5345,    -1,
     -1,    -1,    -1,  5346,    -1,    -1,    -1,    -1,
   5347,    -1,    -1,    -1,    -1,  5348,    -1,    -1,
   5349,    -1,    -1,    -1,    -1,  5350,    -1,  5352,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5353,  5355,  5356,    -1,  5357,    -1,    -1,  5358,
     -1,    -1,  5359,    -1,  5360,    -1,    -1,    -1,
     -1,    -1,  5362,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  5364,    -1,    -1,    -1,    -1,
   5365,    -1,    -1,    -1,    -1,    -1,  5366,  5367,
     -1,    -1,    -1,    -1,    -1,    -1,  5368,  5370,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x7500 */
     -1,  5371,    -1,    -1,    -1,    -1,  5373,    -1,
     -1,    -1,    -1,    -1,  5374,    -1,  5375,    -1,
     -1,    -1,    -1,    -1,    -1,  5378,  5379,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  5381,    -1,
   5384,    -1,  5385,  5387,    -1,    -1,  5389,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5392,
     -1,    -1,    -1,    -1,    -1,  5398,    -1,  5399,
     -1,    -1,  5400,  5402,  5407,  5409,    -1,    -1,
     -1,    -1,  5410,    -1,  5411,  5413,  5414,    -1,
     -1,    -1,  5418,    -1,  5419,  5421,  5423,    -1,
     -1,  5424,  5425,    -1,    -1,    -1,    -1,    -1,
     -1,  5429,  5431,    -1,    -1,  5432,  5437,    -1,
   5441,  5442,  5443,    -1,    -1,  5444,    -1,  5445,
   5446,    -1,  5447,  5448,    -1,    -1,    -1,    -1,
   5450,  5451,    -1,  5453,  5457,  5458,  5460,    -1,
     -1,    -1,  5461,  5462,    -1,    -1,    -1,    -1,
     -1,    -1,  5463,    -1,    -1,  5467,  5469,  5470,
     -1,  5471,  5475,    -1,    -1,    -1,  5479,  5481,
     -1,    -1,    -1,    -1,    -1,    -1,  5483,  5484,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5485,
   5486,  5487,    -1,  5488,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5489,  5490,  5491,  5492,
     -1,  5493,    -1,    -1,  5495,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5496,
   5497,  5498,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5499,    -1,    -1,    -1,  5500,    -1,
     -1,  5504,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5505,  5506,  5507,  5508,    -1,    -1,  5509,    -1,
     -1,    -1,  5510,  5512,  5515,    -1,    -1,    -1,
     -1,  5516,  5517,    -1,    -1,    -1,  5518,    -1,
  /* 0x7600 */
     -1,    -1,  5519,    -1,    -1,  5523,  5524,    -1,
     -1,  5525,    -1,  5527,    -1,  5528,    -1,    -1,
     -1,    -1,    -1,  5529,    -1,    -1,  5530,  5531,
   5532,    -1,    -1,    -1,    -1,    -1,  5533,    -1,
     -1,  5534,    -1,    -1,  5535,    -1,  5536,  5537,
     -1,    -1,  5538,  5539,    -1,    -1,  5540,    -1,
     -1,    -1,  5541,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5542,  5543,    -1,    -1,  5545,  5546,
     -1,    -1,  5547,    -1,  5548,  5550,  5551,  5552,
     -1,  5553,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5554,    -1,    -1,  5556,    -1,    -1,
   5558,    -1,    -1,    -1,    -1,    -1,  5559,  5560,
     -1,  5561,  5562,  5563,  5564,  5565,    -1,  5566,
     -1,  5567,    -1,  5568,  5569,  5570,  5571,  5572,
   5573,  5576,  5577,    -1,  5578,  5579,    -1,    -1,
     -1,    -1,  5580,  5590,  5591,    -1,    -1,    -1,
     -1,  5593,  5595,  5597,    -1,    -1,    -1,    -1,
   5598,    -1,    -1,  5601,    -1,  5602,  5604,    -1,
   5605,  5606,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5607,    -1,    -1,    -1,  5609,    -1,
     -1,  5610,    -1,    -1,    -1,  5611,    -1,    -1,
   5613,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5614,  5616,  5617,    -1,    -1,    -1,    -1,  5619,
   5620,  5622,  5624,    -1,  5625,    -1,    -1,    -1,
     -1,  5626,    -1,  5627,    -1,    -1,    -1,  5629,
   5630,    -1,    -1,  5631,  5634,  5635,    -1,  5636,
   5637,  5639,    -1,    -1,    -1,    -1,  5641,  5643,
   5644,  5645,    -1,    -1,  5646,    -1,  5647,    -1,
     -1,  5648,    -1,  5649,  5651,    -1,    -1,  5652,
     -1,    -1,  5653,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x7700 */
     -1,    -1,    -1,    -1,    -1,    -1,  5655,    -1,
     -1,  5657,    -1,    -1,  5658,  5660,  5661,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5663,    -1,    -1,    -1,    -1,    -1,  5664,  5665,
     -1,    -1,    -1,    -1,    -1,  5666,  5667,    -1,
     -1,    -1,    -1,  5668,  5669,    -1,    -1,  5670,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5672,
     -1,    -1,    -1,    -1,    -1,    -1,  5673,    -1,
   5676,  5677,  5678,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5679,
   5680,  5681,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5682,    -1,  5683,    -1,
   5684,    -1,    -1,    -1,  5685,  5686,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5687,    -1,    -1,    -1,    -1,    -1,  5688,
     -1,    -1,    -1,    -1,    -1,  5690,  5691,  5692,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5693,    -1,    -1,    -1,  5694,    -1,
   5695,    -1,    -1,    -1,    -1,    -1,  5696,    -1,
   5697,    -1,  5698,    -1,    -1,    -1,    -1,    -1,
     -1,  5699,  5700,    -1,    -1,  5701,    -1,    -1,
   5703,    -1,    -1,    -1,    -1,    -1,  5704,    -1,
     -1,    -1,    -1,    -1,  5705,    -1,  5706,    -1,
     -1,  5710,    -1,    -1,    -1,    -1,    -1,  5711,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5712,  5713,    -1,    -1,    -1,    -1,
     -1,  5714,  5715,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5716,    -1,  5717,    -1,  5719,  5720,
     -1,    -1,    -1,  5722,    -1,    -1,    -1,  5723,
     -1,    -1,    -1,    -1,  5724,    -1,  5727,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  5728,  5729,
  /* 0x7800 */
   5734,  5735,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5736,    -1,    -1,  5737,  5738,  5741,
     -1,    -1,  5742,    -1,  5743,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5744,    -1,  5745,
     -1,    -1,    -1,    -1,  5746,    -1,    -1,    -1,
     -1,    -1,  5747,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5750,  5751,    -1,    -1,  5752,  5753,
   5758,  5759,    -1,  5760,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5761,  5762,  5763,
     -1,  5764,  5765,    -1,  5766,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  5767,    -1,    -1,    -1,
   5768,    -1,  5769,    -1,    -1,    -1,  5770,  5773,
     -1,    -1,    -1,    -1,    -1,  5774,    -1,  5775,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5778,    -1,    -1,    -1,    -1,    -1,  5782,
     -1,    -1,    -1,    -1,    -1,  5784,  5785,    -1,
     -1,    -1,    -1,    -1,  5786,  5787,    -1,  5790,
     -1,    -1,    -1,  5793,  5794,    -1,  5795,  5798,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5799,  5800,    -1,    -1,  5801,    -1,  5802,
   5803,  5805,    -1,    -1,    -1,    -1,    -1,    -1,
   5807,    -1,  5808,  5810,  5812,    -1,    -1,    -1,
     -1,  5813,    -1,    -1,    -1,    -1,    -1,  5814,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5815,  5816,    -1,  5817,    -1,  5818,    -1,
     -1,    -1,  5819,    -1,    -1,    -1,    -1,    -1,
   5822,    -1,    -1,  5823,    -1,    -1,    -1,  5824,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5825,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5826,
   5827,    -1,  5828,    -1,    -1,  5829,    -1,    -1,
  /* 0x7900 */
   5830,    -1,    -1,    -1,  5831,    -1,  5832,    -1,
   5835,    -1,    -1,    -1,    -1,    -1,  5837,    -1,
     -1,    -1,  5838,    -1,    -1,    -1,    -1,    -1,
     -1,  5839,    -1,    -1,    -1,    -1,    -1,  5840,
   5843,    -1,    -1,    -1,    -1,    -1,  5844,    -1,
     -1,    -1,  5849,  5850,  5851,    -1,  5852,    -1,
     -1,  5855,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5856,  5857,  5858,    -1,    -1,  5859,
   5860,    -1,    -1,  5861,    -1,  5862,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  5863,    -1,
     -1,    -1,    -1,    -1,    -1,  5864,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  5865,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5866,
     -1,    -1,    -1,    -1,    -1,  5867,    -1,  5868,
   5869,    -1,    -1,    -1,    -1,    -1,    -1,  5870,
   5871,    -1,    -1,    -1,  5872,  5873,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5874,  5875,    -1,
     -1,    -1,    -1,    -1,    -1,  5876,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  5877,    -1,    -1,    -1,    -1,  5878,    -1,
     -1,  5879,  5880,    -1,    -1,    -1,  5881,    -1,
   5882,  5883,    -1,    -1,  5884,    -1,    -1,    -1,
     -1,    -1,    -1,  5885,    -1,    -1,    -1,  5886,
     -1,  5887,    -1,  5888,    -1,    -1,  5889,  5890,
   5895,    -1,  5896,  5897,  5902,  5905,    -1,    -1,
   5906,    -1,    -1,    -1,  5907,  5909,  5910,  5911,
   5912,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5913,
   5914,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   5916,    -1,    -1,    -1,    -1,  5917,  5918,    -1,
  /* 0x7a00 */
     -1,  5919,    -1,    -1,    -1,  5923,  5924,    -1,
   5926,  5927,    -1,    -1,    -1,    -1,  5929,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  5930,
     -1,    -1,  5932,    -1,  5934,    -1,    -1,  5935,
     -1,    -1,    -1,  5936,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  5937,  5938,    -1,
     -1,  5939,  5941,  5942,    -1,    -1,    -1,    -1,
     -1,    -1,  5944,  5946,    -1,    -1,  5947,  5951,
   5955,    -1,  5957,    -1,    -1,  5958,    -1,    -1,
     -1,  5960,    -1,    -1,  5962,  5966,  5967,  5969,
   5971,  5974,    -1,    -1,    -1,    -1,    -1,  5975,
     -1,    -1,    -1,    -1,    -1,    -1,  5976,    -1,
   5978,  5979,  5980,  5981,    -1,    -1,    -1,    -1,
   5982,  5986,  5988,  5990,    -1,  5991,    -1,    -1,
   5993,    -1,    -1,    -1,    -1,    -1,    -1,  5994,
     -1,  5996,    -1,    -1,    -1,  5997,    -1,    -1,
     -1,  5998,    -1,  5999,    -1,    -1,    -1,    -1,
     -1,    -1,  6000,    -1,    -1,  6001,  6002,    -1,
     -1,  6003,    -1,  6005,    -1,    -1,    -1,  6009,
     -1,    -1,    -1,    -1,  6013,  6014,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  6015,  6016,    -1,
     -1,  6017,  6018,    -1,  6019,  6020,  6022,  6024,
   6026,    -1,    -1,  6028,    -1,  6029,  6030,    -1,
     -1,    -1,  6032,  6033,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6037,  6039,  6040,  6041,  6043,
   6044,    -1,  6046,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  6047,    -1,    -1,    -1,  6048,    -1,
     -1,    -1,  6050,    -1,  6051,  6053,  6056,    -1,
     -1,    -1,  6058,    -1,    -1,    -1,    -1,    -1,
     -1,  6059,  6061,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  6063,    -1,
   6065,    -1,    -1,    -1,    -1,    -1,  6067,    -1,
  /* 0x7b00 */
     -1,    -1,    -1,  6068,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6069,    -1,    -1,    -1,    -1,
     -1,  6071,    -1,    -1,  6073,  6074,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  6075,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  6076,
   6082,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  6083,  6085,    -1,
     -1,    -1,  6086,    -1,  6088,    -1,  6090,    -1,
     -1,    -1,    -1,    -1,    -1,  6091,  6092,    -1,
     -1,    -1,    -1,  6093,    -1,  6094,    -1,  6096,
   6098,  6099,    -1,    -1,    -1,    -1,  6100,    -1,
   6104,  6105,  6106,  6107,  6108,  6109,  6110,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  6114,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  6115,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  6116,  6117,    -1,  6118,    -1,  6119,  6122,
   6123,    -1,  6124,    -1,    -1,    -1,  6125,  6126,
     -1,    -1,    -1,  6129,    -1,  6130,    -1,  6131,
     -1,    -1,  6132,  6133,    -1,    -1,    -1,    -1,
     -1,    -1,  6134,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6135,    -1,    -1,  6139,  6141,
   6142,  6143,  6144,  6145,  6146,    -1,    -1,    -1,
   6147,    -1,  6149,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   6150,    -1,    -1,    -1,  6151,    -1,  6153,    -1,
     -1,  6154,    -1,  6155,    -1,    -1,    -1,  6157,
     -1,  6158,    -1,  6159,  6160,    -1,    -1,  6161,
     -1,    -1,    -1,  6162,    -1,    -1,    -1,    -1,
   6163,  6165,    -1,    -1,  6166,    -1,  6167,    -1,
     -1,  6168,  6171,    -1,    -1,  6172,  6174,  6175,
     -1,  6176,  6177,  6178,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x7c00 */
   6179,  6181,    -1,    -1,  6183,    -1,  6185,    -1,
     -1,    -1,    -1,    -1,    -1,  6186,    -1,    -1,
     -1,  6187,  6189,    -1,  6190,    -1,  6192,  6193,
   6195,    -1,    -1,    -1,    -1,    -1,  6196,    -1,
   6197,  6198,    -1,  6199,    -1,    -1,    -1,    -1,
     -1,    -1,  6200,  6201,    -1,    -1,    -1,    -1,
     -1,  6203,    -1,    -1,    -1,    -1,    -1,  6205,
     -1,  6206,    -1,    -1,    -1,  6207,  6208,    -1,
   6209,  6211,    -1,  6212,    -1,    -1,  6213,    -1,
     -1,    -1,    -1,    -1,  6214,    -1,    -1,  6215,
   6217,    -1,  6218,    -1,  6219,  6221,  6222,    -1,
   6224,  6226,    -1,  6227,  6228,    -1,    -1,  6229,
   6230,    -1,    -1,    -1,  6232,    -1,    -1,    -1,
     -1,  6234,  6235,    -1,  6236,  6237,  6239,    -1,
     -1,    -1,  6240,    -1,  6242,    -1,    -1,    -1,
     -1,    -1,    -1,  6243,  6244,    -1,    -1,    -1,
     -1,    -1,    -1,  6245,    -1,    -1,    -1,  6246,
     -1,    -1,    -1,  6248,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  6249,
   6250,    -1,    -1,  6251,  6253,  6254,    -1,    -1,
     -1,    -1,    -1,    -1,  6255,  6256,  6258,  6259,
     -1,    -1,  6263,    -1,  6264,    -1,  6268,    -1,
     -1,    -1,    -1,  6269,    -1,  6271,    -1,    -1,
     -1,  6272,  6273,    -1,    -1,  6274,    -1,    -1,
     -1,  6275,    -1,    -1,    -1,    -1,  6276,  6281,
     -1,  6282,  6283,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6284,    -1,  6286,    -1,    -1,
     -1,    -1,  6287,    -1,    -1,  6291,  6292,    -1,
   6293,    -1,    -1,    -1,    -1,    -1,  6295,  6296,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   6297,    -1,  6299,    -1,  6300,    -1,  6301,    -1,
   6302,  6306,  6308,  6309,  6313,    -1,  6315,  6317,
  /* 0x7d00 */
   6319,    -1,  6320,    -1,  6321,  6322,  6323,  6324,
   6325,  6326,    -1,  6327,    -1,  6328,    -1,    -1,
   6329,    -1,    -1,  6330,  6331,  6332,  6333,  6334,
   6335,  6337,  6339,  6340,  6341,  6342,    -1,    -1,
     -1,  6344,    -1,  6345,    -1,  6346,    -1,  6347,
     -1,    -1,    -1,    -1,  6348,    -1,  6350,  6352,
   6356,  6357,  6358,  6359,    -1,  6360,    -1,    -1,
     -1,  6361,  6362,    -1,  6363,    -1,    -1,  6364,
   6365,    -1,  6366,  6367,  6368,  6369,  6370,    -1,
   6372,    -1,    -1,  6373,  6374,  6376,  6377,  6378,
   6379,    -1,    -1,    -1,    -1,  6380,  6383,    -1,
     -1,    -1,  6384,  6385,    -1,  6387,  6389,    -1,
     -1,  6390,  6391,    -1,  6392,  6396,  6397,    -1,
   6398,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   6399,  6400,  6402,  6404,    -1,  6405,  6407,  6410,
   6411,  6413,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  6414,  6415,  6417,    -1,    -1,  6418,    -1,
   6419,  6420,    -1,  6421,  6422,    -1,    -1,  6423,
     -1,  6424,    -1,  6426,    -1,    -1,    -1,  6428,
     -1,  6429,  6431,    -1,  6434,    -1,  6435,    -1,
   6436,    -1,  6438,  6440,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6441,  6445,  6446,    -1,  6447,
   6448,  6449,  6450,  6451,  6453,  6455,    -1,  6456,
   6457,  6458,  6459,  6460,    -1,  6461,  6463,  6464,
     -1,    -1,    -1,    -1,  6465,    -1,    -1,  6466,
     -1,    -1,  6467,  6468,    -1,  6469,    -1,  6470,
   6474,  6475,  6477,    -1,  6479,  6480,  6481,  6483,
   6484,  6485,  6486,    -1,  6490,  6491,  6492,    -1,
   6493,  6494,    -1,  6496,    -1,  6498,  6499,    -1,
   6500,  6501,  6502,  6503,  6507,    -1,    -1,  6508,
     -1,  6509,  6510,    -1,  6511,    -1,  6512,    -1,
     -1,  6513,    -1,  6514,  6515,    -1,    -1,    -1,
  /* 0x7e00 */
     -1,  6517,    -1,    -1,  6519,    -1,    -1,    -1,
   6521,  6522,  6523,  6524,    -1,    -1,    -1,    -1,
   6525,  6526,    -1,    -1,    -1,  6527,    -1,  6529,
   6530,    -1,    -1,  6533,    -1,  6534,  6535,  6536,
     -1,    -1,    -1,  6537,    -1,    -1,  6539,  6541,
   6543,    -1,    -1,  6544,    -1,  6545,  6546,    -1,
     -1,  6547,  6549,    -1,  6552,  6553,  6554,  6556,
     -1,  6557,    -1,    -1,    -1,  6558,  6563,    -1,
     -1,  6564,    -1,  6565,    -1,  6567,  6569,    -1,
   6570,    -1,  6571,  6574,    -1,  6577,    -1,    -1,
     -1,    -1,  6579,    -1,  6580,  6581,  6582,    -1,
     -1,  6584,  6585,  6586,    -1,    -1,  6587,    -1,
     -1,  6588,  6590,    -1,  6591,    -1,  6592,    -1,
     -1,  6593,  6595,  6597,    -1,  6600,  6603,  6605,
   6606,    -1,    -1,  6609,    -1,    -1,    -1,    -1,
     -1,  6610,    -1,    -1,  6611,  6613,  6614,  6615,
     -1,    -1,  6616,  6617,    -1,    -1,    -1,  6618,
   6619,  6620,  6622,    -1,  6624,  6627,  6628,  6631,
     -1,    -1,  6633,  6635,  6636,    -1,  6637,    -1,
   6640,    -1,    -1,    -1,  6642,  6643,    -1,  6646,
   6648,  6649,  6650,  6651,  6652,  6656,  6657,  6658,
   6659,  6660,  6662,  6663,  6664,  6665,  6666,  6667,
   6668,  6669,  6670,  6671,  6672,  6673,  6675,  6676,
   6677,  6678,  6679,  6680,  6681,  6682,  6683,  6684,
   6688,  6689,  6691,  6692,  6693,  6694,  6695,  6696,
   6697,  6698,  6699,  6700,  6701,  6702,  6703,  6704,
   6706,  6707,  6708,  6709,  6710,  6711,  6713,  6714,
   6715,  6717,  6718,  6719,  6720,  6721,  6724,  6725,
   6727,  6728,  6729,  6730,  6733,  6734,  6735,  6737,
   6739,  6740,  6741,  6743,    -1,  6744,  6746,  6747,
   6748,  6749,  6750,  6751,  6753,  6754,  6756,  6757,
   6759,  6761,  6762,  6763,  6764,  6765,  6766,  6767,
  /* 0x7f00 */
   6769,  6770,  6771,  6772,  6773,  6774,  6775,  6776,
   6777,  6778,  6779,  6781,  6782,  6783,  6784,  6785,
   6786,  6790,  6791,  6792,  6793,  6794,  6795,  6796,
   6797,  6799,  6800,  6801,  6802,  6803,  6804,  6805,
   6806,  6808,  6809,  6810,  6811,  6812,  6813,  6814,
   6817,  6818,  6819,  6820,  6822,  6823,  6824,  6825,
   6826,  6828,  6829,  6830,  6831,  6832,  6834,  6837,
   6838,  6839,  6840,  6842,  6843,  6845,  6848,    -1,
     -1,    -1,  6850,    -1,    -1,    -1,    -1,  6852,
   6853,    -1,    -1,    -1,  6858,    -1,  6860,    -1,
   6865,  6867,  6870,  6872,    -1,    -1,    -1,  6874,
     -1,    -1,  6877,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  6879,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  6880,    -1,    -1,    -1,    -1,    -1,
   6882,    -1,    -1,    -1,  6884,  6885,  6888,  6889,
   6890,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   6892,  6893,    -1,  6895,    -1,  6897,  6898,  6899,
   6902,    -1,  6906,  6907,  6908,    -1,    -1,    -1,
   6909,  6910,    -1,    -1,    -1,    -1,    -1,  6911,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  6912,
     -1,  6913,    -1,  6914,  6915,  6916,    -1,    -1,
   6917,  6918,  6919,    -1,    -1,    -1,  6921,    -1,
     -1,    -1,    -1,    -1,  6922,    -1,  6924,    -1,
     -1,  6926,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  6927,  6929,  6931,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  6932,    -1,    -1,    -1,    -1,    -1,
   6933,  6934,  6935,    -1,    -1,    -1,    -1,    -1,
   6936,    -1,    -1,    -1,  6937,    -1,    -1,    -1,
     -1,    -1,    -1,  6939,  6940,    -1,    -1,    -1,
     -1,  6941,    -1,  6942,    -1,    -1,    -1,    -1,
     -1,  6943,  6944,  6945,    -1,  6947,    -1,    -1,
  /* 0x8000 */
   6948,    -1,    -1,  6949,    -1,    -1,    -1,  6950,
   6951,    -1,  6952,  6953,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  6954,    -1,    -1,
   6955,    -1,    -1,    -1,  6956,    -1,    -1,    -1,
     -1,  6957,  6958,    -1,  6959,    -1,    -1,  6960,
   6961,    -1,    -1,    -1,  6963,    -1,  6964,    -1,
   6965,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   6966,    -1,    -1,  6967,  6968,  6969,    -1,    -1,
   6970,    -1,  6973,  6974,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  6975,  6976,  6979,    -1,    -1,
     -1,    -1,    -1,    -1,  6980,    -1,  6982,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  6984,  6985,
     -1,  6987,    -1,    -1,    -1,    -1,  6990,    -1,
   6993,  6995,  6996,  6999,    -1,  7000,  7002,  7003,
   7007,    -1,  7010,  7011,  7012,  7014,  7015,  7016,
     -1,  7019,    -1,    -1,    -1,  7020,  7022,    -1,
     -1,    -1,    -1,  7023,    -1,  7025,    -1,  7027,
   7028,  7029,    -1,    -1,    -1,    -1,  7030,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   7031,  7033,    -1,    -1,  7034,    -1,    -1,  7035,
     -1,    -1,    -1,    -1,  7036,    -1,  7037,  7038,
     -1,  7040,  7041,    -1,  7043,    -1,    -1,    -1,
     -1,    -1,    -1,  7044,    -1,    -1,  7045,  7046,
   7047,  7048,    -1,    -1,  7051,    -1,  7053,    -1,
     -1,    -1,    -1,    -1,  7055,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7056,    -1,  7057,    -1,    -1,    -1,
     -1,  7058,    -1,    -1,    -1,    -1,    -1,  7060,
   7061,    -1,  7062,  7063,    -1,  7065,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7066,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x8100 */
     -1,    -1,    -1,  7067,    -1,  7069,  7071,  7073,
   7074,  7075,    -1,  7076,    -1,  7078,    -1,  7079,
   7082,  7083,    -1,  7085,  7086,    -1,    -1,  7087,
     -1,    -1,  7088,  7090,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7092,    -1,    -1,    -1,    -1,
     -1,  7093,    -1,  7094,    -1,    -1,    -1,    -1,
     -1,  7095,    -1,  7096,    -1,  7099,  7100,    -1,
   7101,  7102,  7104,    -1,    -1,    -1,  7106,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7107,    -1,
     -1,    -1,  7108,    -1,  7110,    -1,  7111,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7112,  7113,
   7114,    -1,    -1,    -1,    -1,    -1,    -1,  7115,
     -1,  7116,    -1,    -1,    -1,    -1,  7117,    -1,
     -1,    -1,    -1,  7120,    -1,  7121,  7122,    -1,
     -1,    -1,    -1,  7123,    -1,    -1,    -1,    -1,
   7125,    -1,    -1,  7127,    -1,  7128,  7129,    -1,
   7130,    -1,  7131,  7132,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   7133,  7134,    -1,  7135,    -1,  7137,    -1,    -1,
     -1,    -1,  7138,    -1,    -1,    -1,    -1,    -1,
   7139,    -1,    -1,  7140,  7141,    -1,    -1,    -1,
     -1,  7142,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7143,    -1,    -1,    -1,    -1,
   7144,    -1,    -1,  7146,    -1,  7150,  7152,  7153,
   7154,    -1,    -1,  7155,    -1,    -1,    -1,    -1,
   7157,  7159,    -1,  7160,    -1,  7161,    -1,  7162,
     -1,    -1,    -1,  7164,    -1,    -1,    -1,    -1,
   7166,  7168,  7169,    -1,  7170,    -1,  7171,  7172,
   7174,    -1,  7175,    -1,    -1,  7176,    -1,    -1,
   7177,    -1,    -1,    -1,    -1,  7178,    -1,    -1,
   7179,    -1,    -1,    -1,  7180,    -1,    -1,    -1,
     -1,    -1,  7181,    -1,    -1,    -1,    -1,    -1,
  /* 0x8200 */
     -1,    -1,    -1,    -1,    -1,    -1,  7183,  7184,
   7185,  7186,  7189,    -1,    -1,  7190,  7192,    -1,
   7193,    -1,    -1,  7194,    -1,    -1,  7195,  7198,
   7201,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7202,    -1,    -1,    -1,    -1,
     -1,  7203,    -1,    -1,    -1,    -1,  7204,    -1,
   7206,  7207,    -1,    -1,    -1,  7208,  7209,    -1,
     -1,  7210,    -1,  7211,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7213,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7214,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7215,  7216,  7218,    -1,  7219,    -1,
     -1,    -1,  7220,  7222,    -1,    -1,    -1,    -1,
   7224,  7225,    -1,  7227,    -1,    -1,  7231,  7235,
   7239,  7240,  7241,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7246,    -1,    -1,    -1,    -1,    -1,
   7247,    -1,    -1,  7249,  7250,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  7251,    -1,    -1,  7252,
     -1,    -1,    -1,    -1,  7253,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7254,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7255,  7256,    -1,    -1,    -1,    -1,    -1,
   7257,    -1,    -1,  7264,    -1,    -1,    -1,    -1,
     -1,  7265,    -1,    -1,    -1,  7266,    -1,  7268,
   7269,    -1,    -1,  7270,  7271,  7272,  7273,  7274,
   7279,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7280,  7281,    -1,    -1,    -1,    -1,  7282,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7283,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7284,  7286,    -1,    -1,  7287,    -1,    -1,
  /* 0x8300 */
     -1,    -1,    -1,  7288,    -1,    -1,    -1,    -1,
     -1,    -1,  7290,    -1,    -1,    -1,  7292,  7293,
     -1,  7294,    -1,    -1,  7295,  7296,    -1,    -1,
   7297,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7298,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7300,
   7301,    -1,  7302,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7304,  7305,
     -1,    -1,  7306,    -1,    -1,    -1,    -1,    -1,
   7307,  7309,    -1,    -1,  7310,    -1,    -1,    -1,
   7311,  7313,  7314,  7315,  7316,  7317,  7318,  7319,
   7320,  7322,    -1,  7325,  7327,  7328,  7329,  7330,
   7331,  7332,  7333,  7334,  7335,  7336,  7337,  7338,
     -1,    -1,    -1,  7341,    -1,    -1,    -1,    -1,
     -1,  7342,    -1,  7343,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7344,    -1,    -1,
     -1,    -1,  7346,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7348,    -1,  7349,  7350,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7351,    -1,
     -1,    -1,  7352,    -1,    -1,    -1,    -1,  7353,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7354,  7355,  7356,  7357,  7358,  7359,  7360,
   7362,  7363,  7364,    -1,  7367,  7369,  7370,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7371,    -1,  7372,  7373,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7375,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7376,    -1,    -1,    -1,  7377,
   7380,  7381,    -1,    -1,  7382,    -1,    -1,    -1,
     -1,  7385,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x8400 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7386,
     -1,    -1,  7387,    -1,  7388,  7389,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7390,    -1,    -1,
     -1,    -1,  7392,    -1,    -1,  7393,    -1,  7394,
   7399,    -1,    -1,    -1,  7400,  7402,  7404,  7405,
   7406,    -1,    -1,    -1,  7407,    -1,    -1,    -1,
     -1,  7408,    -1,    -1,  7411,  7412,    -1,    -1,
   7413,    -1,    -1,    -1,  7414,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7415,    -1,    -1,
     -1,  7416,  7417,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7420,    -1,    -1,    -1,    -1,  7421,
   7422,    -1,  7423,    -1,    -1,    -1,    -1,    -1,
   7424,    -1,  7425,    -1,  7427,    -1,  7428,    -1,
     -1,    -1,  7429,    -1,  7430,    -1,  7431,  7432,
     -1,  7433,    -1,    -1,    -1,    -1,    -1,  7434,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7435,  7436,    -1,    -1,    -1,  7437,
     -1,  7438,    -1,  7439,  7440,    -1,    -1,    -1,
   7441,    -1,    -1,  7442,  7444,    -1,    -1,    -1,
     -1,  7445,    -1,    -1,    -1,    -1,  7449,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7451,    -1,
   7452,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7453,
     -1,    -1,    -1,    -1,  7454,    -1,    -1,    -1,
   7455,    -1,    -1,    -1,    -1,    -1,  7456,    -1,
     -1,    -1,    -1,  7457,    -1,    -1,    -1,    -1,
     -1,  7459,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7461,    -1,  7462,
   7463,    -1,    -1,  7464,    -1,  7465,  7466,    -1,
     -1,    -1,    -1,    -1,    -1,  7467,  7470,  7471,
     -1,  7472,    -1,    -1,  7473,    -1,    -1,    -1,
     -1,    -1,  7475,    -1,    -1,  7480,    -1,    -1,
  /* 0x8500 */
     -1,    -1,    -1,    -1,    -1,  7481,  7483,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   7484,  7485,    -1,    -1,  7486,  7488,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7489,    -1,
     -1,    -1,    -1,  7490,    -1,  7491,  7492,    -1,
     -1,    -1,    -1,    -1,    -1,  7493,    -1,  7494,
     -1,    -1,  7495,  7496,  7497,  7498,  7500,  7502,
     -1,  7503,  7504,  7505,  7506,    -1,    -1,    -1,
     -1,  7508,    -1,    -1,    -1,    -1,  7509,    -1,
     -1,    -1,  7510,  7512,    -1,    -1,  7514,    -1,
   7515,    -1,  7517,  7518,    -1,  7519,    -1,    -1,
   7520,    -1,  7521,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7522,    -1,    -1,    -1,    -1,    -1,
     -1,  7523,  7524,    -1,    -1,  7525,    -1,    -1,
   7526,    -1,  7527,    -1,  7528,    -1,    -1,  7530,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7531,
   7534,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   7535,    -1,  7536,    -1,  7538,    -1,    -1,    -1,
     -1,  7539,    -1,  7540,  7541,    -1,    -1,  7542,
   7544,    -1,    -1,    -1,    -1,    -1,    -1,  7545,
     -1,    -1,    -1,    -1,    -1,    -1,  7546,    -1,
     -1,  7548,    -1,  7549,  7551,  7553,  7554,  7556,
   7557,    -1,  7559,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7561,    -1,  7563,    -1,    -1,    -1,
     -1,  7564,  7568,    -1,    -1,    -1,    -1,    -1,
     -1,  7572,    -1,    -1,    -1,  7575,  7576,  7577,
     -1,    -1,    -1,  7579,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7580,    -1,  7585,
     -1,    -1,    -1,    -1,  7587,  7588,    -1,    -1,
     -1,    -1,  7590,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7592,  7596,    -1,  7598,  7599,
     -1,  7600,  7602,    -1,  7603,    -1,    -1,    -1,
  /* 0x8600 */
   7606,    -1,  7607,    -1,  7609,    -1,  7610,  7611,
     -1,    -1,  7615,  7617,    -1,  7619,  7621,    -1,
   7623,    -1,    -1,  7626,    -1,    -1,    -1,  7628,
     -1,    -1,  7629,    -1,    -1,    -1,  7630,    -1,
     -1,    -1,  7631,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7632,    -1,  7633,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  7635,    -1,    -1,  7636,    -1,  7638,
     -1,  7639,    -1,    -1,    -1,    -1,  7640,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7642,  7644,
     -1,  7645,    -1,  7646,    -1,  7647,  7649,  7650,
     -1,    -1,  7652,  7654,  7656,  7657,    -1,  7659,
     -1,    -1,    -1,    -1,    -1,  7660,  7661,  7662,
     -1,    -1,    -1,  7663,  7665,    -1,  7666,  7667,
     -1,  7668,    -1,    -1,    -1,  7669,    -1,    -1,
     -1,    -1,  7671,  7672,    -1,  7673,  7674,  7675,
   7676,  7677,  7678,  7679,    -1,    -1,    -1,    -1,
     -1,    -1,  7680,  7682,  7683,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7684,  7687,    -1,
   7688,    -1,    -1,    -1,    -1,  7689,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7690,    -1,
     -1,    -1,    -1,  7691,  7692,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  7693,    -1,    -1,    -1,
     -1,    -1,  7694,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7695,
     -1,    -1,  7697,    -1,    -1,  7698,  7700,  7702,
     -1,    -1,    -1,    -1,  7703,    -1,    -1,    -1,
     -1,  7706,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  7707,  7708,
   7709,  7710,  7711,  7712,  7713,    -1,    -1,    -1,
     -1,    -1,  7714,  7715,    -1,  7716,    -1,    -1,
  /* 0x8700 */
     -1,    -1,  7718,    -1,    -1,    -1,  7719,    -1,
     -1,    -1,    -1,  7720,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7721,  7722,  7723,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7724,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7726,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7727,    -1,    -1,    -1,  7728,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7729,    -1,  7732,
   7734,  7735,    -1,  7736,    -1,    -1,  7738,    -1,
     -1,    -1,    -1,    -1,    -1,  7739,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7740,
     -1,  7741,    -1,    -1,    -1,    -1,  7742,    -1,
   7743,    -1,    -1,    -1,    -1,    -1,    -1,  7744,
     -1,  7745,    -1,    -1,    -1,    -1,    -1,    -1,
   7746,    -1,    -1,    -1,  7747,    -1,  7748,  7749,
   7751,    -1,  7752,    -1,  7753,    -1,    -1,    -1,
   7754,    -1,    -1,    -1,    -1,  7755,  7756,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   7757,  7758,    -1,    -1,    -1,    -1,  7760,    -1,
     -1,  7761,  7762,    -1,  7764,    -1,    -1,  7766,
   7767,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7768,    -1,    -1,    -1,  7769,
     -1,  7770,    -1,    -1,  7771,    -1,  7772,  7773,
   7774,    -1,    -1,    -1,    -1,    -1,  7775,  7776,
     -1,    -1,  7777,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7778,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  7779,    -1,    -1,  7780,
     -1,    -1,  7781,    -1,    -1,    -1,  7782,    -1,
   7783,  7784,    -1,  7785,    -1,    -1,    -1,    -1,
  /* 0x8800 */
     -1,  7787,    -1,    -1,    -1,  7788,  7790,  7791,
     -1,    -1,    -1,    -1,    -1,  7793,  7794,  7795,
   7796,  7797,  7798,    -1,  7801,  7802,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7803,
     -1,  7805,  7806,  7807,    -1,    -1,    -1,  7809,
   7811,    -1,    -1,    -1,    -1,  7812,    -1,    -1,
     -1,  7813,    -1,    -1,    -1,    -1,  7814,    -1,
     -1,  7817,  7819,  7822,  7823,    -1,    -1,    -1,
     -1,    -1,  7825,    -1,  7826,  7827,  7828,    -1,
     -1,  7831,  7833,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7834,  7835,  7838,  7839,    -1,
   7840,    -1,  7843,  7844,  7846,  7849,  7850,  7852,
     -1,    -1,    -1,  7855,  7856,  7857,    -1,    -1,
   7858,    -1,    -1,    -1,  7859,    -1,  7860,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7861,    -1,    -1,    -1,  7862,    -1,    -1,
     -1,    -1,    -1,    -1,  7863,  7864,  7867,    -1,
     -1,    -1,    -1,  7868,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  7869,    -1,  7872,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  7873,  7875,  7876,  7877,
     -1,    -1,    -1,    -1,  7878,  7880,    -1,  7881,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7882,  7883,    -1,
   7884,    -1,  7885,    -1,  7886,    -1,    -1,  7887,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  7889,    -1,    -1,  7890,  7891,    -1,    -1,
   7892,  7893,  7895,  7896,  7897,  7899,  7900,    -1,
   7902,    -1,    -1,    -1,    -1,  7903,    -1,    -1,
     -1,    -1,    -1,    -1,  7904,  7905,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7906,    -1,    -1,
  /* 0x8900 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  7907,
     -1,    -1,  7908,    -1,  7909,    -1,  7910,  7911,
     -1,    -1,  7912,  7914,    -1,    -1,    -1,    -1,
   7915,    -1,    -1,  7916,    -1,  7917,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7918,    -1,    -1,    -1,    -1,
     -1,    -1,  7919,  7920,  7921,    -1,    -1,    -1,
   7922,    -1,    -1,  7923,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  7924,    -1,    -1,    -1,  7926,
     -1,    -1,    -1,    -1,  7927,  7928,    -1,  7929,
     -1,    -1,    -1,    -1,    -1,  7930,  7931,    -1,
     -1,    -1,    -1,    -1,    -1,  7932,    -1,    -1,
   7933,    -1,    -1,    -1,  7934,    -1,    -1,    -1,
     -1,    -1,  7935,    -1,  7936,  7937,    -1,  7938,
     -1,    -1,  7939,    -1,  7940,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  7942,    -1,  7943,
     -1,    -1,    -1,    -1,    -1,    -1,  7945,  7947,
   7948,  7949,  7952,  7956,    -1,  7957,  7958,  7959,
   7960,    -1,    -1,  7963,  7965,    -1,  7967,    -1,
   7969,    -1,  7970,    -1,    -1,    -1,    -1,    -1,
     -1,  7973,    -1,    -1,    -1,  7974,  7975,  7976,
     -1,  7978,  7979,    -1,  7980,    -1,    -1,  7981,
   7982,    -1,  7983,  7984,    -1,    -1,  7986,  7987,
     -1,    -1,  7989,    -1,  7993,  7994,    -1,  7996,
   7997,  7999,  8000,  8002,  8003,  8004,  8006,  8008,
   8009,  8011,  8014,  8015,  8016,  8017,  8018,  8019,
   8020,  8021,    -1,    -1,  8022,    -1,    -1,  8023,
     -1,    -1,    -1,    -1,    -1,  8025,  8026,    -1,
     -1,    -1,    -1,  8027,    -1,    -1,  8028,  8029,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  8030,
     -1,    -1,    -1,    -1,  8032,    -1,  8033,    -1,
   8035,  8036,    -1,    -1,    -1,  8038,    -1,  8040,
  /* 0x8a00 */
   8042,  8044,  8046,  8047,    -1,    -1,    -1,    -1,
   8048,    -1,  8049,    -1,  8050,    -1,  8051,    -1,
   8052,    -1,  8053,  8054,    -1,  8055,  8056,  8057,
   8059,    -1,  8060,  8061,    -1,  8062,    -1,  8063,
   8064,  8066,  8068,  8069,    -1,  8070,    -1,    -1,
     -1,  8071,  8072,  8073,    -1,  8074,    -1,    -1,
     -1,  8075,    -1,  8076,  8078,    -1,  8079,    -1,
     -1,    -1,  8081,  8082,  8083,  8085,    -1,    -1,
     -1,  8086,    -1,    -1,    -1,    -1,  8087,    -1,
     -1,    -1,    -1,  8089,    -1,  8091,  8092,    -1,
   8093,    -1,  8094,    -1,  8095,  8096,  8097,  8098,
   8099,    -1,    -1,  8100,    -1,    -1,  8101,  8102,
   8103,  8104,  8105,  8106,    -1,    -1,  8107,  8108,
     -1,  8109,    -1,  8110,  8111,  8113,  8114,    -1,
   8115,  8116,  8118,  8119,    -1,  8120,    -1,    -1,
     -1,    -1,    -1,    -1,  8121,    -1,    -1,  8122,
     -1,    -1,    -1,    -1,  8123,  8124,  8125,  8126,
     -1,  8127,  8128,    -1,  8129,  8130,    -1,    -1,
     -1,  8131,  8132,    -1,    -1,  8133,  8134,    -1,
   8136,    -1,  8138,    -1,    -1,    -1,  8139,    -1,
   8140,  8141,    -1,  8142,  8143,  8144,  8145,    -1,
   8146,    -1,  8147,    -1,  8149,  8151,    -1,  8153,
   8154,    -1,  8155,    -1,    -1,    -1,  8156,    -1,
     -1,  8157,    -1,    -1,  8158,    -1,  8160,  8161,
     -1,    -1,  8162,    -1,  8163,    -1,    -1,  8164,
     -1,  8165,    -1,  8166,  8167,  8169,    -1,  8170,
     -1,  8171,  8172,    -1,    -1,    -1,  8173,  8174,
     -1,    -1,    -1,  8175,  8176,  8177,  8178,    -1,
   8179,  8181,  8183,    -1,  8184,    -1,  8186,  8187,
     -1,  8188,    -1,  8189,  8191,  8197,  8198,    -1,
     -1,  8199,    -1,  8200,    -1,    -1,  8202,  8203,
   8205,    -1,  8206,    -1,  8207,    -1,  8209,    -1,
  /* 0x8b00 */
   8210,  8211,  8212,    -1,  8213,  8214,    -1,    -1,
     -1,    -1,  8215,    -1,  8216,    -1,  8217,    -1,
   8218,    -1,    -1,    -1,  8219,    -1,  8220,  8221,
     -1,  8222,  8223,  8225,    -1,  8226,    -1,    -1,
   8227,  8229,    -1,    -1,    -1,    -1,    -1,    -1,
   8231,    -1,  8232,  8233,  8235,  8236,    -1,    -1,
   8238,    -1,    -1,  8239,    -1,    -1,    -1,    -1,
     -1,  8240,    -1,    -1,  8241,    -1,  8243,    -1,
     -1,  8244,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  8246,    -1,    -1,  8248,    -1,  8249,  8250,
   8251,    -1,    -1,    -1,    -1,    -1,  8252,    -1,
   8254,  8255,  8256,  8257,  8259,    -1,    -1,  8261,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  8262,  8264,    -1,  8266,  8267,  8269,
   8271,  8272,  8273,    -1,  8275,    -1,    -1,  8276,
   8277,    -1,    -1,    -1,  8278,  8280,  8281,    -1,
   8283,  8285,    -1,  8287,    -1,  8289,    -1,    -1,
     -1,    -1,  8290,  8292,  8293,  8295,  8297,    -1,
   8300,    -1,  8302,  8303,    -1,  8305,  8306,    -1,
     -1,    -1,  8307,    -1,  8309,    -1,  8310,    -1,
   8311,  8313,  8314,  8315,  8316,  8317,  8318,  8319,
   8320,  8321,  8323,  8324,  8325,  8327,  8328,  8329,
   8330,  8331,  8332,  8333,  8334,  8335,  8336,  8337,
   8338,  8339,  8341,  8342,  8343,  8344,  8346,  8347,
   8348,  8349,  8351,  8352,  8353,  8354,  8355,  8356,
   8357,  8358,  8359,  8360,  8361,  8362,  8363,  8364,
   8365,  8366,  8368,  8369,  8370,  8371,  8372,  8373,
   8374,  8375,  8376,  8377,  8378,  8379,  8381,  8382,
   8383,  8384,  8385,  8386,  8387,  8388,  8389,  8390,
   8391,  8392,  8393,  8394,  8395,  8396,  8397,  8398,
   8400,  8401,  8403,  8404,  8405,  8407,  8408,  8409,
   8410,  8411,  8412,  8413,  8415,  8416,  8417,  8418,
  /* 0x8c00 */
   8419,  8420,  8421,  8422,  8423,  8424,  8425,  8426,
   8427,  8428,  8429,  8431,  8432,  8433,  8434,  8435,
   8437,  8438,  8439,  8440,  8441,  8443,  8444,  8446,
   8447,  8448,  8450,  8451,  8452,  8453,  8454,  8455,
   8456,  8457,  8458,  8459,  8461,  8462,  8464,  8465,
   8466,  8467,  8468,  8470,  8472,  8473,  8474,  8476,
   8477,  8478,  8479,  8480,  8481,  8482,  8483,  8484,
     -1,    -1,  8486,    -1,    -1,    -1,    -1,  8487,
   8490,    -1,    -1,    -1,    -1,    -1,  8493,    -1,
   8494,    -1,  8495,    -1,    -1,    -1,  8499,    -1,
   8501,    -1,    -1,  8505,  8509,    -1,    -1,    -1,
   8513,    -1,  8514,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  8515,    -1,    -1,    -1,
     -1,    -1,    -1,  8516,  8517,    -1,  8518,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  8519,    -1,
     -1,    -1,  8520,  8521,  8522,    -1,  8523,    -1,
     -1,    -1,  8524,    -1,    -1,    -1,    -1,  8525,
     -1,  8526,    -1,  8527,  8528,  8529,  8531,    -1,
     -1,    -1,  8532,  8533,  8534,    -1,    -1,    -1,
   8535,    -1,    -1,  8536,  8537,  8539,  8541,  8542,
   8545,  8546,  8548,    -1,    -1,    -1,    -1,  8549,
   8550,  8551,  8552,  8553,  8554,  8555,  8558,  8561,
   8562,    -1,  8563,  8564,  8567,    -1,  8568,  8569,
   8570,    -1,  8571,  8572,  8573,  8574,    -1,  8575,
   8576,  8577,  8578,  8579,  8580,  8581,    -1,  8582,
   8583,  8584,  8586,  8587,    -1,  8588,  8590,    -1,
     -1,  8592,  8593,  8594,  8596,  8598,  8599,    -1,
     -1,  8600,  8601,  8602,  8604,    -1,  8605,    -1,
   8606,  8607,  8610,  8612,  8614,    -1,  8616,  8617,
     -1,    -1,  8618,  8621,  8622,  8623,    -1,    -1,
   8624,    -1,    -1,    -1,  8625,  8627,    -1,  8628,
     -1,    -1,  8630,  8631,  8632,  8633,  8634,    -1,
  /* 0x8d00 */
     -1,  8635,    -1,    -1,  8637,  8638,    -1,  8639,
   8640,    -1,  8641,  8643,    -1,  8646,    -1,  8647,
   8648,    -1,  8649,  8651,  8653,    -1,  8654,  8655,
     -1,    -1,    -1,  8658,  8659,  8660,  8662,  8663,
   8664,  8667,  8668,  8670,  8671,  8673,  8675,  8676,
   8677,  8679,  8680,  8681,  8682,  8683,  8684,  8685,
   8686,  8689,  8691,  8692,  8693,  8694,  8695,  8696,
   8697,  8698,  8699,  8700,  8701,  8702,  8703,  8704,
   8705,  8706,  8707,  8708,  8711,  8712,  8713,  8714,
   8715,  8716,  8717,  8719,  8720,  8721,  8724,  8725,
   8726,  8727,  8728,  8729,  8730,  8731,  8732,  8734,
   8735,  8736,  8737,  8738,  8739,  8740,  8743,  8747,
   8748,  8749,  8750,  8751,  8752,    -1,    -1,    -1,
     -1,    -1,  8754,    -1,  8755,    -1,    -1,    -1,
   8756,  8757,    -1,    -1,    -1,  8758,  8759,    -1,
     -1,    -1,    -1,  8760,    -1,    -1,    -1,  8765,
     -1,  8766,  8767,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  8768,    -1,    -1,    -1,    -1,
     -1,  8769,    -1,    -1,    -1,  8770,    -1,    -1,
     -1,  8771,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  8772,    -1,
   8773,    -1,    -1,    -1,  8774,    -1,    -1,  8775,
     -1,  8777,  8778,    -1,    -1,    -1,    -1,    -1,
   8779,    -1,    -1,  8780,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  8781,  8783,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  8784,    -1,
     -1,  8785,    -1,    -1,    -1,  8786,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  8787,  8788,  8789,  8790,
   8791,  8792,  8793,  8794,  8795,    -1,    -1,    -1,
  /* 0x8e00 */
     -1,  8796,    -1,    -1,    -1,    -1,    -1,    -1,
   8798,    -1,  8800,  8801,  8803,    -1,    -1,    -1,
   8804,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   8805,  8806,  8807,  8808,  8809,    -1,    -1,  8810,
   8811,    -1,    -1,    -1,  8812,    -1,    -1,    -1,
   8813,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  8814,    -1,    -1,    -1,
     -1,    -1,    -1,  8815,  8817,    -1,    -1,  8818,
     -1,  8819,  8820,    -1,    -1,  8821,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  8822,
     -1,    -1,    -1,  8823,  8824,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   8826,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  8828,  8830,    -1,    -1,    -1,    -1,  8832,
     -1,    -1,  8833,  8834,  8835,    -1,    -1,  8836,
     -1,  8837,  8838,  8841,    -1,  8843,    -1,  8845,
     -1,  8846,  8847,  8848,    -1,  8849,    -1,    -1,
     -1,  8851,  8852,    -1,  8853,    -1,    -1,    -1,
     -1,  8854,  8855,    -1,    -1,  8857,  8858,    -1,
     -1,    -1,  8859,    -1,  8861,  8862,    -1,  8863,
   8864,  8868,  8869,  8870,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   8871,    -1,    -1,    -1,  8872,    -1,  8875,    -1,
     -1,    -1,  8879,  8880,  8881,  8882,    -1,    -1,
     -1,  8883,  8884,    -1,  8885,    -1,    -1,    -1,
     -1,    -1,    -1,  8886,    -1,    -1,    -1,  8889,
     -1,    -1,  8890,  8892,  8894,    -1,    -1,    -1,
     -1,    -1,    -1,  8895,    -1,    -1,    -1,    -1,
   8896,    -1,  8898,    -1,    -1,    -1,  8899,    -1,
   8902,  8903,  8904,  8905,  8906,  8907,  8909,    -1,
  /* 0x8f00 */
   8910,    -1,    -1,  8911,    -1,  8912,    -1,  8913,
   8914,  8915,  8916,    -1,  8917,    -1,    -1,  8919,
     -1,    -1,  8920,  8922,  8923,  8925,    -1,    -1,
     -1,  8927,    -1,  8929,  8931,  8933,  8934,  8935,
     -1,  8936,    -1,    -1,    -1,  8937,  8938,    -1,
     -1,  8939,  8941,    -1,  8942,  8943,    -1,  8944,
     -1,    -1,    -1,  8945,    -1,    -1,  8946,    -1,
   8947,    -1,  8948,  8950,  8951,    -1,  8952,  8953,
     -1,  8954,  8957,    -1,  8958,  8960,  8961,    -1,
     -1,  8962,    -1,    -1,    -1,  8964,  8965,    -1,
     -1,    -1,    -1,    -1,  8966,    -1,    -1,  8967,
     -1,    -1,    -1,    -1,  8968,    -1,    -1,  8969,
     -1,  8971,  8972,    -1,  8973,    -1,  8974,  8975,
   8976,  8977,  8978,  8979,  8980,  8982,  8985,  8986,
   8988,  8990,  8991,  8992,  8993,  8994,  8995,  8996,
   8997,  8998,  8999,  9000,  9002,  9003,  9004,  9005,
   9006,  9007,  9008,  9009,  9010,  9012,  9013,  9015,
   9016,  9018,  9019,  9020,  9021,  9022,  9023,  9025,
   9026,  9027,  9028,  9029,  9030,  9031,  9032,  9033,
   9034,  9035,  9036,    -1,    -1,  9037,  9040,  9043,
   9044,  9046,    -1,    -1,  9049,    -1,  9052,  9053,
   9055,  9057,    -1,  9060,    -1,  9061,  9064,  9065,
     -1,    -1,  9068,    -1,    -1,  9069,  9070,    -1,
     -1,  9071,  9074,    -1,    -1,  9077,  9078,    -1,
     -1,  9080,  9081,  9082,    -1,    -1,  9083,  9084,
   9085,    -1,  9086,    -1,    -1,    -1,    -1,    -1,
   9088,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   9089,  9090,    -1,  9091,  9092,  9093,  9094,  9095,
     -1,    -1,    -1,    -1,  9098,  9099,    -1,    -1,
     -1,  9100,  9101,    -1,  9102,    -1,    -1,  9103,
     -1,    -1,    -1,  9104,  9107,    -1,    -1,    -1,
     -1,  9113,  9115,    -1,    -1,    -1,    -1,    -1,
  /* 0x9000 */
     -1,    -1,  9117,  9118,    -1,    -1,    -1,    -1,
   9119,  9120,  9121,    -1,    -1,    -1,  9122,    -1,
     -1,    -1,  9123,  9125,    -1,  9127,    -1,    -1,
     -1,  9130,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  9131,    -1,  9132,  9138,    -1,
     -1,  9139,    -1,    -1,    -1,    -1,    -1,    -1,
   9140,  9142,  9143,    -1,    -1,    -1,    -1,    -1,
     -1,  9144,  9146,  9147,  9148,    -1,    -1,    -1,
     -1,  9149,    -1,    -1,    -1,  9150,    -1,    -1,
     -1,  9152,  9154,  9156,    -1,    -1,  9157,    -1,
     -1,    -1,  9158,  9159,  9162,  9164,    -1,  9165,
     -1,  9166,    -1,    -1,  9167,    -1,  9168,    -1,
   9170,  9172,    -1,    -1,    -1,  9173,    -1,    -1,
     -1,  9174,    -1,    -1,    -1,    -1,    -1,  9175,
     -1,    -1,  9176,    -1,    -1,    -1,  9178,  9179,
   9180,    -1,  9181,    -1,  9182,    -1,    -1,    -1,
     -1,  9183,    -1,    -1,  9184,    -1,    -1,  9185,
     -1,  9186,  9189,    -1,    -1,    -1,    -1,  9192,
   9193,  9194,    -1,  9195,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  9196,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  9197,    -1,
   9198,    -1,    -1,  9199,  9200,    -1,  9201,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,  9202,  9203,  9204,    -1,  9207,    -1,    -1,
     -1,  9208,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  9211,    -1,  9212,  9214,
   9215,  9216,  9217,  9219,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  9220,  9222,
     -1,    -1,    -1,    -1,    -1,    -1,  9223,  9224,
   9225,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  9226,  9227,  9228,
   9232,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x9100 */
     -1,    -1,    -1,    -1,    -1,    -1,  9233,    -1,
     -1,  9234,  9238,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,  9242,    -1,  9243,  9244,  9248,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,  9249,
     -1,    -1,    -1,    -1,    -1,  9250,    -1,    -1,
   9251,    -1,  9254,    -1,  9255,    -1,  9256,  9257,
     -1,    -1,  9258,    -1,  9259,  9260,    -1,    -1,
     -1,    -1,  9263,    -1,    -1,    -1,  9264,  9265,
   9267,  9268,    -1,  9269,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,  9270,    -1,    -1,    -1,
     -1,  9271,    -1,    -1,    -1,  9272,    -1,    -1,
     -1,    -1,  9274,    -1,    -1,    -1,  9275,  9278,
     -1,    -1,  9280,    -1,  9281,    -1,    -1,    -1,
     -1,  9283,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  9285,  9286,  9287,
     -1,    -1,    -1,  9289,    -1,    -1,    -1,  9290,
     -1,  9291,  9292,  9293,    -1,    -1,  9294,    -1,
     -1,    -1,    -1,    -1,  9296,  9297,  9298,  9300,
   9303,    -1,    -1,    -1,  9304,    -1,  9306,    -1,
     -1,    -1,    -1,    -1,  9308,    -1,    -1,    -1,
     -1,    -1,    -1,  9310,  9312,    -1,    -1,    -1,
     -1,  9314,    -1,    -1,    -1,    -1,    -1,    -1,
   9317,    -1,    -1,  9319,  9321,    -1,    -1,    -1,
   9323,  9325,    -1,  9326,    -1,  9327,    -1,  9328,
   9330,    -1,  9332,  9334,  9336,    -1,  9338,    -1,
   9340,  9341,  9343,  9345,  9348,  9349,  9350,  9352,
   9353,  9354,  9355,    -1,  9357,  9358,    -1,    -1,
   9359,  9361,    -1,  9362,  9364,    -1,  9365,  9366,
     -1,  9367,    -1,    -1,    -1,    -1,    -1,  9368,
   9369,    -1,    -1,    -1,    -1,  9377,    -1,  9378,
     -1,  9379,  9380,  9381,  9384,    -1,    -1,    -1,
  /* 0x9200 */
   9392,  9393,    -1,  9394,  9395,  9396,  9397,    -1,
   9399,  9400,    -1,    -1,    -1,  9401,  9402,    -1,
   9404,  9405,  9406,    -1,  9407,  9408,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,  9409,  9411,
     -1,  9412,    -1,  9413,    -1,  9414,  9415,  9416,
   9417,  9418,    -1,    -1,  9420,    -1,  9422,    -1,
   9424,    -1,    -1,  9425,  9426,    -1,    -1,  9427,
   9428,  9429,  9430,    -1,    -1,  9431,  9432,  9433,
   9434,    -1,    -1,    -1,  9435,  9440,  9441,  9442,
   9443,  9444,    -1,  9445,    -1,  9447,    -1,    -1,
     -1,  9448,    -1,    -1,    -1,  9449,    -1,  9450,
     -1,    -1,  9451,  9452,    -1,    -1,  9454,    -1,
   9455,    -1,  9456,    -1,  9459,    -1,  9461,    -1,
   9462,    -1,    -1,    -1,  9464,  9465,    -1,    -1,
     -1,  9466,    -1,    -1,  9471,    -1,  9475,    -1,
   9476,    -1,  9477,  9478,    -1,    -1,    -1,  9479,
   9480,    -1,    -1,  9481,    -1,  9482,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,  9483,    -1,    -1,
     -1,  9484,    -1,  9485,    -1,  9486,  9490,    -1,
   9491,    -1,  9492,  9494,  9496,    -1,  9499,    -1,
   9500,    -1,    -1,  9501,    -1,  9502,  9503,    -1,
   9504,  9505,  9506,  9507,  9508,  9509,  9511,    -1,
     -1,  9512,  9513,  9514,    -1,    -1,    -1,  9516,
     -1,  9518,    -1,  9519,  9520,  9521,    -1,    -1,
     -1,  9522,    -1,  9523,    -1,  9524,    -1,  9525,
     -1,    -1,    -1,    -1,  9526,    -1,    -1,  9527,
     -1,    -1,  9528,    -1,    -1,    -1,    -1,    -1,
     -1,  9529,    -1,  9530,    -1,  9535,    -1,  9536,
     -1,    -1,    -1,  9538,  9540,  9541,  9542,    -1,
   9543,  9548,  9550,    -1,    -1,  9553,  9555,  9556,
   9557,  9558,    -1,  9559,    -1,    -1,  9561,    -1,
   9562,    -1,    -1,    -1,  9563,    -1,    -1,    -1,
  /* 0x9300 */
     -1,  9564,    -1,    -1,  9565,    -1,  9567,  9568,
   9569,    -1,    -1,    -1,    -1,    -1,    -1,  9570,
   9572,    -1,  9573,    -1,    -1,  9574,    -1,    -1,
   9575,  9576,  9578,  9579,    -1,    -1,    -1,  9580,
   9581,  9582,  9583,    -1,    -1,    -1,  9585,    -1,
   9586,  9587,    -1,  9588,  9589,    -1,  9592,  9593,
     -1,    -1,  9594,  9596,    -1,    -1,  9597,  9598,
   9599,    -1,    -1,    -1,    -1,    -1,  9600,    -1,
   9601,  9602,    -1,  9603,    -1,    -1,  9604,  9605,
   9606,    -1,  9608,  9611,    -1,  9612,    -1,    -1,
     -1,    -1,    -1,    -1,  9613,    -1,    -1,    -1,
   9614,    -1,  9615,  9616,    -1,    -1,  9618,    -1,
   9619,    -1,    -1,    -1,  9621,  9622,    -1,    -1,
     -1,  9623,    -1,    -1,  9624,    -1,    -1,  9625,
   9627,    -1,    -1,  9628,    -1,  9632,  9633,    -1,
     -1,    -1,  9634,    -1,    -1,    -1,  9635,  9637,
     -1,  9638,  9639,    -1,  9640,    -1,    -1,  9641,
     -1,    -1,  9642,  9643,  9644,    -1,    -1,    -1,
     -1,    -1,  9646,    -1,  9648,    -1,  9650,  9652,
   9653,    -1,  9654,  9655,    -1,    -1,    -1,    -1,
     -1,  9656,  9658,  9659,    -1,    -1,  9660,  9662,
     -1,  9663,  9664,    -1,  9665,  9666,  9668,    -1,
   9670,    -1,    -1,  9671,    -1,  9672,    -1,    -1,
   9673,    -1,  9674,    -1,    -1,    -1,    -1,  9675,
     -1,  9676,    -1,  9677,    -1,    -1,    -1,  9678,
   9679,    -1,    -1,    -1,  9680,  9681,    -1,    -1,
   9682,  9683,    -1,  9684,    -1,    -1,    -1,  9685,
   9686,    -1,    -1,    -1,  9687,  9688,  9689,  9690,
     -1,  9694,  9696,    -1,  9697,  9698,    -1,    -1,
   9700,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
   9701,    -1,    -1,    -1,    -1,  9702,    -1,  9703,
     -1,  9704,    -1,    -1,    -1,  9705,    -1,    -1,
  /* 0x9400 */
     -1,    -1,  9707,  9709,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,  9710,    -1,    -1,    -1,    -1,
   9711,    -1,  9712,  9713,  9714,    -1,    -1,  9715,
   9716,  9717,  9718,    -1,    -1,  9720,    -1,    -1,
   9722,  9723,    -1,    -1,    -1,    -1,  9728,  9729,
   9730,    -1,    -1,  9731,    -1,    -1,  9732,    -1,
     -1,    -1,  9734,  9735,    -1,  9736,  9741,    -1,
   9742,    -1,  9744,    -1,    -1,    -1,    -1,  9745,
     -1,    -1,    -1,  9746,  9748,  9750,    -1,    -1,
     -1,    -1,  9751,    -1,  9752,    -1,    -1,    -1,
     -1,  9753,  9757,  9761,  9762,  9763,    -1,    -1,
     -1,    -1,  9764,  9766,  9771,    -1,  9773,    -1,
   9774,    -1,    -1,  9775,  9776,  9777,    -1,    -1,
     -1,    -1,  9778,    -1,  9780,  9784,    -1,  9785,
   9786,  9787,  9788,    -1,    -1,  9789,    -1,  9791,
     -1,  9792,    -1,    -1,  9793,  9794,  9796,  9797,
     -1,  9798,  9800,    -1,    -1,  9801,  9803,  9806,
   9807,  9808,  9809,  9810,  9811,  9812,  9813,  9814,
   9815,  9816,  9817,  9818,  9819,  9820,  9821,  9822,
   9823,  9824,  9825,  9826,  9827,  9828,  9829,  9830,
   9834,  9835,  9836,  9837,  9838,  9839,  9841,  9842,
   9843,  9844,  9846,  9847,  9848,  9849,  9850,  9851,
   9852,  9853,  9855,  9856,  9857,  9858,  9861,  9862,
   9863,  9864,  9865,  9866,  9869,  9870,  9871,  9872,
   9873,  9874,  9877,  9878,  9879,  9880,  9882,  9883,
   9885,  9886,  9887,  9889,  9890,  9892,  9893,  9895,
   9896,  9897,  9898,  9899,  9900,  9902,  9903,  9904,
   9905,  9906,  9907,  9908,  9909,  9910,  9911,  9912,
   9913,  9914,  9915,  9916,  9917,  9918,  9919,  9920,
   9921,  9922,  9923,  9924,  9926,  9927,  9928,  9929,
   9930,  9931,  9932,  9936,  9937,  9938,  9939,  9940,
   9941,  9943,  9944,  9947,  9948,  9949,  9950,  9951,
  /* 0x9500 */
   9953,  9954,  9955,  9956,  9957,  9959,  9960,  9961,
   9962,  9965,  9966,  9967,  9968,  9969,  9970,  9971,
   9973,  9975,  9976,  9977,  9978,  9979,  9980,  9981,
   9982,  9983,  9984,  9985,  9986,  9987,  9988,  9989,
   9990,  9991,  9992,  9993,  9994,  9996,  9997,  9998,
   9999, 10000, 10001, 10003, 10004, 10005, 10006, 10007,
  10008, 10009, 10010, 10011, 10012, 10013, 10014, 10015,
  10016, 10017, 10018, 10020, 10021, 10022, 10023, 10024,
  10025, 10026, 10027, 10028, 10029, 10030, 10031, 10032,
  10034, 10035, 10036, 10037, 10038, 10040, 10041, 10042,
  10044, 10045, 10046, 10047, 10048,    -1, 10049, 10050,
  10051, 10052, 10053, 10054, 10055, 10056, 10057, 10058,
  10059, 10060, 10061, 10063, 10064, 10065, 10066, 10067,
  10068, 10069, 10070, 10071, 10072, 10073, 10074, 10075,
  10076, 10078, 10079, 10080, 10081, 10082, 10083, 10084,
  10088, 10092,    -1,    -1,    -1,    -1,    -1, 10094,
  10098,    -1, 10099, 10100,    -1,    -1, 10101, 10102,
  10104, 10105,    -1, 10107, 10108,    -1, 10109, 10110,
     -1, 10112, 10114, 10116, 10117,    -1,    -1,    -1,
  10118, 10119,    -1,    -1,    -1,    -1,    -1,    -1,
  10121, 10123, 10124, 10127, 10128, 10129,    -1, 10130,
  10131, 10132,    -1, 10133, 10134, 10135,    -1,    -1,
     -1, 10136, 10138,    -1, 10140,    -1, 10142,    -1,
     -1, 10143,    -1, 10145, 10146, 10147, 10148, 10149,
     -1,    -1,    -1, 10150,    -1,    -1, 10152, 10153,
  10154,    -1, 10155, 10157, 10158, 10159,    -1,    -1,
  10160,    -1, 10161, 10162, 10163, 10164, 10165, 10166,
  10169, 10174,    -1,    -1, 10180,    -1, 10183,    -1,
  10184, 10185, 10186,    -1, 10187, 10188,    -1,    -1,
  10189, 10190, 10191, 10192, 10193, 10194, 10196, 10197,
  10198, 10200, 10201, 10203, 10204, 10205, 10206, 10207,
  10208, 10209, 10211, 10212, 10213, 10214, 10215, 10216,
  /* 0x9600 */
  10217, 10218, 10219, 10220, 10221, 10222, 10224, 10225,
  10226, 10227, 10229, 10230, 10231, 10232, 10233, 10234,
  10235, 10236, 10237, 10239, 10240, 10242, 10243, 10244,
  10245, 10246, 10247, 10249, 10250, 10251,    -1, 10253,
     -1,    -1,    -1,    -1,    -1, 10254, 10258,    -1,
  10260, 10261, 10263, 10265, 10267,    -1,    -1, 10268,
     -1, 10269,    -1, 10270, 10272, 10276, 10277,    -1,
  10279,    -1,    -1, 10280,    -1,    -1,    -1,    -1,
  10281, 10283,    -1,    -1,    -1, 10285, 10286, 10287,
  10288, 10289,    -1,    -1,    -1, 10290,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 10291, 10294,    -1,
  10295,    -1,    -1,    -1, 10296, 10298, 10299, 10301,
     -1,    -1,    -1, 10302,    -1, 10304, 10305,    -1,
  10312, 10313,    -1,    -1,    -1,    -1,    -1,    -1,
  10316,    -1,    -1, 10320,    -1,    -1,    -1, 10321,
  10322,    -1, 10323, 10326,    -1, 10328,    -1, 10330,
     -1, 10332, 10334,    -1, 10338,    -1,    -1,    -1,
     -1,    -1, 10340,    -1, 10341,    -1, 10342, 10344,
  10345,    -1,    -1,    -1,    -1, 10348, 10350,    -1,
     -1, 10351,    -1, 10352,    -1, 10353,    -1, 10360,
  10361,    -1,    -1, 10364,    -1,    -1,    -1,    -1,
  10367,    -1, 10368, 10371,    -1,    -1, 10373, 10374,
     -1, 10381, 10384,    -1, 10385,    -1, 10386, 10388,
  10390,    -1, 10392, 10399,    -1, 10400, 10401,    -1,
     -1, 10402,    -1,    -1,    -1,    -1,    -1, 10404,
     -1,    -1,    -1, 10406,    -1, 10407,    -1, 10408,
     -1, 10409,    -1,    -1,    -1, 10411, 10414,    -1,
     -1, 10415, 10416, 10417, 10419, 10421, 10422, 10425,
  10426,    -1, 10428, 10429,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  10430,    -1, 10431, 10432,    -1,    -1, 10433, 10434,
     -1,    -1, 10435, 10437,    -1,    -1, 10438,    -1,
  /* 0x9700 */
     -1, 10440,    -1,    -1,    -1,    -1,    -1,    -1,
     -1, 10441, 10442,    -1,    -1,    -1,    -1,    -1,
     -1, 10445,    -1,    -1,    -1,    -1,    -1, 10446,
     -1,    -1, 10447, 10450,    -1,    -1,    -1,    -1,
     -1, 10453, 10454,    -1,    -1,    -1,    -1, 10455,
     -1,    -1,    -1,    -1,    -1, 10458,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  10459,    -1,    -1, 10460,    -1, 10464,    -1,    -1,
     -1, 10465, 10466,    -1, 10467,    -1, 10468,    -1,
  10469, 10472, 10473,    -1,    -1, 10477, 10484, 10491,
     -1, 10498, 10500, 10502, 10503,    -1,    -1,    -1,
     -1, 10506, 10507,    -1, 10508, 10509,    -1,    -1,
     -1,    -1, 10512, 10518, 10519, 10521,    -1, 10522,
  10523,    -1,    -1, 10524,    -1, 10526,    -1,    -1,
     -1, 10529,    -1,    -1, 10532,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 10533,    -1,    -1,
  10534,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1, 10536,    -1, 10538, 10539, 10540,    -1, 10541,
     -1, 10542, 10543,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 10544,
     -1,    -1,    -1,    -1,    -1,    -1, 10545, 10547,
  10549,    -1,    -1,    -1,    -1,    -1,    -1, 10550,
     -1, 10551, 10553,    -1,    -1, 10554,    -1,    -1,
     -1, 10555,    -1,    -1,    -1, 10556, 10557,    -1,
     -1, 10558,    -1, 10560,    -1,    -1, 10561,    -1,
  10562, 10564,    -1, 10565, 10566, 10567,    -1,    -1,
     -1,    -1,    -1, 10569,    -1,    -1,    -1,    -1,
     -1, 10570,    -1,    -1, 10571, 10574, 10575,    -1,
     -1,    -1,    -1,    -1, 10576,    -1, 10578, 10579,
  10582, 10584, 10585, 10586, 10587, 10589, 10590, 10591,
     -1, 10592, 10593,    -1,    -1, 10595,    -1,    -1,
     -1,    -1,    -1, 10596,    -1,    -1,    -1, 10597,
  /* 0x9800 */
     -1, 10598, 10599, 10600,    -1, 10601, 10602, 10603,
  10604,    -1, 10605,    -1, 10606,    -1, 10607, 10608,
  10609, 10610, 10611, 10612,    -1,    -1,    -1, 10613,
  10614,    -1, 10615,    -1, 10617,    -1,    -1,    -1,
     -1, 10618,    -1,    -1, 10619, 10621, 10623,    -1,
     -1,    -1,    -1,    -1, 10624, 10626, 10627,    -1,
  10629,    -1, 10631,    -1, 10632,    -1,    -1, 10634,
  10635, 10637, 10641, 10645, 10646, 10648, 10652,    -1,
     -1,    -1,    -1,    -1,    -1,    -1, 10653,    -1,
     -1,    -1,    -1, 10654, 10655, 10656, 10657, 10658,
     -1,    -1, 10660, 10661, 10662, 10664, 10666,    -1,
  10667, 10668, 10669, 10671,    -1,    -1, 10673,    -1,
     -1,    -1, 10674,    -1,    -1, 10675,    -1, 10676,
     -1,    -1,    -1, 10677, 10678,    -1,    -1, 10679,
  10681, 10682,    -1, 10685, 10686, 10687, 10688, 10689,
  10690, 10691, 10692, 10693, 10695, 10696, 10697, 10698,
  10699, 10700, 10701, 10702, 10703, 10704, 10706, 10707,
  10708, 10710, 10711, 10713, 10714, 10715, 10716, 10717,
  10718, 10720, 10721, 10722, 10726,    -1, 10727, 10729,
  10730, 10731, 10732, 10733, 10734, 10736, 10737, 10738,
  10739, 10741, 10742, 10743, 10744, 10745, 10746, 10747,
  10748,    -1,    -1,    -1,    -1, 10756, 10757, 10761,
     -1, 10762,    -1, 10763,    -1,    -1, 10764, 10765,
  10768,    -1, 10769, 10770, 10771,    -1,    -1,    -1,
  10772,    -1,    -1, 10773, 10775,    -1, 10777, 10782,
  10783,    -1,    -1,    -1, 10787,    -1, 10792, 10798,
  10799, 10800, 10801, 10802, 10803, 10804, 10805, 10806,
  10807, 10809, 10810, 10811, 10812,    -1, 10813, 10814,
  10817, 10820, 10821, 10822,    -1, 10823,    -1,    -1,
  10824, 10825, 10826, 10827, 10829, 10830, 10831, 10833,
  10835,    -1, 10836,    -1, 10838,    -1, 10839,    -1,
     -1,    -1,    -1,    -1, 10840, 10841, 10842, 10843,
  /* 0x9900 */
     -1,    -1,    -1, 10844, 10845, 10846,    -1,    -1,
     -1, 10848, 10851,    -1, 10854, 10855, 10856, 10857,
  10858, 10859, 10860, 10862,    -1, 10863, 10864,    -1,
  10865,    -1, 10867, 10868, 10869,    -1, 10870, 10871,
  10872, 10874,    -1,    -1,    -1,    -1,    -1, 10875,
  10879,    -1,    -1,    -1, 10881,    -1,    -1,    -1,
     -1, 10882,    -1, 10883, 10884, 10885, 10887, 10888,
     -1, 10889, 10890, 10891, 10892, 10893, 10894, 10895,
     -1, 10896,    -1, 10897,    -1, 10898,    -1,    -1,
  10899, 10900, 10901, 10902, 10903, 10904, 10905,    -1,
     -1, 10906, 10907,    -1,    -1,    -1,    -1, 10908,
     -1, 10909,    -1,    -1, 10910,    -1, 10911, 10912,
     -1,    -1, 10915, 10916, 10919, 10920, 10922, 10923,
  10925, 10926, 10927, 10928, 10929, 10930, 10931, 10933,
  10934, 10935, 10936, 10937, 10938, 10939, 10940, 10941,
  10943, 10944, 10945, 10946, 10947, 10949, 10950, 10951,
  10952, 10954, 10956, 10957, 10958, 10959, 10960, 10961,
  10962, 10964, 10965, 10966, 10967, 10968, 10969, 10970,
  10971, 10972, 10973, 10974, 10975, 10976,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 10977, 10978, 10979,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1, 10980, 10981, 10982,    -1,
     -1, 10983, 10985, 10986, 10987,    -1,    -1,    -1,
     -1, 10988,    -1,    -1,    -1, 10989,    -1, 10990,
     -1, 10992,    -1,    -1, 10993, 10995, 10997,    -1,
  11001,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  11005, 11006, 11007,    -1, 11008, 11009,    -1,    -1,
  11010, 11011,    -1, 11012,    -1, 11013, 11015, 11017,
     -1, 11018, 11021,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 11022, 11023,    -1,
  11024, 11025,    -1,    -1,    -1, 11026,    -1,    -1,
  11028,    -1,    -1,    -1,    -1,    -1,    -1, 11029,
  /* 0x9a00 */
     -1, 11030, 11031,    -1,    -1, 11032,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 11033, 11034, 11035,
  11036,    -1, 11039, 11041,    -1,    -1, 11044,    -1,
     -1, 11045,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1, 11046,    -1,    -1,    -1,
  11047,    -1,    -1, 11048,    -1, 11049, 11050,    -1,
  11053,    -1,    -1,    -1,    -1,    -1, 11054, 11055,
  11057,    -1, 11058,    -1,    -1,    -1, 11059,    -1,
  11060, 11061, 11062, 11063, 11064, 11065,    -1, 11069,
     -1,    -1, 11070,    -1, 11071, 11072,    -1, 11073,
     -1, 11074, 11077,    -1,    -1, 11078,    -1, 11079,
     -1,    -1, 11082, 11083,    -1, 11085,    -1, 11086,
     -1,    -1, 11087,    -1, 11089, 11090, 11092,    -1,
     -1, 11093, 11094, 11095, 11096, 11097, 11098, 11100,
  11101, 11102, 11106, 11107, 11109, 11111, 11112, 11113,
  11114, 11115, 11116, 11117, 11118, 11120, 11121, 11122,
  11124, 11125, 11126, 11129, 11130, 11131, 11132, 11133,
  11134, 11135, 11136, 11137, 11138, 11141, 11142, 11143,
  11144, 11145, 11146, 11147,    -1, 11148, 11149, 11150,
  11151, 11153, 11154, 11156, 11157, 11158, 11160, 11161,
  11162, 11163, 11164, 11165, 11166, 11167, 11169, 11170,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11171,
     -1,    -1,    -1,    -1,    -1, 11172,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1, 11176,    -1,
     -1,    -1,    -1,    -1, 11177, 11179,    -1,    -1,
  11180,    -1,    -1, 11181, 11182,    -1,    -1, 11183,
     -1,    -1, 11184, 11185, 11187, 11191, 11193, 11194,
  11197, 11198,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1, 11199, 11201, 11202,    -1,    -1,
     -1, 11203, 11205,    -1,    -1, 11207, 11208, 11210,
     -1,    -1,    -1,    -1, 11211,    -1,    -1,    -1,
     -1, 11213,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x9b00 */
  11214,    -1, 11215,    -1,    -1,    -1, 11218,    -1,
     -1,    -1,    -1,    -1,    -1, 11219,    -1,    -1,
     -1,    -1,    -1, 11220,    -1,    -1,    -1,    -1,
     -1,    -1, 11222, 11223,    -1,    -1,    -1,    -1,
     -1,    -1, 11224, 11227,    -1, 11228, 11233, 11238,
  11240, 11241, 11242, 11247,    -1, 11248, 11253,    -1,
  11254, 11257,    -1,    -1,    -1,    -1, 11260,    -1,
     -1, 11261,    -1, 11262,    -1,    -1,    -1,    -1,
  11264,    -1,    -1,    -1,    -1,    -1,    -1, 11265,
     -1, 11266,    -1,    -1,    -1,    -1, 11267,    -1,
  11269,    -1,    -1,    -1,    -1,    -1,    -1, 11274,
  11276,    -1, 11277, 11278,    -1,    -1,    -1,    -1,
     -1,    -1, 11279, 11280,    -1,    -1, 11281,    -1,
  11282,    -1,    -1,    -1,    -1, 11283,    -1, 11284,
     -1,    -1, 11285, 11287, 11288,    -1,    -1, 11289,
     -1,    -1, 11290,    -1,    -1,    -1,    -1,    -1,
     -1, 11291,    -1, 11292,    -1,    -1,    -1,    -1,
     -1, 11293, 11295, 11296,    -1, 11297, 11298, 11299,
  11300, 11301, 11302, 11303,    -1,    -1,    -1,    -1,
     -1,    -1, 11304,    -1, 11305, 11306, 11309,    -1,
     -1,    -1,    -1,    -1,    -1,    -1, 11310, 11311,
     -1,    -1, 11313, 11314,    -1, 11315, 11316,    -1,
     -1,    -1,    -1, 11321,    -1,    -1, 11322, 11323,
     -1,    -1, 11325, 11326,    -1,    -1, 11327,    -1,
  11328, 11329,    -1,    -1,    -1,    -1,    -1, 11330,
     -1, 11332, 11333, 11335,    -1,    -1,    -1,    -1,
     -1,    -1, 11336,    -1, 11337, 11338, 11339, 11340,
  11342,    -1,    -1, 11343,    -1, 11345,    -1,    -1,
     -1, 11346, 11347,    -1, 11348,    -1,    -1, 11349,
  11350,    -1, 11351, 11352,    -1,    -1,    -1,    -1,
  11353,    -1,    -1,    -1, 11354, 11355,    -1, 11356,
     -1, 11359,    -1,    -1,    -1, 11360,    -1, 11361,
  /* 0x9c00 */
  11362, 11364, 11365, 11366,    -1,    -1,    -1,    -1,
  11367, 11369,    -1,    -1, 11370, 11372,    -1, 11374,
  11375,    -1, 11377, 11378,    -1,    -1,    -1,    -1,
     -1,    -1,    -1, 11379, 11381,    -1,    -1, 11382,
  11383,    -1,    -1, 11384, 11385, 11386,    -1,    -1,
  11388, 11390,    -1,    -1,    -1, 11391, 11392,    -1,
     -1, 11393, 11394, 11396,    -1, 11397,    -1, 11398,
     -1, 11399, 11400, 11402, 11403,    -1, 11404,    -1,
     -1,    -1, 11405,    -1,    -1, 11406,    -1,    -1,
  11407, 11408,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1, 11411, 11412, 11413,    -1, 11414, 11415,
  11416,    -1,    -1,    -1,    -1, 11417, 11418, 11419,
  11420,    -1,    -1, 11421, 11422,    -1,    -1, 11423,
  11424,    -1,    -1,    -1,    -1, 11425, 11426, 11427,
     -1,    -1,    -1,    -1,    -1,    -1, 11428, 11431,
  11433,    -1, 11435, 11436, 11441, 11442, 11443, 11444,
  11445, 11446, 11447,    -1, 11448, 11449, 11450, 11451,
  11452, 11454, 11455, 11456, 11457, 11458, 11460, 11461,
  11462, 11463, 11464, 11465, 11466, 11467, 11468, 11469,
  11470, 11471, 11472, 11473, 11474, 11479, 11480, 11482,
  11483, 11485, 11486, 11487, 11488, 11489, 11490, 11491,
  11492, 11493, 11495, 11496, 11497, 11498, 11499, 11500,
  11501, 11502, 11503, 11504, 11505, 11506, 11507, 11508,
  11510, 11511, 11512, 11513, 11514, 11515, 11516, 11517,
  11518, 11519, 11520, 11521, 11522, 11524, 11526, 11527,
  11528, 11529, 11530, 11531, 11532, 11534, 11535, 11536,
  11537, 11538, 11539, 11540, 11541, 11542, 11544, 11546,
  11547, 11548,    -1, 11549, 11550, 11551, 11553, 11554,
  11555, 11556, 11557, 11558,    -1, 11559,    -1, 11561,
     -1, 11563,    -1, 11564, 11566,    -1,    -1,    -1,
     -1,    -1, 11567, 11568, 11569,    -1, 11570,    -1,
  11571,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  /* 0x9d00 */
     -1,    -1,    -1,    -1,    -1,    -1, 11572, 11573,
  11574, 11576,    -1,    -1,    -1,    -1, 11578,    -1,
     -1,    -1, 11580,    -1,    -1, 11581,    -1,    -1,
     -1,    -1,    -1, 11582,    -1, 11583, 11585, 11586,
     -1,    -1,    -1, 11588,    -1,    -1, 11589,    -1,
  11590,    -1,    -1,    -1, 11591,    -1,    -1, 11593,
  11594,    -1,    -1, 11595, 11596,    -1,    -1,    -1,
     -1,    -1,    -1, 11597, 11598,    -1,    -1, 11599,
     -1,    -1, 11600, 11601, 11602,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  11603, 11604, 11605, 11606,    -1,    -1,    -1,    -1,
     -1, 11607,    -1,    -1, 11608, 11609, 11611,    -1,
  11613, 11614,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1, 11615,    -1, 11616,    -1, 11617, 11618,
  11619,    -1, 11620,    -1,    -1,    -1, 11621, 11623,
     -1,    -1,    -1,    -1,    -1,    -1, 11624,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11626,
     -1, 11628, 11629,    -1,    -1,    -1,    -1, 11630,
     -1,    -1,    -1, 11633,    -1,    -1, 11634,    -1,
  11635,    -1, 11636,    -1,    -1,    -1,    -1,    -1,
     -1, 11637,    -1,    -1, 11638, 11639,    -1,    -1,
     -1, 11640, 11641, 11642, 11644,    -1,    -1, 11645,
     -1,    -1, 11647,    -1, 11648, 11655,    -1,    -1,
     -1, 11656, 11657, 11658, 11659,    -1,    -1, 11660,
  11661, 11662, 11663, 11664, 11665,    -1, 11668,    -1,
  11669, 11670, 11671,    -1,    -1,    -1,    -1, 11672,
     -1,    -1,    -1, 11673,    -1,    -1, 11674, 11675,
     -1, 11677, 11678,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 11679, 11680,    -1,
     -1,    -1,    -1, 11681,    -1,    -1,    -1, 11682,
  11683,    -1, 11684, 11685, 11687,    -1,    -1,    -1,
  11689, 11690, 11691,    -1,    -1, 11693,    -1, 11694,
  /* 0x9e00 */
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11695,
     -1,    -1, 11696,    -1, 11697,    -1, 11698, 11699,
     -1,    -1,    -1,    -1,    -1, 11700, 11703,    -1,
  11710,    -1, 11711, 11712, 11714, 11715, 11716, 11717,
  11719, 11720, 11723, 11724, 11725, 11726, 11728, 11730,
  11731, 11732, 11733, 11734, 11736, 11737, 11738, 11739,
  11740, 11741, 11742, 11743, 11744, 11745, 11746, 11747,
  11748, 11749, 11750, 11751, 11752, 11753, 11754, 11755,
  11756, 11757, 11758, 11759, 11760, 11761, 11763, 11764,
  11766, 11767, 11768, 11769, 11770, 11771, 11772, 11773,
  11774, 11775, 11776, 11777, 11778, 11779, 11780, 11781,
  11782, 11783, 11784, 11786, 11787, 11788, 11789, 11790,
  11791, 11792, 11793, 11794, 11795, 11802, 11803, 11804,
  11805, 11806, 11807, 11808, 11809, 11810,    -1, 11811,
  11812, 11813, 11814, 11815, 11816, 11817,    -1,    -1,
  11819, 11822, 11824, 11825, 11826, 11829, 11831,    -1,
     -1,    -1,    -1, 11832, 11833, 11834,    -1, 11835,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11836,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11837,
     -1,    -1,    -1,    -1,    -1,    -1, 11839,    -1,
     -1,    -1,    -1,    -1, 11840, 11842, 11843,    -1,
     -1, 11844, 11846, 11851, 11856, 11858,    -1, 11859,
     -1,    -1,    -1,    -1, 11863, 11867,    -1,    -1,
  11872, 11874, 11878, 11883, 11886, 11890,    -1,    -1,
     -1,    -1,    -1, 11894, 11895,    -1,    -1,    -1,
     -1, 11897,    -1,    -1, 11898,    -1,    -1, 11899,
     -1, 11900, 11901,    -1,    -1,    -1,    -1,    -1,
  11902, 11903,    -1, 11904,    -1,    -1, 11905,    -1,
     -1, 11907,    -1,    -1, 11908,    -1,    -1,    -1,
  11909, 11910, 11911,    -1,    -1, 11912,    -1,    -1,
     -1, 11913, 11914,    -1, 11915,    -1, 11916, 11917,
     -1, 11918,    -1,    -1,    -1, 11919, 11920, 11921,
  /* 0x9f00 */
     -1,    -1, 11923, 11924,    -1,    -1,    -1, 11925,
  11927, 11930,    -1, 11931, 11932, 11933, 11934,    -1,
     -1, 11935, 11936, 11937,    -1, 11939,    -1, 11940,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
  11942, 11943,    -1,    -1,    -1,    -1, 11944,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1, 11945,    -1,    -1,    -1,
     -1, 11946,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1, 11947,    -1,    -1,
     -1,    -1, 11948, 11952,    -1,    -1, 11954, 11956,
  11958, 11962, 11964, 11966, 11967, 11969,    -1, 11970,
     -1, 11971, 11972,    -1, 11973,    -1,    -1, 11974,
  11975, 11976, 11978, 11980,    -1,    -1, 11981, 11982,
     -1, 11985, 11986,    -1, 11988,    -1,    -1,    -1,
  11989, 11990, 11991,    -1,    -1,    -1, 11992, 11993,
     -1,    -1,    -1,    -1,    -1,    -1,    -1, 11994,
  11996, 11997, 11998, 11999, 12000, 12002, 12003, 12004,
  12005, 12006, 12007, 12008, 12009, 12010, 12013,    -1,
  12014,    -1, 12015,    -1, 12018, 12019,    -1,    -1,
     -1, 12020, 12022, 12023, 12024, 12026, 12029, 12031,
     -1,    -1, 12033,    -1,    -1, 12036,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
     -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
};

