/*
 * Copyright (C) 1999-2001, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 2
 */

static const unsigned short cns11643_2_2uni_page21[7650] = {
  /* 0x21 */
  0x4e42, 0x4e5c, 0x51f5, 0x531a, 0x5382, 0x4e07, 0x4e0c, 0x4e47,
  0x4e8d, 0x56d7, 0x5c6e, 0x5f73, 0x4e0f, 0x5187, 0x4e0e, 0x4e2e,
  0x4e93, 0x4ec2, 0x4ec9, 0x4ec8, 0x5198, 0x52fc, 0x536c, 0x53b9,
  0x5720, 0x5903, 0x592c, 0x5c10, 0x5dff, 0x65e1, 0x6bb3, 0x6bcc,
  0x6c14, 0x723f, 0x4e31, 0x4e3c, 0x4ee8, 0x4edc, 0x4ee9, 0x4ee1,
  0x4edd, 0x4eda, 0x520c, 0x5209, 0x531c, 0x534c, 0x5722, 0x5723,
  0x5917, 0x592f, 0x5b81, 0x5b84, 0x5c12, 0x5c3b, 0x5c74, 0x5c73,
  0x5e04, 0x5e80, 0x5e82, 0x5fc9, 0x6209, 0x6250, 0x6c15, 0x6c36,
  0x6c43, 0x6c3f, 0x6c3b, 0x72ae, 0x72b0, 0x738a, 0x79b8, 0x808a,
  0x961e, 0x4f0e, 0x4f18, 0x4f2c, 0x4ef5, 0x4f14, 0x4ef1, 0x4f00,
  0x4ef7, 0x4f08, 0x4f1d, 0x4f02, 0x4f05, 0x4f22, 0x4f13, 0x4f04,
  0x4ef4, 0x4f12, 0x51b1, 0x5213, 0x5210, 0x52a6,
  /* 0x22 */
  0x5322, 0x531f, 0x534d, 0x538a, 0x5407, 0x56e1, 0x56df, 0x572e,
  0x572a, 0x5734, 0x593c, 0x5980, 0x597c, 0x5985, 0x597b, 0x597e,
  0x5977, 0x597f, 0x5b56, 0x5c15, 0x5c25, 0x5c7c, 0x5c7a, 0x5c7b,
  0x5c7e, 0x5ddf, 0x5e75, 0x5e84, 0x5f02, 0x5f1a, 0x5f74, 0x5fd5,
  0x5fd4, 0x5fcf, 0x625c, 0x625e, 0x6264, 0x6261, 0x6266, 0x6262,
  0x6259, 0x6260, 0x625a, 0x6265, 0x6537, 0x65ef, 0x65ee, 0x673e,
  0x6739, 0x6738, 0x673b, 0x673a, 0x673f, 0x673c, 0x6733, 0x6c18,
  0x6c46, 0x6c52, 0x6c5c, 0x6c4f, 0x6c4a, 0x6c54, 0x6c4b, 0x6c4c,
  0x7071, 0x725e, 0x72b4, 0x72b5, 0x738e, 0x752a, 0x767f, 0x7a75,
  0x7f51, 0x8278, 0x827c, 0x8280, 0x827d, 0x827f, 0x864d, 0x897e,
  0x9099, 0x9097, 0x9098, 0x909b, 0x9094, 0x9622, 0x9624, 0x9620,
  0x9623, 0x4f56, 0x4f3b, 0x4f62, 0x4f49, 0x4f53,
  /* 0x23 */
  0x4f64, 0x4f3e, 0x4f67, 0x4f52, 0x4f5f, 0x4f41, 0x4f58, 0x4f2d,
  0x4f33, 0x4f3f, 0x4f61, 0x518f, 0x51b9, 0x521c, 0x521e, 0x5221,
  0x52ad, 0x52ae, 0x5309, 0x5363, 0x5372, 0x538e, 0x538f, 0x5430,
  0x5437, 0x542a, 0x5454, 0x5445, 0x5419, 0x541c, 0x5425, 0x5418,
  0x543d, 0x544f, 0x5441, 0x5428, 0x5424, 0x5447, 0x56ee, 0x56e7,
  0x56e5, 0x5741, 0x5745, 0x574c, 0x5749, 0x574b, 0x5752, 0x5906,
  0x5940, 0x59a6, 0x5998, 0x59a0, 0x5997, 0x598e, 0x59a2, 0x5990,
  0x598f, 0x59a7, 0x59a1, 0x5b8e, 0x5b92, 0x5c28, 0x5c2a, 0x5c8d,
  0x5c8f, 0x5c88, 0x5c8b, 0x5c89, 0x5c92, 0x5c8a, 0x5c86, 0x5c93,
  0x5c95, 0x5de0, 0x5e0a, 0x5e0e, 0x5e8b, 0x5e89, 0x5e8c, 0x5e88,
  0x5e8d, 0x5f05, 0x5f1d, 0x5f78, 0x5f76, 0x5fd2, 0x5fd1, 0x5fd0,
  0x5fed, 0x5fe8, 0x5fee, 0x5ff3, 0x5fe1, 0x5fe4,
  /* 0x24 */
  0x5fe3, 0x5ffa, 0x5fef, 0x5ff7, 0x5ffb, 0x6000, 0x5ff4, 0x623a,
  0x6283, 0x628c, 0x628e, 0x628f, 0x6294, 0x6287, 0x6271, 0x627b,
  0x627a, 0x6270, 0x6281, 0x6288, 0x6277, 0x627d, 0x6272, 0x6274,
  0x65f0, 0x65f4, 0x65f3, 0x65f2, 0x65f5, 0x6745, 0x6747, 0x6759,
  0x6755, 0x674c, 0x6748, 0x675d, 0x674d, 0x675a, 0x674b, 0x6bd0,
  0x6c19, 0x6c1a, 0x6c78, 0x6c67, 0x6c6b, 0x6c84, 0x6c8b, 0x6c8f,
  0x6c71, 0x6c6f, 0x6c69, 0x6c9a, 0x6c6d, 0x6c87, 0x6c95, 0x6c9c,
  0x6c66, 0x6c73, 0x6c65, 0x6c7b, 0x6c8e, 0x7074, 0x707a, 0x7263,
  0x72bf, 0x72bd, 0x72c3, 0x72c6, 0x72c1, 0x72ba, 0x72c5, 0x7395,
  0x7397, 0x7393, 0x7394, 0x7392, 0x753a, 0x7539, 0x7594, 0x7595,
  0x7681, 0x793d, 0x8034, 0x8095, 0x8099, 0x8090, 0x8092, 0x809c,
  0x8290, 0x828f, 0x8285, 0x828e, 0x8291, 0x8293,
  /* 0x25 */
  0x828a, 0x8283, 0x8284, 0x8c78, 0x8fc9, 0x8fbf, 0x909f, 0x90a1,
  0x90a5, 0x909e, 0x90a7, 0x90a0, 0x9630, 0x9628, 0x962f, 0x962d,
  0x4e33, 0x4f98, 0x4f7c, 0x4f85, 0x4f7d, 0x4f80, 0x4f87, 0x4f76,
  0x4f74, 0x4f89, 0x4f84, 0x4f77, 0x4f4c, 0x4f97, 0x4f6a, 0x4f9a,
  0x4f79, 0x4f81, 0x4f78, 0x4f90, 0x4f9c, 0x4f94, 0x4f9e, 0x4f92,
  0x4f82, 0x4f95, 0x4f6b, 0x4f6e, 0x519e, 0x51bc, 0x51be, 0x5235,
  0x5232, 0x5233, 0x5246, 0x5231, 0x52bc, 0x530a, 0x530b, 0x533c,
  0x5392, 0x5394, 0x5487, 0x547f, 0x5481, 0x5491, 0x5482, 0x5488,
  0x546b, 0x547a, 0x547e, 0x5465, 0x546c, 0x5474, 0x5466, 0x548d,
  0x546f, 0x5461, 0x5460, 0x5498, 0x5463, 0x5467, 0x5464, 0x56f7,
  0x56f9, 0x576f, 0x5772, 0x576d, 0x576b, 0x5771, 0x5770, 0x5776,
  0x5780, 0x5775, 0x577b, 0x5773, 0x5774, 0x5762,
  /* 0x26 */
  0x5768, 0x577d, 0x590c, 0x5945, 0x59b5, 0x59ba, 0x59cf, 0x59ce,
  0x59b2, 0x59cc, 0x59c1, 0x59b6, 0x59bc, 0x59c3, 0x59d6, 0x59b1,
  0x59bd, 0x59c0, 0x59c8, 0x59b4, 0x59c7, 0x5b62, 0x5b65, 0x5b93,
  0x5b95, 0x5c44, 0x5c47, 0x5cae, 0x5ca4, 0x5ca0, 0x5cb5, 0x5caf,
  0x5ca8, 0x5cac, 0x5c9f, 0x5ca3, 0x5cad, 0x5ca2, 0x5caa, 0x5ca7,
  0x5c9d, 0x5ca5, 0x5cb6, 0x5cb0, 0x5ca6, 0x5e17, 0x5e14, 0x5e19,
  0x5f28, 0x5f22, 0x5f23, 0x5f24, 0x5f54, 0x5f82, 0x5f7e, 0x5f7d,
  0x5fde, 0x5fe5, 0x602d, 0x6026, 0x6019, 0x6032, 0x600b, 0x6034,
  0x600a, 0x6017, 0x6033, 0x601a, 0x601e, 0x602c, 0x6022, 0x600d,
  0x6010, 0x602e, 0x6013, 0x6011, 0x600c, 0x6009, 0x601c, 0x6214,
  0x623d, 0x62ad, 0x62b4, 0x62d1, 0x62be, 0x62aa, 0x62b6, 0x62ca,
  0x62ae, 0x62b3, 0x62af, 0x62bb, 0x62a9, 0x62b0,
  /* 0x27 */
  0x62b8, 0x653d, 0x65a8, 0x65bb, 0x6609, 0x65fc, 0x6604, 0x6612,
  0x6608, 0x65fb, 0x6603, 0x660b, 0x660d, 0x6605, 0x65fd, 0x6611,
  0x6610, 0x66f6, 0x670a, 0x6785, 0x676c, 0x678e, 0x6792, 0x6776,
  0x677b, 0x6798, 0x6786, 0x6784, 0x6774, 0x678d, 0x678c, 0x677a,
  0x679f, 0x6791, 0x6799, 0x6783, 0x677d, 0x6781, 0x6778, 0x6779,
  0x6794, 0x6b25, 0x6b80, 0x6b7e, 0x6bde, 0x6c1d, 0x6c93, 0x6cec,
  0x6ceb, 0x6cee, 0x6cd9, 0x6cb6, 0x6cd4, 0x6cad, 0x6ce7, 0x6cb7,
  0x6cd0, 0x6cc2, 0x6cba, 0x6cc3, 0x6cc6, 0x6ced, 0x6cf2, 0x6cd2,
  0x6cdd, 0x6cb4, 0x6c8a, 0x6c9d, 0x6c80, 0x6cde, 0x6cc0, 0x6d30,
  0x6ccd, 0x6cc7, 0x6cb0, 0x6cf9, 0x6ccf, 0x6ce9, 0x6cd1, 0x7094,
  0x7098, 0x7085, 0x7093, 0x7086, 0x7084, 0x7091, 0x7096, 0x7082,
  0x709a, 0x7083, 0x726a, 0x72d6, 0x72cb, 0x72d8,
  /* 0x28 */
  0x72c9, 0x72dc, 0x72d2, 0x72d4, 0x72da, 0x72cc, 0x72d1, 0x73a4,
  0x73a1, 0x73ad, 0x73a6, 0x73a2, 0x73a0, 0x73ac, 0x739d, 0x74dd,
  0x74e8, 0x753f, 0x7540, 0x753e, 0x758c, 0x7598, 0x76af, 0x76f3,
  0x76f1, 0x76f0, 0x76f5, 0x77f8, 0x77fc, 0x77f9, 0x77fb, 0x77fa,
  0x77f7, 0x7942, 0x793f, 0x79c5, 0x7a78, 0x7a7b, 0x7afb, 0x7c75,
  0x7cfd, 0x8035, 0x808f, 0x80ae, 0x80a3, 0x80b8, 0x80b5, 0x80ad,
  0x8220, 0x82a0, 0x82c0, 0x82ab, 0x829a, 0x8298, 0x829b, 0x82b5,
  0x82a7, 0x82ae, 0x82bc, 0x829e, 0x82ba, 0x82b4, 0x82a8, 0x82a1,
  0x82a9, 0x82c2, 0x82a4, 0x82c3, 0x82b6, 0x82a2, 0x8670, 0x866f,
  0x866d, 0x866e, 0x8c56, 0x8fd2, 0x8fcb, 0x8fd3, 0x8fcd, 0x8fd6,
  0x8fd5, 0x8fd7, 0x90b2, 0x90b4, 0x90af, 0x90b3, 0x90b0, 0x9639,
  0x963d, 0x963c, 0x963a, 0x9643, 0x4fcd, 0x4fc5,
  /* 0x29 */
  0x4fd3, 0x4fb2, 0x4fc9, 0x4fcb, 0x4fc1, 0x4fd4, 0x4fdc, 0x4fd9,
  0x4fbb, 0x4fb3, 0x4fdb, 0x4fc7, 0x4fd6, 0x4fba, 0x4fc0, 0x4fb9,
  0x4fec, 0x5244, 0x5249, 0x52c0, 0x52c2, 0x533d, 0x537c, 0x5397,
  0x5396, 0x5399, 0x5398, 0x54ba, 0x54a1, 0x54ad, 0x54a5, 0x54cf,
  0x54c3, 0x830d, 0x54b7, 0x54ae, 0x54d6, 0x54b6, 0x54c5, 0x54c6,
  0x54a0, 0x5470, 0x54bc, 0x54a2, 0x54be, 0x5472, 0x54de, 0x54b0,
  0x57b5, 0x579e, 0x579f, 0x57a4, 0x578c, 0x5797, 0x579d, 0x579b,
  0x5794, 0x5798, 0x578f, 0x5799, 0x57a5, 0x579a, 0x5795, 0x58f4,
  0x590d, 0x5953, 0x59e1, 0x59de, 0x59ee, 0x5a00, 0x59f1, 0x59dd,
  0x59fa, 0x59fd, 0x59fc, 0x59f6, 0x59e4, 0x59f2, 0x59f7, 0x59db,
  0x59e9, 0x59f3, 0x59f5, 0x59e0, 0x59fe, 0x59f4, 0x59ed, 0x5ba8,
  0x5c4c, 0x5cd0, 0x5cd8, 0x5ccc, 0x5cd7, 0x5ccb,
  /* 0x2a */
  0x5cdb, 0x5cde, 0x5cda, 0x5cc9, 0x5cc7, 0x5cca, 0x5cd6, 0x5cd3,
  0x5cd4, 0x5ccf, 0x5cc8, 0x5cc6, 0x5cce, 0x5cdf, 0x5cf8, 0x5df9,
  0x5e21, 0x5e22, 0x5e23, 0x5e20, 0x5e24, 0x5eb0, 0x5ea4, 0x5ea2,
  0x5e9b, 0x5ea3, 0x5ea5, 0x5f07, 0x5f2e, 0x5f56, 0x5f86, 0x6037,
  0x6039, 0x6054, 0x6072, 0x605e, 0x6045, 0x6053, 0x6047, 0x6049,
  0x605b, 0x604c, 0x6040, 0x6042, 0x605f, 0x6024, 0x6044, 0x6058,
  0x6066, 0x606e, 0x6242, 0x6243, 0x62cf, 0x630d, 0x630b, 0x62f5,
  0x630e, 0x6303, 0x62eb, 0x62f9, 0x630f, 0x630c, 0x62f8, 0x62f6,
  0x6300, 0x6313, 0x6314, 0x62fa, 0x6315, 0x62fb, 0x62f0, 0x6541,
  0x6543, 0x65aa, 0x65bf, 0x6636, 0x6621, 0x6632, 0x6635, 0x661c,
  0x6626, 0x6622, 0x6633, 0x662b, 0x663a, 0x661d, 0x6634, 0x6639,
  0x662e, 0x670f, 0x6710, 0x67c1, 0x67f2, 0x67c8,
  /* 0x2b */
  0x67ba, 0x67dc, 0x67bb, 0x67f8, 0x67d8, 0x67c0, 0x67b7, 0x67c5,
  0x67eb, 0x67e4, 0x67df, 0x67b5, 0x67cd, 0x67b3, 0x67f7, 0x67f6,
  0x67ee, 0x67e3, 0x67c2, 0x67b9, 0x67ce, 0x67e7, 0x67f0, 0x67b2,
  0x67fc, 0x67c6, 0x67ed, 0x67cc, 0x67ae, 0x67e6, 0x67db, 0x67fa,
  0x67c9, 0x67ca, 0x67c3, 0x67ea, 0x67cb, 0x6b28, 0x6b82, 0x6b84,
  0x6bb6, 0x6bd6, 0x6bd8, 0x6be0, 0x6c20, 0x6c21, 0x6d28, 0x6d34,
  0x6d2d, 0x6d1f, 0x6d3c, 0x6d3f, 0x6d12, 0x6d0a, 0x6cda, 0x6d33,
  0x6d04, 0x6d19, 0x6d3a, 0x6d1a, 0x6d11, 0x6d00, 0x6d1d, 0x6d42,
  0x6d01, 0x6d18, 0x6d37, 0x6d03, 0x6d0f, 0x6d40, 0x6d07, 0x6d20,
  0x6d2c, 0x6d08, 0x6d22, 0x6d09, 0x6d10, 0x70b7, 0x709f, 0x70be,
  0x70b1, 0x70b0, 0x70a1, 0x70b4, 0x70b5, 0x70a9, 0x7241, 0x7249,
  0x724a, 0x726c, 0x7270, 0x7273, 0x726e, 0x72ca,
  /* 0x2c */
  0x72e4, 0x72e8, 0x72eb, 0x72df, 0x72ea, 0x72e6, 0x72e3, 0x7385,
  0x73cc, 0x73c2, 0x73c8, 0x73c5, 0x73b9, 0x73b6, 0x73b5, 0x73b4,
  0x73eb, 0x73bf, 0x73c7, 0x73be, 0x73c3, 0x73c6, 0x73b8, 0x73cb,
  0x74ec, 0x74ee, 0x752e, 0x7547, 0x7548, 0x75a7, 0x75aa, 0x7679,
  0x76c4, 0x7708, 0x7703, 0x7704, 0x7705, 0x770a, 0x76f7, 0x76fb,
  0x76fa, 0x77e7, 0x77e8, 0x7806, 0x7811, 0x7812, 0x7805, 0x7810,
  0x780f, 0x780e, 0x7809, 0x7803, 0x7813, 0x794a, 0x794c, 0x794b,
  0x7945, 0x7944, 0x79d5, 0x79cd, 0x79cf, 0x79d6, 0x79ce, 0x7a80,
  0x7a7e, 0x7ad1, 0x7b00, 0x7b01, 0x7c7a, 0x7c78, 0x7c79, 0x7c7f,
  0x7c80, 0x7c81, 0x7d03, 0x7d08, 0x7d01, 0x7f58, 0x7f91, 0x7f8d,
  0x7fbe, 0x8007, 0x800e, 0x800f, 0x8014, 0x8037, 0x80d8, 0x80c7,
  0x80e0, 0x80d1, 0x80c8, 0x80c2, 0x80d0, 0x80c5,
  /* 0x2d */
  0x80e3, 0x80d9, 0x80dc, 0x80ca, 0x80d5, 0x80c9, 0x80cf, 0x80d7,
  0x80e6, 0x80cd, 0x81ff, 0x8221, 0x8294, 0x82d9, 0x82fe, 0x82f9,
  0x8307, 0x82e8, 0x8300, 0x82d5, 0x833a, 0x82eb, 0x82d6, 0x82f4,
  0x82ec, 0x82e1, 0x82f2, 0x82f5, 0x830c, 0x82fb, 0x82f6, 0x82f0,
  0x82ea, 0x82e4, 0x82e0, 0x82fa, 0x82f3, 0x82ed, 0x8677, 0x8674,
  0x867c, 0x8673, 0x8841, 0x884e, 0x8867, 0x886a, 0x8869, 0x89d3,
  0x8a04, 0x8a07, 0x8d72, 0x8fe3, 0x8fe1, 0x8fee, 0x8fe0, 0x90f1,
  0x90bd, 0x90bf, 0x90d5, 0x90c5, 0x90be, 0x90c7, 0x90cb, 0x90c8,
  0x91d4, 0x91d3, 0x9654, 0x964f, 0x9651, 0x9653, 0x964a, 0x964e,
  0x501e, 0x5005, 0x5007, 0x5013, 0x5022, 0x5030, 0x501b, 0x4ff5,
  0x4ff4, 0x5033, 0x5037, 0x502c, 0x4ff6, 0x4ff7, 0x5017, 0x501c,
  0x5020, 0x5027, 0x5035, 0x502f, 0x5031, 0x500e,
  /* 0x2e */
  0x515a, 0x5194, 0x5193, 0x51ca, 0x51c4, 0x51c5, 0x51c8, 0x51ce,
  0x5261, 0x525a, 0x5252, 0x525e, 0x525f, 0x5255, 0x5262, 0x52cd,
  0x530e, 0x539e, 0x5526, 0x54e2, 0x5517, 0x5512, 0x54e7, 0x54f3,
  0x54e4, 0x551a, 0x54ff, 0x5504, 0x5508, 0x54eb, 0x5511, 0x5505,
  0x54f1, 0x550a, 0x54fb, 0x54f7, 0x54f8, 0x54e0, 0x550e, 0x5503,
  0x550b, 0x5701, 0x5702, 0x57cc, 0x5832, 0x57d5, 0x57d2, 0x57ba,
  0x57c6, 0x57bd, 0x57bc, 0x57b8, 0x57b6, 0x57bf, 0x57c7, 0x57d0,
  0x57b9, 0x57c1, 0x590e, 0x594a, 0x5a19, 0x5a16, 0x5a2d, 0x5a2e,
  0x5a15, 0x5a0f, 0x5a17, 0x5a0a, 0x5a1e, 0x5a33, 0x5b6c, 0x5ba7,
  0x5bad, 0x5bac, 0x5c03, 0x5c56, 0x5c54, 0x5cec, 0x5cff, 0x5cee,
  0x5cf1, 0x5cf7, 0x5d00, 0x5cf9, 0x5e29, 0x5e28, 0x5ea8, 0x5eae,
  0x5eaa, 0x5eac, 0x5f33, 0x5f30, 0x5f67, 0x605d,
  /* 0x2f */
  0x605a, 0x6067, 0x6041, 0x60a2, 0x6088, 0x6080, 0x6092, 0x6081,
  0x609d, 0x6083, 0x6095, 0x609b, 0x6097, 0x6087, 0x609c, 0x608e,
  0x6219, 0x6246, 0x62f2, 0x6310, 0x6356, 0x632c, 0x6344, 0x6345,
  0x6336, 0x6343, 0x63e4, 0x6339, 0x634b, 0x634a, 0x633c, 0x6329,
  0x6341, 0x6334, 0x6358, 0x6354, 0x6359, 0x632d, 0x6347, 0x6333,
  0x635a, 0x6351, 0x6338, 0x6357, 0x6340, 0x6348, 0x654a, 0x6546,
  0x65c6, 0x65c3, 0x65c4, 0x65c2, 0x664a, 0x665f, 0x6647, 0x6651,
  0x6712, 0x6713, 0x681f, 0x681a, 0x6849, 0x6832, 0x6833, 0x683b,
  0x684b, 0x684f, 0x6816, 0x6831, 0x681c, 0x6835, 0x682b, 0x682d,
  0x682f, 0x684e, 0x6844, 0x6834, 0x681d, 0x6812, 0x6814, 0x6826,
  0x6828, 0x682e, 0x684d, 0x683a, 0x6825, 0x6820, 0x6b2c, 0x6b2f,
  0x6b2d, 0x6b31, 0x6b34, 0x6b6d, 0x8082, 0x6b88,
  /* 0x30 */
  0x6be6, 0x6be4, 0x6be8, 0x6be3, 0x6be2, 0x6be7, 0x6c25, 0x6d7a,
  0x6d63, 0x6d64, 0x6d76, 0x6d0d, 0x6d61, 0x6d92, 0x6d58, 0x6d62,
  0x6d6d, 0x6d6f, 0x6d91, 0x6d8d, 0x6def, 0x6d7f, 0x6d86, 0x6d5e,
  0x6d67, 0x6d60, 0x6d97, 0x6d70, 0x6d7c, 0x6d5f, 0x6d82, 0x6d98,
  0x6d2f, 0x6d68, 0x6d8b, 0x6d7e, 0x6d80, 0x6d84, 0x6d16, 0x6d83,
  0x6d7b, 0x6d7d, 0x6d75, 0x6d90, 0x70dc, 0x70d3, 0x70d1, 0x70dd,
  0x70cb, 0x7f39, 0x70e2, 0x70d7, 0x70d2, 0x70de, 0x70e0, 0x70d4,
  0x70cd, 0x70c5, 0x70c6, 0x70c7, 0x70da, 0x70ce, 0x70e1, 0x7242,
  0x7278, 0x7277, 0x7276, 0x7300, 0x72fa, 0x72f4, 0x72fe, 0x72f6,
  0x72f3, 0x72fb, 0x7301, 0x73d3, 0x73d9, 0x73e5, 0x73d6, 0x73bc,
  0x73e7, 0x73e3, 0x73e9, 0x73dc, 0x73d2, 0x73db, 0x73d4, 0x73dd,
  0x73da, 0x73d7, 0x73d8, 0x73e8, 0x74de, 0x74df,
  /* 0x31 */
  0x74f4, 0x74f5, 0x7521, 0x755b, 0x755f, 0x75b0, 0x75c1, 0x75bb,
  0x75c4, 0x75c0, 0x75bf, 0x75b6, 0x75ba, 0x768a, 0x76c9, 0x771d,
  0x771b, 0x7710, 0x7713, 0x7712, 0x7723, 0x7711, 0x7715, 0x7719,
  0x771a, 0x7722, 0x7727, 0x7823, 0x782c, 0x7822, 0x7835, 0x782f,
  0x7828, 0x782e, 0x782b, 0x7821, 0x7829, 0x7833, 0x782a, 0x7831,
  0x7954, 0x795b, 0x794f, 0x795c, 0x7953, 0x7952, 0x7951, 0x79eb,
  0x79ec, 0x79e0, 0x79ee, 0x79ed, 0x79ea, 0x79dc, 0x79de, 0x79dd,
  0x7a86, 0x7a89, 0x7a85, 0x7a8b, 0x7a8c, 0x7a8a, 0x7a87, 0x7ad8,
  0x7b10, 0x7b04, 0x7b13, 0x7b05, 0x7b0f, 0x7b08, 0x7b0a, 0x7b0e,
  0x7b09, 0x7b12, 0x7c84, 0x7c91, 0x7c8a, 0x7c8c, 0x7c88, 0x7c8d,
  0x7c85, 0x7d1e, 0x7d1d, 0x7d11, 0x7d0e, 0x7d18, 0x7d16, 0x7d13,
  0x7d1f, 0x7d12, 0x7d0f, 0x7d0c, 0x7f5c, 0x7f61,
  /* 0x32 */
  0x7f5e, 0x7f60, 0x7f5d, 0x7f5b, 0x7f96, 0x7f92, 0x7fc3, 0x7fc2,
  0x7fc0, 0x8016, 0x803e, 0x8039, 0x80fa, 0x80f2, 0x80f9, 0x80f5,
  0x8101, 0x80fb, 0x8100, 0x8201, 0x822f, 0x8225, 0x8333, 0x832d,
  0x8344, 0x8319, 0x8351, 0x8325, 0x8356, 0x833f, 0x8341, 0x8326,
  0x831c, 0x8322, 0x8342, 0x834e, 0x831b, 0x832a, 0x8308, 0x833c,
  0x834d, 0x8316, 0x8324, 0x8320, 0x8337, 0x832f, 0x8329, 0x8347,
  0x8345, 0x834c, 0x8353, 0x831e, 0x832c, 0x834b, 0x8327, 0x8348,
  0x8653, 0x8652, 0x86a2, 0x86a8, 0x8696, 0x868d, 0x8691, 0x869e,
  0x8687, 0x8697, 0x8686, 0x868b, 0x869a, 0x8685, 0x86a5, 0x8699,
  0x86a1, 0x86a7, 0x8695, 0x8698, 0x868e, 0x869d, 0x8690, 0x8694,
  0x8843, 0x8844, 0x886d, 0x8875, 0x8876, 0x8872, 0x8880, 0x8871,
  0x887f, 0x886f, 0x8883, 0x887e, 0x8874, 0x887c,
  /* 0x33 */
  0x8a12, 0x8c47, 0x8c57, 0x8c7b, 0x8ca4, 0x8ca3, 0x8d76, 0x8d78,
  0x8db5, 0x8db7, 0x8db6, 0x8ed1, 0x8ed3, 0x8ffe, 0x8ff5, 0x9002,
  0x8fff, 0x8ffb, 0x9004, 0x8ffc, 0x8ff6, 0x90d6, 0x90e0, 0x90d9,
  0x90da, 0x90e3, 0x90df, 0x90e5, 0x90d8, 0x90db, 0x90d7, 0x90dc,
  0x90e4, 0x9150, 0x914e, 0x914f, 0x91d5, 0x91e2, 0x91da, 0x965c,
  0x965f, 0x96bc, 0x98e3, 0x9adf, 0x9b2f, 0x4e7f, 0x5070, 0x506a,
  0x5061, 0x505e, 0x5060, 0x5053, 0x504b, 0x505d, 0x5072, 0x5048,
  0x504d, 0x5041, 0x505b, 0x504a, 0x5062, 0x5015, 0x5045, 0x505f,
  0x5069, 0x506b, 0x5063, 0x5064, 0x5046, 0x5040, 0x506e, 0x5073,
  0x5057, 0x5051, 0x51d0, 0x526b, 0x526d, 0x526c, 0x526e, 0x52d6,
  0x52d3, 0x532d, 0x539c, 0x5575, 0x5576, 0x553c, 0x554d, 0x5550,
  0x5534, 0x552a, 0x5551, 0x5562, 0x5536, 0x5535,
  /* 0x34 */
  0x5530, 0x5552, 0x5545, 0x550c, 0x5532, 0x5565, 0x554e, 0x5539,
  0x5548, 0x552d, 0x553b, 0x5540, 0x554b, 0x570a, 0x5707, 0x57fb,
  0x5814, 0x57e2, 0x57f6, 0x57dc, 0x57f4, 0x5800, 0x57ed, 0x57fd,
  0x5808, 0x57f8, 0x580b, 0x57f3, 0x57cf, 0x5807, 0x57ee, 0x57e3,
  0x57f2, 0x57e5, 0x57ec, 0x57e1, 0x580e, 0x57fc, 0x5810, 0x57e7,
  0x5801, 0x580c, 0x57f1, 0x57e9, 0x57f0, 0x580d, 0x5804, 0x595c,
  0x5a60, 0x5a58, 0x5a55, 0x5a67, 0x5a5e, 0x5a38, 0x5a35, 0x5a6d,
  0x5a50, 0x5a5f, 0x5a65, 0x5a6c, 0x5a53, 0x5a64, 0x5a57, 0x5a43,
  0x5a5d, 0x5a52, 0x5a44, 0x5a5b, 0x5a48, 0x5a8e, 0x5a3e, 0x5a4d,
  0x5a39, 0x5a4c, 0x5a70, 0x5a69, 0x5a47, 0x5a51, 0x5a56, 0x5a42,
  0x5a5c, 0x5b72, 0x5b6e, 0x5bc1, 0x5bc0, 0x5c59, 0x5d1e, 0x5d0b,
  0x5d1d, 0x5d1a, 0x5d20, 0x5d0c, 0x5d28, 0x5d0d,
  /* 0x35 */
  0x5d26, 0x5d25, 0x5d0f, 0x5d30, 0x5d12, 0x5d23, 0x5d1f, 0x5d2e,
  0x5e3e, 0x5e34, 0x5eb1, 0x5eb4, 0x5eb9, 0x5eb2, 0x5eb3, 0x5f36,
  0x5f38, 0x5f9b, 0x5f96, 0x5f9f, 0x608a, 0x6090, 0x6086, 0x60be,
  0x60b0, 0x60ba, 0x60d3, 0x60d4, 0x60cf, 0x60e4, 0x60d9, 0x60dd,
  0x60c8, 0x60b1, 0x60db, 0x60b7, 0x60ca, 0x60bf, 0x60c3, 0x60cd,
  0x60c0, 0x6332, 0x6365, 0x638a, 0x6382, 0x637d, 0x63bd, 0x639e,
  0x63ad, 0x639d, 0x6397, 0x63ab, 0x638e, 0x636f, 0x6387, 0x6390,
  0x636e, 0x63af, 0x6375, 0x639c, 0x636d, 0x63ae, 0x637c, 0x63a4,
  0x633b, 0x639f, 0x6378, 0x6385, 0x6381, 0x6391, 0x638d, 0x6370,
  0x6553, 0x65cd, 0x6665, 0x6661, 0x665b, 0x6659, 0x665c, 0x6662,
  0x6718, 0x6879, 0x6887, 0x6890, 0x689c, 0x686d, 0x686e, 0x68ae,
  0x68ab, 0x6956, 0x686f, 0x68a3, 0x68ac, 0x68a9,
  /* 0x36 */
  0x6875, 0x6874, 0x68b2, 0x688f, 0x6877, 0x6892, 0x687c, 0x686b,
  0x6872, 0x68aa, 0x6880, 0x6871, 0x687e, 0x689b, 0x6896, 0x688b,
  0x68a0, 0x6889, 0x68a4, 0x6878, 0x687b, 0x6891, 0x688c, 0x688a,
  0x687d, 0x6b36, 0x6b33, 0x6b37, 0x6b38, 0x6b91, 0x6b8f, 0x6b8d,
  0x6b8e, 0x6b8c, 0x6c2a, 0x6dc0, 0x6dab, 0x6db4, 0x6db3, 0x6e74,
  0x6dac, 0x6de9, 0x6de2, 0x6db7, 0x6df6, 0x6dd4, 0x6e00, 0x6dc8,
  0x6de0, 0x6ddf, 0x6dd6, 0x6dbe, 0x6de5, 0x6ddc, 0x6ddd, 0x6ddb,
  0x6df4, 0x6dca, 0x6dbd, 0x6ded, 0x6df0, 0x6dba, 0x6dd5, 0x6dc2,
  0x6dcf, 0x6dc9, 0x6dd0, 0x6df2, 0x6dd3, 0x6dfd, 0x6dd7, 0x6dcd,
  0x6de3, 0x6dbb, 0x70fa, 0x710d, 0x70f7, 0x7117, 0x70f4, 0x710c,
  0x70f0, 0x7104, 0x70f3, 0x7110, 0x70fc, 0x70ff, 0x7106, 0x7113,
  0x7100, 0x70f8, 0x70f6, 0x710b, 0x7102, 0x710e,
  /* 0x37 */
  0x727e, 0x727b, 0x727c, 0x727f, 0x731d, 0x7317, 0x7307, 0x7311,
  0x7318, 0x730a, 0x7308, 0x72ff, 0x730f, 0x731e, 0x7388, 0x73f6,
  0x73f8, 0x73f5, 0x7404, 0x7401, 0x73fd, 0x7407, 0x7400, 0x73fa,
  0x73fc, 0x73ff, 0x740c, 0x740b, 0x73f4, 0x7408, 0x7564, 0x7563,
  0x75ce, 0x75d2, 0x75cf, 0x75cb, 0x75cc, 0x75d1, 0x75d0, 0x768f,
  0x7689, 0x76d3, 0x7739, 0x772f, 0x772d, 0x7731, 0x7732, 0x7734,
  0x7733, 0x773d, 0x7725, 0x773b, 0x7735, 0x7848, 0x7852, 0x7849,
  0x784d, 0x784a, 0x784c, 0x7826, 0x7845, 0x7850, 0x7964, 0x7967,
  0x7969, 0x796a, 0x7963, 0x796b, 0x7961, 0x79bb, 0x79fa, 0x79f8,
  0x79f6, 0x79f7, 0x7a8f, 0x7a94, 0x7a90, 0x7b35, 0x7b3b, 0x7b34,
  0x7b25, 0x7b30, 0x7b22, 0x7b24, 0x7b33, 0x7b18, 0x7b2a, 0x7b1d,
  0x7b31, 0x7b2b, 0x7b2d, 0x7b2f, 0x7b32, 0x7b38,
  /* 0x38 */
  0x7b1a, 0x7b23, 0x7c94, 0x7c98, 0x7c96, 0x7ca3, 0x7d35, 0x7d3d,
  0x7d38, 0x7d36, 0x7d3a, 0x7d45, 0x7d2c, 0x7d29, 0x7d41, 0x7d47,
  0x7d3e, 0x7d3f, 0x7d4a, 0x7d3b, 0x7d28, 0x7f63, 0x7f95, 0x7f9c,
  0x7f9d, 0x7f9b, 0x7fca, 0x7fcb, 0x7fcd, 0x7fd0, 0x7fd1, 0x7fc7,
  0x7fcf, 0x7fc9, 0x801f, 0x801e, 0x801b, 0x8047, 0x8043, 0x8048,
  0x8118, 0x8125, 0x8119, 0x811b, 0x812d, 0x811f, 0x812c, 0x811e,
  0x8121, 0x8115, 0x8127, 0x811d, 0x8122, 0x8211, 0x8238, 0x8233,
  0x823a, 0x8234, 0x8232, 0x8274, 0x8390, 0x83a3, 0x83a8, 0x838d,
  0x837a, 0x8373, 0x83a4, 0x8374, 0x838f, 0x8381, 0x8395, 0x8399,
  0x8375, 0x8394, 0x83a9, 0x837d, 0x8383, 0x838c, 0x839d, 0x839b,
  0x83aa, 0x838b, 0x837e, 0x83a5, 0x83af, 0x8388, 0x8397, 0x83b0,
  0x837f, 0x83a6, 0x8387, 0x83ae, 0x8376, 0x8659,
  /* 0x39 */
  0x8656, 0x86bf, 0x86b7, 0x86c2, 0x86c1, 0x86c5, 0x86ba, 0x86b0,
  0x86c8, 0x86b9, 0x86b3, 0x86b8, 0x86cc, 0x86b4, 0x86bb, 0x86bc,
  0x86c3, 0x86bd, 0x86be, 0x8852, 0x8889, 0x8895, 0x88a8, 0x88a2,
  0x88aa, 0x889a, 0x8891, 0x88a1, 0x889f, 0x8898, 0x88a7, 0x8899,
  0x889b, 0x8897, 0x88a4, 0x88ac, 0x888c, 0x8893, 0x888e, 0x8982,
  0x89d6, 0x89d9, 0x89d5, 0x8a30, 0x8a27, 0x8a2c, 0x8a1e, 0x8c39,
  0x8c3b, 0x8c5c, 0x8c5d, 0x8c7d, 0x8ca5, 0x8d7d, 0x8d7b, 0x8d79,
  0x8dbc, 0x8dc2, 0x8db9, 0x8dbf, 0x8dc1, 0x8ed8, 0x8ede, 0x8edd,
  0x8edc, 0x8ed7, 0x8ee0, 0x8ee1, 0x9024, 0x900b, 0x9011, 0x901c,
  0x900c, 0x9021, 0x90ef, 0x90ea, 0x90f0, 0x90f4, 0x90f2, 0x90f3,
  0x90d4, 0x90eb, 0x90ec, 0x90e9, 0x9156, 0x9158, 0x915a, 0x9153,
  0x9155, 0x91ec, 0x91f4, 0x91f1, 0x91f3, 0x91f8,
  /* 0x3a */
  0x91e4, 0x91f9, 0x91ea, 0x91eb, 0x91f7, 0x91e8, 0x91ee, 0x957a,
  0x9586, 0x9588, 0x967c, 0x966d, 0x966b, 0x9671, 0x966f, 0x96bf,
  0x976a, 0x9804, 0x98e5, 0x9997, 0x509b, 0x5095, 0x5094, 0x509e,
  0x508b, 0x50a3, 0x5083, 0x508c, 0x508e, 0x509d, 0x5068, 0x509c,
  0x5092, 0x5082, 0x5087, 0x515f, 0x51d4, 0x5312, 0x5311, 0x53a4,
  0x53a7, 0x5591, 0x55a8, 0x55a5, 0x55ad, 0x5577, 0x5645, 0x55a2,
  0x5593, 0x5588, 0x558f, 0x55b5, 0x5581, 0x55a3, 0x5592, 0x55a4,
  0x557d, 0x558c, 0x55a6, 0x557f, 0x5595, 0x55a1, 0x558e, 0x570c,
  0x5829, 0x5837, 0x5819, 0x581e, 0x5827, 0x5823, 0x5828, 0x57f5,
  0x5848, 0x5825, 0x581c, 0x581b, 0x5833, 0x583f, 0x5836, 0x582e,
  0x5839, 0x5838, 0x582d, 0x582c, 0x583b, 0x5961, 0x5aaf, 0x5a94,
  0x5a9f, 0x5a7a, 0x5aa2, 0x5a9e, 0x5a78, 0x5aa6,
  /* 0x3b */
  0x5a7c, 0x5aa5, 0x5aac, 0x5a95, 0x5aae, 0x5a37, 0x5a84, 0x5a8a,
  0x5a97, 0x5a83, 0x5a8b, 0x5aa9, 0x5a7b, 0x5a7d, 0x5a8c, 0x5a9c,
  0x5a8f, 0x5a93, 0x5a9d, 0x5bea, 0x5bcd, 0x5bcb, 0x5bd4, 0x5bd1,
  0x5bca, 0x5bce, 0x5c0c, 0x5c30, 0x5d37, 0x5d43, 0x5d6b, 0x5d41,
  0x5d4b, 0x5d3f, 0x5d35, 0x5d51, 0x5d4e, 0x5d55, 0x5d33, 0x5d3a,
  0x5d52, 0x5d3d, 0x5d31, 0x5d59, 0x5d42, 0x5d39, 0x5d49, 0x5d38,
  0x5d3c, 0x5d32, 0x5d36, 0x5d40, 0x5d45, 0x5e44, 0x5e41, 0x5f58,
  0x5fa6, 0x5fa5, 0x5fab, 0x60c9, 0x60b9, 0x60cc, 0x60e2, 0x60ce,
  0x60c4, 0x6114, 0x60f2, 0x610a, 0x6116, 0x6105, 0x60f5, 0x6113,
  0x60f8, 0x60fc, 0x60fe, 0x60c1, 0x6103, 0x6118, 0x611d, 0x6110,
  0x60ff, 0x6104, 0x610b, 0x624a, 0x6394, 0x63b1, 0x63b0, 0x63ce,
  0x63e5, 0x63e8, 0x63ef, 0x63c3, 0x649d, 0x63f3,
  /* 0x3c */
  0x63ca, 0x63e0, 0x63f6, 0x63d5, 0x63f2, 0x63f5, 0x6461, 0x63df,
  0x63be, 0x63dd, 0x63dc, 0x63c4, 0x63d8, 0x63d3, 0x63c2, 0x63c7,
  0x63cc, 0x63cb, 0x63c8, 0x63f0, 0x63d7, 0x63d9, 0x6532, 0x6567,
  0x656a, 0x6564, 0x655c, 0x6568, 0x6565, 0x658c, 0x659d, 0x659e,
  0x65ae, 0x65d0, 0x65d2, 0x667c, 0x666c, 0x667b, 0x6680, 0x6671,
  0x6679, 0x666a, 0x6672, 0x6701, 0x690c, 0x68d3, 0x6904, 0x68dc,
  0x692a, 0x68ec, 0x68ea, 0x68f1, 0x690f, 0x68d6, 0x68f7, 0x68eb,
  0x68e4, 0x68f6, 0x6913, 0x6910, 0x68f3, 0x68e1, 0x6907, 0x68cc,
  0x6908, 0x6970, 0x68b4, 0x6911, 0x68ef, 0x68c6, 0x6914, 0x68f8,
  0x68d0, 0x68fd, 0x68fc, 0x68e8, 0x690b, 0x690a, 0x6917, 0x68ce,
  0x68c8, 0x68dd, 0x68de, 0x68e6, 0x68f4, 0x68d1, 0x6906, 0x68d4,
  0x68e9, 0x6915, 0x6925, 0x68c7, 0x6b39, 0x6b3b,
  /* 0x3d */
  0x6b3f, 0x6b3c, 0x6b94, 0x6b97, 0x6b99, 0x6b95, 0x6bbd, 0x6bf0,
  0x6bf2, 0x6bf3, 0x6c30, 0x6dfc, 0x6e46, 0x6e47, 0x6e1f, 0x6e49,
  0x6e88, 0x6e3c, 0x6e3d, 0x6e45, 0x6e62, 0x6e2b, 0x6e3f, 0x6e41,
  0x6e5d, 0x6e73, 0x6e1c, 0x6e33, 0x6e4b, 0x6e40, 0x6e51, 0x6e3b,
  0x6e03, 0x6e2e, 0x6e5e, 0x6e68, 0x6e5c, 0x6e61, 0x6e31, 0x6e28,
  0x6e60, 0x6e71, 0x6e6b, 0x6e39, 0x6e22, 0x6e30, 0x6e53, 0x6e65,
  0x6e27, 0x6e78, 0x6e64, 0x6e77, 0x6e55, 0x6e79, 0x6e52, 0x6e66,
  0x6e35, 0x6e36, 0x6e5a, 0x7120, 0x711e, 0x712f, 0x70fb, 0x712e,
  0x7131, 0x7123, 0x7125, 0x7122, 0x7132, 0x711f, 0x7128, 0x713a,
  0x711b, 0x724b, 0x725a, 0x7288, 0x7289, 0x7286, 0x7285, 0x728b,
  0x7312, 0x730b, 0x7330, 0x7322, 0x7331, 0x7333, 0x7327, 0x7332,
  0x732d, 0x7326, 0x7323, 0x7335, 0x730c, 0x742e,
  /* 0x3e */
  0x742c, 0x7430, 0x742b, 0x7416, 0x741a, 0x7421, 0x742d, 0x7431,
  0x7424, 0x7423, 0x741d, 0x7429, 0x7420, 0x7432, 0x74fb, 0x752f,
  0x756f, 0x756c, 0x75e7, 0x75da, 0x75e1, 0x75e6, 0x75dd, 0x75df,
  0x75e4, 0x75d7, 0x7695, 0x7692, 0x76da, 0x7746, 0x7747, 0x7744,
  0x774d, 0x7745, 0x774a, 0x774e, 0x774b, 0x774c, 0x77de, 0x77ec,
  0x7860, 0x7864, 0x7865, 0x785c, 0x786d, 0x7871, 0x786a, 0x786e,
  0x7870, 0x7869, 0x7868, 0x785e, 0x7862, 0x7974, 0x7973, 0x7972,
  0x7970, 0x7a02, 0x7a0a, 0x7a03, 0x7a0c, 0x7a04, 0x7a99, 0x7ae6,
  0x7ae4, 0x7b4a, 0x7b47, 0x7b44, 0x7b48, 0x7b4c, 0x7b4e, 0x7b40,
  0x7b58, 0x7b45, 0x7ca2, 0x7c9e, 0x7ca8, 0x7ca1, 0x7d58, 0x7d6f,
  0x7d63, 0x7d53, 0x7d56, 0x7d67, 0x7d6a, 0x7d4f, 0x7d6d, 0x7d5c,
  0x7d6b, 0x7d52, 0x7d54, 0x7d69, 0x7d51, 0x7d5f,
  /* 0x3f */
  0x7d4e, 0x7f3e, 0x7f3f, 0x7f65, 0x7f66, 0x7fa2, 0x7fa0, 0x7fa1,
  0x7fd7, 0x8051, 0x804f, 0x8050, 0x80fe, 0x80d4, 0x8143, 0x814a,
  0x8152, 0x814f, 0x8147, 0x813d, 0x814d, 0x813a, 0x81e6, 0x81ee,
  0x81f7, 0x81f8, 0x81f9, 0x8204, 0x823c, 0x823d, 0x823f, 0x8275,
  0x833b, 0x83cf, 0x83f9, 0x8423, 0x83c0, 0x83e8, 0x8412, 0x83e7,
  0x83e4, 0x83fc, 0x83f6, 0x8410, 0x83c6, 0x83c8, 0x83eb, 0x83e3,
  0x83bf, 0x8401, 0x83dd, 0x83e5, 0x83d8, 0x83ff, 0x83e1, 0x83cb,
  0x83ce, 0x83d6, 0x83f5, 0x83c9, 0x8409, 0x840f, 0x83de, 0x8411,
  0x8406, 0x83c2, 0x83f3, 0x83d5, 0x83fa, 0x83c7, 0x83d1, 0x83ea,
  0x8413, 0x839a, 0x83c3, 0x83ec, 0x83ee, 0x83c4, 0x83fb, 0x83d7,
  0x83e2, 0x841b, 0x83db, 0x83fe, 0x86d8, 0x86e2, 0x86e6, 0x86d3,
  0x86e3, 0x86da, 0x86ea, 0x86dd, 0x86eb, 0x86dc,
  /* 0x40 */
  0x86ec, 0x86e9, 0x86d7, 0x86e8, 0x86d1, 0x8848, 0x8856, 0x8855,
  0x88ba, 0x88d7, 0x88b9, 0x88b8, 0x88c0, 0x88be, 0x88b6, 0x88bc,
  0x88b7, 0x88bd, 0x88b2, 0x8901, 0x88c9, 0x8995, 0x8998, 0x8997,
  0x89dd, 0x89da, 0x89db, 0x8a4e, 0x8a4d, 0x8a39, 0x8a59, 0x8a40,
  0x8a57, 0x8a58, 0x8a44, 0x8a45, 0x8a52, 0x8a48, 0x8a51, 0x8a4a,
  0x8a4c, 0x8a4f, 0x8c5f, 0x8c81, 0x8c80, 0x8cba, 0x8cbe, 0x8cb0,
  0x8cb9, 0x8cb5, 0x8d84, 0x8d80, 0x8d89, 0x8dd8, 0x8dd3, 0x8dcd,
  0x8dc7, 0x8dd6, 0x8ddc, 0x8dcf, 0x8dd5, 0x8dd9, 0x8dc8, 0x8dd7,
  0x8dc5, 0x8eef, 0x8ef7, 0x8efa, 0x8ef9, 0x8ee6, 0x8eee, 0x8ee5,
  0x8ef5, 0x8ee7, 0x8ee8, 0x8ef6, 0x8eeb, 0x8ef1, 0x8eec, 0x8ef4,
  0x8ee9, 0x902d, 0x9034, 0x902f, 0x9106, 0x912c, 0x9104, 0x90ff,
  0x90fc, 0x9108, 0x90f9, 0x90fb, 0x9101, 0x9100,
  /* 0x41 */
  0x9107, 0x9105, 0x9103, 0x9161, 0x9164, 0x915f, 0x9162, 0x9160,
  0x9201, 0x920a, 0x9225, 0x9203, 0x921a, 0x9226, 0x920f, 0x920c,
  0x9200, 0x9212, 0x91ff, 0x91fd, 0x9206, 0x9204, 0x9227, 0x9202,
  0x921c, 0x9224, 0x9219, 0x9217, 0x9205, 0x9216, 0x957b, 0x958d,
  0x958c, 0x9590, 0x9687, 0x967e, 0x9688, 0x9689, 0x9683, 0x9680,
  0x96c2, 0x96c8, 0x96c3, 0x96f1, 0x96f0, 0x976c, 0x9770, 0x976e,
  0x9807, 0x98a9, 0x98eb, 0x9ce6, 0x9ef9, 0x4e83, 0x4e84, 0x4eb6,
  0x50bd, 0x50bf, 0x50c6, 0x50ae, 0x50c4, 0x50ca, 0x50b4, 0x50c8,
  0x50c2, 0x50b0, 0x50c1, 0x50ba, 0x50b1, 0x50cb, 0x50c9, 0x50b6,
  0x50b8, 0x51d7, 0x527a, 0x5278, 0x527b, 0x527c, 0x55c3, 0x55db,
  0x55cc, 0x55d0, 0x55cb, 0x55ca, 0x55dd, 0x55c0, 0x55d4, 0x55c4,
  0x55e9, 0x55bf, 0x55d2, 0x558d, 0x55cf, 0x55d5,
  /* 0x42 */
  0x55e2, 0x55d6, 0x55c8, 0x55f2, 0x55cd, 0x55d9, 0x55c2, 0x5714,
  0x5853, 0x5868, 0x5864, 0x584f, 0x584d, 0x5849, 0x586f, 0x5855,
  0x584e, 0x585d, 0x5859, 0x5865, 0x585b, 0x583d, 0x5863, 0x5871,
  0x58fc, 0x5ac7, 0x5ac4, 0x5acb, 0x5aba, 0x5ab8, 0x5ab1, 0x5ab5,
  0x5ab0, 0x5abf, 0x5ac8, 0x5abb, 0x5ac6, 0x5ab7, 0x5ac0, 0x5aca,
  0x5ab4, 0x5ab6, 0x5acd, 0x5ab9, 0x5a90, 0x5bd6, 0x5bd8, 0x5bd9,
  0x5c1f, 0x5c33, 0x5d71, 0x5d63, 0x5d4a, 0x5d65, 0x5d72, 0x5d6c,
  0x5d5e, 0x5d68, 0x5d67, 0x5d62, 0x5df0, 0x5e4f, 0x5e4e, 0x5e4a,
  0x5e4d, 0x5e4b, 0x5ec5, 0x5ecc, 0x5ec6, 0x5ecb, 0x5ec7, 0x5f40,
  0x5faf, 0x5fad, 0x60f7, 0x6149, 0x614a, 0x612b, 0x6145, 0x6136,
  0x6132, 0x612e, 0x6146, 0x612f, 0x614f, 0x6129, 0x6140, 0x6220,
  0x9168, 0x6223, 0x6225, 0x6224, 0x63c5, 0x63f1,
  /* 0x43 */
  0x63eb, 0x6410, 0x6412, 0x6409, 0x6420, 0x6424, 0x6433, 0x6443,
  0x641f, 0x6415, 0x6418, 0x6439, 0x6437, 0x6422, 0x6423, 0x640c,
  0x6426, 0x6430, 0x6428, 0x6441, 0x6435, 0x642f, 0x640a, 0x641a,
  0x6440, 0x6425, 0x6427, 0x640b, 0x63e7, 0x641b, 0x642e, 0x6421,
  0x640e, 0x656f, 0x6592, 0x65d3, 0x6686, 0x668c, 0x6695, 0x6690,
  0x668b, 0x668a, 0x6699, 0x6694, 0x6678, 0x6720, 0x6966, 0x695f,
  0x6938, 0x694e, 0x6962, 0x6971, 0x693f, 0x6945, 0x696a, 0x6939,
  0x6942, 0x6957, 0x6959, 0x697a, 0x6948, 0x6949, 0x6935, 0x696c,
  0x6933, 0x693d, 0x6965, 0x68f0, 0x6978, 0x6934, 0x6969, 0x6940,
  0x696f, 0x6944, 0x6976, 0x6958, 0x6941, 0x6974, 0x694c, 0x693b,
  0x694b, 0x6937, 0x695c, 0x694f, 0x6951, 0x6932, 0x6952, 0x692f,
  0x697b, 0x693c, 0x6b46, 0x6b45, 0x6b43, 0x6b42,
  /* 0x44 */
  0x6b48, 0x6b41, 0x6b9b, 0x6bfb, 0x6bfc, 0x6bf9, 0x6bf7, 0x6bf8,
  0x6e9b, 0x6ed6, 0x6ec8, 0x6e8f, 0x6ec0, 0x6e9f, 0x6e93, 0x6e94,
  0x6ea0, 0x6eb1, 0x6eb9, 0x6ec6, 0x6ed2, 0x6ebd, 0x6ec1, 0x6e9e,
  0x6ec9, 0x6eb7, 0x6eb0, 0x6ecd, 0x6ea6, 0x6ecf, 0x6eb2, 0x6ebe,
  0x6ec3, 0x6edc, 0x6ed8, 0x6e99, 0x6e92, 0x6e8e, 0x6e8d, 0x6ea4,
  0x6ea1, 0x6ebf, 0x6eb3, 0x6ed0, 0x6eca, 0x6e97, 0x6eae, 0x6ea3,
  0x7147, 0x7154, 0x7152, 0x7163, 0x7160, 0x7141, 0x715d, 0x7162,
  0x7172, 0x7178, 0x716a, 0x7161, 0x7142, 0x7158, 0x7143, 0x714b,
  0x7170, 0x715f, 0x7150, 0x7153, 0x7144, 0x714d, 0x715a, 0x724f,
  0x728d, 0x728c, 0x7291, 0x7290, 0x728e, 0x733c, 0x7342, 0x733b,
  0x733a, 0x7340, 0x734a, 0x7349, 0x7444, 0x744a, 0x744b, 0x7452,
  0x7451, 0x7457, 0x7440, 0x744f, 0x7450, 0x744e,
  /* 0x45 */
  0x7442, 0x7446, 0x744d, 0x7454, 0x74e1, 0x74ff, 0x74fe, 0x74fd,
  0x751d, 0x7579, 0x7577, 0x6983, 0x75ef, 0x760f, 0x7603, 0x75f7,
  0x75fe, 0x75fc, 0x75f9, 0x75f8, 0x7610, 0x75fb, 0x75f6, 0x75ed,
  0x75f5, 0x75fd, 0x7699, 0x76b5, 0x76dd, 0x7755, 0x775f, 0x7760,
  0x7752, 0x7756, 0x775a, 0x7769, 0x7767, 0x7754, 0x7759, 0x776d,
  0x77e0, 0x7887, 0x789a, 0x7894, 0x788f, 0x7884, 0x7895, 0x7885,
  0x7886, 0x78a1, 0x7883, 0x7879, 0x7899, 0x7880, 0x7896, 0x787b,
  0x797c, 0x7982, 0x797d, 0x7979, 0x7a11, 0x7a18, 0x7a19, 0x7a12,
  0x7a17, 0x7a15, 0x7a22, 0x7a13, 0x7a1b, 0x7a10, 0x7aa3, 0x7aa2,
  0x7a9e, 0x7aeb, 0x7b66, 0x7b64, 0x7b6d, 0x7b74, 0x7b69, 0x7b72,
  0x7b65, 0x7b73, 0x7b71, 0x7b70, 0x7b61, 0x7b78, 0x7b76, 0x7b63,
  0x7cb2, 0x7cb4, 0x7caf, 0x7d88, 0x7d86, 0x7d80,
  /* 0x46 */
  0x7d8d, 0x7d7f, 0x7d85, 0x7d7a, 0x7d8e, 0x7d7b, 0x7d83, 0x7d7c,
  0x7d8c, 0x7d94, 0x7d84, 0x7d7d, 0x7d92, 0x7f6d, 0x7f6b, 0x7f67,
  0x7f68, 0x7f6c, 0x7fa6, 0x7fa5, 0x7fa7, 0x7fdb, 0x7fdc, 0x8021,
  0x8164, 0x8160, 0x8177, 0x815c, 0x8169, 0x815b, 0x8162, 0x8172,
  0x6721, 0x815e, 0x8176, 0x8167, 0x816f, 0x8144, 0x8161, 0x821d,
  0x8249, 0x8244, 0x8240, 0x8242, 0x8245, 0x84f1, 0x843f, 0x8456,
  0x8476, 0x8479, 0x848f, 0x848d, 0x8465, 0x8451, 0x8440, 0x8486,
  0x8467, 0x8430, 0x844d, 0x847d, 0x845a, 0x8459, 0x8474, 0x8473,
  0x845d, 0x8507, 0x845e, 0x8437, 0x843a, 0x8434, 0x847a, 0x8443,
  0x8478, 0x8432, 0x8445, 0x8429, 0x83d9, 0x844b, 0x842f, 0x8442,
  0x842d, 0x845f, 0x8470, 0x8439, 0x844e, 0x844c, 0x8452, 0x846f,
  0x84c5, 0x848e, 0x843b, 0x8447, 0x8436, 0x8433,
  /* 0x47 */
  0x8468, 0x847e, 0x8444, 0x842b, 0x8460, 0x8454, 0x846e, 0x8450,
  0x870b, 0x8704, 0x86f7, 0x870c, 0x86fa, 0x86d6, 0x86f5, 0x874d,
  0x86f8, 0x870e, 0x8709, 0x8701, 0x86f6, 0x870d, 0x8705, 0x88d6,
  0x88cb, 0x88cd, 0x88ce, 0x88de, 0x88db, 0x88da, 0x88cc, 0x88d0,
  0x8985, 0x899b, 0x89df, 0x89e5, 0x89e4, 0x89e1, 0x89e0, 0x89e2,
  0x89dc, 0x89e6, 0x8a76, 0x8a86, 0x8a7f, 0x8a61, 0x8a3f, 0x8a77,
  0x8a82, 0x8a84, 0x8a75, 0x8a83, 0x8a81, 0x8a74, 0x8a7a, 0x8c3c,
  0x8c4b, 0x8c4a, 0x8c65, 0x8c64, 0x8c66, 0x8c86, 0x8c84, 0x8c85,
  0x8ccc, 0x8d68, 0x8d69, 0x8d91, 0x8d8c, 0x8d8e, 0x8d8f, 0x8d8d,
  0x8d93, 0x8d94, 0x8d90, 0x8d92, 0x8df0, 0x8de0, 0x8dec, 0x8df1,
  0x8dee, 0x8dd0, 0x8de9, 0x8de3, 0x8de2, 0x8de7, 0x8df2, 0x8deb,
  0x8df4, 0x8f06, 0x8eff, 0x8f01, 0x8f00, 0x8f05,
  /* 0x48 */
  0x8f07, 0x8f08, 0x8f02, 0x8f0b, 0x9052, 0x903f, 0x9044, 0x9049,
  0x903d, 0x9110, 0x910d, 0x910f, 0x9111, 0x9116, 0x9114, 0x910b,
  0x910e, 0x916e, 0x916f, 0x9248, 0x9252, 0x9230, 0x923a, 0x9266,
  0x9233, 0x9265, 0x925e, 0x9283, 0x922e, 0x924a, 0x9246, 0x926d,
  0x926c, 0x924f, 0x9260, 0x9267, 0x926f, 0x9236, 0x9261, 0x9270,
  0x9231, 0x9254, 0x9263, 0x9250, 0x9272, 0x924e, 0x9253, 0x924c,
  0x9256, 0x9232, 0x959f, 0x959c, 0x959e, 0x959b, 0x9692, 0x9693,
  0x9691, 0x9697, 0x96ce, 0x96fa, 0x96fd, 0x96f8, 0x96f5, 0x9773,
  0x9777, 0x9778, 0x9772, 0x980f, 0x980d, 0x980e, 0x98ac, 0x98f6,
  0x98f9, 0x99af, 0x99b2, 0x99b0, 0x99b5, 0x9aad, 0x9aab, 0x9b5b,
  0x9cea, 0x9ced, 0x9ce7, 0x9e80, 0x9efd, 0x50e6, 0x50d4, 0x50d7,
  0x50e8, 0x50f3, 0x50db, 0x50ea, 0x50dd, 0x50e4,
  /* 0x49 */
  0x50d3, 0x50ec, 0x50f0, 0x50ef, 0x50e3, 0x50e0, 0x51d8, 0x5280,
  0x5281, 0x52e9, 0x52eb, 0x5330, 0x53ac, 0x5627, 0x5615, 0x560c,
  0x5612, 0x55fc, 0x560f, 0x561c, 0x5601, 0x5613, 0x5602, 0x55fa,
  0x561d, 0x5604, 0x55ff, 0x55f9, 0x5889, 0x587c, 0x5890, 0x5898,
  0x5886, 0x5881, 0x587f, 0x5874, 0x588b, 0x587a, 0x5887, 0x5891,
  0x588e, 0x5876, 0x5882, 0x5888, 0x587b, 0x5894, 0x588f, 0x58fe,
  0x596b, 0x5adc, 0x5aee, 0x5ae5, 0x5ad5, 0x5aea, 0x5ada, 0x5aed,
  0x5aeb, 0x5af3, 0x5ae2, 0x5ae0, 0x5adb, 0x5aec, 0x5ade, 0x5add,
  0x5ad9, 0x5ae8, 0x5adf, 0x5b77, 0x5be0, 0x5be3, 0x5c63, 0x5d82,
  0x5d80, 0x5d7d, 0x5d86, 0x5d7a, 0x5d81, 0x5d77, 0x5d8a, 0x5d89,
  0x5d88, 0x5d7e, 0x5d7c, 0x5d8d, 0x5d79, 0x5d7f, 0x5e58, 0x5e59,
  0x5e53, 0x5ed8, 0x5ed1, 0x5ed7, 0x5ece, 0x5edc,
  /* 0x4a */
  0x5ed5, 0x5ed9, 0x5ed2, 0x5ed4, 0x5f44, 0x5f43, 0x5f6f, 0x5fb6,
  0x612c, 0x6128, 0x6141, 0x615e, 0x6171, 0x6173, 0x6152, 0x6153,
  0x6172, 0x616c, 0x6180, 0x6174, 0x6154, 0x617a, 0x615b, 0x6165,
  0x613b, 0x616a, 0x6161, 0x6156, 0x6229, 0x6227, 0x622b, 0x642b,
  0x644d, 0x645b, 0x645d, 0x6474, 0x6476, 0x6472, 0x6473, 0x647d,
  0x6475, 0x6466, 0x64a6, 0x644e, 0x6482, 0x645e, 0x645c, 0x644b,
  0x6453, 0x6460, 0x6450, 0x647f, 0x643f, 0x646c, 0x646b, 0x6459,
  0x6465, 0x6477, 0x6573, 0x65a0, 0x66a1, 0x66a0, 0x669f, 0x6705,
  0x6704, 0x6722, 0x69b1, 0x69b6, 0x69c9, 0x69a0, 0x69ce, 0x6996,
  0x69b0, 0x69ac, 0x69bc, 0x6991, 0x6999, 0x698e, 0x69a7, 0x698d,
  0x69a9, 0x69be, 0x69af, 0x69bf, 0x69c4, 0x69bd, 0x69a4, 0x69d4,
  0x69b9, 0x69ca, 0x699a, 0x69cf, 0x69b3, 0x6993,
  /* 0x4b */
  0x69aa, 0x69a1, 0x699e, 0x69d9, 0x6997, 0x6990, 0x69c2, 0x69b5,
  0x69a5, 0x69c6, 0x6b4a, 0x6b4d, 0x6b4b, 0x6b9e, 0x6b9f, 0x6ba0,
  0x6bc3, 0x6bc4, 0x6bfe, 0x6ece, 0x6ef5, 0x6ef1, 0x6f03, 0x6f25,
  0x6ef8, 0x6f37, 0x6efb, 0x6f2e, 0x6f09, 0x6f4e, 0x6f19, 0x6f1a,
  0x6f27, 0x6f18, 0x6f3b, 0x6f12, 0x6eed, 0x6f0a, 0x6f36, 0x6f73,
  0x6ef9, 0x6eee, 0x6f2d, 0x6f40, 0x6f30, 0x6f3c, 0x6f35, 0x6eeb,
  0x6f07, 0x6f0e, 0x6f43, 0x6f05, 0x6efd, 0x6ef6, 0x6f39, 0x6f1c,
  0x6efc, 0x6f3a, 0x6f1f, 0x6f0d, 0x6f1e, 0x6f08, 0x6f21, 0x7187,
  0x7190, 0x7189, 0x7180, 0x7185, 0x7182, 0x718f, 0x717b, 0x7186,
  0x7181, 0x7197, 0x7244, 0x7253, 0x7297, 0x7295, 0x7293, 0x7343,
  0x734d, 0x7351, 0x734c, 0x7462, 0x7473, 0x7471, 0x7475, 0x7472,
  0x7467, 0x746e, 0x7500, 0x7502, 0x7503, 0x757d,
  /* 0x4c */
  0x7590, 0x7616, 0x7608, 0x760c, 0x7615, 0x7611, 0x760a, 0x7614,
  0x76b8, 0x7781, 0x777c, 0x7785, 0x7782, 0x776e, 0x7780, 0x776f,
  0x777e, 0x7783, 0x78b2, 0x78aa, 0x78b4, 0x78ad, 0x78a8, 0x787e,
  0x78ab, 0x789e, 0x78a5, 0x78a0, 0x78ac, 0x78a2, 0x78a4, 0x7998,
  0x798a, 0x798b, 0x7996, 0x7995, 0x7994, 0x7993, 0x7997, 0x7988,
  0x7992, 0x7990, 0x7a2b, 0x7a4a, 0x7a30, 0x7a2f, 0x7a28, 0x7a26,
  0x7aa8, 0x7aab, 0x7aac, 0x7aee, 0x7b88, 0x7b9c, 0x7b8a, 0x7b91,
  0x7b90, 0x7b96, 0x7b8d, 0x7b8c, 0x7b9b, 0x7b8e, 0x7b85, 0x7b98,
  0x5284, 0x7b99, 0x7ba4, 0x7b82, 0x7cbb, 0x7cbf, 0x7cbc, 0x7cba,
  0x7da7, 0x7db7, 0x7dc2, 0x7da3, 0x7daa, 0x7dc1, 0x7dc0, 0x7dc5,
  0x7d9d, 0x7dce, 0x7dc4, 0x7dc6, 0x7dcb, 0x7dcc, 0x7daf, 0x7db9,
  0x7d96, 0x7dbc, 0x7d9f, 0x7da6, 0x7dae, 0x7da9,
  /* 0x4d */
  0x7da1, 0x7dc9, 0x7f73, 0x7fe2, 0x7fe3, 0x7fe5, 0x7fde, 0x8024,
  0x805d, 0x805c, 0x8189, 0x8186, 0x8183, 0x8187, 0x818d, 0x818c,
  0x818b, 0x8215, 0x8497, 0x84a4, 0x84a1, 0x849f, 0x84ba, 0x84ce,
  0x84c2, 0x84ac, 0x84ae, 0x84ab, 0x84b9, 0x84b4, 0x84c1, 0x84cd,
  0x84aa, 0x849a, 0x84b1, 0x84d0, 0x849d, 0x84a7, 0x84bb, 0x84a2,
  0x8494, 0x84c7, 0x84cc, 0x849b, 0x84a9, 0x84af, 0x84a8, 0x84d6,
  0x8498, 0x84b6, 0x84cf, 0x84a0, 0x84d7, 0x84d4, 0x84d2, 0x84db,
  0x84b0, 0x8491, 0x8661, 0x8733, 0x8723, 0x8728, 0x876b, 0x8740,
  0x872e, 0x871e, 0x8721, 0x8719, 0x871b, 0x8743, 0x872c, 0x8741,
  0x873e, 0x8746, 0x8720, 0x8732, 0x872a, 0x872d, 0x873c, 0x8712,
  0x873a, 0x8731, 0x8735, 0x8742, 0x8726, 0x8727, 0x8738, 0x8724,
  0x871a, 0x8730, 0x8711, 0x88f7, 0x88e7, 0x88f1,
  /* 0x4e */
  0x88f2, 0x88fa, 0x88fe, 0x88ee, 0x88fc, 0x88f6, 0x88fb, 0x88f0,
  0x88ec, 0x88eb, 0x899d, 0x89a1, 0x899f, 0x899e, 0x89e9, 0x89eb,
  0x89e8, 0x8aab, 0x8a99, 0x8a8b, 0x8a92, 0x8a8f, 0x8a96, 0x8c3d,
  0x8c68, 0x8c69, 0x8cd5, 0x8ccf, 0x8cd7, 0x8d96, 0x8e09, 0x8e02,
  0x8dff, 0x8e0d, 0x8dfd, 0x8e0a, 0x8e03, 0x8e07, 0x8e06, 0x8e05,
  0x8dfe, 0x8e00, 0x8e04, 0x8f10, 0x8f11, 0x8f0e, 0x8f0d, 0x9123,
  0x911c, 0x9120, 0x9122, 0x911f, 0x911d, 0x911a, 0x9124, 0x9121,
  0x911b, 0x917a, 0x9172, 0x9179, 0x9173, 0x92a5, 0x92a4, 0x9276,
  0x929b, 0x927a, 0x92a0, 0x9294, 0x92aa, 0x928d, 0x92a6, 0x929a,
  0x92ab, 0x9279, 0x9297, 0x927f, 0x92a3, 0x92ee, 0x928e, 0x9282,
  0x9295, 0x92a2, 0x927d, 0x9288, 0x92a1, 0x928a, 0x9286, 0x928c,
  0x9299, 0x92a7, 0x927e, 0x9287, 0x92a9, 0x929d,
  /* 0x4f */
  0x928b, 0x922d, 0x969e, 0x96a1, 0x96ff, 0x9758, 0x977d, 0x977a,
  0x977e, 0x9783, 0x9780, 0x9782, 0x977b, 0x9784, 0x9781, 0x977f,
  0x97ce, 0x97cd, 0x9816, 0x98ad, 0x98ae, 0x9902, 0x9900, 0x9907,
  0x999d, 0x999c, 0x99c3, 0x99b9, 0x99bb, 0x99ba, 0x99c2, 0x99bd,
  0x99c7, 0x9ab1, 0x9ae3, 0x9ae7, 0x9b3e, 0x9b3f, 0x9b60, 0x9b61,
  0x9b5f, 0x9cf1, 0x9cf2, 0x9cf5, 0x9ea7, 0x50ff, 0x5103, 0x5130,
  0x50f8, 0x5106, 0x5107, 0x50f6, 0x50fe, 0x510b, 0x510c, 0x50fd,
  0x510a, 0x528b, 0x528c, 0x52f1, 0x52ef, 0x5648, 0x5642, 0x564c,
  0x5635, 0x5641, 0x564a, 0x5649, 0x5646, 0x5658, 0x565a, 0x5640,
  0x5633, 0x563d, 0x562c, 0x563e, 0x5638, 0x562a, 0x563a, 0x571a,
  0x58ab, 0x589d, 0x58b1, 0x58a0, 0x58a3, 0x58af, 0x58ac, 0x58a5,
  0x58a1, 0x58ff, 0x5aff, 0x5af4, 0x5afd, 0x5af7,
  /* 0x50 */
  0x5af6, 0x5b03, 0x5af8, 0x5b02, 0x5af9, 0x5b01, 0x5b07, 0x5b05,
  0x5b0f, 0x5c67, 0x5d99, 0x5d97, 0x5d9f, 0x5d92, 0x5da2, 0x5d93,
  0x5d95, 0x5da0, 0x5d9c, 0x5da1, 0x5d9a, 0x5d9e, 0x5e69, 0x5e5d,
  0x5e60, 0x5e5c, 0x7df3, 0x5edb, 0x5ede, 0x5ee1, 0x5f49, 0x5fb2,
  0x618b, 0x6183, 0x6179, 0x61b1, 0x61b0, 0x61a2, 0x6189, 0x619b,
  0x6193, 0x61af, 0x61ad, 0x619f, 0x6192, 0x61aa, 0x61a1, 0x618d,
  0x6166, 0x61b3, 0x622d, 0x646e, 0x6470, 0x6496, 0x64a0, 0x6485,
  0x6497, 0x649c, 0x648f, 0x648b, 0x648a, 0x648c, 0x64a3, 0x649f,
  0x6468, 0x64b1, 0x6498, 0x6576, 0x657a, 0x6579, 0x657b, 0x65b2,
  0x65b3, 0x66b5, 0x66b0, 0x66a9, 0x66b2, 0x66b7, 0x66aa, 0x66af,
  0x6a00, 0x6a06, 0x6a17, 0x69e5, 0x69f8, 0x6a15, 0x69f1, 0x69e4,
  0x6a20, 0x69ff, 0x69ec, 0x69e2, 0x6a1b, 0x6a1d,
  /* 0x51 */
  0x69fe, 0x6a27, 0x69f2, 0x69ee, 0x6a14, 0x69f7, 0x69e7, 0x6a40,
  0x6a08, 0x69e6, 0x69fb, 0x6a0d, 0x69fc, 0x69eb, 0x6a09, 0x6a04,
  0x6a18, 0x6a25, 0x6a0f, 0x69f6, 0x6a26, 0x6a07, 0x69f4, 0x6a16,
  0x6b51, 0x6ba5, 0x6ba3, 0x6ba2, 0x6ba6, 0x6c01, 0x6c00, 0x6bff,
  0x6c02, 0x6f41, 0x6f26, 0x6f7e, 0x6f87, 0x6fc6, 0x6f92, 0x6f8d,
  0x6f89, 0x6f8c, 0x6f62, 0x6f4f, 0x6f85, 0x6f5a, 0x6f96, 0x6f76,
  0x6f6c, 0x6f82, 0x6f55, 0x6f72, 0x6f52, 0x6f50, 0x6f57, 0x6f94,
  0x6f93, 0x6f5d, 0x6f00, 0x6f61, 0x6f6b, 0x6f7d, 0x6f67, 0x6f90,
  0x6f53, 0x6f8b, 0x6f69, 0x6f7f, 0x6f95, 0x6f63, 0x6f77, 0x6f6a,
  0x6f7b, 0x71b2, 0x71af, 0x719b, 0x71b0, 0x71a0, 0x719a, 0x71a9,
  0x71b5, 0x719d, 0x71a5, 0x719e, 0x71a4, 0x71a1, 0x71aa, 0x719c,
  0x71a7, 0x71b3, 0x7298, 0x729a, 0x7358, 0x7352,
  /* 0x52 */
  0x735e, 0x735f, 0x7360, 0x735d, 0x735b, 0x7361, 0x735a, 0x7359,
  0x7362, 0x7487, 0x7489, 0x748a, 0x7486, 0x7481, 0x747d, 0x7485,
  0x7488, 0x747c, 0x7479, 0x7508, 0x7507, 0x757e, 0x7625, 0x761e,
  0x7619, 0x761d, 0x761c, 0x7623, 0x761a, 0x7628, 0x761b, 0x769c,
  0x769d, 0x769e, 0x769b, 0x778d, 0x778f, 0x7789, 0x7788, 0x78cd,
  0x78bb, 0x78cf, 0x78cc, 0x78d1, 0x78ce, 0x78d4, 0x78c8, 0x78c3,
  0x78c4, 0x78c9, 0x799a, 0x79a1, 0x79a0, 0x799c, 0x79a2, 0x799b,
  0x6b76, 0x7a39, 0x7ab2, 0x7ab4, 0x7ab3, 0x7bb7, 0x7bcb, 0x7bbe,
  0x7bac, 0x7bce, 0x7baf, 0x7bb9, 0x7bca, 0x7bb5, 0x7cc5, 0x7cc8,
  0x7ccc, 0x7ccb, 0x7df7, 0x7ddb, 0x7dea, 0x7de7, 0x7dd7, 0x7de1,
  0x7e03, 0x7dfa, 0x7de6, 0x7df6, 0x7df1, 0x7df0, 0x7dee, 0x7ddf,
  0x7f76, 0x7fac, 0x7fb0, 0x7fad, 0x7fed, 0x7feb,
  /* 0x53 */
  0x7fea, 0x7fec, 0x7fe6, 0x7fe8, 0x8064, 0x8067, 0x81a3, 0x819f,
  0x819e, 0x8195, 0x81a2, 0x8199, 0x8197, 0x8216, 0x824f, 0x8253,
  0x8252, 0x8250, 0x824e, 0x8251, 0x8524, 0x853b, 0x850f, 0x8500,
  0x8529, 0x850e, 0x8509, 0x850d, 0x851f, 0x850a, 0x8527, 0x851c,
  0x84fb, 0x852b, 0x84fa, 0x8508, 0x850c, 0x84f4, 0x852a, 0x84f2,
  0x8515, 0x84f7, 0x84eb, 0x84f3, 0x84fc, 0x8512, 0x84ea, 0x84e9,
  0x8516, 0x84fe, 0x8528, 0x851d, 0x852e, 0x8502, 0x84fd, 0x851e,
  0x84f6, 0x8531, 0x8526, 0x84e7, 0x84e8, 0x84f0, 0x84ef, 0x84f9,
  0x8518, 0x8520, 0x8530, 0x850b, 0x8519, 0x852f, 0x8662, 0x8756,
  0x8763, 0x8764, 0x8777, 0x87e1, 0x8773, 0x8758, 0x8754, 0x875b,
  0x8752, 0x8761, 0x875a, 0x8751, 0x875e, 0x876d, 0x876a, 0x8750,
  0x874e, 0x875f, 0x875d, 0x876f, 0x876c, 0x877a,
  /* 0x54 */
  0x876e, 0x875c, 0x8765, 0x874f, 0x877b, 0x8775, 0x8762, 0x8767,
  0x8769, 0x885a, 0x8905, 0x890c, 0x8914, 0x890b, 0x8917, 0x8918,
  0x8919, 0x8906, 0x8916, 0x8911, 0x890e, 0x8909, 0x89a2, 0x89a4,
  0x89a3, 0x89ed, 0x89f0, 0x89ec, 0x8acf, 0x8ac6, 0x8ab8, 0x8ad3,
  0x8ad1, 0x8ad4, 0x8ad5, 0x8abb, 0x8ad7, 0x8abe, 0x8ac0, 0x8ac5,
  0x8ad8, 0x8ac3, 0x8aba, 0x8abd, 0x8ad9, 0x8c3e, 0x8c4d, 0x8c8f,
  0x8ce5, 0x8cdf, 0x8cd9, 0x8ce8, 0x8cda, 0x8cdd, 0x8ce7, 0x8da0,
  0x8d9c, 0x8da1, 0x8d9b, 0x8e20, 0x8e23, 0x8e25, 0x8e24, 0x8e2e,
  0x8e15, 0x8e1b, 0x8e16, 0x8e11, 0x8e19, 0x8e26, 0x8e27, 0x8e14,
  0x8e12, 0x8e18, 0x8e13, 0x8e1c, 0x8e17, 0x8e1a, 0x8f2c, 0x8f24,
  0x8f18, 0x8f1a, 0x8f20, 0x8f23, 0x8f16, 0x8f17, 0x9073, 0x9070,
  0x906f, 0x9067, 0x906b, 0x912f, 0x912b, 0x9129,
  /* 0x55 */
  0x912a, 0x9132, 0x9126, 0x912e, 0x9185, 0x9186, 0x918a, 0x9181,
  0x9182, 0x9184, 0x9180, 0x92d0, 0x92c3, 0x92c4, 0x92c0, 0x92d9,
  0x92b6, 0x92cf, 0x92f1, 0x92df, 0x92d8, 0x92e9, 0x92d7, 0x92dd,
  0x92cc, 0x92ef, 0x92c2, 0x92e8, 0x92ca, 0x92c8, 0x92ce, 0x92e6,
  0x92cd, 0x92d5, 0x92c9, 0x92e0, 0x92de, 0x92e7, 0x92d1, 0x92d3,
  0x92b5, 0x92e1, 0x9325, 0x92c6, 0x92b4, 0x957c, 0x95ac, 0x95ab,
  0x95ae, 0x95b0, 0x96a4, 0x96a2, 0x96d3, 0x9705, 0x9708, 0x9702,
  0x975a, 0x978a, 0x978e, 0x9788, 0x97d0, 0x97cf, 0x981e, 0x981d,
  0x9826, 0x9829, 0x9828, 0x9820, 0x981b, 0x9827, 0x98b2, 0x9908,
  0x98fa, 0x9911, 0x9914, 0x9916, 0x9917, 0x9915, 0x99dc, 0x99cd,
  0x99cf, 0x99d3, 0x99d4, 0x99ce, 0x99c9, 0x99d6, 0x99d8, 0x99cb,
  0x99d7, 0x99cc, 0x9ab3, 0x9aec, 0x9aeb, 0x9af3,
  /* 0x56 */
  0x9af2, 0x9af1, 0x9b46, 0x9b43, 0x9b67, 0x9b74, 0x9b71, 0x9b66,
  0x9b76, 0x9b75, 0x9b70, 0x9b68, 0x9b64, 0x9b6c, 0x9cfc, 0x9cfa,
  0x9cfd, 0x9cff, 0x9cf7, 0x9d07, 0x9d00, 0x9cf9, 0x9cfb, 0x9d08,
  0x9d05, 0x9d04, 0x9e83, 0x9ed3, 0x9f0f, 0x9f10, 0x511c, 0x5113,
  0x5117, 0x511a, 0x5111, 0x51de, 0x5334, 0x53e1, 0x5670, 0x5660,
  0x566e, 0x5673, 0x5666, 0x5663, 0x566d, 0x5672, 0x565e, 0x5677,
  0x571c, 0x571b, 0x58c8, 0x58bd, 0x58c9, 0x58bf, 0x58ba, 0x58c2,
  0x58bc, 0x58c6, 0x5b17, 0x5b19, 0x5b1b, 0x5b21, 0x5b14, 0x5b13,
  0x5b10, 0x5b16, 0x5b28, 0x5b1a, 0x5b20, 0x5b1e, 0x5bef, 0x5dac,
  0x5db1, 0x5da9, 0x5da7, 0x5db5, 0x5db0, 0x5dae, 0x5daa, 0x5da8,
  0x5db2, 0x5dad, 0x5daf, 0x5db4, 0x5e67, 0x5e68, 0x5e66, 0x5e6f,
  0x5ee9, 0x5ee7, 0x5ee6, 0x5ee8, 0x5ee5, 0x5f4b,
  /* 0x57 */
  0x5fbc, 0x5fbb, 0x619d, 0x61a8, 0x6196, 0x61c5, 0x61b4, 0x61c6,
  0x61c1, 0x61cc, 0x61ba, 0x61bf, 0x61b8, 0x618c, 0x64d7, 0x64d6,
  0x64d0, 0x64cf, 0x64c9, 0x64bd, 0x6489, 0x64c3, 0x64db, 0x64f3,
  0x64d9, 0x6533, 0x657f, 0x657c, 0x65a2, 0x66c8, 0x66be, 0x66c0,
  0x66ca, 0x66cb, 0x66cf, 0x66bd, 0x66bb, 0x66ba, 0x66cc, 0x6723,
  0x6a34, 0x6a66, 0x6a49, 0x6a67, 0x6a32, 0x6a68, 0x6a3e, 0x6a5d,
  0x6a6d, 0x6a76, 0x6a5b, 0x6a51, 0x6a28, 0x6a5a, 0x6a3b, 0x6a3f,
  0x6a41, 0x6a6a, 0x6a64, 0x6a50, 0x6a4f, 0x6a54, 0x6a6f, 0x6a69,
  0x6a60, 0x6a3c, 0x6a5e, 0x6a56, 0x6a55, 0x6a4d, 0x6a4e, 0x6a46,
  0x6b55, 0x6b54, 0x6b56, 0x6ba7, 0x6baa, 0x6bab, 0x6bc8, 0x6bc7,
  0x6c04, 0x6c03, 0x6c06, 0x6fad, 0x6fcb, 0x6fa3, 0x6fc7, 0x6fbc,
  0x6fce, 0x6fc8, 0x6f5e, 0x6fc4, 0x6fbd, 0x6f9e,
  /* 0x58 */
  0x6fca, 0x6fa8, 0x7004, 0x6fa5, 0x6fae, 0x6fba, 0x6fac, 0x6faa,
  0x6fcf, 0x6fbf, 0x6fb8, 0x6fa2, 0x6fc9, 0x6fab, 0x6fcd, 0x6faf,
  0x6fb2, 0x6fb0, 0x71c5, 0x71c2, 0x71bf, 0x71b8, 0x71d6, 0x71c0,
  0x71c1, 0x71cb, 0x71d4, 0x71ca, 0x71c7, 0x71cf, 0x71bd, 0x71d8,
  0x71bc, 0x71c6, 0x71da, 0x71db, 0x729d, 0x729e, 0x7369, 0x7366,
  0x7367, 0x736c, 0x7365, 0x736b, 0x736a, 0x747f, 0x749a, 0x74a0,
  0x7494, 0x7492, 0x7495, 0x74a1, 0x750b, 0x7580, 0x762f, 0x762d,
  0x7631, 0x763d, 0x7633, 0x763c, 0x7635, 0x7632, 0x7630, 0x76bb,
  0x76e6, 0x779a, 0x779d, 0x77a1, 0x779c, 0x779b, 0x77a2, 0x77a3,
  0x7795, 0x7799, 0x7797, 0x78dd, 0x78e9, 0x78e5, 0x78ea, 0x78de,
  0x78e3, 0x78db, 0x78e1, 0x78e2, 0x78ed, 0x78df, 0x78e0, 0x79a4,
  0x7a44, 0x7a48, 0x7a47, 0x7ab6, 0x7ab8, 0x7ab5,
  /* 0x59 */
  0x7ab1, 0x7ab7, 0x7bde, 0x7be3, 0x7be7, 0x7bdd, 0x7bd5, 0x7be5,
  0x7bda, 0x7be8, 0x7bf9, 0x7bd4, 0x7bea, 0x7be2, 0x7bdc, 0x7beb,
  0x7bd8, 0x7bdf, 0x7cd2, 0x7cd4, 0x7cd7, 0x7cd0, 0x7cd1, 0x7e12,
  0x7e21, 0x7e17, 0x7e0c, 0x7e1f, 0x7e20, 0x7e13, 0x7e0e, 0x7e1c,
  0x7e15, 0x7e1a, 0x7e22, 0x7e0b, 0x7e0f, 0x7e16, 0x7e0d, 0x7e14,
  0x7e25, 0x7e24, 0x7f43, 0x7f7b, 0x7f7c, 0x7f7a, 0x7fb1, 0x7fef,
  0x802a, 0x8029, 0x806c, 0x81b1, 0x81a6, 0x81ae, 0x81b9, 0x81b5,
  0x81ab, 0x81b0, 0x81ac, 0x81b4, 0x81b2, 0x81b7, 0x81a7, 0x81f2,
  0x8255, 0x8256, 0x8257, 0x8556, 0x8545, 0x856b, 0x854d, 0x8553,
  0x8561, 0x8558, 0x8540, 0x8546, 0x8564, 0x8541, 0x8562, 0x8544,
  0x8551, 0x8547, 0x8563, 0x853e, 0x855b, 0x8571, 0x854e, 0x856e,
  0x8575, 0x8555, 0x8567, 0x8560, 0x858c, 0x8566,
  /* 0x5a */
  0x855d, 0x8554, 0x8565, 0x856c, 0x8663, 0x8665, 0x8664, 0x87a4,
  0x879b, 0x878f, 0x8797, 0x8793, 0x8792, 0x8788, 0x8781, 0x8796,
  0x8798, 0x8779, 0x8787, 0x87a3, 0x8785, 0x8790, 0x8791, 0x879d,
  0x8784, 0x8794, 0x879c, 0x879a, 0x8789, 0x891e, 0x8926, 0x8930,
  0x892d, 0x892e, 0x8927, 0x8931, 0x8922, 0x8929, 0x8923, 0x892f,
  0x892c, 0x891f, 0x89f1, 0x8ae0, 0x8ae2, 0x8af2, 0x8af4, 0x8af5,
  0x8add, 0x8b14, 0x8ae4, 0x8adf, 0x8af0, 0x8ac8, 0x8ade, 0x8ae1,
  0x8ae8, 0x8aff, 0x8aef, 0x8afb, 0x8c91, 0x8c92, 0x8c90, 0x8cf5,
  0x8cee, 0x8cf1, 0x8cf0, 0x8cf3, 0x8d6c, 0x8d6e, 0x8da5, 0x8da7,
  0x8e33, 0x8e3e, 0x8e38, 0x8e40, 0x8e45, 0x8e36, 0x8e3c, 0x8e3d,
  0x8e41, 0x8e30, 0x8e3f, 0x8ebd, 0x8f36, 0x8f2e, 0x8f35, 0x8f32,
  0x8f39, 0x8f37, 0x8f34, 0x9076, 0x9079, 0x907b,
  /* 0x5b */
  0x9086, 0x90fa, 0x9133, 0x9135, 0x9136, 0x9193, 0x9190, 0x9191,
  0x918d, 0x918f, 0x9327, 0x931e, 0x9308, 0x931f, 0x9306, 0x930f,
  0x937a, 0x9338, 0x933c, 0x931b, 0x9323, 0x9312, 0x9301, 0x9346,
  0x932d, 0x930e, 0x930d, 0x92cb, 0x931d, 0x92fa, 0x9313, 0x92f9,
  0x92f7, 0x9334, 0x9302, 0x9324, 0x92ff, 0x9329, 0x9339, 0x9335,
  0x932a, 0x9314, 0x930c, 0x930b, 0x92fe, 0x9309, 0x9300, 0x92fb,
  0x9316, 0x95bc, 0x95cd, 0x95be, 0x95b9, 0x95ba, 0x95b6, 0x95bf,
  0x95b5, 0x95bd, 0x96a9, 0x96d4, 0x970b, 0x9712, 0x9710, 0x9799,
  0x9797, 0x9794, 0x97f0, 0x97f8, 0x9835, 0x982f, 0x9832, 0x9924,
  0x991f, 0x9927, 0x9929, 0x999e, 0x99ee, 0x99ec, 0x99e5, 0x99e4,
  0x99f0, 0x99e3, 0x99ea, 0x99e9, 0x99e7, 0x9ab9, 0x9abf, 0x9ab4,
  0x9abb, 0x9af6, 0x9afa, 0x9af9, 0x9af7, 0x9b33,
  /* 0x5c */
  0x9b80, 0x9b85, 0x9b87, 0x9b7c, 0x9b7e, 0x9b7b, 0x9b82, 0x9b93,
  0x9b92, 0x9b90, 0x9b7a, 0x9b95, 0x9b7d, 0x9b88, 0x9d25, 0x9d17,
  0x9d20, 0x9d1e, 0x9d14, 0x9d29, 0x9d1d, 0x9d18, 0x9d22, 0x9d10,
  0x9d19, 0x9d1f, 0x9e88, 0x9e86, 0x9e87, 0x9eae, 0x9ead, 0x9ed5,
  0x9ed6, 0x9efa, 0x9f12, 0x9f3d, 0x5126, 0x5125, 0x5122, 0x5124,
  0x5120, 0x5129, 0x52f4, 0x5693, 0x568c, 0x568d, 0x5686, 0x5684,
  0x5683, 0x567e, 0x5682, 0x567f, 0x5681, 0x58d6, 0x58d4, 0x58cf,
  0x58d2, 0x5b2d, 0x5b25, 0x5b32, 0x5b23, 0x5b2c, 0x5b27, 0x5b26,
  0x5b2f, 0x5b2e, 0x5b7b, 0x5bf1, 0x5bf2, 0x5db7, 0x5e6c, 0x5e6a,
  0x5fbe, 0x61c3, 0x61b5, 0x61bc, 0x61e7, 0x61e0, 0x61e5, 0x61e4,
  0x61e8, 0x61de, 0x64ef, 0x64e9, 0x64e3, 0x64eb, 0x64e4, 0x64e8,
  0x6581, 0x6580, 0x65b6, 0x65da, 0x66d2, 0x6a8d,
  /* 0x5d */
  0x6a96, 0x6a81, 0x6aa5, 0x6a89, 0x6a9f, 0x6a9b, 0x6aa1, 0x6a9e,
  0x6a87, 0x6a93, 0x6a8e, 0x6a95, 0x6a83, 0x6aa8, 0x6aa4, 0x6a91,
  0x6a7f, 0x6aa6, 0x6a9a, 0x6a85, 0x6a8c, 0x6a92, 0x6b5b, 0x6bad,
  0x6c09, 0x6fcc, 0x6fa9, 0x6ff4, 0x6fd4, 0x6fe3, 0x6fdc, 0x6fed,
  0x6fe7, 0x6fe6, 0x6fde, 0x6ff2, 0x6fdd, 0x6fe2, 0x6fe8, 0x71e1,
  0x71f1, 0x71e8, 0x71f2, 0x71e4, 0x71f0, 0x71e2, 0x7373, 0x736e,
  0x736f, 0x7497, 0x74b2, 0x74ab, 0x7490, 0x74aa, 0x74ad, 0x74b1,
  0x74a5, 0x74af, 0x7510, 0x7511, 0x7512, 0x750f, 0x7584, 0x7643,
  0x7648, 0x7649, 0x7647, 0x76a4, 0x76e9, 0x77b5, 0x77ab, 0x77b2,
  0x77b7, 0x77b6, 0x77b4, 0x77b1, 0x77a8, 0x77f0, 0x78f3, 0x78fd,
  0x7902, 0x78fb, 0x78fc, 0x78ff, 0x78f2, 0x7905, 0x78f9, 0x78fe,
  0x7904, 0x79ab, 0x79a8, 0x7a5c, 0x7a5b, 0x7a56,
  /* 0x5e */
  0x7a58, 0x7a54, 0x7a5a, 0x7abe, 0x7ac0, 0x7ac1, 0x7c05, 0x7c0f,
  0x7bf2, 0x7c00, 0x7bff, 0x7bfb, 0x7c0e, 0x7bf4, 0x7c0b, 0x7bf3,
  0x7c02, 0x7c09, 0x7c03, 0x7c01, 0x7bf8, 0x7bfd, 0x7c06, 0x7bf0,
  0x7bf1, 0x7c10, 0x7c0a, 0x7ce8, 0x7e2d, 0x7e3c, 0x7e42, 0x7e33,
  0x9848, 0x7e38, 0x7e2a, 0x7e49, 0x7e40, 0x7e47, 0x7e29, 0x7e4c,
  0x7e30, 0x7e3b, 0x7e36, 0x7e44, 0x7e3a, 0x7f45, 0x7f7f, 0x7f7e,
  0x7f7d, 0x7ff4, 0x7ff2, 0x802c, 0x81bb, 0x81c4, 0x81cc, 0x81ca,
  0x81c5, 0x81c7, 0x81bc, 0x81e9, 0x825b, 0x825a, 0x825c, 0x8583,
  0x8580, 0x858f, 0x85a7, 0x8595, 0x85a0, 0x858b, 0x85a3, 0x857b,
  0x85a4, 0x859a, 0x859e, 0x8577, 0x857c, 0x8589, 0x85a1, 0x857a,
  0x8578, 0x8557, 0x858e, 0x8596, 0x8586, 0x858d, 0x8599, 0x859d,
  0x8581, 0x85a2, 0x8582, 0x8588, 0x8585, 0x8579,
  /* 0x5f */
  0x8576, 0x8598, 0x8590, 0x859f, 0x8668, 0x87be, 0x87aa, 0x87ad,
  0x87c5, 0x87b0, 0x87ac, 0x87b9, 0x87b5, 0x87bc, 0x87ae, 0x87c9,
  0x87c3, 0x87c2, 0x87cc, 0x87b7, 0x87af, 0x87c4, 0x87ca, 0x87b4,
  0x87b6, 0x87bf, 0x87b8, 0x87bd, 0x87de, 0x87b2, 0x8935, 0x8933,
  0x893c, 0x893e, 0x8941, 0x8952, 0x8937, 0x8942, 0x89ad, 0x89af,
  0x89ae, 0x89f2, 0x89f3, 0x8b1e, 0x8b18, 0x8b16, 0x8b11, 0x8b05,
  0x8b0b, 0x8b22, 0x8b0f, 0x8b12, 0x8b15, 0x8b07, 0x8b0d, 0x8b08,
  0x8b06, 0x8b1c, 0x8b13, 0x8b1a, 0x8c4f, 0x8c70, 0x8c72, 0x8c71,
  0x8c6f, 0x8c95, 0x8c94, 0x8cf9, 0x8d6f, 0x8e4e, 0x8e4d, 0x8e53,
  0x8e50, 0x8e4c, 0x8e47, 0x8f43, 0x8f40, 0x9085, 0x907e, 0x9138,
  0x919a, 0x91a2, 0x919b, 0x9199, 0x919f, 0x91a1, 0x919d, 0x91a0,
  0x93a1, 0x9383, 0x93af, 0x9364, 0x9356, 0x9347,
  /* 0x60 */
  0x937c, 0x9358, 0x935c, 0x9376, 0x9349, 0x9350, 0x9351, 0x9360,
  0x936d, 0x938f, 0x934c, 0x936a, 0x9379, 0x9357, 0x9355, 0x9352,
  0x934f, 0x9371, 0x9377, 0x937b, 0x9361, 0x935e, 0x9363, 0x9367,
  0x934e, 0x9359, 0x95c7, 0x95c0, 0x95c9, 0x95c3, 0x95c5, 0x95b7,
  0x96ae, 0x96b0, 0x96ac, 0x9720, 0x971f, 0x9718, 0x971d, 0x9719,
  0x979a, 0x97a1, 0x979c, 0x979e, 0x979d, 0x97d5, 0x97d4, 0x97f1,
  0x9841, 0x9844, 0x984a, 0x9849, 0x9845, 0x9843, 0x9925, 0x992b,
  0x992c, 0x992a, 0x9933, 0x9932, 0x992f, 0x992d, 0x9931, 0x9930,
  0x9998, 0x99a3, 0x99a1, 0x9a02, 0x99fa, 0x99f4, 0x99f7, 0x99f9,
  0x99f8, 0x99f6, 0x99fb, 0x99fd, 0x99fe, 0x99fc, 0x9a03, 0x9abe,
  0x9afe, 0x9afd, 0x9b01, 0x9afc, 0x9b48, 0x9b9a, 0x9ba8, 0x9b9e,
  0x9b9b, 0x9ba6, 0x9ba1, 0x9ba5, 0x9ba4, 0x9b86,
  /* 0x61 */
  0x9ba2, 0x9ba0, 0x9baf, 0x9d33, 0x9d41, 0x9d67, 0x9d36, 0x9d2e,
  0x9d2f, 0x9d31, 0x9d38, 0x9d30, 0x9d45, 0x9d42, 0x9d43, 0x9d3e,
  0x9d37, 0x9d40, 0x9d3d, 0x7ff5, 0x9d2d, 0x9e8a, 0x9e89, 0x9e8d,
  0x9eb0, 0x9ec8, 0x9eda, 0x9efb, 0x9eff, 0x9f24, 0x9f23, 0x9f22,
  0x9f54, 0x9fa0, 0x5131, 0x512d, 0x512e, 0x5698, 0x569c, 0x5697,
  0x569a, 0x569d, 0x5699, 0x5970, 0x5b3c, 0x5c69, 0x5c6a, 0x5dc0,
  0x5e6d, 0x5e6e, 0x61d8, 0x61df, 0x61ed, 0x61ee, 0x61f1, 0x61ea,
  0x61f0, 0x61eb, 0x61d6, 0x61e9, 0x64ff, 0x6504, 0x64fd, 0x64f8,
  0x6501, 0x6503, 0x64fc, 0x6594, 0x65db, 0x66da, 0x66db, 0x66d8,
  0x6ac5, 0x6ab9, 0x6abd, 0x6ae1, 0x6ac6, 0x6aba, 0x6ab6, 0x6ab7,
  0x6ac7, 0x6ab4, 0x6aad, 0x6b5e, 0x6bc9, 0x6c0b, 0x7007, 0x700c,
  0x700d, 0x7001, 0x7005, 0x7014, 0x700e, 0x6fff,
  /* 0x62 */
  0x7000, 0x6ffb, 0x7026, 0x6ffc, 0x6ff7, 0x700a, 0x7201, 0x71ff,
  0x71f9, 0x7203, 0x71fd, 0x7376, 0x74b8, 0x74c0, 0x74b5, 0x74c1,
  0x74be, 0x74b6, 0x74bb, 0x74c2, 0x7514, 0x7513, 0x765c, 0x7664,
  0x7659, 0x7650, 0x7653, 0x7657, 0x765a, 0x76a6, 0x76bd, 0x76ec,
  0x77c2, 0x77ba, 0x790c, 0x7913, 0x7914, 0x7909, 0x7910, 0x7912,
  0x7911, 0x79ad, 0x79ac, 0x7a5f, 0x7c1c, 0x7c29, 0x7c19, 0x7c20,
  0x7c1f, 0x7c2d, 0x7c1d, 0x7c26, 0x7c28, 0x7c22, 0x7c25, 0x7c30,
  0x7e5c, 0x7e50, 0x7e56, 0x7e63, 0x7e58, 0x7e62, 0x7e5f, 0x7e51,
  0x7e60, 0x7e57, 0x7e53, 0x7fb5, 0x7fb3, 0x7ff7, 0x7ff8, 0x8075,
  0x81d1, 0x81d2, 0x81d0, 0x825f, 0x825e, 0x85b4, 0x85c6, 0x85c0,
  0x85c3, 0x85c2, 0x85b3, 0x85b5, 0x85bd, 0x85c7, 0x85c4, 0x85bf,
  0x85cb, 0x85ce, 0x85c8, 0x85c5, 0x85b1, 0x85b6,
  /* 0x63 */
  0x85d2, 0x8624, 0x85b8, 0x85b7, 0x85be, 0x8669, 0x87e7, 0x87e6,
  0x87e2, 0x87db, 0x87eb, 0x87ea, 0x87e5, 0x87df, 0x87f3, 0x87e4,
  0x87d4, 0x87dc, 0x87d3, 0x87ed, 0x87d8, 0x87e3, 0x87d7, 0x87d9,
  0x8801, 0x87f4, 0x87e8, 0x87dd, 0x8953, 0x894b, 0x894f, 0x894c,
  0x8946, 0x8950, 0x8951, 0x8949, 0x8b2a, 0x8b27, 0x8b23, 0x8b33,
  0x8b30, 0x8b35, 0x8b47, 0x8b2f, 0x8b3c, 0x8b3e, 0x8b31, 0x8b25,
  0x8b37, 0x8b26, 0x8b36, 0x8b2e, 0x8b24, 0x8b3b, 0x8b3d, 0x8b3a,
  0x8c42, 0x8c75, 0x8c99, 0x8c98, 0x8c97, 0x8cfe, 0x8d04, 0x8d02,
  0x8d00, 0x8e5c, 0x8e62, 0x8e60, 0x8e57, 0x8e56, 0x8e5e, 0x8e65,
  0x8e67, 0x8e5b, 0x8e5a, 0x8e61, 0x8e5d, 0x8e69, 0x8e54, 0x8f46,
  0x8f47, 0x8f48, 0x8f4b, 0x9128, 0x913a, 0x913b, 0x913e, 0x91a8,
  0x91a5, 0x91a7, 0x91af, 0x91aa, 0x93b5, 0x938c,
  /* 0x64 */
  0x9392, 0x93b7, 0x939b, 0x939d, 0x9389, 0x93a7, 0x938e, 0x93aa,
  0x939e, 0x93a6, 0x9395, 0x9388, 0x9399, 0x939f, 0x9380, 0x938d,
  0x93b1, 0x9391, 0x93b2, 0x93a4, 0x93a8, 0x93b4, 0x93a3, 0x95d2,
  0x95d3, 0x95d1, 0x96b3, 0x96d7, 0x96da, 0x5dc2, 0x96df, 0x96d8,
  0x96dd, 0x9723, 0x9722, 0x9725, 0x97ac, 0x97ae, 0x97a8, 0x97ab,
  0x97a4, 0x97aa, 0x97a2, 0x97a5, 0x97d7, 0x97d9, 0x97d6, 0x97d8,
  0x97fa, 0x9850, 0x9851, 0x9852, 0x98b8, 0x9941, 0x993c, 0x993a,
  0x9a0f, 0x9a0b, 0x9a09, 0x9a0d, 0x9a04, 0x9a11, 0x9a0a, 0x9a05,
  0x9a07, 0x9a06, 0x9ac0, 0x9adc, 0x9b08, 0x9b04, 0x9b05, 0x9b29,
  0x9b35, 0x9b4a, 0x9b4c, 0x9b4b, 0x9bc7, 0x9bc6, 0x9bc3, 0x9bbf,
  0x9bc1, 0x9bb5, 0x9bb8, 0x9bd3, 0x9bb6, 0x9bc4, 0x9bb9, 0x9bbd,
  0x9d5c, 0x9d53, 0x9d4f, 0x9d4a, 0x9d5b, 0x9d4b,
  /* 0x65 */
  0x9d59, 0x9d56, 0x9d4c, 0x9d57, 0x9d52, 0x9d54, 0x9d5f, 0x9d58,
  0x9d5a, 0x9e8e, 0x9e8c, 0x9edf, 0x9f01, 0x9f00, 0x9f16, 0x9f25,
  0x9f2b, 0x9f2a, 0x9f29, 0x9f28, 0x9f4c, 0x9f55, 0x5134, 0x5135,
  0x5296, 0x52f7, 0x53b4, 0x56ab, 0x56ad, 0x56a6, 0x56a7, 0x56aa,
  0x56ac, 0x58da, 0x58dd, 0x58db, 0x5912, 0x5b3d, 0x5b3e, 0x5b3f,
  0x5dc3, 0x5e70, 0x5fbf, 0x61fb, 0x6507, 0x6510, 0x650d, 0x6509,
  0x650c, 0x650e, 0x6584, 0x65de, 0x65dd, 0x66de, 0x6ae7, 0x6ae0,
  0x6acc, 0x6ad1, 0x6ad9, 0x6acb, 0x6adf, 0x6adc, 0x6ad0, 0x6aeb,
  0x6acf, 0x6acd, 0x6ade, 0x6b60, 0x6bb0, 0x6c0c, 0x7019, 0x7027,
  0x7020, 0x7016, 0x702b, 0x7021, 0x7022, 0x7023, 0x7029, 0x7017,
  0x7024, 0x701c, 0x720c, 0x720a, 0x7207, 0x7202, 0x7205, 0x72a5,
  0x72a6, 0x72a4, 0x72a3, 0x72a1, 0x74cb, 0x74c5,
  /* 0x66 */
  0x74b7, 0x74c3, 0x7516, 0x7660, 0x77c9, 0x77ca, 0x77c4, 0x77f1,
  0x791d, 0x791b, 0x7921, 0x791c, 0x7917, 0x791e, 0x79b0, 0x7a67,
  0x7a68, 0x7c33, 0x7c3c, 0x7c39, 0x7c2c, 0x7c3b, 0x7cec, 0x7cea,
  0x7e76, 0x7e75, 0x7e78, 0x7e70, 0x7e77, 0x7e6f, 0x7e7a, 0x7e72,
  0x7e74, 0x7e68, 0x7f4b, 0x7f4a, 0x7f83, 0x7f86, 0x7fb7, 0x7ffd,
  0x7ffe, 0x8078, 0x81d7, 0x81d5, 0x820b, 0x8264, 0x8261, 0x8263,
  0x85eb, 0x85f1, 0x85ed, 0x85d9, 0x85e1, 0x85e8, 0x85da, 0x85d7,
  0x85ec, 0x85f2, 0x85f8, 0x85d8, 0x85df, 0x85e3, 0x85dc, 0x85d1,
  0x85f0, 0x85e6, 0x85ef, 0x85de, 0x85e2, 0x8800, 0x87fa, 0x8803,
  0x87f6, 0x87f7, 0x8809, 0x880c, 0x880b, 0x8806, 0x87fc, 0x8808,
  0x87ff, 0x880a, 0x8802, 0x8962, 0x895a, 0x895b, 0x8957, 0x8961,
  0x895c, 0x8958, 0x895d, 0x8959, 0x8988, 0x89b7,
  /* 0x67 */
  0x89b6, 0x89f6, 0x8b50, 0x8b48, 0x8b4a, 0x8b40, 0x8b53, 0x8b56,
  0x8b54, 0x8b4b, 0x8b55, 0x8b51, 0x8b42, 0x8b52, 0x8b57, 0x8c43,
  0x8c77, 0x8c76, 0x8c9a, 0x8d06, 0x8d07, 0x8d09, 0x8dac, 0x8daa,
  0x8dad, 0x8dab, 0x8e6d, 0x8e78, 0x8e73, 0x8e6a, 0x8e6f, 0x8e7b,
  0x8ec2, 0x8f52, 0x8f51, 0x8f4f, 0x8f50, 0x8f53, 0x8fb4, 0x9140,
  0x913f, 0x91b0, 0x91ad, 0x93de, 0x93c7, 0x93cf, 0x93c2, 0x93da,
  0x93d0, 0x93f9, 0x93ec, 0x93cc, 0x93d9, 0x93a9, 0x93e6, 0x93ca,
  0x93d4, 0x93ee, 0x93e3, 0x93d5, 0x93c4, 0x93ce, 0x93c0, 0x93d2,
  0x93a5, 0x93e7, 0x957d, 0x95da, 0x95db, 0x96e1, 0x9729, 0x972b,
  0x972c, 0x9728, 0x9726, 0x97b3, 0x97b7, 0x97b6, 0x97dd, 0x97de,
  0x97df, 0x985c, 0x9859, 0x985d, 0x9857, 0x98bf, 0x98bd, 0x98bb,
  0x98be, 0x9948, 0x9947, 0x9943, 0x99a6, 0x99a7,
  /* 0x68 */
  0x9a1a, 0x9a15, 0x9a25, 0x9a1d, 0x9a24, 0x9a1b, 0x9a22, 0x9a20,
  0x9a27, 0x9a23, 0x9a1e, 0x9a1c, 0x9a14, 0x9ac2, 0x9b0b, 0x9b0a,
  0x9b0e, 0x9b0c, 0x9b37, 0x9bea, 0x9beb, 0x9be0, 0x9bde, 0x9be4,
  0x9be6, 0x9be2, 0x9bf0, 0x9bd4, 0x9bd7, 0x9bec, 0x9bdc, 0x9bd9,
  0x9be5, 0x9bd5, 0x9be1, 0x9bda, 0x9d77, 0x9d81, 0x9d8a, 0x9d84,
  0x9d88, 0x9d71, 0x9d80, 0x9d78, 0x9d86, 0x9d8b, 0x9d8c, 0x9d7d,
  0x9d6b, 0x9d74, 0x9d75, 0x9d70, 0x9d69, 0x9d85, 0x9d73, 0x9d7b,
  0x9d82, 0x9d6f, 0x9d79, 0x9d7f, 0x9d87, 0x9d68, 0x9e94, 0x9e91,
  0x9ec0, 0x9efc, 0x9f2d, 0x9f40, 0x9f41, 0x9f4d, 0x9f56, 0x9f57,
  0x9f58, 0x5337, 0x56b2, 0x56b5, 0x56b3, 0x58e3, 0x5b45, 0x5dc6,
  0x5dc7, 0x5eee, 0x5eef, 0x5fc0, 0x5fc1, 0x61f9, 0x6517, 0x6516,
  0x6515, 0x6513, 0x65df, 0x66e8, 0x66e3, 0x66e4,
  /* 0x69 */
  0x6af3, 0x6af0, 0x6aea, 0x6ae8, 0x6af9, 0x6af1, 0x6aee, 0x6aef,
  0x703c, 0x7035, 0x702f, 0x7037, 0x7034, 0x7031, 0x7042, 0x7038,
  0x703f, 0x703a, 0x7039, 0x702a, 0x7040, 0x703b, 0x7033, 0x7041,
  0x7213, 0x7214, 0x72a8, 0x737d, 0x737c, 0x74ba, 0x76ab, 0x76aa,
  0x76be, 0x76ed, 0x77cc, 0x77ce, 0x77cf, 0x77cd, 0x77f2, 0x7925,
  0x7923, 0x7927, 0x7928, 0x7924, 0x7929, 0x79b2, 0x7a6e, 0x7a6c,
  0x7a6d, 0x7af7, 0x7c49, 0x7c48, 0x7c4a, 0x7c47, 0x7c45, 0x7cee,
  0x7e7b, 0x7e7e, 0x7e81, 0x7e80, 0x7fba, 0x7fff, 0x8079, 0x81db,
  0x81d9, 0x8268, 0x8269, 0x8622, 0x85ff, 0x8601, 0x85fe, 0x861b,
  0x8600, 0x85f6, 0x8604, 0x8609, 0x8605, 0x860c, 0x85fd, 0x8819,
  0x8810, 0x8811, 0x8817, 0x8813, 0x8816, 0x8963, 0x8966, 0x89b9,
  0x89f7, 0x8b60, 0x8b6a, 0x8b5d, 0x8b68, 0x8b63,
  /* 0x6a */
  0x8b65, 0x8b67, 0x8b6d, 0x8dae, 0x8e86, 0x8e88, 0x8e84, 0x8f59,
  0x8f56, 0x8f57, 0x8f55, 0x8f58, 0x8f5a, 0x908d, 0x9143, 0x9141,
  0x91b7, 0x91b5, 0x91b2, 0x91b3, 0x940b, 0x9413, 0x93fb, 0x9420,
  0x940f, 0x9414, 0x93fe, 0x9415, 0x9410, 0x9428, 0x9419, 0x940d,
  0x93f5, 0x9400, 0x93f7, 0x9407, 0x940e, 0x9416, 0x9412, 0x93fa,
  0x9409, 0x93f8, 0x943c, 0x940a, 0x93ff, 0x93fc, 0x940c, 0x93f6,
  0x9411, 0x9406, 0x95de, 0x95e0, 0x95df, 0x972e, 0x972f, 0x97b9,
  0x97bb, 0x97fd, 0x97fe, 0x9860, 0x9862, 0x9863, 0x985f, 0x98c1,
  0x98c2, 0x9950, 0x994e, 0x9959, 0x994c, 0x994b, 0x9953, 0x9a32,
  0x9a34, 0x9a31, 0x9a2c, 0x9a2a, 0x9a36, 0x9a29, 0x9a2e, 0x9a38,
  0x9a2d, 0x9ac7, 0x9aca, 0x9ac6, 0x9b10, 0x9b12, 0x9b11, 0x9c0b,
  0x9c08, 0x9bf7, 0x9c05, 0x9c12, 0x9bf8, 0x9c40,
  /* 0x6b */
  0x9c07, 0x9c0e, 0x9c06, 0x9c17, 0x9c14, 0x9c09, 0x9d9f, 0x9d99,
  0x9da4, 0x9d9d, 0x9d92, 0x9d98, 0x9d90, 0x9d9b, 0x9da0, 0x9d94,
  0x9d9c, 0x9daa, 0x9d97, 0x9da1, 0x9d9a, 0x9da2, 0x9da8, 0x9d9e,
  0x9da3, 0x9dbf, 0x9da9, 0x9d96, 0x9da6, 0x9da7, 0x9e99, 0x9e9b,
  0x9e9a, 0x9ee5, 0x9ee4, 0x9ee7, 0x9ee6, 0x9f30, 0x9f2e, 0x9f5b,
  0x9f60, 0x9f5e, 0x9f5d, 0x9f59, 0x9f91, 0x513a, 0x5139, 0x5298,
  0x5297, 0x56c3, 0x56bd, 0x56be, 0x5b48, 0x5b47, 0x5dcb, 0x5dcf,
  0x5ef1, 0x61fd, 0x651b, 0x6b02, 0x6afc, 0x6b03, 0x6af8, 0x6b00,
  0x7043, 0x7044, 0x704a, 0x7048, 0x7049, 0x7045, 0x7046, 0x721d,
  0x721a, 0x7219, 0x737e, 0x7517, 0x766a, 0x77d0, 0x792d, 0x7931,
  0x792f, 0x7c54, 0x7c53, 0x7cf2, 0x7e8a, 0x7e87, 0x7e88, 0x7e8b,
  0x7e86, 0x7e8d, 0x7f4d, 0x7fbb, 0x8030, 0x81dd,
  /* 0x6c */
  0x8618, 0x862a, 0x8626, 0x861f, 0x8623, 0x861c, 0x8619, 0x8627,
  0x862e, 0x8621, 0x8620, 0x8629, 0x861e, 0x8625, 0x8829, 0x881d,
  0x881b, 0x8820, 0x8824, 0x881c, 0x882b, 0x884a, 0x896d, 0x8969,
  0x896e, 0x896b, 0x89fa, 0x8b79, 0x8b78, 0x8b45, 0x8b7a, 0x8b7b,
  0x8d10, 0x8d14, 0x8daf, 0x8e8e, 0x8e8c, 0x8f5e, 0x8f5b, 0x8f5d,
  0x9146, 0x9144, 0x9145, 0x91b9, 0x943f, 0x943b, 0x9436, 0x9429,
  0x943d, 0x9430, 0x9439, 0x942a, 0x9437, 0x942c, 0x9440, 0x9431,
  0x95e5, 0x95e4, 0x95e3, 0x9735, 0x973a, 0x97bf, 0x97e1, 0x9864,
  0x98c9, 0x98c6, 0x98c0, 0x9958, 0x9956, 0x9a39, 0x9a3d, 0x9a46,
  0x9a44, 0x9a42, 0x9a41, 0x9a3a, 0x9a3f, 0x9acd, 0x9b15, 0x9b17,
  0x9b18, 0x9b16, 0x9b3a, 0x9b52, 0x9c2b, 0x9c1d, 0x9c1c, 0x9c2c,
  0x9c23, 0x9c28, 0x9c29, 0x9c24, 0x9c21, 0x9db7,
  /* 0x6d */
  0x9db6, 0x9dbc, 0x9dc1, 0x9dc7, 0x9dca, 0x9dcf, 0x9dbe, 0x9dc5,
  0x9dc3, 0x9dbb, 0x9db5, 0x9dce, 0x9db9, 0x9dba, 0x9dac, 0x9dc8,
  0x9db1, 0x9dad, 0x9dcc, 0x9db3, 0x9dcd, 0x9db2, 0x9e7a, 0x9e9c,
  0x9eeb, 0x9eee, 0x9eed, 0x9f1b, 0x9f18, 0x9f1a, 0x9f31, 0x9f4e,
  0x9f65, 0x9f64, 0x9f92, 0x4eb9, 0x56c6, 0x56c5, 0x56cb, 0x5971,
  0x5b4b, 0x5b4c, 0x5dd5, 0x5dd1, 0x5ef2, 0x6521, 0x6520, 0x6526,
  0x6522, 0x6b0b, 0x6b08, 0x6b09, 0x6c0d, 0x7055, 0x7056, 0x7057,
  0x7052, 0x721e, 0x721f, 0x72a9, 0x737f, 0x74d8, 0x74d5, 0x74d9,
  0x74d7, 0x766d, 0x76ad, 0x7935, 0x79b4, 0x7a70, 0x7a71, 0x7c57,
  0x7c5c, 0x7c59, 0x7c5b, 0x7c5a, 0x7cf4, 0x7cf1, 0x7e91, 0x7f4f,
  0x7f87, 0x81de, 0x826b, 0x8634, 0x8635, 0x8633, 0x862c, 0x8632,
  0x8636, 0x882c, 0x8828, 0x8826, 0x882a, 0x8825,
  /* 0x6e */
  0x8971, 0x89bf, 0x89be, 0x89fb, 0x8b7e, 0x8b84, 0x8b82, 0x8b86,
  0x8b85, 0x8b7f, 0x8d15, 0x8e95, 0x8e94, 0x8e9a, 0x8e92, 0x8e90,
  0x8e96, 0x8e97, 0x8f60, 0x8f62, 0x9147, 0x944c, 0x9450, 0x944a,
  0x944b, 0x944f, 0x9447, 0x9445, 0x9448, 0x9449, 0x9446, 0x973f,
  0x97e3, 0x986a, 0x9869, 0x98cb, 0x9954, 0x995b, 0x9a4e, 0x9a53,
  0x9a54, 0x9a4c, 0x9a4f, 0x9a48, 0x9a4a, 0x9a49, 0x9a52, 0x9a50,
  0x9ad0, 0x9b19, 0x9b2b, 0x9b3b, 0x9b56, 0x9b55, 0x9c46, 0x9c48,
  0x9c3f, 0x9c44, 0x9c39, 0x9c33, 0x9c41, 0x9c3c, 0x9c37, 0x9c34,
  0x9c32, 0x9c3d, 0x9c36, 0x9ddb, 0x9dd2, 0x9dde, 0x9dda, 0x9dcb,
  0x9dd0, 0x9ddc, 0x9dd1, 0x9ddf, 0x9de9, 0x9dd9, 0x9dd8, 0x9dd6,
  0x9df5, 0x9dd5, 0x9ddd, 0x9eb6, 0x9ef0, 0x9f35, 0x9f33, 0x9f32,
  0x9f42, 0x9f6b, 0x9f95, 0x9fa2, 0x513d, 0x5299,
  /* 0x6f */
  0x58e8, 0x58e7, 0x5972, 0x5b4d, 0x5dd8, 0x882f, 0x5f4f, 0x6201,
  0x6203, 0x6204, 0x6529, 0x6525, 0x6596, 0x66eb, 0x6b11, 0x6b12,
  0x6b0f, 0x6bca, 0x705b, 0x705a, 0x7222, 0x7382, 0x7381, 0x7383,
  0x7670, 0x77d4, 0x7c67, 0x7c66, 0x7e95, 0x826c, 0x863a, 0x8640,
  0x8639, 0x863c, 0x8631, 0x863b, 0x863e, 0x8830, 0x8832, 0x882e,
  0x8833, 0x8976, 0x8974, 0x8973, 0x89fe, 0x8b8c, 0x8b8e, 0x8b8b,
  0x8b88, 0x8c45, 0x8d19, 0x8e98, 0x8f64, 0x8f63, 0x91bc, 0x9462,
  0x9455, 0x945d, 0x9457, 0x945e, 0x97c4, 0x97c5, 0x9800, 0x9a56,
  0x9a59, 0x9b1e, 0x9b1f, 0x9b20, 0x9c52, 0x9c58, 0x9c50, 0x9c4a,
  0x9c4d, 0x9c4b, 0x9c55, 0x9c59, 0x9c4c, 0x9c4e, 0x9dfb, 0x9df7,
  0x9def, 0x9de3, 0x9deb, 0x9df8, 0x9de4, 0x9df6, 0x9de1, 0x9dee,
  0x9de6, 0x9df2, 0x9df0, 0x9de2, 0x9dec, 0x9df4,
  /* 0x70 */
  0x9df3, 0x9de8, 0x9ded, 0x9ec2, 0x9ed0, 0x9ef2, 0x9ef3, 0x9f06,
  0x9f1c, 0x9f38, 0x9f37, 0x9f36, 0x9f43, 0x9f4f, 0x9f71, 0x9f70,
  0x9f6e, 0x9f6f, 0x56d3, 0x56cd, 0x5b4e, 0x5c6d, 0x652d, 0x66ed,
  0x66ee, 0x6b13, 0x705f, 0x7061, 0x705d, 0x7060, 0x7223, 0x74db,
  0x74e5, 0x77d5, 0x7938, 0x79b7, 0x79b6, 0x7c6a, 0x7e97, 0x7f89,
  0x826d, 0x8643, 0x8838, 0x8837, 0x8835, 0x884b, 0x8b94, 0x8b95,
  0x8e9e, 0x8e9f, 0x8ea0, 0x8e9d, 0x91be, 0x91bd, 0x91c2, 0x946b,
  0x9468, 0x9469, 0x96e5, 0x9746, 0x9743, 0x9747, 0x97c7, 0x97e5,
  0x9a5e, 0x9ad5, 0x9b59, 0x9c63, 0x9c67, 0x9c66, 0x9c62, 0x9c5e,
  0x9c60, 0x9e02, 0x9dfe, 0x9e07, 0x9e03, 0x9e06, 0x9e05, 0x9e00,
  0x9e01, 0x9e09, 0x9dff, 0x9dfd, 0x9e04, 0x9ea0, 0x9f1e, 0x9f46,
  0x9f74, 0x9f75, 0x9f76, 0x56d4, 0x652e, 0x65b8,
  /* 0x71 */
  0x6b18, 0x6b19, 0x6b17, 0x6b1a, 0x7062, 0x7226, 0x72aa, 0x77d8,
  0x77d9, 0x7939, 0x7c69, 0x7c6b, 0x7cf6, 0x7e9a, 0x7e98, 0x7e9b,
  0x7e99, 0x81e0, 0x81e1, 0x8646, 0x8647, 0x8648, 0x8979, 0x897a,
  0x897c, 0x897b, 0x89ff, 0x8b98, 0x8b99, 0x8ea5, 0x8ea4, 0x8ea3,
  0x946e, 0x946d, 0x946f, 0x9471, 0x9473, 0x9749, 0x9872, 0x995f,
  0x9c68, 0x9c6e, 0x9c6d, 0x9e0b, 0x9e0d, 0x9e10, 0x9e0f, 0x9e12,
  0x9e11, 0x9ea1, 0x9ef5, 0x9f09, 0x9f47, 0x9f78, 0x9f7b, 0x9f7a,
  0x9f79, 0x571e, 0x7066, 0x7c6f, 0x883c, 0x8db2, 0x8ea6, 0x91c3,
  0x9474, 0x9478, 0x9476, 0x9475, 0x9a60, 0x9b2e, 0x9c74, 0x9c73,
  0x9c71, 0x9c75, 0x9e14, 0x9e13, 0x9ef6, 0x9f0a, 0x9fa4, 0x7068,
  0x7065, 0x7cf7, 0x866a, 0x883e, 0x883d, 0x883f, 0x8b9e, 0x8c9c,
  0x8ea9, 0x8ec9, 0x974b, 0x9873, 0x9874, 0x98cc,
  /* 0x72 */
  0x9961, 0x99ab, 0x9a64, 0x9a66, 0x9a67, 0x9b24, 0x9e15, 0x9e17,
  0x9f48, 0x6207, 0x6b1e, 0x7227, 0x864c, 0x8ea8, 0x9482, 0x9480,
  0x9481, 0x9a69, 0x9a68, 0x9e19, 0x864b, 0x8b9f, 0x9483, 0x9c79,
  0x9eb7, 0x7675, 0x9a6b, 0x9c7a, 0x9e1d, 0x7069, 0x706a, 0x7229,
  0x9ea4, 0x9f7e, 0x9f49, 0x9f98,
};

static int
cns11643_2_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x72)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        unsigned short wc = 0xfffd;
        {
          if (i < 7650)
            wc = cns11643_2_2uni_page21[i];
        }
        if (wc != 0xfffd) {
          *pwc = (ucs4_t) wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

