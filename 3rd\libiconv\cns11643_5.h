/*
 * Copyright (C) 1999-2002, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 5
 */

static const unsigned short cns11643_5_2uni_page21[8603] = {
  /* 0x21 */
  0x3bd1, 0x3bcb, 0x3bc9, 0x3c0c, 0x3b00, 0x3b87, 0x3c0d, 0x3c0f,
  0xe21d, 0x5e98, 0x3bd2, 0x3c11, 0x3b7e, 0x45d3, 0x5052, 0x57fe,
  0x60a9, 0x7442, 0x3b09, 0x3bd6, 0x3fdd, 0x3fdc, 0x4002, 0x4073,
  0x4176, 0x41a7, 0x41a8, 0x4333, 0x43cc, 0x450d, 0x452c, 0x461b,
  0x015a, 0x461d, 0x4699, 0x4d3e, 0x582d, 0x582f, 0x5e99, 0x6eb3,
  0x74b6, 0x761b, 0x7fef, 0x83e9, 0x3b14, 0x3b94, 0x3be1, 0x4006,
  0x404a, 0x4044, 0x4131, 0x417a, 0x41ad, 0x41ae, 0x41b1, 0x4338,
  0x4337, 0x447d, 0x447e, 0x452d, 0x4532, 0x4623, 0x4626, 0xe23a,
  0x46a0, 0x51a8, 0x543f, 0x54bc, 0x56c4, 0x583b, 0x5b19, 0x5b18,
  0x5cb3, 0x5eaa, 0x5fbe, 0x60ac, 0x6525, 0x6566, 0x656b, 0x7443,
  0x74b5, 0x75ec, 0x7776, 0x08bc, 0x8114, 0x50dc, 0x821c, 0x821d,
  0x9fd0, 0xa112, 0xa5f3, 0xc169, 0x15b9, 0xc911,
  /* 0x22 */
  0xc915, 0xc910, 0xc913, 0x3ba4, 0x3ba5, 0x3cd0, 0x3cba, 0x3ccb,
  0x407b, 0x4186, 0x41bb, 0x41ba, 0x41c3, 0x41b9, 0x41c0, 0x00de,
  0x4339, 0x433b, 0x4341, 0x43d1, 0x43cf, 0x43d2, 0x442e, 0x446e,
  0x448c, 0x4488, 0x4535, 0x0143, 0x453a, 0x453b, 0x4538, 0x4539,
  0x462e, 0x462c, 0x46c8, 0x46b5, 0x46ad, 0x46b0, 0x46ab, 0x4cb3,
  0x4ca9, 0x51bb, 0x51b9, 0x565a, 0x56cd, 0x572b, 0x5805, 0x03ac,
  0x03aa, 0x5851, 0x584b, 0x5aea, 0x5aed, 0x5b36, 0x5b62, 0x5b56,
  0x5b55, 0x5b64, 0x5b58, 0x1c07, 0x5cda, 0x5cd8, 0x5ead, 0x5eab,
  0x5fc0, 0x5fc3, 0x60b3, 0x60b7, 0x60ba, 0x1dcb, 0x04fe, 0x60c7,
  0x60b5, 0x648e, 0x6573, 0x656c, 0x6574, 0xe2b4, 0x6572, 0x6571,
  0x6582, 0x6570, 0x6c3f, 0x6eba, 0x6eb8, 0x74b7, 0x8223, 0x8221,
  0x822b, 0x8226, 0x8222, 0x8825, 0x8be4, 0x8f15,
  /* 0x23 */
  0x9fe4, 0xa133, 0xa12a, 0xa132, 0xa129, 0xa5fb, 0xb45b, 0xb45c,
  0xb728, 0xc16f, 0xc16d, 0xc16b, 0xc16e, 0xc916, 0x3b22, 0x3c16,
  0x3c48, 0x3cd8, 0x3d00, 0x0041, 0x3f80, 0x3fe4, 0x00b4, 0x3fe7,
  0x400c, 0x4053, 0x40b7, 0x45e8, 0x41d1, 0x41e5, 0x41d8, 0x41d6,
  0x41da, 0x41d9, 0x41d5, 0x41e6, 0x41d4, 0x41d3, 0x4344, 0x43d6,
  0x43d7, 0x4413, 0x3afd, 0x4470, 0x4492, 0x44db, 0x44da, 0x4541,
  0x4543, 0x4633, 0x46f6, 0x46d4, 0x46dd, 0x46d0, 0x4704, 0x46d9,
  0x46db, 0x4705, 0x46d3, 0x46e1, 0x4d80, 0x4d77, 0x4d81, 0x4d82,
  0x4d83, 0xe25c, 0x5061, 0x5060, 0x51d8, 0x51d6, 0x51d7, 0x8105,
  0x544d, 0x5448, 0x56d6, 0x56cf, 0x56d9, 0x56d8, 0x573d, 0x03b3,
  0x5854, 0x585b, 0x585c, 0x03b4, 0x5859, 0x5858, 0x585a, 0x5855,
  0x5856, 0x5882, 0x5af0, 0x5b1c, 0x5b6a, 0x5b69,
  /* 0x24 */
  0x5b71, 0x5b67, 0x5b6c, 0x5b6e, 0x5b79, 0x5c8b, 0x5cb8, 0x5ce7,
  0x5ce8, 0x5ce4, 0x5ce6, 0x5ce5, 0x5cf0, 0x5e83, 0x5eb7, 0x5ebb,
  0x5eb9, 0x5ec5, 0x5f53, 0x5fc4, 0x5fc6, 0x5fcb, 0x60c8, 0xe29f,
  0x60c9, 0x60db, 0x6494, 0x6595, 0x6588, 0x658d, 0x69ec, 0x69ee,
  0x69f1, 0x6c2e, 0x6c49, 0x6e5f, 0x6ed1, 0x6ecb, 0x7385, 0x081d,
  0x744a, 0x7447, 0x744b, 0x74bb, 0x74c4, 0x087c, 0x7602, 0x7782,
  0x7791, 0x778f, 0x7792, 0x7c8f, 0x7c91, 0x7cb4, 0x7c92, 0x7ff4,
  0x80aa, 0x8235, 0x8237, 0x823d, 0x823c, 0x822f, 0x8230, 0x83f8,
  0x83ef, 0x8626, 0x0b55, 0x8826, 0x8827, 0x8a41, 0x8ac6, 0x8ac7,
  0x8beb, 0x8be8, 0x8bea, 0x8f1a, 0x8f19, 0x0d26, 0x9423, 0x9424,
  0x9925, 0xa135, 0xa134, 0xa507, 0xad1d, 0xb2e1, 0xb6ab, 0xbf60,
  0xc177, 0xc17d, 0x15bc, 0xc91e, 0xc91a, 0xc91d,
  /* 0x25 */
  0x3b6b, 0x3c2d, 0x3c4c, 0x3d2a, 0x004a, 0x3d15, 0x3d2c, 0x3d06,
  0x3d08, 0x3d0a, 0x004e, 0x4056, 0x4084, 0x00c9, 0x40c6, 0x41f5,
  0x4209, 0x41f8, 0x41e8, 0x41fb, 0x41e9, 0x41f6, 0x1ab7, 0x1ab8,
  0x4352, 0x1ab6, 0x1aba, 0x4354, 0x4351, 0x4439, 0x443a, 0x4498,
  0x454e, 0x017b, 0x4736, 0x470c, 0x4710, 0x4732, 0x4711, 0x4718,
  0x471c, 0x471a, 0x4719, 0x470b, 0x470f, 0x471d, 0x4721, 0x4713,
  0x471b, 0x4715, 0x0220, 0x4cd1, 0x4cc8, 0x4d8d, 0x4db5, 0x0289,
  0x0295, 0x50f8, 0x51e0, 0x51e1, 0x02be, 0x51de, 0x51fe, 0x51dc,
  0x5201, 0x51fd, 0x5200, 0x54d7, 0x54d6, 0x54d9, 0x5665, 0x56d2,
  0x56dc, 0x56e1, 0x56de, 0x5742, 0x574b, 0x03c3, 0x588e, 0x5891,
  0x588b, 0x5890, 0x5888, 0x5889, 0x5884, 0x58aa, 0x5b8d, 0x5b8f,
  0x5b7d, 0x5b7f, 0x5b7b, 0x5b80, 0x5b7e, 0x5b83,
  /* 0x26 */
  0x5b81, 0x5b86, 0x5b8a, 0x5cbd, 0x5cbe, 0x0477, 0x5cf4, 0x5cf3,
  0x5d02, 0x5cf6, 0x5cf5, 0x5cf2, 0x5d04, 0x5e3d, 0x5ec6, 0x5f89,
  0x5fd4, 0x5fd6, 0x5fd2, 0x60fa, 0x6106, 0x610c, 0x610a, 0x610f,
  0x652f, 0x05d4, 0x65b7, 0x65be, 0x65bc, 0x65e2, 0x6a06, 0x69f7,
  0x6a07, 0x69f6, 0x7635, 0x6c5f, 0x6c65, 0x6c64, 0x6c61, 0x6c5a,
  0x6c5d, 0xe2d7, 0x6ed7, 0x6ed5, 0x6ede, 0x6ee1, 0x6ee0, 0x6ed9,
  0x074c, 0x6eda, 0x6edf, 0x6ef6, 0x6f03, 0x0820, 0x7393, 0x738b,
  0x7391, 0x7392, 0x738a, 0x7389, 0x738f, 0x7456, 0x7459, 0x74ca,
  0x74cc, 0x085b, 0x74d0, 0x74cd, 0x74d6, 0x74cb, 0x7583, 0x7582,
  0x7606, 0x762a, 0x762c, 0x762b, 0x7629, 0x77bd, 0x77b3, 0x77be,
  0x77c0, 0x77b5, 0x77b6, 0x7c9d, 0x7c9f, 0x8120, 0x0a2d, 0x811e,
  0x811c, 0x8132, 0x811f, 0x812b, 0x8121, 0x8126,
  /* 0x27 */
  0x8124, 0x811d, 0x8127, 0x825b, 0x8259, 0x8280, 0x8255, 0x8250,
  0x825a, 0x8256, 0x8263, 0x8252, 0x8258, 0x0a63, 0x8239, 0x83f9,
  0x8628, 0x0b18, 0x86bc, 0x86d6, 0x8705, 0x8720, 0x0b74, 0x8833,
  0x8a46, 0x8a42, 0x8a43, 0x8a47, 0x8acb, 0x8b42, 0x8b45, 0x8bfd,
  0x8bf4, 0x8bf9, 0x8bfa, 0x8e5e, 0x8f1d, 0x0d28, 0x9258, 0x9255,
  0x9557, 0x9558, 0x95d9, 0x95dc, 0x95db, 0x9927, 0x9a85, 0x9a87,
  0x9a8a, 0x9d8b, 0x9e4f, 0xa030, 0xa02f, 0xa168, 0xa149, 0x0fda,
  0xa148, 0xa13f, 0xa14b, 0xa15c, 0xa146, 0xa140, 0xa50d, 0xa61b,
  0xa61d, 0xa617, 0x10a4, 0xa622, 0xb0a7, 0xb108, 0xb306, 0xbf7d,
  0x148e, 0xbf63, 0xbf64, 0xbf7f, 0xbf6b, 0xbf7c, 0xbf68, 0xbf65,
  0xbf6a, 0xc198, 0xc199, 0xc1ae, 0xc92b, 0xc92d, 0xc931, 0xc92e,
  0xc92f, 0x3b6d, 0x3c31, 0x3d2d, 0x3d39, 0x3d38,
  /* 0x28 */
  0x005b, 0x3d3a, 0x3d35, 0x3d62, 0x3fea, 0x3feb, 0x4015, 0x40cc,
  0x40c8, 0x40cd, 0x40db, 0x40cb, 0x4211, 0x4226, 0x4362, 0x435e,
  0x4361, 0x4441, 0x443f, 0x4475, 0x7465, 0x4649, 0x44eb, 0x451a,
  0x4557, 0x476b, 0x47a9, 0x4754, 0x4762, 0x47aa, 0x4758, 0x4772,
  0x4763, 0x4773, 0x478b, 0x478c, 0x475c, 0x4751, 0x4716, 0x4760,
  0x4761, 0x475e, 0x475d, 0x4764, 0x4753, 0x475f, 0x475b, 0x476e,
  0x4755, 0x4752, 0x4768, 0x4cd2, 0x4cd6, 0x4cd3, 0x4db8, 0x4dbb,
  0x4deb, 0x4de8, 0x4db6, 0x4dea, 0x4de7, 0x4de9, 0x5091, 0x5110,
  0x510e, 0x510f, 0x520b, 0x5203, 0x522b, 0x5209, 0x5228, 0x522c,
  0x5225, 0x5227, 0x520f, 0x54e9, 0x54ec, 0x0353, 0x5627, 0x5673,
  0x56e2, 0x56e6, 0xe276, 0x5761, 0x5751, 0x5812, 0x580e, 0x58ad,
  0x58af, 0x58b1, 0x58d3, 0x5b94, 0x5b92, 0x5b90,
  /* 0x29 */
  0x5b9d, 0x5b93, 0x5b95, 0x5b98, 0x5b97, 0x0480, 0xe28a, 0x5d07,
  0x5d0b, 0x5d08, 0x5ed9, 0x5ed5, 0x5fd7, 0x5fdf, 0x04dd, 0x5fde,
  0x5fe3, 0x5fe2, 0x04de, 0x6147, 0x0522, 0x613d, 0x6138, 0x6544,
  0x653a, 0x65b8, 0x662a, 0x6600, 0x65f3, 0x65f2, 0x65eb, 0x65fa,
  0x65ed, 0x65ec, 0x65ff, 0x65fb, 0x1f06, 0x664e, 0x65ef, 0x65f7,
  0x6a10, 0x6a11, 0x6a0c, 0x6a0b, 0x6bdd, 0x6c30, 0x06db, 0x6c7a,
  0x6c77, 0x6e28, 0x6e27, 0x6e65, 0x6f0e, 0x6f0b, 0x6f41, 0x6f13,
  0x6f0f, 0x6f12, 0x6f30, 0x73a0, 0x73a3, 0x739e, 0x7397, 0x73a1,
  0x739d, 0x739b, 0x7463, 0x74e3, 0x74e4, 0x74e6, 0x74e7, 0x74dd,
  0x2185, 0x74ec, 0x74e5, 0x74f1, 0x763b, 0x7639, 0x763a, 0x763c,
  0x763d, 0x7647, 0x763f, 0x7644, 0x7748, 0x7749, 0x7760, 0x77e3,
  0x77e9, 0x77f0, 0x08da, 0x08db, 0x77f2, 0x77ed,
  /* 0x2a */
  0x77ec, 0x77e6, 0x7816, 0x08d7, 0x7cbc, 0x7cbe, 0x7cc0, 0x7ce0,
  0x8000, 0x8002, 0x7ffe, 0x805e, 0x80b3, 0x80b7, 0x813a, 0x8139,
  0x813e, 0x8138, 0x813d, 0x814f, 0x826e, 0x825f, 0x8281, 0x8282,
  0x8271, 0x827b, 0x8279, 0x8277, 0x8273, 0x826f, 0x8297, 0x827e,
  0x83fc, 0x8411, 0x8432, 0x8431, 0x8410, 0x85ec, 0x85eb, 0x862c,
  0x862d, 0x86da, 0x872e, 0x872c, 0x872a, 0x8733, 0x874b, 0x8818,
  0x8842, 0x883b, 0x883f, 0x8841, 0x8843, 0x883c, 0x8a4c, 0x8a4a,
  0x8a49, 0x8a56, 0x8acf, 0x8b47, 0x8b48, 0x8b46, 0x8c1b, 0x8c11,
  0x8c14, 0x8c1d, 0x8c17, 0x8c1e, 0x8c0b, 0x8c1c, 0x8c12, 0x8c16,
  0x8c0d, 0x8c15, 0x8c13, 0x8c18, 0x0c1a, 0x8eac, 0x8f44, 0x8f2c,
  0x8f45, 0x9266, 0x926e, 0x9265, 0x9268, 0x9284, 0x9438, 0x943b,
  0x943a, 0x943f, 0x95df, 0x95dd, 0x95de, 0x95e2,
  /* 0x2b */
  0x0dd6, 0x992c, 0x992f, 0x9a8f, 0x9a90, 0x9a8c, 0x9a93, 0x9d25,
  0x9d88, 0x9da7, 0x9ef7, 0x9fbb, 0xa038, 0xa039, 0xa037, 0xa114,
  0xa16e, 0xa17b, 0x0fde, 0xa16c, 0xa17f, 0xa178, 0xa17a, 0xa16f,
  0x0fe5, 0xa3fe, 0xa648, 0xa64b, 0xa641, 0xa649, 0xa63b, 0xad2d,
  0xb0e3, 0xb11a, 0xb10d, 0xb113, 0xb111, 0xb11c, 0x1272, 0xb3b5,
  0x12af, 0xb729, 0xb7a1, 0xb824, 0xbabb, 0xbda1, 0xbf83, 0xbf8d,
  0xbf87, 0xbf85, 0xbf8a, 0xbfa3, 0xbf89, 0xbf84, 0xbfa2, 0xc1b1,
  0xc1b0, 0xc1af, 0xc1b6, 0xc1c9, 0xc1ca, 0xc1c8, 0xc1b4, 0xc759,
  0xc7c9, 0xc941, 0xc94c, 0x15c4, 0xc945, 0xc95a, 0x3bb9, 0x3d94,
  0x3d6e, 0x3da2, 0x3d67, 0x0063, 0x3da3, 0x3d76, 0x3d6c, 0x3d64,
  0x3da5, 0x3d30, 0x3da6, 0x3d69, 0x3da4, 0x3f90, 0x405f, 0x4060,
  0x40da, 0x40d8, 0x40dd, 0x00ec, 0x4259, 0x425b,
  /* 0x2c */
  0x425a, 0x4239, 0x4234, 0x4244, 0x4233, 0x423c, 0x4258, 0x426a,
  0x436f, 0x4372, 0x4370, 0x4371, 0x436e, 0x43f0, 0x43eb, 0x4449,
  0x444c, 0x44f5, 0x4520, 0x456a, 0x4572, 0x464b, 0x465d, 0x4750,
  0x47f9, 0x47fa, 0x47fb, 0x4823, 0x47b7, 0x4822, 0x47b5, 0x47c4,
  0x47b4, 0x47ef, 0x0198, 0x47cd, 0x47f0, 0x47b8, 0x47cb, 0x47f1,
  0x47ba, 0x4803, 0x47f8, 0x47b6, 0x47bc, 0x47b3, 0x4821, 0x47f7,
  0x47c2, 0x47be, 0x47c9, 0x47bd, 0x47ce, 0x47b9, 0x47c7, 0x47ca,
  0x47cc, 0x4826, 0x4ce4, 0x4ce0, 0x4cf0, 0x4dec, 0x4e24, 0x4e18,
  0x4e2f, 0x4e25, 0x5028, 0x5022, 0x506a, 0x3c57, 0x5094, 0x5092,
  0x509c, 0x5122, 0x5130, 0x511d, 0x5123, 0x5235, 0x5233, 0x522f,
  0x524d, 0x5231, 0x525b, 0x525a, 0x5232, 0x525c, 0x5259, 0x525d,
  0x5469, 0x546d, 0x5d32, 0x0358, 0x550a, 0x5520,
  /* 0x2d */
  0x551f, 0x550d, 0x56e8, 0x56ec, 0x5777, 0x5770, 0x5771, 0x58f8,
  0x58d6, 0x58d9, 0x58de, 0x58d5, 0x58e1, 0x03d3, 0x58e2, 0x58dd,
  0x58e0, 0x590e, 0x5908, 0x58dc, 0x590a, 0x590c, 0x5bb4, 0x5bb1,
  0x5bb6, 0x5bbc, 0x5d1d, 0x5d24, 0x5d19, 0x5d1b, 0x5d22, 0x5d1a,
  0x5d1c, 0x5d21, 0x5ee6, 0x5ee4, 0x5ee7, 0x5eea, 0x04e2, 0xe29b,
  0x5ff0, 0x5ff5, 0x5fef, 0x6142, 0x616e, 0x613c, 0x6197, 0x618c,
  0x6181, 0x6171, 0x61ce, 0x61ba, 0x617a, 0x617e, 0x0554, 0x6172,
  0x61bb, 0x052f, 0x6173, 0x6182, 0x05b6, 0x64ba, 0x64b8, 0x654b,
  0x6548, 0x662b, 0x65f4, 0x662c, 0x6642, 0x6648, 0x6644, 0x6645,
  0x663c, 0x6637, 0x6633, 0x6641, 0x6632, 0x6687, 0x6a27, 0x6a23,
  0x6a2d, 0x6a1f, 0x6a2c, 0x6a28, 0x6b75, 0x6b74, 0x6be7, 0x6c32,
  0x6c31, 0x6c99, 0x6c96, 0x6c98, 0x6c9d, 0x6c92,
  /* 0x2e */
  0x6c94, 0x6c95, 0x6c97, 0xa1b9, 0x0735, 0x6f11, 0x6f3b, 0x6f79,
  0x6f42, 0x6f43, 0x075d, 0x6f78, 0x73ac, 0x0829, 0x73b1, 0x73b4,
  0x73b3, 0x73af, 0x73aa, 0x73b2, 0x7468, 0x74f2, 0x74fe, 0x74f8,
  0x74f9, 0x74ff, 0x74f5, 0x74f7, 0x74fd, 0x7500, 0x7588, 0x766e,
  0x765d, 0x7663, 0x7660, 0x7761, 0x7837, 0x7871, 0x7823, 0x7822,
  0x781f, 0x7825, 0x7cfd, 0x7d11, 0x7d23, 0x7cf0, 0x7cef, 0x800e,
  0x800c, 0x80ba, 0x0a1f, 0x8152, 0x8155, 0x8153, 0x8154, 0x8151,
  0x8158, 0x82b0, 0x829f, 0x0a72, 0x82a1, 0x829a, 0x82be, 0x82a0,
  0x8437, 0x0b0b, 0x85f1, 0x85f2, 0x8634, 0x8637, 0x8635, 0x5e8c,
  0x86df, 0x874c, 0x874e, 0x8764, 0x8763, 0x8736, 0x8858, 0x8868,
  0x885b, 0x885f, 0x8859, 0x8865, 0x8860, 0x885e, 0x8ad5, 0x8ad6,
  0x8b4e, 0x8b52, 0x8c35, 0x8c39, 0x8c58, 0x8c41,
  /* 0x2f */
  0x8c57, 0x8c38, 0x8c3d, 0x8c32, 0x8c44, 0xe344, 0x8e63, 0x8e62,
  0x8eb8, 0x8eb0, 0x8eb1, 0x8f4d, 0x8f63, 0x8f70, 0x8f4b, 0x8f4f,
  0x8f4e, 0x8f53, 0x8f47, 0x0ca2, 0x8f54, 0x8f52, 0x8f59, 0x8f7e,
  0x912b, 0x912e, 0x927f, 0x927e, 0x9281, 0x9283, 0x9447, 0x9454,
  0x944d, 0x944c, 0x944b, 0x9457, 0x9565, 0x9564, 0x9561, 0x9562,
  0x95f0, 0x95f3, 0x95f9, 0x95f4, 0x95f5, 0x95ef, 0x95f8, 0x95fc,
  0x95f7, 0x95fd, 0x9617, 0x9934, 0x9936, 0x9938, 0x9aaf, 0x9aae,
  0x9aac, 0x9aa2, 0x9d28, 0x69c2, 0x9d9c, 0x9db3, 0x9d90, 0x9f06,
  0x9f04, 0x9f0b, 0x9f05, 0xa040, 0xa04f, 0xa1ab, 0xa19d, 0xa1b8,
  0xa1b2, 0xa1d2, 0xa1cf, 0xa1a2, 0x0fec, 0xa1a5, 0xa1a9, 0xa1a7,
  0xa1d0, 0xa402, 0xa4cf, 0xa4d0, 0xa516, 0xa519, 0xa675, 0xa671,
  0xa691, 0xa672, 0xa68e, 0xa66d, 0xa688, 0xa673,
  /* 0x30 */
  0x10bb, 0xa681, 0xa676, 0xa67b, 0xa67f, 0xa690, 0xa678, 0xad4b,
  0xad48, 0xad5b, 0xad51, 0xad3d, 0xad40, 0xad46, 0xad4d, 0xad3b,
  0xad4a, 0xad41, 0xad3e, 0xad4e, 0xb0ac, 0xb0e6, 0xb0e9, 0xb129,
  0x1221, 0xb133, 0xb12e, 0xb11e, 0x121f, 0xe3c5, 0xb122, 0xb127,
  0xb2e7, 0xb30b, 0xb466, 0xb6ad, 0xb6ae, 0x3038, 0xb72b, 0xb72a,
  0xb82c, 0xb82d, 0x1391, 0x1396, 0xb98f, 0xb993, 0xbac6, 0xbac5,
  0xbd15, 0xbd13, 0xbdb1, 0xbda9, 0x31ce, 0xbfa8, 0xbfab, 0xbfbe,
  0xbfac, 0xbfa9, 0xbfa6, 0xbfc1, 0xc1cc, 0xc1d1, 0xc1d3, 0xc1e4,
  0xc1cb, 0xc1e1, 0xc1d2, 0xc1e3, 0xc1cf, 0xc1d0, 0xc1e5, 0xc20e,
  0xc7cc, 0xc7ca, 0xc7cb, 0xc95c, 0xc961, 0xc95d, 0xc959, 0xcb77,
  0xd184, 0x3b41, 0x3bfd, 0x3c1a, 0x3c63, 0x3db6, 0x3db2, 0x3de9,
  0x3de7, 0x3dd6, 0x3e30, 0x3dec, 0x3ddd, 0x3de8,
  /* 0x31 */
  0x3db0, 0x3db5, 0x3de1, 0x3f98, 0x3f99, 0x00bc, 0x4093, 0x40e5,
  0x00d8, 0x4260, 0x425e, 0x00f4, 0x437b, 0x43f1, 0x43f3, 0x457f,
  0xe238, 0x4657, 0x4658, 0x4839, 0x48a7, 0x4835, 0x4860, 0x4851,
  0x4862, 0x4842, 0x483c, 0x4843, 0x48ab, 0x48a9, 0x48b4, 0x4879,
  0x486a, 0x483b, 0x48aa, 0x4833, 0x4837, 0x4827, 0x48a8, 0x4870,
  0x482f, 0x4836, 0x48b5, 0x4830, 0x483a, 0x4838, 0x48b1, 0x48ac,
  0x482e, 0x4875, 0x48b0, 0x4cf7, 0x4d00, 0x4cf1, 0x4cf3, 0x4e60,
  0x4e61, 0x4e32, 0x4e2d, 0x4e4a, 0x4e2a, 0x5067, 0x50a2, 0x50a3,
  0x509e, 0x50a4, 0x5144, 0x5132, 0x529b, 0x5298, 0x5299, 0x529a,
  0x5266, 0x5262, 0x526b, 0x8062, 0x5267, 0x553b, 0x5523, 0x5529,
  0x567e, 0x56f3, 0x0385, 0x56f5, 0x0383, 0x5780, 0x577e, 0x577c,
  0x577f, 0x577d, 0x5781, 0x5925, 0x5929, 0x5917,
  /* 0x32 */
  0x5963, 0x5967, 0x5965, 0x592a, 0x5968, 0x5926, 0x5964, 0x591b,
  0x5962, 0x5969, 0x5afe, 0x5b44, 0x5b42, 0x5bb7, 0x5bd2, 0x5bd4,
  0x5bd7, 0x5bdc, 0x5bd1, 0x5bd5, 0x5bcd, 0x5bd8, 0x5c9a, 0x5d42,
  0x5d35, 0x5d58, 0x5d34, 0x5d3c, 0x5d3b, 0x5d3e, 0x5d3d, 0x5d5a,
  0x5d41, 0x5d38, 0x5d45, 0x5d33, 0x5e26, 0x04ba, 0x5ef7, 0x5eef,
  0x5ef0, 0x5eee, 0x5f9d, 0x6006, 0x6011, 0x6008, 0x6002, 0x600a,
  0x6007, 0x6191, 0x618e, 0x6185, 0x61e8, 0x6217, 0x61d4, 0x0569,
  0x61c4, 0x61d5, 0x0545, 0x61d8, 0x6180, 0x61de, 0x6242, 0x64c7,
  0x64bf, 0x668d, 0x6689, 0x6690, 0x669a, 0x66c8, 0x6692, 0x66a1,
  0x6684, 0x6a57, 0x6a47, 0x6a77, 0x6a3a, 0x6a50, 0x6a42, 0x6a43,
  0x6aa3, 0x6b77, 0x6cb2, 0x6ce2, 0x6cbd, 0x6e74, 0x6f82, 0x6fb8,
  0x076c, 0x6fba, 0x6fb9, 0x6fbb, 0x6f87, 0xe2e4,
  /* 0x33 */
  0x6f83, 0x6fb7, 0x3b43, 0x73ce, 0x73ba, 0x7473, 0x7509, 0x750a,
  0x750b, 0x7507, 0x7505, 0x750e, 0x7597, 0x7677, 0x767b, 0x767a,
  0x7674, 0x7679, 0x7886, 0x78f5, 0x78a5, 0x789d, 0x78be, 0x7896,
  0x78e1, 0x78a4, 0x78a1, 0x78f6, 0x0904, 0x788d, 0x788b, 0x7878,
  0x7898, 0x790a, 0x7d5d, 0x7d27, 0x7d5f, 0x80c0, 0x80c1, 0x816d,
  0x815e, 0x818a, 0x8162, 0x8164, 0x0a3a, 0x82c0, 0x82ca, 0x82d1,
  0x0a79, 0x82c7, 0x82ce, 0x2309, 0x82c5, 0x8436, 0x8457, 0x8477,
  0x85f6, 0x85f7, 0x8655, 0x8644, 0x863e, 0x8642, 0x8652, 0x86e1,
  0x8708, 0x8768, 0x8767, 0x0b5d, 0xe337, 0x887e, 0x8893, 0x8879,
  0x8881, 0x887d, 0x887b, 0x8894, 0x0b8a, 0x8a66, 0x8ad9, 0x8ae0,
  0xe33e, 0x8b5f, 0x8c5f, 0x8c5e, 0x8c68, 0x0c27, 0x8c88, 0x8c6a,
  0x8c6c, 0x8c66, 0x8c67, 0x8c89, 0x8c60, 0x8c85,
  /* 0x34 */
  0x3e2a, 0x8e68, 0x8eb9, 0x8eba, 0x8ebe, 0x8f6f, 0x8f80, 0x8f74,
  0x8f81, 0x8f7a, 0x8f9c, 0x8f73, 0x8f82, 0x8f7f, 0x8fa7, 0x8f79,
  0x8f78, 0x8f7d, 0x8fa8, 0x8f7c, 0x9168, 0x914e, 0x929e, 0x0d46,
  0x0d40, 0x9298, 0x0d3f, 0x9285, 0x929c, 0x92c5, 0x929a, 0x9468,
  0x9465, 0x9467, 0x9461, 0x9460, 0x0d9d, 0x0dcb, 0x957b, 0x0de1,
  0x9619, 0x960e, 0x9631, 0x9612, 0x9610, 0x9615, 0x963f, 0x961d,
  0x961e, 0x994d, 0x9948, 0x9945, 0x9942, 0x9949, 0x994a, 0x9947,
  0x0e7f, 0x994c, 0x9acd, 0x9ad5, 0x9ac4, 0x9aca, 0x9ac3, 0x0f46,
  0x9d29, 0x9db4, 0x9e6d, 0x9e66, 0x9e6a, 0x9f15, 0x9fd3, 0x9fec,
  0xa055, 0x101a, 0xa1d6, 0xa1d8, 0xa251, 0xa1e0, 0xa203, 0xa204,
  0xa1da, 0xa1ea, 0xa202, 0xa1d3, 0xa1e4, 0xa1e5, 0xa43b, 0xa466,
  0xa52c, 0xa521, 0xa526, 0xa5d4, 0xa5d5, 0xa5d9,
  /* 0x35 */
  0xa6d5, 0xa6eb, 0xa6c5, 0x10d3, 0xa727, 0xa6f0, 0xa6b9, 0xa718,
  0xa6ee, 0x10d0, 0xa6b7, 0xa6bb, 0xa6ef, 0xa6b8, 0xa6df, 0xa6da,
  0xa6e3, 0xa6c9, 0xa6ec, 0xaca4, 0x118e, 0xaca3, 0xad5e, 0xad61,
  0xad62, 0xad63, 0xad8e, 0xad69, 0xad6b, 0xad85, 0xad8d, 0xad64,
  0xad6d, 0xb14c, 0xb149, 0xb147, 0xb148, 0xb142, 0xb145, 0xb15b,
  0xb15d, 0xb146, 0xb313, 0xb31a, 0xb30e, 0xb30f, 0xb31b, 0xb312,
  0xb3c3, 0xb3be, 0xb3bd, 0xb479, 0xb47c, 0x12c3, 0x12b6, 0xb480,
  0xb475, 0xb49b, 0x2f2f, 0xb474, 0x12c0, 0x12b8, 0x5b41, 0x5f94,
  0xb730, 0xb7a7, 0xb7a5, 0xb7a4, 0xb83a, 0xb95b, 0xb99f, 0xb9a8,
  0xb9b3, 0xb9a1, 0xb9a7, 0xb9b2, 0xb99d, 0xb9a3, 0xb9a2, 0xbaef,
  0xbad9, 0x13d9, 0xbad5, 0xbadd, 0xbada, 0xbaee, 0xbad7, 0xbd1a,
  0xbd19, 0xbd18, 0xbdb3, 0xbdbf, 0xbdbb, 0xbdc0,
  /* 0x36 */
  0xbdb9, 0xbdb8, 0xbdd5, 0xbf11, 0xbf10, 0xbfc3, 0xbfc2, 0xbfe9,
  0xbfcd, 0xbfe5, 0xbfca, 0xbfc7, 0xbfe8, 0xc1f1, 0xc20c, 0xc1ef,
  0xc1ee, 0xc1f3, 0xc20d, 0xc1f6, 0xc1f0, 0x14ec, 0xc2f5, 0xc2f4,
  0xc2f8, 0xc2fe, 0xc411, 0xc422, 0xc442, 0xc75c, 0xc75a, 0xc75d,
  0xc7d1, 0xc7d0, 0xc975, 0xc97b, 0xca89, 0xcf4d, 0xd189, 0xd192,
  0xd18c, 0xd188, 0xd367, 0xd368, 0x3e21, 0x3df2, 0x3e09, 0x3df8,
  0x3df0, 0x3df3, 0x3df5, 0x3dfb, 0x3df7, 0x3def, 0x3e0b, 0x3fa2,
  0x40f5, 0x40f3, 0x40f4, 0x40f2, 0x4198, 0x4268, 0x4280, 0x4285,
  0x428e, 0x428f, 0x3df4, 0x4286, 0x42a4, 0x4386, 0x4389, 0x4387,
  0x4385, 0x1adc, 0x4388, 0x45ff, 0x4666, 0x01cc, 0x48c3, 0x01a2,
  0x48c4, 0x493b, 0x48e7, 0x48f8, 0x48fb, 0x01b6, 0x48be, 0x48c6,
  0x01c5, 0x01c6, 0x48fc, 0x48c0, 0x4933, 0x48c9,
  /* 0x37 */
  0x48fe, 0x48da, 0x48cc, 0x48bb, 0x48fd, 0x48df, 0x48cd, 0x48c2,
  0x48c8, 0x4932, 0x492d, 0x48d2, 0x4931, 0x48d3, 0x492e, 0x48cf,
  0x4cff, 0x4d09, 0x4cfc, 0x4e72, 0x4eaa, 0x4eab, 0x4ea7, 0x4e7b,
  0x4e76, 0x4ea8, 0x4eac, 0x0283, 0x1b21, 0x50aa, 0x4503, 0x50ac,
  0x50ad, 0x50ab, 0x5150, 0x5158, 0x52dd, 0x02f8, 0x52ae, 0x52a2,
  0x52ab, 0x52e3, 0x52af, 0x52e0, 0x52e4, 0x02f6, 0x52a7, 0x52aa,
  0x52e2, 0x52e1, 0x52df, 0x52ad, 0x52e5, 0x52d1, 0x52ac, 0x52d5,
  0x52a3, 0x529f, 0x5479, 0x5476, 0x5543, 0x553d, 0x5547, 0x5544,
  0x553f, 0x555b, 0x567f, 0x579a, 0x579c, 0x039b, 0x581e, 0x5988,
  0x596a, 0x5976, 0x5972, 0x5970, 0x597e, 0x596c, 0x596f, 0x5975,
  0x5977, 0x5978, 0x598a, 0x5979, 0x5994, 0x5b01, 0x5c05, 0x5bf1,
  0x5bee, 0x5bef, 0x5c04, 0x1c3f, 0x5bfa, 0x5c07,
  /* 0x38 */
  0x5bf4, 0x5bf5, 0x5c9c, 0x5d6d, 0x5d69, 0x5d66, 0x5d62, 0x5d46,
  0x048d, 0x5d65, 0x5d5d, 0x5d5e, 0x5d5f, 0x5e8e, 0x5f02, 0x5f06,
  0x5f04, 0x5f03, 0x5f62, 0x6027, 0x6021, 0x6020, 0x6024, 0x6029,
  0x6031, 0x6023, 0x6022, 0x61dc, 0x624d, 0x61e5, 0x61d3, 0x61ee,
  0x61e6, 0x0556, 0x6236, 0x6240, 0x633f, 0x623d, 0x6244, 0x055b,
  0x625f, 0x6229, 0x6249, 0x628a, 0x622a, 0x6287, 0x624c, 0x6231,
  0x6248, 0x622b, 0x623b, 0x6241, 0x61c9, 0x6234, 0x6253, 0x6235,
  0x6247, 0x6238, 0x64d1, 0x64d2, 0x6551, 0x6555, 0x6552, 0x6553,
  0x668f, 0x66f4, 0x6747, 0x670d, 0x671c, 0x66fb, 0x6719, 0x66f7,
  0x66f9, 0x66f5, 0x66e9, 0x670a, 0x66ee, 0x670b, 0x66fd, 0x6706,
  0x6702, 0x6716, 0x6718, 0x66f0, 0x69d4, 0x6a66, 0x6a6a, 0x6a75,
  0x6a76, 0x6a80, 0x6a6d, 0x0696, 0x6a69, 0x6a67,
  /* 0x39 */
  0x6a68, 0x6a5d, 0x6ba4, 0x6bfe, 0x6bfd, 0x6cec, 0x6ce9, 0x6d21,
  0x6ceb, 0x6ce7, 0x6cf2, 0x6d20, 0x6e7a, 0x702d, 0x702e, 0x6fca,
  0x702f, 0x6fc8, 0x077e, 0x6fcb, 0x6fc3, 0x6f84, 0x6fd2, 0x6fc6,
  0x0781, 0x6fcf, 0x6fd5, 0x6fd4, 0x6fdd, 0x702b, 0x70a3, 0x6fdb,
  0x702c, 0x7013, 0x0835, 0x73d1, 0x73cc, 0x73d5, 0x73c9, 0x73cf,
  0x73d2, 0x747b, 0x747c, 0x7523, 0x751d, 0x751c, 0x751e, 0x7522,
  0x7524, 0x7520, 0x7518, 0x7521, 0x7688, 0x768a, 0x7694, 0x768f,
  0x768e, 0x7690, 0x089b, 0x76a7, 0x7764, 0x0900, 0x7918, 0x7914,
  0x791d, 0x7905, 0x78ff, 0x791b, 0x791a, 0x7919, 0x7903, 0x790e,
  0x7947, 0x7904, 0x795a, 0x7907, 0x7920, 0x7d6a, 0x7d66, 0x7d7a,
  0x7d65, 0x7daf, 0x80cd, 0x80cf, 0x80d1, 0x80ce, 0x815f, 0x8177,
  0x8174, 0x8188, 0x8175, 0x8189, 0x8172, 0x82c6,
  /* 0x3a */
  0x82ea, 0x82e0, 0x8307, 0x82e6, 0x82e5, 0x82dd, 0x82e3, 0x82da,
  0x84ae, 0x847e, 0x847d, 0x847f, 0x0ad3, 0x84d9, 0x85fa, 0x85fb,
  0x864d, 0x8654, 0x8651, 0x8650, 0x8898, 0x8895, 0x889d, 0x88c1,
  0x889a, 0x889b, 0x0b9f, 0x889c, 0x88d7, 0x88a4, 0x8896, 0x88a0,
  0x8a31, 0x8ae9, 0x8b6e, 0x8c9a, 0x8ca5, 0x8c94, 0x8ca6, 0x8c99,
  0x8c8b, 0x8c98, 0x8c91, 0x8c8c, 0x8ca1, 0x8ca3, 0x8cc6, 0x8e6f,
  0x8e6d, 0x8e6a, 0x8e6c, 0x8e71, 0x8e6e, 0x8edf, 0x8fac, 0x8faa,
  0x0cb1, 0x8fb5, 0x8fad, 0x8fb2, 0x8fb3, 0x8fab, 0x8fb6, 0x8fc7,
  0x8fc6, 0x916c, 0x9170, 0x92b3, 0x0d4b, 0x948c, 0x9481, 0x9483,
  0x947d, 0x9485, 0x9490, 0x94a2, 0x948d, 0x948f, 0x947e, 0x948a,
  0x947f, 0x9476, 0x9487, 0x9478, 0x9580, 0x9582, 0x9581, 0x9583,
  0x965a, 0x9634, 0x962c, 0x962a, 0x9640, 0x962d,
  /* 0x3b */
  0x9633, 0x962b, 0x964b, 0x283f, 0x963c, 0x995a, 0x9977, 0x9960,
  0x9afa, 0x9af9, 0x9afc, 0x0ed6, 0x9af5, 0x9ae8, 0x9b09, 0x9ae7,
  0x9ae6, 0x9ae9, 0x9d32, 0x9d2c, 0x9dbd, 0x9dbc, 0x9e7a, 0x9e7e,
  0x9e79, 0x0f89, 0x9f23, 0x9f30, 0x9f28, 0x9fd4, 0xa5ce, 0x9ff1,
  0x9ff0, 0xa060, 0xa065, 0xa1a8, 0xa20a, 0xa21b, 0xa212, 0xa207,
  0xa24f, 0xa20e, 0xa252, 0xa216, 0x1000, 0xa253, 0xa254, 0xe384,
  0xa256, 0xa46d, 0xa472, 0xa52f, 0xa72a, 0x10dc, 0xa763, 0xa784,
  0xa7af, 0xa745, 0xa770, 0xa756, 0xa716, 0xa73d, 0xa738, 0xa76f,
  0xa777, 0xa72e, 0xa731, 0xa74c, 0xa75f, 0xa775, 0xa739, 0xa73a,
  0xa72c, 0xa72d, 0xa73f, 0xa72f, 0xa730, 0xa73e, 0x2ad0, 0x1190,
  0xacb2, 0xadb7, 0xad8f, 0x11b1, 0xad96, 0xad9e, 0xad97, 0xad95,
  0xada5, 0xad98, 0x2be7, 0xada3, 0xad9a, 0xadad,
  /* 0x3c */
  0xada6, 0xadb6, 0xad99, 0xadaf, 0xadac, 0x11aa, 0xad9f, 0xad94,
  0xb0eb, 0xb16f, 0xb16d, 0xb17e, 0xb16c, 0xb161, 0xb163, 0xb16b,
  0xb15e, 0xb13e, 0xb164, 0xb1ad, 0x127d, 0xb320, 0xb3d1, 0xb3d5,
  0xb4ae, 0xb4af, 0xb49f, 0xb4a8, 0xb4ca, 0xb6e1, 0xb739, 0xb738,
  0xb73b, 0xb7ad, 0xb7b5, 0xb7af, 0xb7b1, 0xb7ae, 0xb7b4, 0xb7b0,
  0xb84b, 0xb851, 0xb84c, 0x1369, 0xb860, 0xb9be, 0xb9b9, 0xb9b7,
  0xb9b6, 0x13a1, 0xb9bd, 0xb9b8, 0xb9c1, 0xbaf7, 0xbaf3, 0xbaf4,
  0xbafb, 0xbafd, 0xbaf5, 0x13e7, 0xbafa, 0xbb14, 0x13ec, 0xbaf2,
  0xbafc, 0xbb00, 0x13df, 0xbd2b, 0xbd29, 0xbd2e, 0x1433, 0xbdd7,
  0xbddb, 0xbdd9, 0xbdd8, 0xbde6, 0xbdd6, 0xbdd1, 0xbde7, 0xbdd0,
  0xbddc, 0xbfed, 0xc01a, 0xc019, 0xc016, 0xbfec, 0xbfef, 0xbfee,
  0xbff4, 0xc02f, 0xc230, 0xc219, 0xc218, 0xc211,
  /* 0x3d */
  0xc216, 0xc212, 0xc210, 0xc214, 0xc30c, 0xc30d, 0xc410, 0xc429,
  0xc428, 0xc765, 0xc766, 0xc7de, 0xc7df, 0xc7e0, 0xc7d9, 0xc7db,
  0xc7d8, 0xc7e3, 0xc7d7, 0xc9a6, 0xc9a7, 0xc9bf, 0xc9a8, 0xc99f,
  0xc9a1, 0xc9a2, 0xc9ad, 0xc9a3, 0xc9a0, 0xcb83, 0xcb85, 0xcb82,
  0xcb84, 0xccee, 0xcd5d, 0xce92, 0xcf55, 0xcf53, 0xe3fc, 0xcf51,
  0xcf52, 0xcf50, 0xd09d, 0xd187, 0xd197, 0xd19b, 0xd19d, 0xd369,
  0xd511, 0x17ee, 0xd77a, 0x1832, 0x6d44, 0x3bc6, 0x3e40, 0x3e2e,
  0x3e68, 0x3e2d, 0x3e34, 0x3e32, 0x3e69, 0x3e31, 0x3e6a, 0x3e3e,
  0x3e6f, 0x3e75, 0x3ff6, 0x4020, 0x4028, 0x4067, 0x40fb, 0x40fe,
  0x40fc, 0x40fd, 0x40ff, 0x40fa, 0x42ac, 0x42af, 0x42b1, 0x00fc,
  0x42bf, 0x42be, 0x42a7, 0x42aa, 0x4394, 0x4393, 0x4399, 0x012a,
  0x43fa, 0x4456, 0x4522, 0x4591, 0x4592, 0x4608,
  /* 0x3e */
  0x4606, 0x48d6, 0x4937, 0x494b, 0x49ce, 0x49d0, 0x4939, 0x4964,
  0x49cf, 0x4946, 0x4966, 0x4956, 0x4943, 0x494a, 0x4958, 0x4965,
  0x495a, 0x495b, 0x4955, 0x4950, 0x494c, 0x49cd, 0x4951, 0x4947,
  0x01d3, 0x4953, 0x4962, 0x49cc, 0x01d7, 0x497f, 0x494d, 0x4d0a,
  0x4d0c, 0x4d0f, 0x4ebb, 0x4eb9, 0x4eeb, 0x4eae, 0x4ec1, 0x4eea,
  0x4ebe, 0x4ebf, 0x4eba, 0x4eb7, 0x4eb3, 0x4ee9, 0x506e, 0x50af,
  0x50b3, 0x50b2, 0x5168, 0x5169, 0x516a, 0x52de, 0x52eb, 0x5311,
  0x52ed, 0x52e9, 0x52f7, 0x52f4, 0x52ea, 0x5312, 0x52e8, 0x5313,
  0x548e, 0x5486, 0x5563, 0x5565, 0x5699, 0x57c2, 0x57ae, 0x57b0,
  0x57ad, 0x59b4, 0x59d1, 0x59ad, 0x03f3, 0x59bd, 0x59b7, 0x59ab,
  0x59af, 0x59c1, 0x59b5, 0x5c0f, 0x5c0e, 0x5c1c, 0x5c13, 0x5c0a,
  0x5c1e, 0x5c14, 0x5c1a, 0x5ccb, 0x5d87, 0x5d93,
  /* 0x3f */
  0x0491, 0x5d84, 0x5d85, 0x5d92, 0x5d80, 0x5e2b, 0x5e63, 0x5f0a,
  0x04c0, 0x5f66, 0x6034, 0x6033, 0x604d, 0x6289, 0x6257, 0x1e17,
  0x624a, 0x0563, 0x6239, 0x623f, 0x6290, 0x628f, 0x62b2, 0x629f,
  0x6295, 0x62a1, 0x629c, 0x628e, 0x62a0, 0x62ae, 0x6296, 0x62ca,
  0x62ac, 0x0570, 0x62a6, 0x6297, 0x6293, 0x3eb6, 0x64d6, 0x64e0,
  0x64d8, 0x6556, 0x676e, 0x677e, 0x6774, 0x6777, 0x6780, 0x677a,
  0x676b, 0x675f, 0x6776, 0x67b0, 0x6763, 0x6a92, 0x6a82, 0x6a9d,
  0x6a94, 0x6a83, 0x6a8b, 0x6a84, 0x6b57, 0x6b84, 0x6bb5, 0x6c09,
  0x6ce6, 0x0702, 0x6d25, 0x6d51, 0x6e3e, 0xe2da, 0x6e85, 0x703f,
  0x7042, 0x7038, 0x703a, 0x704e, 0x7045, 0x7039, 0x7037, 0x7044,
  0xe2e7, 0x70a4, 0x703d, 0x7041, 0x73df, 0x73de, 0x73ca, 0x73e9,
  0x73e2, 0x73e0, 0x7489, 0x7487, 0x7532, 0x7533,
  /* 0x40 */
  0x75ac, 0x75f9, 0x760d, 0x76b9, 0x795e, 0x796a, 0x0935, 0x7981,
  0x7969, 0x79ac, 0x7980, 0x7975, 0x7971, 0x7976, 0x796f, 0x7db6,
  0x7e03, 0x7dbf, 0x7e01, 0x7db2, 0x7dc4, 0x80db, 0x80dc, 0x80d8,
  0xd172, 0x81b5, 0x81a8, 0x818f, 0x819e, 0x818e, 0x82de, 0x82e2,
  0x8320, 0x831d, 0x831a, 0x8319, 0x832b, 0x8318, 0x8310, 0x830e,
  0x8324, 0x831f, 0x8313, 0x8335, 0x8483, 0x0ae0, 0x84d7, 0x8602,
  0x8603, 0x865c, 0x865f, 0x86c7, 0x879a, 0x8797, 0x87a3, 0x88f7,
  0x88c6, 0x88cc, 0x88d6, 0x88cd, 0x88f9, 0x88d9, 0x88d8, 0x0b9a,
  0x88d1, 0x88ce, 0x88d5, 0x0ba3, 0x88d4, 0x88f8, 0x8a7a, 0x8a79,
  0x8a7f, 0x8af3, 0x8af5, 0x8af6, 0x8af4, 0x8af7, 0x0c05, 0x8b79,
  0x8c9c, 0x8cdf, 0x8cf5, 0x8cff, 0x8cd1, 0x8cda, 0x8ccf, 0x8ccd,
  0x8cde, 0x8cd8, 0x75ad, 0x8ce0, 0x8cfe, 0x8c9e,
  /* 0x41 */
  0x8e75, 0x8e73, 0x8e72, 0x8ed8, 0x8fd0, 0x8fd6, 0x8fd7, 0x8fca,
  0x8fcd, 0x918a, 0x9189, 0x918b, 0x92d3, 0x92da, 0x92ce, 0x92d2,
  0x0d5e, 0x92d4, 0x92d7, 0x92d5, 0x92fb, 0x92cd, 0x92fa, 0x92d6,
  0x0da6, 0x949b, 0x949e, 0x949d, 0x94a4, 0x949f, 0x94a5, 0x94a8,
  0x958f, 0x958c, 0x9595, 0x9660, 0x9655, 0x965c, 0x966f, 0x9651,
  0x9661, 0x96a7, 0x965e, 0x9662, 0x9654, 0x966e, 0x9652, 0x9658,
  0x966d, 0x965f, 0x966c, 0x9650, 0x965d, 0x968e, 0x9972, 0x9978,
  0x9975, 0x9976, 0x997c, 0x997e, 0x997d, 0x9b15, 0x9b14, 0x9b1d,
  0x9b1c, 0x9b23, 0x9b57, 0x9dc8, 0x0f5d, 0x9dc9, 0x9dc5, 0x9e87,
  0x9e8a, 0x9e94, 0x9f39, 0x9f40, 0x9f3e, 0x9f38, 0x9fd6, 0x0fb8,
  0xa077, 0xa078, 0xa07d, 0xa1dd, 0xa25e, 0xa277, 0xa2d4, 0xa266,
  0xa25f, 0xa273, 0xa272, 0xa297, 0xa25a, 0xa296,
  /* 0x42 */
  0xa25d, 0xa265, 0xa270, 0xa275, 0xa274, 0xa25c, 0xa260, 0x1034,
  0xa26d, 0xa441, 0xa53c, 0xa53e, 0x106f, 0xa549, 0xa6e4, 0xa80c,
  0xa815, 0xa80e, 0xa7d4, 0xa7c7, 0xa814, 0xa7db, 0xa85f, 0xa7ed,
  0xa809, 0xa7cf, 0xa7ff, 0xa81a, 0xa7f5, 0xa7ee, 0xa7e5, 0xa80d,
  0xa7df, 0xa7ec, 0xa7d7, 0xa82c, 0xa7cd, 0xa7e3, 0xa800, 0xa7eb,
  0xa7fd, 0xa80f, 0xa82d, 0xa850, 0xa7fe, 0xa7c4, 0xa7e1, 0xa7f9,
  0xacbb, 0xacbc, 0x1194, 0x1193, 0xacba, 0xadd0, 0xadd6, 0xadce,
  0xade1, 0xadd5, 0xadcf, 0xadd3, 0x2c14, 0xadd4, 0xb0b5, 0xb190,
  0xb1a1, 0xb181, 0xb185, 0xb16e, 0xb188, 0xb182, 0xb186, 0xb18f,
  0xb189, 0xb180, 0xb184, 0x2dd1, 0xb329, 0xb32b, 0xb32a, 0xb330,
  0xb3e2, 0xb3e3, 0xb3e1, 0xb4d5, 0xb4c4, 0xb4c9, 0xb4e0, 0xb4df,
  0xb4cb, 0xb4dd, 0xb4e8, 0xb4d4, 0xb51c, 0xb6b5,
  /* 0x43 */
  0xb6b6, 0xb6e5, 0xb745, 0xb749, 0xb740, 0xb746, 0xb744, 0xb74a,
  0xb7c9, 0xb7c4, 0xb7c2, 0xb868, 0x30cb, 0xb882, 0xb86b, 0xb867,
  0xb86c, 0xb86d, 0xb871, 0x1392, 0xb9d7, 0xb9d2, 0xb9d9, 0x13aa,
  0xb9bc, 0xb9dd, 0xb9d6, 0x13a7, 0xb9d8, 0xbb20, 0xbb16, 0xbb18,
  0xbb15, 0xbb19, 0xbb27, 0xbb50, 0xbb1d, 0xbb2c, 0xbb1c, 0xbb29,
  0xbb2b, 0xbb24, 0x13ef, 0xbb28, 0xbd39, 0xbdf2, 0xbdf0, 0x31ed,
  0xbded, 0xbdef, 0xbdea, 0xbe01, 0xbfbc, 0xc05c, 0xc05b, 0xc05a,
  0xc026, 0xc243, 0xc233, 0xc23a, 0xc237, 0xc236, 0xc23c, 0xc234,
  0xc24a, 0xc23b, 0xc235, 0xc23d, 0xc240, 0xc23e, 0xc217, 0xc326,
  0xc324, 0xc310, 0xc336, 0xc325, 0x1522, 0xc466, 0xc77f, 0xc77a,
  0xc7fb, 0xc7fd, 0xc7fc, 0xc7fa, 0xc9d6, 0xc9d2, 0xc9c8, 0xc9c2,
  0xc9c7, 0xc9cd, 0xc9c1, 0xc9c6, 0xc9cc, 0xcaa1,
  /* 0x44 */
  0xcb95, 0xcb94, 0xcb97, 0xcb96, 0xcb93, 0xcba1, 0xcd09, 0xcd08,
  0xcd67, 0xcd65, 0xcd62, 0xcd61, 0xce97, 0xcf01, 0xcf19, 0xcf18,
  0xcf5c, 0xcf67, 0xcf6c, 0x16b5, 0xe3fd, 0xcf66, 0xcf61, 0xcf6e,
  0xcf5d, 0xcf5a, 0xd0a2, 0xd0a9, 0xd0a5, 0xd0a6, 0xd0b2, 0xd0a3,
  0xd1ac, 0xd1aa, 0xd1af, 0xd1ab, 0xd1b1, 0xd1c6, 0xd1ad, 0xd1b9,
  0xd370, 0x1775, 0xd70c, 0xd781, 0xd783, 0xd77e, 0xd851, 0xda99,
  0xda94, 0xda95, 0xddfe, 0xdf18, 0x3c05, 0x3e72, 0x0082, 0x3e99,
  0x3fad, 0x4106, 0x42c2, 0x42cb, 0x42d5, 0x42d2, 0x42cc, 0x42d7,
  0x42c5, 0x42ca, 0x1aec, 0x43a2, 0x43a3, 0x43a1, 0x1ae8, 0x43a6,
  0x43a4, 0x43ff, 0x4400, 0x0135, 0x4506, 0x4525, 0x459f, 0x467f,
  0x49ea, 0x49eb, 0x49de, 0x4a18, 0x49e0, 0x49e8, 0x49df, 0x49f1,
  0x49ec, 0x4a1a, 0x49e6, 0x49e1, 0x4a5d, 0x3b77,
  /* 0x45 */
  0x4a1e, 0x01f0, 0x4a80, 0x4d17, 0x4f1d, 0x4ef7, 0x4eef, 0x4eee,
  0x4eed, 0x4ef5, 0x4f1e, 0x50bd, 0x92ff, 0x50be, 0x516d, 0x5176,
  0x534c, 0x5319, 0x5348, 0x531e, 0x534a, 0x5349, 0x5326, 0x5495,
  0x5585, 0x557e, 0x5597, 0x557d, 0x5642, 0x569e, 0x570c, 0x570b,
  0x570e, 0x57cf, 0x59d8, 0x59e0, 0x59e8, 0x59e9, 0x59e2, 0x59e1,
  0x59da, 0x59e3, 0x59d9, 0x59f0, 0x59e7, 0x59e4, 0x5c23, 0x5c31,
  0x5c33, 0x5c0c, 0xa4e0, 0x5c26, 0x5c24, 0x5c32, 0x5c27, 0x5c21,
  0x5c30, 0x5ca2, 0x5ccc, 0x5d96, 0xe28f, 0x5d98, 0x5d9c, 0x5d9b,
  0x5e6b, 0x5f12, 0x5f14, 0x605a, 0x6054, 0x6051, 0x605e, 0x6058,
  0x04f0, 0x6059, 0x629a, 0x62f2, 0x62ad, 0x6320, 0x62f1, 0x62f0,
  0x62f3, 0x62f4, 0x62e9, 0x62fe, 0x632e, 0x62e8, 0x62e7, 0x62ff,
  0x62f5, 0x64e5, 0x64e7, 0x64e6, 0x64e9, 0x6558,
  /* 0x46 */
  0x676c, 0x67d9, 0x063e, 0x67cb, 0x0639, 0x67be, 0x67b5, 0x67d1,
  0x67c2, 0x67b6, 0x67d3, 0xe2c2, 0x69da, 0x06a2, 0x6aae, 0x06a1,
  0x6aac, 0x6aa9, 0x6aba, 0x6aa7, 0x6ab3, 0x6abd, 0x6ab1, 0x6c0e,
  0x6c11, 0x6c0f, 0x6d52, 0x6d6a, 0x6d54, 0x6d55, 0x6d53, 0x6d57,
  0xe2d0, 0x6d5b, 0x6d58, 0x6d59, 0x073b, 0x70b8, 0x70b5, 0x70bf,
  0x70c1, 0x70bb, 0x07b7, 0x70c3, 0x70ad, 0x70a8, 0x70ae, 0x70aa,
  0x70d0, 0x70b1, 0x70bc, 0x70b7, 0x70b0, 0x70fa, 0x70fb, 0x70b9,
  0x7407, 0x73fc, 0x73ff, 0x73f2, 0x73f8, 0x73f4, 0x0857, 0x0870,
  0x75ae, 0x760f, 0x7610, 0xd5c1, 0x76c7, 0x76c6, 0x76ce, 0x08a7,
  0x76dc, 0x79d5, 0x79d3, 0x7a20, 0x79e2, 0x79f1, 0x79db, 0x79ea,
  0x79eb, 0x79e1, 0x79ff, 0x79d6, 0x79e0, 0x79d7, 0x79e5, 0x0954,
  0x7e1b, 0x7e10, 0x7e0f, 0x7e0e, 0x7e39, 0x7e0a,
  /* 0x47 */
  0x7e14, 0x7e0c, 0x8023, 0x805a, 0x80e2, 0x8193, 0x81b0, 0x81ab,
  0x81bd, 0x81b1, 0x818d, 0x81ad, 0x81aa, 0x81ac, 0x81bc, 0x81c9,
  0x8347, 0x8362, 0x8340, 0x8344, 0x833f, 0x8343, 0x833d, 0x8360,
  0x835f, 0x833a, 0x8342, 0x835e, 0x835d, 0x84bc, 0x84e0, 0x84ee,
  0x84eb, 0x850d, 0x8609, 0x860c, 0x8669, 0x8674, 0x8667, 0x0b31,
  0x2427, 0x87ad, 0x88ff, 0x8916, 0x8908, 0x8909, 0x8900, 0x88fc,
  0x8913, 0x8914, 0x890a, 0xd33e, 0x8929, 0x8917, 0x893b, 0x88fb,
  0x0bbf, 0x890e, 0x0be4, 0x8b08, 0x8d1b, 0x8d1a, 0x8d19, 0x8d02,
  0x0c4a, 0x8d17, 0x8d07, 0x8d13, 0x8d09, 0x8d0a, 0x8d12, 0x8daa,
  0x8d38, 0x8e7b, 0x8e7c, 0x8e7a, 0x8eea, 0x8eeb, 0x8eef, 0x8ef0,
  0x9009, 0x9002, 0x9001, 0x902b, 0x9007, 0x0ccf, 0x9028, 0x8ffb,
  0x902a, 0x8ffe, 0x9004, 0x9029, 0x900a, 0x268c,
  /* 0x48 */
  0x91c2, 0x2691, 0x91a6, 0x0d14, 0x6ac6, 0x5707, 0x930a, 0x92fd,
  0x9306, 0x930d, 0x9309, 0x930b, 0x9300, 0x9305, 0x933d, 0x94d5,
  0x94bf, 0x94ba, 0x94c7, 0x9598, 0x959a, 0x0e0d, 0x96a4, 0x969c,
  0x969d, 0x96a2, 0x9696, 0x9695, 0x96a5, 0x96aa, 0x96ad, 0x969a,
  0x96a3, 0x9697, 0x9690, 0x96af, 0x968d, 0x0e01, 0x96a8, 0x96ee,
  0x99a3, 0x9999, 0x99a1, 0x999c, 0x99a4, 0x99cb, 0x9b56, 0x9b48,
  0x9b59, 0x9b4a, 0x9b5c, 0x0ef1, 0x9b4f, 0x9b4d, 0x9b5d, 0x9d3a,
  0x9de3, 0x9de1, 0x9dde, 0x9e97, 0x9e9a, 0x9f48, 0x9f4a, 0x9f4c,
  0x9f4e, 0x9f4f, 0x9f4b, 0x9f4d, 0x9f47, 0x9f51, 0x9fda, 0x9ff9,
  0x9ffc, 0x9ffb, 0x9ffd, 0x9ff8, 0x9ffa, 0xa08a, 0xa08e, 0xa088,
  0xa089, 0xa08d, 0xa090, 0xe37e, 0xa21c, 0xa2ab, 0xa2a4, 0xa2a8,
  0xa2ad, 0xa29f, 0xa29a, 0xa2b0, 0xa2a5, 0xa2d5,
  /* 0x49 */
  0xa2a2, 0xa2b2, 0xa29d, 0xa2a1, 0xa415, 0xa442, 0xa473, 0xa484,
  0xa4dd, 0xa55c, 0xa54c, 0x1076, 0xa54b, 0xa767, 0xa8a8, 0xa87f,
  0xa88d, 0xa88b, 0xa8f7, 0xa8a9, 0xa8f4, 0xa89a, 0xa88c, 0xa895,
  0xa87e, 0xa877, 0x110a, 0x1103, 0xa871, 0xa8fd, 0xa888, 0xa899,
  0xa86d, 0xa8d4, 0xa891, 0xa87d, 0xa863, 0xa875, 0xa8f9, 0xa88e,
  0xa874, 0xa8d9, 0xa866, 0xa8fa, 0xa8f5, 0x2b60, 0xacc1, 0xacc9,
  0xae03, 0xae2c, 0xae34, 0xae17, 0xae09, 0xae04, 0xae06, 0xae1a,
  0xae0e, 0xae27, 0xae05, 0xae07, 0xae19, 0xae14, 0xae0c, 0xae1d,
  0xae22, 0xae23, 0xb0bb, 0xb0bc, 0xb0ba, 0xb1a9, 0xb1b5, 0xb1ac,
  0xb1aa, 0xb1af, 0xb1b7, 0x1235, 0x1236, 0xb33d, 0xb345, 0xb348,
  0xb33c, 0xb33e, 0xb3ec, 0xb3ed, 0xb3e9, 0xb50a, 0xb50b, 0xb4f8,
  0xb504, 0xb4f6, 0xb4f9, 0xb753, 0xb752, 0xb760,
  /* 0x4a */
  0xb763, 0xb7ce, 0xb7cd, 0xb7d0, 0xb7cc, 0xb887, 0xb88d, 0xb89d,
  0x1373, 0xb963, 0xb9f4, 0xb9f1, 0xb9ff, 0xb9f5, 0xb9fc, 0xb9f2,
  0xb9f6, 0x13b1, 0xb9fa, 0x13af, 0xbb2e, 0xbb45, 0xbb43, 0xbb51,
  0xbb4c, 0xbb73, 0xbb47, 0xbb4b, 0xbb4f, 0x13f4, 0xbb44, 0xbb4d,
  0xbb4e, 0xbb4a, 0xbb41, 0xbb52, 0xbb9f, 0xbb54, 0xbb59, 0xbba2,
  0xbd4b, 0xbd48, 0xbe0d, 0xbe1f, 0xbe08, 0xbe0e, 0xbe1d, 0xbe04,
  0xbf18, 0xbf1b, 0xc066, 0xc062, 0xc083, 0xc067, 0xc262, 0xc24f,
  0xc24e, 0xc267, 0xc253, 0xc24d, 0xc24c, 0xc268, 0xc251, 0x14dc,
  0xc258, 0xc25b, 0xc250, 0xc26a, 0xc339, 0xc338, 0xc482, 0xc484,
  0xc486, 0xc780, 0xc786, 0xc825, 0xc811, 0x1599, 0xc80e, 0xc815,
  0xc80f, 0xc818, 0xc80d, 0xc813, 0xc816, 0xc819, 0xca08, 0xc9f7,
  0xc9fb, 0xc9fc, 0xcab4, 0xcabe, 0xcac1, 0xcba6,
  /* 0x4b */
  0xcba5, 0xcba2, 0xcba4, 0xcd12, 0xcd0e, 0xcd7a, 0xcd78, 0xcd7d,
  0xcd7e, 0xcd81, 0xcd83, 0xcd80, 0xcd82, 0xce9b, 0xce99, 0xce9c,
  0xce9a, 0xcea1, 0xcf1a, 0xcf1c, 0xcf8a, 0xcf79, 0xcf73, 0xcf75,
  0xcf7c, 0xcf78, 0xcf60, 0xcf83, 0xcf70, 0x16b6, 0x16bd, 0xcf87,
  0xcf84, 0xcf7b, 0xcf7e, 0xcf74, 0xd0b9, 0xd0b8, 0xd0b7, 0xd0b5,
  0xd1cc, 0xd1ce, 0xd1d1, 0x3504, 0xd1cb, 0x359b, 0xd37e, 0xd37a,
  0xd377, 0xd38c, 0xd3e1, 0xd520, 0xd51e, 0xd5c2, 0xd60f, 0xd600,
  0x17f4, 0xd610, 0xd70d, 0xd70e, 0xd78e, 0xd78d, 0xd78a, 0xd78b,
  0xd78c, 0xd78f, 0xd857, 0x1835, 0xd855, 0xd85b, 0xdaac, 0xdaa7,
  0xdaa0, 0xda9e, 0x18aa, 0xdd8d, 0x191f, 0xde93, 0xde94, 0xde92,
  0x3c81, 0x3ed6, 0x3ec3, 0x3ee4, 0x3ee2, 0x3f09, 0x3ebf, 0x3ec8,
  0x3ec7, 0x3fb5, 0x3fb2, 0x410c, 0x410b, 0x410a,
  /* 0x4c */
  0x4105, 0x42d3, 0x42e6, 0x42e9, 0x42f0, 0x42ea, 0x42e7, 0x43b1,
  0x43b3, 0x43b4, 0x43b0, 0x445c, 0x445d, 0x445e, 0x44c0, 0x45af,
  0x0150, 0x4686, 0x4683, 0x4684, 0x4a5c, 0x4a70, 0x4a8c, 0x4a7b,
  0x4a66, 0x4a79, 0x4a63, 0x4ac5, 0x4a6b, 0x4a6d, 0x4a72, 0x4a69,
  0x4a75, 0x4a89, 0x4ac6, 0x4aa5, 0x3ee7, 0x4a6a, 0x4a97, 0x4f5a,
  0x4f5c, 0x4f59, 0x4f24, 0x4f25, 0x4f30, 0x4f58, 0x4f31, 0x5076,
  0x50c1, 0x5185, 0x517d, 0x5365, 0x5387, 0x5352, 0x5354, 0x538a,
  0x5350, 0x5386, 0x534f, 0x5368, 0x549d, 0x55a0, 0x55ba, 0x55bd,
  0x55b8, 0x56a6, 0x037b, 0x57de, 0x57d8, 0x57d1, 0x5a13, 0x0409,
  0x5a0e, 0x5a1b, 0x5a3a, 0x0407, 0x5a1c, 0x5a12, 0x5a16, 0x5a1a,
  0x4f48, 0x5c4b, 0x5c37, 0x5c36, 0x5c38, 0x5c3a, 0x5c49, 0x5c3c,
  0x5c4a, 0x5db1, 0x5dc2, 0x5db5, 0x5dc4, 0x5db6,
  /* 0x4d */
  0x5f1e, 0x5f1f, 0x5faf, 0x606b, 0x606f, 0x6292, 0x62e4, 0x632c,
  0x62ef, 0x1e84, 0x634f, 0x6352, 0x6350, 0x633a, 0x6337, 0x6347,
  0x6364, 0x6340, 0x633c, 0x6345, 0x6341, 0x64f3, 0x05bb, 0x67c1,
  0x67b4, 0x064b, 0x682a, 0x6822, 0x6829, 0x064d, 0x683e, 0x683c,
  0x6830, 0x6ac7, 0x6ad5, 0x6ad6, 0x6ad3, 0x6ace, 0x6ac8, 0x6b5c,
  0x6b5f, 0x6b62, 0x06c2, 0x6bbd, 0x6bbf, 0x6d5c, 0x6db6, 0x071a,
  0x6d9d, 0x6d7f, 0x0712, 0x6d94, 0x6d81, 0x6e47, 0x6e46, 0x073c,
  0x6e94, 0x7175, 0x711a, 0x712a, 0x7132, 0x7117, 0x7123, 0x7174,
  0x07c3, 0x7176, 0x712e, 0x7125, 0x7120, 0x7171, 0x7116, 0x7170,
  0x712c, 0x712f, 0x711f, 0x7164, 0x07c0, 0x7408, 0x7414, 0x740a,
  0x740b, 0x754f, 0x7559, 0x7554, 0x7551, 0x75b6, 0x76ec, 0x76ed,
  0x76ea, 0x79ee, 0x7a5a, 0x7a73, 0x7a65, 0x7a61,
  /* 0x4e */
  0x7a55, 0x7a6b, 0x7a64, 0x7a5b, 0x7a4c, 0x7a6f, 0x7a84, 0x7a70,
  0x7e5d, 0x7e57, 0x7e66, 0x7e53, 0x7e98, 0x7e97, 0x8028, 0x80eb,
  0x80ea, 0x80e8, 0x80ec, 0x80ef, 0x81d1, 0x81ca, 0x229f, 0x81cf,
  0x81cd, 0x81ce, 0x8370, 0x8367, 0x8373, 0x836d, 0x8376, 0x8379,
  0x836a, 0x838b, 0x8372, 0x8371, 0x836e, 0x837a, 0x8516, 0x8539,
  0x853b, 0x8610, 0x8696, 0x867b, 0x867c, 0x867d, 0x87be, 0x895c,
  0x8938, 0x8939, 0x892e, 0x8934, 0x8932, 0x895b, 0x8933, 0x893c,
  0x0be5, 0x8b0e, 0x8b0f, 0x8b10, 0x8b87, 0x8d5b, 0x8d53, 0x0c5f,
  0x8d5c, 0x8d3f, 0x8d59, 0x8d4a, 0x8d44, 0x8d4c, 0x8d40, 0x8d5f,
  0x8d5e, 0x8d4e, 0x8d54, 0x8d43, 0x8d87, 0x8e82, 0x8e85, 0x9031,
  0x9047, 0x902e, 0x902f, 0x9048, 0x9034, 0x0d18, 0x409f, 0x9332,
  0x9336, 0x9333, 0x9331, 0x9340, 0x9341, 0x94dd,
  /* 0x4f */
  0x94d2, 0x94d9, 0x0dad, 0x94df, 0x94db, 0x94d8, 0x94d3, 0x94de,
  0x94e0, 0x94d4, 0x94d7, 0x94da, 0x95a7, 0x96fe, 0x96dd, 0x9740,
  0x96e2, 0x0e1e, 0x96d6, 0x96de, 0x96ef, 0x0e27, 0x96eb, 0x96ea,
  0x96e4, 0x96d1, 0x0e18, 0x96ec, 0x96fa, 0x96d9, 0x96f3, 0x96e1,
  0x96dc, 0x96e5, 0x96df, 0x96d4, 0x0e20, 0x9705, 0x99d6, 0x99d5,
  0x99d0, 0x99c8, 0x99c4, 0x99c9, 0x99cd, 0x99d2, 0x99cc, 0x99ca,
  0x9ba0, 0x9b92, 0x0ef4, 0x0f02, 0x9b8b, 0x9ba1, 0x9b95, 0x9b88,
  0x9b86, 0x9b8d, 0x9b85, 0x9b91, 0x9b89, 0x9ba2, 0x9df0, 0x9df3,
  0x9df4, 0x9def, 0x9e06, 0x9ea2, 0x9f5e, 0x9f63, 0x9f60, 0x9f5b,
  0x9f7b, 0x9f58, 0x9f59, 0x9f5d, 0xa005, 0xa006, 0xa002, 0xa003,
  0xa004, 0xa0a2, 0xa0a7, 0xa0a6, 0xa0a4, 0xa0ac, 0xa0a9, 0xa2e7,
  0xa301, 0xa2df, 0xa2d9, 0xa2e3, 0xa30f, 0xa41a,
  /* 0x50 */
  0xa445, 0xa448, 0xa47b, 0xa485, 0xa486, 0xa4fe, 0xa4ff, 0xa564,
  0xa571, 0xa572, 0xa561, 0xa562, 0xa56c, 0xa560, 0xa55d, 0xa563,
  0xa567, 0xa901, 0xa92a, 0xa930, 0xa906, 0xa97d, 0xa922, 0xa9b9,
  0xa90a, 0xa910, 0xa88f, 0xa980, 0xa913, 0xa92e, 0xa918, 0xa917,
  0xa91a, 0xa914, 0xa947, 0xa963, 0xa938, 0x1124, 0xa97f, 0xa921,
  0xa937, 0xa931, 0xa91b, 0xa9a5, 0xaa6c, 0xacd3, 0xae88, 0xae6e,
  0xae5c, 0xae74, 0xae54, 0xae83, 0x2c70, 0xae65, 0xae60, 0xae70,
  0xae6f, 0xae6d, 0xae72, 0xae6c, 0xae76, 0xae75, 0xae59, 0xaec7,
  0xb0c1, 0xb0f9, 0xb1e3, 0xb1e7, 0xb1d6, 0xb1cf, 0xb1da, 0xb1e9,
  0xb1d2, 0xb355, 0xb34f, 0xb34d, 0xb351, 0xb356, 0xb3f9, 0xb3f8,
  0xb406, 0xb403, 0xb409, 0xb3f7, 0xb3f5, 0xb547, 0xb545, 0xb53e,
  0xb546, 0xb529, 0xb534, 0xb53f, 0xb544, 0xb535,
  /* 0x51 */
  0xb55a, 0xb52a, 0xb533, 0xb537, 0xb543, 0xb539, 0xb530, 0xb55d,
  0xb532, 0xb527, 0xb6bd, 0xb6ba, 0xb6bc, 0xb6f1, 0xb6f0, 0xb761,
  0xb75c, 0xb758, 0xb75d, 0xb7da, 0xb7d9, 0xb7dd, 0xb7dc, 0xb7de,
  0x137d, 0xb8a0, 0xb8a2, 0xba0f, 0xba13, 0xba12, 0xba11, 0xba14,
  0xba19, 0xba0e, 0xba17, 0xba21, 0xba20, 0xba16, 0xbb78, 0x3137,
  0xbb87, 0xbb79, 0xbb80, 0xbb77, 0xbb81, 0xbb46, 0xbb7a, 0xbb9c,
  0xbb83, 0xbb84, 0xbbad, 0xbb9d, 0xbb9e, 0xbd55, 0xbe36, 0xbe2b,
  0xbe27, 0xbe46, 0xbe2c, 0xbe45, 0xbe33, 0xbe2d, 0xbe34, 0xbe22,
  0x1464, 0xbf1f, 0xc0b3, 0xc08c, 0xc08f, 0xc0af, 0xc0ad, 0xc08e,
  0xc0ac, 0xc0b0, 0xc0b1, 0xc0ae, 0xc099, 0xc1eb, 0xc274, 0xc275,
  0xc28e, 0xc26d, 0xc270, 0xc28c, 0xc34f, 0xc351, 0xc358, 0xc34c,
  0xc34e, 0xc415, 0xc4be, 0xc4db, 0xc4b3, 0xc4ae,
  /* 0x52 */
  0xc787, 0xc78a, 0xc788, 0xc78b, 0xc78c, 0xc844, 0xc82d, 0xc82a,
  0xc831, 0xc82c, 0xc845, 0xc830, 0xc829, 0xc846, 0xc9f4, 0xca14,
  0xca10, 0xca0f, 0xca12, 0xca0b, 0xca0c, 0xca0a, 0xca13, 0xca0e,
  0xcad9, 0xcad0, 0x3403, 0xcbc1, 0xcbbf, 0xcbbd, 0xcbbc, 0xcbba,
  0xcbbb, 0xcbd1, 0xcbbe, 0xcbd0, 0xcbb9, 0xcd1a, 0xcd1c, 0xcd1b,
  0xcd91, 0xcd96, 0xcd9f, 0xcd9c, 0xcd9a, 0xcd9d, 0xcead, 0xcea5,
  0xceae, 0xcf03, 0xcf26, 0xcf20, 0xcf23, 0xcf24, 0xcf21, 0xcf28,
  0xcf25, 0xcf1e, 0xcf94, 0xcf93, 0xcf8f, 0xcf9a, 0xcfad, 0x16c2,
  0xd0ca, 0xd0c5, 0xd1bb, 0xd1e1, 0xd1ea, 0xd1e4, 0xd1ed, 0xd1e6,
  0xd1e0, 0xd1e8, 0xd1e5, 0xd31a, 0xd394, 0xd396, 0xd39e, 0xd395,
  0xd3a1, 0xd38e, 0xd39b, 0xd392, 0xd397, 0xd399, 0xd393, 0xd532,
  0xd52f, 0xd52e, 0xd533, 0xd61c, 0xd61e, 0xd611,
  /* 0x53 */
  0xd620, 0xd61f, 0xd619, 0xd616, 0xd7ac, 0xd7b9, 0xd7b3, 0xd7a6,
  0xd7a2, 0xd7a9, 0xd7a7, 0xd86c, 0xd86d, 0xd869, 0xd880, 0xd866,
  0xd865, 0xd871, 0xd86b, 0xdabd, 0xdac1, 0xdad3, 0xdab6, 0x18ac,
  0xdab9, 0xdad4, 0xdab7, 0xdadb, 0xdab8, 0xdac0, 0xdabc, 0xdad5,
  0xdabf, 0xdac3, 0xdac9, 0xdd58, 0xdd90, 0xdd95, 0xdd97, 0xde09,
  0xde08, 0xde06, 0xde05, 0xde10, 0xde97, 0xde95, 0xdeee, 0xdf1f,
  0xe41a, 0x564b, 0x3c8e, 0x3ee8, 0x3ef0, 0x3ef4, 0x3f06, 0x3eed,
  0x009b, 0x3ee9, 0x3f00, 0x3fb8, 0x406d, 0x4304, 0x4301, 0x4303,
  0x4302, 0x1af6, 0x0126, 0x4462, 0x45ba, 0x4613, 0x4ade, 0x4ad7,
  0x4ae4, 0x4ace, 0x4ae3, 0x4add, 0x4b3a, 0x4adb, 0x4ad6, 0x4ae0,
  0x4ad4, 0x4acb, 0x4ae1, 0x4ac9, 0x4adf, 0x4b3c, 0x4afc, 0x4acf,
  0x4f79, 0x4f76, 0x4f78, 0x5187, 0x539c, 0x53b1,
  /* 0x54 */
  0x53c1, 0x539a, 0x5392, 0x0daf, 0x53c2, 0x5396, 0x53c0, 0x5391,
  0x5395, 0x54a2, 0x55c3, 0x55c0, 0x55c2, 0x468a, 0x56b0, 0x5716,
  0x5a46, 0x5a4a, 0x5a3e, 0x5a45, 0x5a42, 0x0415, 0x5a5b, 0x5a44,
  0x5b04, 0x7b22, 0x5c55, 0x5c57, 0x5c51, 0x5c4e, 0x5c5a, 0x5dc6,
  0x5dc3, 0x049c, 0x5dc5, 0x5dcc, 0x5e71, 0x5f26, 0x5fb2, 0x607a,
  0x6084, 0x607b, 0x6374, 0x638c, 0x6351, 0x6348, 0x638d, 0x6392,
  0x6398, 0x6393, 0x63b0, 0x634e, 0x6396, 0x6397, 0x639c, 0x63ca,
  0x6833, 0x6883, 0x6884, 0x689a, 0x688c, 0x20cc, 0x6899, 0x69e0,
  0x6ae4, 0x6af8, 0x6aed, 0x6af3, 0x6af4, 0x6af5, 0x6afd, 0x6c19,
  0x6d9e, 0x6dc4, 0x6d9f, 0x6e9b, 0x6e9f, 0x6e9a, 0x71aa, 0x719d,
  0x7192, 0x71a2, 0x71af, 0x71eb, 0x71a0, 0x71a1, 0x7194, 0x7198,
  0x718f, 0x7187, 0x7184, 0x71a9, 0x717c, 0x7418,
  /* 0x55 */
  0x755f, 0x7562, 0x7561, 0x75c0, 0x7615, 0x76fc, 0x76f9, 0x7ac8,
  0x7ac9, 0x7ade, 0x7aca, 0x7ae2, 0x0964, 0x096e, 0x7b04, 0x7acc,
  0x7add, 0x7ae4, 0x7ad3, 0x7ac7, 0x7ac6, 0x095f, 0x7b37, 0x7ed8,
  0x7eee, 0x7eb2, 0x7ea3, 0x7eb3, 0x7eed, 0x7ef8, 0x8031, 0x805b,
  0x8066, 0x8069, 0x8096, 0x809b, 0x80f7, 0x80f3, 0x80f4, 0x80f5,
  0x81e2, 0x81e7, 0x81e5, 0x81e9, 0x81e6, 0x81e3, 0x8374, 0x837f,
  0x838f, 0x8390, 0x8397, 0x83a3, 0x838e, 0x8398, 0x838c, 0x8542,
  0x8544, 0x8569, 0x8543, 0x8568, 0x0af6, 0x868d, 0x8688, 0x868b,
  0x8689, 0x87cc, 0x881f, 0x8980, 0x895e, 0x8967, 0x8968, 0x8965,
  0x254a, 0x8974, 0x8969, 0x8961, 0x8962, 0x896c, 0x8993, 0x8986,
  0x8a9d, 0x8a9b, 0x8b1b, 0x8b16, 0x8b19, 0x8b14, 0x8b18, 0x8b15,
  0x8b99, 0x8b98, 0x8d5d, 0x8d89, 0x8d7a, 0x8d7d,
  /* 0x56 */
  0x8d4b, 0x0c73, 0x8d78, 0x8d7f, 0x8d77, 0x8d7e, 0x8d79, 0x8dab,
  0x8d7c, 0x8d74, 0x8d75, 0x8da7, 0x8e8d, 0x904e, 0x9066, 0x9061,
  0x904d, 0x904f, 0x0cdd, 0x9054, 0x907c, 0x91da, 0x91de, 0x91d8,
  0x91dd, 0x91df, 0x9366, 0x9362, 0x935f, 0x9364, 0x9363, 0x9360,
  0x9388, 0x936a, 0x9367, 0x9387, 0x933f, 0x936c, 0x936e, 0x93ad,
  0x94f1, 0x94f4, 0x94f6, 0x94f5, 0x94f8, 0x94fb, 0x94ec, 0x94ef,
  0x94ed, 0x27b9, 0x94f7, 0x94f9, 0x94fd, 0x95b1, 0x9736, 0x971b,
  0x9732, 0x9742, 0x974d, 0x971f, 0x9721, 0x971c, 0x9731, 0x972e,
  0x9747, 0x973b, 0x9741, 0x9718, 0x9739, 0x971d, 0x9727, 0x9723,
  0x28d7, 0x99ee, 0x99e8, 0x99e5, 0x99ef, 0x99e4, 0x99ec, 0x99f0,
  0x9bd7, 0x9bd8, 0x9bd4, 0x9bca, 0x9bd2, 0x9bcb, 0x9bd3, 0x9be6,
  0x9be2, 0x9d49, 0x9d48, 0x9dff, 0x9e09, 0x9eb0,
  /* 0x57 */
  0x9eaf, 0x9f7c, 0x9f78, 0x9f7a, 0x9f72, 0x9f79, 0x9f7e, 0xa00c,
  0xa00b, 0xa0cd, 0xa0be, 0xa0bc, 0xa0bf, 0xa0c0, 0xa0bd, 0xa338,
  0xa308, 0xa305, 0xa33b, 0xa310, 0xa30c, 0xa30d, 0xa304, 0xa33a,
  0xa313, 0xa337, 0xa339, 0xa41f, 0xa44b, 0xa4ee, 0xa575, 0xa578,
  0xa57c, 0xa574, 0xa576, 0xa5ea, 0xa5eb, 0xa8fb, 0xa919, 0xa9a7,
  0xa98c, 0xa9dc, 0xa998, 0xa9be, 0xa99e, 0xaa0f, 0xa99f, 0xa9dd,
  0xa993, 0xa9bb, 0xa9b6, 0xa990, 0xa9a1, 0xa9bd, 0xa9de, 0xa93a,
  0xaa22, 0xa997, 0xa994, 0xa9c3, 0xa98e, 0xa9a8, 0xa999, 0xa9ad,
  0xa99b, 0xa9a2, 0xaa21, 0xa9ac, 0xaa0e, 0xaa31, 0xace1, 0xacde,
  0xacdf, 0xacdc, 0xacdd, 0xacec, 0xace7, 0xae69, 0xaeb8, 0xaea1,
  0xaea8, 0xaeba, 0xaec2, 0xaea6, 0xaea4, 0xaea3, 0xaeab, 0xaebc,
  0xaeb7, 0xaebf, 0xaead, 0xaeb1, 0xaeca, 0xaec4,
  /* 0x58 */
  0xaeb9, 0xb0c8, 0xb0c6, 0xb0c7, 0xb20a, 0xb20d, 0xb1fb, 0xb203,
  0xb202, 0xb1fc, 0xb1f9, 0xb1f8, 0xb36e, 0xb363, 0xb362, 0xb361,
  0xb36b, 0x1288, 0xb36f, 0xb366, 0xb36c, 0xb40e, 0xb415, 0xb416,
  0xb410, 0xb417, 0xb411, 0xb56e, 0xb56c, 0xb587, 0xb583, 0xb563,
  0xb5dc, 0xb6c0, 0xb6f7, 0xb6fa, 0xb770, 0xb76a, 0xb768, 0xb769,
  0xb784, 0xb7ec, 0xb7e7, 0xb7ee, 0xb8ba, 0xb8b2, 0xb8b5, 0xb8cb,
  0x430b, 0xb8d0, 0xb96d, 0xb96c, 0x13bb, 0xba45, 0xba46, 0xba34,
  0xba2c, 0xba35, 0xba44, 0x13ba, 0xba76, 0xbbb1, 0xbbaa, 0xbba1,
  0xbbb2, 0x1412, 0xbba6, 0xbbb5, 0xbbb4, 0xbbb8, 0xbbaf, 0xbbb0,
  0xbba3, 0xbd62, 0xbd64, 0xbe56, 0xbe51, 0xbe4f, 0xbe68, 0xbe4c,
  0xbe50, 0x1461, 0xbe48, 0xbe4a, 0xbf21, 0xc0e3, 0xc0b9, 0xc0de,
  0xc0b7, 0xc0e1, 0xc0b6, 0xc0b5, 0xc0df, 0x14e1,
  /* 0x59 */
  0xc297, 0xc29a, 0xc29b, 0xc298, 0xc292, 0xc293, 0xc2d7, 0xc273,
  0xc36b, 0xc374, 0xc378, 0xc36d, 0xc418, 0xc4e9, 0xc4f5, 0xc4ea,
  0xc52e, 0xc4e7, 0xc4fe, 0xc4e5, 0xc536, 0xc4f0, 0xc4e6, 0xc52c,
  0xc789, 0xc795, 0xc793, 0xc84d, 0xc84a, 0xc84f, 0xc850, 0xc84b,
  0xca2a, 0xca2b, 0xca2f, 0xca2e, 0xca7c, 0xcaed, 0xcae2, 0xcbe0,
  0xcbdc, 0xcbda, 0xcbd6, 0xcbf4, 0xcbd9, 0xcbd5, 0xcd22, 0xcd21,
  0xcd24, 0xcd25, 0xcd26, 0xcd23, 0xcdaa, 0xcdaf, 0xcdb0, 0xcdab,
  0xceaf, 0xceb7, 0xceb5, 0xceb2, 0xceb3, 0xcf2b, 0xcfd8, 0xcfc2,
  0xcfaf, 0xcfbc, 0xcfb8, 0xcfbe, 0xcfb7, 0xcfb4, 0xcfbf, 0xcfb3,
  0xcfb1, 0xcfbb, 0xcfbd, 0xcfd6, 0xcfdd, 0xd0d8, 0xd0d3, 0xd0d5,
  0xd0e3, 0xd0e2, 0xd0d9, 0xd0de, 0xd0df, 0xd0da, 0xd0d4, 0xd1f3,
  0xd1e2, 0xd20d, 0xd201, 0xd205, 0xd21a, 0xd203,
  /* 0x5a */
  0xd21f, 0xd216, 0xd1fa, 0xd1fc, 0xd20a, 0x359f, 0xd3bc, 0xd3ca,
  0xd3b6, 0xd3c7, 0xd3bf, 0x1789, 0xd3b9, 0x178c, 0xd3b0, 0xd3b8,
  0xd3bd, 0xd391, 0xd3bb, 0xd3be, 0xd53e, 0xd53d, 0xd638, 0xd63d,
  0xd639, 0xd633, 0xd733, 0xd7bb, 0xd7c6, 0xd7c5, 0xd7c7, 0xd7cb,
  0xd7a8, 0xd7c8, 0xd7be, 0xd7c1, 0xd7bd, 0xd882, 0xd89e, 0xd881,
  0xd884, 0x368d, 0xd896, 0xd88e, 0xd888, 0xd887, 0xdae0, 0xdb0d,
  0xdadf, 0xdae4, 0xdae2, 0xdadd, 0xdaec, 0xdade, 0xdae7, 0xdaea,
  0xdae3, 0xdd5c, 0x3977, 0xdd5d, 0xdd9c, 0xde1d, 0xde9d, 0xde9e,
  0xde9b, 0xdeb5, 0xdeb9, 0xdeb6, 0xdef3, 0xdef2, 0xdef4, 0xdf26,
  0xdf27, 0xdf25, 0xe006, 0xe00b, 0xe03a, 0xe03c, 0x3f0a, 0x3f0b,
  0x3fbf, 0x3ffd, 0x4118, 0xccdf, 0x8e90, 0x419e, 0x42fc, 0x4310,
  0x430f, 0x430d, 0x43b9, 0x43b7, 0x43ba, 0x440a,
  /* 0x5b */
  0x4b41, 0x4b8b, 0x4b46, 0x4b53, 0x4be2, 0x4b3f, 0x4a7c, 0x4b4b,
  0x4b4e, 0x4b8a, 0x4b47, 0x4f93, 0xb8ce, 0x4f8c, 0x4faf, 0x4fc9,
  0x50c6, 0x50c8, 0x5191, 0x53cf, 0x53d4, 0x53ce, 0x55dd, 0x55d4,
  0x5a49, 0x5a63, 0x5a5d, 0x041a, 0x5a67, 0x5abb, 0x5a60, 0x5a80,
  0x5c5f, 0x5c60, 0x5dda, 0x5dd2, 0x5ddd, 0x608e, 0x6088, 0x606c,
  0x639e, 0x63c9, 0x63a4, 0x0598, 0x63d9, 0x63d2, 0x63da, 0x63dd,
  0x63ce, 0x63fc, 0x6514, 0x6560, 0x68d7, 0x68cb, 0x68cd, 0x68d5,
  0x69e7, 0x6b00, 0x6b0a, 0x6b0f, 0x6b02, 0x6b01, 0x6c1d, 0x6dca,
  0x6dcb, 0x6dcd, 0x6e4f, 0x6e9c, 0x7180, 0x720d, 0x7202, 0x07f1,
  0x7207, 0x71f7, 0x71f8, 0x71fd, 0x7224, 0x71fb, 0x7239, 0x723a,
  0x7422, 0x21a9, 0x756a, 0x756d, 0x7574, 0x770e, 0x7adf, 0x7b2b,
  0x7ae3, 0x7b26, 0x7b2a, 0x7b23, 0x7b35, 0x7b4a,
  /* 0x5c */
  0x7efd, 0x7f00, 0x7f1e, 0x7eff, 0x809e, 0x80fa, 0x81f1, 0x8395,
  0x83a8, 0x83a6, 0x856e, 0x8583, 0x856d, 0x868f, 0x0b6a, 0x87df,
  0x87d5, 0x87e0, 0x87d3, 0x87d8, 0x898c, 0x254b, 0x8994, 0x8996,
  0x8985, 0x898f, 0x89a9, 0x898e, 0x8990, 0x89b8, 0x89c3, 0x89bb,
  0x8aa5, 0x8aa2, 0x25a2, 0x8aa3, 0x8bb5, 0x8bac, 0x8ba8, 0x8dad,
  0x8db8, 0x8db4, 0x8dae, 0x8db6, 0x1085, 0x8dc1, 0x8dbf, 0x8e92,
  0x8f00, 0x8f01, 0x9075, 0x9072, 0x9078, 0x9070, 0x907e, 0x907d,
  0x907f, 0x91ef, 0x936d, 0x938e, 0x938f, 0x938a, 0x938d, 0x9395,
  0x938b, 0x938c, 0x93b0, 0x9393, 0x94fc, 0x9515, 0x950e, 0x9518,
  0x9511, 0x950d, 0x95bb, 0x95b3, 0x0e39, 0x97a1, 0x979a, 0x9784,
  0x97a0, 0x9786, 0x979d, 0x97aa, 0x9778, 0x978d, 0x978a, 0x97a6,
  0x977a, 0x9797, 0x9788, 0x978e, 0x0e3d, 0x0e44,
  /* 0x5d */
  0x9776, 0x9781, 0x9785, 0x9775, 0x97a8, 0x978f, 0x9791, 0x97a2,
  0x979c, 0x9789, 0x977f, 0x9796, 0x9779, 0x979f, 0x97a7, 0x0e46,
  0x9787, 0x979b, 0x97a5, 0x978b, 0x97c9, 0x99ff, 0x9a03, 0x9a00,
  0x9a02, 0x9a04, 0x9a05, 0x99e6, 0x9a1f, 0x9c14, 0x0f0e, 0x9c0c,
  0x9c0f, 0x9c19, 0x9c0b, 0x9c13, 0x9bd5, 0x0f10, 0x9c1c, 0x9d50,
  0x9d4e, 0x9e12, 0x9eb9, 0x9f85, 0x9f88, 0x9f90, 0xa013, 0xa0d0,
  0xa0d1, 0xa0d2, 0xa0d7, 0xa0d6, 0xa0e5, 0xa346, 0xa36b, 0xa345,
  0xa33f, 0xa33e, 0x103e, 0xa36a, 0xa368, 0xa34c, 0xa423, 0xa422,
  0xa497, 0xa491, 0x95b5, 0xa498, 0xa49c, 0xa589, 0x1084, 0xa58b,
  0xa58a, 0xa58d, 0xa58e, 0xa588, 0xaa4e, 0xaa44, 0xaa37, 0xaa75,
  0xaa54, 0xaa76, 0xaa34, 0xaa6b, 0xaa32, 0xaa57, 0xaa52, 0xaa45,
  0x1149, 0xa9b1, 0xaa4b, 0xaa47, 0xaa33, 0xaa40,
  /* 0x5e */
  0xaa3c, 0xaa43, 0xaa4f, 0xaa55, 0xaa41, 0xaab2, 0xaac0, 0xaf34,
  0xaef9, 0xaf19, 0xaf0d, 0xaefa, 0xaf1e, 0xaf1f, 0xaf0e, 0xaf40,
  0xaf08, 0x11df, 0xaf13, 0xaf4c, 0x11f0, 0xb0cc, 0xb0cb, 0xb224,
  0xb225, 0xb23d, 0xb220, 0xb227, 0xb226, 0xb21d, 0xb21e, 0xb232,
  0xb26c, 0xb259, 0x128a, 0xb37a, 0xb379, 0xb41b, 0xb42e, 0xb423,
  0xb420, 0xb41f, 0xb5b6, 0xb5b9, 0xb5a1, 0xb5a3, 0xb5a8, 0xb5af,
  0xb59a, 0xb599, 0xb5a2, 0xb59d, 0x1302, 0xb5ab, 0xb6c9, 0xb6fe,
  0xb700, 0xb6fc, 0xb707, 0xb775, 0xb772, 0xb773, 0xb774, 0xb7fa,
  0xb7fc, 0xb7f8, 0xb7f6, 0xb7fb, 0xb8cd, 0xb8d1, 0xb8cf, 0xb974,
  0xb972, 0xb973, 0xba54, 0xba51, 0x13c0, 0xba53, 0xba49, 0xba4c,
  0xba4a, 0xba4f, 0xba56, 0xbc00, 0xbbd5, 0xbbd7, 0xbbff, 0xbbd9,
  0xbbe3, 0xbbd3, 0x1415, 0xbbd8, 0xbbd4, 0xbbde,
  /* 0x5f */
  0xbd71, 0xbe74, 0xbe88, 0xbe7f, 0xbe6b, 0xbe87, 0xbe79, 0xbe78,
  0xbe89, 0xbe80, 0xbe76, 0xbf29, 0xbf28, 0xbf2f, 0xc0e5, 0xc104,
  0xc103, 0xc0f0, 0xc0e8, 0xc0ea, 0xc0f1, 0xc101, 0xc102, 0xc2a9,
  0xc2ab, 0xc2b7, 0xc2b6, 0x14e2, 0xc3a0, 0xc38e, 0xc386, 0xc387,
  0xc385, 0xc38b, 0xc388, 0xc390, 0xc41a, 0xc434, 0xc537, 0xc52f,
  0xc530, 0xc539, 0xc534, 0xc533, 0xc585, 0xc584, 0xc53a, 0xc79f,
  0xc869, 0xc86c, 0xc86a, 0xc867, 0xc86b, 0xca3f, 0xca40, 0xcb0b,
  0xcbd2, 0xcbf8, 0xcc01, 0xcbfa, 0xcc16, 0xe3f6, 0xcd2c, 0xcd2d,
  0xcd2f, 0xcd2e, 0xcdc1, 0xcdf5, 0xcdc4, 0xcdde, 0xcdcc, 0xcdd2,
  0xcdc2, 0xcdcd, 0xcdcf, 0xcddd, 0xcdc8, 0xceba, 0xcebe, 0xcebf,
  0xcf2e, 0xcf30, 0xcfe7, 0xcfee, 0xcfe8, 0xcfe6, 0xcfe2, 0xcfe4,
  0xcffb, 0xcffc, 0xcfea, 0xd0ed, 0xd0f6, 0xd0f3,
  /* 0x60 */
  0xd0f4, 0xd0f1, 0xd0f7, 0xd0f5, 0xd0ea, 0xd0eb, 0xd200, 0xd22c,
  0xd212, 0xd23d, 0xd233, 0x174d, 0xd230, 0xd240, 0xd231, 0xd257,
  0x174a, 0xd235, 0xd232, 0xd22d, 0xd236, 0xd238, 0xd262, 0xd25f,
  0xd28a, 0xd3d6, 0xd3d8, 0xd3dd, 0xd3e4, 0xd3e3, 0xd54d, 0xd5d7,
  0xd64a, 0xd64c, 0xd650, 0xd64b, 0xd64e, 0xd64f, 0xd739, 0xd7d2,
  0xd7cd, 0xd7d3, 0xd7e1, 0xd7ce, 0xd7d5, 0xd7dd, 0xd7d4, 0xd7cf,
  0xd8cb, 0xd8a6, 0xd8c2, 0xd8a5, 0xd8a9, 0xd8a2, 0xd8a4, 0xd8b0,
  0xd8cc, 0xd8af, 0xd8bf, 0xdb24, 0xdb1a, 0xdb14, 0xdb3a, 0xdb20,
  0xdb1b, 0xdb21, 0xdb25, 0xdb1e, 0xdb3f, 0xdb40, 0xdb18, 0xdb2c,
  0xdb15, 0xdb2d, 0xdb1f, 0xdb29, 0xdb4b, 0xddb3, 0xdda5, 0xdda7,
  0xddab, 0xdda6, 0xddaa, 0xde22, 0xde23, 0xdea1, 0xdea3, 0xdea0,
  0xdebd, 0xdeba, 0xdefa, 0xdef8, 0xdefc, 0xdef6,
  /* 0x61 */
  0xdf34, 0xdf43, 0x1963, 0xdfd4, 0xe00d, 0xe043, 0xe041, 0xe03d,
  0xe040, 0xe03e, 0xe03f, 0xe046, 0x1993, 0xe0d4, 0x3f35, 0x3f36,
  0x3f32, 0x3f3a, 0x3fc8, 0x4036, 0x411e, 0x411d, 0x411f, 0x431c,
  0x431d, 0x4320, 0x010e, 0x43c0, 0x4b9a, 0x4b93, 0x4bdd, 0x020a,
  0x4ba3, 0x4ba9, 0x4b9c, 0x4b9b, 0x020d, 0x4b97, 0x4fb1, 0x4fca,
  0x4fb3, 0x4fcd, 0x53ea, 0x53ee, 0x53ef, 0x55df, 0x5650, 0x56bb,
  0x5a88, 0x5a89, 0x5a8c, 0x5a85, 0x5a5e, 0x5a94, 0x5a95, 0x5c6b,
  0x5c6a, 0x5c69, 0x5de3, 0x5df1, 0x5f37, 0x5f33, 0x6091, 0x608f,
  0x6097, 0x63d4, 0x63de, 0x63d3, 0x63e0, 0x6443, 0x640b, 0x63ff,
  0x6404, 0x6407, 0x68f9, 0x68fa, 0x68fb, 0x68f8, 0x6b12, 0x6b10,
  0x6c1f, 0x6ddf, 0x6de3, 0x6e52, 0x6ea9, 0x7265, 0x7287, 0x7242,
  0x7252, 0x724c, 0x719f, 0x7201, 0x7248, 0x724f,
  /* 0x62 */
  0x727e, 0x724d, 0x7258, 0x7247, 0x725e, 0x7249, 0x724e, 0x725d,
  0x725a, 0x7286, 0x7251, 0x7429, 0x74a6, 0x74a7, 0x7570, 0x756f,
  0x75d3, 0x75d2, 0x7728, 0x771b, 0x771a, 0x771c, 0x7721, 0x7b32,
  0x7b66, 0x7b7d, 0x7b73, 0x7b7f, 0x7b65, 0x7b80, 0x7b61, 0x7b75,
  0x7b6e, 0x7b67, 0x7b71, 0x7b6c, 0x7b63, 0x7b62, 0x7b83, 0x7bb2,
  0x7b81, 0x7b6f, 0x7b6b, 0x7b82, 0x7b8a, 0x7f29, 0x7f30, 0x7f31,
  0x8097, 0x8100, 0x80ff, 0x83b6, 0x83b5, 0x83c3, 0x858d, 0x8618,
  0x869c, 0x869a, 0x8699, 0x89b7, 0x89c4, 0x89c6, 0x89c7, 0x89bc,
  0x89c0, 0x89c5, 0x89cd, 0x89c1, 0x89be, 0x8aa7, 0x8ab8, 0x8b23,
  0x0c74, 0x8de8, 0x8dde, 0x8de3, 0x8def, 0x8ddc, 0x8de4, 0x8de1,
  0x8de5, 0x8e95, 0x8e94, 0x8e93, 0x8e8e, 0x9098, 0x909c, 0x9099,
  0x90a0, 0x909e, 0x9204, 0x93d3, 0x93b4, 0x93bb,
  /* 0x63 */
  0x93b7, 0x93b8, 0x93bd, 0x93b6, 0x93b9, 0x93b5, 0x9522, 0x9521,
  0x95c3, 0x95bc, 0x97ff, 0x97e5, 0x97f7, 0x97d2, 0x9800, 0x97db,
  0x97f0, 0x97e2, 0x97cd, 0x0e56, 0x97e1, 0x97f2, 0x97dc, 0x97cc,
  0x97d6, 0x97f3, 0x97fa, 0x97f6, 0x97ec, 0x97ea, 0x97e3, 0x97d0,
  0x0e5a, 0x9795, 0x97d5, 0x97f4, 0x97cb, 0x97da, 0x97c8, 0x97df,
  0x97f5, 0x97cf, 0x97c7, 0x97d7, 0x9a24, 0x0ea3, 0x9a1c, 0x9a21,
  0x9a1e, 0x9a18, 0x9a1b, 0x0ea8, 0x9c46, 0x9c4b, 0x9c48, 0x9c47,
  0x9c67, 0x9c54, 0x9e15, 0x9e22, 0x9ec5, 0x9e29, 0x9ec7, 0x9f8d,
  0xa01b, 0xa020, 0xa0e2, 0xa0e7, 0xa0e8, 0xa0e1, 0xa372, 0xa37b,
  0xa374, 0xa371, 0xa379, 0xa375, 0xa390, 0xa377, 0xa37d, 0xa44f,
  0xa450, 0xa4a3, 0xa4a2, 0xa4f4, 0xa594, 0xa59a, 0xa59b, 0xa5a7,
  0xa597, 0xa595, 0xa592, 0xa59c, 0xa596, 0xaab6,
  /* 0x64 */
  0xaab8, 0xaab0, 0xab18, 0xaac5, 0xaab5, 0xaac2, 0xab06, 0xab19,
  0xaab9, 0xab15, 0xaad6, 0xaaac, 0x113c, 0xaac6, 0xaab3, 0xaac3,
  0xaaca, 0xaacf, 0xaabd, 0xaace, 0xab14, 0xaaba, 0xab1a, 0xaac1,
  0xaabb, 0x119b, 0x119a, 0xad01, 0xacfc, 0xaf5a, 0xaf54, 0xaf61,
  0xaf5c, 0xaf55, 0xaf4a, 0xaf4b, 0xaf51, 0xaf69, 0xaf6b, 0x2cf1,
  0xaf66, 0xaf58, 0xaf5d, 0xaf67, 0xaf56, 0xaf88, 0xaf64, 0xaf4e,
  0xb257, 0xb25a, 0xb251, 0xb24a, 0xb24b, 0x125e, 0xb247, 0xb26f,
  0xb26a, 0xb26b, 0xb246, 0xb26d, 0xb254, 0xb26e, 0xb24c, 0xb378,
  0xb386, 0xb382, 0x12a7, 0xb5e1, 0xb5e5, 0xb5db, 0xb5de, 0xb5d7,
  0xb703, 0xb77c, 0xb77e, 0xb805, 0xb807, 0xb8e6, 0xb8e1, 0xb8fb,
  0xb8e5, 0xb8e7, 0xb8df, 0xb8ff, 0xb976, 0xba63, 0xba66, 0xba65,
  0xba5e, 0xba64, 0xba6b, 0xba5f, 0xba67, 0xba68,
  /* 0x65 */
  0xbc08, 0xbc09, 0xbc17, 0xbc15, 0xbc1b, 0xbc0b, 0xbc28, 0xbc0e,
  0xbc18, 0xbc53, 0xbc45, 0xbc0d, 0xbc0a, 0xbc13, 0xbc4a, 0xbd79,
  0xbea1, 0xbe8d, 0xbea2, 0xbe90, 0x146e, 0xbf31, 0xbf30, 0xc11f,
  0xc119, 0xc10c, 0xc11e, 0xc11d, 0xc107, 0xc266, 0xc2c5, 0xc2ba,
  0xc2bd, 0xc2c2, 0xc2c3, 0xc2bf, 0x150d, 0xc3a1, 0xc3a2, 0xc3a8,
  0xc3a3, 0xc3aa, 0xc3af, 0xc3b9, 0xc437, 0xc58f, 0x1543, 0xc58e,
  0xc587, 0xc58a, 0xc592, 0xc597, 0xc59f, 0xc605, 0xc7a9, 0xc7a7,
  0xc88a, 0xc882, 0xc885, 0xc88b, 0xc889, 0xc881, 0xc880, 0xc887,
  0xc886, 0xca4d, 0xcb1c, 0xcb1f, 0xcc21, 0xcc1d, 0xcc22, 0xcbfe,
  0xcc1b, 0xcc3a, 0xcc37, 0xcc17, 0xcc38, 0xcc26, 0xcc18, 0xcd34,
  0xcd35, 0xcd32, 0x1673, 0xcde1, 0xcdfd, 0xcde3, 0xcde8, 0xcdf9,
  0xcdff, 0xcdfe, 0x1674, 0xcde0, 0xce00, 0x1670,
  /* 0x66 */
  0xcdec, 0xcde4, 0xcdef, 0xcdfa, 0xceca, 0xcf31, 0xcf32, 0xcf34,
  0xcf41, 0xd000, 0xd006, 0xd008, 0xd005, 0xd003, 0xd00b, 0xd002,
  0xd00a, 0xd0f0, 0xd113, 0xd10a, 0xd10f, 0xd111, 0xd108, 0xd10b,
  0xd112, 0xd10d, 0xd25b, 0xd263, 0xd261, 0xd268, 0xd25a, 0xd34e,
  0xd34d, 0xd350, 0xd3fc, 0xd412, 0xd3f5, 0xd41e, 0xd3f0, 0xd3f3,
  0xd3f2, 0xd401, 0xd3ef, 0xd3ee, 0xd416, 0xd3f6, 0xd3fb, 0xd41c,
  0x17db, 0xd55d, 0xd560, 0xd566, 0xd55f, 0xd561, 0xd55b, 0xd562,
  0xd557, 0xd669, 0xd66b, 0xd661, 0xd677, 0xd65f, 0xd663, 0xd662,
  0xd665, 0xd7ef, 0xd7e2, 0xd800, 0xd7e8, 0xd7f2, 0xd7e7, 0x1829,
  0xd7e5, 0xd8d5, 0xd8d0, 0xd8da, 0xd8d3, 0x1851, 0xd8d9, 0xd8cf,
  0xd8d6, 0xd8d8, 0xd8f5, 0xd8ce, 0xd8d7, 0xd8f4, 0xd8cd, 0xd901,
  0x36ba, 0xdb52, 0xdb55, 0xdb5a, 0xdb4d, 0xdb54,
  /* 0x67 */
  0xdb53, 0xdb5e, 0xdb67, 0xdb65, 0xdb4e, 0x18db, 0xdb4f, 0xdb61,
  0xdb6e, 0xdb51, 0xdb5b, 0xdd63, 0xddb7, 0xddb6, 0xddc3, 0xddbb,
  0xddb5, 0xde2e, 0xde30, 0xde33, 0xde31, 0xdea4, 0xdec1, 0xdebb,
  0xdebe, 0xdf00, 0xdeff, 0xdf40, 0x1965, 0xdf3f, 0xdf44, 0x1964,
  0x1967, 0xdfdb, 0xe00f, 0xe011, 0xe04e, 0xe04d, 0xe04b, 0xe04c,
  0xe095, 0x3f3b, 0x3f45, 0x3f44, 0x3f3e, 0x3f3c, 0x3f3f, 0x3fcc,
  0x3fce, 0x4122, 0x4123, 0x419f, 0x43c5, 0x43c4, 0x4be9, 0x4b99,
  0x0211, 0x4be6, 0x4be7, 0x4bf7, 0x4fd2, 0x0333, 0x53fd, 0x540c,
  0x540b, 0x57ed, 0x0421, 0x0422, 0x5a9b, 0x5b0a, 0x5c6f, 0x5c75,
  0x5df2, 0x5df8, 0x5f3e, 0x6424, 0x640e, 0x6416, 0x6418, 0x6410,
  0x6431, 0x6444, 0x05a6, 0x6429, 0x642f, 0x644b, 0x6436, 0x05a3,
  0x6934, 0x6900, 0x692b, 0x6b20, 0x6b21, 0x6b1e,
  /* 0x68 */
  0x6b1d, 0x6df5, 0x6df2, 0x6df6, 0x7290, 0x729d, 0x729c, 0x7292,
  0x7294, 0x72d1, 0x7293, 0x72b7, 0x7297, 0x72b0, 0x729f, 0x72c9,
  0x742d, 0x742c, 0x7577, 0x772c, 0x7bc0, 0x7bb9, 0x7f53, 0x8040,
  0x8202, 0x81fb, 0x0aa1, 0x858b, 0x85ae, 0x85ab, 0x86a1, 0xddcd,
  0x87ea, 0x89dd, 0x89dc, 0x89d9, 0x8aab, 0x8aac, 0x8aad, 0x8ab2,
  0x8b2c, 0x8b2b, 0x8bc2, 0x8e00, 0x8e0d, 0x8e06, 0x8dff, 0x8e03,
  0x8e01, 0x8e10, 0x8e0f, 0x8e05, 0x8e98, 0x8e97, 0x8e96, 0x8e99,
  0x90ac, 0x90ab, 0x9212, 0x93d8, 0x93df, 0x93d6, 0x952d, 0x9532,
  0x983f, 0x982f, 0x9826, 0x983a, 0x9839, 0x0e5f, 0x983b, 0x9835,
  0x982a, 0x9821, 0x9838, 0x9837, 0x9834, 0x0e5c, 0x9822, 0x9836,
  0x9844, 0x9a45, 0x9a3b, 0x9a36, 0x9a42, 0x9c7a, 0x9c86, 0x9c8b,
  0x9c7f, 0x9c81, 0x9e2a, 0x9ed5, 0x9f9f, 0x9f9d,
  /* 0x69 */
  0xa026, 0xa0f4, 0xa0f5, 0xa315, 0xa38e, 0xa38f, 0xa426, 0xa4a7,
  0xa4af, 0xa5ad, 0xa5ac, 0xa5ab, 0xa5aa, 0xab2f, 0xab21, 0xab23,
  0xaba3, 0xab49, 0xab3a, 0xab48, 0xab2d, 0xab25, 0xab29, 0xab32,
  0xab34, 0xab24, 0xab2c, 0xab4b, 0xab3b, 0xab20, 0xab28, 0xaf98,
  0x11f5, 0xaf97, 0x2d04, 0xaf9d, 0xafa8, 0xb0d5, 0xb277, 0xb278,
  0xb272, 0xb273, 0xb302, 0xb43b, 0xb5fe, 0xb60b, 0xb5ff, 0xb607,
  0x1311, 0x130c, 0xb630, 0xb6cd, 0xb6cf, 0xb710, 0xb70a, 0xb783,
  0xb815, 0xb80e, 0xb80c, 0xb902, 0xb8fe, 0xb905, 0xb915, 0xb908,
  0xba7f, 0xba77, 0xba7c, 0xba82, 0xba7e, 0xba78, 0xba7d, 0xba79,
  0xba81, 0xbc4b, 0xbc63, 0xbc64, 0xbc56, 0xbc54, 0xbc4e, 0xbc10,
  0xbc4f, 0xbc57, 0xbc5e, 0xbc51, 0xbc6a, 0xbc69, 0xbead, 0xbea4,
  0xbeac, 0xbea9, 0xbeae, 0x3f4c, 0xc150, 0xc135,
  /* 0x6a */
  0xc132, 0xc2d8, 0xc2d1, 0xc2cf, 0xc2be, 0xc3d5, 0xc3c1, 0xc3c6,
  0xc3c3, 0xc3c2, 0xc3c0, 0xc3c5, 0xc3c7, 0xc3bf, 0xc3c4, 0xc3d4,
  0xc590, 0xc5d2, 0x154e, 0xc5d4, 0xc7af, 0xc7ae, 0xc7b2, 0xc7ad,
  0xc89c, 0xc8a0, 0xc8b6, 0xca3d, 0xca56, 0xca82, 0xcb28, 0xcb2b,
  0xcc3c, 0xcc3e, 0xcc3f, 0xcc42, 0xcc3d, 0xcc41, 0xcc3b, 0xcc49,
  0xcc43, 0xcd39, 0xcd38, 0xce22, 0xce08, 0xce0c, 0xce06, 0xce13,
  0xce04, 0xce20, 0xce1d, 0xce05, 0xce0a, 0xced6, 0xced7, 0xcf36,
  0xcf37, 0xd023, 0xd022, 0xd020, 0xd01a, 0xd01d, 0xd11c, 0xd120,
  0xd177, 0xd27f, 0xd28b, 0xd27d, 0xd299, 0xd284, 0xd289, 0xd285,
  0xd283, 0xd286, 0xd29e, 0xd353, 0xd417, 0x1794, 0xd419, 0xd420,
  0xd41f, 0xd423, 0xd418, 0xd421, 0xd429, 0xd424, 0xd426, 0xd55e,
  0xd56f, 0xd56e, 0xd574, 0xd572, 0xd573, 0xd67d,
  /* 0x6b */
  0xd67e, 0xd685, 0xd67f, 0xd684, 0xd744, 0xd7f5, 0xd7f8, 0xd803,
  0xd7f6, 0xd928, 0x1863, 0xd939, 0xd8fb, 0xd90e, 0xd8fd, 0xd91f,
  0x1861, 0xd903, 0x36d8, 0xd910, 0x185a, 0xd90d, 0xd927, 0xd941,
  0xdb72, 0xdb78, 0xdb80, 0x18e3, 0xdb85, 0xdb7b, 0x387c, 0xdb7d,
  0xdb91, 0xdb88, 0xdbaa, 0xdb8d, 0xdb89, 0xdb95, 0xdb9b, 0xdb8c,
  0xdb9e, 0xdb7c, 0xdb86, 0xdb84, 0xdd68, 0xddc5, 0xddc4, 0xddc9,
  0xddc6, 0xde42, 0xde45, 0xde41, 0xde44, 0xdea6, 0xdec2, 0xdf42,
  0xdf49, 0xdf48, 0xdf4a, 0xdf4c, 0xdf4b, 0xe017, 0xe018, 0xe015,
  0xe052, 0xe054, 0xe053, 0xe09a, 0xe09b, 0xe0dc, 0xe0e4, 0xe191,
  0x19af, 0xe1ba, 0x3f51, 0x3f5b, 0x3fcf, 0x6e05, 0x4c13, 0x4c15,
  0x4c14, 0x4c23, 0x0213, 0x4c11, 0x4c12, 0x0280, 0x4ff0, 0x519b,
  0x5412, 0x5416, 0x5417, 0x54b5, 0x57f4, 0x5ab1,
  /* 0x6c */
  0x5c79, 0x5dff, 0x5dfc, 0x5dfb, 0x5f3f, 0x5f44, 0x609d, 0x6432,
  0x644c, 0x642b, 0x645a, 0x651f, 0x6901, 0x692d, 0x6927, 0x6959,
  0x695a, 0x694d, 0x6958, 0x6b23, 0x6b25, 0x6b2b, 0x6dff, 0x6eae,
  0x72cb, 0x72ca, 0x72d0, 0x72ce, 0x72cc, 0x72d8, 0x72c6, 0x72d2,
  0x72cf, 0x72c8, 0x7617, 0x19b0, 0x7bc4, 0xcd40, 0x7be9, 0x7bf2,
  0x7bfc, 0x7bea, 0x7beb, 0x7bfd, 0x7f78, 0x7f77, 0x7f73, 0x7f9e,
  0x7f79, 0x80a2, 0x8103, 0x8204, 0x8205, 0x83ce, 0x85bf, 0x89f6,
  0x89f7, 0x8b31, 0x8b30, 0x8bc9, 0x8bc7, 0x8e1c, 0x8e28, 0x8e1a,
  0x8e1e, 0x8e1b, 0x8e1f, 0x90bf, 0x90bb, 0x90bc, 0x90c0, 0x921a,
  0x93ef, 0x93ec, 0x93e9, 0x93f0, 0x93fe, 0x9534, 0x986a, 0x9895,
  0x986c, 0x9872, 0x9867, 0x9860, 0x986b, 0x985e, 0x986f, 0x9866,
  0x2946, 0x9862, 0x985d, 0x985c, 0xe365, 0x9a50,
  /* 0x6d */
  0x9c9f, 0x0f23, 0x9c9e, 0x9ca6, 0x9e35, 0x9e38, 0x9e36, 0x9e3a,
  0x9edc, 0xa37c, 0xa3ab, 0x1049, 0xa3a8, 0xa3a7, 0xa42b, 0xa42c,
  0xa428, 0x442b, 0xa4a9, 0xa4aa, 0xa4ab, 0xa4f8, 0xa5b1, 0xa5f0,
  0xa5ef, 0xaba8, 0xab8b, 0xab94, 0xab9e, 0xab8f, 0xab88, 0xab7e,
  0xab81, 0xab30, 0xab9b, 0xab82, 0xab90, 0xab85, 0xab7f, 0xaba9,
  0xabde, 0xad0d, 0x11fa, 0xafcf, 0xafcb, 0xafd8, 0xafdd, 0xafd3,
  0xafd0, 0xafd5, 0xafd6, 0xb0d6, 0xb292, 0xb295, 0xe0cb, 0xb28d,
  0xb29b, 0xb29d, 0xb28f, 0xb29e, 0xb2a6, 0xb396, 0xb392, 0xb616,
  0xb62a, 0xb629, 0xb62c, 0xb715, 0xb712, 0xb711, 0xb713, 0xb788,
  0xb78b, 0xb78a, 0xb787, 0xb817, 0xb816, 0xb81a, 0xb919, 0xb917,
  0xba91, 0xba94, 0xbc8b, 0xbc90, 0xbc8f, 0xbc86, 0xbc83, 0xbc8e,
  0xbc87, 0xbca8, 0xbc85, 0xbca6, 0xbc82, 0xbca7,
  /* 0x6e */
  0xbeb9, 0xbeb7, 0xbeb4, 0xbeb6, 0xbeb3, 0xbec6, 0xc13c, 0xc140,
  0xc138, 0xc291, 0xc2a6, 0xc2da, 0xc3da, 0xc3d8, 0xc3d9, 0xc3db,
  0xc3d7, 0xc616, 0xc612, 0xc61f, 0x1559, 0xc614, 0xc61a, 0xc610,
  0xc7b3, 0xc8ae, 0xc8c1, 0xc8b0, 0xc8af, 0xc8b1, 0xc8ad, 0xc8b2,
  0xc8c4, 0xcb3c, 0xcb3f, 0xcc61, 0xcc66, 0xcd3c, 0xcd3b, 0xce2c,
  0x167e, 0xce2a, 0xce3e, 0xce2f, 0xce32, 0xce27, 0xce29, 0xce40,
  0xcedf, 0xcede, 0xcf3c, 0xcf3b, 0xcf3e, 0xd021, 0xd046, 0xd03c,
  0xd036, 0xd038, 0xd035, 0xd131, 0xd136, 0xd12d, 0xd133, 0xd12f,
  0xd12e, 0xd135, 0xd2ac, 0xd2a9, 0xd2a6, 0x17a8, 0xd44c, 0xd443,
  0xd441, 0xd44f, 0xd442, 0xd451, 0x17a9, 0xd440, 0xd450, 0xd445,
  0xd44a, 0xd44b, 0xd583, 0xd582, 0xd581, 0xd5e0, 0xd698, 0xd69f,
  0xd69b, 0xd69a, 0xd699, 0xd696, 0xd6ae, 0xd69e,
  /* 0x6f */
  0xd809, 0xd80d, 0xd94e, 0xd94a, 0xd94d, 0xd940, 0xd93e, 0xd948,
  0xd942, 0xd962, 0xd945, 0xd951, 0xdbc6, 0xdbd0, 0xdbc0, 0xdbb7,
  0xdbc2, 0xdbbc, 0xdbc5, 0xdbdc, 0xdbdb, 0xdbd2, 0xdbc7, 0xdbb6,
  0xdbc9, 0xdbcc, 0xdbd1, 0xdbcd, 0xdbda, 0xdbba, 0xdbd3, 0xdbce,
  0xdbf6, 0xdbbd, 0xdbdd, 0xdbc8, 0xdc0d, 0xdc35, 0xdd71, 0xdd6e,
  0xdd6f, 0xddd6, 0xde4c, 0xde4f, 0xde54, 0xde53, 0xdec9, 0xdec8,
  0xdf03, 0x1954, 0xdf04, 0x1956, 0xdf57, 0xdf52, 0xdf53, 0x196d,
  0xdf56, 0xdf5c, 0xdf55, 0xe064, 0xe05d, 0xe05e, 0xe0a2, 0xe0a3,
  0x198e, 0xe0e7, 0xe0e6, 0xe198, 0xe1ac, 0xe1af, 0xe1ae, 0x3f59,
  0x40a9, 0x432a, 0x43c7, 0x4c41, 0x4c37, 0x4c35, 0x4c33, 0x4c39,
  0x4c32, 0x4fff, 0x5001, 0x4ff8, 0x541b, 0x5419, 0x56bf, 0x5abc,
  0x5abe, 0x5abd, 0x5c7d, 0x5f46, 0x5f47, 0x60a4,
  /* 0x70 */
  0x6521, 0x6562, 0x6986, 0x0672, 0x6b2f, 0x6b31, 0x0814, 0x72f8,
  0x72f5, 0x72f9, 0x72f2, 0x72fa, 0x72f3, 0x7314, 0x72fd, 0x730f,
  0x730e, 0x7301, 0x7437, 0x7435, 0x7434, 0x7431, 0x757a, 0x757b,
  0x7737, 0x7c2b, 0x7bfb, 0x7c16, 0x7c13, 0x0993, 0x7c11, 0x7c0f,
  0x7c1b, 0x7c38, 0x7fa4, 0x8209, 0x8207, 0x820b, 0x83d3, 0x83d1,
  0x83d8, 0x861d, 0x86a9, 0x86d0, 0xad15, 0x8a02, 0x8a05, 0x8a01,
  0x8a00, 0x8e2e, 0x8e30, 0x8e2f, 0x8e31, 0x90d2, 0x90d3, 0x9402,
  0x9540, 0x9542, 0x953b, 0x95ce, 0x9898, 0x988f, 0x9894, 0x9891,
  0x0e6f, 0x98ba, 0x9890, 0x9886, 0x989a, 0x988c, 0x9893, 0x9887,
  0x9888, 0x9897, 0x988d, 0x989c, 0x98bd, 0x9a3c, 0x9a59, 0x0eb0,
  0x9cd1, 0x9cbb, 0x9cbe, 0x9d5d, 0x9ee2, 0xa105, 0xa3ba, 0x012e,
  0xa3f4, 0xa4b2, 0xa4f9, 0xa5b7, 0xa5b6, 0xab89,
  /* 0x71 */
  0xabf9, 0xabd9, 0xabe8, 0xabd4, 0xabdb, 0xabe2, 0xabdf, 0xabd1,
  0xabe9, 0xabea, 0xad13, 0xad11, 0xaffa, 0xaff8, 0xaff4, 0xaffb,
  0xb00e, 0xb002, 0xb00f, 0xb290, 0xb2ad, 0xb2a9, 0xb448, 0xb65a,
  0xb64f, 0xb64e, 0xb655, 0xb654, 0xb64a, 0xb6d5, 0xb718, 0xb78d,
  0xb81d, 0xb819, 0xb926, 0xb928, 0xb92b, 0xb97d, 0xbaa0, 0xba9a,
  0xba9b, 0xbcb5, 0xbcad, 0xbcb2, 0xbd94, 0xbec9, 0xc14e, 0xc14f,
  0xc144, 0xc152, 0xc3e9, 0xc439, 0x1569, 0x337e, 0xc8c7, 0xc8d3,
  0xc8c6, 0xc8c3, 0x15b4, 0xc8d2, 0xca66, 0xcc7f, 0xcc80, 0xcc84,
  0xcc85, 0xcce3, 0x164d, 0xcd41, 0xcd44, 0xcd43, 0xce4e, 0xce4f,
  0x1683, 0xce49, 0xce4a, 0xce4b, 0xce43, 0xcee0, 0xcee5, 0xcee1,
  0xcee6, 0xcee2, 0xcf0c, 0xcf40, 0xd049, 0xd04a, 0xd054, 0xd04c,
  0xd055, 0xd056, 0xd13b, 0xd13d, 0xd2a4, 0xd2a8,
  /* 0x72 */
  0xd2c3, 0xd2bf, 0xd2c8, 0xd2c2, 0xd2ca, 0xd2cc, 0xd2c9, 0xd2be,
  0xd2cd, 0xd2c7, 0xd2c5, 0xd35d, 0x17b0, 0xd46c, 0xd46b, 0xd470,
  0xd46d, 0xd46f, 0xd489, 0xd484, 0xd58d, 0xd58a, 0xd58e, 0xd591,
  0xd6b5, 0xd6b1, 0xd6af, 0xd6b9, 0xd6b7, 0xd6b0, 0x180c, 0xd717,
  0x1818, 0xd74f, 0xd819, 0xd810, 0xd818, 0xd811, 0xd81c, 0xd812,
  0xd976, 0xd971, 0x3720, 0xd97a, 0xd97f, 0x3722, 0xd973, 0xd9ab,
  0x371e, 0xd977, 0xd974, 0xd97e, 0xd99b, 0xd984, 0xd97c, 0xdc29,
  0xdc2b, 0xdc0e, 0xdc00, 0xdc0b, 0xdbfe, 0xdbfa, 0xdc17, 0xdbff,
  0xdc0c, 0xdc0f, 0x18f5, 0xdc02, 0xdc01, 0xdbfc, 0xdc49, 0xdc06,
  0xdc12, 0xdc13, 0xdd78, 0xde5b, 0xde62, 0xde5f, 0xde5d, 0xdeab,
  0xded5, 0xded4, 0xded3, 0xdf07, 0xdf6c, 0xdf70, 0xdf6e, 0xdf68,
  0xdf6d, 0xdf77, 0xdf6a, 0xdfce, 0xdfec, 0xe069,
  /* 0x73 */
  0xe068, 0xe0a6, 0xe0a9, 0xe0aa, 0xe100, 0xe10d, 0xe0f8, 0xe0fc,
  0xe10a, 0xe0f7, 0xe101, 0xe1b6, 0xe1bb, 0xe1b7, 0xe1b9, 0xe1ca,
  0x3f69, 0x4125, 0x4c59, 0x5007, 0x5009, 0x5422, 0x5607, 0x5604,
  0x6e0f, 0x57f8, 0x5ac7, 0x5ad1, 0x5c7e, 0x5e08, 0x5f4a, 0xe298,
  0x5fb9, 0x6988, 0x6991, 0x6984, 0x6973, 0x6989, 0x6985, 0x6b33,
  0x6e13, 0x731d, 0x731f, 0x731c, 0x7320, 0x731a, 0x731b, 0x7439,
  0x74af, 0x75e5, 0x773c, 0x7c37, 0x7c3a, 0x7fbb, 0x0a0e, 0x8210,
  0x820d, 0x86af, 0x8711, 0x0bd8, 0x8a0d, 0x8a0c, 0x8a0b, 0x8bd4,
  0x8e3d, 0x8e3e, 0x8e3b, 0x8e43, 0x8e40, 0x8e46, 0x8f11, 0x90dd,
  0x90df, 0x90ea, 0x924a, 0x9406, 0x98c1, 0x98b9, 0x98c6, 0x98b8,
  0x98bb, 0x98c8, 0x98c5, 0x98bf, 0x98c7, 0x98c4, 0x9a65, 0x9a67,
  0x9cd7, 0x9cdb, 0x9cd4, 0x9cd6, 0x9ee8, 0xa10a,
  /* 0x74 */
  0xa5bd, 0xa5be, 0xac1a, 0xac0d, 0xac0f, 0xac1b, 0xac10, 0xac11,
  0xac13, 0xad18, 0xb020, 0xb01f, 0xb023, 0xb01d, 0xb037, 0xb025,
  0xb024, 0xb02a, 0xb027, 0xb033, 0xb028, 0xb034, 0xb2ba, 0x2e70,
  0xb39d, 0xb44c, 0xb65c, 0xb66a, 0xb65d, 0xb665, 0xb663, 0xb65e,
  0xb719, 0xb797, 0xb93f, 0xb933, 0xb932, 0xbaa1, 0xbaa5, 0xbaa4,
  0xbaa2, 0xbcc1, 0x142c, 0xbcc7, 0xbcc4, 0xbcc6, 0xbcc5, 0xbcd4,
  0xbcca, 0xc153, 0xc3f1, 0xc421, 0x156e, 0xc6b7, 0xc692, 0xc8d4,
  0xca44, 0xcc98, 0xcc9b, 0xcc91, 0xcc95, 0xcc9a, 0xcc92, 0xce53,
  0xce57, 0x1686, 0xce5c, 0xce5d, 0xce64, 0xceea, 0xceed, 0xcf42,
  0xcf43, 0xd064, 0xd061, 0xd060, 0xd17d, 0xd2d4, 0xd2d5, 0xd2d9,
  0xd487, 0xd499, 0xd48c, 0xd48a, 0xd48f, 0x17b3, 0xd48b, 0xd482,
  0xd49b, 0x17eb, 0xd6c8, 0xd6c4, 0xd6cc, 0xd6c7,
  /* 0x75 */
  0xd6c3, 0xd6c6, 0xd6cb, 0xd6ca, 0xd6c9, 0xd6cd, 0xd753, 0xd821,
  0xd829, 0xd81d, 0xd824, 0xd828, 0x3743, 0xd9b1, 0xd9b2, 0xd9b5,
  0xd9d6, 0xd9af, 0xd9ca, 0xd9b8, 0xe412, 0xe411, 0xdc45, 0xdc47,
  0xdc34, 0xdc6e, 0xdc42, 0xdc31, 0xdc2e, 0xdc56, 0xdc38, 0xdc37,
  0xdc4b, 0xdc2d, 0xdc33, 0xdc36, 0x38e0, 0xdc48, 0xdddc, 0x1942,
  0xde66, 0xde6d, 0xde63, 0xde64, 0x1941, 0xde67, 0xded9, 0xdf0b,
  0xdf7e, 0xdf8b, 0xe026, 0xe02c, 0xe029, 0xe06f, 0xe06b, 0xe06d,
  0xe06e, 0xe11c, 0xe111, 0xe110, 0xe124, 0xe112, 0xe115, 0xe117,
  0x19ac, 0x3f6f, 0x40ab, 0x432e, 0x43c9, 0x4696, 0x4c85, 0x51a0,
  0x542b, 0x5e0d, 0x6b36, 0x3afd, 0x072f, 0x072e, 0x6eb1, 0x734a,
  0x7337, 0x733c, 0x7338, 0x733a, 0x733e, 0x7349, 0x084d, 0x087b,
  0x7580, 0x757f, 0x75e3, 0x773f, 0x7c52, 0x7c4e,
  /* 0x76 */
  0x7c4a, 0x7c4b, 0x7fd5, 0x85d8, 0x8620, 0x86b3, 0x86b1, 0x86b0,
  0x8a17, 0x8bd9, 0x8e49, 0x8f13, 0x90ed, 0x90eb, 0x90ee, 0x940a,
  0x940b, 0x954a, 0x98d5, 0x98d7, 0x98de, 0x98dc, 0x98ee, 0x9a70,
  0x0eb2, 0x9cd9, 0x9ed7, 0xa3cb, 0xa3c7, 0xa4fc, 0xac3b, 0xac39,
  0xac4b, 0xac43, 0xac40, 0xac46, 0xb04d, 0xb043, 0xb047, 0xb04b,
  0xb055, 0xb052, 0xb65f, 0x1322, 0xb67c, 0xb67b, 0xbaa8, 0xbaa9,
  0xbcde, 0xbcd7, 0xbcdd, 0xbcd6, 0xbcd8, 0xbd9b, 0xbee0, 0xbee8,
  0xbee6, 0xc3f8, 0xc3fb, 0xc6bb, 0x3259, 0xc6b8, 0x1570, 0xc7c1,
  0xc7c0, 0xcca4, 0xccab, 0xcd4d, 0xce65, 0xce67, 0xce6a, 0xce66,
  0xce69, 0xd073, 0xd080, 0xd06f, 0xd071, 0xd2e4, 0xd2e6, 0xd2e7,
  0xd4a0, 0xd4a4, 0xd5a2, 0xd5a7, 0xd5a4, 0xd6de, 0xd6db, 0xd758,
  0xd75c, 0xd82f, 0xd82e, 0xd9dd, 0xd9e4, 0xd9d8,
  /* 0x77 */
  0xd9e7, 0xd9da, 0xd975, 0x1895, 0xdc79, 0xdc80, 0xdc7f, 0xdc7c,
  0xdc75, 0xdc7b, 0xdc82, 0x1900, 0xdc89, 0xdc74, 0xdc7d, 0xdc7a,
  0xdc86, 0xdca8, 0xdc72, 0x1902, 0xdc8b, 0xdc91, 0xdcb3, 0xdc81,
  0xdd82, 0xdde1, 0xdde3, 0xdde2, 0xde76, 0xde74, 0xde72, 0xde75,
  0xdf0e, 0xdf0d, 0xdf94, 0xdf92, 0xdf93, 0xdf91, 0xdf8f, 0xdf95,
  0xdfd0, 0xdff7, 0xe076, 0xe0af, 0x199f, 0xe126, 0xe125, 0xe12d,
  0xe1a0, 0xe1c3, 0x3fd7, 0x45cc, 0x4c79, 0x4c7a, 0x5015, 0x5adb,
  0x5c85, 0x6470, 0x647b, 0x69a5, 0x699f, 0x6e17, 0x6eb2, 0x7339,
  0x7340, 0x734e, 0x743e, 0x75e8, 0x75e7, 0x7c66, 0x7c61, 0x7fda,
  0x8214, 0x83df, 0x8a18, 0x8b39, 0x8b3a, 0x9230, 0x9232, 0x940e,
  0x954c, 0x98e8, 0x98f1, 0x98eb, 0x98ec, 0x9a74, 0x9a73, 0x9cf1,
  0x9e42, 0x9e3e, 0x9e41, 0xa02c, 0xa3d2, 0xa4bb,
  /* 0x78 */
  0xac58, 0xac57, 0xac56, 0xac5a, 0x117d, 0xb061, 0xb068, 0xb065,
  0xb05f, 0xb064, 0xb05e, 0xb05b, 0xb067, 0xb2c3, 0xb3a2, 0xb453,
  0xb67d, 0xb720, 0xbaae, 0xbaaf, 0xbab0, 0xbce7, 0xbce6, 0xbce9,
  0xbef1, 0xbeeb, 0xbeea, 0xbee9, 0xc163, 0xc402, 0xc3fe, 0xc6de,
  0xc7c2, 0xc8e6, 0xca5d, 0xccad, 0xce75, 0xce72, 0xce77, 0x16f6,
  0xd151, 0xd2e8, 0xd2ed, 0xd2ee, 0xd4b9, 0xd4a1, 0xd4b6, 0xd5ae,
  0xd6e8, 0x1812, 0xd71e, 0xd831, 0xd832, 0x1891, 0xda0e, 0xda12,
  0xda09, 0xda05, 0x1890, 0xda03, 0xda1f, 0xda0d, 0xda0c, 0xda04,
  0xda0a, 0xdcc2, 0xdcbf, 0xdcc9, 0xdcb2, 0xdcc1, 0xdcaf, 0xdcb4,
  0xdcb0, 0xdcb6, 0xdcb7, 0xdcbb, 0xdcb1, 0xddf0, 0xde78, 0xde7a,
  0xde79, 0xdee4, 0xdee6, 0xdf9f, 0xdf9d, 0xdf98, 0xdf99, 0xdff9,
  0xe030, 0xe082, 0xe081, 0xe0b3, 0xe07f, 0xe13a,
  /* 0x79 */
  0xe13e, 0xe148, 0x4c86, 0x5436, 0x5613, 0x5722, 0x5add, 0x60a7,
  0x647d, 0x0679, 0x6e1c, 0x7365, 0x7360, 0x7367, 0x084e, 0x761a,
  0x85e3, 0x9234, 0x9418, 0x9552, 0x98fc, 0x9a79, 0x9a78, 0x9a76,
  0x9cfa, 0x9cf8, 0xa02d, 0xa3d6, 0xa4bd, 0xa4bf, 0xa4be, 0xac44,
  0xac70, 0xac62, 0xac6e, 0xb06e, 0xb07c, 0xb074, 0xb078, 0xb070,
  0xb079, 0xb071, 0xb2cc, 0xb3a7, 0xb3a6, 0xb693, 0xb721, 0xb79c,
  0xbd9d, 0xbef4, 0xbef3, 0xc8f0, 0xccb8, 0xccb6, 0xccbd, 0xce73,
  0xce82, 0xd087, 0xd156, 0xd159, 0xd2f6, 0xd4c9, 0xd4c5, 0xd4c7,
  0xd4ca, 0xd4c2, 0xd4c4, 0xd6f2, 0xd6f0, 0xd83b, 0xd83a, 0xda26,
  0xda28, 0xda34, 0xda2d, 0xdcd7, 0xdcd2, 0xdcd6, 0xdcdc, 0xdcd3,
  0xdcd1, 0xdd86, 0x191e, 0xddef, 0xddee, 0xdee8, 0xdfac, 0xdfa9,
  0xdfaa, 0xdfab, 0xdffb, 0xe033, 0xe088, 0xe0b6,
  /* 0x7a */
  0xe0b7, 0xe0d0, 0xe0cf, 0xe14f, 0xe159, 0xe14c, 0x5618, 0x5ae0,
  0x7369, 0x7c73, 0x7c72, 0x85e8, 0x90fb, 0x941c, 0x9909, 0x990a,
  0x9908, 0x9a7d, 0x9a7f, 0x9d67, 0xac7a, 0xac7b, 0xb2d1, 0xe3cb,
  0xb69d, 0xb79d, 0xbcfc, 0xbcfb, 0xbcfd, 0xbef5, 0xbef6, 0xc70f,
  0xc71d, 0xccc7, 0xccc1, 0xccbf, 0xcd54, 0xce7f, 0xcef5, 0xd08a,
  0xd08c, 0xd15c, 0xd365, 0xd4cf, 0xd4d0, 0xd5b8, 0xd6fa, 0xd766,
  0xda40, 0xda41, 0x3772, 0xdcf7, 0xdcf3, 0xdcef, 0xdcf4, 0xdced,
  0xdcf2, 0xdcf1, 0xdcf9, 0xdfb4, 0xdffc, 0xe0bc, 0xe15e, 0xe15b,
  0xe15f, 0xe15d, 0xe1cf, 0xe1ce, 0x543a, 0x5ae1, 0x5e15, 0x5e14,
  0x7c78, 0x7c79, 0x7fe6, 0x86b8, 0x8a22, 0x90fe, 0xa3db, 0xa506,
  0xa5c8, 0xac89, 0xb086, 0xb3ad, 0xb699, 0xb6d8, 0xb723, 0xb823,
  0xbab5, 0xbef9, 0xc407, 0xc71e, 0xc8fb, 0xca72,
  /* 0x7b */
  0xca73, 0xce87, 0xcf49, 0xd15d, 0xd2f8, 0xd703, 0xda4e, 0xda4d,
  0xda50, 0xda55, 0xdd09, 0xdd07, 0xdd0c, 0xdd03, 0xdd06, 0xdd0b,
  0xdd0a, 0xdd89, 0xdfbb, 0xdfff, 0xe036, 0xe08b, 0xe166, 0xe169,
  0xe167, 0xe1c2, 0xe1d2, 0x5ae2, 0x0428, 0x6488, 0x6e5b, 0x7376,
  0x7c7d, 0x80a6, 0x8e5a, 0x9917, 0xa3dc, 0xac8f, 0xb094, 0xb095,
  0xbab6, 0xbd04, 0xc2ee, 0xce8a, 0xcef9, 0xd707, 0xd71f, 0xd72a,
  0xd845, 0xda5c, 0xda5b, 0xda61, 0xda5d, 0xdd18, 0xdd1f, 0xde83,
  0xdf16, 0xdf14, 0xdfbf, 0xdfc0, 0xe173, 0xe1c0, 0x5017, 0x6489,
  0x941e, 0x941f, 0x9554, 0x9918, 0x9d05, 0xac95, 0xb098, 0xb09b,
  0xb459, 0xbd08, 0xbf01, 0xccce, 0xcefa, 0xd5be, 0xd847, 0xda6a,
  0xda69, 0xda68, 0xda67, 0xdd25, 0xdd28, 0xdfc4, 0xe037, 0xe08d,
  0xe08c, 0x1991, 0xe320, 0x9922, 0x9a82, 0xb2d9,
  /* 0x7c */
  0xc738, 0xcefb, 0xd4e1, 0xd772, 0xd848, 0xda6c, 0xda70, 0xdd31,
  0xdd30, 0xe179, 0x9923, 0xa3f8, 0xd774, 0xda73, 0xdd34, 0xde87,
  0xdeb2, 0xe0c2, 0xe17d, 0x5ae5, 0xce8c, 0xda77, 0xda75, 0xdd38,
  0xdd3a, 0xe183, 0xe181, 0x7c80, 0xac99, 0x1187, 0xcf4a, 0xd84a,
  0xdd3c, 0xe1c8, 0x9104, 0xb3af, 0xe189, 0xddfa, 0xd161, 0xdd3f,
  0xac93, 0xdfc9, 0xb2de, 0xce91, 0xe18e, 0xe18d, 0xac98, 0xa4c5,
  0xe1a5,
};

static const ucs4_t cns11643_5_2uni_upages[229] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x04c00, 0x04d00, 0x05200, 0x05900, 0x05e00, 0x05f00, 0x06100, 0x06300,
  0x06400, 0x06b00, 0x07200, 0x07300, 0x07500, 0x07600, 0x07900, 0x07a00,
  0x07b00, 0x07c00, 0x08300, 0x08600, 0x08700, 0x08800, 0x08900, 0x08a00,
  0x08c00, 0x08e00, 0x09400, 0x09500, 0x09700, 0x09900, 0x09b00, 0x09c00,
  0x09d00, 0x09e00, 0x0ff00, 0x20000, 0x20100, 0x20200, 0x20300, 0x20400,
  0x20500, 0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20c00,
  0x20d00, 0x20e00, 0x20f00, 0x21000, 0x21100, 0x21200, 0x21300, 0x21400,
  0x21500, 0x21600, 0x21700, 0x21800, 0x21900, 0x21a00, 0x21b00, 0x21c00,
  0x21d00, 0x21e00, 0x21f00, 0x22000, 0x22100, 0x22200, 0x22300, 0x22400,
  0x22500, 0x22600, 0x22700, 0x22800, 0x22900, 0x22a00, 0x22b00, 0x22c00,
  0x22d00, 0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200, 0x23300, 0x23400,
  0x23500, 0x23600, 0x23700, 0x23800, 0x23900, 0x23a00, 0x23b00, 0x23c00,
  0x23d00, 0x23e00, 0x23f00, 0x24000, 0x24100, 0x24200, 0x24300, 0x24400,
  0x24500, 0x24600, 0x24700, 0x24800, 0x24900, 0x24a00, 0x24b00, 0x24c00,
  0x24d00, 0x24e00, 0x24f00, 0x25000, 0x25100, 0x25200, 0x25300, 0x25400,
  0x25500, 0x25600, 0x25700, 0x25800, 0x25900, 0x25a00, 0x25b00, 0x25c00,
  0x25d00, 0x25e00, 0x25f00, 0x26000, 0x26100, 0x26200, 0x26300, 0x26400,
  0x26500, 0x26600, 0x26700, 0x26800, 0x26900, 0x26a00, 0x26b00, 0x26c00,
  0x26d00, 0x26e00, 0x26f00, 0x27000, 0x27100, 0x27200, 0x27300, 0x27400,
  0x27500, 0x27600, 0x27700, 0x27800, 0x27900, 0x27a00, 0x27b00, 0x27c00,
  0x27d00, 0x27e00, 0x27f00, 0x28000, 0x28100, 0x28200, 0x28300, 0x28400,
  0x28500, 0x28600, 0x28700, 0x28800, 0x28900, 0x28a00, 0x28b00, 0x28c00,
  0x28d00, 0x28e00, 0x28f00, 0x29000, 0x29100, 0x29200, 0x29300, 0x29400,
  0x29500, 0x29600, 0x29700, 0x29800, 0x29900, 0x29a00, 0x29b00, 0x29c00,
  0x29d00, 0x29e00, 0x29f00, 0x2a000, 0x2a100, 0x2a200, 0x2a300, 0x2a400,
  0x2a500, 0x2a600, 0x2f800, 0x2f900, 0x2fa00,
};

static int
cns11643_5_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x7c)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        {
          if (i < 8603)
            swc = cns11643_5_2uni_page21[i],
            wc = cns11643_5_2uni_upages[swc>>8] | (swc & 0xff);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

