<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns='http://www.w3.org/1999/xhtml' xml:lang='en'>
<head>
    <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type" />
    <link rel="stylesheet" type="text/css" href="style.css" />
    <title>SOCI</title>
</head>
<body>
<table class="banner" cellpadding="0" cellspacing="0">
    <tr>
    <td class="banner_left">
        SOCI - The C++ Database Access Library
    </td>
    </tr>
</table>

<table class="main">
    <tr>
    <td class="main_navigator">
        <p>Home<br />
        <a href="http://sourceforge.net/project/showfiles.php?group_id=121480" target="_blank">Download</a><br />
        <a href="doc/index.html">Documentation</a><br />
        <a href="articles.html">Articles</a><br />
        <a href="people.html">People</a><br />
        <a href="events.html">Events</a><br />
        <a href="links.html">Links</a><br />
        </p>
        <a href="http://sourceforge.net"><img src="http://sflogo.sourceforge.net/sflogo.php?group_id=121480&amp;type=1" width="88" height="31" border="0" alt="SourceForge.net Logo" /></a>
    </td>
    <td class="main_text">
<p>SOCI is a database access library
for C++ that makes the illusion of <i>embedding</i>
SQL queries in the regular
C++ code, staying entirely within the Standard C++.</p>

<p>The idea is to provide C++ programmers a way to access SQL
databases in the most natural and intuitive way. If you find existing
libraries too difficult for your needs or just distracting, SOCI can be
a good alternative.</p>

<p>The simplest motivating code example for the SQL query that is supposed
to retrieve a single row is:</p>
<pre>
int id = ...;
string name;
int salary;

<span class="bold">sql &lt;&lt; "</span><span class="literal">select name, salary from persons where id = </span><span class="bold">" &lt;&lt; id,
       into(name), into(salary);</span>
</pre>

<p>and the following benefits from extensive support for object-relational
mapping:</p>
<pre>
int id = ...;
Person p;

<span class="bold">sql &lt;&lt; "</span><span class="literal">select first_name, last_name, date_of_birth "
       "from persons where id = </span><span class="bold">" &lt;&lt; id,
       into(p);</span>
</pre>

<p>Integration with STL is also supported:</p>

<pre>
<span class="bold">Rowset&lt;string&gt; rs = (sql.prepare &lt;&lt; "</span><span class="literal">select name from persons</span><span class="bold">");
copy(rs.begin(), rs.end(), ostream_iterator&lt;string&gt;(cout, "\n"));</span>
</pre>

<p>SOCI offers also extensive integration with Boost datatypes (optional, tuple and fusion) and flexible support
for user-defined datatypes.</p>

<p>Even though SOCI is mainly a C++ library, it also allows to use it from other programming languages. Currently the package contains the Ada binding, with more bindings likely to come in the future.</p>

<p>Starting from its 2.0.0 release, SOCI uses the plug-in architecture for
backends - this allows to target various database servers.
Currently (4.0.2), the following database systems are supported:</p>
<ul>
    <li>DB2</li>
    <li>Firebird</li>
    <li>MySQL</li>
    <li>ODBC (generic backend)</li>
    <li>Oracle</li>
    <li>PostgreSQL</li>
    <li>SQLite3</li>
</ul>

<p>The intent of the library is to cover as many database technologies as
possible. For this, the project has to rely on volunteer contributions
from other programmers, who have expertise with the existing database
interfaces and would like to help writing dedicated backends.<br />
If you are interested in participating, please contact the <a
 href="people.html">project admin</a>.</p>

<p>The SOCI library is distributed under the terms of the <a
 target="_blank" href="http://www.boost.org/LICENSE_1_0.txt">Boost
Software License</a>.</p>

<p>All SOCI downloads are hosted on SourceForge.net servers. The current
stable release (4.0.2) can be downloaded <a href="https://sourceforge.net/p/soci/" target="_blank">here</a>,
and all previous releases are available
<a href="http://sourceforge.net/project/showfiles.php?group_id=121480" target="_blank">here</a>.</p>

<p>The development of SOCI happens on GitHub. All repositories live
under the <a target="_blank" href="http://github.com/organizations/SOCI">SOCI</a>
organization where all Git repositories are available.</p>

<p>The main Git repository with SOCI source code can be cloned with:</p>
<pre>
$ git clone git://github.com/SOCI/soci.git
</pre>

<p>The <a href="https://github.com/SOCI/soci/issues">Issues</a> tracker is open
for bug reports and patches submission.</p>

<p>The best way to contribute to SOCI is to follow the typical GitHub workflow:
fork SOCI, apply your edits and submit <a href="https://github.com/SOCI/soci/pulls">Pull Request</a>.
<br /> Feel free to join SOCI development!</p>

<p>To meet other users, please consider subscribing to the
<a target="_blank" href="https://lists.sourceforge.net/lists/listinfo/soci-users">SOCI-users mailing list</a>.
There is also <a target="_blank" href="https://lists.sourceforge.net/lists/listinfo/soci-devel">SOCI-devel mailing list</a>
available dedicated to development discussions only.</p>

<p>There is also community-driven <a target="_blank" href="https://github.com/SOCI/soci/wiki/">Wiki</a>
and <a target="_blank" href="https://github.com/SOCI/soci/wiki/FAQ">FAQ</a> hosted at GitHub, where everybody
is welcome to contribute.</p>
      </td>
    </tr>
</table>

<a href="http://github.com/SOCI"><img style="position: absolute; top: 0; right: 0; border: 0;" src="forkus_github.png" alt="Fork us on GitHub"></a>
</body>
</html>
