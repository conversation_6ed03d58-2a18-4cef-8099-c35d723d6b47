/*
MySQL Data Transfer
Source Host: 127.0.0.1
Source Database: big5
Target Host: 127.0.0.1
Target Database: big5
Date: 2013-11-5 0:17:18
*/

SET FOREIGN_KEY_CHECKS=0;
-- ----------------------------
-- Table structure for itemcard_draw
-- ----------------------------
CREATE TABLE `itemcard_draw` (
  `card` int(11) NOT NULL,
  `weight` int(11) NOT NULL,
  `name` varchar(20) COLLATE big5_bin NOT NULL,
  `id` varchar(20) COLLATE big5_bin NOT NULL,
  `type` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for itemcode
-- ----------------------------
CREATE TABLE `itemcode` (
  `item_sn` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) DEFAULT NULL,
  `item_code` varchar(30) COLLATE big5_bin NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `sell_time` date DEFAULT NULL,
  `sellto` varchar(30) COLLATE big5_bin DEFAULT NULL,
  `sold` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`item_sn`,`item_code`)
) ENGINE=MyISAM AUTO_INCREMENT=175 DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for iteminfo
-- ----------------------------
CREATE TABLE `iteminfo` (
  `item_sn` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) DEFAULT '0',
  `type` int(11) NOT NULL,
  `item_name` varchar(255) COLLATE big5_bin DEFAULT NULL,
  `item_des` text COLLATE big5_bin,
  `item_price` int(11) DEFAULT '0',
  `quantity` int(11) NOT NULL DEFAULT '1',
  `item_pic` varchar(255) COLLATE big5_bin DEFAULT NULL,
  `item_sold` int(11) DEFAULT '0',
  `item_limit` int(11) DEFAULT '0',
  `item_type` int(11) DEFAULT '1',
  `item_up` date DEFAULT NULL,
  `item_down` date DEFAULT NULL,
  PRIMARY KEY (`item_sn`),
  KEY `item_id` (`item_id`),
  KEY `item_id_2` (`item_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1136 DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for itemlog
-- ----------------------------
CREATE TABLE `itemlog` (
  `buyer_id` varchar(20) COLLATE big5_bin DEFAULT NULL,
  `item_buytime` datetime DEFAULT NULL,
  `item_price` int(11) DEFAULT NULL,
  `buyer_registernumber` int(11) DEFAULT NULL,
  `item_name` varchar(30) COLLATE big5_bin DEFAULT NULL,
  `item_id` int(11) DEFAULT NULL,
  `card_pass` varchar(50) COLLATE big5_bin DEFAULT NULL,
  `remind` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_account
-- ----------------------------
CREATE TABLE `tbl_account` (
  `player_id` varchar(20) COLLATE big5_bin NOT NULL DEFAULT '',
  `password` varchar(20) COLLATE big5_bin NOT NULL,
  `online` int(11) NOT NULL DEFAULT '0',
  `Point` int(11) NOT NULL DEFAULT '0',
  `Point2` int(11) NOT NULL DEFAULT '0',
  `LoginTime` datetime NOT NULL,
  `LoginIP` varchar(20) COLLATE big5_bin DEFAULT NULL,
  `RegTime` datetime NOT NULL,
  `RegIP` varchar(20) COLLATE big5_bin DEFAULT NULL,
  `Email` varchar(40) COLLATE big5_bin DEFAULT NULL,
  `ban` int(11) DEFAULT '0',
  `MACCODE` varchar(40) COLLATE big5_bin DEFAULT NULL,
  `Invitor` varchar(12) COLLATE big5_bin DEFAULT NULL,
  PRIMARY KEY (`player_id`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_addressbook
-- ----------------------------
CREATE TABLE `tbl_addressbook` (
  `CdKey` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '-1',
  `Level` int(10) NOT NULL DEFAULT '0',
  `GraphicNo` int(10) NOT NULL DEFAULT '-1',
  `RegistSeqNumber` int(10) NOT NULL DEFAULT '-1',
  `ToRegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ToCdKey` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CharName` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `toGuildID` int(10) NOT NULL DEFAULT '0',
  `toGuildTitleID` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`DataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_addressbook_bak
-- ----------------------------
CREATE TABLE `tbl_addressbook_bak` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '-1',
  `Level` int(10) NOT NULL DEFAULT '0',
  `GraphicNo` int(10) NOT NULL DEFAULT '-1',
  `RegistSeqNumber` int(10) NOT NULL DEFAULT '-1',
  `ToRegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ToCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `toGuildID` int(10) NOT NULL DEFAULT '0',
  `toGuildTitleID` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`DataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_addressbook_del
-- ----------------------------
CREATE TABLE `tbl_addressbook_del` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '-1',
  `Level` int(10) NOT NULL DEFAULT '0',
  `GraphicNo` int(10) NOT NULL DEFAULT '-1',
  `RegistSeqNumber` int(10) NOT NULL DEFAULT '-1',
  `ToRegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ToCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`DataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_character
-- ----------------------------
CREATE TABLE `tbl_character` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Quick` int(11) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `MainJob` int(10) NOT NULL DEFAULT '0',
  `JobRank` int(10) NOT NULL DEFAULT '0',
  `JobAncestry` int(11) NOT NULL DEFAULT '-1',
  `FaceImageNumber` int(10) NOT NULL DEFAULT '0',
  `Gold` int(10) NOT NULL DEFAULT '0',
  `PoolGold` int(10) NOT NULL DEFAULT '0',
  `Power` int(11) NOT NULL DEFAULT '0',
  `Dex` int(11) NOT NULL DEFAULT '0',
  `Intelligence` int(11) NOT NULL DEFAULT '0',
  `Charm` int(10) NOT NULL DEFAULT '0',
  `Fame` int(11) NOT NULL DEFAULT '0',
  `IndexOfEqTitle` int(10) NOT NULL DEFAULT '0',
  `SavePoint` int(10) NOT NULL DEFAULT '0',
  `DefaultPet` int(10) NOT NULL DEFAULT '0',
  `ChatVolume` int(10) NOT NULL DEFAULT '0',
  `HelpPoint` int(10) NOT NULL DEFAULT '0',
  `LoginCount` int(10) NOT NULL DEFAULT '0',
  `TalkCount` int(10) NOT NULL DEFAULT '0',
  `GetPetCount` int(10) NOT NULL DEFAULT '0',
  `SendMailCount` int(10) NOT NULL DEFAULT '0',
  `MergeItemCount` int(10) NOT NULL DEFAULT '0',
  `WalkCount` int(10) NOT NULL DEFAULT '0',
  `DeadPetCount` int(10) NOT NULL DEFAULT '0',
  `ResurrectPetCount` int(10) NOT NULL DEFAULT '0',
  `HealPetCount` int(10) NOT NULL DEFAULT '0',
  `CaptureCount` int(10) NOT NULL DEFAULT '0',
  `OtherFlg` int(10) NOT NULL DEFAULT '0',
  `FameGetValue` int(10) NOT NULL DEFAULT '0',
  `FameGetTime` int(10) NOT NULL DEFAULT '0',
  `FameAutoDownTime` int(10) NOT NULL DEFAULT '0',
  `SickLevel` int(10) NOT NULL DEFAULT '0',
  `RenewalFlg` int(10) NOT NULL DEFAULT '0',
  `EndEvent1` int(10) NOT NULL DEFAULT '0',
  `EndEvent2` int(10) NOT NULL DEFAULT '0',
  `EndEvent3` int(10) NOT NULL DEFAULT '0',
  `EndEvent4` int(10) NOT NULL DEFAULT '0',
  `EndEvent5` int(10) NOT NULL DEFAULT '0',
  `EndEvent6` int(10) NOT NULL DEFAULT '0',
  `EndEvent7` int(10) NOT NULL DEFAULT '0',
  `EndEvent8` int(10) NOT NULL DEFAULT '0',
  `NowEvent1` int(10) NOT NULL DEFAULT '0',
  `NowEvent2` int(10) NOT NULL DEFAULT '0',
  `NowEvent3` int(10) NOT NULL DEFAULT '0',
  `NowEvent4` int(10) NOT NULL DEFAULT '0',
  `NowEvent5` int(10) NOT NULL DEFAULT '0',
  `NowEvent6` int(10) NOT NULL DEFAULT '0',
  `NowEvent7` int(10) NOT NULL DEFAULT '0',
  `NowEvent8` int(10) NOT NULL DEFAULT '0',
  `Recipe1` int(10) NOT NULL DEFAULT '0',
  `Recipe2` int(10) NOT NULL DEFAULT '0',
  `Recipe3` int(10) NOT NULL DEFAULT '0',
  `Recipe4` int(10) NOT NULL DEFAULT '0',
  `Recipe5` int(10) NOT NULL DEFAULT '0',
  `Recipe6` int(10) NOT NULL DEFAULT '0',
  `Recipe7` int(10) NOT NULL DEFAULT '0',
  `Recipe8` int(10) NOT NULL DEFAULT '0',
  `Recipe9` int(10) NOT NULL DEFAULT '0',
  `Recipe10` int(10) NOT NULL DEFAULT '0',
  `Recipe11` int(10) NOT NULL DEFAULT '0',
  `Recipe12` int(10) NOT NULL DEFAULT '0',
  `Recipe13` int(10) NOT NULL DEFAULT '0',
  `Recipe14` int(10) NOT NULL DEFAULT '0',
  `Recipe15` int(10) NOT NULL DEFAULT '0',
  `Recipe16` int(10) NOT NULL DEFAULT '0',
  `Recipe17` int(10) NOT NULL DEFAULT '0',
  `Recipe18` int(10) NOT NULL DEFAULT '0',
  `Recipe19` int(10) NOT NULL DEFAULT '0',
  `Recipe20` int(10) NOT NULL DEFAULT '0',
  `Recipe21` int(10) NOT NULL DEFAULT '0',
  `Recipe22` int(10) NOT NULL DEFAULT '0',
  `Recipe23` int(10) NOT NULL DEFAULT '0',
  `Recipe24` int(10) NOT NULL DEFAULT '0',
  `Recipe25` int(10) NOT NULL DEFAULT '0',
  `Recipe26` int(10) NOT NULL DEFAULT '0',
  `Recipe27` int(10) NOT NULL DEFAULT '0',
  `Recipe28` int(10) NOT NULL DEFAULT '0',
  `Recipe29` int(10) NOT NULL DEFAULT '0',
  `Recipe30` int(10) NOT NULL DEFAULT '0',
  `Recipe31` int(10) NOT NULL DEFAULT '0',
  `Recipe32` int(10) NOT NULL DEFAULT '0',
  `Album1` int(10) NOT NULL DEFAULT '0',
  `Album2` int(10) NOT NULL DEFAULT '0',
  `Album3` int(10) NOT NULL DEFAULT '0',
  `Album4` int(10) NOT NULL DEFAULT '0',
  `Album5` int(10) NOT NULL DEFAULT '0',
  `Album6` int(10) NOT NULL DEFAULT '0',
  `Album7` int(10) NOT NULL DEFAULT '0',
  `Album8` int(10) NOT NULL DEFAULT '0',
  `MaxPoolPetHaveLimit` int(10) NOT NULL DEFAULT '0',
  `MaxPoolItemHaveLimit` int(10) NOT NULL DEFAULT '0',
  `DuelPoint` int(10) NOT NULL DEFAULT '0',
  `PopupNameColor` int(10) NOT NULL DEFAULT '0',
  `Position` int(10) NOT NULL DEFAULT '0',
  `Debugger` tinyint(4) NOT NULL DEFAULT '0',
  `DungeonClr_1` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_2` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_3` int(11) NOT NULL DEFAULT '0',
  `LastSaveTime` int(11) NOT NULL DEFAULT '0',
  `RankDownFlg` tinyint(4) NOT NULL DEFAULT '0',
  `Penalty` int(10) NOT NULL DEFAULT '0',
  `LoginPoint` int(10) NOT NULL DEFAULT '0',
  `FeverResetTime` int(11) NOT NULL DEFAULT '0',
  `FeverHaveTime` int(11) NOT NULL DEFAULT '0',
  `HouseId` int(10) NOT NULL DEFAULT '0',
  `HouseLimit` int(10) NOT NULL DEFAULT '0',
  `TimeOutDungeonId` int(10) NOT NULL DEFAULT '0',
  `TimeOutServerNumber` int(10) NOT NULL DEFAULT '0',
  `OriginalImageNumber` int(10) NOT NULL DEFAULT '0',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  `PetSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `Title1` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId1` int(10) NOT NULL DEFAULT '-1',
  `Title2` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId2` int(10) NOT NULL DEFAULT '-1',
  `Title3` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId3` int(10) NOT NULL DEFAULT '-1',
  `Title4` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId4` int(10) NOT NULL DEFAULT '-1',
  `Title5` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId5` int(10) NOT NULL DEFAULT '-1',
  `Title6` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId6` int(10) NOT NULL DEFAULT '-1',
  `Title7` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId7` int(10) NOT NULL DEFAULT '-1',
  `Title8` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId8` int(10) NOT NULL DEFAULT '-1',
  `Title9` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId9` int(10) NOT NULL DEFAULT '-1',
  `Title10` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId10` int(10) NOT NULL DEFAULT '-1',
  `Title11` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId11` int(10) NOT NULL DEFAULT '-1',
  `Title12` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId12` int(10) NOT NULL DEFAULT '-1',
  `Title13` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId13` int(10) NOT NULL DEFAULT '-1',
  `Title14` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId14` int(10) NOT NULL DEFAULT '-1',
  `Title15` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId15` int(10) NOT NULL DEFAULT '-1',
  `Title16` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId16` int(10) NOT NULL DEFAULT '-1',
  `Title17` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId17` int(10) NOT NULL DEFAULT '-1',
  `Title18` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId18` int(10) NOT NULL DEFAULT '-1',
  `Title19` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId19` int(10) NOT NULL DEFAULT '-1',
  `Title20` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId20` int(10) NOT NULL DEFAULT '-1',
  `Title21` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId21` int(10) NOT NULL DEFAULT '-1',
  `Title22` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId22` int(10) NOT NULL DEFAULT '-1',
  `Title23` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId23` int(10) NOT NULL DEFAULT '-1',
  `Title24` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId24` int(10) NOT NULL DEFAULT '-1',
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `titleID` int(10) NOT NULL DEFAULT '-1',
  `sequence` int(10) NOT NULL DEFAULT '-1',
  `closenessMonster0` int(10) NOT NULL DEFAULT '0',
  `closenessMonster1` int(10) NOT NULL DEFAULT '0',
  `closenessMonster2` int(10) NOT NULL DEFAULT '0',
  `sortType` int(10) NOT NULL DEFAULT '0',
  `globalFlag1` int(10) NOT NULL DEFAULT '0',
  `globalFlag2` int(10) NOT NULL DEFAULT '0',
  `globalFlag3` int(10) NOT NULL DEFAULT '0',
  `globalFlag4` int(10) NOT NULL DEFAULT '0',
  `globalFlag5` int(10) NOT NULL DEFAULT '0',
  `globalFlag6` int(10) NOT NULL DEFAULT '0',
  `globalFlag7` int(10) NOT NULL DEFAULT '0',
  `globalFlag8` int(10) NOT NULL DEFAULT '0',
  `globalFlag9` int(10) NOT NULL DEFAULT '0',
  `globalFlag10` int(10) NOT NULL DEFAULT '0',
  `globalFlag11` int(10) NOT NULL DEFAULT '0',
  `globalFlag12` int(10) NOT NULL DEFAULT '0',
  `globalFlag13` int(10) NOT NULL DEFAULT '0',
  `globalFlag14` int(10) NOT NULL DEFAULT '0',
  `globalFlag15` int(10) NOT NULL DEFAULT '0',
  `globalFlag16` int(10) NOT NULL DEFAULT '0',
  `Title25` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId25` int(10) NOT NULL DEFAULT '-1',
  `Title26` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId26` int(10) NOT NULL DEFAULT '-1',
  `Title27` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId27` int(10) NOT NULL DEFAULT '-1',
  `Title28` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId28` int(10) NOT NULL DEFAULT '-1',
  `Title29` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId29` int(10) NOT NULL DEFAULT '-1',
  `Title30` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId30` int(10) NOT NULL DEFAULT '-1',
  `Title31` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId31` int(10) NOT NULL DEFAULT '-1',
  `Title32` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId32` int(10) NOT NULL DEFAULT '-1',
  `Title33` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId33` int(10) NOT NULL DEFAULT '-1',
  `Title34` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId34` int(10) NOT NULL DEFAULT '-1',
  `Title35` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId35` int(10) NOT NULL DEFAULT '-1',
  `Title36` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId36` int(10) NOT NULL DEFAULT '-1',
  `Title37` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId37` int(10) NOT NULL DEFAULT '-1',
  `Title38` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId38` int(10) NOT NULL DEFAULT '-1',
  `Title39` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId39` int(10) NOT NULL DEFAULT '-1',
  `Title40` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId40` int(10) NOT NULL DEFAULT '-1',
  `Title41` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId41` int(10) NOT NULL DEFAULT '-1',
  `Title42` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId42` int(10) NOT NULL DEFAULT '-1',
  `Title43` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId43` int(10) NOT NULL DEFAULT '-1',
  `Title44` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId44` int(10) NOT NULL DEFAULT '-1',
  `Title45` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId45` int(10) NOT NULL DEFAULT '-1',
  `Title46` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId46` int(10) NOT NULL DEFAULT '-1',
  `Title47` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId47` int(10) NOT NULL DEFAULT '-1',
  `Title48` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId48` int(10) NOT NULL DEFAULT '-1',
  `Title49` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId49` int(10) NOT NULL DEFAULT '-1',
  `Title50` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId50` int(10) NOT NULL DEFAULT '-1',
  `Title51` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId51` int(10) NOT NULL DEFAULT '-1',
  `Title52` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId52` int(10) NOT NULL DEFAULT '-1',
  `Title53` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId53` int(10) NOT NULL DEFAULT '-1',
  `Title54` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId54` int(10) NOT NULL DEFAULT '-1',
  `Title55` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId55` int(10) NOT NULL DEFAULT '-1',
  `Title56` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId56` int(10) NOT NULL DEFAULT '-1',
  `Title57` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId57` int(10) NOT NULL DEFAULT '-1',
  `Title58` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId58` int(10) NOT NULL DEFAULT '-1',
  `Title59` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId59` int(10) NOT NULL DEFAULT '-1',
  `Title60` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId60` int(10) NOT NULL DEFAULT '-1',
  `Title61` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId61` int(10) NOT NULL DEFAULT '-1',
  `Title62` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId62` int(10) NOT NULL DEFAULT '-1',
  `Title63` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId63` int(10) NOT NULL DEFAULT '-1',
  `Title64` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId64` int(10) NOT NULL DEFAULT '-1',
  `globalFlagT1` int(10) NOT NULL DEFAULT '0',
  `globalFlagT2` int(10) NOT NULL DEFAULT '0',
  `globalFlagT3` int(10) NOT NULL DEFAULT '0',
  `globalFlagT4` int(10) NOT NULL DEFAULT '0',
  `globalFlagT5` int(10) NOT NULL DEFAULT '0',
  `globalFlagT6` int(10) NOT NULL DEFAULT '0',
  `globalFlagT7` int(10) NOT NULL DEFAULT '0',
  `globalFlagT8` int(10) NOT NULL DEFAULT '0',
  `Album9` int(10) NOT NULL DEFAULT '0',
  `Album10` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`),
  KEY `INDEX_JOBANCESTRY` (`Debugger`,`JobAncestry`),
  KEY `INDEX_MAINJOB` (`Debugger`,`MainJob`),
  KEY `INDEX_JOBRANK` (`Debugger`,`JobRank`,`LastSaveTime`),
  KEY `INDEX_GUILDID` (`guildID`),
  KEY `INDEX_NAME` (`Name`(10))
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_character_bak
-- ----------------------------
CREATE TABLE `tbl_character_bak` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Quick` int(11) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `MainJob` int(10) NOT NULL DEFAULT '0',
  `JobRank` int(10) NOT NULL DEFAULT '0',
  `JobAncestry` int(11) NOT NULL DEFAULT '-1',
  `FaceImageNumber` int(10) NOT NULL DEFAULT '0',
  `Gold` int(10) NOT NULL DEFAULT '0',
  `PoolGold` int(10) NOT NULL DEFAULT '0',
  `Power` int(11) NOT NULL DEFAULT '0',
  `Dex` int(11) NOT NULL DEFAULT '0',
  `Intelligence` int(11) NOT NULL DEFAULT '0',
  `Charm` int(10) NOT NULL DEFAULT '0',
  `Fame` int(11) NOT NULL DEFAULT '0',
  `IndexOfEqTitle` int(10) NOT NULL DEFAULT '0',
  `SavePoint` int(10) NOT NULL DEFAULT '0',
  `DefaultPet` int(10) NOT NULL DEFAULT '0',
  `ChatVolume` int(10) NOT NULL DEFAULT '0',
  `HelpPoint` int(10) NOT NULL DEFAULT '0',
  `LoginCount` int(10) NOT NULL DEFAULT '0',
  `TalkCount` int(10) NOT NULL DEFAULT '0',
  `GetPetCount` int(10) NOT NULL DEFAULT '0',
  `SendMailCount` int(10) NOT NULL DEFAULT '0',
  `MergeItemCount` int(10) NOT NULL DEFAULT '0',
  `WalkCount` int(10) NOT NULL DEFAULT '0',
  `DeadPetCount` int(10) NOT NULL DEFAULT '0',
  `ResurrectPetCount` int(10) NOT NULL DEFAULT '0',
  `HealPetCount` int(10) NOT NULL DEFAULT '0',
  `CaptureCount` int(10) NOT NULL DEFAULT '0',
  `OtherFlg` int(10) NOT NULL DEFAULT '0',
  `FameGetValue` int(10) NOT NULL DEFAULT '0',
  `FameGetTime` int(10) NOT NULL DEFAULT '0',
  `FameAutoDownTime` int(10) NOT NULL DEFAULT '0',
  `SickLevel` int(10) NOT NULL DEFAULT '0',
  `RenewalFlg` int(10) NOT NULL DEFAULT '0',
  `EndEvent1` int(10) NOT NULL DEFAULT '0',
  `EndEvent2` int(10) NOT NULL DEFAULT '0',
  `EndEvent3` int(10) NOT NULL DEFAULT '0',
  `EndEvent4` int(10) NOT NULL DEFAULT '0',
  `EndEvent5` int(10) NOT NULL DEFAULT '0',
  `EndEvent6` int(10) NOT NULL DEFAULT '0',
  `EndEvent7` int(10) NOT NULL DEFAULT '0',
  `EndEvent8` int(10) NOT NULL DEFAULT '0',
  `NowEvent1` int(10) NOT NULL DEFAULT '0',
  `NowEvent2` int(10) NOT NULL DEFAULT '0',
  `NowEvent3` int(10) NOT NULL DEFAULT '0',
  `NowEvent4` int(10) NOT NULL DEFAULT '0',
  `NowEvent5` int(10) NOT NULL DEFAULT '0',
  `NowEvent6` int(10) NOT NULL DEFAULT '0',
  `NowEvent7` int(10) NOT NULL DEFAULT '0',
  `NowEvent8` int(10) NOT NULL DEFAULT '0',
  `Recipe1` int(10) NOT NULL DEFAULT '0',
  `Recipe2` int(10) NOT NULL DEFAULT '0',
  `Recipe3` int(10) NOT NULL DEFAULT '0',
  `Recipe4` int(10) NOT NULL DEFAULT '0',
  `Recipe5` int(10) NOT NULL DEFAULT '0',
  `Recipe6` int(10) NOT NULL DEFAULT '0',
  `Recipe7` int(10) NOT NULL DEFAULT '0',
  `Recipe8` int(10) NOT NULL DEFAULT '0',
  `Recipe9` int(10) NOT NULL DEFAULT '0',
  `Recipe10` int(10) NOT NULL DEFAULT '0',
  `Recipe11` int(10) NOT NULL DEFAULT '0',
  `Recipe12` int(10) NOT NULL DEFAULT '0',
  `Recipe13` int(10) NOT NULL DEFAULT '0',
  `Recipe14` int(10) NOT NULL DEFAULT '0',
  `Recipe15` int(10) NOT NULL DEFAULT '0',
  `Recipe16` int(10) NOT NULL DEFAULT '0',
  `Recipe17` int(10) NOT NULL DEFAULT '0',
  `Recipe18` int(10) NOT NULL DEFAULT '0',
  `Recipe19` int(10) NOT NULL DEFAULT '0',
  `Recipe20` int(10) NOT NULL DEFAULT '0',
  `Recipe21` int(10) NOT NULL DEFAULT '0',
  `Recipe22` int(10) NOT NULL DEFAULT '0',
  `Recipe23` int(10) NOT NULL DEFAULT '0',
  `Recipe24` int(10) NOT NULL DEFAULT '0',
  `Recipe25` int(10) NOT NULL DEFAULT '0',
  `Recipe26` int(10) NOT NULL DEFAULT '0',
  `Recipe27` int(10) NOT NULL DEFAULT '0',
  `Recipe28` int(10) NOT NULL DEFAULT '0',
  `Recipe29` int(10) NOT NULL DEFAULT '0',
  `Recipe30` int(10) NOT NULL DEFAULT '0',
  `Recipe31` int(10) NOT NULL DEFAULT '0',
  `Recipe32` int(10) NOT NULL DEFAULT '0',
  `Album1` int(10) NOT NULL DEFAULT '0',
  `Album2` int(10) NOT NULL DEFAULT '0',
  `Album3` int(10) NOT NULL DEFAULT '0',
  `Album4` int(10) NOT NULL DEFAULT '0',
  `Album5` int(10) NOT NULL DEFAULT '0',
  `Album6` int(10) NOT NULL DEFAULT '0',
  `Album7` int(10) NOT NULL DEFAULT '0',
  `Album8` int(10) NOT NULL DEFAULT '0',
  `MaxPoolPetHaveLimit` int(10) NOT NULL DEFAULT '0',
  `MaxPoolItemHaveLimit` int(10) NOT NULL DEFAULT '0',
  `DuelPoint` int(10) NOT NULL DEFAULT '0',
  `PopupNameColor` int(10) NOT NULL DEFAULT '0',
  `Position` int(10) NOT NULL DEFAULT '0',
  `Debugger` tinyint(4) NOT NULL DEFAULT '0',
  `DungeonClr_1` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_2` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_3` int(11) NOT NULL DEFAULT '0',
  `LastSaveTime` int(11) NOT NULL DEFAULT '0',
  `RankDownFlg` tinyint(4) NOT NULL DEFAULT '0',
  `Penalty` int(10) NOT NULL DEFAULT '0',
  `LoginPoint` int(10) NOT NULL DEFAULT '0',
  `FeverResetTime` int(11) NOT NULL DEFAULT '0',
  `FeverHaveTime` int(11) NOT NULL DEFAULT '0',
  `HouseId` int(10) NOT NULL DEFAULT '0',
  `HouseLimit` int(10) NOT NULL DEFAULT '0',
  `TimeOutDungeonId` int(10) NOT NULL DEFAULT '0',
  `TimeOutServerNumber` int(10) NOT NULL DEFAULT '0',
  `OriginalImageNumber` int(10) NOT NULL DEFAULT '0',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  `PetSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `Title1` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId1` int(10) NOT NULL DEFAULT '-1',
  `Title2` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId2` int(10) NOT NULL DEFAULT '-1',
  `Title3` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId3` int(10) NOT NULL DEFAULT '-1',
  `Title4` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId4` int(10) NOT NULL DEFAULT '-1',
  `Title5` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId5` int(10) NOT NULL DEFAULT '-1',
  `Title6` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId6` int(10) NOT NULL DEFAULT '-1',
  `Title7` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId7` int(10) NOT NULL DEFAULT '-1',
  `Title8` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId8` int(10) NOT NULL DEFAULT '-1',
  `Title9` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId9` int(10) NOT NULL DEFAULT '-1',
  `Title10` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId10` int(10) NOT NULL DEFAULT '-1',
  `Title11` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId11` int(10) NOT NULL DEFAULT '-1',
  `Title12` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId12` int(10) NOT NULL DEFAULT '-1',
  `Title13` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId13` int(10) NOT NULL DEFAULT '-1',
  `Title14` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId14` int(10) NOT NULL DEFAULT '-1',
  `Title15` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId15` int(10) NOT NULL DEFAULT '-1',
  `Title16` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId16` int(10) NOT NULL DEFAULT '-1',
  `Title17` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId17` int(10) NOT NULL DEFAULT '-1',
  `Title18` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId18` int(10) NOT NULL DEFAULT '-1',
  `Title19` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId19` int(10) NOT NULL DEFAULT '-1',
  `Title20` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId20` int(10) NOT NULL DEFAULT '-1',
  `Title21` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId21` int(10) NOT NULL DEFAULT '-1',
  `Title22` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId22` int(10) NOT NULL DEFAULT '-1',
  `Title23` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId23` int(10) NOT NULL DEFAULT '-1',
  `Title24` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId24` int(10) NOT NULL DEFAULT '-1',
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `titleID` int(10) NOT NULL DEFAULT '-1',
  `sequence` int(10) NOT NULL DEFAULT '-1',
  `closenessMonster0` int(10) NOT NULL DEFAULT '0',
  `closenessMonster1` int(10) NOT NULL DEFAULT '0',
  `closenessMonster2` int(10) NOT NULL DEFAULT '0',
  `sortType` int(10) NOT NULL DEFAULT '0',
  `globalFlag1` int(10) NOT NULL DEFAULT '0',
  `globalFlag2` int(10) NOT NULL DEFAULT '0',
  `globalFlag3` int(10) NOT NULL DEFAULT '0',
  `globalFlag4` int(10) NOT NULL DEFAULT '0',
  `globalFlag5` int(10) NOT NULL DEFAULT '0',
  `globalFlag6` int(10) NOT NULL DEFAULT '0',
  `globalFlag7` int(10) NOT NULL DEFAULT '0',
  `globalFlag8` int(10) NOT NULL DEFAULT '0',
  `globalFlag9` int(10) NOT NULL DEFAULT '0',
  `globalFlag10` int(10) NOT NULL DEFAULT '0',
  `globalFlag11` int(10) NOT NULL DEFAULT '0',
  `globalFlag12` int(10) NOT NULL DEFAULT '0',
  `globalFlag13` int(10) NOT NULL DEFAULT '0',
  `globalFlag14` int(10) NOT NULL DEFAULT '0',
  `globalFlag15` int(10) NOT NULL DEFAULT '0',
  `globalFlag16` int(10) NOT NULL DEFAULT '0',
  `Title25` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId25` int(10) NOT NULL DEFAULT '-1',
  `Title26` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId26` int(10) NOT NULL DEFAULT '-1',
  `Title27` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId27` int(10) NOT NULL DEFAULT '-1',
  `Title28` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId28` int(10) NOT NULL DEFAULT '-1',
  `Title29` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId29` int(10) NOT NULL DEFAULT '-1',
  `Title30` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId30` int(10) NOT NULL DEFAULT '-1',
  `Title31` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId31` int(10) NOT NULL DEFAULT '-1',
  `Title32` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId32` int(10) NOT NULL DEFAULT '-1',
  `Title33` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId33` int(10) NOT NULL DEFAULT '-1',
  `Title34` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId34` int(10) NOT NULL DEFAULT '-1',
  `Title35` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId35` int(10) NOT NULL DEFAULT '-1',
  `Title36` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId36` int(10) NOT NULL DEFAULT '-1',
  `Title37` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId37` int(10) NOT NULL DEFAULT '-1',
  `Title38` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId38` int(10) NOT NULL DEFAULT '-1',
  `Title39` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId39` int(10) NOT NULL DEFAULT '-1',
  `Title40` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId40` int(10) NOT NULL DEFAULT '-1',
  `Title41` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId41` int(10) NOT NULL DEFAULT '-1',
  `Title42` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId42` int(10) NOT NULL DEFAULT '-1',
  `Title43` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId43` int(10) NOT NULL DEFAULT '-1',
  `Title44` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId44` int(10) NOT NULL DEFAULT '-1',
  `Title45` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId45` int(10) NOT NULL DEFAULT '-1',
  `Title46` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId46` int(10) NOT NULL DEFAULT '-1',
  `Title47` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId47` int(10) NOT NULL DEFAULT '-1',
  `Title48` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId48` int(10) NOT NULL DEFAULT '-1',
  `Title49` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId49` int(10) NOT NULL DEFAULT '-1',
  `Title50` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId50` int(10) NOT NULL DEFAULT '-1',
  `Title51` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId51` int(10) NOT NULL DEFAULT '-1',
  `Title52` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId52` int(10) NOT NULL DEFAULT '-1',
  `Title53` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId53` int(10) NOT NULL DEFAULT '-1',
  `Title54` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId54` int(10) NOT NULL DEFAULT '-1',
  `Title55` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId55` int(10) NOT NULL DEFAULT '-1',
  `Title56` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId56` int(10) NOT NULL DEFAULT '-1',
  `Title57` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId57` int(10) NOT NULL DEFAULT '-1',
  `Title58` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId58` int(10) NOT NULL DEFAULT '-1',
  `Title59` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId59` int(10) NOT NULL DEFAULT '-1',
  `Title60` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId60` int(10) NOT NULL DEFAULT '-1',
  `Title61` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId61` int(10) NOT NULL DEFAULT '-1',
  `Title62` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId62` int(10) NOT NULL DEFAULT '-1',
  `Title63` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId63` int(10) NOT NULL DEFAULT '-1',
  `Title64` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId64` int(10) NOT NULL DEFAULT '-1',
  `globalFlagT1` int(10) NOT NULL DEFAULT '0',
  `globalFlagT2` int(10) NOT NULL DEFAULT '0',
  `globalFlagT3` int(10) NOT NULL DEFAULT '0',
  `globalFlagT4` int(10) NOT NULL DEFAULT '0',
  `globalFlagT5` int(10) NOT NULL DEFAULT '0',
  `globalFlagT6` int(10) NOT NULL DEFAULT '0',
  `globalFlagT7` int(10) NOT NULL DEFAULT '0',
  `globalFlagT8` int(10) NOT NULL DEFAULT '0',
  `Album9` int(10) NOT NULL DEFAULT '0',
  `Album10` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`),
  KEY `INDEX_JOBANCESTRY` (`Debugger`,`JobAncestry`),
  KEY `INDEX_MAINJOB` (`Debugger`,`MainJob`),
  KEY `INDEX_JOBRANK` (`Debugger`,`JobRank`,`LastSaveTime`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_character_del
-- ----------------------------
CREATE TABLE `tbl_character_del` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Quick` int(11) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `DataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `MainJob` int(10) NOT NULL DEFAULT '0',
  `JobRank` int(10) NOT NULL DEFAULT '0',
  `JobAncestry` int(11) NOT NULL DEFAULT '-1',
  `FaceImageNumber` int(10) NOT NULL DEFAULT '0',
  `Gold` int(10) NOT NULL DEFAULT '0',
  `PoolGold` int(10) NOT NULL DEFAULT '0',
  `Power` int(11) NOT NULL DEFAULT '0',
  `Dex` int(11) NOT NULL DEFAULT '0',
  `Intelligence` int(11) NOT NULL DEFAULT '0',
  `Charm` int(10) NOT NULL DEFAULT '0',
  `Fame` int(11) NOT NULL DEFAULT '0',
  `IndexOfEqTitle` int(10) NOT NULL DEFAULT '0',
  `SavePoint` int(10) NOT NULL DEFAULT '0',
  `DefaultPet` int(10) NOT NULL DEFAULT '0',
  `ChatVolume` int(10) NOT NULL DEFAULT '0',
  `HelpPoint` int(10) NOT NULL DEFAULT '0',
  `LoginCount` int(10) NOT NULL DEFAULT '0',
  `TalkCount` int(10) NOT NULL DEFAULT '0',
  `GetPetCount` int(10) NOT NULL DEFAULT '0',
  `SendMailCount` int(10) NOT NULL DEFAULT '0',
  `MergeItemCount` int(10) NOT NULL DEFAULT '0',
  `WalkCount` int(10) NOT NULL DEFAULT '0',
  `DeadPetCount` int(10) NOT NULL DEFAULT '0',
  `ResurrectPetCount` int(10) NOT NULL DEFAULT '0',
  `HealPetCount` int(10) NOT NULL DEFAULT '0',
  `CaptureCount` int(10) NOT NULL DEFAULT '0',
  `OtherFlg` int(10) NOT NULL DEFAULT '0',
  `FameGetValue` int(10) NOT NULL DEFAULT '0',
  `FameGetTime` int(10) NOT NULL DEFAULT '0',
  `FameAutoDownTime` int(10) NOT NULL DEFAULT '0',
  `SickLevel` int(10) NOT NULL DEFAULT '0',
  `RenewalFlg` int(10) NOT NULL DEFAULT '0',
  `EndEvent1` int(10) NOT NULL DEFAULT '0',
  `EndEvent2` int(10) NOT NULL DEFAULT '0',
  `EndEvent3` int(10) NOT NULL DEFAULT '0',
  `EndEvent4` int(10) NOT NULL DEFAULT '0',
  `EndEvent5` int(10) NOT NULL DEFAULT '0',
  `EndEvent6` int(10) NOT NULL DEFAULT '0',
  `EndEvent7` int(10) NOT NULL DEFAULT '0',
  `EndEvent8` int(10) NOT NULL DEFAULT '0',
  `NowEvent1` int(10) NOT NULL DEFAULT '0',
  `NowEvent2` int(10) NOT NULL DEFAULT '0',
  `NowEvent3` int(10) NOT NULL DEFAULT '0',
  `NowEvent4` int(10) NOT NULL DEFAULT '0',
  `NowEvent5` int(10) NOT NULL DEFAULT '0',
  `NowEvent6` int(10) NOT NULL DEFAULT '0',
  `NowEvent7` int(10) NOT NULL DEFAULT '0',
  `NowEvent8` int(10) NOT NULL DEFAULT '0',
  `Recipe1` int(10) NOT NULL DEFAULT '0',
  `Recipe2` int(10) NOT NULL DEFAULT '0',
  `Recipe3` int(10) NOT NULL DEFAULT '0',
  `Recipe4` int(10) NOT NULL DEFAULT '0',
  `Recipe5` int(10) NOT NULL DEFAULT '0',
  `Recipe6` int(10) NOT NULL DEFAULT '0',
  `Recipe7` int(10) NOT NULL DEFAULT '0',
  `Recipe8` int(10) NOT NULL DEFAULT '0',
  `Recipe9` int(10) NOT NULL DEFAULT '0',
  `Recipe10` int(10) NOT NULL DEFAULT '0',
  `Recipe11` int(10) NOT NULL DEFAULT '0',
  `Recipe12` int(10) NOT NULL DEFAULT '0',
  `Recipe13` int(10) NOT NULL DEFAULT '0',
  `Recipe14` int(10) NOT NULL DEFAULT '0',
  `Recipe15` int(10) NOT NULL DEFAULT '0',
  `Recipe16` int(10) NOT NULL DEFAULT '0',
  `Recipe17` int(10) NOT NULL DEFAULT '0',
  `Recipe18` int(10) NOT NULL DEFAULT '0',
  `Recipe19` int(10) NOT NULL DEFAULT '0',
  `Recipe20` int(10) NOT NULL DEFAULT '0',
  `Recipe21` int(10) NOT NULL DEFAULT '0',
  `Recipe22` int(10) NOT NULL DEFAULT '0',
  `Recipe23` int(10) NOT NULL DEFAULT '0',
  `Recipe24` int(10) NOT NULL DEFAULT '0',
  `Recipe25` int(10) NOT NULL DEFAULT '0',
  `Recipe26` int(10) NOT NULL DEFAULT '0',
  `Recipe27` int(10) NOT NULL DEFAULT '0',
  `Recipe28` int(10) NOT NULL DEFAULT '0',
  `Recipe29` int(10) NOT NULL DEFAULT '0',
  `Recipe30` int(10) NOT NULL DEFAULT '0',
  `Recipe31` int(10) NOT NULL DEFAULT '0',
  `Recipe32` int(10) NOT NULL DEFAULT '0',
  `Album1` int(10) NOT NULL DEFAULT '0',
  `Album2` int(10) NOT NULL DEFAULT '0',
  `Album3` int(10) NOT NULL DEFAULT '0',
  `Album4` int(10) NOT NULL DEFAULT '0',
  `Album5` int(10) NOT NULL DEFAULT '0',
  `Album6` int(10) NOT NULL DEFAULT '0',
  `Album7` int(10) NOT NULL DEFAULT '0',
  `Album8` int(10) NOT NULL DEFAULT '0',
  `MaxPoolPetHaveLimit` int(10) NOT NULL DEFAULT '0',
  `MaxPoolItemHaveLimit` int(10) NOT NULL DEFAULT '0',
  `DuelPoint` int(10) NOT NULL DEFAULT '0',
  `PopupNameColor` int(10) NOT NULL DEFAULT '0',
  `Position` int(10) NOT NULL DEFAULT '0',
  `Debugger` tinyint(4) NOT NULL DEFAULT '0',
  `DungeonClr_1` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_2` int(11) NOT NULL DEFAULT '0',
  `DungeonClr_3` int(11) NOT NULL DEFAULT '0',
  `LastSaveTime` int(11) NOT NULL DEFAULT '0',
  `RankDownFlg` tinyint(4) NOT NULL DEFAULT '0',
  `Penalty` int(10) NOT NULL DEFAULT '0',
  `LoginPoint` int(10) NOT NULL DEFAULT '0',
  `FeverResetTime` int(11) NOT NULL DEFAULT '0',
  `FeverHaveTime` int(11) NOT NULL DEFAULT '0',
  `HouseId` int(10) NOT NULL DEFAULT '0',
  `HouseLimit` int(10) NOT NULL DEFAULT '0',
  `TimeOutDungeonId` int(10) NOT NULL DEFAULT '0',
  `TimeOutServerNumber` int(10) NOT NULL DEFAULT '0',
  `OriginalImageNumber` int(10) NOT NULL DEFAULT '0',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnTitle` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  `PetSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `Title1` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId1` int(10) NOT NULL DEFAULT '-1',
  `Title2` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId2` int(10) NOT NULL DEFAULT '-1',
  `Title3` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId3` int(10) NOT NULL DEFAULT '-1',
  `Title4` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId4` int(10) NOT NULL DEFAULT '-1',
  `Title5` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId5` int(10) NOT NULL DEFAULT '-1',
  `Title6` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId6` int(10) NOT NULL DEFAULT '-1',
  `Title7` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId7` int(10) NOT NULL DEFAULT '-1',
  `Title8` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId8` int(10) NOT NULL DEFAULT '-1',
  `Title9` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId9` int(10) NOT NULL DEFAULT '-1',
  `Title10` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId10` int(10) NOT NULL DEFAULT '-1',
  `Title11` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId11` int(10) NOT NULL DEFAULT '-1',
  `Title12` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId12` int(10) NOT NULL DEFAULT '-1',
  `Title13` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId13` int(10) NOT NULL DEFAULT '-1',
  `Title14` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId14` int(10) NOT NULL DEFAULT '-1',
  `Title15` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId15` int(10) NOT NULL DEFAULT '-1',
  `Title16` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId16` int(10) NOT NULL DEFAULT '-1',
  `Title17` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId17` int(10) NOT NULL DEFAULT '-1',
  `Title18` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId18` int(10) NOT NULL DEFAULT '-1',
  `Title19` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId19` int(10) NOT NULL DEFAULT '-1',
  `Title20` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId20` int(10) NOT NULL DEFAULT '-1',
  `Title21` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId21` int(10) NOT NULL DEFAULT '-1',
  `Title22` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId22` int(10) NOT NULL DEFAULT '-1',
  `Title23` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId23` int(10) NOT NULL DEFAULT '-1',
  `Title24` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId24` int(10) NOT NULL DEFAULT '-1',
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `titleID` int(10) NOT NULL DEFAULT '-1',
  `sequence` int(10) NOT NULL DEFAULT '-1',
  `closenessMonster0` int(10) NOT NULL DEFAULT '0',
  `closenessMonster1` int(10) NOT NULL DEFAULT '0',
  `closenessMonster2` int(10) NOT NULL DEFAULT '0',
  `sortType` int(10) NOT NULL DEFAULT '0',
  `globalFlag1` int(10) NOT NULL DEFAULT '0',
  `globalFlag2` int(10) NOT NULL DEFAULT '0',
  `globalFlag3` int(10) NOT NULL DEFAULT '0',
  `globalFlag4` int(10) NOT NULL DEFAULT '0',
  `globalFlag5` int(10) NOT NULL DEFAULT '0',
  `globalFlag6` int(10) NOT NULL DEFAULT '0',
  `globalFlag7` int(10) NOT NULL DEFAULT '0',
  `globalFlag8` int(10) NOT NULL DEFAULT '0',
  `globalFlag9` int(10) NOT NULL DEFAULT '0',
  `globalFlag10` int(10) NOT NULL DEFAULT '0',
  `globalFlag11` int(10) NOT NULL DEFAULT '0',
  `globalFlag12` int(10) NOT NULL DEFAULT '0',
  `globalFlag13` int(10) NOT NULL DEFAULT '0',
  `globalFlag14` int(10) NOT NULL DEFAULT '0',
  `globalFlag15` int(10) NOT NULL DEFAULT '0',
  `globalFlag16` int(10) NOT NULL DEFAULT '0',
  `Title25` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId25` int(10) NOT NULL DEFAULT '-1',
  `Title26` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId26` int(10) NOT NULL DEFAULT '-1',
  `Title27` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId27` int(10) NOT NULL DEFAULT '-1',
  `Title28` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId28` int(10) NOT NULL DEFAULT '-1',
  `Title29` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId29` int(10) NOT NULL DEFAULT '-1',
  `Title30` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId30` int(10) NOT NULL DEFAULT '-1',
  `Title31` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId31` int(10) NOT NULL DEFAULT '-1',
  `Title32` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId32` int(10) NOT NULL DEFAULT '-1',
  `Title33` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId33` int(10) NOT NULL DEFAULT '-1',
  `Title34` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId34` int(10) NOT NULL DEFAULT '-1',
  `Title35` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId35` int(10) NOT NULL DEFAULT '-1',
  `Title36` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId36` int(10) NOT NULL DEFAULT '-1',
  `Title37` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId37` int(10) NOT NULL DEFAULT '-1',
  `Title38` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId38` int(10) NOT NULL DEFAULT '-1',
  `Title39` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId39` int(10) NOT NULL DEFAULT '-1',
  `Title40` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId40` int(10) NOT NULL DEFAULT '-1',
  `Title41` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId41` int(10) NOT NULL DEFAULT '-1',
  `Title42` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId42` int(10) NOT NULL DEFAULT '-1',
  `Title43` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId43` int(10) NOT NULL DEFAULT '-1',
  `Title44` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId44` int(10) NOT NULL DEFAULT '-1',
  `Title45` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId45` int(10) NOT NULL DEFAULT '-1',
  `Title46` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId46` int(10) NOT NULL DEFAULT '-1',
  `Title47` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId47` int(10) NOT NULL DEFAULT '-1',
  `Title48` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId48` int(10) NOT NULL DEFAULT '-1',
  `Title49` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId49` int(10) NOT NULL DEFAULT '-1',
  `Title50` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId50` int(10) NOT NULL DEFAULT '-1',
  `Title51` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId51` int(10) NOT NULL DEFAULT '-1',
  `Title52` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId52` int(10) NOT NULL DEFAULT '-1',
  `Title53` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId53` int(10) NOT NULL DEFAULT '-1',
  `Title54` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId54` int(10) NOT NULL DEFAULT '-1',
  `Title55` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId55` int(10) NOT NULL DEFAULT '-1',
  `Title56` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId56` int(10) NOT NULL DEFAULT '-1',
  `Title57` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId57` int(10) NOT NULL DEFAULT '-1',
  `Title58` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId58` int(10) NOT NULL DEFAULT '-1',
  `Title59` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId59` int(10) NOT NULL DEFAULT '-1',
  `Title60` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId60` int(10) NOT NULL DEFAULT '-1',
  `Title61` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId61` int(10) NOT NULL DEFAULT '-1',
  `Title62` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId62` int(10) NOT NULL DEFAULT '-1',
  `Title63` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId63` int(10) NOT NULL DEFAULT '-1',
  `Title64` int(10) NOT NULL DEFAULT '-1',
  `TitleMakeId64` int(10) NOT NULL DEFAULT '-1',
  `globalFlagT1` int(10) NOT NULL DEFAULT '0',
  `globalFlagT2` int(10) NOT NULL DEFAULT '0',
  `globalFlagT3` int(10) NOT NULL DEFAULT '0',
  `globalFlagT4` int(10) NOT NULL DEFAULT '0',
  `globalFlagT5` int(10) NOT NULL DEFAULT '0',
  `globalFlagT6` int(10) NOT NULL DEFAULT '0',
  `globalFlagT7` int(10) NOT NULL DEFAULT '0',
  `globalFlagT8` int(10) NOT NULL DEFAULT '0',
  `Album9` int(10) NOT NULL DEFAULT '0',
  `Album10` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`),
  KEY `INDEX_JOBANCESTRY` (`Debugger`,`JobAncestry`),
  KEY `INDEX_MAINJOB` (`Debugger`,`MainJob`),
  KEY `INDEX_JOBRANK` (`Debugger`,`JobRank`,`LastSaveTime`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_family
-- ----------------------------
CREATE TABLE `tbl_family` (
  `FamilyId` int(10) NOT NULL DEFAULT '0',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Password` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Description` char(255) COLLATE big5_bin NOT NULL DEFAULT '',
  `BankGold` int(10) NOT NULL DEFAULT '0',
  `Level` tinyint(4) NOT NULL DEFAULT '0',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Ready` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`FamilyId`),
  UNIQUE KEY `Name` (`Name`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_family_del
-- ----------------------------
CREATE TABLE `tbl_family_del` (
  `FamilyId` int(10) NOT NULL DEFAULT '0',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Password` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Description` char(255) COLLATE big5_bin NOT NULL DEFAULT '',
  `BankGold` int(10) NOT NULL DEFAULT '0',
  `Level` tinyint(4) NOT NULL DEFAULT '0',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Ready` tinyint(1) NOT NULL DEFAULT '0',
  `DeleteTime` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`FamilyId`),
  UNIQUE KEY `Name` (`Name`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_family_history
-- ----------------------------
CREATE TABLE `tbl_family_history` (
  `FamilyId` int(10) NOT NULL DEFAULT '0',
  `CreateDatetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `author` char(64) COLLATE big5_bin NOT NULL DEFAULT '',
  `title` char(128) COLLATE big5_bin NOT NULL DEFAULT '',
  `content` char(255) COLLATE big5_bin NOT NULL DEFAULT '',
  KEY `FamilyId` (`FamilyId`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_family_member
-- ----------------------------
CREATE TABLE `tbl_family_member` (
  `FamilyId` int(10) NOT NULL DEFAULT '0',
  `MemberName` char(16) COLLATE big5_bin NOT NULL DEFAULT '',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `Rank` tinyint(1) NOT NULL DEFAULT '0',
  `AccessRightSetrights` tinyint(1) NOT NULL DEFAULT '0',
  `AccessRightDepsit` tinyint(1) NOT NULL DEFAULT '0',
  `AccessRightWithdraw` tinyint(1) NOT NULL DEFAULT '0',
  `AccessRightHistory` tinyint(1) NOT NULL DEFAULT '0',
  `AccessRightStorage` tinyint(1) NOT NULL DEFAULT '0',
  `Status` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`),
  KEY `FamilyId` (`FamilyId`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guild
-- ----------------------------
CREATE TABLE `tbl_guild` (
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `serverNumber` tinyint(4) NOT NULL DEFAULT '-1',
  `roomType` int(10) NOT NULL DEFAULT '-1',
  `guildName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `roomName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `brief` char(100) COLLATE big5_bin NOT NULL DEFAULT '',
  `createTime` int(10) NOT NULL DEFAULT '0',
  `lastAccess` int(10) NOT NULL DEFAULT '0',
  `guildMark` int(10) NOT NULL DEFAULT '-1',
  `titleName0` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition0` tinyint(4) NOT NULL DEFAULT '0',
  `titleName1` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition1` tinyint(4) NOT NULL DEFAULT '0',
  `titleName2` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition2` tinyint(4) NOT NULL DEFAULT '0',
  `titleName3` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition3` tinyint(4) NOT NULL DEFAULT '0',
  `titleName4` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition4` tinyint(4) NOT NULL DEFAULT '0',
  `titleName5` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition5` tinyint(4) NOT NULL DEFAULT '0',
  `titleName6` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition6` tinyint(4) NOT NULL DEFAULT '0',
  `titleName7` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition7` tinyint(4) NOT NULL DEFAULT '0',
  `titleName8` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition8` tinyint(4) NOT NULL DEFAULT '0',
  `titleName9` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition9` tinyint(4) NOT NULL DEFAULT '0',
  `titleName10` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition10` tinyint(4) NOT NULL DEFAULT '0',
  `titleName11` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition11` tinyint(4) NOT NULL DEFAULT '0',
  `titleName12` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition12` tinyint(4) NOT NULL DEFAULT '0',
  `titleName13` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition13` tinyint(4) NOT NULL DEFAULT '0',
  `titleName14` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition14` tinyint(4) NOT NULL DEFAULT '0',
  `titleName15` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition15` tinyint(4) NOT NULL DEFAULT '0',
  `titleName16` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition16` tinyint(4) NOT NULL DEFAULT '0',
  `titleName17` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition17` tinyint(4) NOT NULL DEFAULT '0',
  `titleName18` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition18` tinyint(4) NOT NULL DEFAULT '0',
  `titleName19` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition19` tinyint(4) NOT NULL DEFAULT '0',
  `titleName20` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition20` tinyint(4) NOT NULL DEFAULT '0',
  `titleName21` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition21` tinyint(4) NOT NULL DEFAULT '0',
  `titleName22` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition22` tinyint(4) NOT NULL DEFAULT '0',
  `titleName23` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition23` tinyint(4) NOT NULL DEFAULT '0',
  `titleName24` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition24` tinyint(4) NOT NULL DEFAULT '0',
  `titleName25` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition25` tinyint(4) NOT NULL DEFAULT '0',
  `titleName26` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition26` tinyint(4) NOT NULL DEFAULT '0',
  `titleName27` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition27` tinyint(4) NOT NULL DEFAULT '0',
  `titleName28` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition28` tinyint(4) NOT NULL DEFAULT '0',
  `titleName29` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition29` tinyint(4) NOT NULL DEFAULT '0',
  `titleName30` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition30` tinyint(4) NOT NULL DEFAULT '0',
  `titleName31` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition31` tinyint(4) NOT NULL DEFAULT '0',
  `floorID` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`serverNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guild_bak
-- ----------------------------
CREATE TABLE `tbl_guild_bak` (
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `serverNumber` tinyint(4) NOT NULL DEFAULT '-1',
  `roomType` int(10) NOT NULL DEFAULT '-1',
  `guildName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `roomName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `brief` char(100) COLLATE big5_bin NOT NULL DEFAULT '',
  `createTime` int(10) NOT NULL DEFAULT '0',
  `lastAccess` int(10) NOT NULL DEFAULT '0',
  `guildMark` int(10) NOT NULL DEFAULT '-1',
  `titleName0` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition0` tinyint(4) NOT NULL DEFAULT '0',
  `titleName1` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition1` tinyint(4) NOT NULL DEFAULT '0',
  `titleName2` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition2` tinyint(4) NOT NULL DEFAULT '0',
  `titleName3` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition3` tinyint(4) NOT NULL DEFAULT '0',
  `titleName4` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition4` tinyint(4) NOT NULL DEFAULT '0',
  `titleName5` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition5` tinyint(4) NOT NULL DEFAULT '0',
  `titleName6` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition6` tinyint(4) NOT NULL DEFAULT '0',
  `titleName7` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition7` tinyint(4) NOT NULL DEFAULT '0',
  `titleName8` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition8` tinyint(4) NOT NULL DEFAULT '0',
  `titleName9` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition9` tinyint(4) NOT NULL DEFAULT '0',
  `titleName10` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition10` tinyint(4) NOT NULL DEFAULT '0',
  `titleName11` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition11` tinyint(4) NOT NULL DEFAULT '0',
  `titleName12` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition12` tinyint(4) NOT NULL DEFAULT '0',
  `titleName13` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition13` tinyint(4) NOT NULL DEFAULT '0',
  `titleName14` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition14` tinyint(4) NOT NULL DEFAULT '0',
  `titleName15` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition15` tinyint(4) NOT NULL DEFAULT '0',
  `titleName16` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition16` tinyint(4) NOT NULL DEFAULT '0',
  `titleName17` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition17` tinyint(4) NOT NULL DEFAULT '0',
  `titleName18` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition18` tinyint(4) NOT NULL DEFAULT '0',
  `titleName19` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition19` tinyint(4) NOT NULL DEFAULT '0',
  `titleName20` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition20` tinyint(4) NOT NULL DEFAULT '0',
  `titleName21` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition21` tinyint(4) NOT NULL DEFAULT '0',
  `titleName22` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition22` tinyint(4) NOT NULL DEFAULT '0',
  `titleName23` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition23` tinyint(4) NOT NULL DEFAULT '0',
  `titleName24` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition24` tinyint(4) NOT NULL DEFAULT '0',
  `titleName25` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition25` tinyint(4) NOT NULL DEFAULT '0',
  `titleName26` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition26` tinyint(4) NOT NULL DEFAULT '0',
  `titleName27` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition27` tinyint(4) NOT NULL DEFAULT '0',
  `titleName28` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition28` tinyint(4) NOT NULL DEFAULT '0',
  `titleName29` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition29` tinyint(4) NOT NULL DEFAULT '0',
  `titleName30` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition30` tinyint(4) NOT NULL DEFAULT '0',
  `titleName31` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition31` tinyint(4) NOT NULL DEFAULT '0',
  `floorID` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`serverNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guild_del
-- ----------------------------
CREATE TABLE `tbl_guild_del` (
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `serverNumber` tinyint(4) NOT NULL DEFAULT '-1',
  `roomType` int(10) NOT NULL DEFAULT '-1',
  `guildName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `roomName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `brief` char(100) COLLATE big5_bin NOT NULL DEFAULT '',
  `createTime` int(10) NOT NULL DEFAULT '0',
  `lastAccess` int(10) NOT NULL DEFAULT '0',
  `guildMark` int(10) NOT NULL DEFAULT '-1',
  `titleName0` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition0` tinyint(4) NOT NULL DEFAULT '0',
  `titleName1` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition1` tinyint(4) NOT NULL DEFAULT '0',
  `titleName2` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition2` tinyint(4) NOT NULL DEFAULT '0',
  `titleName3` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition3` tinyint(4) NOT NULL DEFAULT '0',
  `titleName4` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition4` tinyint(4) NOT NULL DEFAULT '0',
  `titleName5` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition5` tinyint(4) NOT NULL DEFAULT '0',
  `titleName6` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition6` tinyint(4) NOT NULL DEFAULT '0',
  `titleName7` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition7` tinyint(4) NOT NULL DEFAULT '0',
  `titleName8` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition8` tinyint(4) NOT NULL DEFAULT '0',
  `titleName9` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition9` tinyint(4) NOT NULL DEFAULT '0',
  `titleName10` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition10` tinyint(4) NOT NULL DEFAULT '0',
  `titleName11` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition11` tinyint(4) NOT NULL DEFAULT '0',
  `titleName12` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition12` tinyint(4) NOT NULL DEFAULT '0',
  `titleName13` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition13` tinyint(4) NOT NULL DEFAULT '0',
  `titleName14` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition14` tinyint(4) NOT NULL DEFAULT '0',
  `titleName15` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition15` tinyint(4) NOT NULL DEFAULT '0',
  `titleName16` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition16` tinyint(4) NOT NULL DEFAULT '0',
  `titleName17` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition17` tinyint(4) NOT NULL DEFAULT '0',
  `titleName18` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition18` tinyint(4) NOT NULL DEFAULT '0',
  `titleName19` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition19` tinyint(4) NOT NULL DEFAULT '0',
  `titleName20` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition20` tinyint(4) NOT NULL DEFAULT '0',
  `titleName21` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition21` tinyint(4) NOT NULL DEFAULT '0',
  `titleName22` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition22` tinyint(4) NOT NULL DEFAULT '0',
  `titleName23` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition23` tinyint(4) NOT NULL DEFAULT '0',
  `titleName24` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition24` tinyint(4) NOT NULL DEFAULT '0',
  `titleName25` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition25` tinyint(4) NOT NULL DEFAULT '0',
  `titleName26` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition26` tinyint(4) NOT NULL DEFAULT '0',
  `titleName27` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition27` tinyint(4) NOT NULL DEFAULT '0',
  `titleName28` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition28` tinyint(4) NOT NULL DEFAULT '0',
  `titleName29` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition29` tinyint(4) NOT NULL DEFAULT '0',
  `titleName30` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition30` tinyint(4) NOT NULL DEFAULT '0',
  `titleName31` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition31` tinyint(4) NOT NULL DEFAULT '0',
  `floorID` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`serverNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guild_test
-- ----------------------------
CREATE TABLE `tbl_guild_test` (
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `serverNumber` tinyint(4) NOT NULL DEFAULT '-1',
  `roomType` int(10) NOT NULL DEFAULT '-1',
  `guildName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `roomName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `brief` char(100) COLLATE big5_bin NOT NULL DEFAULT '',
  `createTime` int(10) NOT NULL DEFAULT '0',
  `lastAccess` int(10) NOT NULL DEFAULT '0',
  `guildMark` int(10) NOT NULL DEFAULT '-1',
  `titleName0` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition0` tinyint(4) NOT NULL DEFAULT '0',
  `titleName1` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition1` tinyint(4) NOT NULL DEFAULT '0',
  `titleName2` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition2` tinyint(4) NOT NULL DEFAULT '0',
  `titleName3` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition3` tinyint(4) NOT NULL DEFAULT '0',
  `titleName4` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition4` tinyint(4) NOT NULL DEFAULT '0',
  `titleName5` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition5` tinyint(4) NOT NULL DEFAULT '0',
  `titleName6` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition6` tinyint(4) NOT NULL DEFAULT '0',
  `titleName7` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition7` tinyint(4) NOT NULL DEFAULT '0',
  `titleName8` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition8` tinyint(4) NOT NULL DEFAULT '0',
  `titleName9` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition9` tinyint(4) NOT NULL DEFAULT '0',
  `titleName10` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition10` tinyint(4) NOT NULL DEFAULT '0',
  `titleName11` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition11` tinyint(4) NOT NULL DEFAULT '0',
  `titleName12` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition12` tinyint(4) NOT NULL DEFAULT '0',
  `titleName13` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition13` tinyint(4) NOT NULL DEFAULT '0',
  `titleName14` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition14` tinyint(4) NOT NULL DEFAULT '0',
  `titleName15` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition15` tinyint(4) NOT NULL DEFAULT '0',
  `titleName16` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition16` tinyint(4) NOT NULL DEFAULT '0',
  `titleName17` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition17` tinyint(4) NOT NULL DEFAULT '0',
  `titleName18` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition18` tinyint(4) NOT NULL DEFAULT '0',
  `titleName19` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition19` tinyint(4) NOT NULL DEFAULT '0',
  `titleName20` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition20` tinyint(4) NOT NULL DEFAULT '0',
  `titleName21` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition21` tinyint(4) NOT NULL DEFAULT '0',
  `titleName22` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition22` tinyint(4) NOT NULL DEFAULT '0',
  `titleName23` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition23` tinyint(4) NOT NULL DEFAULT '0',
  `titleName24` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition24` tinyint(4) NOT NULL DEFAULT '0',
  `titleName25` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition25` tinyint(4) NOT NULL DEFAULT '0',
  `titleName26` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition26` tinyint(4) NOT NULL DEFAULT '0',
  `titleName27` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition27` tinyint(4) NOT NULL DEFAULT '0',
  `titleName28` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition28` tinyint(4) NOT NULL DEFAULT '0',
  `titleName29` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition29` tinyint(4) NOT NULL DEFAULT '0',
  `titleName30` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition30` tinyint(4) NOT NULL DEFAULT '0',
  `titleName31` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `commition31` tinyint(4) NOT NULL DEFAULT '0',
  `floorID` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`serverNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guilditembox
-- ----------------------------
CREATE TABLE `tbl_guilditembox` (
  `Id` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `Cost` int(10) NOT NULL DEFAULT '0',
  `Type` int(10) NOT NULL DEFAULT '0',
  `OtherFlg` int(10) NOT NULL DEFAULT '0',
  `EquipBothHand` int(10) NOT NULL DEFAULT '0',
  `AbleUseField` int(10) NOT NULL DEFAULT '0',
  `AbleUseBattle` int(10) NOT NULL DEFAULT '0',
  `Target` int(10) NOT NULL DEFAULT '0',
  `Remain` int(10) NOT NULL DEFAULT '0',
  `MaxRemain` int(10) NOT NULL DEFAULT '0',
  `Level` int(10) NOT NULL DEFAULT '0',
  `BaseFailedProb` int(11) NOT NULL DEFAULT '-1',
  `MaxDurability` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Min` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Max` int(10) NOT NULL DEFAULT '0',
  `AbleEffectBetweenHave` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyModFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyAttack` int(10) NOT NULL DEFAULT '0',
  `ModifyDefence` int(10) NOT NULL DEFAULT '0',
  `ModifyQuick` int(10) NOT NULL DEFAULT '0',
  `ModifyMagic` int(10) NOT NULL DEFAULT '0',
  `ModifyRecovery` int(10) NOT NULL DEFAULT '0',
  `ModifyCritical` int(10) NOT NULL DEFAULT '0',
  `ModifyCounter` int(10) NOT NULL DEFAULT '0',
  `ModifyHitRate` int(10) NOT NULL DEFAULT '0',
  `ModifyAvoid` int(10) NOT NULL DEFAULT '0',
  `ModifyHp` int(10) NOT NULL DEFAULT '0',
  `ModifyForcePoint` int(10) NOT NULL DEFAULT '0',
  `ModifyLuck` int(10) NOT NULL DEFAULT '0',
  `ModifyCharisma` int(10) NOT NULL DEFAULT '0',
  `ModifyMastery` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib2` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue2` int(10) NOT NULL DEFAULT '0',
  `ModifyStamina` int(10) NOT NULL DEFAULT '0',
  `ModifyDex` int(10) NOT NULL DEFAULT '0',
  `ModifyIntelligence` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `SpecialEffect` int(10) NOT NULL DEFAULT '-1',
  `SpecialEffectValue` int(10) NOT NULL DEFAULT '0',
  `SpecialEffectValue2` int(10) NOT NULL DEFAULT '0',
  `Material_Weapon` int(10) NOT NULL DEFAULT '-1',
  `Material_Armour` int(10) NOT NULL DEFAULT '-1',
  `Material_Accessory` int(10) NOT NULL DEFAULT '-1',
  `UseAction` int(10) NOT NULL DEFAULT '0',
  `DropAtLogout` int(10) NOT NULL DEFAULT '0',
  `VanishatDrop` tinyint(4) NOT NULL DEFAULT '0',
  `CanPetMail` tinyint(4) NOT NULL DEFAULT '0',
  `Rss` int(10) NOT NULL DEFAULT '0',
  `CanSell` tinyint(4) NOT NULL DEFAULT '0',
  `Explanation1` int(10) NOT NULL DEFAULT '-1',
  `Explanation2` int(10) NOT NULL DEFAULT '-1',
  `LeakedProb` int(10) NOT NULL DEFAULT '0',
  `RareFlg` int(10) NOT NULL DEFAULT '0',
  `InboxFlg` int(10) NOT NULL DEFAULT '-1',
  `PutTime` int(10) NOT NULL DEFAULT '0',
  `LeakLevel` int(10) NOT NULL DEFAULT '0',
  `MergeFlg` int(10) NOT NULL DEFAULT '0',
  `Durability` int(10) NOT NULL DEFAULT '0',
  `SellUnit` int(10) NOT NULL DEFAULT '0',
  `CreateTime` int(10) DEFAULT '0',
  `Var1` int(10) NOT NULL DEFAULT '0',
  `Var2` int(10) NOT NULL DEFAULT '0',
  `Var3` int(10) NOT NULL DEFAULT '0',
  `Var4` int(10) NOT NULL DEFAULT '0',
  `Adm` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `FirstName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `TrueName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Memo` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Argument` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RenameCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `InitFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `WatchFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UseFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `AttachFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DetachFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DropFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `PrePickUpFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `PickUpFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolItem` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ItemDataPlaceNumber` tinyint(4) NOT NULL DEFAULT '0',
  `SelectItem` tinyint(4) NOT NULL DEFAULT '0',
  `GuildId` int(10) NOT NULL DEFAULT '0',
  `updateTime` int(10) NOT NULL DEFAULT '0',
  `MonsterAttrib` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolItem`,`ItemDataPlaceNumber`,`SelectItem`,`GuildId`),
  KEY `INDEX_GuildId` (`GuildId`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin MAX_ROWS=1000000000 DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guilditembox_bak
-- ----------------------------
CREATE TABLE `tbl_guilditembox_bak` (
  `Id` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `Cost` int(10) NOT NULL DEFAULT '0',
  `Type` int(10) NOT NULL DEFAULT '0',
  `OtherFlg` int(10) NOT NULL DEFAULT '0',
  `EquipBothHand` int(10) NOT NULL DEFAULT '0',
  `AbleUseField` int(10) NOT NULL DEFAULT '0',
  `AbleUseBattle` int(10) NOT NULL DEFAULT '0',
  `Target` int(10) NOT NULL DEFAULT '0',
  `Remain` int(10) NOT NULL DEFAULT '0',
  `MaxRemain` int(10) NOT NULL DEFAULT '0',
  `Level` int(10) NOT NULL DEFAULT '0',
  `BaseFailedProb` int(11) NOT NULL DEFAULT '-1',
  `MaxDurability` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Min` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Max` int(10) NOT NULL DEFAULT '0',
  `AbleEffectBetweenHave` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyModFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyAttack` int(10) NOT NULL DEFAULT '0',
  `ModifyDefence` int(10) NOT NULL DEFAULT '0',
  `ModifyQuick` int(10) NOT NULL DEFAULT '0',
  `ModifyMagic` int(10) NOT NULL DEFAULT '0',
  `ModifyRecovery` int(10) NOT NULL DEFAULT '0',
  `ModifyCritical` int(10) NOT NULL DEFAULT '0',
  `ModifyCounter` int(10) NOT NULL DEFAULT '0',
  `ModifyHitRate` int(10) NOT NULL DEFAULT '0',
  `ModifyAvoid` int(10) NOT NULL DEFAULT '0',
  `ModifyHp` int(10) NOT NULL DEFAULT '0',
  `ModifyForcePoint` int(10) NOT NULL DEFAULT '0',
  `ModifyLuck` int(10) NOT NULL DEFAULT '0',
  `ModifyCharisma` int(10) NOT NULL DEFAULT '0',
  `ModifyMastery` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib2` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue2` int(10) NOT NULL DEFAULT '0',
  `ModifyStamina` int(10) NOT NULL DEFAULT '0',
  `ModifyDex` int(10) NOT NULL DEFAULT '0',
  `ModifyIntelligence` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `SpecialEffect` int(10) NOT NULL DEFAULT '-1',
  `SpecialEffectValue` int(10) NOT NULL DEFAULT '0',
  `SpecialEffectValue2` int(10) NOT NULL DEFAULT '0',
  `Material_Weapon` int(10) NOT NULL DEFAULT '-1',
  `Material_Armour` int(10) NOT NULL DEFAULT '-1',
  `Material_Accessory` int(10) NOT NULL DEFAULT '-1',
  `UseAction` int(10) NOT NULL DEFAULT '0',
  `DropAtLogout` int(10) NOT NULL DEFAULT '0',
  `VanishatDrop` tinyint(4) NOT NULL DEFAULT '0',
  `CanPetMail` tinyint(4) NOT NULL DEFAULT '0',
  `Rss` int(10) NOT NULL DEFAULT '0',
  `CanSell` tinyint(4) NOT NULL DEFAULT '0',
  `Explanation1` int(10) NOT NULL DEFAULT '-1',
  `Explanation2` int(10) NOT NULL DEFAULT '-1',
  `LeakedProb` int(10) NOT NULL DEFAULT '0',
  `RareFlg` int(10) NOT NULL DEFAULT '0',
  `InboxFlg` int(10) NOT NULL DEFAULT '-1',
  `PutTime` int(10) NOT NULL DEFAULT '0',
  `LeakLevel` int(10) NOT NULL DEFAULT '0',
  `MergeFlg` int(10) NOT NULL DEFAULT '0',
  `Durability` int(10) NOT NULL DEFAULT '0',
  `SellUnit` int(10) NOT NULL DEFAULT '0',
  `CreateTime` int(10) DEFAULT '0',
  `Var1` int(10) NOT NULL DEFAULT '0',
  `Var2` int(10) NOT NULL DEFAULT '0',
  `Var3` int(10) NOT NULL DEFAULT '0',
  `Var4` int(10) NOT NULL DEFAULT '0',
  `Adm` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `FirstName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `TrueName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Memo` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Argument` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RenameCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `InitFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `WatchFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UseFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `AttachFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DetachFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `DropFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `PrePickUpFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `PickUpFunc` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolItem` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ItemDataPlaceNumber` tinyint(4) NOT NULL DEFAULT '0',
  `SelectItem` tinyint(4) NOT NULL DEFAULT '0',
  `GuildId` int(10) NOT NULL DEFAULT '0',
  `updateTime` int(10) NOT NULL DEFAULT '0',
  `MonsterAttrib` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolItem`,`ItemDataPlaceNumber`,`SelectItem`,`GuildId`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guilditemboxpet
-- ----------------------------
CREATE TABLE `tbl_guilditemboxpet` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Dex` int(10) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `OwnerRegistNumber` int(11) NOT NULL DEFAULT '-1',
  `Size` int(10) NOT NULL DEFAULT '0',
  `ModLoyalty` int(10) NOT NULL DEFAULT '0',
  `VariableLoyalty` int(10) NOT NULL DEFAULT '0',
  `Slot` int(10) NOT NULL DEFAULT '0',
  `PetGetLv` int(10) NOT NULL DEFAULT '0',
  `Rare` int(10) NOT NULL DEFAULT '0',
  `PutPetTime` int(10) NOT NULL DEFAULT '0',
  `MailMode` int(10) NOT NULL DEFAULT '0',
  `PetMailBufIndex` int(10) NOT NULL DEFAULT '0',
  `PetMailIdleTime` int(10) NOT NULL DEFAULT '0',
  `PetMailFromMapId` int(10) NOT NULL DEFAULT '-1',
  `PetMailFromFloor` int(10) NOT NULL DEFAULT '0',
  `PetMailFromX` int(10) NOT NULL DEFAULT '0',
  `PetMailFromY` int(10) NOT NULL DEFAULT '0',
  `PetMailEffect` int(10) NOT NULL DEFAULT '0',
  `PetSendMailCount` int(10) NOT NULL DEFAULT '0',
  `PetMailDropItemFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ResurrectedCount` int(10) NOT NULL DEFAULT '0',
  `PetRank` int(10) NOT NULL DEFAULT '0',
  `LevelupRandomPattern` int(10) NOT NULL DEFAULT '0',
  `PetId` int(10) NOT NULL DEFAULT '0',
  `DepartureBattleStatus` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `PetSkill1` int(10) NOT NULL DEFAULT '-1',
  `PetSkill2` int(10) NOT NULL DEFAULT '-1',
  `PetSkill3` int(10) NOT NULL DEFAULT '-1',
  `PetSkill4` int(10) NOT NULL DEFAULT '-1',
  `PetSkill5` int(10) NOT NULL DEFAULT '-1',
  `PetSkill6` int(10) NOT NULL DEFAULT '-1',
  `PetSkill7` int(10) NOT NULL DEFAULT '-1',
  `PetSkill8` int(10) NOT NULL DEFAULT '-1',
  `PetSkill9` int(10) NOT NULL DEFAULT '-1',
  `PetSkill10` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo6` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo7` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo8` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo9` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo10` int(10) NOT NULL DEFAULT '-1',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UserPetName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `NamedCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolPet` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `PetDataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `ServerID` int(10) NOT NULL DEFAULT '-1',
  `RoomID` int(10) NOT NULL DEFAULT '-1',
  `BoxPosition` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`ServerID`,`RoomID`,`BoxPosition`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guilditemboxpet_bak
-- ----------------------------
CREATE TABLE `tbl_guilditemboxpet_bak` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Dex` int(10) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `OwnerRegistNumber` int(11) NOT NULL DEFAULT '-1',
  `Size` int(10) NOT NULL DEFAULT '0',
  `ModLoyalty` int(10) NOT NULL DEFAULT '0',
  `VariableLoyalty` int(10) NOT NULL DEFAULT '0',
  `Slot` int(10) NOT NULL DEFAULT '0',
  `PetGetLv` int(10) NOT NULL DEFAULT '0',
  `Rare` int(10) NOT NULL DEFAULT '0',
  `PutPetTime` int(10) NOT NULL DEFAULT '0',
  `MailMode` int(10) NOT NULL DEFAULT '0',
  `PetMailBufIndex` int(10) NOT NULL DEFAULT '0',
  `PetMailIdleTime` int(10) NOT NULL DEFAULT '0',
  `PetMailFromMapId` int(10) NOT NULL DEFAULT '-1',
  `PetMailFromFloor` int(10) NOT NULL DEFAULT '0',
  `PetMailFromX` int(10) NOT NULL DEFAULT '0',
  `PetMailFromY` int(10) NOT NULL DEFAULT '0',
  `PetMailEffect` int(10) NOT NULL DEFAULT '0',
  `PetSendMailCount` int(10) NOT NULL DEFAULT '0',
  `PetMailDropItemFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ResurrectedCount` int(10) NOT NULL DEFAULT '0',
  `PetRank` int(10) NOT NULL DEFAULT '0',
  `LevelupRandomPattern` int(10) NOT NULL DEFAULT '0',
  `PetId` int(10) NOT NULL DEFAULT '0',
  `DepartureBattleStatus` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `PetSkill1` int(10) NOT NULL DEFAULT '-1',
  `PetSkill2` int(10) NOT NULL DEFAULT '-1',
  `PetSkill3` int(10) NOT NULL DEFAULT '-1',
  `PetSkill4` int(10) NOT NULL DEFAULT '-1',
  `PetSkill5` int(10) NOT NULL DEFAULT '-1',
  `PetSkill6` int(10) NOT NULL DEFAULT '-1',
  `PetSkill7` int(10) NOT NULL DEFAULT '-1',
  `PetSkill8` int(10) NOT NULL DEFAULT '-1',
  `PetSkill9` int(10) NOT NULL DEFAULT '-1',
  `PetSkill10` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo6` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo7` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo8` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo9` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo10` int(10) NOT NULL DEFAULT '-1',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UserPetName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `NamedCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolPet` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `PetDataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  `guildID` int(10) NOT NULL DEFAULT '-1',
  `ServerID` int(10) NOT NULL DEFAULT '-1',
  `RoomID` int(10) NOT NULL DEFAULT '-1',
  `BoxPosition` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`guildID`,`ServerID`,`RoomID`,`BoxPosition`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guildmonster
-- ----------------------------
CREATE TABLE `tbl_guildmonster` (
  `guildID` int(10) NOT NULL DEFAULT '0',
  `roomNumber` int(10) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `updateTime` int(10) NOT NULL DEFAULT '0',
  `isUse` int(10) NOT NULL DEFAULT '0',
  `monsterName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `monsterID` int(10) NOT NULL DEFAULT '0',
  `metamorph` int(10) NOT NULL DEFAULT '0',
  `imageNumber` int(10) NOT NULL DEFAULT '0',
  `eggImage` int(10) NOT NULL DEFAULT '0',
  `totalAge` int(10) NOT NULL DEFAULT '0',
  `year` int(10) NOT NULL DEFAULT '0',
  `yearCount` int(10) NOT NULL DEFAULT '0',
  `maxIntake` int(10) NOT NULL DEFAULT '0',
  `hunger` int(10) NOT NULL DEFAULT '0',
  `escapeCnt` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteCnt` int(10) NOT NULL DEFAULT '0',
  `elements1` int(10) NOT NULL DEFAULT '0',
  `elements2` int(10) NOT NULL DEFAULT '0',
  `elements3` int(10) NOT NULL DEFAULT '0',
  `elements4` int(10) NOT NULL DEFAULT '0',
  `personality1` int(10) NOT NULL DEFAULT '0',
  `personality2` int(10) NOT NULL DEFAULT '0',
  `personality3` int(10) NOT NULL DEFAULT '0',
  `personality4` int(10) NOT NULL DEFAULT '0',
  `personality5` int(10) NOT NULL DEFAULT '0',
  `personality6` int(10) NOT NULL DEFAULT '0',
  `personality7` int(10) NOT NULL DEFAULT '0',
  `personality8` int(10) NOT NULL DEFAULT '0',
  `favorite1` int(10) NOT NULL DEFAULT '0',
  `favorite2` int(10) NOT NULL DEFAULT '0',
  `favorite3` int(10) NOT NULL DEFAULT '0',
  `favorite4` int(10) NOT NULL DEFAULT '0',
  `favorite5` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteNum` int(10) NOT NULL DEFAULT '0',
  `mostFavorite1` int(10) NOT NULL DEFAULT '0',
  `mostFavorite2` int(10) NOT NULL DEFAULT '0',
  `mostFavorite3` int(10) NOT NULL DEFAULT '0',
  `mostFavorite4` int(10) NOT NULL DEFAULT '0',
  `mostFavorite5` int(10) NOT NULL DEFAULT '0',
  `poolGold` int(10) NOT NULL DEFAULT '0',
  `itemBox1` int(10) NOT NULL DEFAULT '0',
  `itemBox2` int(10) NOT NULL DEFAULT '0',
  `itemBox3` int(10) NOT NULL DEFAULT '0',
  `itemBox4` int(10) NOT NULL DEFAULT '0',
  `itemBox5` int(10) NOT NULL DEFAULT '0',
  `itemBox6` int(10) NOT NULL DEFAULT '0',
  `itemBox7` int(10) NOT NULL DEFAULT '0',
  `itemBox8` int(10) NOT NULL DEFAULT '0',
  `itemBox9` int(10) NOT NULL DEFAULT '0',
  `itemBox10` int(10) NOT NULL DEFAULT '0',
  `itemBox11` int(10) NOT NULL DEFAULT '0',
  `itemBox12` int(10) NOT NULL DEFAULT '0',
  `itemBox13` int(10) NOT NULL DEFAULT '0',
  `itemBox14` int(10) NOT NULL DEFAULT '0',
  `itemBox15` int(10) NOT NULL DEFAULT '0',
  `itemBox16` int(10) NOT NULL DEFAULT '0',
  `itemBox17` int(10) NOT NULL DEFAULT '0',
  `itemBox18` int(10) NOT NULL DEFAULT '0',
  `itemBox19` int(10) NOT NULL DEFAULT '0',
  `itemBox20` int(10) NOT NULL DEFAULT '0',
  `foodBox1` int(10) NOT NULL DEFAULT '0',
  `foodBox2` int(10) NOT NULL DEFAULT '0',
  `foodBox3` int(10) NOT NULL DEFAULT '0',
  `foodBox4` int(10) NOT NULL DEFAULT '0',
  `foodBox5` int(10) NOT NULL DEFAULT '0',
  `foodBox6` int(10) NOT NULL DEFAULT '0',
  `foodBox7` int(10) NOT NULL DEFAULT '0',
  `foodBox8` int(10) NOT NULL DEFAULT '0',
  `foodBox9` int(10) NOT NULL DEFAULT '0',
  `foodBox10` int(10) NOT NULL DEFAULT '0',
  `foodBox11` int(10) NOT NULL DEFAULT '0',
  `foodBox12` int(10) NOT NULL DEFAULT '0',
  `foodBox13` int(10) NOT NULL DEFAULT '0',
  `foodBox14` int(10) NOT NULL DEFAULT '0',
  `foodBox15` int(10) NOT NULL DEFAULT '0',
  `foodBox16` int(10) NOT NULL DEFAULT '0',
  `foodBox17` int(10) NOT NULL DEFAULT '0',
  `foodBox18` int(10) NOT NULL DEFAULT '0',
  `foodBox19` int(10) NOT NULL DEFAULT '0',
  `foodBox20` int(10) NOT NULL DEFAULT '0',
  `roomID` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`guildID`,`roomNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guildmonster_bak
-- ----------------------------
CREATE TABLE `tbl_guildmonster_bak` (
  `guildID` int(10) NOT NULL DEFAULT '0',
  `roomNumber` int(10) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `updateTime` int(10) NOT NULL DEFAULT '0',
  `isUse` int(10) NOT NULL DEFAULT '0',
  `monsterName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `monsterID` int(10) NOT NULL DEFAULT '0',
  `metamorph` int(10) NOT NULL DEFAULT '0',
  `imageNumber` int(10) NOT NULL DEFAULT '0',
  `eggImage` int(10) NOT NULL DEFAULT '0',
  `totalAge` int(10) NOT NULL DEFAULT '0',
  `year` int(10) NOT NULL DEFAULT '0',
  `yearCount` int(10) NOT NULL DEFAULT '0',
  `maxIntake` int(10) NOT NULL DEFAULT '0',
  `hunger` int(10) NOT NULL DEFAULT '0',
  `escapeCnt` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteCnt` int(10) NOT NULL DEFAULT '0',
  `elements1` int(10) NOT NULL DEFAULT '0',
  `elements2` int(10) NOT NULL DEFAULT '0',
  `elements3` int(10) NOT NULL DEFAULT '0',
  `elements4` int(10) NOT NULL DEFAULT '0',
  `personality1` int(10) NOT NULL DEFAULT '0',
  `personality2` int(10) NOT NULL DEFAULT '0',
  `personality3` int(10) NOT NULL DEFAULT '0',
  `personality4` int(10) NOT NULL DEFAULT '0',
  `personality5` int(10) NOT NULL DEFAULT '0',
  `personality6` int(10) NOT NULL DEFAULT '0',
  `personality7` int(10) NOT NULL DEFAULT '0',
  `personality8` int(10) NOT NULL DEFAULT '0',
  `favorite1` int(10) NOT NULL DEFAULT '0',
  `favorite2` int(10) NOT NULL DEFAULT '0',
  `favorite3` int(10) NOT NULL DEFAULT '0',
  `favorite4` int(10) NOT NULL DEFAULT '0',
  `favorite5` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteNum` int(10) NOT NULL DEFAULT '0',
  `mostFavorite1` int(10) NOT NULL DEFAULT '0',
  `mostFavorite2` int(10) NOT NULL DEFAULT '0',
  `mostFavorite3` int(10) NOT NULL DEFAULT '0',
  `mostFavorite4` int(10) NOT NULL DEFAULT '0',
  `mostFavorite5` int(10) NOT NULL DEFAULT '0',
  `poolGold` int(10) NOT NULL DEFAULT '0',
  `itemBox1` int(10) NOT NULL DEFAULT '0',
  `itemBox2` int(10) NOT NULL DEFAULT '0',
  `itemBox3` int(10) NOT NULL DEFAULT '0',
  `itemBox4` int(10) NOT NULL DEFAULT '0',
  `itemBox5` int(10) NOT NULL DEFAULT '0',
  `itemBox6` int(10) NOT NULL DEFAULT '0',
  `itemBox7` int(10) NOT NULL DEFAULT '0',
  `itemBox8` int(10) NOT NULL DEFAULT '0',
  `itemBox9` int(10) NOT NULL DEFAULT '0',
  `itemBox10` int(10) NOT NULL DEFAULT '0',
  `itemBox11` int(10) NOT NULL DEFAULT '0',
  `itemBox12` int(10) NOT NULL DEFAULT '0',
  `itemBox13` int(10) NOT NULL DEFAULT '0',
  `itemBox14` int(10) NOT NULL DEFAULT '0',
  `itemBox15` int(10) NOT NULL DEFAULT '0',
  `itemBox16` int(10) NOT NULL DEFAULT '0',
  `itemBox17` int(10) NOT NULL DEFAULT '0',
  `itemBox18` int(10) NOT NULL DEFAULT '0',
  `itemBox19` int(10) NOT NULL DEFAULT '0',
  `itemBox20` int(10) NOT NULL DEFAULT '0',
  `foodBox1` int(10) NOT NULL DEFAULT '0',
  `foodBox2` int(10) NOT NULL DEFAULT '0',
  `foodBox3` int(10) NOT NULL DEFAULT '0',
  `foodBox4` int(10) NOT NULL DEFAULT '0',
  `foodBox5` int(10) NOT NULL DEFAULT '0',
  `foodBox6` int(10) NOT NULL DEFAULT '0',
  `foodBox7` int(10) NOT NULL DEFAULT '0',
  `foodBox8` int(10) NOT NULL DEFAULT '0',
  `foodBox9` int(10) NOT NULL DEFAULT '0',
  `foodBox10` int(10) NOT NULL DEFAULT '0',
  `foodBox11` int(10) NOT NULL DEFAULT '0',
  `foodBox12` int(10) NOT NULL DEFAULT '0',
  `foodBox13` int(10) NOT NULL DEFAULT '0',
  `foodBox14` int(10) NOT NULL DEFAULT '0',
  `foodBox15` int(10) NOT NULL DEFAULT '0',
  `foodBox16` int(10) NOT NULL DEFAULT '0',
  `foodBox17` int(10) NOT NULL DEFAULT '0',
  `foodBox18` int(10) NOT NULL DEFAULT '0',
  `foodBox19` int(10) NOT NULL DEFAULT '0',
  `foodBox20` int(10) NOT NULL DEFAULT '0',
  `roomID` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`guildID`,`roomNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_guildmonster_del
-- ----------------------------
CREATE TABLE `tbl_guildmonster_del` (
  `guildID` int(10) NOT NULL DEFAULT '0',
  `roomNumber` int(10) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `updateTime` int(10) NOT NULL DEFAULT '0',
  `isUse` int(10) NOT NULL DEFAULT '0',
  `monsterName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `monsterID` int(10) NOT NULL DEFAULT '0',
  `metamorph` int(10) NOT NULL DEFAULT '0',
  `imageNumber` int(10) NOT NULL DEFAULT '0',
  `eggImage` int(10) NOT NULL DEFAULT '0',
  `totalAge` int(10) NOT NULL DEFAULT '0',
  `year` int(10) NOT NULL DEFAULT '0',
  `yearCount` int(10) NOT NULL DEFAULT '0',
  `maxIntake` int(10) NOT NULL DEFAULT '0',
  `hunger` int(10) NOT NULL DEFAULT '0',
  `escapeCnt` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteCnt` int(10) NOT NULL DEFAULT '0',
  `elements1` int(10) NOT NULL DEFAULT '0',
  `elements2` int(10) NOT NULL DEFAULT '0',
  `elements3` int(10) NOT NULL DEFAULT '0',
  `elements4` int(10) NOT NULL DEFAULT '0',
  `personality1` int(10) NOT NULL DEFAULT '0',
  `personality2` int(10) NOT NULL DEFAULT '0',
  `personality3` int(10) NOT NULL DEFAULT '0',
  `personality4` int(10) NOT NULL DEFAULT '0',
  `personality5` int(10) NOT NULL DEFAULT '0',
  `personality6` int(10) NOT NULL DEFAULT '0',
  `personality7` int(10) NOT NULL DEFAULT '0',
  `personality8` int(10) NOT NULL DEFAULT '0',
  `favorite1` int(10) NOT NULL DEFAULT '0',
  `favorite2` int(10) NOT NULL DEFAULT '0',
  `favorite3` int(10) NOT NULL DEFAULT '0',
  `favorite4` int(10) NOT NULL DEFAULT '0',
  `favorite5` int(10) NOT NULL DEFAULT '0',
  `mostFavoriteNum` int(10) NOT NULL DEFAULT '0',
  `mostFavorite1` int(10) NOT NULL DEFAULT '0',
  `mostFavorite2` int(10) NOT NULL DEFAULT '0',
  `mostFavorite3` int(10) NOT NULL DEFAULT '0',
  `mostFavorite4` int(10) NOT NULL DEFAULT '0',
  `mostFavorite5` int(10) NOT NULL DEFAULT '0',
  `poolGold` int(10) NOT NULL DEFAULT '0',
  `itemBox1` int(10) NOT NULL DEFAULT '0',
  `itemBox2` int(10) NOT NULL DEFAULT '0',
  `itemBox3` int(10) NOT NULL DEFAULT '0',
  `itemBox4` int(10) NOT NULL DEFAULT '0',
  `itemBox5` int(10) NOT NULL DEFAULT '0',
  `itemBox6` int(10) NOT NULL DEFAULT '0',
  `itemBox7` int(10) NOT NULL DEFAULT '0',
  `itemBox8` int(10) NOT NULL DEFAULT '0',
  `itemBox9` int(10) NOT NULL DEFAULT '0',
  `itemBox10` int(10) NOT NULL DEFAULT '0',
  `itemBox11` int(10) NOT NULL DEFAULT '0',
  `itemBox12` int(10) NOT NULL DEFAULT '0',
  `itemBox13` int(10) NOT NULL DEFAULT '0',
  `itemBox14` int(10) NOT NULL DEFAULT '0',
  `itemBox15` int(10) NOT NULL DEFAULT '0',
  `itemBox16` int(10) NOT NULL DEFAULT '0',
  `itemBox17` int(10) NOT NULL DEFAULT '0',
  `itemBox18` int(10) NOT NULL DEFAULT '0',
  `itemBox19` int(10) NOT NULL DEFAULT '0',
  `itemBox20` int(10) NOT NULL DEFAULT '0',
  `foodBox1` int(10) NOT NULL DEFAULT '0',
  `foodBox2` int(10) NOT NULL DEFAULT '0',
  `foodBox3` int(10) NOT NULL DEFAULT '0',
  `foodBox4` int(10) NOT NULL DEFAULT '0',
  `foodBox5` int(10) NOT NULL DEFAULT '0',
  `foodBox6` int(10) NOT NULL DEFAULT '0',
  `foodBox7` int(10) NOT NULL DEFAULT '0',
  `foodBox8` int(10) NOT NULL DEFAULT '0',
  `foodBox9` int(10) NOT NULL DEFAULT '0',
  `foodBox10` int(10) NOT NULL DEFAULT '0',
  `foodBox11` int(10) NOT NULL DEFAULT '0',
  `foodBox12` int(10) NOT NULL DEFAULT '0',
  `foodBox13` int(10) NOT NULL DEFAULT '0',
  `foodBox14` int(10) NOT NULL DEFAULT '0',
  `foodBox15` int(10) NOT NULL DEFAULT '0',
  `foodBox16` int(10) NOT NULL DEFAULT '0',
  `foodBox17` int(10) NOT NULL DEFAULT '0',
  `foodBox18` int(10) NOT NULL DEFAULT '0',
  `foodBox19` int(10) NOT NULL DEFAULT '0',
  `foodBox20` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`guildID`,`roomNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_item
-- ----------------------------
CREATE TABLE `tbl_item` (
  `Id` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `Cost` int(10) NOT NULL DEFAULT '0',
  `Type` int(10) NOT NULL DEFAULT '0',
  `Rank` int(10) NOT NULL DEFAULT '0',
  `EquipBothHand` int(10) NOT NULL DEFAULT '0',
  `AbleUseField` int(10) NOT NULL DEFAULT '0',
  `AbleUseBattle` int(10) NOT NULL DEFAULT '0',
  `Target` int(10) NOT NULL DEFAULT '0',
  `Remain` int(10) NOT NULL DEFAULT '0',
  `MaxRemain` int(10) NOT NULL DEFAULT '0',
  `Level` int(10) NOT NULL DEFAULT '0',
  `BaseFailedProb` int(11) NOT NULL DEFAULT '-1',
  `MaxDurability` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Min` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Max` int(10) NOT NULL DEFAULT '0',
  `AbleEffectBetweenHave` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyModFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyAttack` int(10) NOT NULL DEFAULT '0',
  `ModifyDefence` int(10) NOT NULL DEFAULT '0',
  `ModifyQuick` int(10) NOT NULL DEFAULT '0',
  `ModifyMagic` int(10) NOT NULL DEFAULT '0',
  `ModifyRecovery` int(10) NOT NULL DEFAULT '0',
  `ModifyCritical` int(10) NOT NULL DEFAULT '0',
  `ModifyCounter` int(10) NOT NULL DEFAULT '0',
  `ModifyHitRate` int(10) NOT NULL DEFAULT '0',
  `ModifyAvoid` int(10) NOT NULL DEFAULT '0',
  `ModifyHp` int(10) NOT NULL DEFAULT '0',
  `ModifyForcePoint` int(10) NOT NULL DEFAULT '0',
  `ModifyLuck` int(10) NOT NULL DEFAULT '0',
  `ModifyCharisma` int(10) NOT NULL DEFAULT '0',
  `ModifyMastery` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib2` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue2` int(10) NOT NULL DEFAULT '0',
  `ModifyStamina` int(10) NOT NULL DEFAULT '0',
  `ModifyDex` int(10) NOT NULL DEFAULT '0',
  `ModifyIntelligence` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `SpecialEffect` int(10) NOT NULL DEFAULT '-1',
  `SpecialEffectValue` int(10) NOT NULL DEFAULT '0',
  `SpecialEffectValue2` int(10) NOT NULL DEFAULT '0',
  `Material_Weapon` int(10) NOT NULL DEFAULT '-1',
  `Material_Armour` int(10) NOT NULL DEFAULT '-1',
  `Material_Accessory` int(10) NOT NULL DEFAULT '-1',
  `UseAction` int(10) NOT NULL DEFAULT '0',
  `DropAtLogout` int(10) NOT NULL DEFAULT '0',
  `VanishatDrop` tinyint(4) NOT NULL DEFAULT '0',
  `CanPetMail` tinyint(4) NOT NULL DEFAULT '0',
  `CanMergeFrom` int(10) NOT NULL DEFAULT '0',
  `CanSell` tinyint(4) NOT NULL DEFAULT '0',
  `Explanation1` int(10) NOT NULL DEFAULT '-1',
  `Explanation2` int(10) NOT NULL DEFAULT '-1',
  `LeakedProb` int(10) NOT NULL DEFAULT '0',
  `RareFlg` int(10) NOT NULL DEFAULT '0',
  `InboxFlg` int(10) NOT NULL DEFAULT '-1',
  `PutTime` int(10) NOT NULL DEFAULT '0',
  `LeakLevel` int(10) NOT NULL DEFAULT '0',
  `MergeFlg` int(10) NOT NULL DEFAULT '0',
  `Durability` int(10) NOT NULL DEFAULT '0',
  `BoltacId` int(10) NOT NULL DEFAULT '0',
  `CreateTime` int(10) DEFAULT '0',
  `TimeLimit` int(10) NOT NULL DEFAULT '0',
  `EndTime` int(10) NOT NULL DEFAULT '0',
  `Var1` int(10) NOT NULL DEFAULT '0',
  `Var2` int(10) NOT NULL DEFAULT '0',
  `Var3` int(10) NOT NULL DEFAULT '0',
  `Var4` int(10) NOT NULL DEFAULT '0',
  `Adm` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `FirstName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `TrueName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Memo` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Argument` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RenameCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `InitFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `WatchFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `UseFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `AttachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DetachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DropFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PrePickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolItem` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ItemDataPlaceNumber` tinyint(4) NOT NULL DEFAULT '0',
  `MonsterAttrib` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolItem`,`ItemDataPlaceNumber`),
  KEY `itemid` (`Id`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_item_bak
-- ----------------------------
CREATE TABLE `tbl_item_bak` (
  `Id` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `Cost` int(10) NOT NULL DEFAULT '0',
  `Type` int(10) NOT NULL DEFAULT '0',
  `Rank` int(10) NOT NULL DEFAULT '0',
  `EquipBothHand` int(10) NOT NULL DEFAULT '0',
  `AbleUseField` int(10) NOT NULL DEFAULT '0',
  `AbleUseBattle` int(10) NOT NULL DEFAULT '0',
  `Target` int(10) NOT NULL DEFAULT '0',
  `Remain` int(10) NOT NULL DEFAULT '0',
  `MaxRemain` int(10) NOT NULL DEFAULT '0',
  `Level` int(10) NOT NULL DEFAULT '0',
  `BaseFailedProb` int(11) NOT NULL DEFAULT '-1',
  `MaxDurability` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Min` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Max` int(10) NOT NULL DEFAULT '0',
  `AbleEffectBetweenHave` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyModFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyAttack` int(10) NOT NULL DEFAULT '0',
  `ModifyDefence` int(10) NOT NULL DEFAULT '0',
  `ModifyQuick` int(10) NOT NULL DEFAULT '0',
  `ModifyMagic` int(10) NOT NULL DEFAULT '0',
  `ModifyRecovery` int(10) NOT NULL DEFAULT '0',
  `ModifyCritical` int(10) NOT NULL DEFAULT '0',
  `ModifyCounter` int(10) NOT NULL DEFAULT '0',
  `ModifyHitRate` int(10) NOT NULL DEFAULT '0',
  `ModifyAvoid` int(10) NOT NULL DEFAULT '0',
  `ModifyHp` int(10) NOT NULL DEFAULT '0',
  `ModifyForcePoint` int(10) NOT NULL DEFAULT '0',
  `ModifyLuck` int(10) NOT NULL DEFAULT '0',
  `ModifyCharisma` int(10) NOT NULL DEFAULT '0',
  `ModifyMastery` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib2` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue2` int(10) NOT NULL DEFAULT '0',
  `ModifyStamina` int(10) NOT NULL DEFAULT '0',
  `ModifyDex` int(10) NOT NULL DEFAULT '0',
  `ModifyIntelligence` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `SpecialEffect` int(10) NOT NULL DEFAULT '-1',
  `SpecialEffectValue` int(10) NOT NULL DEFAULT '0',
  `SpecialEffectValue2` int(10) NOT NULL DEFAULT '0',
  `Material_Weapon` int(10) NOT NULL DEFAULT '-1',
  `Material_Armour` int(10) NOT NULL DEFAULT '-1',
  `Material_Accessory` int(10) NOT NULL DEFAULT '-1',
  `UseAction` int(10) NOT NULL DEFAULT '0',
  `DropAtLogout` int(10) NOT NULL DEFAULT '0',
  `VanishatDrop` tinyint(4) NOT NULL DEFAULT '0',
  `CanPetMail` tinyint(4) NOT NULL DEFAULT '0',
  `CanMergeFrom` tinyint(4) NOT NULL DEFAULT '0',
  `CanSell` tinyint(4) NOT NULL DEFAULT '0',
  `Explanation1` int(10) NOT NULL DEFAULT '-1',
  `Explanation2` int(10) NOT NULL DEFAULT '-1',
  `LeakedProb` int(10) NOT NULL DEFAULT '0',
  `RareFlg` int(10) NOT NULL DEFAULT '0',
  `InboxFlg` int(10) NOT NULL DEFAULT '-1',
  `PutTime` int(10) NOT NULL DEFAULT '0',
  `LeakLevel` int(10) NOT NULL DEFAULT '0',
  `MergeFlg` int(10) NOT NULL DEFAULT '0',
  `Durability` int(10) NOT NULL DEFAULT '0',
  `BoltacId` int(10) NOT NULL DEFAULT '0',
  `CreateTime` int(10) DEFAULT '0',
  `TimeLimit` int(10) NOT NULL DEFAULT '0',
  `EndTime` int(10) NOT NULL DEFAULT '0',
  `Var1` int(10) NOT NULL DEFAULT '0',
  `Var2` int(10) NOT NULL DEFAULT '0',
  `Var3` int(10) NOT NULL DEFAULT '0',
  `Var4` int(10) NOT NULL DEFAULT '0',
  `Adm` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `FirstName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `TrueName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Memo` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Argument` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RenameCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `InitFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `WatchFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `UseFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `AttachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DetachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DropFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PrePickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolItem` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ItemDataPlaceNumber` tinyint(4) NOT NULL DEFAULT '0',
  `MonsterAttrib` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolItem`,`ItemDataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_item_del
-- ----------------------------
CREATE TABLE `tbl_item_del` (
  `Id` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `Cost` int(10) NOT NULL DEFAULT '0',
  `Type` int(10) NOT NULL DEFAULT '0',
  `Rank` int(10) NOT NULL DEFAULT '0',
  `EquipBothHand` int(10) NOT NULL DEFAULT '0',
  `AbleUseField` int(10) NOT NULL DEFAULT '0',
  `AbleUseBattle` int(10) NOT NULL DEFAULT '0',
  `Target` int(10) NOT NULL DEFAULT '0',
  `Remain` int(10) NOT NULL DEFAULT '0',
  `MaxRemain` int(10) NOT NULL DEFAULT '0',
  `Level` int(10) NOT NULL DEFAULT '0',
  `BaseFailedProb` int(11) NOT NULL DEFAULT '-1',
  `MaxDurability` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Min` int(10) NOT NULL DEFAULT '0',
  `AttackNum_Max` int(10) NOT NULL DEFAULT '0',
  `AbleEffectBetweenHave` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyModFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ModifyAttack` int(10) NOT NULL DEFAULT '0',
  `ModifyDefence` int(10) NOT NULL DEFAULT '0',
  `ModifyQuick` int(10) NOT NULL DEFAULT '0',
  `ModifyMagic` int(10) NOT NULL DEFAULT '0',
  `ModifyRecovery` int(10) NOT NULL DEFAULT '0',
  `ModifyCritical` int(10) NOT NULL DEFAULT '0',
  `ModifyCounter` int(10) NOT NULL DEFAULT '0',
  `ModifyHitRate` int(10) NOT NULL DEFAULT '0',
  `ModifyAvoid` int(10) NOT NULL DEFAULT '0',
  `ModifyHp` int(10) NOT NULL DEFAULT '0',
  `ModifyForcePoint` int(10) NOT NULL DEFAULT '0',
  `ModifyLuck` int(10) NOT NULL DEFAULT '0',
  `ModifyCharisma` int(10) NOT NULL DEFAULT '0',
  `ModifyMastery` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib` int(10) NOT NULL DEFAULT '0',
  `ModifyAttrib2` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue` int(10) NOT NULL DEFAULT '0',
  `ModifyAttribValue2` int(10) NOT NULL DEFAULT '0',
  `ModifyStamina` int(10) NOT NULL DEFAULT '0',
  `ModifyDex` int(10) NOT NULL DEFAULT '0',
  `ModifyIntelligence` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `SpecialEffect` int(10) NOT NULL DEFAULT '-1',
  `SpecialEffectValue` int(10) NOT NULL DEFAULT '0',
  `SpecialEffectValue2` int(10) NOT NULL DEFAULT '0',
  `Material_Weapon` int(10) NOT NULL DEFAULT '-1',
  `Material_Armour` int(10) NOT NULL DEFAULT '-1',
  `Material_Accessory` int(10) NOT NULL DEFAULT '-1',
  `UseAction` int(10) NOT NULL DEFAULT '0',
  `DropAtLogout` int(10) NOT NULL DEFAULT '0',
  `VanishatDrop` tinyint(4) NOT NULL DEFAULT '0',
  `CanPetMail` tinyint(4) NOT NULL DEFAULT '0',
  `CanMergeFrom` int(10) NOT NULL DEFAULT '0',
  `CanSell` tinyint(4) NOT NULL DEFAULT '0',
  `Explanation1` int(10) NOT NULL DEFAULT '-1',
  `Explanation2` int(10) NOT NULL DEFAULT '-1',
  `LeakedProb` int(10) NOT NULL DEFAULT '0',
  `RareFlg` int(10) NOT NULL DEFAULT '0',
  `InboxFlg` int(10) NOT NULL DEFAULT '-1',
  `PutTime` int(10) NOT NULL DEFAULT '0',
  `LeakLevel` int(10) NOT NULL DEFAULT '0',
  `MergeFlg` int(10) NOT NULL DEFAULT '0',
  `Durability` int(10) NOT NULL DEFAULT '0',
  `BoltacId` int(10) NOT NULL DEFAULT '0',
  `CreateTime` int(10) DEFAULT '0',
  `TimeLimit` int(10) NOT NULL DEFAULT '0',
  `EndTime` int(10) NOT NULL DEFAULT '0',
  `Var1` int(10) NOT NULL DEFAULT '0',
  `Var2` int(10) NOT NULL DEFAULT '0',
  `Var3` int(10) NOT NULL DEFAULT '0',
  `Var4` int(10) NOT NULL DEFAULT '0',
  `Adm` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `FirstName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `TrueName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Memo` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Argument` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RenameCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `InitFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `WatchFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `UseFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `AttachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DetachFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `DropFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PrePickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `PickUpFunc` char(4) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolItem` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `ItemDataPlaceNumber` tinyint(4) NOT NULL DEFAULT '0',
  `MonsterAttrib` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolItem`,`ItemDataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_lock
-- ----------------------------
CREATE TABLE `tbl_lock` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `ServerName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '0',
  `LoginTime` int(10) NOT NULL DEFAULT '0',
  `CharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  PRIMARY KEY (`CdKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_lock2
-- ----------------------------
CREATE TABLE `tbl_lock2` (
  `CdKey` varchar(20) collate big5_bin NOT NULL default '0',
  `MACCODE` varchar(32) collate big5_bin NOT NULL default '0',
  `IP` varchar(20) collate big5_bin NOT NULL default '0',
  `MAC` varchar(20) collate big5_bin NOT NULL default '0'
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_loginlog
-- ----------------------------
CREATE TABLE `tbl_loginlog` (
  `CdKey` varchar(32) COLLATE big5_bin NOT NULL,
  `LoginTime` datetime NOT NULL,
  `LoginIP` varchar(32) COLLATE big5_bin NOT NULL,
  `MACCODE` varchar(32) COLLATE big5_bin NOT NULL,
  `LoginServer` varchar(4) COLLATE big5_bin NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_onlineshop
-- ----------------------------
CREATE TABLE `tbl_onlineshop` (
  `Cdkey` varchar(20) COLLATE big5_bin NOT NULL,
  `ItemName` varchar(30) COLLATE big5_bin NOT NULL,
  `ServiceId` varchar(20) COLLATE big5_bin NOT NULL,
  `Type` int(11) NOT NULL,
  `Id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `BuyTime` datetime NOT NULL,
  `OpenTime` datetime NOT NULL,
  `Open` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_pet
-- ----------------------------
CREATE TABLE `tbl_pet` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Dex` int(10) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `OwnerRegistNumber` int(11) NOT NULL DEFAULT '-1',
  `Size` int(10) NOT NULL DEFAULT '0',
  `ModLoyalty` int(10) NOT NULL DEFAULT '0',
  `VariableLoyalty` int(10) NOT NULL DEFAULT '0',
  `Slot` int(10) NOT NULL DEFAULT '0',
  `PetGetLv` int(10) NOT NULL DEFAULT '0',
  `Rare` int(10) NOT NULL DEFAULT '0',
  `PutPetTime` int(10) NOT NULL DEFAULT '0',
  `MailMode` int(10) NOT NULL DEFAULT '0',
  `PetMailBufIndex` int(10) NOT NULL DEFAULT '0',
  `PetMailIdleTime` int(10) NOT NULL DEFAULT '0',
  `PetMailFromMapId` int(10) NOT NULL DEFAULT '-1',
  `PetMailFromFloor` int(10) NOT NULL DEFAULT '0',
  `PetMailFromX` int(10) NOT NULL DEFAULT '0',
  `PetMailFromY` int(10) NOT NULL DEFAULT '0',
  `PetMailEffect` int(10) NOT NULL DEFAULT '0',
  `PetSendMailCount` int(10) NOT NULL DEFAULT '0',
  `PetMailDropItemFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ResurrectedCount` int(10) NOT NULL DEFAULT '0',
  `PetRank` int(10) NOT NULL DEFAULT '0',
  `LevelupRandomPattern` int(10) NOT NULL DEFAULT '0',
  `PetId` int(10) NOT NULL DEFAULT '0',
  `DepartureBattleStatus` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `PetSkill1` int(10) NOT NULL DEFAULT '-1',
  `PetSkill2` int(10) NOT NULL DEFAULT '-1',
  `PetSkill3` int(10) NOT NULL DEFAULT '-1',
  `PetSkill4` int(10) NOT NULL DEFAULT '-1',
  `PetSkill5` int(10) NOT NULL DEFAULT '-1',
  `PetSkill6` int(10) NOT NULL DEFAULT '-1',
  `PetSkill7` int(10) NOT NULL DEFAULT '-1',
  `PetSkill8` int(10) NOT NULL DEFAULT '-1',
  `PetSkill9` int(10) NOT NULL DEFAULT '-1',
  `PetSkill10` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo6` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo7` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo8` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo9` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo10` int(10) NOT NULL DEFAULT '-1',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UserPetName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `NamedCdKey` char(32) COLLATE big5_bin DEFAULT NULL,
  `OwnerCdKey` char(32) COLLATE big5_bin DEFAULT NULL,
  `OwnerCharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolPet` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `PetDataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolPet`,`PetDataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_pet_bak
-- ----------------------------
CREATE TABLE `tbl_pet_bak` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Dex` int(10) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `OwnerRegistNumber` int(11) NOT NULL DEFAULT '-1',
  `Size` int(10) NOT NULL DEFAULT '0',
  `ModLoyalty` int(10) NOT NULL DEFAULT '0',
  `VariableLoyalty` int(10) NOT NULL DEFAULT '0',
  `Slot` int(10) NOT NULL DEFAULT '0',
  `PetGetLv` int(10) NOT NULL DEFAULT '0',
  `Rare` int(10) NOT NULL DEFAULT '0',
  `PutPetTime` int(10) NOT NULL DEFAULT '0',
  `MailMode` int(10) NOT NULL DEFAULT '0',
  `PetMailBufIndex` int(10) NOT NULL DEFAULT '0',
  `PetMailIdleTime` int(10) NOT NULL DEFAULT '0',
  `PetMailFromMapId` int(10) NOT NULL DEFAULT '-1',
  `PetMailFromFloor` int(10) NOT NULL DEFAULT '0',
  `PetMailFromX` int(10) NOT NULL DEFAULT '0',
  `PetMailFromY` int(10) NOT NULL DEFAULT '0',
  `PetMailEffect` int(10) NOT NULL DEFAULT '0',
  `PetSendMailCount` int(10) NOT NULL DEFAULT '0',
  `PetMailDropItemFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ResurrectedCount` int(10) NOT NULL DEFAULT '0',
  `PetRank` int(10) NOT NULL DEFAULT '0',
  `LevelupRandomPattern` int(10) NOT NULL DEFAULT '0',
  `PetId` int(10) NOT NULL DEFAULT '0',
  `DepartureBattleStatus` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `PetSkill1` int(10) NOT NULL DEFAULT '-1',
  `PetSkill2` int(10) NOT NULL DEFAULT '-1',
  `PetSkill3` int(10) NOT NULL DEFAULT '-1',
  `PetSkill4` int(10) NOT NULL DEFAULT '-1',
  `PetSkill5` int(10) NOT NULL DEFAULT '-1',
  `PetSkill6` int(10) NOT NULL DEFAULT '-1',
  `PetSkill7` int(10) NOT NULL DEFAULT '-1',
  `PetSkill8` int(10) NOT NULL DEFAULT '-1',
  `PetSkill9` int(10) NOT NULL DEFAULT '-1',
  `PetSkill10` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo6` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo7` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo8` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo9` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo10` int(10) NOT NULL DEFAULT '-1',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UserPetName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `NamedCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolPet` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `PetDataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolPet`,`PetDataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_pet_del
-- ----------------------------
CREATE TABLE `tbl_pet_del` (
  `WhichType` int(10) NOT NULL DEFAULT '0',
  `BaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `BaseBaseImageNumber` int(10) NOT NULL DEFAULT '0',
  `MapId` int(10) NOT NULL DEFAULT '0',
  `Floor` int(10) NOT NULL DEFAULT '0',
  `X` int(10) NOT NULL DEFAULT '0',
  `Y` int(10) NOT NULL DEFAULT '0',
  `Dir` int(10) NOT NULL DEFAULT '0',
  `Lv` int(10) NOT NULL DEFAULT '0',
  `Hp` int(10) NOT NULL DEFAULT '0',
  `ForcePoint` int(10) NOT NULL DEFAULT '0',
  `Vital` int(10) NOT NULL DEFAULT '0',
  `Str` int(10) NOT NULL DEFAULT '0',
  `Tough` int(10) NOT NULL DEFAULT '0',
  `Dex` int(10) NOT NULL DEFAULT '0',
  `Magic` int(10) NOT NULL DEFAULT '0',
  `Luck` int(10) NOT NULL DEFAULT '0',
  `Tribe` int(10) NOT NULL DEFAULT '0',
  `Attrib_Earth` int(10) NOT NULL DEFAULT '0',
  `Attrib_Water` int(10) NOT NULL DEFAULT '0',
  `Attrib_Fire` int(10) NOT NULL DEFAULT '0',
  `Attrib_Wind` int(10) NOT NULL DEFAULT '0',
  `Poison` int(10) NOT NULL DEFAULT '0',
  `Sleep` int(10) NOT NULL DEFAULT '0',
  `Stone` int(10) NOT NULL DEFAULT '0',
  `Drunk` int(10) NOT NULL DEFAULT '0',
  `Confusion` int(10) NOT NULL DEFAULT '0',
  `Amnesia` int(10) NOT NULL DEFAULT '0',
  `Critical` int(10) NOT NULL DEFAULT '0',
  `Counter` int(10) NOT NULL DEFAULT '0',
  `HitRate` int(10) NOT NULL DEFAULT '0',
  `Avoid` int(10) NOT NULL DEFAULT '0',
  `ItemLimit` int(10) NOT NULL DEFAULT '0',
  `HaveSkillLimit` int(10) NOT NULL DEFAULT '0',
  `DeadCount` int(10) NOT NULL DEFAULT '0',
  `DamageCount` int(10) NOT NULL DEFAULT '0',
  `KillPetCount` int(10) NOT NULL DEFAULT '0',
  `LastTimeSetLuck` int(10) NOT NULL DEFAULT '0',
  `Injury` int(10) NOT NULL DEFAULT '0',
  `WalkInterval` int(10) NOT NULL DEFAULT '0',
  `LoopInterval` int(10) NOT NULL DEFAULT '0',
  `Exp` int(10) NOT NULL DEFAULT '0',
  `LevelUpPoint` int(10) NOT NULL DEFAULT '0',
  `ImageType` int(10) NOT NULL DEFAULT '0',
  `NameColor` int(10) NOT NULL DEFAULT '0',
  `AllocPoint` int(10) NOT NULL DEFAULT '0',
  `EatTime` int(11) NOT NULL DEFAULT '0',
  `ThankFower` int(11) NOT NULL DEFAULT '0',
  `OwnerRegistNumber` int(11) NOT NULL DEFAULT '-1',
  `Size` int(10) NOT NULL DEFAULT '0',
  `ModLoyalty` int(10) NOT NULL DEFAULT '0',
  `VariableLoyalty` int(10) NOT NULL DEFAULT '0',
  `Slot` int(10) NOT NULL DEFAULT '0',
  `PetGetLv` int(10) NOT NULL DEFAULT '0',
  `Rare` int(10) NOT NULL DEFAULT '0',
  `PutPetTime` int(10) NOT NULL DEFAULT '0',
  `MailMode` int(10) NOT NULL DEFAULT '0',
  `PetMailBufIndex` int(10) NOT NULL DEFAULT '0',
  `PetMailIdleTime` int(10) NOT NULL DEFAULT '0',
  `PetMailFromMapId` int(10) NOT NULL DEFAULT '-1',
  `PetMailFromFloor` int(10) NOT NULL DEFAULT '0',
  `PetMailFromX` int(10) NOT NULL DEFAULT '0',
  `PetMailFromY` int(10) NOT NULL DEFAULT '0',
  `PetMailEffect` int(10) NOT NULL DEFAULT '0',
  `PetSendMailCount` int(10) NOT NULL DEFAULT '0',
  `PetMailDropItemFlg` tinyint(4) NOT NULL DEFAULT '0',
  `ResurrectedCount` int(10) NOT NULL DEFAULT '0',
  `PetRank` int(10) NOT NULL DEFAULT '0',
  `LevelupRandomPattern` int(10) NOT NULL DEFAULT '0',
  `PetId` int(10) NOT NULL DEFAULT '0',
  `DepartureBattleStatus` int(10) NOT NULL DEFAULT '0',
  `RoomX` int(10) NOT NULL DEFAULT '0',
  `RoomY` int(10) NOT NULL DEFAULT '0',
  `PetSkill1` int(10) NOT NULL DEFAULT '-1',
  `PetSkill2` int(10) NOT NULL DEFAULT '-1',
  `PetSkill3` int(10) NOT NULL DEFAULT '-1',
  `PetSkill4` int(10) NOT NULL DEFAULT '-1',
  `PetSkill5` int(10) NOT NULL DEFAULT '-1',
  `PetSkill6` int(10) NOT NULL DEFAULT '-1',
  `PetSkill7` int(10) NOT NULL DEFAULT '-1',
  `PetSkill8` int(10) NOT NULL DEFAULT '-1',
  `PetSkill9` int(10) NOT NULL DEFAULT '-1',
  `PetSkill10` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo1` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo2` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo3` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo4` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo5` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo6` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo7` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo8` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo9` int(10) NOT NULL DEFAULT '-1',
  `PetSkillSeqNo10` int(10) NOT NULL DEFAULT '-1',
  `Name` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `UserPetName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `NamedCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `OwnerCharName` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `IsPoolPet` tinyint(4) NOT NULL DEFAULT '0',
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `PetDataPlaceNumber` int(10) NOT NULL DEFAULT '0',
  `DataFlg1` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`IsPoolPet`,`PetDataPlaceNumber`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_playernum
-- ----------------------------
CREATE TABLE `tbl_playernum` (
  `Id` int(11) NOT NULL DEFAULT '0',
  `num` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`Id`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_product
-- ----------------------------
CREATE TABLE `tbl_product` (
  `ProductKey` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Kind` tinyint(4) NOT NULL DEFAULT '0',
  `UseFlg` tinyint(4) NOT NULL DEFAULT '0',
  `EnableFlg` tinyint(4) NOT NULL DEFAULT '0',
  `VerUpdate` int(10) NOT NULL DEFAULT '0',
  `LinkCdKey` varchar(32) COLLATE big5_bin DEFAULT NULL,
  PRIMARY KEY (`ProductKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_puk2
-- ----------------------------
CREATE TABLE `tbl_puk2` (
  `Puk2Key` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Kind` tinyint(4) NOT NULL DEFAULT '0',
  `UseFlg` tinyint(4) NOT NULL DEFAULT '0',
  `EnableFlg` tinyint(4) NOT NULL DEFAULT '0',
  `VerUpdate` int(10) NOT NULL DEFAULT '0',
  `LinkCdKey` varchar(32) COLLATE big5_bin DEFAULT NULL,
  PRIMARY KEY (`Puk2Key`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_puk3
-- ----------------------------
CREATE TABLE `tbl_puk3` (
  `Puk3Key` varchar(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `Kind` tinyint(4) NOT NULL DEFAULT '0',
  `UseFlg` tinyint(4) NOT NULL DEFAULT '0',
  `EnableFlg` tinyint(4) NOT NULL DEFAULT '0',
  `VerUpdate` int(10) NOT NULL DEFAULT '0',
  `LinkCdKey` varchar(32) COLLATE big5_bin DEFAULT NULL,
  PRIMARY KEY (`Puk3Key`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for tbl_skill
-- ----------------------------
CREATE TABLE `tbl_skill` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `SkillNum` int(10) NOT NULL DEFAULT '0',
  `SkillId` int(10) NOT NULL DEFAULT '-1',
  `SkillLevel` int(10) NOT NULL DEFAULT '0',
  `SkillExp` int(10) NOT NULL DEFAULT '-1',
  `SeqNo` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`SkillId`),
  KEY `SkillId` (`SkillId`,`CdKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_skill_bak
-- ----------------------------
CREATE TABLE `tbl_skill_bak` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `SkillNum` int(10) NOT NULL DEFAULT '0',
  `SkillId` int(10) NOT NULL DEFAULT '-1',
  `SkillLevel` int(10) NOT NULL DEFAULT '0',
  `SkillExp` int(10) NOT NULL DEFAULT '-1',
  `SeqNo` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`SkillId`),
  KEY `SkillId` (`SkillId`,`CdKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_skill_del
-- ----------------------------
CREATE TABLE `tbl_skill_del` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `RegistNumber` int(10) NOT NULL DEFAULT '-1',
  `SkillNum` int(10) NOT NULL DEFAULT '0',
  `SkillId` int(10) NOT NULL DEFAULT '-1',
  `SkillLevel` int(10) NOT NULL DEFAULT '0',
  `SkillExp` int(10) NOT NULL DEFAULT '-1',
  `SeqNo` int(10) NOT NULL DEFAULT '-1',
  PRIMARY KEY (`CdKey`,`RegistNumber`,`SkillId`),
  KEY `SkillId` (`SkillId`,`CdKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_skillpopulation
-- ----------------------------
CREATE TABLE `tbl_skillpopulation` (
  `SkillId` int(11) NOT NULL DEFAULT '-1',
  `Num` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`SkillId`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for tbl_user
-- ----------------------------
CREATE TABLE `tbl_user` (
  `CdKey` char(32) COLLATE big5_bin NOT NULL DEFAULT '',
  `SequenceNumber` int(10) NOT NULL DEFAULT '0',
  `AccountID` char(42) COLLATE big5_bin DEFAULT NULL,
  `AccountPassWord` char(16) COLLATE big5_bin DEFAULT NULL,
  `EnableFlg` tinyint(4) NOT NULL DEFAULT '0',
  `UseFlg` tinyint(4) NOT NULL DEFAULT '0',
  `BadMsg` int(10) NOT NULL DEFAULT '0',
  `TrialFlg` tinyint(4) NOT NULL DEFAULT '0',
  `DownFlg` int(10) NOT NULL DEFAULT '0',
  `ExpFlg` tinyint(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`CdKey`)
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin DELAY_KEY_WRITE=1;

-- ----------------------------
-- Table structure for web_ban
-- ----------------------------
CREATE TABLE `web_ban` (
  `MACCODE` varchar(40) COLLATE big5_bin NOT NULL,
  `IP` varchar(40) COLLATE big5_bin NOT NULL,
  `FLAG` varchar(40) COLLATE big5_bin NOT NULL,
  `REASON` varchar(40) COLLATE big5_bin NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

-- ----------------------------
-- Table structure for web_payment
-- ----------------------------
CREATE TABLE `web_payment` (
  `id` varchar(20) COLLATE big5_bin NOT NULL,
  `player_id` varchar(16) COLLATE big5_bin NOT NULL,
  `amount` int(11) NOT NULL,
  `bonus` int(11) NOT NULL,
  `picture` text COLLATE big5_bin NOT NULL,
  `add` int(11) NOT NULL,
  `ip` varchar(30) COLLATE big5_bin NOT NULL,
  `deal` varchar(11) COLLATE big5_bin NOT NULL DEFAULT '',
  `createtime` datetime NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=big5 COLLATE=big5_bin;

