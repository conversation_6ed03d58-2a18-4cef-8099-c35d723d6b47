body
{
  background-color: white;
  color: black;
  margin-left: 100px;
  margin-right: 100px;
/*   font-family: arial, sans-serif; */
}

table.banner
{
  width: 100%;
  border-bottom-color: black;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  padding-top: 16px;
  padding-right: 16px;
  padding-bottom: 5px;
}

td.banner_left
{
  font-size: x-large;
  font-weight: bold;
  font-stretch: expanded;
}

td.banner_right
{
  text-align: right;
}

table.footer
{
  width: 100%;
  border-top-color: black;
  border-top-style: solid;
  border-top-width: 1px;
  padding-bottom: 16px;
  padding-top: 5px;
}

table.main
{
  width: 60%;
  padding-top: 25px;
}

td.main_navigator
{
  vertical-align: top;
}

td.main_text
{
  vertical-align: top;
  padding-left: 20px;
}

span.bold
{
  font-weight: bold;
}

span.literal
{
  font-weight: bold;
  background-color: white;
  color: #8B0000;
}

div.indent
{
  margin-left: 40px;
}

div.column-left
{
  width: 50%;
  float: left;
}

div.column-right
{
  width: 50%;
  float: right;
}
