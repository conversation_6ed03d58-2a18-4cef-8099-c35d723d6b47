---
name: Bug report
about: Create a report to help us improve
title: ''
labels: 'bug'
assignees: ''
---

**Describe the bug**

Include a clear and concise description of what the problem is, including what
you expected to happen, and what actually happened.

**Steps to reproduce the bug**

It's important that we are able to reproduce the problem that you are
experiencing. Please provide all code and relevant steps to reproduce the
problem, including your `BUILD`/`CMakeLists.txt` file and build commands. Links
to a GitHub branch or [godbolt.org](https://godbolt.org/) that demonstrate the
problem are also helpful.

**Does the bug persist in the most recent commit?**

We recommend using the latest commit in the master branch in your projects.

**What operating system and version are you using?**

If you are using a Linux distribution please include the name and version of the
distribution as well.

**What compiler and version are you using?**

Please include the output of `gcc -v` or `clang -v`, or the equivalent for your
compiler.

**What build system are you using?**

Please include the output of `bazel --version` or `cmake --version`, or the
equivalent for your build system.

**Additional context**

Add any other context about the problem here.
