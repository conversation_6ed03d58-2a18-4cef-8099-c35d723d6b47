/* Copyright (C) 2001, 2008 Free Software Foundation, Inc.
   This file is part of the GNU LIBICONV Library.

   The GNU LIBICONV Library is free software; you can redistribute it
   and/or modify it under the terms of the GNU Lesser General Public
   License as published by the Free Software Foundation; either version 2.1
   of the License, or (at your option) any later version.

   The GNU LIBICONV Library is distributed in the hope that it will be
   useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   Lesser General Public License for more details.

   You should have received a copy of the GNU Lesser General Public
   License along with the GNU LIBICONV Library; see the file COPYING.LIB.
   If not, see <https://www.gnu.org/licenses/>.  */

/* Encodings used by system dependent locales on OSF/1 a.k.a. Tru64. */

DEFENCODING(( "DEC-KANJI",
            ),
            dec_kanji,
            { dec_kanji_mbtowc, NULL },   { dec_kanji_wctomb, NULL })
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "DECKANJI",               /* OSF/1 */
            dec_kanji)
#endif

DEFENCODING(( "DEC-HANYU",
            ),
            dec_hanyu,
            { dec_hanyu_mbtowc, NULL },   { dec_hanyu_wctomb, NULL })
#ifdef USE_OSF1_ALIASES
DEFALIAS(     "DECHANYU",               /* OSF/1 */
            dec_hanyu)
#endif
