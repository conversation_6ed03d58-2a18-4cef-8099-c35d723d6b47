/*
 * Copyright (C) 1999-2004, 2012 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * JISX0213:2004
 */

#ifndef _JISX0213_H
#define _JISX0213_H

/* JISX0213 plane 1 (= ISO-IR-233) characters are in the range
   0x{21..7E}{21..7E}.
   JISX0213 plane 2 (= ISO-IR-229) characters are in the range
   0x{21,23..25,28,2C..2F,6E..7E}{21..7E}.
   Together this makes 120 rows of 94 characters.
*/

static const unsigned short jisx0213_to_ucs_combining[][2] = {
  { 0x304b, 0x309a },
  { 0x304d, 0x309a },
  { 0x304f, 0x309a },
  { 0x3051, 0x309a },
  { 0x3053, 0x309a },
  { 0x30ab, 0x309a },
  { 0x30ad, 0x309a },
  { 0x30af, 0x309a },
  { 0x30b1, 0x309a },
  { 0x30b3, 0x309a },
  { 0x30bb, 0x309a },
  { 0x30c4, 0x309a },
  { 0x30c8, 0x309a },
  { 0x31f7, 0x309a },
  { 0x00e6, 0x0300 },
  { 0x0254, 0x0300 },
  { 0x0254, 0x0301 },
  { 0x028c, 0x0300 },
  { 0x028c, 0x0301 },
  { 0x0259, 0x0300 },
  { 0x0259, 0x0301 },
  { 0x025a, 0x0300 },
  { 0x025a, 0x0301 },
  { 0x02e9, 0x02e5 },
  { 0x02e5, 0x02e9 },
};

static const unsigned short jisx0213_to_ucs_main[120 * 94] = {
  /* 0x12121..0x1217E */
  0x1000, 0x1001, 0x1002, 0x830c, 0x830e, 0x10fb, 0x831a, 0x831b,
  0x831f, 0x8301, 0x109b, 0x109c, 0x00b4, 0x8340, 0x00a8, 0x833e,
  0x83e3, 0x833f, 0x10fd, 0x10fe, 0x109d, 0x109e, 0x1003, 0x2edd,
  0x1005, 0x1006, 0x1007, 0x10fc, 0x0714, 0x0710, 0x830f, 0x833c,
  0x101c, 0x0716, 0x835c, 0x0726, 0x0725, 0x0718, 0x0719, 0x071c,
  0x071d, 0x8308, 0x8309, 0x1014, 0x1015, 0x833b, 0x833d, 0x835b,
  0x835d, 0x1008, 0x1009, 0x100a, 0x100b, 0x100c, 0x100d, 0x100e,
  0x100f, 0x1010, 0x1011, 0x830b, 0x0912, 0x00b1, 0x00d7, 0x00f7,
  0x831d, 0x0960, 0x831c, 0x831e, 0x0966, 0x0967, 0x091e, 0x0934,
  0x0d42, 0x0d40, 0x00b0, 0x0732, 0x0733, 0x0803, 0x83e5, 0x8304,
  0x00a2, 0x00a3, 0x8305, 0x8303, 0x8306, 0x830a, 0x8320, 0x00a7,
  0x0d06, 0x0d05, 0x0ccb, 0x0ccf, 0x0cce, 0x0cc7,
  /* 0x12221..0x1227E */
  0x0cc6, 0x0ca1, 0x0ca0, 0x0cb3, 0x0cb2, 0x0cbd, 0x0cbc, 0x073b,
  0x1012, 0x0892, 0x0890, 0x0891, 0x0893, 0x1013, 0x8307, 0x8302,
  0x830d, 0x835e, 0x1033, 0x1034, 0x1035, 0x103b, 0x103c, 0x10ff,
  0x109f, 0x0908, 0x090b, 0x0986, 0x0987, 0x0982, 0x0983, 0x092a,
  0x0929, 0x0984, 0x0985, 0x098a, 0x098b, 0x0909, 0x0905, 0x0a05,
  0x0a06, 0x0927, 0x0928, 0x00ac, 0x08d2, 0x08d4, 0x0900, 0x0903,
  0x0995, 0x0996, 0x0997, 0x0925, 0x0926, 0x835f, 0x8360, 0x1018,
  0x1019, 0x1016, 0x1017, 0x0920, 0x09a5, 0x0a12, 0x0902, 0x0907,
  0x0961, 0x0952, 0x096a, 0x096b, 0x091a, 0x093d, 0x091d, 0x0935,
  0x092b, 0x092c, 0x0962, 0x0943, 0x0945, 0x0948, 0x0976, 0x0977,
  0x0894, 0x082b, 0x0730, 0x0d6f, 0x0d6d, 0x0d6a, 0x0720, 0x0721,
  0x00b6, 0x0d6e, 0x0d6b, 0x0d6c, 0x0d69, 0x0cef,
  /* 0x12321..0x1237E */
  0x0cb7, 0x0cb6, 0x0cc1, 0x0cc0, 0x0897, 0x0898, 0x0896, 0x0899,
  0x08c4, 0x08e8, 0x08e6, 0x08e7, 0x08e9, 0x0f34, 0x0f35, 0x8310,
  0x8311, 0x8312, 0x8313, 0x8314, 0x8315, 0x8316, 0x8317, 0x8318,
  0x8319, 0x0fbf, 0x0cc9, 0x103d, 0x8246, 0x8245, 0x0ce6, 0x0722,
  0x8321, 0x8322, 0x8323, 0x8324, 0x8325, 0x8326, 0x8327, 0x8328,
  0x8329, 0x832a, 0x832b, 0x832c, 0x832d, 0x832e, 0x832f, 0x8330,
  0x8331, 0x8332, 0x8333, 0x8334, 0x8335, 0x8336, 0x8337, 0x8338,
  0x8339, 0x833a, 0x0913, 0x0835, 0x080f, 0x13cb, 0x0813, 0x0827,
  0x8341, 0x8342, 0x8343, 0x8344, 0x8345, 0x8346, 0x8347, 0x8348,
  0x8349, 0x834a, 0x834b, 0x834c, 0x834d, 0x834e, 0x834f, 0x8350,
  0x8351, 0x8352, 0x8353, 0x8354, 0x8355, 0x8356, 0x8357, 0x8358,
  0x8359, 0x835a, 0x10a0, 0x0713, 0x0ffa, 0x0ffb,
  /* 0x12421..0x1247E */
  0x1041, 0x1042, 0x1043, 0x1044, 0x1045, 0x1046, 0x1047, 0x1048,
  0x1049, 0x104a, 0x104b, 0x104c, 0x104d, 0x104e, 0x104f, 0x1050,
  0x1051, 0x1052, 0x1053, 0x1054, 0x1055, 0x1056, 0x1057, 0x1058,
  0x1059, 0x105a, 0x105b, 0x105c, 0x105d, 0x105e, 0x105f, 0x1060,
  0x1061, 0x1062, 0x1063, 0x1064, 0x1065, 0x1066, 0x1067, 0x1068,
  0x1069, 0x106a, 0x106b, 0x106c, 0x106d, 0x106e, 0x106f, 0x1070,
  0x1071, 0x1072, 0x1073, 0x1074, 0x1075, 0x1076, 0x1077, 0x1078,
  0x1079, 0x107a, 0x107b, 0x107c, 0x107d, 0x107e, 0x107f, 0x1080,
  0x1081, 0x1082, 0x1083, 0x1084, 0x1085, 0x1086, 0x1087, 0x1088,
  0x1089, 0x108a, 0x108b, 0x108c, 0x108d, 0x108e, 0x108f, 0x1090,
  0x1091, 0x1092, 0x1093, 0x1094, 0x1095, 0x1096, 0x0001, 0x0002,
  0x0003, 0x0004, 0x0005, 0x0000, 0x0000, 0x0000,
  /* 0x12521..0x1257E */
  0x10a1, 0x10a2, 0x10a3, 0x10a4, 0x10a5, 0x10a6, 0x10a7, 0x10a8,
  0x10a9, 0x10aa, 0x10ab, 0x10ac, 0x10ad, 0x10ae, 0x10af, 0x10b0,
  0x10b1, 0x10b2, 0x10b3, 0x10b4, 0x10b5, 0x10b6, 0x10b7, 0x10b8,
  0x10b9, 0x10ba, 0x10bb, 0x10bc, 0x10bd, 0x10be, 0x10bf, 0x10c0,
  0x10c1, 0x10c2, 0x10c3, 0x10c4, 0x10c5, 0x10c6, 0x10c7, 0x10c8,
  0x10c9, 0x10ca, 0x10cb, 0x10cc, 0x10cd, 0x10ce, 0x10cf, 0x10d0,
  0x10d1, 0x10d2, 0x10d3, 0x10d4, 0x10d5, 0x10d6, 0x10d7, 0x10d8,
  0x10d9, 0x10da, 0x10db, 0x10dc, 0x10dd, 0x10de, 0x10df, 0x10e0,
  0x10e1, 0x10e2, 0x10e3, 0x10e4, 0x10e5, 0x10e6, 0x10e7, 0x10e8,
  0x10e9, 0x10ea, 0x10eb, 0x10ec, 0x10ed, 0x10ee, 0x10ef, 0x10f0,
  0x10f1, 0x10f2, 0x10f3, 0x10f4, 0x10f5, 0x10f6, 0x0006, 0x0007,
  0x0008, 0x0009, 0x000a, 0x000b, 0x000c, 0x000d,
  /* 0x12621..0x1267E */
  0x0391, 0x0392, 0x0393, 0x0394, 0x0395, 0x0396, 0x0397, 0x0398,
  0x0399, 0x039a, 0x039b, 0x039c, 0x039d, 0x039e, 0x039f, 0x03a0,
  0x03a1, 0x03a3, 0x03a4, 0x03a5, 0x03a6, 0x03a7, 0x03a8, 0x03a9,
  0x0d64, 0x0d60, 0x0d62, 0x0d66, 0x0d61, 0x0d65, 0x0d67, 0x0d63,
  0x03b1, 0x03b2, 0x03b3, 0x03b4, 0x03b5, 0x03b6, 0x03b7, 0x03b8,
  0x03b9, 0x03ba, 0x03bb, 0x03bc, 0x03bd, 0x03be, 0x03bf, 0x03c0,
  0x03c1, 0x03c3, 0x03c4, 0x03c5, 0x03c6, 0x03c7, 0x03c8, 0x03c9,
  0x03c2, 0x0bf5, 0x0bf6, 0x0bf7, 0x0bf8, 0x0bf9, 0x0bfa, 0x0bfb,
  0x0bfc, 0x0bfd, 0x0bfe, 0x0d16, 0x0d17, 0x1020, 0x0d0e, 0x0d00,
  0x0d01, 0x0d02, 0x0d03, 0x0d68, 0x0cb1, 0x11f0, 0x11f1, 0x11f2,
  0x11f3, 0x11f4, 0x11f5, 0x11f6, 0x11f7, 0x11f8, 0x11f9, 0x000e,
  0x11fa, 0x11fb, 0x11fc, 0x11fd, 0x11fe, 0x11ff,
  /* 0x12721..0x1277E */
  0x0410, 0x0411, 0x0412, 0x0413, 0x0414, 0x0415, 0x0401, 0x0416,
  0x0417, 0x0418, 0x0419, 0x041a, 0x041b, 0x041c, 0x041d, 0x041e,
  0x041f, 0x0420, 0x0421, 0x0422, 0x0423, 0x0424, 0x0425, 0x0426,
  0x0427, 0x0428, 0x0429, 0x042a, 0x042b, 0x042c, 0x042d, 0x042e,
  0x042f, 0x0abe, 0x0abf, 0x0ac0, 0x0ac1, 0x0ac2, 0x0ac3, 0x0ac4,
  0x0ac5, 0x0ac6, 0x0ac7, 0x0ac8, 0x0ac9, 0x0aca, 0x0acb, 0x0acc,
  0x0430, 0x0431, 0x0432, 0x0433, 0x0434, 0x0435, 0x0451, 0x0436,
  0x0437, 0x0438, 0x0439, 0x043a, 0x043b, 0x043c, 0x043d, 0x043e,
  0x043f, 0x0440, 0x0441, 0x0442, 0x0443, 0x0444, 0x0445, 0x0446,
  0x0447, 0x0448, 0x0449, 0x044a, 0x044b, 0x044c, 0x044d, 0x044e,
  0x044f, 0x10f7, 0x10f8, 0x10f9, 0x10fa, 0x09da, 0x09db, 0x0853,
  0x0854, 0x0855, 0x0e13, 0x0a18, 0x0b23, 0x0ace,
  /* 0x12821..0x1287E */
  0x0c00, 0x0c02, 0x0c0c, 0x0c10, 0x0c18, 0x0c14, 0x0c1c, 0x0c2c,
  0x0c24, 0x0c34, 0x0c3c, 0x0c01, 0x0c03, 0x0c0f, 0x0c13, 0x0c1b,
  0x0c17, 0x0c23, 0x0c33, 0x0c2b, 0x0c3b, 0x0c4b, 0x0c20, 0x0c2f,
  0x0c28, 0x0c37, 0x0c3f, 0x0c1d, 0x0c30, 0x0c25, 0x0c38, 0x0c42,
  0x1251, 0x1252, 0x1253, 0x1254, 0x1255, 0x1256, 0x1257, 0x1258,
  0x1259, 0x125a, 0x125b, 0x125c, 0x125d, 0x125e, 0x125f, 0x12b1,
  0x12b2, 0x12b3, 0x12b4, 0x12b5, 0x12b6, 0x12b7, 0x12b8, 0x12b9,
  0x12ba, 0x12bb, 0x12bc, 0x12bd, 0x12be, 0x12bf, 0x0000, 0x0000,
  0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0cd0, 0x0cd1,
  0x0cd2, 0x0cd3, 0x073c, 0x0747, 0x0748, 0x0749, 0x01cd, 0x01ce,
  0x01d0, 0x053e, 0x053f, 0x01f8, 0x01f9, 0x01d1, 0x01d2, 0x01d4,
  0x01d6, 0x01d8, 0x01da, 0x01dc, 0x0000, 0x0000,
  /* 0x12921..0x1297E */
  0x07ac, 0x00a0, 0x00a1, 0x00a4, 0x00a6, 0x00a9, 0x00aa, 0x00ab,
  0x00ad, 0x00ae, 0x00af, 0x00b2, 0x00b3, 0x00b7, 0x00b8, 0x00b9,
  0x00ba, 0x00bb, 0x00bc, 0x00bd, 0x00be, 0x00bf, 0x00c0, 0x00c1,
  0x00c2, 0x00c3, 0x00c4, 0x00c5, 0x00c6, 0x00c7, 0x00c8, 0x00c9,
  0x00ca, 0x00cb, 0x00cc, 0x00cd, 0x00ce, 0x00cf, 0x00d0, 0x00d1,
  0x00d2, 0x00d3, 0x00d4, 0x00d5, 0x00d6, 0x00d8, 0x00d9, 0x00da,
  0x00db, 0x00dc, 0x00dd, 0x00de, 0x00df, 0x00e0, 0x00e1, 0x00e2,
  0x00e3, 0x00e4, 0x00e5, 0x00e6, 0x00e7, 0x00e8, 0x00e9, 0x00ea,
  0x00eb, 0x00ec, 0x00ed, 0x00ee, 0x00ef, 0x00f0, 0x00f1, 0x00f2,
  0x00f3, 0x00f4, 0x00f5, 0x00f6, 0x00f8, 0x00f9, 0x00fa, 0x00fb,
  0x00fc, 0x00fd, 0x00fe, 0x00ff, 0x0100, 0x012a, 0x016a, 0x0112,
  0x014c, 0x0101, 0x012b, 0x016b, 0x0113, 0x014d,
  /* 0x12A21..0x12A7E */
  0x0104, 0x02d8, 0x0141, 0x013d, 0x015a, 0x0160, 0x015e, 0x0164,
  0x0179, 0x017d, 0x017b, 0x0105, 0x02db, 0x0142, 0x013e, 0x015b,
  0x02c7, 0x0161, 0x015f, 0x0165, 0x017a, 0x02dd, 0x017e, 0x017c,
  0x0154, 0x0102, 0x0139, 0x0106, 0x010c, 0x0118, 0x011a, 0x010e,
  0x0143, 0x0147, 0x0150, 0x0158, 0x016e, 0x0170, 0x0162, 0x0155,
  0x0103, 0x013a, 0x0107, 0x010d, 0x0119, 0x011b, 0x010f, 0x0111,
  0x0144, 0x0148, 0x0151, 0x0159, 0x016f, 0x0171, 0x0163, 0x02d9,
  0x0108, 0x011c, 0x0124, 0x0134, 0x015c, 0x016c, 0x0109, 0x011d,
  0x0125, 0x0135, 0x015d, 0x016d, 0x0271, 0x028b, 0x027e, 0x0283,
  0x0292, 0x026c, 0x026e, 0x0279, 0x0288, 0x0256, 0x0273, 0x027d,
  0x0282, 0x0290, 0x027b, 0x026d, 0x025f, 0x0272, 0x029d, 0x028e,
  0x0261, 0x014b, 0x0270, 0x0281, 0x0127, 0x0295,
  /* 0x12B21..0x12B7E */
  0x0294, 0x0266, 0x0298, 0x01c2, 0x0253, 0x0257, 0x0284, 0x0260,
  0x0193, 0x0153, 0x0152, 0x0268, 0x0289, 0x0258, 0x0275, 0x0259,
  0x025c, 0x025e, 0x0250, 0x026f, 0x028a, 0x0264, 0x028c, 0x0254,
  0x0251, 0x0252, 0x028d, 0x0265, 0x02a2, 0x02a1, 0x0255, 0x0291,
  0x027a, 0x0267, 0x025a, 0x000f, 0x01fd, 0x0670, 0x0671, 0x0010,
  0x0011, 0x0012, 0x0013, 0x0014, 0x0015, 0x0016, 0x0017, 0x0672,
  0x0673, 0x0361, 0x02c8, 0x02cc, 0x02d0, 0x02d1, 0x0306, 0x073f,
  0x030b, 0x0301, 0x0304, 0x0300, 0x030f, 0x030c, 0x0302, 0x02e5,
  0x02e6, 0x02e7, 0x02e8, 0x02e9, 0x0018, 0x0019, 0x0325, 0x032c,
  0x0339, 0x031c, 0x031f, 0x0320, 0x0308, 0x033d, 0x0329, 0x032f,
  0x02de, 0x0324, 0x0330, 0x033c, 0x0334, 0x031d, 0x031e, 0x0318,
  0x0319, 0x032a, 0x033a, 0x033b, 0x0303, 0x031a,
  /* 0x12C21..0x12C7E */
  0x0e76, 0x0e77, 0x0e78, 0x0e79, 0x0e7a, 0x0e7b, 0x0e7c, 0x0e7d,
  0x0e7e, 0x0e7f, 0x0beb, 0x0bec, 0x0bed, 0x0bee, 0x0bef, 0x0bf0,
  0x0bf1, 0x0bf2, 0x0bf3, 0x0bf4, 0x0870, 0x0871, 0x0872, 0x0873,
  0x0874, 0x0875, 0x0876, 0x0877, 0x0878, 0x0879, 0x087a, 0x087b,
  0x0bd0, 0x0bd1, 0x0bd2, 0x0bd3, 0x0bd4, 0x0bd5, 0x0bd6, 0x0bd7,
  0x0bd8, 0x0bd9, 0x0bda, 0x0bdb, 0x0bdc, 0x0bdd, 0x0bde, 0x0bdf,
  0x0be0, 0x0be1, 0x0be2, 0x0be3, 0x0be4, 0x0be5, 0x0be6, 0x0be7,
  0x0be8, 0x0be9, 0x12d0, 0x12d1, 0x12d2, 0x12d3, 0x12d4, 0x12d5,
  0x12d6, 0x12d7, 0x12d8, 0x12d9, 0x12da, 0x12db, 0x12dc, 0x12dd,
  0x12de, 0x12df, 0x12e0, 0x12e1, 0x12e2, 0x12e3, 0x12fa, 0x12e9,
  0x12e5, 0x12ed, 0x12ec, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
  0x0000, 0x0000, 0x0000, 0x0000, 0x0751, 0x0742,
  /* 0x12D21..0x12D7E */
  0x0b60, 0x0b61, 0x0b62, 0x0b63, 0x0b64, 0x0b65, 0x0b66, 0x0b67,
  0x0b68, 0x0b69, 0x0b6a, 0x0b6b, 0x0b6c, 0x0b6d, 0x0b6e, 0x0b6f,
  0x0b70, 0x0b71, 0x0b72, 0x0b73, 0x0860, 0x0861, 0x0862, 0x0863,
  0x0864, 0x0865, 0x0866, 0x0867, 0x0868, 0x0869, 0x086a, 0x1349,
  0x1314, 0x1322, 0x134d, 0x1318, 0x1327, 0x1303, 0x1336, 0x1351,
  0x1357, 0x130d, 0x1326, 0x1323, 0x132b, 0x134a, 0x133b, 0x139c,
  0x139d, 0x139e, 0x138e, 0x138f, 0x13c4, 0x13a1, 0x086b, 0x0000,
  0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x137b, 0x101d,
  0x101f, 0x0816, 0x13cd, 0x0821, 0x12a4, 0x12a5, 0x12a6, 0x12a7,
  0x12a8, 0x1231, 0x1232, 0x1239, 0x137e, 0x137d, 0x137c, 0x0000,
  0x0000, 0x0000, 0x092e, 0x0000, 0x0000, 0x0000, 0x0000, 0x091f,
  0x09bf, 0x0000, 0x0000, 0x0000, 0x0e56, 0x0d1e,
  /* 0x12E21..0x12E7E */
  0x2ff1, 0x840b, 0x1402, 0x2e28, 0x2e2f, 0x2e30, 0x2e8d, 0x2ee1,
  0x2efd, 0x2eff, 0x2f03, 0x2f0b, 0x2f60, 0x2f48, 0x2f49, 0x2f56,
  0x2f5f, 0x2f6a, 0x2f6c, 0x2f7e, 0x2f8a, 0x2f94, 0x2f97, 0x8130,
  0x2fc9, 0x2fe0, 0x3001, 0x3002, 0x300e, 0x3018, 0x3027, 0x302e,
  0x3040, 0x303b, 0x3041, 0x3094, 0x30cc, 0x30f2, 0x30d0, 0x30e6,
  0x8131, 0x3106, 0x3103, 0x310b, 0x311e, 0x3135, 0x314a, 0x8132,
  0x3155, 0x3157, 0x14b5, 0x319d, 0x31c3, 0x31ca, 0x31de, 0x31e2,
  0x31ee, 0x3201, 0x14db, 0x3213, 0x3215, 0x3249, 0x3257, 0x3261,
  0x3293, 0x32c8, 0x8133, 0x32cc, 0x32d0, 0x32d6, 0x32db, 0x8134,
  0x32f0, 0x32fb, 0x3300, 0x3307, 0x331c, 0x8135, 0x3361, 0x3363,
  0x337d, 0x3393, 0x339d, 0x33b2, 0x3412, 0x3427, 0x344d, 0x349c,
  0x346b, 0x3474, 0x347f, 0x3488, 0x3496, 0x34a1,
  /* 0x12F21..0x12F7E */
  0x34a9, 0x34c6, 0x34ff, 0x350e, 0x352b, 0x3535, 0x3550, 0x355e,
  0x3581, 0x3586, 0x358e, 0x8136, 0x35ad, 0x35ce, 0x8137, 0x3608,
  0x360e, 0x363b, 0x3649, 0x3676, 0x3666, 0x8138, 0x366f, 0x3671,
  0x3672, 0x3699, 0x369e, 0x36a9, 0x36ac, 0x36b3, 0x36c9, 0x36ca,
  0x370a, 0x923d, 0x3721, 0x372f, 0x3733, 0x3734, 0x3770, 0x3777,
  0x377c, 0x379c, 0x810f, 0x931b, 0x37b8, 0x37c7, 0x37c8, 0x37cf,
  0x37e4, 0x37ed, 0x37f5, 0x37f6, 0x37ff, 0x3809, 0x8110, 0x3861,
  0x3864, 0x8139, 0x387c, 0x3889, 0x389e, 0x813a, 0x38a9, 0x946e,
  0x38d2, 0x38ce, 0x38d4, 0x38da, 0x38e0, 0x38e9, 0x390c, 0x6641,
  0x395d, 0x396d, 0x398b, 0x3992, 0x39a4, 0x39c3, 0x39d2, 0x39dd,
  0x3a13, 0x3a23, 0x3a67, 0x3a6d, 0x3a77, 0x3a7e, 0x3a84, 0x3a9e,
  0x3aa7, 0x3ac4, 0x98bd, 0x3b19, 0x3b25, 0x325d,
  /* 0x13021..0x1307E */
  0x2e9c, 0x3516, 0x3a03, 0x763f, 0x34c0, 0x411b, 0x4328, 0x39f6,
  0x7022, 0x6475, 0x631c, 0x5a50, 0x40aa, 0x43e1, 0x4e25, 0x45ed,
  0x6466, 0x62a6, 0x7bf5, 0x4893, 0x3727, 0x45a1, 0x4271, 0x3b9b,
  0x39d0, 0x667b, 0x78f4, 0x5d62, 0x5dbe, 0x7b8e, 0x4216, 0x5c9f,
  0x68b7, 0x3b89, 0x3eb5, 0x4309, 0x4697, 0x4848, 0x75c7, 0x778d,
  0x474f, 0x2ee5, 0x2f0a, 0x2f4d, 0x2f9d, 0x3049, 0x36f2, 0x3937,
  0x39d4, 0x3a01, 0x3c09, 0x40df, 0x410f, 0x4170, 0x4613, 0x4905,
  0x50ba, 0x554f, 0x5570, 0x59fb, 0x5dad, 0x5def, 0x60c3, 0x640e,
  0x6863, 0x6b02, 0x7055, 0x707a, 0x333b, 0x2e95, 0x2ea5, 0x37df,
  0x60b2, 0x70c1, 0x58ef, 0x2e00, 0x38f1, 0x4ea2, 0x7038, 0x5a32,
  0x6328, 0x628b, 0x7c2f, 0x3141, 0x3370, 0x34bd, 0x34e1, 0x36e0,
  0x39fb, 0x3f15, 0x78f2, 0x4deb, 0x60e4, 0x652d,
  /* 0x13121..0x1317E */
  0x7662, 0x7670, 0x76a0, 0x77fb, 0x340b, 0x33f3, 0x3b87, 0x50cf,
  0x5fbd, 0x6fc2, 0x76e8, 0x336f, 0x7d5c, 0x5aba, 0x2e11, 0x5893,
  0x61fc, 0x4e26, 0x3618, 0x3504, 0x4b1d, 0x651a, 0x7c3b, 0x39e5,
  0x33a9, 0x4d66, 0x54dc, 0x758f, 0x3642, 0x2e91, 0x704b, 0x76f2,
  0x634f, 0x790c, 0x33e1, 0x35b6, 0x3b30, 0x3f71, 0x4620, 0x46f3,
  0x4804, 0x4c38, 0x4cf3, 0x4d29, 0x545b, 0x56c8, 0x5a4e, 0x7834,
  0x62f1, 0x685b, 0x6a60, 0x72ed, 0x4db2, 0x55ab, 0x56ca, 0x79c5,
  0x40a6, 0x6b01, 0x6d8a, 0x75b2, 0x498e, 0x33ad, 0x3186, 0x3712,
  0x3830, 0x3944, 0x3bb4, 0x3ef6, 0x4028, 0x43a9, 0x43f4, 0x4cbf,
  0x4f14, 0x508e, 0x5114, 0x5159, 0x51d5, 0x533f, 0x5e01, 0x6276,
  0x62d1, 0x6597, 0x7060, 0x725b, 0x7d1b, 0x3869, 0x45bc, 0x4c5a,
  0x5525, 0x31f9, 0x392e, 0x3965, 0x3f80, 0x3fdc,
  /* 0x13221..0x1327E */
  0x42bc, 0x45fa, 0x4a2a, 0x4b27, 0x4bb4, 0x538b, 0x5fc1, 0x6956,
  0x7d2c, 0x7d0e, 0x7ec4, 0x3ca1, 0x4c96, 0x637b, 0x3104, 0x3c4b,
  0x41b6, 0x61c6, 0x4876, 0x5261, 0x2e59, 0x2ffa, 0x3378, 0x4069,
  0x4e29, 0x5a4f, 0x77f3, 0x2e0b, 0x3316, 0x2eee, 0x2f55, 0x2f3d,
  0x2fa1, 0x2f73, 0x32a0, 0x33ef, 0x3609, 0x390f, 0x3ac1, 0x3bb6,
  0x3be1, 0x59d1, 0x4687, 0x479c, 0x47b6, 0x4b4c, 0x4cb3, 0x506b,
  0x53c2, 0x598d, 0x59be, 0x5a3c, 0x5b87, 0x62b1, 0x62db, 0x6304,
  0x6377, 0x63ef, 0x63d3, 0x6766, 0x6ab2, 0x3629, 0x6ca8, 0x6fe6,
  0x704e, 0x771e, 0x668a, 0x2fc4, 0x3ce8, 0x4211, 0x5259, 0x553b,
  0x61e5, 0x62bd, 0x66fe, 0x6cc0, 0x76c5, 0x7913, 0x79d5, 0x2ecb,
  0x2f1a, 0x69e3, 0x36de, 0x384a, 0x38ca, 0x3efb, 0x3feb, 0x402a,
  0x4094, 0x4062, 0x41d0, 0x4212, 0x42d0, 0x4539,
  /* 0x13321..0x1337E */
  0x7b41, 0x4666, 0x48b0, 0x4d77, 0x5070, 0x554c, 0x5686, 0x5d75,
  0x62a5, 0x67f9, 0x758b, 0x768e, 0x6c9d, 0x31f1, 0x32be, 0x3916,
  0x34b3, 0x3bb3, 0x3d16, 0x4168, 0x4982, 0x4daf, 0x588d, 0x64cb,
  0x6857, 0x6a72, 0x73a7, 0x7ab8, 0x4d6c, 0x79a8, 0x66d9, 0x37a3,
  0x47ff, 0x66ce, 0x720e, 0x3283, 0x3687, 0x3404, 0x3ed3, 0x42e1,
  0x44b9, 0x483c, 0x4838, 0x4bbb, 0x5372, 0x58ba, 0x5a6b, 0x699a,
  0x69d2, 0x6d6b, 0x6f03, 0x70ed, 0x75a3, 0x7694, 0x7769, 0x3b66,
  0x3cb3, 0x497d, 0x784d, 0x784e, 0x439b, 0x5b20, 0x4a2b, 0x4a7f,
  0x48b6, 0x7c0d, 0x4f5f, 0x3272, 0x359d, 0x4070, 0x42ec, 0x4d3b,
  0x4e07, 0x4ed1, 0x645b, 0x6910, 0x6f44, 0x2e14, 0x7c39, 0x33f6,
  0x491b, 0x4a3a, 0x7784, 0x482a, 0x315c, 0x5ac3, 0x64b2, 0x71dc,
  0x738c, 0x365b, 0x7d28, 0x4822, 0x6305, 0x6431,
  /* 0x13421..0x1347E */
  0x5ca5, 0x3208, 0x62c5, 0x54e6, 0x2e7e, 0x2f83, 0x31a0, 0x3bd2,
  0x320a, 0x32d8, 0x32e7, 0x3dfb, 0x359a, 0x382a, 0x39e6, 0x3b8c,
  0x3b98, 0x3bdb, 0x3e72, 0x3e79, 0x40a3, 0x411f, 0x4163, 0x41be,
  0x43db, 0x4562, 0x47d1, 0x4853, 0x48fa, 0x4b3e, 0x4b53, 0x4c57,
  0x4f22, 0x4f97, 0x4f45, 0x54b0, 0x5518, 0x56e3, 0x570b, 0x5aff,
  0x5ba1, 0x5c21, 0x5de9, 0x5f36, 0x5ff0, 0x609d, 0x6266, 0x639e,
  0x69b3, 0x6acc, 0x6cab, 0x7084, 0x7451, 0x7593, 0x7591, 0x75a2,
  0x7665, 0x77d3, 0x7928, 0x6218, 0x2e38, 0x342b, 0x3cb8, 0x3dcc,
  0x53a9, 0x564c, 0x573c, 0x3ca9, 0x5feb, 0x6d0b, 0x76c1, 0x7811,
  0x7854, 0x7858, 0x2f01, 0x2f0e, 0x3371, 0x359c, 0x3668, 0x37fa,
  0x3947, 0x3b09, 0x3bc4, 0x3c90, 0x3e0c, 0x3e7e, 0x3fcc, 0x43ee,
  0x473a, 0x45d7, 0x45e2, 0x471f, 0x48cb, 0x48c4,
  /* 0x13521..0x1357E */
  0x4a5f, 0x3e30, 0x4bc5, 0x4c17, 0x4c7d, 0x557f, 0x5948, 0x3b63,
  0x5a00, 0x5d00, 0x3fbd, 0x698f, 0x6a18, 0x6cb4, 0x6d77, 0x6ecc,
  0x6f1d, 0x78e2, 0x7a0e, 0x7b3c, 0x2e80, 0x307d, 0x3100, 0x3993,
  0x3b9c, 0x422f, 0x4280, 0x44ec, 0x4b3a, 0x52a0, 0x5591, 0x5947,
  0x5fa9, 0x67fb, 0x6abc, 0x6b70, 0x43ac, 0x63ca, 0x77a0, 0x3409,
  0x3403, 0x35ab, 0x4854, 0x4a58, 0x6a70, 0x5827, 0x4775, 0x7ecd,
  0x3374, 0x3ba2, 0x611a, 0x6650, 0x7006, 0x2e18, 0x2e45, 0x2ec7,
  0x2f11, 0x33ca, 0x3438, 0x3bae, 0x3f13, 0x4025, 0x4551, 0x473d,
  0x4c42, 0x4c72, 0x4ce3, 0x5078, 0x5403, 0x5a76, 0x5aae, 0x5b08,
  0x5d1a, 0x5cfe, 0x5d66, 0x45e7, 0x525b, 0x33bb, 0x3c45, 0x3de8,
  0x42d2, 0x42e0, 0x4319, 0x4e20, 0x665a, 0x6a31, 0x6ddd, 0x72f8,
  0x4f01, 0x59a6, 0x7b5a, 0x2ea8, 0x2eab, 0x2eac,
  /* 0x13621..0x1367E */
  0x2f9b, 0x2fa0, 0x30d1, 0x3147, 0x5af6, 0x3171, 0x31f6, 0x3354,
  0x3321, 0x337f, 0x33eb, 0x35ac, 0x3883, 0x3ce1, 0x3f37, 0x3f4a,
  0x402f, 0x4050, 0x406d, 0x431f, 0x4559, 0x4a4b, 0x4cc1, 0x52c2,
  0x52ed, 0x57ef, 0x60f8, 0x6105, 0x6208, 0x654e, 0x70f7, 0x73e1,
  0x77ff, 0x7957, 0x7a5a, 0x2ef0, 0x31dd, 0x3c2d, 0x4681, 0x496d,
  0x3c40, 0x46f2, 0x4975, 0x5389, 0x4850, 0x5c81, 0x30c5, 0x32e4,
  0x3747, 0x3dfe, 0x7326, 0x45a4, 0x4b23, 0x4b3d, 0x5434, 0x5981,
  0x59bd, 0x5b4b, 0x5dca, 0x62b9, 0x63cc, 0x687f, 0x695f, 0x6b39,
  0x6fd1, 0x71d1, 0x341f, 0x7280, 0x2e5d, 0x3036, 0x33e5, 0x333a,
  0x52d7, 0x5396, 0x57e9, 0x62e6, 0x6eaf, 0x79c6, 0x79c8, 0x79d2,
  0x3177, 0x411a, 0x665e, 0x35b0, 0x5a7a, 0x3076, 0x3bd3, 0x7047,
  0x7685, 0x2e32, 0x4adb, 0x71e7, 0x3c51, 0x3c48,
  /* 0x13721..0x1377E */
  0x4398, 0x5a9f, 0x4c93, 0x7774, 0x6f61, 0x5aaa, 0x518a, 0x7688,
  0x5c82, 0x4817, 0x5e70, 0x4851, 0x736c, 0x32f2, 0x341b, 0x65ab,
  0x6a13, 0x5fa4, 0x6ecd, 0x70e1, 0x3366, 0x6888, 0x5941, 0x2fc2,
  0x30be, 0x3211, 0x3144, 0x3553, 0x372d, 0x53ea, 0x378b, 0x3951,
  0x3f62, 0x3f84, 0x4075, 0x4176, 0x4167, 0x41a9, 0x43b2, 0x443a,
  0x456c, 0x466f, 0x4842, 0x4e13, 0x5566, 0x5a3d, 0x5cfb, 0x5d4c,
  0x5d99, 0x5e4b, 0x5f6b, 0x630e, 0x634a, 0x66cd, 0x6a08, 0x6a63,
  0x6b66, 0x6efd, 0x781a, 0x7d8f, 0x62b8, 0x6fce, 0x7be8, 0x3287,
  0x421f, 0x4483, 0x4fc0, 0x7699, 0x4841, 0x3091, 0x4b20, 0x4c7a,
  0x4f54, 0x5a74, 0x5d50, 0x6840, 0x6a23, 0x4708, 0x2ef6, 0x3039,
  0x3026, 0x3065, 0x317c, 0x3238, 0x3263, 0x35a7, 0x370f, 0x3805,
  0x3acc, 0x3efa, 0x41b2, 0x41f8, 0x42f3, 0x4372,
  /* 0x13821..0x1387E */
  0x491c, 0x4a29, 0x527d, 0x52ac, 0x532e, 0x5814, 0x586f, 0x5d79,
  0x570c, 0x60a9, 0x698b, 0x6b19, 0x6ce2, 0x6ed2, 0x7063, 0x7375,
  0x767a, 0x7855, 0x7a13, 0x7e78, 0x3143, 0x339f, 0x33b3, 0x3e7b,
  0x3f26, 0x4e1b, 0x4e90, 0x5384, 0x53fe, 0x5d43, 0x6237, 0x6a00,
  0x6afa, 0x7650, 0x2e4e, 0x300b, 0x33e4, 0x347c, 0x36fa, 0x39d1,
  0x3b64, 0x3df1, 0x3eab, 0x3f27, 0x4238, 0x4545, 0x47af, 0x4e56,
  0x52d0, 0x5cca, 0x68b4, 0x60a1, 0x60e1, 0x63f0, 0x664e, 0x6a87,
  0x6de8, 0x7237, 0x76c7, 0x7867, 0x7f13, 0x2e94, 0x2e92, 0x2f0d,
  0x3348, 0x3449, 0x343e, 0x3a2f, 0x3f8c, 0x3fa1, 0x409f, 0x48a7,
  0x4a8e, 0x545a, 0x5881, 0x6a9e, 0x6aa4, 0x6b77, 0x7190, 0x2e5e,
  0x7bc9, 0x2ea4, 0x2f7c, 0x2faf, 0x3019, 0x3016, 0x3149, 0x316c,
  0x329f, 0x32b9, 0x32fe, 0x339a, 0x33e3, 0x3411,
  /* 0x13921..0x1397E */
  0x340e, 0x3589, 0x3751, 0x37a2, 0x397d, 0x3b54, 0x3b5d, 0x3b8f,
  0x3de5, 0x3de7, 0x3df7, 0x3e78, 0x3e83, 0x3e9a, 0x3eb7, 0x3f18,
  0x4052, 0x414c, 0x4297, 0x42d8, 0x43a7, 0x453b, 0x4602, 0x4643,
  0x46f4, 0x476d, 0x4821, 0x4897, 0x49cb, 0x4c5f, 0x4d2a, 0x4d69,
  0x4e2f, 0x4e9d, 0x5532, 0x5687, 0x586c, 0x5a3f, 0x5ce0, 0x5d05,
  0x5d18, 0x5d5e, 0x5db1, 0x6015, 0x6003, 0x60af, 0x60b1, 0x6154,
  0x618f, 0x622a, 0x6352, 0x684c, 0x6861, 0x6b1b, 0x6ca2, 0x6cfc,
  0x70ca, 0x7175, 0x7271, 0x583f, 0x72fc, 0x75a4, 0x764d, 0x7805,
  0x7999, 0x7ad8, 0x7d3b, 0x325b, 0x32ab, 0x33f7, 0x3408, 0x38d5,
  0x42f7, 0x4fe0, 0x6c6a, 0x6f5f, 0x7eb9, 0x314b, 0x323b, 0x344a,
  0x36fd, 0x5a40, 0x7177, 0x7d60, 0x7ed2, 0x5344, 0x4f09, 0x6170,
  0x5511, 0x3ffd, 0x40da, 0x7aa8, 0x52db, 0x6fbc,
  /* 0x13A21..0x13A7E */
  0x4b64, 0x7803, 0x2eca, 0x36f0, 0x3764, 0x38be, 0x3a5a, 0x4068,
  0x41c7, 0x460f, 0x4606, 0x4839, 0x48b1, 0x4df7, 0x55d5, 0x5d3a,
  0x626e, 0x7b42, 0x2e9b, 0x2f50, 0x33c9, 0x3506, 0x3d6f, 0x3de6,
  0x3dee, 0x47fb, 0x4c99, 0x5473, 0x5802, 0x6a50, 0x7396, 0x68df,
  0x3750, 0x3ea7, 0x432b, 0x30b5, 0x30ac, 0x318d, 0x4700, 0x34c9,
  0x385e, 0x39bb, 0x3bb0, 0x3f69, 0x424d, 0x43a1, 0x483d, 0x4b73,
  0x4e08, 0x507d, 0x71c7, 0x5280, 0x5815, 0x5826, 0x596d, 0x458e,
  0x5d30, 0x63dc, 0x68c1, 0x6f09, 0x769b, 0x3264, 0x3728, 0x4750,
  0x5f6a, 0x6ca1, 0x31b4, 0x3742, 0x762a, 0x383a, 0x498a, 0x60b4,
  0x34b2, 0x3d0e, 0x37fc, 0x5895, 0x7dfa, 0x2f5c, 0x324a, 0x348b,
  0x443e, 0x4628, 0x4714, 0x47f5, 0x5a84, 0x5b56, 0x5d22, 0x732f,
  0x485c, 0x7bad, 0x5b39, 0x3319, 0x318a, 0x3237,
  /* 0x13B21..0x13B7E */
  0x3bdf, 0x42f6, 0x44ae, 0x44e6, 0x472d, 0x4bba, 0x65a9, 0x76d1,
  0x5690, 0x7bd6, 0x434c, 0x7306, 0x7bab, 0x56bf, 0x4652, 0x2e09,
  0x3098, 0x33c2, 0x3c71, 0x40e8, 0x4492, 0x4563, 0x485f, 0x51e6,
  0x53ca, 0x5523, 0x5b97, 0x5e82, 0x6695, 0x6b83, 0x6cdb, 0x7178,
  0x7910, 0x45ac, 0x46ab, 0x4b8b, 0x2ed5, 0x2ed4, 0x2f3a, 0x2f7f,
  0x323a, 0x33f8, 0x33f2, 0x35e3, 0x36db, 0x38eb, 0x39cb, 0x39c9,
  0x39ff, 0x3b50, 0x3c4d, 0x3e02, 0x3e2b, 0x3fd7, 0x401d, 0x4307,
  0x452f, 0x3b5c, 0x45af, 0x45bd, 0x45e8, 0x479d, 0x4b62, 0x4b7b,
  0x4c0f, 0x5345, 0x5949, 0x59c1, 0x5cf8, 0x5d19, 0x5d2b, 0x60a2,
  0x6102, 0x61f3, 0x6996, 0x6a5e, 0x6a69, 0x6a66, 0x6a8c, 0x6aee,
  0x6cc7, 0x6cdc, 0x76cc, 0x78fc, 0x4b6f, 0x2e8b, 0x2f3c, 0x2f8d,
  0x3150, 0x3b57, 0x3bfa, 0x4148, 0x4301, 0x4642,
  /* 0x13C21..0x13C7E */
  0x4b21, 0x4ecb, 0x4cbb, 0x523e, 0x54bd, 0x55d4, 0x58c1, 0x593a,
  0x600c, 0x6033, 0x61ea, 0x6494, 0x6f9e, 0x4c50, 0x7e7f, 0x3f0f,
  0x6b58, 0x7d2b, 0x5afa, 0x6ef8, 0x3b8d, 0x76eb, 0x2e03, 0x33f1,
  0x37f7, 0x3931, 0x3ac9, 0x3ba4, 0x4089, 0x4e7f, 0x4f06, 0x55be,
  0x6cea, 0x3b9f, 0x6500, 0x5be0, 0x3072, 0x47f4, 0x629d, 0x3c61,
  0x654a, 0x5e1e, 0x620e, 0x3199, 0x3c04, 0x4368, 0x6d66, 0x459c,
  0x516e, 0x593e, 0x5d17, 0x6005, 0x6b1d, 0x6eca, 0x706e, 0x66c7,
  0x70aa, 0x301f, 0x32fa, 0x3c3a, 0x4753, 0x507c, 0x5235, 0x714c,
  0x71c8, 0x732b, 0x62e5, 0x3bc2, 0x3f31, 0x40f9, 0x2e3b, 0x33d6,
  0x3b88, 0x424b, 0x4731, 0x4b8a, 0x52e9, 0x53e0, 0x5a2e, 0x616b,
  0x6da3, 0x7152, 0x7996, 0x3112, 0x33d7, 0x346a, 0x3bff, 0x4388,
  0x4a39, 0x5dac, 0x7700, 0x36da, 0x33ce, 0x3468,
  /* 0x13D21..0x13D7E */
  0x3b97, 0x3c31, 0x3dde, 0x2fee, 0x4101, 0x42fe, 0x4d32, 0x59c0,
  0x59cb, 0x5d42, 0x5e4d, 0x5fd2, 0x61ed, 0x621f, 0x6490, 0x6846,
  0x6972, 0x6b90, 0x6e74, 0x6f2f, 0x7031, 0x714b, 0x716c, 0x76c6,
  0x719c, 0x2ec0, 0x2f4f, 0x3145, 0x3341, 0x3f93, 0x420e, 0x47d4,
  0x4c41, 0x4e0b, 0x5363, 0x5e26, 0x71cd, 0x7283, 0x33d4, 0x3919,
  0x3bbf, 0x4dd1, 0x595d, 0x5e2e, 0x5c9b, 0x387e, 0x519f, 0x31fa,
  0x6853, 0x6ff0, 0x2fca, 0x3cfb, 0x4625, 0x57ac, 0x5ae3, 0x621c,
  0x79ff, 0x31c6, 0x3faa, 0x45ec, 0x496f, 0x4b89, 0x4df3, 0x4e96,
  0x4f64, 0x56fe, 0x5d14, 0x3de1, 0x7075, 0x7187, 0x7806, 0x31e6,
  0x321d, 0x4240, 0x4691, 0x46d9, 0x4e1a, 0x3eb6, 0x5dd2, 0x5f72,
  0x46f8, 0x65af, 0x65f7, 0x6af8, 0x32a9, 0x33d9, 0x3973, 0x3e8f,
  0x3f90, 0x4055, 0x72e4, 0x7664, 0x30b7, 0x311f,
  /* 0x13E21..0x13E7E */
  0x32dd, 0x3320, 0x3347, 0x33ec, 0x34e8, 0x3546, 0x3531, 0x3617,
  0x3968, 0x39be, 0x3a3c, 0x3bb5, 0x3c06, 0x3c0f, 0x3c11, 0x3c1a,
  0x3e84, 0x3e8a, 0x3ee0, 0x3f70, 0x427f, 0x4284, 0x42db, 0x438c,
  0x4377, 0x4607, 0x460c, 0x462d, 0x4676, 0x477e, 0x48a2, 0x4a1f,
  0x4a35, 0x4cbc, 0x4d88, 0x4e09, 0x4e58, 0x513c, 0x5126, 0x5167,
  0x55c7, 0x5701, 0x585d, 0x5901, 0x5965, 0x59f0, 0x5ae0, 0x5b11,
  0x5ca7, 0x5d39, 0x6096, 0x63d6, 0x648b, 0x6549, 0x685d, 0x68f3,
  0x6a1f, 0x6a3c, 0x6a54, 0x6a73, 0x6c61, 0x6cde, 0x71a4, 0x7266,
  0x737e, 0x7418, 0x769c, 0x7798, 0x2e0a, 0x2e08, 0x2e1e, 0x2e57,
  0x3197, 0x3270, 0x37ce, 0x3834, 0x38cc, 0x3b22, 0x3e38, 0x40c5,
  0x44fe, 0x4761, 0x4756, 0x4d44, 0x52b6, 0x5573, 0x5a63, 0x64b8,
  0x6b72, 0x71b8, 0x7320, 0x3631, 0x37f4, 0x78fe,
  /* 0x13F21..0x13F7E */
  0x42ed, 0x490d, 0x4b96, 0x51ed, 0x5e54, 0x6077, 0x6272, 0x69e6,
  0x78df, 0x6755, 0x6fb1, 0x3c3b, 0x2f38, 0x2fe1, 0x2fb5, 0x3507,
  0x3a20, 0x3bdd, 0x3be9, 0x3fc3, 0x414e, 0x432f, 0x45b0, 0x464b,
  0x48ee, 0x499b, 0x4d78, 0x4df1, 0x5533, 0x55b9, 0x571f, 0x595e,
  0x59e6, 0x5d33, 0x61e3, 0x62af, 0x65aa, 0x69aa, 0x6a3a, 0x6eab,
  0x6f9b, 0x7032, 0x71dd, 0x7707, 0x2eba, 0x2ec1, 0x3203, 0x3875,
  0x38ec, 0x3c0b, 0x551a, 0x3c3d, 0x614e, 0x6a0a, 0x6fc5, 0x7663,
  0x776d, 0x5b25, 0x6acf, 0x7808, 0x7162, 0x36f3, 0x33a8, 0x7017,
  0x3439, 0x3782, 0x3e25, 0x43a8, 0x4c34, 0x508a, 0x5761, 0x5c8b,
  0x5fe0, 0x6870, 0x7042, 0x7154, 0x7310, 0x7318, 0x768f, 0x545e,
  0x7ac4, 0x3d07, 0x3d69, 0x4570, 0x47a2, 0x6da8, 0x76db, 0x436e,
  0x4749, 0x4919, 0x63c5, 0x7817, 0x76c0, 0x68fe,
  /* 0x14021..0x1407E */
  0x4f84, 0x447a, 0x3bf8, 0x2e16, 0x502c, 0x555d, 0x462f, 0x31c4,
  0x3236, 0x32e2, 0x39d3, 0x3f81, 0x4027, 0x4210, 0x453f, 0x4574,
  0x461f, 0x4674, 0x48f2, 0x4816, 0x4b63, 0x4e05, 0x5272, 0x551f,
  0x56db, 0x5cbe, 0x6056, 0x38f0, 0x68fd, 0x697f, 0x6aa0, 0x6a93,
  0x6acb, 0x701d, 0x7192, 0x7752, 0x7759, 0x4589, 0x5a0e, 0x6106,
  0x76bb, 0x3e2d, 0x40dc, 0x421a, 0x45a5, 0x4614, 0x4790, 0x57f3,
  0x5a4d, 0x5c4d, 0x5e3e, 0x610a, 0x6cac, 0x6d64, 0x6de1, 0x6e5f,
  0x58a9, 0x3207, 0x42d9, 0x43a5, 0x4442, 0x4298, 0x6a2d, 0x5a83,
  0x5bc0, 0x6aac, 0x76ea, 0x5d76, 0x620c, 0x6749, 0x2ed9, 0x3148,
  0x3343, 0x3360, 0x3ba3, 0x3c02, 0x3c16, 0x3ddd, 0x4226, 0x4247,
  0x44b0, 0x4813, 0x4834, 0x4cc9, 0x4d45, 0x4d17, 0x47d3, 0x4f5c,
  0x514e, 0x517d, 0x45cb, 0x5a7f, 0x5bad, 0x5dda,
  /* 0x14121..0x1417E */
  0x5e4a, 0x5fa8, 0x617a, 0x621b, 0x6239, 0x65a6, 0x6a6e, 0x6cce,
  0x6df5, 0x7078, 0x7077, 0x72ad, 0x7291, 0x7583, 0x7bae, 0x324d,
  0x3584, 0x4f38, 0x5136, 0x3168, 0x5985, 0x5e55, 0x61b3, 0x5cce,
  0x364c, 0x3851, 0x3ca8, 0x43aa, 0x46fe, 0x46fd, 0x495a, 0x52d9,
  0x558f, 0x558e, 0x590e, 0x5956, 0x59df, 0x5c97, 0x5d20, 0x5d44,
  0x6607, 0x6a34, 0x763b, 0x7061, 0x7f20, 0x30e7, 0x3275, 0x33cc,
  0x33e2, 0x3009, 0x35aa, 0x38ee, 0x394f, 0x523d, 0x3b8b, 0x3c64,
  0x331d, 0x40e3, 0x40f3, 0x435c, 0x4383, 0x433f, 0x43bb, 0x44cd,
  0x45e9, 0x46f9, 0x3de3, 0x49cd, 0x49fd, 0x4f15, 0x51e5, 0x2e89,
  0x55e9, 0x56f8, 0x5a93, 0x5cdf, 0x5dcf, 0x5d9c, 0x6061, 0x6349,
  0x6358, 0x646c, 0x64bc, 0x65fb, 0x68c5, 0x6d70, 0x7001, 0x706d,
  0x7397, 0x771c, 0x7a12, 0x30cf, 0x3897, 0x418e,
  /* 0x14221..0x1427E */
  0x61d3, 0x6535, 0x6d08, 0x7020, 0x2fc3, 0x3074, 0x3247, 0x3373,
  0x406f, 0x4349, 0x475f, 0x4e2c, 0x6db3, 0x701f, 0x2fd7, 0x3c5e,
  0x6cca, 0x45cf, 0x5d9a, 0x3352, 0x6896, 0x3176, 0x43c3, 0x3b58,
  0x3b6b, 0x3c0a, 0x440d, 0x4751, 0x705c, 0x2ed6, 0x391a, 0x392a,
  0x4c70, 0x6a51, 0x353e, 0x3815, 0x39a5, 0x40f0, 0x4253, 0x47c1,
  0x6235, 0x4955, 0x7640, 0x79c4, 0x7a28, 0x2f53, 0x3806, 0x3bfe,
  0x6010, 0x3cb1, 0x3e2f, 0x3f85, 0x4020, 0x414b, 0x4234, 0x46ff,
  0x4cf0, 0x4ede, 0x60ce, 0x617f, 0x62d4, 0x688b, 0x6cb8, 0x7000,
  0x702e, 0x768a, 0x7edb, 0x7bdb, 0x2ee3, 0x33f0, 0x3927, 0x5b2c,
  0x718d, 0x784c, 0x7df9, 0x4edd, 0x5027, 0x3353, 0x3544, 0x3b85,
  0x4258, 0x429e, 0x42d3, 0x4ca2, 0x4fef, 0x5422, 0x6a17, 0x7438,
  0x4fc1, 0x6afe, 0x6338, 0x31e7, 0x66f8, 0x33ea,
  /* 0x14321..0x1437E */
  0x33e9, 0x2f46, 0x7054, 0x6fb0, 0x396a, 0x6131, 0x3dfd, 0x5aea,
  0x6fbf, 0x48da, 0x6c37, 0x52f8, 0x7c48, 0x4a3d, 0x6ab0, 0x2e39,
  0x3358, 0x3606, 0x3766, 0x42c5, 0x43a2, 0x45e6, 0x4b4e, 0x4de1,
  0x4e5b, 0x50ad, 0x57ed, 0x5aef, 0x5baa, 0x5dbb, 0x603d, 0x60c6,
  0x66cb, 0x6a95, 0x735b, 0x36e3, 0x38c7, 0x3f3e, 0x45ad, 0x4696,
  0x4a80, 0x4bb5, 0x5537, 0x6ac7, 0x3024, 0x57e5, 0x3730, 0x3f1b,
  0x4065, 0x467a, 0x4c60, 0x55f4, 0x5a1a, 0x5f6e, 0x61f4, 0x6718,
  0x7045, 0x79b3, 0x5bc9, 0x555c, 0x5af9, 0x5b51, 0x64c4, 0x7010,
  0x59e9, 0x5a92, 0x6336, 0x3ae1, 0x5740, 0x2e2d, 0x2ef2, 0x3b99,
  0x3fe0, 0x42bd, 0x463c, 0x47f1, 0x4ce8, 0x666b, 0x6877, 0x6a3b,
  0x714e, 0x72f3, 0x79d0, 0x4a17, 0x5026, 0x532a, 0x62e7, 0x6457,
  0x6caf, 0x2e01, 0x3146, 0x31cb, 0x358b, 0x3bf5,
  /* 0x14421..0x1447E */
  0x3e16, 0x3e33, 0x3e81, 0x3f14, 0x3f35, 0x3f6b, 0x3fb4, 0x41f2,
  0x4311, 0x46a2, 0x471d, 0x4f6e, 0x5252, 0x553a, 0x573a, 0x6074,
  0x6139, 0x6178, 0x6776, 0x6abf, 0x6adc, 0x6d85, 0x6df3, 0x729a,
  0x7577, 0x7802, 0x7ce5, 0x32c5, 0x4357, 0x56f4, 0x4715, 0x4c88,
  0x53cd, 0x6cc3, 0x73ae, 0x7673, 0x4d25, 0x389c, 0x490e, 0x49cc,
  0x6ffd, 0x739a, 0x55db, 0x701a, 0x385a, 0x4802, 0x43b4, 0x49fb,
  0x2f43, 0x4f2c, 0x47d8, 0x6fbb, 0x6526, 0x5db4, 0x7354, 0x493f,
  0x4f70, 0x376a, 0x38f7, 0x3b2c, 0x5d2c, 0x522a, 0x340a, 0x71e3,
  0x7db4, 0x2ead, 0x2f4e, 0x305c, 0x3075, 0x3243, 0x6c9e, 0x3448,
  0x3824, 0x3b9a, 0x3e1d, 0x3e95, 0x3ead, 0x3ef7, 0x3f1f, 0x408c,
  0x42b5, 0x433a, 0x43d0, 0x48af, 0x4c40, 0x5887, 0x598e, 0x5a0b,
  0x5de0, 0x6247, 0x6a02, 0x6ae6, 0x6e44, 0x7013,
  /* 0x14521..0x1457E */
  0x70b8, 0x712d, 0x71d8, 0x7f0e, 0x4ce5, 0x4458, 0x44e2, 0x4575,
  0x4ef4, 0x5684, 0x5b1b, 0x7069, 0x73d1, 0x4eba, 0x34f2, 0x3fb9,
  0x44a4, 0x6f4d, 0x6fed, 0x7244, 0x3178, 0x386b, 0x3929, 0x3c55,
  0x3e97, 0x4dfb, 0x5e8f, 0x551c, 0x6cbc, 0x6ee2, 0x785b, 0x50b9,
  0x2f1d, 0x4bbf, 0x4fb1, 0x5530, 0x76fb, 0x314e, 0x3410, 0x3835,
  0x3857, 0x39ac, 0x3c60, 0x3f92, 0x4597, 0x475c, 0x4e21, 0x567b,
  0x63df, 0x6ced, 0x7014, 0x70fd, 0x734d, 0x5825, 0x583a, 0x32aa,
  0x3ea6, 0x371f, 0x3974, 0x4012, 0x3012, 0x315a, 0x31ac, 0x31cd,
  0x3200, 0x3510, 0x3854, 0x3858, 0x3957, 0x3b95, 0x3cf6, 0x3d8b,
  0x40bc, 0x4295, 0x442d, 0x4771, 0x4843, 0x48bc, 0x48df, 0x56d7,
  0x4dd8, 0x4e6f, 0x4d9b, 0x506f, 0x51c8, 0x3f53, 0x55d8, 0x5977,
  0x5b49, 0x5b54, 0x5b52, 0x5cd6, 0x5d71, 0x3230,
  /* 0x14621..0x1467E */
  0x6463, 0x6569, 0x65e4, 0x6a0e, 0x6b04, 0x6c46, 0x6e0f, 0x7003,
  0x700f, 0x7419, 0x7676, 0x782d, 0x7a30, 0x75d8, 0x30cd, 0x32d5,
  0x340c, 0x3802, 0x3c0e, 0x41a7, 0x449e, 0x4d1e, 0x57b3, 0x5ae5,
  0x60f4, 0x6404, 0x7053, 0x7285, 0x3ce0, 0x7d07, 0x333f, 0x3f97,
  0x3fb3, 0x4d9c, 0x5279, 0x5763, 0x59bf, 0x5be4, 0x4bd2, 0x52ec,
  0x6aad, 0x4803, 0x4a61, 0x31f8, 0x5a81, 0x4934, 0x3c4a, 0x7cf6,
  0x62eb, 0x3bc5, 0x7149, 0x501e, 0x3678, 0x3c6f, 0x40c7, 0x4566,
  0x4c8c, 0x6c5a, 0x7041, 0x7813, 0x3451, 0x46c7, 0x720d, 0x3948,
  0x70a3, 0x3185, 0x2e4d, 0x31ea, 0x6599, 0x6b0e, 0x5058, 0x437a,
  0x734b, 0x4962, 0x79b4, 0x5e04, 0x5577, 0x3357, 0x4960, 0x6edf,
  0x76e3, 0x4c5d, 0x2e8c, 0x3c3c, 0x3f10, 0x6fe9, 0x3302, 0x6cd1,
  0x6089, 0x6679, 0x3eff, 0x45e5, 0x2e73, 0x3165,
  /* 0x14721..0x1477E */
  0x3982, 0x3c3f, 0x77ee, 0x2efb, 0x398a, 0x3fcd, 0x6a8d, 0x4fe1,
  0x59b0, 0x5962, 0x3be7, 0x6471, 0x532b, 0x51b1, 0x3e74, 0x3ff5,
  0x437b, 0x449a, 0x51c3, 0x5c98, 0x2e43, 0x3efc, 0x2e4b, 0x37dc,
  0x36a2, 0x40a9, 0x4fc3, 0x5d0d, 0x60fd, 0x6133, 0x61bf, 0x6fb2,
  0x6997, 0x66a4, 0x3df4, 0x428a, 0x44ad, 0x6987, 0x4777, 0x4ce2,
  0x4d3e, 0x5436, 0x5834, 0x3a46, 0x5f75, 0x62ad, 0x79ac, 0x2ff3,
  0x3ec3, 0x42dd, 0x4392, 0x4557, 0x476f, 0x56c3, 0x524c, 0x60cc,
  0x60ba, 0x6f29, 0x714d, 0x300d, 0x37f9, 0x3a92, 0x4885, 0x4973,
  0x5164, 0x52fd, 0x6cb7, 0x38f2, 0x6ce0, 0x766a, 0x7019, 0x677f,
  0x59e4, 0x57e7, 0x6429, 0x2f2f, 0x3265, 0x335a, 0x42cd, 0x47cf,
  0x4cca, 0x567d, 0x5b94, 0x5c95, 0x6236, 0x6584, 0x6feb, 0x46dd,
  0x4f20, 0x5206, 0x5e1b, 0x63ab, 0x79c1, 0x7ea6,
  /* 0x14821..0x1487E */
  0x31fd, 0x5bb1, 0x5872, 0x5bb8, 0x6087, 0x5b48, 0x4ae8, 0x3e61,
  0x608c, 0x5551, 0x5560, 0x316b, 0x7262, 0x4e8c, 0x567a, 0x7197,
  0x7aea, 0x2f10, 0x5f70, 0x429c, 0x5b4f, 0x75a5, 0x7ce9, 0x367a,
  0x3859, 0x66e4, 0x76bc, 0x2f34, 0x3224, 0x334a, 0x33cd, 0x33db,
  0x3e06, 0x442c, 0x4591, 0x477f, 0x4c3e, 0x4c4e, 0x5248, 0x52af,
  0x53ed, 0x5554, 0x5e41, 0x622c, 0x65e9, 0x6ca9, 0x5bc4, 0x71c6,
  0x5169, 0x7812, 0x78ef, 0x433d, 0x4669, 0x556a, 0x56e4, 0x58d0,
  0x6543, 0x66ee, 0x332a, 0x3351, 0x3426, 0x3983, 0x3e87, 0x3f7c,
  0x40b2, 0x4249, 0x4279, 0x42ab, 0x4590, 0x4bd4, 0x4ccc, 0x55b2,
  0x56ae, 0x5891, 0x59d8, 0x5dcb, 0x5f77, 0x60a5, 0x68ab, 0x6ab9,
  0x6cbb, 0x707f, 0x775e, 0x78db, 0x4a0b, 0x5c38, 0x3099, 0x3c3e,
  0x3fae, 0x4787, 0x4bd8, 0x5435, 0x5709, 0x5f8e,
  /* 0x14921..0x1497E */
  0x7f3b, 0x47ca, 0x5a17, 0x3339, 0x558b, 0x7aed, 0x3f66, 0x619d,
  0x63f1, 0x6098, 0x3f3c, 0x3fc5, 0x5562, 0x5b46, 0x703c, 0x4867,
  0x39eb, 0x3a9b, 0x5d10, 0x567e, 0x6b2c, 0x2ff5, 0x3f6a, 0x4a19,
  0x4c37, 0x4f02, 0x54e2, 0x5968, 0x6868, 0x6a55, 0x6c79, 0x3edf,
  0x43cf, 0x55c5, 0x59d2, 0x62d7, 0x7328, 0x72f2, 0x649c, 0x66ed,
  0x7c2d, 0x34c1, 0x3f6c, 0x458c, 0x4d5c, 0x5015, 0x6ca7, 0x6cd3,
  0x783b, 0x454f, 0x54f6, 0x2e0d, 0x2ed8, 0x37e0, 0x392b, 0x3a66,
  0x3bcc, 0x31a8, 0x3e03, 0x3e9c, 0x4016, 0x4276, 0x4577, 0x45a7,
  0x466e, 0x4d6e, 0x5236, 0x5b26, 0x6150, 0x619a, 0x6299, 0x6b5c,
  0x6ca0, 0x6ce6, 0x6d74, 0x761c, 0x7644, 0x2fae, 0x44ab, 0x4b66,
  0x621e, 0x6461, 0x656a, 0x70e8, 0x3c01, 0x4953, 0x78a8, 0x647a,
  0x6557, 0x2f0f, 0x326f, 0x3fa9, 0x3e45, 0x470d,
  /* 0x14A21..0x14A7E */
  0x598f, 0x6179, 0x6907, 0x6986, 0x4df5, 0x3f17, 0x4255, 0x4cb8,
  0x2ecf, 0x5269, 0x7b92, 0x3206, 0x343b, 0x3674, 0x38b3, 0x41a4,
  0x426e, 0x511a, 0x396e, 0x5c89, 0x5cde, 0x5d1b, 0x76f0, 0x4587,
  0x605e, 0x2e19, 0x2f75, 0x3175, 0x3840, 0x3e63, 0x3e73, 0x3f0a,
  0x47c4, 0x2e26, 0x653d, 0x7589, 0x765b, 0x5c73, 0x7801, 0x30fb,
  0x38c1, 0x5656, 0x58a7, 0x3225, 0x57a5, 0x6511, 0x5b86, 0x304f,
  0x3909, 0x5247, 0x5bc7, 0x5de8, 0x6fba, 0x6fd4, 0x704d, 0x2fbf,
  0x32c9, 0x3a29, 0x3f01, 0x77ad, 0x2fdd, 0x6217, 0x72ea, 0x3703,
  0x4355, 0x4b69, 0x552b, 0x68dc, 0x6f14, 0x5a42, 0x32df, 0x3893,
  0x4155, 0x420a, 0x46ae, 0x4bcd, 0x5c3f, 0x63e9, 0x3023, 0x2ff8,
  0x3305, 0x3446, 0x3831, 0x3949, 0x3b9d, 0x3cf0, 0x3cef, 0x3d29,
  0x3e96, 0x42b1, 0x4367, 0x453e, 0x45b9, 0x470b,
  /* 0x14B21..0x14B7E */
  0x4cd5, 0x4ce1, 0x50f9, 0x5832, 0x5e2b, 0x60de, 0x62b3, 0x640c,
  0x64ec, 0x6702, 0x6912, 0x6a2a, 0x6c4a, 0x70a6, 0x72d2, 0x78fd,
  0x7cf3, 0x7d6c, 0x2e4f, 0x2ea1, 0x308d, 0x3256, 0x374a, 0x39a8,
  0x3e3d, 0x3fd8, 0x3fd9, 0x423f, 0x46b4, 0x471b, 0x47d0, 0x48d2,
  0x3192, 0x5d21, 0x60aa, 0x61a8, 0x6b00, 0x6c8c, 0x6cbf, 0x727e,
  0x7632, 0x3420, 0x782c, 0x3317, 0x30d5, 0x335c, 0x38a8, 0x44b2,
  0x4734, 0x5267, 0x5766, 0x5a46, 0x71e6, 0x32c3, 0x4ca1, 0x4b86,
  0x3800, 0x3e4c, 0x3954, 0x472c, 0x5ffb, 0x31e1, 0x56c6, 0x4469,
  0x58e8, 0x7b54, 0x7ebb, 0x37cb, 0x39b9, 0x4627, 0x479a, 0x4bce,
  0x34e9, 0x49d9, 0x3e55, 0x619c, 0x4795, 0x7baa, 0x47fe, 0x7c52,
  0x485d, 0x2ea6, 0x2fe3, 0x33c8, 0x42b9, 0x472b, 0x4cab, 0x6fc4,
  0x2fad, 0x5e6d, 0x7ebf, 0x2e07, 0x4162, 0x4e80,
  /* 0x14C21..0x14C7E */
  0x4f2b, 0x6513, 0x3473, 0x472a, 0x7b45, 0x3df3, 0x5b95, 0x3cac,
  0x3bc6, 0x671c, 0x4e4a, 0x64d1, 0x5a14, 0x6108, 0x3999, 0x5c8d,
  0x4c11, 0x5720, 0x32d9, 0x3922, 0x5121, 0x525f, 0x57db, 0x7727,
  0x7d61, 0x490b, 0x3a7f, 0x3a18, 0x31a5, 0x340d, 0x347d, 0x460e,
  0x56df, 0x6ff7, 0x7298, 0x7cf4, 0x39ea, 0x525d, 0x4ec5, 0x314d,
  0x48c9, 0x5dbf, 0x5dec, 0x7762, 0x7eba, 0x4478, 0x4a21, 0x6302,
  0x3984, 0x3b5f, 0x4bdb, 0x531b, 0x56f2, 0x5db2, 0x6017, 0x6499,
  0x3132, 0x4728, 0x7ed9, 0x56ee, 0x4762, 0x32ff, 0x7905, 0x3c24,
  0x423b, 0x5c7e, 0x6cb0, 0x354f, 0x40b6, 0x5d0b, 0x7580, 0x3301,
  0x2e5f, 0x31b6, 0x391c, 0x523a, 0x6036, 0x71ce, 0x3f25, 0x57e2,
  0x3384, 0x3f79, 0x5d04, 0x65ac, 0x6a33, 0x6e8d, 0x7756, 0x47f3,
  0x65ae, 0x7453, 0x4109, 0x4108, 0x4cb9, 0x5652,
  /* 0x14D21..0x14D7E */
  0x6aed, 0x6f38, 0x352f, 0x2f51, 0x312a, 0x32c7, 0x33cb, 0x3ba5,
  0x3e7d, 0x40a0, 0x4182, 0x43d6, 0x4709, 0x47da, 0x4e67, 0x4d8c,
  0x5336, 0x5337, 0x5531, 0x5950, 0x68d5, 0x6a98, 0x704a, 0x7091,
  0x70f5, 0x76c4, 0x678d, 0x3915, 0x2e88, 0x2f59, 0x2e0e, 0x6a89,
  0x6f3f, 0x7810, 0x30ad, 0x3e7c, 0x3996, 0x3bb9, 0x3eb8, 0x43da,
  0x43fa, 0x44c1, 0x46dc, 0x494a, 0x49d8, 0x4d0b, 0x4eb6, 0x5194,
  0x5528, 0x5aaf, 0x5f8a, 0x6000, 0x6449, 0x64c9, 0x6981, 0x6b21,
  0x6e0a, 0x7065, 0x767d, 0x790a, 0x417e, 0x4291, 0x4b32, 0x4c83,
  0x4d74, 0x5fcc, 0x5ffc, 0x4dc0, 0x5f85, 0x67ba, 0x68f8, 0x4765,
  0x63b1, 0x783c, 0x76f7, 0x4d1b, 0x5d61, 0x643d, 0x716a, 0x2e71,
  0x3375, 0x3d50, 0x4b04, 0x4feb, 0x65cd, 0x662d, 0x69a7, 0x3229,
  0x340f, 0x3c65, 0x474e, 0x48a8, 0x5406, 0x5483,
  /* 0x14E21..0x14E7E */
  0x55e2, 0x68cf, 0x68e1, 0x71cc, 0x76e2, 0x7678, 0x3f8b, 0x5387,
  0x5acb, 0x644e, 0x43a0, 0x5565, 0x3289, 0x4d41, 0x4e9c, 0x5409,
  0x5559, 0x586b, 0x5c92, 0x7686, 0x5adc, 0x7f8d, 0x2fb6, 0x416e,
  0x45c5, 0x665c, 0x2e86, 0x2eae, 0x30da, 0x2e21, 0x31cc, 0x3bee,
  0x4599, 0x4881, 0x4dbc, 0x531f, 0x5642, 0x57ad, 0x5a1c, 0x5ce7,
  0x626f, 0x6ad2, 0x707c, 0x71cf, 0x7675, 0x7818, 0x329b, 0x5dd1,
  0x302b, 0x3398, 0x4797, 0x4dcb, 0x51d0, 0x5433, 0x61e8, 0x6f2a,
  0x76a3, 0x7c57, 0x7e9f, 0x5460, 0x3841, 0x4d99, 0x5d2f, 0x785e,
  0x2ee4, 0x2f36, 0x2f8b, 0x31b7, 0x32b1, 0x3dba, 0x401c, 0x53b2,
  0x593c, 0x62d3, 0x7234, 0x76b7, 0x76f6, 0x770a, 0x7e97, 0x7f62,
  0x46a6, 0x4b74, 0x3217, 0x32a3, 0x50c8, 0x68c2, 0x3ec9, 0x404b,
  0x4190, 0x4f23, 0x5149, 0x5c3e, 0x5df4, 0x606f,
  /* 0x14F21..0x14F7E */
  0x64ee, 0x7023, 0x732c, 0x3442, 0x7b6f, 0x4ad3, 0x5089, 0x6cc2,
  0x6def, 0x7732, 0x32b4, 0x3a41, 0x3eca, 0x3f04, 0x4717, 0x497c,
  0x4994, 0x4d6a, 0x4f0f, 0x5262, 0x52fc, 0x5bed, 0x6001, 0x607e,
  0x674b, 0x70ce, 0x316d, 0x7e93, 0x5984, 0x608b, 0x7332, 0x6ad6,
  0x302d, 0x348c, 0x6a71, 0x4b6a, 0x6cc4, 0x6107, 0x40d1, 0x47a0,
  0x7df2, 0x2e99, 0x2e98, 0x7c10, 0x6a6b, 0x65c1, 0x6568, 0x4900,
  0x4e7e, 0x5897, 0x6155, 0x8e9f, 0x3b41, 0x3b56, 0x3b7d, 0x3b93,
  0x3bd8, 0x3bec, 0x3c12, 0x3c1e, 0x3c23, 0x3c2b, 0x178d, 0x3c62,
  0x813b, 0x813c, 0x96b4, 0x3c7a, 0x3c8f, 0x3c9f, 0x3ca3, 0x3caa,
  0x3cba, 0x3ccb, 0x3cd0, 0x3cd2, 0x3cf4, 0x9c34, 0x17e2, 0x3d0d,
  0x3d27, 0x8111, 0x3d46, 0x3d47, 0x3d53, 0x3d4a, 0x3d6d, 0x3d81,
  0x3da0, 0x3da4, 0x3da7, 0x3db8, 0x3dcb, 0x341e,
  /* 0x15021..0x1507E */
  0x3f0c, 0x2e10, 0x2e15, 0x2e2a, 0x2e31, 0x2e36, 0x2e3c, 0x2e3f,
  0x2e42, 0x2e56, 0x2e58, 0x2e82, 0x2e85, 0x6c6b, 0x2e8a, 0x6212,
  0x3f0d, 0x2e8e, 0x2e9e, 0x2e9f, 0x2ea0, 0x2ea2, 0x2eb0, 0x2eb3,
  0x2eb6, 0x2ece, 0x2ecd, 0x2ec4, 0x2ec6, 0x2ec2, 0x2ed7, 0x2ede,
  0x2eed, 0x2edf, 0x2ef7, 0x2f09, 0x2f5a, 0x2f30, 0x2f5b, 0x2f5d,
  0x2f57, 0x2f47, 0x2f76, 0x2f88, 0x2f8f, 0x2f98, 0x2f7b, 0x2f69,
  0x2f70, 0x2f91, 0x2f6f, 0x2f86, 0x2f96, 0x3118, 0x2fd4, 0x2fdf,
  0x2fce, 0x2fd8, 0x2fdb, 0x2fd1, 0x2fda, 0x2fd0, 0x2fe4, 0x2fe5,
  0x301a, 0x3028, 0x3014, 0x302a, 0x3025, 0x3005, 0x2f1c, 0x2ff6,
  0x3021, 0x3029, 0x302c, 0x2ffe, 0x2fef, 0x3011, 0x3006, 0x3043,
  0x3047, 0x4703, 0x3055, 0x3050, 0x3048, 0x305a, 0x3056, 0x306c,
  0x3078, 0x3080, 0x309a, 0x3085, 0x30b4, 0x30b2,
  /* 0x15121..0x1517E */
  0x30c9, 0x30ca, 0x30b3, 0x30c2, 0x30d6, 0x30de, 0x30e5, 0x30ed,
  0x30e3, 0x30ee, 0x30f9, 0x30f5, 0x3109, 0x3101, 0x3102, 0x3116,
  0x3115, 0x3114, 0x311a, 0x3121, 0x313a, 0x3137, 0x313c, 0x313b,
  0x313f, 0x3140, 0x3152, 0x314c, 0x3154, 0x3162, 0x5af8, 0x3169,
  0x316a, 0x316e, 0x3180, 0x3182, 0x36d8, 0x318c, 0x3189, 0x318f,
  0x3191, 0x3193, 0x3195, 0x3196, 0x31a4, 0x31a6, 0x31a2, 0x31a9,
  0x31aa, 0x31ab, 0x31b3, 0x31b1, 0x31b2, 0x31b0, 0x31b5, 0x31bd,
  0x31c5, 0x31c9, 0x31db, 0x31e0, 0x6655, 0x31e9, 0x31ed, 0x31f0,
  0x31f5, 0x31fe, 0x3204, 0x320b, 0x3214, 0x320e, 0x3227, 0x322a,
  0x322e, 0x3233, 0x3239, 0x324f, 0x3244, 0x324b, 0x324c, 0x325e,
  0x3254, 0x326a, 0x3274, 0x3269, 0x3273, 0x327f, 0x327d, 0x328d,
  0x3294, 0x3292, 0x3271, 0x3288, 0x3291, 0x6fa8,
  /* 0x15221..0x1527E */
  0x6fa7, 0x32ac, 0x32ad, 0x32bc, 0x32b5, 0x32c1, 0x32cd, 0x32d7,
  0x32de, 0x32e3, 0x32e6, 0x78ed, 0x32e0, 0x32f3, 0x32f5, 0x32f8,
  0x32f9, 0x3306, 0x3308, 0x5538, 0x330d, 0x3310, 0x330f, 0x3315,
  0x331a, 0x3323, 0x332f, 0x3331, 0x3333, 0x3338, 0x3340, 0x3346,
  0x3345, 0x2e17, 0x3349, 0x334d, 0x31d6, 0x335e, 0x3369, 0x336e,
  0x3918, 0x337b, 0x3377, 0x3382, 0x3396, 0x33a0, 0x33a6, 0x33a5,
  0x33ae, 0x33b0, 0x33b6, 0x33c3, 0x5c12, 0x76d9, 0x33df, 0x46fc,
  0x51ee, 0x33ee, 0x33e8, 0x33ed, 0x33fa, 0x3401, 0x343d, 0x3440,
  0x342c, 0x342d, 0x343c, 0x342e, 0x3436, 0x3429, 0x341d, 0x344e,
  0x348f, 0x3475, 0x348e, 0x345f, 0x3471, 0x3477, 0x3470, 0x3492,
  0x347b, 0x3480, 0x3476, 0x3484, 0x3490, 0x3486, 0x34c7, 0x34a2,
  0x34b8, 0x34a5, 0x34ac, 0x34c4, 0x34c8, 0x34a8,
  /* 0x15321..0x1537E */
  0x34ab, 0x34c2, 0x34a4, 0x34be, 0x34bc, 0x34d8, 0x34e5, 0x34e6,
  0x350f, 0x3514, 0x34fd, 0x34ee, 0x34ed, 0x34fa, 0x34e2, 0x3539,
  0x3540, 0x3563, 0x354c, 0x352e, 0x355c, 0x3545, 0x3556, 0x3557,
  0x3538, 0x3533, 0x355d, 0x3599, 0x3580, 0x34af, 0x358a, 0x359f,
  0x357b, 0x357e, 0x3598, 0x359e, 0x35ae, 0x357c, 0x3583, 0x35a9,
  0x3587, 0x35a8, 0x35da, 0x35c5, 0x35df, 0x35c4, 0x35dc, 0x35e4,
  0x35d4, 0x3614, 0x35f7, 0x3616, 0x35fe, 0x35fd, 0x361b, 0x35f9,
  0x364e, 0x3650, 0x51df, 0x3634, 0x3636, 0x3632, 0x3638, 0x366b,
  0x3664, 0x362f, 0x366c, 0x366a, 0x3686, 0x3680, 0x368a, 0x36a0,
  0x3694, 0x368f, 0x36a5, 0x36ae, 0x36b6, 0x36b4, 0x36c2, 0x36bc,
  0x36c1, 0x36c3, 0x36c0, 0x36c8, 0x36ce, 0x36d1, 0x36d3, 0x36d7,
  0x36ee, 0x36f9, 0x3700, 0x36ff, 0x3704, 0x3709,
  /* 0x15421..0x1547E */
  0x3708, 0x370b, 0x370d, 0x3713, 0x3718, 0x3716, 0x35c7, 0x371c,
  0x3726, 0x3737, 0x3738, 0x374e, 0x373b, 0x3740, 0x374f, 0x3769,
  0x37c0, 0x3788, 0x3761, 0x377f, 0x3789, 0x3793, 0x37a0, 0x37b3,
  0x37a4, 0x37aa, 0x37b0, 0x37c3, 0x37c6, 0x37d4, 0x37d2, 0x37d3,
  0x380a, 0x37d6, 0x37e3, 0x380b, 0x3819, 0x381d, 0x3872, 0x3821,
  0x3862, 0x384b, 0x3870, 0x4bc0, 0x3852, 0x383d, 0x3879, 0x3885,
  0x38b9, 0x389f, 0x38ab, 0x38ba, 0x38de, 0x38bb, 0x38b8, 0x38ae,
  0x38c5, 0x38d3, 0x38d1, 0x38d7, 0x38d9, 0x38d8, 0x38e5, 0x38dc,
  0x38e4, 0x38df, 0x38ef, 0x38fa, 0x38f9, 0x38fb, 0x38fc, 0x38fd,
  0x3902, 0x390a, 0x3910, 0x391b, 0x48a6, 0x3925, 0x392c, 0x392d,
  0x3932, 0x3938, 0x393e, 0x5ad2, 0x3955, 0x3950, 0x394e, 0x395a,
  0x3958, 0x3962, 0x3960, 0x3967, 0x396c, 0x3969,
  /* 0x15521..0x1557E */
  0x3978, 0x3981, 0x399d, 0x2f5e, 0x2fab, 0x39a3, 0x39b2, 0x39c6,
  0x39e8, 0x39dc, 0x398d, 0x39d9, 0x39da, 0x3a25, 0x3a1f, 0x3a11,
  0x3a1c, 0x3a09, 0x3a1a, 0x3a40, 0x3a6c, 0x3a49, 0x3a35, 0x3a36,
  0x3a62, 0x3a6a, 0x3a9a, 0x3abc, 0x3abe, 0x3acb, 0x3ac2, 0x3abd,
  0x3ae3, 0x3ad7, 0x3ae6, 0x3ae9, 0x3ad6, 0x3afa, 0x3afb, 0x3b0c,
  0x3b0b, 0x3b16, 0x3b32, 0x3ad0, 0x3b2a, 0x3b36, 0x3b3e, 0x3b43,
  0x3b45, 0x3b40, 0x3b51, 0x3b55, 0x3b5a, 0x3b5b, 0x3b65, 0x3b69,
  0x3b70, 0x3b73, 0x3b75, 0x3b78, 0x4588, 0x3b7a, 0x3b80, 0x3b83,
  0x3ba6, 0x3bb8, 0x3bc3, 0x3bc7, 0x3bc9, 0x3bd4, 0x3bd0, 0x3be4,
  0x3be6, 0x3be2, 0x3bde, 0x3be5, 0x3beb, 0x3bf0, 0x3bf6, 0x3bf3,
  0x3c05, 0x3c07, 0x3c08, 0x3c0d, 0x3c13, 0x3c20, 0x3c22, 0x3c28,
  0x3c38, 0x3c39, 0x3c41, 0x3c46, 0x3c4e, 0x3c53,
  /* 0x15621..0x1567E */
  0x3c50, 0x3c4f, 0x3b71, 0x3c6c, 0x3c6e, 0x2e62, 0x3c76, 0x3c79,
  0x3c8c, 0x3c91, 0x3c94, 0x399b, 0x3cab, 0x3cbb, 0x3cb6, 0x3cbc,
  0x3cb7, 0x3cc5, 0x3cbe, 0x3cc7, 0x3cd9, 0x3ce9, 0x3cfd, 0x3cfa,
  0x3ced, 0x3d8c, 0x3cea, 0x3d0b, 0x3d15, 0x3d17, 0x3d5c, 0x3d1f,
  0x3d1b, 0x3d11, 0x3d14, 0x3d22, 0x3d1a, 0x3d19, 0x3d18, 0x3d4c,
  0x3d52, 0x3d4e, 0x3d4b, 0x3d6c, 0x3d73, 0x3d76, 0x3d87, 0x3d84,
  0x3d82, 0x3da2, 0x3d9d, 0x3dac, 0x3dae, 0x3dbd, 0x3d90, 0x3db7,
  0x3dbc, 0x3dc9, 0x3dcd, 0x3dd3, 0x3dd2, 0x3dd6, 0x3ddb, 0x3deb,
  0x3df2, 0x3df5, 0x3e0b, 0x3e1a, 0x3e19, 0x3e11, 0x3e1b, 0x3e36,
  0x3e37, 0x3e44, 0x3e43, 0x3e40, 0x3e4e, 0x3e57, 0x3e54, 0x3e5f,
  0x3e62, 0x3e64, 0x3e47, 0x3e75, 0x3e76, 0x3e7a, 0x7ebc, 0x3e7f,
  0x3ea0, 0x3ec1, 0x3ec2, 0x3ec8, 0x3ed0, 0x3ecf,
  /* 0x15721..0x1577E */
  0x3ed6, 0x3ee3, 0x3edd, 0x3eda, 0x3edb, 0x3ee2, 0x3ee1, 0x3ee8,
  0x3ee9, 0x3eec, 0x3ef1, 0x3ef3, 0x3ef0, 0x3ef4, 0x3ef8, 0x3efe,
  0x3f03, 0x3f09, 0x3f5d, 0x3f5c, 0x3f0b, 0x3f11, 0x3f16, 0x3f29,
  0x3f2d, 0x3f38, 0x3f41, 0x3f48, 0x3f4c, 0x3f4e, 0x3f2f, 0x3f51,
  0x3f56, 0x3f57, 0x3f59, 0x3f61, 0x3f6d, 0x3f73, 0x3f77, 0x3f83,
  0x3f82, 0x3f7f, 0x3f8a, 0x3f88, 0x3f91, 0x3f87, 0x3f9e, 0x3f99,
  0x3f98, 0x3fa0, 0x3fa8, 0x3fad, 0x3fbc, 0x3fd6, 0x3ffb, 0x3fe4,
  0x3ff8, 0x3ff1, 0x3fdd, 0x40b3, 0x3fff, 0x4021, 0x4060, 0x4019,
  0x4010, 0x4029, 0x400e, 0x4031, 0x401b, 0x4015, 0x402b, 0x4026,
  0x400f, 0x403a, 0x405a, 0x4041, 0x406a, 0x4077, 0x405f, 0x404a,
  0x4046, 0x404d, 0x4063, 0x4043, 0x4064, 0x4042, 0x406c, 0x406b,
  0x4059, 0x4081, 0x408d, 0x40e7, 0x4083, 0x409a,
  /* 0x15821..0x1587E */
  0x4084, 0x409b, 0x4096, 0x4097, 0x4092, 0x40a7, 0x408b, 0x40e1,
  0x40b8, 0x40e0, 0x40d3, 0x40b4, 0x3ff0, 0x40bd, 0x40c6, 0x40b5,
  0x40d8, 0x414d, 0x4115, 0x4106, 0x40f6, 0x40f7, 0x4100, 0x40f4,
  0x40fa, 0x4103, 0x4121, 0x40fb, 0x40f1, 0x410d, 0x410e, 0x4147,
  0x413e, 0x4128, 0x4127, 0x414a, 0x413f, 0x413c, 0x412c, 0x4134,
  0x413d, 0x4142, 0x4144, 0x4173, 0x4177, 0x4158, 0x4159, 0x415a,
  0x416b, 0x4174, 0x416f, 0x4165, 0x4171, 0x415f, 0x415d, 0x4153,
  0x4175, 0x4199, 0x4196, 0x4187, 0x41ac, 0x4194, 0x419a, 0x418a,
  0x4191, 0x41ab, 0x41ae, 0x41cc, 0x41ca, 0x41c9, 0x41f7, 0x41c8,
  0x41c3, 0x41c6, 0x41ba, 0x41cb, 0x5f79, 0x41cd, 0x41e6, 0x41e3,
  0x41f6, 0x41fa, 0x41f4, 0x41ff, 0x41fd, 0x41fc, 0x41fe, 0x4200,
  0x4208, 0x4209, 0x420d, 0x420c, 0x4214, 0x421b,
  /* 0x15921..0x1597E */
  0x421e, 0x4221, 0x422a, 0x422e, 0x4230, 0x4232, 0x4233, 0x4241,
  0x424e, 0x425e, 0x4263, 0x425b, 0x4260, 0x4268, 0x427c, 0x4282,
  0x4289, 0x427e, 0x4292, 0x4293, 0x4296, 0x42d4, 0x4283, 0x4294,
  0x42d7, 0x42d1, 0x42bb, 0x42cf, 0x42ff, 0x42c6, 0x44d4, 0x42c8,
  0x42dc, 0x42cc, 0x42ca, 0x42c2, 0x42c7, 0x429b, 0x42c9, 0x430c,
  0x42ee, 0x42f1, 0x4327, 0x4302, 0x4308, 0x42ef, 0x42f5, 0x4350,
  0x433e, 0x434d, 0x441c, 0x434f, 0x4396, 0x438e, 0x4380, 0x43ab,
  0x4376, 0x43a3, 0x438f, 0x4389, 0x439f, 0x43b5, 0x436b, 0x4369,
  0x43be, 0x43e9, 0x43c0, 0x43c6, 0x43e3, 0x43c9, 0x43d2, 0x43f6,
  0x43c4, 0x4416, 0x4434, 0x4406, 0x4413, 0x4426, 0x4436, 0x451d,
  0x4417, 0x4428, 0x440f, 0x4467, 0x446f, 0x4476, 0x444e, 0x452a,
  0x4495, 0x4493, 0x44a5, 0x44a9, 0x4488, 0x44bc,
  /* 0x15A21..0x15A7E */
  0x44da, 0x44d2, 0x44c5, 0x44c7, 0x44bb, 0x44d8, 0x44c2, 0x44f1,
  0x44e7, 0x6209, 0x44e0, 0x44e1, 0x42ac, 0x44e3, 0x44ef, 0x452c,
  0x44f6, 0x44f4, 0x44f2, 0x44fa, 0x4500, 0x44fd, 0x4518, 0x451c,
  0x4505, 0x4524, 0x4523, 0x452b, 0x4534, 0x4535, 0x4537, 0x4536,
  0x4538, 0x554b, 0x4548, 0x4556, 0x4555, 0x454d, 0x4558, 0x455e,
  0x455d, 0x4572, 0x4578, 0x4582, 0x4583, 0x6b8a, 0x459b, 0x459f,
  0x45ab, 0x45b7, 0x45c3, 0x45c6, 0x45c1, 0x45c4, 0x45cc, 0x45d2,
  0x45db, 0x45d9, 0x45e0, 0x45e1, 0x45f1, 0x4772, 0x460a, 0x4603,
  0x45fb, 0x4773, 0x4635, 0x4636, 0x4634, 0x461c, 0x464f, 0x4644,
  0x4649, 0x4641, 0x465e, 0x465d, 0x4664, 0x4667, 0x4668, 0x465f,
  0x4662, 0x4670, 0x4683, 0x4688, 0x468e, 0x4689, 0x4684, 0x4698,
  0x469d, 0x46c1, 0x46b9, 0x46c9, 0x46be, 0x46bc,
  /* 0x15B21..0x15B7E */
  0x46c4, 0x46b8, 0x46d6, 0x46da, 0x46e0, 0x463f, 0x46e6, 0x46e9,
  0x46f0, 0x46f5, 0x46f7, 0x470f, 0x4716, 0x471e, 0x4726, 0x4727,
  0x7738, 0x472e, 0x473f, 0x4736, 0x4741, 0x4738, 0x4737, 0x4746,
  0x475e, 0x4760, 0x4759, 0x4763, 0x4764, 0x4789, 0x4770, 0x47a9,
  0x477c, 0x476a, 0x478c, 0x478b, 0x47a6, 0x47a1, 0x4785, 0x47b7,
  0x47ef, 0x47b4, 0x47ec, 0x47b3, 0x47e9, 0x47b8, 0x47e4, 0x47de,
  0x47dd, 0x47e2, 0x47ee, 0x47b9, 0x47ce, 0x47c6, 0x47e7, 0x4a9c,
  0x481e, 0x4846, 0x4829, 0x4840, 0x484d, 0x4832, 0x484e, 0x48b3,
  0x482b, 0x4859, 0x4863, 0x4877, 0x487f, 0x489f, 0x488f, 0x48ad,
  0x4894, 0x489d, 0x489b, 0x4883, 0x4aae, 0x48b9, 0x4874, 0x48b5,
  0x48a0, 0x48ba, 0x490f, 0x488d, 0x487e, 0x4901, 0x48ca, 0x4908,
  0x48d8, 0x4922, 0x4926, 0x48e1, 0x490c, 0x48cd,
  /* 0x15C21..0x15C7E */
  0x48d4, 0x48e7, 0x48d5, 0x4936, 0x4912, 0x4904, 0x48d7, 0x48e3,
  0x4925, 0x48f9, 0x48e0, 0x48ef, 0x4928, 0x492a, 0x491a, 0x4923,
  0x4921, 0x48c6, 0x4979, 0x4977, 0x495c, 0x4978, 0x496b, 0x4954,
  0x497e, 0x496e, 0x4939, 0x4974, 0x493d, 0x4959, 0x4930, 0x4961,
  0x495e, 0x495d, 0x4981, 0x496a, 0x49b2, 0x49ae, 0x49d0, 0x49bf,
  0x49c1, 0x49d3, 0x49be, 0x49ce, 0x3be8, 0x49ca, 0x49dd, 0x49bb,
  0x49c3, 0x49a7, 0x4a2e, 0x4991, 0x49a0, 0x499c, 0x4995, 0x49b4,
  0x49de, 0x49e8, 0x4a02, 0x4a1b, 0x49ff, 0x4b0a, 0x49f9, 0x49f2,
  0x49e7, 0x4a05, 0x49b1, 0x4a1e, 0x49ed, 0x4a14, 0x49eb, 0x4a0a,
  0x4a12, 0x4ac1, 0x4a23, 0x4a13, 0x4a44, 0x4a0c, 0x4a72, 0x4a36,
  0x4a78, 0x4a47, 0x4a62, 0x4a59, 0x4a66, 0x4a48, 0x4a38, 0x4a22,
  0x4a90, 0x4a8d, 0x4aa0, 0x4a84, 0x4aa2, 0x4aa3,
  /* 0x15D21..0x15D7E */
  0x4a97, 0x6617, 0x4abb, 0x4ac3, 0x4ac2, 0x4ab8, 0x4ab3, 0x4aac,
  0x4ade, 0x4ad1, 0x4adf, 0x4aaa, 0x4ada, 0x4aea, 0x4afb, 0x4b05,
  0x6616, 0x4afa, 0x4b12, 0x4b16, 0x7b31, 0x4b1f, 0x4b38, 0x4b37,
  0x56dc, 0x4b39, 0x78ee, 0x4b47, 0x4b43, 0x4b49, 0x4b50, 0x4b59,
  0x4b54, 0x4b5b, 0x4b5f, 0x4b61, 0x4b78, 0x4b79, 0x4b7f, 0x4b80,
  0x4b84, 0x4b83, 0x4b8d, 0x4b98, 0x4b95, 0x4b9e, 0x4ba4, 0x4baa,
  0x4bab, 0x4baf, 0x4bb2, 0x4bb1, 0x4bb3, 0x4bb7, 0x4bbc, 0x4bc6,
  0x4bcb, 0x4bd3, 0x4bdf, 0x4bec, 0x4beb, 0x4bf3, 0x4bef, 0x7ebe,
  0x4c08, 0x4c13, 0x4c14, 0x4c1b, 0x4c24, 0x4c23, 0x4c5e, 0x4c55,
  0x4c62, 0x4c6a, 0x4c82, 0x4c8d, 0x4c9a, 0x4c81, 0x4c9b, 0x4c7e,
  0x4c68, 0x4c73, 0x4c92, 0x4c90, 0x4cc4, 0x4cf1, 0x4cd3, 0x4cbd,
  0x4cd7, 0x4cc5, 0x4cdd, 0x4cae, 0x4cb1, 0x4cbe,
  /* 0x15E21..0x15E7E */
  0x4cba, 0x4cdb, 0x4cef, 0x4cd9, 0x4cea, 0x4d1f, 0x684d, 0x4d36,
  0x4d2b, 0x4d3d, 0x4d38, 0x4d19, 0x4d35, 0x4d33, 0x4d12, 0x4d0c,
  0x4d63, 0x4d93, 0x4d64, 0x4d5a, 0x4d79, 0x4d59, 0x4d8e, 0x4d95,
  0x4fe4, 0x4d85, 0x4df9, 0x4e15, 0x4e0a, 0x4db5, 0x4dc7, 0x4de6,
  0x4db8, 0x4dc6, 0x4dec, 0x4dde, 0x4dcc, 0x4de8, 0x4dd2, 0x4dc5,
  0x4dfa, 0x4dd9, 0x4de4, 0x4dd5, 0x4dea, 0x4dee, 0x4e2d, 0x4e6e,
  0x4e2e, 0x4e19, 0x4e72, 0x4e5f, 0x4e3e, 0x4e23, 0x4e6b, 0x4e2b,
  0x4e76, 0x4e4d, 0x4e1f, 0x4e43, 0x4e3a, 0x4e4e, 0x4e24, 0x4eff,
  0x4e1d, 0x4e38, 0x4e82, 0x4eaa, 0x4e98, 0x4ec9, 0x4eb7, 0x4ed3,
  0x4ebd, 0x4eaf, 0x4ec4, 0x4eb2, 0x4ed4, 0x4ed5, 0x4e8f, 0x4ea5,
  0x4ec2, 0x4e9f, 0x4f41, 0x4f11, 0x504c, 0x4eec, 0x4ef8, 0x4efe,
  0x4f3f, 0x4ef2, 0x4f31, 0x4eef, 0x4f32, 0x4ecc,
  /* 0x15F21..0x15F7E */
  0x4f3e, 0x4f13, 0x4ef7, 0x4f86, 0x4f7a, 0x4f78, 0x4f81, 0x4f80,
  0x4f6f, 0x4f5b, 0x4ff3, 0x4f6d, 0x4f82, 0x4f7c, 0x4f58, 0x4f8e,
  0x4f91, 0x4fc2, 0x4f66, 0x4fb3, 0x4fa3, 0x4fa1, 0x4fa4, 0x4fb9,
  0x4fc6, 0x4faa, 0x4fdf, 0x4fd5, 0x4fec, 0x4fd4, 0x4fd8, 0x4ff1,
  0x4fee, 0x4fdb, 0x5009, 0x500b, 0x4ffa, 0x5011, 0x5001, 0x500f,
  0x4ffe, 0x501b, 0x501a, 0x4f74, 0x501d, 0x5018, 0x501f, 0x5030,
  0x503e, 0x5032, 0x5051, 0x5063, 0x5099, 0x5092, 0x50af, 0x50f1,
  0x50ac, 0x50b8, 0x50b3, 0x50ae, 0x50df, 0x50cb, 0x50dd, 0x50d9,
  0x5109, 0x50fd, 0x511c, 0x5119, 0x5165, 0x5155, 0x5188, 0x5166,
  0x5162, 0x514c, 0x5156, 0x516c, 0x518f, 0x51fb, 0x5184, 0x5195,
  0x51a8, 0x51ac, 0x51d7, 0x51b9, 0x51be, 0x51d2, 0x51c9, 0x51d4,
  0x51ce, 0x51e0, 0x51ec, 0x51e7, 0x51f5, 0x51fc,
  /* 0x16021..0x1607E */
  0x51f9, 0x51ff, 0x520d, 0x5210, 0x521b, 0x5228, 0x522d, 0x522c,
  0x5230, 0x5232, 0x523b, 0x523c, 0x523f, 0x5240, 0x5246, 0x524b,
  0x5258, 0x5274, 0x527e, 0x5282, 0x5281, 0x5287, 0x5292, 0x5296,
  0x52a2, 0x52a7, 0x52b9, 0x52b2, 0x52c3, 0x52c6, 0x52c4, 0x52ce,
  0x52d2, 0x52e2, 0x52e0, 0x52e1, 0x52f9, 0x52f7, 0x300f, 0x5317,
  0x530a, 0x531c, 0x5316, 0x531d, 0x5334, 0x532f, 0x5329, 0x5325,
  0x533e, 0x534e, 0x534f, 0x7ed8, 0x5357, 0x536a, 0x5368, 0x5370,
  0x5378, 0x5375, 0x537b, 0x537a, 0x53c8, 0x53b3, 0x53ce, 0x53bb,
  0x53c0, 0x53e5, 0x53ee, 0x53de, 0x54a2, 0x5405, 0x546f, 0x5425,
  0x53f8, 0x5432, 0x543a, 0x5455, 0x543f, 0x545f, 0x5459, 0x5441,
  0x545c, 0x5469, 0x5470, 0x5463, 0x546a, 0x5476, 0x547e, 0x548b,
  0x549e, 0x54a7, 0x54ca, 0x54cf, 0x54d4, 0x53f1,
  /* 0x16121..0x1617E */
  0x54e0, 0x54e3, 0x54e7, 0x54e9, 0x54ee, 0x54f2, 0x54f0, 0x54f1,
  0x54f8, 0x54f7, 0x5504, 0x5503, 0x5505, 0x550c, 0x550e, 0x550d,
  0x5515, 0x5513, 0x551e, 0x5526, 0x552c, 0x553c, 0x5544, 0x554d,
  0x554a, 0x5549, 0x555b, 0x5546, 0x555a, 0x5569, 0x5564, 0x5567,
  0x556b, 0x556d, 0x5578, 0x5576, 0x5586, 0x5587, 0x5574, 0x558a,
  0x5589, 0x5582, 0x5594, 0x559a, 0x559d, 0x55a5, 0x55a3, 0x55c2,
  0x55b3, 0x55c3, 0x55b5, 0x55bd, 0x55b8, 0x55bc, 0x55b1, 0x55cd,
  0x55ca, 0x55d2, 0x55d9, 0x55e3, 0x55de, 0x55fe, 0x55ff, 0x55fc,
  0x5601, 0x55f0, 0x55fa, 0x55f2, 0x55f3, 0x560b, 0x560d, 0x5609,
  0x561f, 0x5627, 0x5620, 0x5621, 0x5622, 0x5624, 0x5634, 0x5630,
  0x563b, 0x5647, 0x5648, 0x5646, 0x565c, 0x5658, 0x5661, 0x5662,
  0x5668, 0x5669, 0x566a, 0x5667, 0x566c, 0x5670,
  /* 0x16221..0x1627E */
  0x5672, 0x5676, 0x5678, 0x567c, 0x5680, 0x5683, 0x5688, 0x568b,
  0x568e, 0x5696, 0x5693, 0x5699, 0x569a, 0x56b0, 0x56b4, 0x56b8,
  0x56b9, 0x56ba, 0x56c2, 0x56cd, 0x56d6, 0x56d2, 0x56de, 0x56e1,
  0x56e5, 0x56e7, 0x56ea, 0x662f, 0x56fb, 0x5708, 0x5707, 0x5704,
  0x5729, 0x5724, 0x571e, 0x5725, 0x5726, 0x571b, 0x5737, 0x5738,
  0x5747, 0x575a, 0x5768, 0x576b, 0x575b, 0x5765, 0x577f, 0x577e,
  0x5779, 0x578e, 0x578b, 0x5791, 0x57a0, 0x579e, 0x57b0, 0x57b6,
  0x57b9, 0x57bf, 0x57bc, 0x57bd, 0x57bb, 0x57c7, 0x57cd, 0x57d7,
  0x57da, 0x57dc, 0x57e3, 0x57ee, 0x57fc, 0x580c, 0x5812, 0x5926,
  0x5820, 0x592a, 0x5845, 0x588e, 0x5874, 0x5886, 0x587c, 0x589a,
  0x588c, 0x58a3, 0x58b5, 0x58aa, 0x58af, 0x58d1, 0x58c6, 0x58cb,
  0x58d4, 0x58be, 0x58bc, 0x58c5, 0x58ca, 0x58ec,
  /* 0x16321..0x1637E */
  0x58e7, 0x58da, 0x58fd, 0x58f4, 0x5907, 0x5912, 0x5911, 0x5919,
  0x592c, 0x592b, 0x5940, 0x5960, 0x5957, 0x595f, 0x595a, 0x5955,
  0x5953, 0x597a, 0x597f, 0x598a, 0x599d, 0x59a7, 0x7f4b, 0x59aa,
  0x59ae, 0x59b3, 0x59b9, 0x59ba, 0x59c9, 0x59d5, 0x59e7, 0x59ec,
  0x59e1, 0x59e3, 0x5a08, 0x5a0d, 0x5a18, 0x5a19, 0x5a20, 0x5a1f,
  0x5980, 0x5a31, 0x5a3b, 0x5a3e, 0x5a37, 0x5a43, 0x5a57, 0x5a49,
  0x5a61, 0x5a62, 0x5a69, 0x7f9d, 0x5a70, 0x5a79, 0x5a7d, 0x5a88,
  0x5a97, 0x5a95, 0x5a98, 0x5a96, 0x5aa9, 0x5ac8, 0x5ab0, 0x5ab6,
  0x5ac5, 0x5ac4, 0x5abf, 0x7083, 0x5ac7, 0x5aca, 0x5acd, 0x5acf,
  0x5ad5, 0x5ad3, 0x5ad9, 0x5ada, 0x5add, 0x5ae1, 0x5ae2, 0x5ae6,
  0x5aed, 0x5af0, 0x5b02, 0x5b0f, 0x5b0a, 0x5b06, 0x5b33, 0x5b18,
  0x5b19, 0x5b1e, 0x5b35, 0x5b28, 0x5b36, 0x5b50,
  /* 0x16421..0x1647E */
  0x5b7a, 0x5b04, 0x5b4d, 0x5b0b, 0x5b4c, 0x5b45, 0x5b75, 0x5b65,
  0x5b74, 0x5b67, 0x5b70, 0x5b71, 0x5b6c, 0x5b6e, 0x5b9d, 0x5b98,
  0x5b9f, 0x5b8d, 0x5b9c, 0x5b9a, 0x5b8b, 0x5b92, 0x5b8f, 0x5b5d,
  0x5b99, 0x5bcb, 0x5bc1, 0x5bcc, 0x5bcf, 0x5bb4, 0x5bc6, 0x5bdd,
  0x5be9, 0x5c11, 0x5c14, 0x5be6, 0x5be5, 0x5c60, 0x5c00, 0x5c07,
  0x5c13, 0x5bf3, 0x5bf7, 0x5c17, 0x5c0d, 0x5bf6, 0x5c23, 0x5c27,
  0x5c2a, 0x5c1f, 0x5c37, 0x5c2b, 0x5c3d, 0x5c4c, 0x5c43, 0x5c54,
  0x5c4f, 0x5c40, 0x5c50, 0x5c58, 0x5c5f, 0x5c64, 0x5c56, 0x5c65,
  0x5c6c, 0x5c75, 0x5c83, 0x5c90, 0x5ca4, 0x5cad, 0x5ca2, 0x5cab,
  0x5ca1, 0x5ca8, 0x5cb3, 0x5cb2, 0x5cb1, 0x5cae, 0x5cb9, 0x5cbd,
  0x5cc0, 0x5cc5, 0x5cc2, 0x5cd8, 0x5cd2, 0x5cdc, 0x5ce2, 0x7b3b,
  0x5cef, 0x5cf2, 0x5cf4, 0x5cf6, 0x5cfa, 0x5d06,
  /* 0x16521..0x1657E */
  0x5d02, 0x5d1c, 0x5d15, 0x5d0a, 0x5d45, 0x5d4b, 0x5d2e, 0x5d32,
  0x5d3f, 0x5d35, 0x5d46, 0x5d73, 0x5d56, 0x5d4e, 0x5d72, 0x5d68,
  0x5d6e, 0x5d4f, 0x5d63, 0x5d93, 0x5d89, 0x5d5b, 0x5d8f, 0x5d7d,
  0x5d9b, 0x5dba, 0x5dae, 0x5da3, 0x5db5, 0x5dc7, 0x5dbd, 0x5dab,
  0x5e3d, 0x5da2, 0x5daf, 0x5ddc, 0x5db8, 0x5d9f, 0x5db0, 0x5dd8,
  0x5ddd, 0x5de4, 0x5dde, 0x5dfb, 0x5df2, 0x5de1, 0x5e05, 0x5e0a,
  0x5e23, 0x5e21, 0x5e12, 0x5e31, 0x5e1f, 0x5e09, 0x5e0b, 0x5e22,
  0x5e46, 0x5e66, 0x5e3b, 0x5e35, 0x5e39, 0x5e43, 0x5e37, 0x5e32,
  0x5e3a, 0x5e67, 0x5e5d, 0x5e56, 0x5e5e, 0x5e59, 0x5e5a, 0x5e79,
  0x5e6a, 0x5e69, 0x5e7c, 0x5e7b, 0x5e83, 0x5dd5, 0x5e7d, 0x6fae,
  0x5e7f, 0x5e88, 0x5e89, 0x5e8c, 0x5e92, 0x5e90, 0x5e93, 0x5e94,
  0x5e96, 0x5e8e, 0x5e9b, 0x5e9c, 0x5f38, 0x5f3a,
  /* 0x16621..0x1667E */
  0x5f45, 0x5f4c, 0x5f4d, 0x5f4e, 0x5f50, 0x5f51, 0x5f55, 0x5f54,
  0x5f58, 0x5f5f, 0x5f60, 0x5f68, 0x5f69, 0x5f67, 0x5f78, 0x5f82,
  0x5f86, 0x5f83, 0x5f88, 0x5f87, 0x5f8c, 0x5f94, 0x5f9e, 0x5f9d,
  0x5f9a, 0x5fa3, 0x5faf, 0x5fb2, 0x5fb9, 0x5fae, 0x5fb6, 0x5fb8,
  0x6b71, 0x5fc5, 0x5fc6, 0x5fca, 0x5fd5, 0x5fd4, 0x5fe1, 0x5fe6,
  0x5fe9, 0x5ff3, 0x5ff9, 0x78dc, 0x6006, 0x6004, 0x600b, 0x6012,
  0x6018, 0x6019, 0x601c, 0x6021, 0x6028, 0x603f, 0x603b, 0x604a,
  0x6046, 0x6052, 0x6058, 0x605a, 0x605f, 0x6062, 0x6068, 0x6073,
  0x6072, 0x6070, 0x6076, 0x6079, 0x607d, 0x607f, 0x6084, 0x6086,
  0x6085, 0x609b, 0x6093, 0x609a, 0x60ad, 0x3190, 0x60ac, 0x60db,
  0x60e5, 0x60d9, 0x60dd, 0x60c4, 0x60da, 0x60d6, 0x6109, 0x60ef,
  0x60f1, 0x611b, 0x6129, 0x6123, 0x612f, 0x614b,
  /* 0x16721..0x1677E */
  0x768b, 0x6146, 0x613e, 0x6153, 0x6151, 0x60fc, 0x6171, 0x616e,
  0x6165, 0x6166, 0x6174, 0x6183, 0x6188, 0x618a, 0x6180, 0x6182,
  0x61a0, 0x6195, 0x61a4, 0x61a3, 0x615f, 0x6193, 0x61a9, 0x61b0,
  0x61b5, 0x61be, 0x61b8, 0x61bd, 0x61c0, 0x61c2, 0x61ba, 0x61c9,
  0x61cd, 0x61d1, 0x61d9, 0x61d8, 0x61c8, 0x61da, 0x61df, 0x61e0,
  0x61e7, 0x61fa, 0x61fb, 0x61fe, 0x6201, 0x6202, 0x6205, 0x6207,
  0x620a, 0x620d, 0x6210, 0x6216, 0x6229, 0x622b, 0x6238, 0x6233,
  0x6240, 0x6259, 0x6258, 0x625d, 0x625a, 0x625f, 0x6264, 0x6262,
  0x6268, 0x626a, 0x626b, 0x622e, 0x6271, 0x6277, 0x6278, 0x627e,
  0x628d, 0x6292, 0x62ab, 0x629f, 0x62bb, 0x62ac, 0x62e1, 0x62e3,
  0x62df, 0x62d2, 0x62f4, 0x62f3, 0x62fa, 0x6393, 0x6303, 0x62fb,
  0x62f9, 0x62de, 0x6306, 0x62dc, 0x6309, 0x62d9,
  /* 0x16821..0x1687E */
  0x6335, 0x6334, 0x6316, 0x6332, 0x6331, 0x6340, 0x6339, 0x6350,
  0x6345, 0x632f, 0x632b, 0x6317, 0x6318, 0x6385, 0x639a, 0x63aa,
  0x639f, 0x63a2, 0x6396, 0x6323, 0x638e, 0x6387, 0x638a, 0x637c,
  0x63b5, 0x6373, 0x6375, 0x63a0, 0x6389, 0x63a8, 0x63f4, 0x6413,
  0x63eb, 0x63ce, 0x63fd, 0x6403, 0x63d8, 0x640b, 0x63c1, 0x63f7,
  0x6407, 0x63e0, 0x63f2, 0x640d, 0x6422, 0x6420, 0x63bd, 0x6438,
  0x6506, 0x63fb, 0x646d, 0x642a, 0x643c, 0x655a, 0x6484, 0x6477,
  0x646b, 0x64ad, 0x646e, 0x6482, 0x6469, 0x6446, 0x642c, 0x646f,
  0x6479, 0x6435, 0x64ca, 0x6462, 0x64b9, 0x64bf, 0x649f, 0x64d9,
  0x64cd, 0x64bb, 0x64da, 0x64d0, 0x64c1, 0x64c6, 0x64d6, 0x64a1,
  0x6521, 0x64ff, 0x64f4, 0x6517, 0x6518, 0x652c, 0x651f, 0x6515,
  0x6514, 0x64fc, 0x6540, 0x6563, 0x6558, 0x6548,
  /* 0x16921..0x1697E */
  0x6541, 0x6602, 0x654b, 0x6555, 0x6580, 0x65a4, 0x6588, 0x6591,
  0x658a, 0x65a8, 0x656d, 0x6594, 0x659b, 0x65ea, 0x6587, 0x659c,
  0x6577, 0x657e, 0x6590, 0x65c9, 0x65ba, 0x65cf, 0x65b9, 0x65d0,
  0x65d5, 0x65dd, 0x65e5, 0x65dc, 0x65f9, 0x660a, 0x6613, 0x660b,
  0x65fe, 0x65fa, 0x6606, 0x6622, 0x661a, 0x6630, 0x663f, 0x664d,
  0x2e55, 0x6654, 0x665f, 0x6667, 0x6671, 0x6693, 0x66a3, 0x66a9,
  0x66aa, 0x668b, 0x668c, 0x66b6, 0x66af, 0x66c4, 0x66c6, 0x66b0,
  0x66c9, 0x6823, 0x66ab, 0x66d4, 0x66de, 0x66e9, 0x66ec, 0x66df,
  0x66db, 0x66ef, 0x6712, 0x6706, 0x6708, 0x6700, 0x6703, 0x66fb,
  0x6711, 0x6709, 0x670d, 0x66f9, 0x670a, 0x6734, 0x673f, 0x6737,
  0x673b, 0x6725, 0x6729, 0x671a, 0x6760, 0x675f, 0x6778, 0x674c,
  0x674e, 0x6774, 0x6757, 0x6768, 0x676e, 0x6759,
  /* 0x16A21..0x16A7E */
  0x6753, 0x6763, 0x676a, 0x6805, 0x67a2, 0x679f, 0x6782, 0x67af,
  0x67cb, 0x67bd, 0x67c0, 0x67d0, 0x76d6, 0x67ab, 0x67c4, 0x67b3,
  0x67c7, 0x67c6, 0x67bb, 0x67ef, 0x67f2, 0x67e0, 0x680f, 0x680d,
  0x67fe, 0x67f6, 0x67f7, 0x680e, 0x67d2, 0x6811, 0x6816, 0x6815,
  0x6822, 0x6821, 0x6831, 0x6836, 0x6839, 0x6827, 0x683b, 0x6844,
  0x6842, 0x6852, 0x6859, 0x685e, 0x6862, 0x686b, 0x6881, 0x687e,
  0x689e, 0x6875, 0x687d, 0x68b5, 0x6872, 0x6882, 0x6897, 0x6892,
  0x68ae, 0x6899, 0x68a2, 0x688d, 0x68a4, 0x68b0, 0x68bf, 0x68b1,
  0x68c3, 0x68c4, 0x68d4, 0x68d8, 0x68d9, 0x68dd, 0x68f9, 0x6902,
  0x68fc, 0x68f4, 0x68e8, 0x68f2, 0x6904, 0x690c, 0x690a, 0x6913,
  0x6943, 0x691e, 0x6925, 0x692a, 0x692b, 0x6941, 0x6944, 0x693b,
  0x6936, 0x6938, 0x694c, 0x691d, 0x6960, 0x695e,
  /* 0x16B21..0x16B7E */
  0x6966, 0x6964, 0x696d, 0x696a, 0x696f, 0x6974, 0x6977, 0x697e,
  0x6983, 0x6988, 0x698a, 0x6993, 0x6998, 0x69a1, 0x69a9, 0x69a6,
  0x69ac, 0x69af, 0x69b2, 0x69ba, 0x69bd, 0x69bf, 0x69c0, 0x69da,
  0x69dc, 0x69dd, 0x69e7, 0x69f4, 0x69f8, 0x6a03, 0x6a16, 0x6a10,
  0x6a0c, 0x6a1b, 0x6a1d, 0x6a25, 0x6a36, 0x6a41, 0x6a5b, 0x6a52,
  0x6a46, 0x6a48, 0x6a7c, 0x6a6d, 0x6a6c, 0x6a62, 0x6a85, 0x6a82,
  0x6a84, 0x6aa8, 0x6aa1, 0x6a91, 0x6aa5, 0x6aa6, 0x6a9a, 0x6aa3,
  0x6ac4, 0x6acd, 0x6ac2, 0x6ada, 0x6aeb, 0x6af3, 0x6ae7, 0x6ae4,
  0x6af1, 0x6b14, 0x6ae0, 0x6ae2, 0x6af7, 0x6ade, 0x6adb, 0x6b0c,
  0x6b07, 0x6b1a, 0x6ae1, 0x6b16, 0x6b10, 0x6b17, 0x6b20, 0x6b33,
  0x77ab, 0x6b26, 0x6b2b, 0x6b3e, 0x6b28, 0x6b41, 0x6b4c, 0x6b4f,
  0x6b4e, 0x6b49, 0x6b56, 0x6b5b, 0x6b5a, 0x6b6b,
  /* 0x16C21..0x16C7E */
  0x6b5f, 0x6b6c, 0x6b6f, 0x6b74, 0x6b7d, 0x6b80, 0x6b8c, 0x6b8e,
  0x6b92, 0x6b93, 0x6b96, 0x6b99, 0x6b9a, 0x6c3a, 0x6c41, 0x6c3f,
  0x6c48, 0x6c4c, 0x6c4e, 0x6c50, 0x6c55, 0x6c62, 0x6c6c, 0x6c78,
  0x6c7a, 0x6c82, 0x6c89, 0x6c85, 0x6c8a, 0x6c8d, 0x6c8e, 0x6c94,
  0x6c7c, 0x6c98, 0x421d, 0x6cad, 0x6caa, 0x6cbd, 0x6cb2, 0x6cb3,
  0x6cae, 0x6cb6, 0x6cc8, 0x6cc1, 0x6ce4, 0x6ce3, 0x6cda, 0x6cfd,
  0x6cfa, 0x6cfb, 0x6d04, 0x6d05, 0x6d0a, 0x6d07, 0x6d0f, 0x6d0d,
  0x6d10, 0x7f4e, 0x6d13, 0x6ccd, 0x6d14, 0x6d16, 0x6d67, 0x6d6d,
  0x6d71, 0x6d73, 0x6d81, 0x6d99, 0x6dc2, 0x6dbe, 0x6dba, 0x6dcf,
  0x6dda, 0x6dd6, 0x6dcc, 0x6ddb, 0x6dcb, 0x6dea, 0x6deb, 0x6ddf,
  0x6de3, 0x6dfc, 0x6e08, 0x6e09, 0x6dff, 0x6e1d, 0x6e1e, 0x6e10,
  0x6e1f, 0x6e42, 0x6e35, 0x6e30, 0x6e34, 0x6e4a,
  /* 0x16D21..0x16D7E */
  0x6e47, 0x6e49, 0x6e4c, 0x6e50, 0x6e48, 0x6e59, 0x6e64, 0x6e60,
  0x6e2a, 0x6e63, 0x6e55, 0x6e76, 0x6e72, 0x6e7c, 0x6e81, 0x6e87,
  0x6e85, 0x6e84, 0x6e8b, 0x6e8a, 0x6e93, 0x6e91, 0x6e94, 0x6e99,
  0x6eaa, 0x6ea1, 0x6eac, 0x6eb0, 0x6ec6, 0x6eb1, 0x6ebe, 0x6ec5,
  0x6ec8, 0x6ecb, 0x6edb, 0x6ee3, 0x6efc, 0x6efb, 0x6eeb, 0x6efe,
  0x6f0a, 0x6f05, 0x6f15, 0x6f12, 0x6f19, 0x6f13, 0x6f1c, 0x6f1f,
  0x6f1b, 0x6f0c, 0x6f26, 0x6f33, 0x6f3b, 0x6f39, 0x6f45, 0x6f42,
  0x6f3e, 0x6f4c, 0x6f49, 0x6f46, 0x6f4e, 0x6f57, 0x6f5c, 0x6f62,
  0x6f63, 0x6f64, 0x6f9c, 0x6f9f, 0x6fa3, 0x6fad, 0x6faf, 0x6fb7,
  0x6fda, 0x6fe5, 0x6fe2, 0x6fea, 0x6fef, 0x7087, 0x6ff4, 0x7005,
  0x6ff9, 0x6ffa, 0x7011, 0x7015, 0x7021, 0x700d, 0x701e, 0x7016,
  0x700b, 0x7027, 0x7036, 0x7035, 0x7039, 0x6ff8,
  /* 0x16E21..0x16E7E */
  0x704f, 0x7050, 0x7051, 0x7052, 0x700e, 0x7049, 0x703e, 0x7056,
  0x7058, 0x705e, 0x7068, 0x706f, 0x7076, 0x76a8, 0x7072, 0x7082,
  0x707d, 0x7081, 0x7080, 0x708a, 0x7089, 0x708f, 0x70a8, 0x70af,
  0x70b1, 0x70b5, 0x70e2, 0x70e4, 0x4248, 0x70db, 0x7102, 0x7112,
  0x7119, 0x7132, 0x7130, 0x714a, 0x7156, 0x7158, 0x7163, 0x7165,
  0x7169, 0x7173, 0x7172, 0x718b, 0x7189, 0x7182, 0x71a2, 0x71ab,
  0x71af, 0x71aa, 0x71b5, 0x71b4, 0x71ba, 0x71c0, 0x71c1, 0x71c9,
  0x71cb, 0x71d0, 0x71d6, 0x71df, 0x71e1, 0x71db, 0x71fc, 0x71f5,
  0x71f6, 0x721e, 0x71ff, 0x7214, 0x722c, 0x7215, 0x7211, 0x725e,
  0x7257, 0x7245, 0x7249, 0x7264, 0x7248, 0x7295, 0x723f, 0x724b,
  0x7250, 0x729c, 0x7296, 0x7293, 0x729b, 0x725a, 0x72cf, 0x72b9,
  0x72b7, 0x72e9, 0x730f, 0x72fa, 0x7344, 0x732e,
  /* 0x16F21..0x16F7E */
  0x7319, 0x7322, 0x731a, 0x7323, 0x733a, 0x7335, 0x733b, 0x735c,
  0x7360, 0x737c, 0x736e, 0x7356, 0x73b0, 0x73ac, 0x73ad, 0x7394,
  0x73b9, 0x73d6, 0x73d7, 0x73e8, 0x73e5, 0x73d8, 0x73c3, 0x73dd,
  0x73d0, 0x73c8, 0x73e4, 0x741a, 0x7414, 0x7413, 0x7403, 0x7407,
  0x7410, 0x7436, 0x742b, 0x7435, 0x7421, 0x743a, 0x7441, 0x7452,
  0x7444, 0x745b, 0x7460, 0x7462, 0x745e, 0x746a, 0x7229, 0x7470,
  0x7475, 0x7477, 0x747d, 0x745a, 0x747c, 0x747e, 0x7481, 0x747f,
  0x7582, 0x7587, 0x758a, 0x7594, 0x7596, 0x7598, 0x7599, 0x75a0,
  0x75a8, 0x75a7, 0x75ad, 0x75bc, 0x75bb, 0x75b9, 0x75be, 0x75ca,
  0x4ff6, 0x75c3, 0x75cd, 0x75cc, 0x75d5, 0x75d4, 0x75d6, 0x75dc,
  0x75e1, 0x75e5, 0x75e2, 0x7621, 0x7628, 0x762e, 0x762f, 0x7642,
  0x764c, 0x764f, 0x764b, 0x7677, 0x765c, 0x765e,
  /* 0x17021..0x1707E */
  0x765d, 0x765f, 0x7666, 0x7672, 0x766c, 0x768d, 0x7698, 0x7695,
  0x7697, 0x76aa, 0x76a7, 0x76b1, 0x76b2, 0x76b0, 0x76b4, 0x76b6,
  0x76b8, 0x76b9, 0x76ce, 0x76cb, 0x76c9, 0x76cd, 0x694d, 0x76dc,
  0x770d, 0x76d5, 0x76f9, 0x7704, 0x7706, 0x7708, 0x7713, 0x770e,
  0x7711, 0x770f, 0x7716, 0x7719, 0x7724, 0x772a, 0x7730, 0x7739,
  0x773d, 0x773e, 0x7744, 0x7746, 0x7748, 0x7742, 0x7749, 0x775c,
  0x7760, 0x7764, 0x7766, 0x7768, 0x32d2, 0x776b, 0x7771, 0x7779,
  0x7785, 0x777c, 0x7781, 0x777a, 0x7786, 0x778b, 0x778f, 0x7790,
  0x779c, 0x77a8, 0x77a6, 0x77a3, 0x77b3, 0x77b4, 0x77c3, 0x77c6,
  0x77c8, 0x77cb, 0x77dc, 0x77ed, 0x7f4f, 0x77f2, 0x5adf, 0x77f6,
  0x77f5, 0x780f, 0x780c, 0x7838, 0x7824, 0x7821, 0x7837, 0x783d,
  0x7846, 0x784f, 0x784b, 0x786b, 0x786f, 0x7870,
  /* 0x17121..0x1717E */
  0x7871, 0x7874, 0x7873, 0x78aa, 0x78af, 0x78b1, 0x78b6, 0x78c4,
  0x78c3, 0x78c6, 0x78e9, 0x78eb, 0x7903, 0x7909, 0x7912, 0x7914,
  0x7918, 0x7921, 0x791d, 0x791e, 0x7924, 0x7920, 0x792c, 0x792e,
  0x793d, 0x793e, 0x7942, 0x7949, 0x7945, 0x7950, 0x794b, 0x7951,
  0x7952, 0x794c, 0x7955, 0x7997, 0x7998, 0x79a5, 0x79ad, 0x79ae,
  0x79bc, 0x79df, 0x79db, 0x79dd, 0x79d8, 0x79d1, 0x79ed, 0x79ee,
  0x79f1, 0x79f2, 0x79fb, 0x79f8, 0x7a01, 0x7a0f, 0x7a05, 0x79e2,
  0x7a19, 0x7a2b, 0x7a37, 0x7a45, 0x7a42, 0x7a40, 0x7a43, 0x7a3e,
  0x7a55, 0x7a4d, 0x7a5b, 0x7a57, 0x7a5f, 0x7a62, 0x7a65, 0x7a64,
  0x7a69, 0x7a6b, 0x7a6a, 0x7aad, 0x7ab0, 0x7abc, 0x7ac0, 0x7acf,
  0x7ad1, 0x7ad3, 0x7ad4, 0x7ade, 0x7adf, 0x7ae2, 0x7ae3, 0x7ae6,
  0x7aef, 0x7aeb, 0x7aee, 0x7af4, 0x7af1, 0x7af7,
  /* 0x17221..0x1727E */
  0x7afb, 0x7b06, 0x7b18, 0x7b1a, 0x7b1f, 0x7b22, 0x7b23, 0x7b25,
  0x7b27, 0x7b28, 0x7b29, 0x7b2a, 0x7b2e, 0x7b2f, 0x7b32, 0x7b44,
  0x7b43, 0x7b4f, 0x7b4d, 0x7b4e, 0x7b51, 0x7b58, 0x7b74, 0x7b93,
  0x7b83, 0x7b91, 0x7b96, 0x7b97, 0x7b9f, 0x7ba0, 0x7ba8, 0x7bb4,
  0x7bc0, 0x7bca, 0x7bb9, 0x7bc6, 0x7bcf, 0x7bd1, 0x7bd2, 0x7be3,
  0x7be2, 0x7be4, 0x7bd4, 0x7be1, 0x7c3a, 0x7bf2, 0x7bf1, 0x7bf0,
  0x7c15, 0x7c14, 0x7c09, 0x7c13, 0x7c0c, 0x7c06, 0x7c08, 0x7c12,
  0x7c0a, 0x7c04, 0x7c2e, 0x7c1b, 0x7c25, 0x7c24, 0x7c21, 0x7c30,
  0x7c47, 0x7c32, 0x7c46, 0x7c3e, 0x7c5a, 0x7c60, 0x7c67, 0x7c76,
  0x7c78, 0x7ce7, 0x7cec, 0x7cf0, 0x7d09, 0x7d08, 0x7ceb, 0x7d03,
  0x7d06, 0x7d2a, 0x7d26, 0x7daf, 0x7d23, 0x7d1f, 0x7d44, 0x7d15,
  0x7d12, 0x7d41, 0x7d3f, 0x7d3e, 0x7d46, 0x7d48,
  /* 0x17321..0x1737E */
  0x7d5d, 0x7d5e, 0x7d64, 0x7d51, 0x7d50, 0x7d59, 0x7d72, 0x7d89,
  0x7d87, 0x7dab, 0x7d6f, 0x7d7a, 0x7d9a, 0x7da4, 0x7da9, 0x7db2,
  0x7dc4, 0x7dc1, 0x7dbb, 0x7db8, 0x7dba, 0x7dc6, 0x7dcf, 0x7dc2,
  0x7dd9, 0x7dd3, 0x7df8, 0x7de6, 0x7ded, 0x7def, 0x7dfd, 0x7e1a,
  0x7e1b, 0x7e1e, 0x7e75, 0x7e79, 0x7e7d, 0x7e81, 0x7e88, 0x7e8b,
  0x7e8c, 0x7e92, 0x7e95, 0x7e91, 0x7e9d, 0x7ea5, 0x7ea9, 0x7eb8,
  0x7eaa, 0x7ead, 0x7761, 0x7ecc, 0x7ece, 0x7ecf, 0x7ed0, 0x7ed4,
  0x7edc, 0x7ede, 0x7edd, 0x7ee0, 0x7ee5, 0x7ee8, 0x7eef, 0x7ef4,
  0x7ef6, 0x7ef7, 0x7ef9, 0x7efb, 0x7efc, 0x7efd, 0x7f07, 0x7f08,
  0x56b7, 0x7f15, 0x7f21, 0x7f2c, 0x7f3e, 0x7f4a, 0x7f52, 0x7f54,
  0x7f63, 0x7f5f, 0x7f60, 0x7f61, 0x7f66, 0x7f67, 0x7f6c, 0x7f6a,
  0x7f77, 0x7f72, 0x7f76, 0x7f95, 0x7f9c, 0x7fa0,
  /* 0x17421..0x1747E */
  0x382f, 0x49c7, 0x7059, 0x5464, 0x31dc, 0x5199, 0x3653, 0x3de2,
  0x3e14, 0x3e18, 0x3e58, 0x3e5e, 0x3ebe, 0x8028, 0x3ecb, 0x3ef9,
  0x3f00, 0x3f02, 0x3f07, 0x3f1d, 0x3f23, 0x3f34, 0x3f36, 0x3f3d,
  0x3f40, 0x3f45, 0x3f54, 0x3f58, 0x3f64, 0x3f67, 0x3f7d, 0x3f89,
  0x3f9c, 0x3fa7, 0x3faf, 0x3fb5, 0x3fb7, 0x3fc9, 0x3fde, 0x3fe1,
  0x3fe9, 0x400d, 0x4014, 0x4018, 0x4033, 0x4035, 0x4047, 0x813d,
  0x409d, 0x409e, 0x40cb, 0x40d4, 0x40d5, 0x40dd, 0x40f8, 0x411c,
  0x412b, 0x4130, 0x4137, 0x813e, 0x418d, 0x813f, 0x41bc, 0x41b9,
  0x8140, 0x4222, 0x423e, 0x4243, 0x4256, 0x425a, 0x426f, 0x4285,
  0x42c4, 0x42d6, 0x42fc, 0x430a, 0x4318, 0x4339, 0x4343, 0x4365,
  0x437c, 0x43e5, 0x43ed, 0x43f5, 0x4410, 0x4414, 0x4422, 0x4479,
  0x4451, 0x4460, 0x446d, 0x44ce, 0x44be, 0x44bf,
  /* 0x17521..0x1757E */
  0x44c4, 0x44ca, 0x44d0, 0x44f7, 0x44fb, 0x4522, 0x4529, 0x8141,
  0x4567, 0x459d, 0x8142, 0x4600, 0x4609, 0x4615, 0x461e, 0x463a,
  0x4622, 0x4624, 0x462b, 0x4630, 0x4631, 0x4633, 0x46fb, 0x4648,
  0x464c, 0xa8c4, 0x4659, 0x465a, 0x4661, 0x4665, 0x4673, 0x4677,
  0x4678, 0x468d, 0x8143, 0x46a0, 0x46b2, 0x46bb, 0x46c6, 0x46c8,
  0x1b22, 0x46db, 0x46e8, 0x46fa, 0x4713, 0x8029, 0x4733, 0x4766,
  0x4747, 0x4748, 0x477b, 0x4781, 0x4793, 0x4798, 0x479b, 0x47bb,
  0x47f9, 0x47c0, 0x47d7, 0x47fc, 0x4801, 0x4852, 0x481d, 0x482c,
  0x4831, 0x485b, 0x4872, 0x4875, 0x8144, 0x48a3, 0x48a5, 0x48b2,
  0x48c8, 0x48d0, 0x48e8, 0x48ed, 0x48f0, 0x48f1, 0x48fc, 0x490a,
  0x4949, 0xabc4, 0x4935, 0x4942, 0x4957, 0x4963, 0x4964, 0x4968,
  0x4980, 0x8114, 0x49a5, 0x49ad, 0x49cf, 0x1bb6,
  /* 0x17621..0x1767E */
  0x1bc3, 0x49e2, 0x49e9, 0x49ea, 0x49f5, 0x49f6, 0x4a0f, 0x4a15,
  0xad3f, 0x4a3b, 0x4a3e, 0x4a45, 0x4a50, 0x4a56, 0x4a5b, 0x4a6b,
  0x4a73, 0xad63, 0x4a89, 0x4a94, 0x4a9d, 0x4a9e, 0x4aa5, 0x4ae4,
  0x4ae7, 0x1c0f, 0x801d, 0x4b1b, 0x4b1e, 0x4b2c, 0x4b35, 0x4b46,
  0x4b56, 0x4b60, 0x4b65, 0x4b67, 0x4b77, 0x4b82, 0x4ba9, 0x4bad,
  0x8070, 0x4bcf, 0x4bd6, 0x4bd7, 0x4bff, 0x4c05, 0x4c10, 0x4c33,
  0x4c59, 0x4c5c, 0x4caa, 0x4c74, 0x4c76, 0x4c85, 0x4c86, 0x4c98,
  0x4c9c, 0x4cfb, 0x4cc6, 0x4cd4, 0x4ce0, 0x4ceb, 0x4cee, 0xb0fe,
  0x4d04, 0x4d0e, 0x4d2e, 0x4d31, 0x4d39, 0x4d3f, 0x4d58, 0x4d65,
  0x8145, 0x4d82, 0x4d87, 0x4d89, 0x4d94, 0x4daa, 0x4dac, 0x4dbf,
  0x4dc4, 0x4dd6, 0x4dda, 0x4ddb, 0x4ddd, 0x4dfc, 0x8146, 0x4e34,
  0x4e44, 0x4e5c, 0x4e5e, 0x4eab, 0x4eb1, 0x4ec1,
  /* 0x17721..0x1777E */
  0x4ec7, 0x4ece, 0x4f10, 0x4f1a, 0x8147, 0x4f2a, 0x4f2f, 0x4f33,
  0x4f51, 0x4f59, 0x4f5e, 0x4f61, 0x4f62, 0x4f7e, 0x4f88, 0x4f8c,
  0x4f8d, 0x4f94, 0x4fa0, 0x4fa7, 0x4fb6, 0x4fbc, 0x4fc7, 0x4fca,
  0x4ff9, 0x4ff0, 0x4ff5, 0x5005, 0x5006, 0x5028, 0x504a, 0x505d,
  0x505e, 0x504e, 0x5064, 0x5075, 0x5085, 0x50a4, 0x50ab, 0x50b7,
  0x50d4, 0x50d8, 0x50e4, 0x510f, 0x512b, 0x511e, 0x5120, 0x512e,
  0x5130, 0x5146, 0x5147, 0x5151, 0x8148, 0x5152, 0x515c, 0x5160,
  0x5168, 0x8115, 0x5185, 0x5187, 0x5192, 0x51c1, 0x51ba, 0x51c4,
  0x51fe, 0x5200, 0x5215, 0x5255, 0x5256, 0x1e3f, 0x528d, 0x529b,
  0x52be, 0x52c0, 0x52fb, 0xb7f1, 0x5327, 0x5328, 0x8116, 0x5350,
  0x5366, 0x537c, 0x5395, 0x539f, 0x53a0, 0x53a2, 0x53a6, 0x53ab,
  0x53c9, 0x53cf, 0x53d6, 0x53d9, 0x53e3, 0x53e9,
  /* 0x17821..0x1787E */
  0x5407, 0x540a, 0x541a, 0x541b, 0x814a, 0x5426, 0x5428, 0x542a,
  0x542b, 0x542c, 0x542e, 0x542f, 0x5430, 0x5444, 0x5446, 0x5447,
  0x544b, 0x5457, 0x5462, 0x546b, 0x546d, 0x5486, 0x5487, 0x5489,
  0x5498, 0x549c, 0x549f, 0x54a3, 0x5490, 0x54a6, 0x54a8, 0x54a9,
  0x54b5, 0x54bf, 0x54c8, 0x54c9, 0x54da, 0x54ff, 0x5501, 0x5517,
  0x552f, 0x556f, 0x5579, 0x5592, 0x1f72, 0x55ce, 0x55e4, 0x5600,
  0x5602, 0x5608, 0x5615, 0x5616, 0x5619, 0x561e, 0x562d, 0x5635,
  0x5643, 0x564b, 0x5664, 0x5665, 0x566d, 0x566f, 0x5671, 0x5681,
  0x569b, 0x569d, 0x569e, 0x56a6, 0x56aa, 0x56b6, 0x56c5, 0x56cc,
  0x56ce, 0x56d4, 0x56e6, 0x56f1, 0x56fc, 0x570a, 0x5719, 0x5734,
  0x5736, 0x5746, 0x574d, 0x574e, 0x575c, 0x575f, 0x5762, 0x577a,
  0x5780, 0x5794, 0x57aa, 0x57e0, 0x582d, 0xc18e,
  /* 0x17921..0x1797E */
  0x5843, 0x584e, 0x584f, 0x5851, 0x5868, 0x586e, 0x814b, 0x58b0,
  0xc20e, 0x58ad, 0x58e4, 0x58f2, 0x5900, 0x58f7, 0x591c, 0x592e,
  0x5931, 0x5934, 0x814c, 0x814d, 0x5945, 0x5946, 0x814e, 0x814f,
  0x8150, 0x595c, 0x8151, 0x8119, 0x811a, 0x5979, 0x8152, 0x8153,
  0x811b, 0x5998, 0x59b1, 0x59b8, 0x59c8, 0x59ca, 0xc371, 0x59d4,
  0x59de, 0x59eb, 0x59ed, 0x5a03, 0x8154, 0x5a39, 0x5a5d, 0x5a6d,
  0x8155, 0x5a85, 0x5aa0, 0xc4c4, 0x5ab3, 0x5abb, 0x5ace, 0x5aeb,
  0x5afd, 0x5b12, 0x5b2d, 0x5b3b, 0x5b47, 0x5b4e, 0x5b60, 0x5b6d,
  0x5b6f, 0x5b72, 0x5b9e, 0x8156, 0x5bd7, 0x5bd9, 0x5c01, 0x5c31,
  0x5c1e, 0x5c20, 0x5c33, 0x5c36, 0x2264, 0xc7a1, 0x5c59, 0x5c6d,
  0x5c79, 0x5c8f, 0x5c94, 0x5ca0, 0x5cbc, 0x5cd5, 0x5cd9, 0x5cdd,
  0x5d07, 0x5d08, 0x5d13, 0x5d1d, 0x5d23, 0x5d31,
  /* 0x17A21..0x17A7E */
  0x5d41, 0x5d48, 0x5d53, 0x5d5c, 0x5d7a, 0x5d83, 0x5d8b, 0x5da0,
  0x5da6, 0x5dc2, 0x5dcc, 0x5dd6, 0x5de3, 0x8157, 0x5e28, 0x5e08,
  0x5e11, 0x5e15, 0x8159, 0x5e47, 0x5e52, 0x5e61, 0x5e8a, 0x5e8d,
  0x5f47, 0x815a, 0x5f91, 0x5f97, 0x5fbf, 0x5fce, 0x5fdb, 0x5fdf,
  0x5fec, 0x5fee, 0x5ffa, 0x815b, 0x6014, 0x6026, 0x6035, 0x6037,
  0x603c, 0x60ca, 0x60d7, 0x60e0, 0x60f3, 0x6118, 0x614a, 0x6160,
  0x6167, 0x6168, 0x616d, 0x61bb, 0x61ca, 0x61cf, 0x61d7, 0x815c,
  0x2453, 0x245b, 0x6260, 0x6274, 0xd2ff, 0x628e, 0x62a1, 0x62a3,
  0x62a4, 0x62a9, 0x62ae, 0x62b7, 0x62be, 0x62bf, 0x62c6, 0x62d5,
  0x62fd, 0x62fe, 0x6300, 0x6301, 0x6362, 0x6322, 0x632d, 0x633a,
  0x6343, 0x6347, 0x6351, 0x6355, 0x637d, 0x6386, 0x6392, 0x6398,
  0x63a7, 0x63a9, 0x63bf, 0x63c0, 0x63c7, 0x63cf,
  /* 0x17B21..0x17B7E */
  0x63d1, 0x63e1, 0x63ea, 0x6401, 0x6406, 0x640a, 0x815f, 0x6448,
  0x645f, 0x6470, 0x6473, 0x6485, 0x649e, 0x64af, 0x64b4, 0x64ba,
  0x64c0, 0x64c2, 0xd440, 0x6532, 0x651e, 0x6523, 0x652f, 0x6559,
  0x6564, 0x811f, 0x65ad, 0x657a, 0x658c, 0x658f, 0x65a2, 0x65b0,
  0x65cb, 0x65ce, 0x65ed, 0x6612, 0x65ff, 0x6604, 0x6605, 0x6610,
  0xd674, 0x6618, 0x6629, 0x6638, 0x6657, 0x665b, 0x8036, 0x6662,
  0x259d, 0x666c, 0x6675, 0x6698, 0x66b8, 0x66fa, 0x66fc, 0x66fd,
  0x670b, 0x6771, 0x6787, 0x6788, 0x67ac, 0x67ad, 0x67b5, 0x25ea,
  0x67d6, 0x67ec, 0x6806, 0x680a, 0x6810, 0x6814, 0x681f, 0x6898,
  0x68aa, 0x68ca, 0x68ce, 0xd884, 0x68f5, 0x691c, 0x8160, 0x6918,
  0x6919, 0x691a, 0x6927, 0x6930, 0x6932, 0x6939, 0x6940, 0x6994,
  0x8161, 0x69d4, 0x69e5, 0x69f6, 0x6a12, 0x6a15,
  /* 0x17C21..0x17C7E */
  0x6a22, 0x6a37, 0x6a47, 0x6a4e, 0x6a5d, 0x6a61, 0x6a75, 0x6a79,
  0x6aa7, 0x6ad0, 0x6adf, 0x6af4, 0x6af6, 0x8122, 0x8162, 0x8163,
  0x6b46, 0x6b54, 0x6b59, 0x6b69, 0x6b9d, 0x6c49, 0x6c68, 0x8164,
  0x6ce1, 0x6cf4, 0x6cf8, 0x6cfe, 0x8165, 0x6d12, 0x6d1b, 0x6daf,
  0x6dce, 0x6dd1, 0x6dd7, 0x6e20, 0x6e23, 0x6e3d, 0x6e70, 0x6e7b,
  0xe177, 0x6ec0, 0x2844, 0x6efa, 0x6f1e, 0x6f2d, 0x6f36, 0x6f54,
  0xe24d, 0x6fa6, 0x6fb5, 0x6fe4, 0x6fe8, 0x6fee, 0x7008, 0x702d,
  0x8167, 0x7088, 0x7095, 0x7097, 0x7099, 0x709b, 0x70a2, 0x70b3,
  0x70be, 0x70c4, 0x70c5, 0x70c7, 0x70d7, 0x70dd, 0x70de, 0x70ef,
  0x70f4, 0x8126, 0x7114, 0x7115, 0x7116, 0x7122, 0x7123, 0x7127,
  0x712f, 0x7131, 0x7134, 0x713d, 0x7148, 0x715b, 0x7183, 0x719e,
  0x71ac, 0x71b1, 0x71bc, 0x71d7, 0x71fb, 0x71e4,
  /* 0x17D21..0x17D7E */
  0x71e5, 0x71ed, 0x71f1, 0x7207, 0x7210, 0x7238, 0x7239, 0x723a,
  0x723c, 0x7240, 0x7243, 0x724f, 0x7278, 0x7288, 0x72c2, 0x72cb,
  0x72cc, 0x72d3, 0x72e0, 0x72ff, 0x7304, 0x731f, 0x7321, 0x7325,
  0x7348, 0x7349, 0x734a, 0x7364, 0x7365, 0x736a, 0x7370, 0x739b,
  0x73a3, 0x73ba, 0x73c6, 0x73de, 0x73df, 0x7404, 0x73fd, 0x7433,
  0x744a, 0x7463, 0x746b, 0x7471, 0x7472, 0x758e, 0x759f, 0x75a6,
  0x75a9, 0x75ac, 0x75b6, 0x75bd, 0x75cb, 0x75d0, 0x75d3, 0x29b0,
  0x75da, 0x75de, 0x7658, 0x7684, 0x80dc, 0x769d, 0x76a4, 0x76a5,
  0x76d2, 0x76de, 0x8168, 0x76e9, 0x76ef, 0x7733, 0x773b, 0x774d,
  0x774e, 0x774f, 0x775a, 0x776e, 0x7773, 0x7795, 0x77ae, 0x77ba,
  0x77c1, 0x77c9, 0x77de, 0x77db, 0x77f4, 0x8169, 0x780a, 0x781e,
  0x782b, 0x7830, 0x816a, 0x7852, 0x7853, 0x7856,
  /* 0x17E21..0x17E7E */
  0x7857, 0x7859, 0x785a, 0x80d0, 0x7865, 0x786c, 0x78ba, 0x78c8,
  0x78e7, 0x7958, 0x799e, 0x7a02, 0x7a03, 0x7a24, 0x7a2d, 0x7a2e,
  0x7a38, 0x7a4a, 0x7a4e, 0x7a52, 0x7ab6, 0x7ac1, 0x7ac3, 0x7ace,
  0x7ad6, 0x7af9, 0x7b02, 0x7b08, 0x7b20, 0x2c17, 0x7b2d, 0x7b5e,
  0x7b79, 0x7b66, 0x7b72, 0x7b75, 0x7b84, 0x7b8a, 0x7b8f, 0x7b9e,
  0x7ba7, 0x7bc1, 0x7bce, 0x7be5, 0x7bf8, 0x7bfd, 0x7c00, 0x7c23,
  0x7c41, 0x7c4f, 0x7c50, 0x7c53, 0x7c63, 0x7c65, 0x7c77, 0x7d1d,
  0x7d1e, 0x7d43, 0x7d47, 0x7d52, 0x7d63, 0x7d70, 0x7d7c, 0x7d8a,
  0x7d96, 0x7dc0, 0x7dac, 0x7dbc, 0x7dd7, 0xf690, 0x7de7, 0x7e07,
  0x7e15, 0x7e7c, 0x7e9e, 0x7ea4, 0x7eac, 0x7eaf, 0x7eb4, 0x7eb5,
  0x7ec3, 0x7ed1, 0x7f10, 0x7f39, 0x7f57, 0x7f90, 0x7f94, 0x7f97,
  0x7fa2, 0x39f8, 0x3c5b, 0x3e77, 0x5626, 0x5e6b,
  /* 0x22121..0x2217E */
  0x8489, 0x2e02, 0x2e0f, 0x2e12, 0x2e29, 0x2e2b, 0x2e2e, 0x2e40,
  0x2e47, 0x2e48, 0x84a2, 0x2e51, 0x1406, 0x84a4, 0x2e5a, 0x2e69,
  0x2e9d, 0x142c, 0x142e, 0x2eb9, 0x2ebb, 0x8522, 0x2ebc, 0x2ec3,
  0x2ec8, 0x2ed0, 0x2eeb, 0x2eda, 0x2ef1, 0x2ef5, 0x2f00, 0x2f16,
  0x2f64, 0x2f37, 0x2f3e, 0x2f54, 0x2f58, 0x8593, 0x2f77, 0x2f78,
  0x2f7a, 0x2f7d, 0x2f82, 0x2f85, 0x2f92, 0x2f9a, 0x2fe6, 0x2fb2,
  0x2fbe, 0x2fc5, 0x2fcb, 0x2fcf, 0x2fd2, 0x146a, 0x2ff2, 0x3000,
  0x3010, 0x3013, 0x301c, 0x301e, 0x3022, 0x1468, 0x3042, 0x3046,
  0x304e, 0x3053, 0x3057, 0x3063, 0x3066, 0x306a, 0x3070, 0x30a3,
  0x3088, 0x3092, 0x3093, 0x3095, 0x3096, 0x309c, 0x30aa, 0x862b,
  0x30b1, 0x30ba, 0x30bb, 0x30c4, 0x30c7, 0x30f3, 0x8681, 0x30ce,
  0x8671, 0x30d4, 0x30d9, 0x30e1, 0x30e9, 0x1492,
  /* 0x22321..0x2237E */
  0x3108, 0x86f9, 0x3117, 0x311b, 0x874a, 0x3160, 0x8809, 0x3173,
  0x3183, 0x318b, 0x14bc, 0x3198, 0x31a3, 0x31ad, 0x14c7, 0x31bc,
  0x88d6, 0x8928, 0x31f3, 0x31f4, 0x3202, 0x3212, 0x3216, 0x8a4f,
  0x3255, 0x325c, 0x326c, 0x3277, 0x3284, 0x3282, 0x8b07, 0x3298,
  0x8b3a, 0x32a4, 0x32a6, 0x32af, 0x32ba, 0x32bb, 0x32ca, 0x151f,
  0x32d1, 0x8bb9, 0x32f7, 0x330a, 0x330b, 0x3324, 0x3335, 0x333e,
  0x3342, 0x8c7c, 0x8c9d, 0x3367, 0x336c, 0x337a, 0x33a4, 0x33b4,
  0x8dd3, 0x33b7, 0x33c0, 0x8e1d, 0x155d, 0x155e, 0x33d5, 0x33da,
  0x1563, 0x33f4, 0x33f5, 0x3455, 0x3424, 0x3428, 0x156e, 0x3443,
  0x3462, 0x3466, 0x346c, 0x348a, 0x348d, 0x3495, 0x34a0, 0x34a6,
  0x34ad, 0x34ae, 0x34b7, 0x34ba, 0x34bf, 0x34c3, 0x8f45, 0x34ec,
  0x34ef, 0x34f1, 0x34f3, 0x3500, 0x3501, 0x3509,
  /* 0x22421..0x2247E */
  0x353c, 0x3541, 0x15a6, 0x3547, 0x354a, 0x15a8, 0x3560, 0x3561,
  0x3564, 0x8fe1, 0x357d, 0x3582, 0x3588, 0x3591, 0x15c5, 0x35d2,
  0x9095, 0x906d, 0x35bf, 0x35c9, 0x35cc, 0x35d1, 0x35dd, 0x15da,
  0x35e2, 0x9064, 0x35e9, 0x3628, 0x915f, 0x3607, 0x3610, 0x3630,
  0x3637, 0x15f4, 0x363d, 0x363f, 0x3640, 0x3647, 0x365e, 0x3660,
  0x366d, 0x1605, 0x3688, 0x368c, 0x3695, 0x369a, 0x369d, 0x36a8,
  0x36ad, 0x36b2, 0x36c5, 0x36cd, 0x36df, 0x36e8, 0x36f6, 0x36f7,
  0x9201, 0x3715, 0x3723, 0x9255, 0x3729, 0x927b, 0x3745, 0x3746,
  0x374c, 0x374d, 0x9274, 0x3768, 0x376f, 0x3773, 0x3774, 0x3775,
  0x377b, 0x92e4, 0x92d7, 0x37ac, 0x379a, 0x379d, 0x379e, 0x37a8,
  0x37d7, 0x92fd, 0x37cc, 0x9336, 0x9344, 0x37de, 0x37e6, 0x37f0,
  0x164a, 0x37f8, 0x37fb, 0x37fd, 0x3804, 0x381e,
  /* 0x22521..0x2257E */
  0x3820, 0x3827, 0x3832, 0x3839, 0x93c4, 0x3849, 0x384c, 0x3867,
  0x388a, 0x388b, 0x388d, 0x388f, 0x3890, 0x3894, 0x389d, 0x38aa,
  0x38b1, 0x946d, 0x38c3, 0x38cd, 0x38e2, 0x38f3, 0x38f4, 0x3905,
  0x3906, 0x390b, 0x390d, 0x3914, 0x3924, 0x95d7, 0x1691, 0x393d,
  0x1699, 0x3946, 0x1696, 0xd329, 0x395b, 0x395f, 0x9647, 0x3975,
  0x3976, 0x397c, 0x399f, 0x39ae, 0x39bc, 0x39c8, 0x39cd, 0x39de,
  0x39e3, 0x39e4, 0x39e7, 0x39ee, 0x9706, 0x9742, 0x16cf, 0x3a0c,
  0x3a0d, 0x3a17, 0x3a27, 0x3a2d, 0x3a55, 0x3a65, 0x3a7a, 0x3a8b,
  0x3a9c, 0x3a9f, 0x3aa0, 0x3aa2, 0x3ab1, 0x3ab3, 0x3ab5, 0x3aba,
  0x3abf, 0x3ada, 0x3adc, 0x3ae0, 0x3ae5, 0x3af0, 0x3aee, 0x3af5,
  0x3b00, 0x3b08, 0x3b17, 0x3b34, 0x3b2d, 0x3b4c, 0x3b52, 0x3b68,
  0x3b6f, 0x3b7c, 0x3b7f, 0x3b81, 0x3b84, 0x99c3,
  /* 0x22821..0x2287E */
  0x3b96, 0x3bac, 0x1761, 0x3bc0, 0x1762, 0x3bce, 0x3bd6, 0x176c,
  0x176b, 0x3bf1, 0x3bfd, 0x1775, 0x3c03, 0x3c29, 0x3c30, 0x9a56,
  0x3c5f, 0x3c63, 0x3c67, 0x3c68, 0x3c69, 0x3c70, 0x9b2d, 0x9b45,
  0x3c7c, 0x9b78, 0x9b62, 0x3c88, 0x3c8a, 0x17c1, 0x9ba1, 0x9b9c,
  0x3ca0, 0x3ca2, 0x3ca6, 0x3ca7, 0x9b92, 0x3cad, 0x3cb5, 0x9bb7,
  0x3cc9, 0x9be0, 0x9c33, 0x3d06, 0x3d10, 0x3d2b, 0x3d1d, 0x3d20,
  0x3d24, 0x3d26, 0x3d31, 0x3d39, 0x3d42, 0x17e8, 0x3d61, 0x3d6a,
  0x17f4, 0x3d70, 0x9d1e, 0x17fd, 0x3d88, 0x1800, 0x3d92, 0x3d94,
  0x3d97, 0x3d99, 0x3db0, 0x3db2, 0x3db4, 0x9d76, 0x3db9, 0x3dd1,
  0x3dd7, 0x3dd8, 0x3de0, 0x9dfa, 0x3de4, 0x3de9, 0x182f, 0x3e00,
  0x1836, 0x3e12, 0x3e15, 0x1840, 0x3e1f, 0x3e2e, 0x3e3e, 0x3e49,
  0x185c, 0x3e56, 0x1861, 0x3e6b, 0x3e6c, 0x3e6d,
  /* 0x22C21..0x22C7E */
  0x3e6e, 0x9e7b, 0x3ea5, 0x3eaa, 0x3eac, 0x3eb9, 0x3ebf, 0x3ec6,
  0x3ed2, 0x3ed9, 0xa01e, 0x3efd, 0x3f08, 0x3f0e, 0x3f1c, 0xa0ad,
  0x3f1e, 0x3f47, 0x3f63, 0x3f72, 0x3f7e, 0x3f8f, 0x3fa2, 0x3fa4,
  0x3fb8, 0x3fc4, 0x18fa, 0x3fc7, 0x3fcb, 0x3fd2, 0x3fd3, 0x3fd4,
  0x3fe2, 0x3fee, 0x3fef, 0x3ff3, 0x3ffc, 0x1917, 0x4017, 0x4022,
  0x4024, 0x191a, 0x404c, 0x407f, 0x408a, 0x4095, 0x40a8, 0xa1f3,
  0x40b0, 0x40b1, 0x40be, 0x40c8, 0x40d9, 0x40db, 0x40ee, 0x40f2,
  0x40f5, 0x4110, 0x4112, 0x4113, 0x4119, 0x411e, 0x413a, 0x196f,
  0x4141, 0x4146, 0x4160, 0x417c, 0xa25b, 0x4192, 0x4193, 0x4197,
  0x4198, 0x41a5, 0x41a8, 0x41ad, 0xa2ab, 0x41d5, 0x41dd, 0x41df,
  0x41f5, 0xa38f, 0x4215, 0x4223, 0x4229, 0x4246, 0x424c, 0x4251,
  0x4252, 0x4261, 0x4264, 0x427b, 0x426d, 0x4273,
  /* 0x22D21..0x22D7E */
  0x4299, 0x42a6, 0x42d5, 0xa4b8, 0x42fd, 0x4303, 0x430d, 0x4310,
  0xa54f, 0xa550, 0x4332, 0x4335, 0x433b, 0x433c, 0x4341, 0x4344,
  0x434e, 0xa546, 0x4359, 0xa61d, 0xa5a6, 0x436c, 0x4384, 0x4399,
  0xa624, 0x4394, 0x43bd, 0x43f7, 0x43d4, 0x43d5, 0x43dc, 0x43e0,
  0x43eb, 0x43ec, 0x43f2, 0x4409, 0x441e, 0x4425, 0x4429, 0x442f,
  0x445a, 0x445b, 0x445d, 0x4473, 0x447d, 0x4487, 0x4491, 0x449d,
  0x449f, 0x44cb, 0x44cc, 0x44d5, 0x44d7, 0xa7e1, 0x44e4, 0x44e5,
  0x44ff, 0x4504, 0x1a6e, 0x450f, 0x4514, 0x4516, 0x1a73, 0x451e,
  0x4532, 0x4544, 0x4554, 0x456b, 0x457a, 0x4581, 0x4584, 0x4585,
  0x458a, 0x45b2, 0x45b5, 0x45b8, 0x45bf, 0x45c2, 0x45c9, 0x45d4,
  0x1ad6, 0x45f2, 0x45f9, 0x45fc, 0x4604, 0x4608, 0x4621, 0x462a,
  0x4645, 0x4651, 0x464e, 0x1aea, 0xa8c3, 0x4657,
  /* 0x22E21..0x22E7E */
  0x465b, 0x4663, 0xa8f5, 0xa8b6, 0x466a, 0x466b, 0x466c, 0x466d,
  0x467b, 0x4680, 0x4690, 0x4692, 0x4699, 0x1b0e, 0x46ad, 0x46b1,
  0x46b5, 0x1b1a, 0x46bf, 0x1b1c, 0x46ec, 0x1ad7, 0x4701, 0x4705,
  0x4712, 0xa972, 0x4719, 0xa9d3, 0xa9d2, 0x474c, 0x474d, 0x4754,
  0x475d, 0xa9d0, 0xa9e4, 0xa9d5, 0x4774, 0x4776, 0xa9da, 0x4792,
  0xa9df, 0x6363, 0x4810, 0x47b0, 0x47b2, 0x47c3, 0x47c8, 0x47d2,
  0x47d9, 0x47db, 0x47f0, 0x47f7, 0xaa4a, 0xaa51, 0xaa4b, 0x4818,
  0x481f, 0x482d, 0xaa65, 0x4833, 0x483b, 0x483e, 0x4844, 0x4845,
  0x4849, 0x484c, 0x4855, 0x4857, 0x1b77, 0x486b, 0x486e, 0x487a,
  0x487c, 0x4882, 0x4890, 0x4896, 0x1b6d, 0x4898, 0x4899, 0x489a,
  0x489c, 0x48aa, 0x48ab, 0x48b4, 0x48bb, 0x48fb, 0xaae4, 0xab5a,
  0x8113, 0x48c3, 0x48c5, 0x48cc, 0x48cf, 0x48d6,
  /* 0x22F21..0x22F7E */
  0x48d9, 0x48e4, 0x48e5, 0x48ec, 0x48f7, 0x4903, 0x4907, 0x1b87,
  0x1b88, 0xab94, 0x493b, 0x1b8d, 0x4946, 0x4969, 0x496c, 0x4972,
  0x497a, 0x497f, 0x4992, 0x1ba4, 0x4996, 0x4998, 0x49a6, 0x49b0,
  0x49b7, 0x49ba, 0x49bc, 0x49c0, 0x49d1, 0x49d6, 0xac39, 0xac47,
  0x4a30, 0xac38, 0xac3a, 0x49e3, 0x49ee, 0x49ef, 0x49f3, 0x1bcd,
  0x49f4, 0x49fe, 0x4a11, 0x4a1a, 0x4a1d, 0xad1c, 0x4a32, 0x4a33,
  0x4a34, 0x4a3f, 0x4a46, 0x4a49, 0x4a7a, 0x4a4e, 0x4a52, 0x4a64,
  0xad0c, 0x4a7e, 0x4a83, 0x4a8b, 0x1bf0, 0x4a91, 0x4a9f, 0x4aa1,
  0xad64, 0x4aab, 0x4abd, 0x4ac6, 0x4ad4, 0x4ad0, 0x4adc, 0x4add,
  0xadff, 0xade7, 0x4aec, 0x4af1, 0x4af2, 0x4af3, 0x4afd, 0xae24,
  0x4b0b, 0x4b0f, 0x4b10, 0x4b11, 0xae3d, 0x4b17, 0x1c26, 0x4b2f,
  0x4b4a, 0x4b58, 0x4b6c, 0x4b75, 0x4b7a, 0x4b81,
  /* 0x26E21..0x26E7E */
  0x4b9b, 0x4bae, 0xaf98, 0x4bbd, 0x4bbe, 0x4bc7, 0x4bc8, 0x4bc9,
  0x4bda, 0x4be6, 0x4be7, 0x4bee, 0x4bf1, 0x4c02, 0x4c0a, 0x4c0e,
  0x4c35, 0x4c36, 0x4c3a, 0xb07f, 0x4c3f, 0x4c4d, 0x4c5b, 0x4c6d,
  0x4c84, 0x4c89, 0x1cc3, 0x4c94, 0x4c95, 0x4c97, 0x4cad, 0x4cc2,
  0x4cd0, 0x1cd2, 0x4cd6, 0x4cda, 0x4cdc, 0x4ce9, 0x4cec, 0x4ced,
  0xb100, 0x4d00, 0x4d0a, 0x4d24, 0x4d26, 0x4d27, 0x4c67, 0x4d2f,
  0x4d3c, 0x4d5b, 0x4d5e, 0x4d60, 0x4d70, 0x4d80, 0x4d81, 0x4d8a,
  0x4d8d, 0x4d91, 0x4d98, 0xb140, 0x4e17, 0xb1fa, 0xb1f9, 0xb1d3,
  0x4dab, 0x4dae, 0x4db4, 0x4dc2, 0x4d34, 0x4dc8, 0x4dce, 0x4dcf,
  0x4dd0, 0x4ddf, 0x4de9, 0x4df6, 0x4e36, 0x4e1e, 0x4e22, 0x4e27,
  0x1d11, 0x4e32, 0x4e3c, 0x4e48, 0x4e49, 0x4e4b, 0x4e4c, 0x4e4f,
  0x4e51, 0x4e53, 0x4e54, 0x4e57, 0x4e63, 0x1d1e,
  /* 0x26F21..0x26F7E */
  0x4e93, 0x4ea7, 0x4eb4, 0x4ebf, 0x4ec3, 0x4eca, 0x4ed9, 0x4f35,
  0x4eeb, 0x4ef9, 0x4efb, 0x4f0a, 0x4f0c, 0x4f18, 0x4f25, 0x4f36,
  0x4f3c, 0xb27e, 0x4f52, 0x4f57, 0x4f5a, 0x4f60, 0x4f68, 0x4f98,
  0x4f7d, 0x4f90, 0x4f96, 0x4fbe, 0x4f9f, 0x4fa5, 0x4faf, 0x1d64,
  0x4fb5, 0x4fc8, 0x4fc9, 0x4fda, 0x4fde, 0x4fe9, 0xb396, 0x4ffc,
  0x5000, 0x5007, 0x500a, 0x5023, 0xb403, 0x5039, 0x503a, 0x503c,
  0x5043, 0x5047, 0x504b, 0x1d9a, 0x5054, 0x5065, 0x5069, 0x506c,
  0x506e, 0x5076, 0x507e, 0x5081, 0x5086, 0x5095, 0x5097, 0x50bb,
  0xb4c6, 0x509f, 0x50b1, 0xb4fe, 0x50ec, 0x50ca, 0x50d1, 0x50d3,
  0x50dc, 0x5103, 0x5104, 0x5106, 0x5107, 0x5108, 0x510c, 0x1dc0,
  0x512f, 0x5131, 0x5150, 0x514a, 0x5153, 0x515e, 0x1dd4, 0x5196,
  0x5180, 0x519b, 0x51a0, 0x51a2, 0x51ae, 0x51af,
  /* 0x27021..0x2707E */
  0x51b3, 0xb5bc, 0x51cb, 0x51d3, 0x51d9, 0x51dc, 0x5207, 0x1e05,
  0x8149, 0x522b, 0x5234, 0x5238, 0x5239, 0x2e2c, 0x5242, 0x5253,
  0x5257, 0x5263, 0xb629, 0x526e, 0x526f, 0x5278, 0x527f, 0x528e,
  0xb6a5, 0x52ad, 0x52ae, 0x52b0, 0x52b1, 0x52c1, 0x1e60, 0x52cc,
  0x1e66, 0x1e68, 0x52f3, 0x52fa, 0x5307, 0x5312, 0x5318, 0x5319,
  0x1e83, 0x5339, 0x532c, 0x5331, 0x5333, 0x533d, 0x5352, 0x1e94,
  0x536b, 0x536c, 0xb896, 0x536e, 0x536f, 0x5371, 0x5377, 0x5381,
  0x5385, 0x538a, 0x5394, 0x5398, 0x539c, 0x539e, 0x53a5, 0x53a8,
  0x53b5, 0x53b7, 0x53b9, 0x53bc, 0x53bf, 0x53c5, 0x53cb, 0x53e1,
  0x53e7, 0x53f9, 0x5413, 0x53fa, 0x5401, 0x5424, 0x5431, 0x5439,
  0x5453, 0x5440, 0x5443, 0x544d, 0x5452, 0x545d, 0x5471, 0x5481,
  0x5485, 0x5488, 0xb94d, 0x5492, 0x5497, 0x5499,
  /* 0x27121..0x2717E */
  0x54a0, 0x54a1, 0x54a5, 0x54aa, 0x54ab, 0x54b9, 0x54bb, 0x54ba,
  0x54d6, 0x54d8, 0x54de, 0x54ef, 0x54eb, 0xba56, 0x54fa, 0xba6f,
  0x5520, 0x5524, 0x552a, 0x1f57, 0xbb16, 0x553d, 0x553e, 0x5540,
  0x5548, 0x554e, 0x5550, 0x5552, 0x556c, 0x5572, 0x5571, 0x557a,
  0x557d, 0x557e, 0x5581, 0xbc14, 0x558c, 0x1f75, 0x55a2, 0x1f77,
  0x55b0, 0x55b7, 0x55bf, 0x55c0, 0x55c6, 0x55cf, 0x55d3, 0x55dd,
  0x55df, 0x55e0, 0x55e7, 0x55ec, 0x55ee, 0x55f1, 0x55f9, 0x5603,
  0x5618, 0x5607, 0x560f, 0x1fae, 0xbd0e, 0x5613, 0x561b, 0x561c,
  0xbd37, 0x5625, 0x5628, 0x563c, 0x5633, 0xbd6a, 0x1fc9, 0x5641,
  0xbd8b, 0x5649, 0x5655, 0x1fd7, 0x566e, 0x5695, 0x569c, 0x56a1,
  0x56a0, 0x56a7, 0x56a8, 0x56af, 0xbe4a, 0x56c9, 0xbe55, 0x56e8,
  0x56ec, 0xbf22, 0x5717, 0x571a, 0x572d, 0x5735,
  /* 0x27221..0x2727E */
  0xbfa9, 0x2039, 0xbfe5, 0xbfcd, 0x5758, 0x5760, 0x576a, 0xc01e,
  0x5772, 0x577c, 0x577d, 0xc04c, 0x2058, 0x579a, 0x579f, 0x57a2,
  0x57a4, 0x57a9, 0x57de, 0x57df, 0x57e4, 0x57e6, 0x57ea, 0x57ec,
  0x2093, 0x57f0, 0x57f4, 0x57fb, 0xc12e, 0x5805, 0x5806, 0x5809,
  0x580d, 0x5819, 0x5821, 0x582c, 0x5847, 0x5864, 0x586a, 0xc1d9,
  0x588a, 0x5894, 0x58a4, 0x589d, 0x589e, 0x589f, 0x58bb, 0x58c8,
  0x58cc, 0x58ce, 0x58d5, 0x58e0, 0x58e1, 0x58e6, 0x58f9, 0x58fa,
  0x58fb, 0x58fe, 0xc2a7, 0x5910, 0x591b, 0x5930, 0x5925, 0x593b,
  0x594a, 0x5958, 0x595b, 0x2105, 0x5967, 0x5972, 0x5994, 0x5995,
  0x5996, 0x599b, 0x59a1, 0x59a9, 0x59b4, 0x59bb, 0x59c2, 0x59c7,
  0x59cc, 0x59cd, 0x59d6, 0x2148, 0xc3a9, 0xc3b4, 0x214f, 0x5a0a,
  0x5a11, 0x5a15, 0x5a1b, 0x5a1e, 0x2163, 0x5a2d,
  /* 0x27321..0x2737E */
  0x5a38, 0x5a47, 0x5a4c, 0x5a56, 0x5a59, 0x5a5c, 0x5a5f, 0x5a60,
  0x5a67, 0x5a6a, 0x5a75, 0x5a78, 0x5a82, 0x5a8a, 0x5a90, 0x5aa3,
  0x5aac, 0xc4d4, 0x21b4, 0x5ab9, 0x5abc, 0x5abe, 0x21bf, 0x5acc,
  0x5ad1, 0x5ae7, 0x5ae8, 0x5af4, 0xc5e4, 0xc5e3, 0x5b07, 0xc5f1,
  0x5b3d, 0x5b27, 0x5b2a, 0x5b2e, 0x5b2f, 0x5b31, 0x21e6, 0x21f3,
  0x5b7f, 0x5b41, 0x21ee, 0x5b55, 0x5b79, 0x5b64, 0x5b66, 0x5b69,
  0x5b73, 0xc632, 0x2207, 0x5b90, 0x5b91, 0x5b9b, 0x220e, 0x5baf,
  0x5bb5, 0x5bbc, 0x5bc5, 0x5bca, 0xc6cb, 0xc6e4, 0x5bd4, 0x5bd6,
  0x5bda, 0x5bea, 0x5bf0, 0x5c03, 0x5c0b, 0x5c0e, 0x5c0f, 0x5c26,
  0x5c45, 0x5c4a, 0x5c51, 0x5c57, 0x5c5e, 0x5c61, 0x5c69, 0x5c6e,
  0x5c6f, 0x5c70, 0xc82e, 0xc856, 0xc865, 0x5ca6, 0xc862, 0x5cb6,
  0x5cb7, 0x5cbf, 0xc8d8, 0x5cc4, 0xc8c2, 0x5cc8,
  /* 0x27421..0x2747E */
  0x5ccd, 0xc8e8, 0x5cd7, 0xc923, 0x5ce6, 0x5ceb, 0xc95c, 0x5cf5,
  0x5d03, 0x5d09, 0x22c6, 0x5d12, 0x5d1e, 0xc9e0, 0xc9d4, 0x5d3d,
  0x5d3e, 0x5d40, 0x5d47, 0xca0c, 0xc9fb, 0x22d6, 0x5d59, 0x5d5a,
  0x5d6a, 0x5d70, 0x22dd, 0x5d7f, 0xca17, 0x5d86, 0x5d88, 0x5d8c,
  0x5d97, 0xca60, 0x5d9d, 0x5da7, 0x5daa, 0x5db6, 0x5db7, 0x5dc0,
  0x5dd7, 0x5dd9, 0x5de6, 0x5df1, 0x5df9, 0x2302, 0xcaed, 0x8158,
  0x5e10, 0x5e17, 0x5e1d, 0x5e20, 0x5e27, 0x5e2c, 0x5e45, 0x5e73,
  0x5e75, 0x5e7e, 0x5e86, 0x5e87, 0x232b, 0x5e91, 0x5e98, 0x5e9a,
  0x2343, 0x5f3c, 0x5f3b, 0x5f3e, 0x5f43, 0x5f44, 0x5f4f, 0x14c1,
  0xcb70, 0x5f52, 0xcb86, 0x5f61, 0x5f63, 0x5f64, 0x5f6d, 0x5f7d,
  0x5f7e, 0xcc4c, 0x5f90, 0x317b, 0xb10e, 0x5f96, 0x5f9c, 0x5fad,
  0xcd02, 0x5fc3, 0x5fcf, 0x5fe3, 0x5fe5, 0x5fef,
  /* 0x27521..0x2757E */
  0x5ff2, 0x6002, 0x600a, 0x6008, 0x600e, 0x6011, 0x6016, 0x6024,
  0x602c, 0x6030, 0x6043, 0x6066, 0x6071, 0x6075, 0x607b, 0x6099,
  0x609c, 0x60a4, 0x60a7, 0x60b8, 0xce7e, 0x60c5, 0x60d5, 0x60d8,
  0x60e6, 0xceb0, 0x610d, 0x60f5, 0x60fb, 0x23ee, 0x6135, 0x6116,
  0x611e, 0x23f0, 0x6124, 0x6127, 0x612c, 0xcf1d, 0x613d, 0x2408,
  0x6169, 0x2417, 0x6181, 0x241c, 0x6184, 0x6185, 0x2422, 0x6198,
  0x61b2, 0x61c1, 0x61c3, 0x61d6, 0x61db, 0xd0dd, 0x61e4, 0xd0ea,
  0x61ec, 0xd151, 0x61fd, 0x61ff, 0xd16f, 0x6204, 0xd1dd, 0x6219,
  0x6221, 0x6222, 0xd21e, 0x6232, 0x6234, 0x623c, 0x6246, 0x6249,
  0x6245, 0xd258, 0x624b, 0x2476, 0x624f, 0x247a, 0x6257, 0xd28c,
  0x625c, 0x6263, 0xd2b7, 0x815d, 0x815e, 0x6279, 0x2491, 0x627d,
  0x627f, 0x6283, 0x628a, 0x6293, 0x62a7, 0x62a8,
  /* 0x27621..0x2767E */
  0x62b2, 0x62b4, 0x62ba, 0x62bc, 0x62e2, 0x62e8, 0x62f7, 0x6307,
  0x6308, 0x630c, 0x6354, 0x631b, 0x631d, 0x6330, 0x633c, 0x6344,
  0x6357, 0x24be, 0x637f, 0x24d4, 0x24b3, 0x638d, 0x6394, 0x6395,
  0x639b, 0x639d, 0x63c9, 0x63d0, 0x63d4, 0x63dd, 0x63e5, 0x63f9,
  0x640f, 0x6411, 0x6415, 0xd373, 0x6417, 0x6439, 0x644a, 0x644f,
  0x6451, 0x6452, 0x6459, 0x645a, 0x645c, 0xd3dd, 0x6465, 0x6476,
  0x6478, 0x647c, 0x6481, 0x250d, 0x64dc, 0x6497, 0x64a6, 0x64be,
  0x2508, 0x64ce, 0x64cf, 0x64d3, 0xd465, 0x64e7, 0x64ea, 0x64ef,
  0x64f0, 0x64f1, 0x64fa, 0x64fd, 0x650c, 0x651b, 0x6524, 0x6525,
  0x652b, 0x6534, 0x654f, 0x656f, 0x2525, 0x2543, 0x653e, 0x6551,
  0x6553, 0x655e, 0x6561, 0x6562, 0xd594, 0x657b, 0x657d, 0x657f,
  0x6581, 0x6586, 0x6593, 0x659d, 0x659f, 0xd5f8,
  /* 0x27721..0x2777E */
  0xd5f6, 0xd5f7, 0x65b7, 0x65bc, 0x65c7, 0x65ca, 0x65d8, 0x65d9,
  0x65df, 0x65e1, 0x65e6, 0x65f6, 0x6600, 0x6611, 0x661e, 0x6621,
  0x6624, 0x6627, 0xd68d, 0x6639, 0x663c, 0xd6b9, 0x6640, 0x8120,
  0x6653, 0x6656, 0x666f, 0x6677, 0x667a, 0x6687, 0x6689, 0x668d,
  0x6691, 0x669c, 0x669d, 0x66a8, 0x8121, 0x66b1, 0x66b3, 0x66c1,
  0x66c3, 0x66d1, 0x66d5, 0x66d7, 0x66e3, 0x66e6, 0x25b8, 0x6705,
  0x6707, 0x670e, 0x6710, 0x6713, 0x6719, 0x671f, 0x6721, 0x6723,
  0x6731, 0x673a, 0x673e, 0x6740, 0x6743, 0x6751, 0x6758, 0x6764,
  0x6765, 0x6772, 0x677c, 0xd75b, 0xd75a, 0x67a7, 0x6789, 0x678b,
  0x6793, 0x67a0, 0xd77e, 0x25e5, 0x67be, 0xd790, 0x67c1, 0x67ce,
  0x67f5, 0x67df, 0xd7c9, 0x67e3, 0x67e5, 0x67e6, 0x67ea, 0x67eb,
  0x67ed, 0x6801, 0x6803, 0x680b, 0x6813, 0x6828,
  /* 0x27821..0x2787E */
  0x682e, 0x6832, 0x683c, 0x260f, 0x684a, 0x6858, 0x685f, 0x6864,
  0xd815, 0xd814, 0x6869, 0xd831, 0x686f, 0x68a0, 0x68bc, 0x68bd,
  0x68be, 0x68c0, 0x68d2, 0xd893, 0x68d1, 0x68d3, 0x68db, 0x68f0,
  0x68f1, 0x2641, 0x6901, 0xd90e, 0x6937, 0xd923, 0x6942, 0x6945,
  0x6949, 0xd952, 0x2665, 0x6962, 0x6980, 0x6989, 0x6990, 0x699f,
  0x69b0, 0x69b7, 0x69d6, 0x69d8, 0x69eb, 0x26a1, 0x69f1, 0x69f3,
  0x69fd, 0x69ff, 0x26af, 0x6a11, 0x6a14, 0xda85, 0x6a21, 0x6a35,
  0x6a3e, 0x6a45, 0x6a4d, 0x6a58, 0x6aae, 0x6a90, 0x6ab7, 0x6abe,
  0x6ad7, 0x6afc, 0xdb84, 0x6b0a, 0x6b05, 0x6b0d, 0x6b1c, 0x6b1f,
  0x6b2d, 0x6b43, 0x270c, 0x6b51, 0x6b5e, 0x6b76, 0x6b7f, 0x6b81,
  0x6b8b, 0x6b94, 0x6b95, 0x6b9c, 0x6b9e, 0x6c39, 0xdcb3, 0x6c3d,
  0xdcbe, 0xdcc7, 0x6c45, 0x6c47, 0x6c4f, 0x6c54,
  /* 0x27921..0x2797E */
  0x6c57, 0x6c69, 0x6c6d, 0x6c73, 0xddb8, 0x6c93, 0x6c92, 0x6c99,
  0x2764, 0x6c9b, 0x6ca4, 0x6cd6, 0x6cd5, 0x6cd9, 0xde20, 0x6cf0,
  0x6cf1, 0xde90, 0x6d09, 0x6d0e, 0x6d6c, 0x6d84, 0x6d95, 0x6da6,
  0xdfb7, 0x6dc6, 0x6dc8, 0x6dd9, 0x6dec, 0x6e0c, 0x27fd, 0x6dfd,
  0x6e06, 0xe08a, 0x6e14, 0x6e16, 0x6e21, 0x6e22, 0x6e27, 0xe0bb,
  0x2816, 0x6e36, 0x6e39, 0x6e4b, 0x6e54, 0x6e62, 0x6e6c, 0x6e6d,
  0x6e6f, 0x6e98, 0x6e9e, 0x6eae, 0x6eb3, 0x6eb5, 0x6eb6, 0x6ebb,
  0xe182, 0x6ed1, 0x6ed4, 0x284e, 0x6ef9, 0xe1f3, 0x6f00, 0x6f08,
  0x6f17, 0x6f2b, 0x6f40, 0x6f4a, 0x6f58, 0xe28c, 0x6fa4, 0x6fb4,
  0x8166, 0x6fb6, 0xe2d5, 0x6fc1, 0x6fc6, 0x8124, 0x6fca, 0x6fcd,
  0x6fd3, 0x6fd5, 0x6fe0, 0x6ff1, 0x6ff5, 0x6ffb, 0x7002, 0x700c,
  0x7037, 0xe36b, 0x7043, 0x7044, 0x705d, 0xe3c8,
  /* 0x27A21..0x27A7E */
  0xe3c9, 0x7085, 0x708c, 0x7090, 0x761d, 0x70a1, 0x28b5, 0x70b0,
  0x70b6, 0x70c3, 0x70c8, 0xe4d7, 0x70dc, 0x70df, 0xe4fa, 0x70f6,
  0x70f2, 0x7100, 0x70eb, 0x70fe, 0x70ff, 0x7104, 0x7106, 0x7118,
  0x711c, 0x711e, 0x7137, 0x7139, 0x713a, 0x7146, 0x7147, 0x7157,
  0x7159, 0x7161, 0x7164, 0x7174, 0x7179, 0x7185, 0x718e, 0x71a8,
  0x71ae, 0x71b3, 0x71b6, 0x71c3, 0x71c4, 0x71da, 0xe549, 0xe546,
  0x71ec, 0x71ee, 0x7201, 0x720a, 0x7216, 0x7217, 0xe56b, 0x7233,
  0x7242, 0x7247, 0x724a, 0x724e, 0x7251, 0x7256, 0x7259, 0x7260,
  0x7261, 0x7265, 0x7267, 0x7268, 0xe587, 0xe588, 0x727c, 0x727d,
  0x727f, 0x7289, 0x728d, 0x7297, 0x7299, 0x729f, 0x72a7, 0x72ab,
  0xe5ba, 0xe5bb, 0x72b2, 0x72bf, 0x72c0, 0x72c6, 0x72ce, 0x72d0,
  0x72d7, 0x72d9, 0x72e5, 0x72e7, 0x7311, 0xe61e,
  /* 0x27B21..0x27B7E */
  0xe629, 0x72f7, 0x72f9, 0x72fb, 0x7302, 0x730d, 0x7315, 0x731d,
  0x731e, 0x7327, 0x7329, 0xe671, 0xe643, 0x7347, 0x7351, 0x7357,
  0x735a, 0x736b, 0x7371, 0x7373, 0x73a1, 0xe699, 0xe6cd, 0x7388,
  0x738b, 0x738f, 0x739e, 0x73f5, 0xe6e4, 0xe6dd, 0x73f1, 0x73c1,
  0x73c7, 0x73dc, 0x73e2, 0x73e7, 0x7409, 0x740f, 0x7416, 0x7417,
  0x73fb, 0x7432, 0x7434, 0x743b, 0x7445, 0xe7c1, 0xe7ef, 0x746d,
  0x746f, 0x7578, 0x7579, 0x7586, 0x758c, 0x758d, 0xe810, 0x75ab,
  0x75b4, 0xe871, 0x75c8, 0xe8fb, 0xe91f, 0x762c, 0x7633, 0x7634,
  0xe936, 0x763c, 0x7641, 0x7661, 0xe989, 0x7682, 0xe9eb, 0x769a,
  0xea32, 0x29e7, 0x76a9, 0x76af, 0x76b3, 0x76ba, 0x76bd, 0x29fa,
  0xeaf8, 0x76d8, 0x76da, 0x76dd, 0x2a04, 0x7714, 0x7723, 0x2a29,
  0x7736, 0x7741, 0x7747, 0x7755, 0x7757, 0x775b,
  /* 0x27C21..0x27C7E */
  0x776a, 0xeba0, 0xebb1, 0x7796, 0x779a, 0x779e, 0x77a2, 0x77b1,
  0x77b2, 0x77be, 0x77cc, 0x77d1, 0x77d4, 0x77d8, 0x77d9, 0x77e1,
  0x77f1, 0x7804, 0x780d, 0x780e, 0x7814, 0x7816, 0x2abc, 0xec90,
  0x7823, 0x7832, 0x7833, 0x7825, 0x7847, 0x7866, 0x78ab, 0x78ad,
  0x78b0, 0xedcf, 0x78b7, 0x78b8, 0x78bb, 0x78bc, 0x78bf, 0x78c2,
  0x78c7, 0x78cb, 0x78e0, 0xee7f, 0x78e1, 0x78e3, 0x78e5, 0x78ea,
  0x78f0, 0x78f1, 0x78f3, 0x7908, 0x2b3b, 0xeef0, 0x7916, 0x7917,
  0xef19, 0x791a, 0x791b, 0x791c, 0xef50, 0x7931, 0x7932, 0x7933,
  0x793a, 0x793b, 0x793c, 0x7940, 0x7941, 0x7946, 0x794d, 0x794e,
  0x795c, 0x795f, 0x7960, 0x79a3, 0x79a6, 0x79b9, 0x79bd, 0x79bf,
  0x79c3, 0x79c9, 0x79d4, 0x79d9, 0x79de, 0xf0c6, 0x79f0, 0x79f9,
  0x79fc, 0x7a0a, 0x7a11, 0x7a16, 0x7a1a, 0x7a20,
  /* 0x27D21..0x27D7E */
  0x7a31, 0x7a36, 0x7a44, 0x7a4c, 0x7a58, 0x2bc2, 0x7aaf, 0x2bca,
  0x7ab7, 0x2bd2, 0x7ab9, 0xf172, 0x7ac6, 0x7ad0, 0x7ad2, 0x7ad5,
  0x2be8, 0x7adc, 0x7ae0, 0x7ae5, 0x7ae9, 0x7b03, 0x7b0c, 0x7b10,
  0x7b12, 0x7b16, 0x7b1c, 0x7b2b, 0x7b33, 0x7b3d, 0x2c20, 0x7b4b,
  0x7b63, 0x7b65, 0x7b6b, 0x7b6c, 0x7b73, 0x7b76, 0x7b77, 0x7ba6,
  0x7bac, 0x7bb1, 0xf2db, 0xf33d, 0x7bb2, 0x7bb8, 0x7bbe, 0x7bc7,
  0x7bf3, 0x7bd8, 0x7bdd, 0x7be7, 0x7bea, 0x7beb, 0x7bef, 0x7bee,
  0xf315, 0x7bfa, 0xf38a, 0x7bf7, 0xf349, 0x7c16, 0x7c18, 0x7c19,
  0x7c1a, 0x7c1d, 0x7c22, 0x7c27, 0x7c29, 0x7c2a, 0xf3c4, 0x7c31,
  0x7c36, 0x7c37, 0x7c45, 0x7c5c, 0xf3e9, 0x7c49, 0x7c4a, 0xf3db,
  0x7c54, 0x7c58, 0x7c5b, 0x7c5d, 0x7c5f, 0x7c69, 0x7c6a, 0x7c6b,
  0x7c6d, 0x7c6e, 0x7c70, 0x7c72, 0x7c75, 0x7c7a,
  /* 0x27E21..0x27E7E */
  0x7ce6, 0x7cf2, 0x7d0b, 0x7d02, 0xf4ce, 0x7d11, 0x7d17, 0x7d18,
  0xf52f, 0x2cc4, 0xf51a, 0x7d32, 0x2cd1, 0x7d42, 0x7d4a, 0x7d5f,
  0x7d62, 0xf5f9, 0x7d69, 0x7d6b, 0xf582, 0x7d73, 0x7d76, 0x7d77,
  0x7d7e, 0x7d84, 0x7d8d, 0x7d99, 0x7da1, 0x7dbf, 0x7db5, 0x7db9,
  0x7dbd, 0x7dc3, 0x7dc7, 0x7dc9, 0x7dd6, 0x7dda, 0x7ddf, 0x7de0,
  0x7de3, 0x7df4, 0x2d07, 0x7e0a, 0x7e02, 0x7e0d, 0x7e19, 0x7e1c,
  0x7e1d, 0x7e7b, 0x9f18, 0x7e80, 0x7e85, 0x7e9b, 0x7ea8, 0xf70c,
  0x7ebd, 0xf7b7, 0x7edf, 0x7ee7, 0x7eee, 0x7eff, 0x7f02, 0x2d77,
  0x7f03, 0x7f17, 0x7f19, 0x7f2f, 0x7f37, 0x7f3a, 0x7f3d, 0x7f41,
  0x7f45, 0x7f46, 0x7f53, 0x7f55, 0x7f58, 0xf8f1, 0x7f5d, 0xf902,
  0x7f69, 0xf91a, 0x7f6d, 0x7f70, 0x7f75, 0xf9b2, 0x0000, 0x0000,
  0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
};

static const ucs4_t jisx0213_to_ucs_pagestart[] = {
   0x0000,  0x0100,  0x0200,  0x0300,  0x0400,  0x1e00,  0x1f00,  0x2000,
   0x2100,  0x2200,  0x2300,  0x2400,  0x2500,  0x2600,  0x2700,  0x2900,
   0x3000,  0x3100,  0x3200,  0x3300,  0x3400,  0x3500,  0x3600,  0x3700,
   0x3800,  0x3900,  0x3a00,  0x3b00,  0x3c00,  0x3d00,  0x3e00,  0x3f00,
   0x4000,  0x4100,  0x4200,  0x4300,  0x4400,  0x4500,  0x4600,  0x4700,
   0x4800,  0x4900,  0x4a00,  0x4b00,  0x4c00,  0x4d00,  0x4e00,  0x4f00,
   0x5000,  0x5100,  0x5200,  0x5300,  0x5400,  0x5500,  0x5600,  0x5700,
   0x5800,  0x5900,  0x5a00,  0x5b00,  0x5c00,  0x5d00,  0x5e00,  0x5f00,
   0x6000,  0x6100,  0x6200,  0x6300,  0x6400,  0x6500,  0x6600,  0x6700,
   0x6800,  0x6900,  0x6a00,  0x6b00,  0x6c00,  0x6d00,  0x6e00,  0x6f00,
   0x7000,  0x7100,  0x7200,  0x7300,  0x7400,  0x7500,  0x7600,  0x7700,
   0x7800,  0x7900,  0x7a00,  0x7b00,  0x7c00,  0x7d00,  0x7e00,  0x7f00,
   0x8000,  0x8100,  0x8200,  0x8300,  0x8400,  0x8500,  0x8600,  0x8700,
   0x8800,  0x8900,  0x8a00,  0x8b00,  0x8c00,  0x8d00,  0x8e00,  0x8f00,
   0x9000,  0x9100,  0x9200,  0x9300,  0x9400,  0x9500,  0x9600,  0x9700,
   0x9800,  0x9900,  0x9a00,  0x9b00,  0x9c00,  0x9d00,  0x9e00,  0x9f00,
   0xf900,  0xfa00,  0xfe00,  0xff00, 0x20000, 0x20180, 0x20300, 0x20400,
  0x20500, 0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20d00,
  0x20e00, 0x20f00, 0x21200, 0x21300, 0x21400, 0x21500, 0x21600, 0x21700,
  0x21800, 0x21900, 0x21c00, 0x21d00, 0x21e00, 0x21f00, 0x22100, 0x22200,
  0x22300, 0x22600, 0x22800, 0x22900, 0x22a00, 0x22b00, 0x22c00, 0x22d00,
  0x23100, 0x23300, 0x23400, 0x23500, 0x23600, 0x23700, 0x23800, 0x23a00,
  0x23c00, 0x23d00, 0x23f00, 0x24000, 0x24100, 0x24300, 0x24600, 0x24700,
  0x24800, 0x24a00, 0x24b00, 0x24c00, 0x24d00, 0x24e00, 0x25000, 0x25100,
  0x25200, 0x25400, 0x25500, 0x25700, 0x25900, 0x25a00, 0x25b80, 0x25d00,
  0x25e00, 0x25f00, 0x26000, 0x26200, 0x26300, 0x26400, 0x26600, 0x26700,
  0x26800, 0x26900, 0x26a00, 0x26c00, 0x26e00, 0x26f00, 0x27080, 0x27380,
  0x27600, 0x27700, 0x27900, 0x27a00, 0x27b00, 0x27c00, 0x27d80, 0x27f00,
  0x28000, 0x28200, 0x28380, 0x28500, 0x28600, 0x28900, 0x28a00, 0x28b00,
  0x28d00, 0x28e00, 0x28f00, 0x29200, 0x29400, 0x29500, 0x29600, 0x29700,
  0x29800, 0x29a00, 0x29d00, 0x29e00, 0x29f00, 0x2a000, 0x2a100, 0x2a380,
  0x2a500, 0x2a600,
};

static const short jisx0213_from_ucs_level1[2715] = {
   -1,  -1,   0,   1,   2,   3,   4,   5,
   -1,   6,   7,   8,   9,  10,  11,  12,
   13,  14,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   15,  -1,  -1,  -1,  -1,  16,  -1,  -1,
   17,  18,  19,  -1,  20,  21,  22,  23,
   24,  25,  26,  27,  28,  -1,  29,  30,
   31,  32,  -1,  33,  34,  35,  36,  37,
   38,  39,  -1,  -1,  40,  41,  -1,  -1,
   -1,  -1,  -1,  -1,  42,  -1,  43,  44,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   45,  46,  47,  48,  -1,  -1,  -1,  49,
   50,  51,  52,  53,  54,  55,  56,  57,
   58,  59,  60,  61,  62,  63,  64,  65,
   66,  67,  68,  69,  -1,  70,  71,  72,
   73,  74,  -1,  75,  76,  77,  -1,  -1,
   -1,  78,  -1,  79,  80,  81,  82,  83,
   84,  -1,  -1,  85,  86,  87,  88,  89,
   90,  91,  92,  -1,  -1,  93,  94,  95,
   96,  97,  98,  -1,  99, 100, 101, 102,
  103, 104,  -1, 105, 106, 107,  -1, 108,
  109, 110, 111, 112, 113, 114, 115, 116,
  117, 118, 119,  -1, 120, 121,  -1, 122,
  123, 124, 125,  -1,  -1,  -1, 126, 127,
  128,  -1, 129,  -1, 130,  -1,  -1, 131,
  132,  -1,  -1, 133, 134, 135,  -1,  -1,
  136, 137, 138, 139, 140, 141, 142, 143,
  144, 145, 146, 147, 148, 149, 150, 151,
  152, 153, 154, 155, 156, 157, 158, 159,
  160, 161, 162, 163, 164, 165, 166, 167,
  168, 169, 170, 171, 172, 173, 174, 175,
  176, 177, 178, 179, 180, 181, 182, 183,
  184, 185, 186, 187, 188, 189, 190, 191,
  192, 193, 194, 195, 196, 197, 198, 199,
  200, 201, 202, 203, 204, 205, 206, 207,
  208, 209, 210, 211, 212, 213, 214, 215,
  216, 217, 218, 219, 220, 221, 222, 223,
  224, 225, 226, 227, 228, 229, 230, 231,
  232, 233, 234, 235, 236, 237, 238, 239,
  240, 241, 242, 243, 244, 245, 246, 247,
  248, 249, 250, 251, 252, 253, 254, 255,
  256, 257, 258, 259, 260, 261, 262, 263,
  264, 265, 266, 267, 268, 269, 270, 271,
  272, 273, 274, 275, 276, 277, 278, 279,
  280, 281, 282, 283, 284, 285, 286, 287,
  288, 289, 290, 291, 292, 293, 294, 295,
  296, 297, 298, 299, 300, 301, 302, 303,
  304, 305, 306, 307, 308, 309, 310, 311,
  312, 313, 314, 315, 316, 317, 318, 319,
  320, 321, 322, 323, 324, 325, 326, 327,
  328, 329, 330,  -1, 331, 332, 333, 334,
  335, 336, 337, 338, 339, 340, 341, 342,
  343, 344, 345, 346, 347, 348, 349, 350,
  351, 352, 353, 354, 355, 356, 357, 358,
  359, 360, 361, 362, 363, 364, 365, 366,
  367, 368, 369, 370, 371, 372, 373, 374,
  375, 376, 377, 378, 379, 380, 381,  -1,
  382, 383, 384, 385, 386, 387, 388, 389,
  390, 391, 392, 393, 394, 395, 396, 397,
  398, 399, 400, 401, 402, 403, 404, 405,
  406, 407, 408, 409, 410, 411, 412, 413,
  414, 415, 416,  -1,  -1, 417, 418, 419,
  420, 421, 422, 423, 424, 425, 426, 427,
  428, 429, 430, 431, 432, 433, 434, 435,
  436, 437, 438, 439, 440, 441, 442, 443,
  444, 445,  -1, 446, 447, 448, 449, 450,
  451, 452, 453, 454, 455, 456, 457,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1, 458, 459,  -1, 460,
  461, 462,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1, 463,  -1,  -1, 464, 465,  -1, 466,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
  467,  -1, 468,  -1,  -1,  -1, 469,  -1,
  470,  -1,  -1,  -1, 471, 472, 473, 474,
   -1, 475,  -1,  -1, 476,  -1,  -1, 477,
  478,  -1,  -1,  -1,  -1, 479,  -1,  -1,
  480,  -1, 481,  -1,  -1, 482, 483,  -1,
   -1,  -1,  -1, 484, 485,  -1, 486,  -1,
   -1,  -1,  -1,  -1,  -1, 487,  -1, 488,
   -1, 489, 490,  -1,  -1, 491,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
  492, 493,  -1, 494, 495, 496,  -1, 497,
   -1, 498,  -1,  -1,  -1,  -1,  -1, 499,
   -1, 500, 501,  -1, 502, 503,  -1,  -1,
   -1,  -1, 504,  -1,  -1,  -1,  -1, 505,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1, 506,  -1,  -1, 507, 508, 509, 510,
  511,  -1,  -1,  -1, 512, 513,  -1, 514,
   -1,  -1,  -1,  -1,  -1, 515,  -1,  -1,
  516,  -1,  -1,  -1, 517,  -1, 518,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1, 519,  -1,  -1,  -1,  -1,
   -1, 520, 521,  -1,  -1,  -1, 522,  -1,
   -1,  -1, 523,  -1,  -1, 524, 525,  -1,
  526,  -1,  -1,  -1,  -1,  -1,  -1, 527,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1, 528, 529,
   -1,  -1,  -1,  -1,  -1, 530,  -1, 531,
   -1, 532,  -1, 533,  -1, 534, 535, 536,
  537, 538,  -1,  -1, 539, 540,  -1, 541,
  542,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1, 543,  -1,  -1,  -1,  -1,  -1,
   -1, 544,  -1, 545, 546, 547,  -1, 548,
   -1,  -1,  -1,  -1,  -1, 549,  -1,  -1,
   -1,  -1, 550,  -1, 551,  -1,  -1, 552,
   -1,  -1,  -1,  -1,  -1,  -1, 553,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
  554,  -1, 555,  -1,  -1,  -1,  -1, 556,
   -1,  -1, 557,  -1,  -1,  -1,  -1,  -1,
   -1, 558,  -1,  -1,  -1, 559,  -1,  -1,
  560,  -1,  -1,  -1, 561,  -1,  -1,  -1,
  562, 563, 564,  -1,  -1,  -1,  -1,  -1,
   -1, 565,  -1,  -1, 566,  -1, 567, 568,
  569, 570,  -1,  -1,  -1,  -1,  -1,  -1,
  571,  -1, 572, 573, 574,  -1, 575,  -1,
   -1,  -1,  -1,  -1,  -1, 576, 577,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1, 578,
   -1,  -1,  -1, 579,  -1,  -1, 580,  -1,
   -1, 581,  -1,  -1,  -1,  -1, 582,  -1,
  583, 584,  -1, 585, 586, 587,  -1, 588,
  589, 590,  -1, 591,  -1,  -1,  -1,  -1,
   -1, 592, 593,  -1,  -1, 594,  -1,  -1,
  595,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1, 596, 597,  -1, 598,  -1,  -1,  -1,
   -1,  -1,  -1, 599,  -1, 600,  -1, 601,
  602, 603, 604, 605,  -1,  -1,  -1,  -1,
  606, 607,  -1, 608,  -1,  -1,  -1,  -1,
   -1, 609,  -1,  -1,  -1,  -1, 610, 611,
   -1,  -1,  -1, 612, 613,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1, 614,
  615, 616,  -1,  -1,  -1,  -1,  -1,  -1,
  617,  -1, 618,  -1, 619, 620,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1, 621,  -1,
   -1,  -1, 622,  -1,  -1,  -1, 623, 624,
   -1,  -1, 625,  -1,  -1,  -1, 626,  -1,
  627,  -1,  -1,  -1,  -1,  -1, 628,  -1,
   -1,  -1, 629,  -1,  -1,  -1,  -1,  -1,
   -1, 630, 631, 632,  -1,  -1,  -1, 633,
  634, 635,  -1,  -1,  -1, 636,  -1, 637,
   -1,  -1,  -1, 638,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1, 639, 640,  -1,
  641, 642, 643, 644,  -1,  -1,  -1, 645,
   -1,  -1,  -1,  -1, 646, 647,  -1, 648,
  649,  -1, 650, 651, 652,  -1,  -1, 653,
   -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1, 654,  -1,  -1,  -1,  -1,  -1,
   -1,  -1, 655,  -1,  -1,  -1,  -1, 656,
   -1, 657,  -1, 658, 659, 660,  -1,  -1,
   -1,  -1,  -1, 661,  -1,  -1,  -1,  -1,
   -1, 662,  -1,  -1,  -1,  -1,  -1,  -1,
   -1,  -1,  -1,  -1,  -1,  -1,  -1, 663,
  664, 665, 666, 667,  -1,  -1,  -1, 668,
  669,  -1, 670, 671,  -1,  -1, 672,  -1,
   -1,  -1,  -1,  -1,  -1,  -1, 673,  -1,
  674,  -1,  -1,  -1,  -1,  -1,  -1, 675,
  676,  -1, 677,
};

static const unsigned short jisx0213_from_ucs_level2_data[] = {
  /* 0x0080 */
  0x2922, 0x2923, 0x2171, 0x2172, 0x2924, 0x2925, 0x2178, 0x212f,
  0x2926, 0x2927, 0x2928, 0x224c, 0x2929, 0x292a, 0x292b, 0x216b,
  0x215e, 0x292c, 0x292d, 0x212d, 0x2279, 0x292e, 0x292f, 0x2930,
  0x2931, 0x2932, 0x2933, 0x2934, 0x2935, 0x2936,
  /* 0x00C0 */
  0x2937, 0x2938, 0x2939, 0x293a, 0x293b, 0x293c, 0x293d, 0x293e,
  0x293f, 0x2940, 0x2941, 0x2942, 0x2943, 0x2944, 0x2945, 0x2946,
  0x2947, 0x2948, 0x2949, 0x294a, 0x294b, 0x294c, 0x294d, 0x215f,
  0x294e, 0x294f, 0x2950, 0x2951, 0x2952, 0x2953, 0x2954, 0x2955,
  0x2956, 0x2957, 0x2958, 0x2959, 0x295a, 0x295b, 0x29dc, 0x295d,
  0x295e, 0x295f, 0x2960, 0x2961, 0x2962, 0x2963, 0x2964, 0x2965,
  0x2966, 0x2967, 0x2968, 0x2969, 0x296a, 0x296b, 0x296c, 0x2160,
  0x296d, 0x296e, 0x296f, 0x2970, 0x2971, 0x2972, 0x2973, 0x2974,
  /* 0x0100 */
  0x2975, 0x297a, 0x2a3a, 0x2a49, 0x2a21, 0x2a2c, 0x2a3c, 0x2a4b,
  0x2a59, 0x2a5f, 0x2a3d, 0x2a4c, 0x2a40, 0x2a4f, 0x2a50, 0x2978,
  0x297d, 0x2a3e, 0x2a4d, 0x2a3f, 0x2a4e, 0x2a5a, 0x2a60, 0x2a5b,
  0x2a61, 0x2a7d, 0x2976, 0x297b, 0x2a5c, 0x2a62, 0x2a3b, 0x2a4a,
  0x2a24, 0x2a2f,
  /* 0x0140 */
  0x2a23, 0x2a2e, 0x2a41, 0x2a51, 0x2a42, 0x2a52, 0x2a7a, 0x2979,
  0x297e, 0x2a43, 0x2a53, 0x2b2b, 0x2b2a, 0x2a39, 0x2a48, 0x2a44,
  0x2a54, 0x2a25, 0x2a30, 0x2a5d, 0x2a63, 0x2a27, 0x2a33, 0x2a26,
  0x2a32, 0x2a47, 0x2a57, 0x2a28, 0x2a34, 0x2977, 0x297c, 0x2a5e,
  0x2a64, 0x2a45, 0x2a55, 0x2a46, 0x2a56, 0x2a29, 0x2a35, 0x2a2b,
  0x2a38, 0x2a2a, 0x2a37,
  /* 0x0180 */
  0x2b29,
  /* 0x01C0 */
  0x2b24, 0x286f, 0x2870, 0x2871, 0x2876, 0x2877, 0x2878, 0x2879,
  0x287a, 0x287b, 0x287c, 0x2874, 0x2875, 0x2b45,
  /* 0x0240 */
  0x2b33, 0x2b39, 0x2b3a, 0x2b25, 0x2bb8, 0x2b3f, 0x2a6e, 0x2b26,
  0x2b2e, 0x2bb0, 0x2bc3, 0x2b31, 0x2b32, 0x2a75, 0x2b28, 0x2a79,
  0x2b36, 0x2b3c, 0x2b22, 0x2b42, 0x2b2c, 0x2a6a, 0x2a74, 0x2a6b,
  0x2b34, 0x2a7b, 0x2a65, 0x2a76, 0x2a6f, 0x2b2f, 0x2a6c, 0x2b41,
  0x2a73, 0x2a70, 0x2a67,
  /* 0x0280 */
  0x2a7c, 0x2a71, 0x2a68, 0x2b27, 0x2a6d, 0x2b2d, 0x2b35, 0x2a66,
  0x2bb7, 0x2b3b, 0x2a78, 0x2a72, 0x2b40, 0x2a69, 0x2b21, 0x2a7e,
  0x2b23, 0x2a77, 0x2b3e, 0x2b3d,
  /* 0x02C0 */
  0x2a31, 0x2b53, 0x2b54, 0x2b55, 0x2b56, 0x2a22, 0x2a58, 0x2a2d,
  0x2a36, 0x2b71, 0x2be0, 0x2b61, 0x2b62, 0x2b63, 0x2be4,
  /* 0x0300 */
  0x2b5c, 0x2b5a, 0x2b5f, 0x2b7d, 0x2b5b, 0x2b57, 0x2b6d, 0x2b59,
  0x2b5e, 0x2b5d, 0x2b78, 0x2b79, 0x2b7e, 0x2b6a, 0x2b76, 0x2b77,
  0x2b6b, 0x2b6c, 0x2b72, 0x2b67, 0x2b6f, 0x2b7a, 0x2b68, 0x2b70,
  0x2b73, 0x2b75, 0x2b69, 0x2b7b, 0x2b7c, 0x2b74, 0x2b6e,
  /* 0x0340 */
  0x2b52,
  /* 0x0380 */
  0x2621, 0x2622, 0x2623, 0x2624, 0x2625, 0x2626, 0x2627, 0x2628,
  0x2629, 0x262a, 0x262b, 0x262c, 0x262d, 0x262e, 0x262f, 0x2630,
  0x2631, 0x2632, 0x2633, 0x2634, 0x2635, 0x2636, 0x2637, 0x2638,
  0x2641, 0x2642, 0x2643, 0x2644, 0x2645, 0x2646, 0x2647, 0x2648,
  0x2649, 0x264a, 0x264b, 0x264c, 0x264d, 0x264e, 0x264f,
  /* 0x03C0 */
  0x2650, 0x2651, 0x2659, 0x2652, 0x2653, 0x2654, 0x2655, 0x2656,
  0x2657, 0x2658,
  /* 0x0400 */
  0x2727, 0x2721, 0x2722, 0x2723, 0x2724, 0x2725, 0x2726, 0x2728,
  0x2729, 0x272a, 0x272b, 0x272c, 0x272d, 0x272e, 0x272f, 0x2730,
  0x2731, 0x2732, 0x2733, 0x2734, 0x2735, 0x2736, 0x2737, 0x2738,
  0x2739, 0x273a, 0x273b, 0x273c, 0x273d, 0x273e, 0x273f, 0x2740,
  0x2741, 0x2751, 0x2752, 0x2753, 0x2754, 0x2755, 0x2756, 0x2758,
  0x2759, 0x275a, 0x275b, 0x275c, 0x275d, 0x275e, 0x275f, 0x2760,
  0x2761,
  /* 0x0440 */
  0x2762, 0x2763, 0x2764, 0x2765, 0x2766, 0x2767, 0x2768, 0x2769,
  0x276a, 0x276b, 0x276c, 0x276d, 0x276e, 0x276f, 0x2770, 0x2771,
  0x2757,
  /* 0x1E00 */
  0x2872, 0x2873,
  /* 0x1F40 */
  0x2b46, 0x2b47, 0x2b50, 0x2b51,
  /* 0x2000 */
  0x213e, 0x237c, 0x213d, 0x2142, 0x2146, 0x2147, 0x2148, 0x2149,
  0x2277, 0x2278, 0x2340, 0x2145, 0x2144, 0x2273, 0x216c, 0x216d,
  0x2228, 0x286b, 0x2b58,
  /* 0x2040 */
  0x2c7e, 0x286c, 0x286d, 0x286e, 0x2c7d,
  /* 0x2080 */
  0x2921,
  /* 0x2100 */
  0x216e, 0x235d, 0x235f, 0x2d62, 0x2d64, 0x2360, 0x2272, 0x235c,
  /* 0x2140 */
  0x2778, 0x2779, 0x277a, 0x2d35, 0x2d36, 0x2d37, 0x2d38, 0x2d39,
  0x2d3a, 0x2d3b, 0x2d3c, 0x2d3d, 0x2d3e, 0x2d3f, 0x2d57, 0x2c35,
  0x2c36, 0x2c37, 0x2c38, 0x2c39, 0x2c3a, 0x2c3b, 0x2c3c, 0x2c3d,
  0x2c3e, 0x2c3f, 0x2c40,
  /* 0x2180 */
  0x222b, 0x222c, 0x222a, 0x222d, 0x2271, 0x2327, 0x2325, 0x2326,
  0x2328,
  /* 0x21C0 */
  0x2329, 0x224d, 0x224e, 0x232b, 0x232c, 0x232a, 0x232d,
  /* 0x2200 */
  0x224f, 0x225f, 0x2250, 0x2247, 0x2260, 0x223a, 0x2246, 0x223b,
  0x215d, 0x235b, 0x2265, 0x2267, 0x2167, 0x2d78, 0x225c, 0x2254,
  0x2255, 0x224a, 0x224b, 0x2241, 0x2240, 0x2269, 0x226a, 0x2d73,
  0x2168, 0x2268, 0x2266,
  /* 0x2240 */
  0x226c, 0x226d, 0x226e, 0x2262, 0x2162, 0x2261, 0x226b, 0x2165,
  0x2166, 0x2263, 0x2264, 0x226f, 0x2270,
  /* 0x2280 */
  0x223e, 0x223f, 0x2242, 0x2243, 0x223c, 0x223d, 0x2244, 0x2245,
  0x2251, 0x2252, 0x2253, 0x225d, 0x2d79,
  /* 0x22C0 */
  0x2776, 0x2777,
  /* 0x2300 */
  0x2248, 0x2249, 0x225e, 0x277c,
  /* 0x2380 */
  0x2742, 0x2743,
  /* 0x23C0 */
  0x2744, 0x2745, 0x2746, 0x2747, 0x2748, 0x2749, 0x274a, 0x274b,
  0x274c, 0x274d, 0x274e, 0x274f, 0x2750, 0x277e,
  /* 0x2400 */
  0x277d,
  /* 0x2440 */
  0x2d21, 0x2d22, 0x2d23, 0x2d24, 0x2d25, 0x2d26, 0x2d27, 0x2d28,
  0x2d29, 0x2d2a, 0x2d2b, 0x2d2c, 0x2d2d, 0x2d2e, 0x2d2f, 0x2d30,
  0x2d31, 0x2d32, 0x2d33, 0x2d34,
  /* 0x24C0 */
  0x2c41, 0x2c42, 0x2c43, 0x2c44, 0x2c45, 0x2c46, 0x2c47, 0x2c48,
  0x2c49, 0x2c4a, 0x2c4b, 0x2c4c, 0x2c4d, 0x2c4e, 0x2c4f, 0x2c50,
  0x2c51, 0x2c52, 0x2c53, 0x2c54, 0x2c55, 0x2c56, 0x2c57, 0x2c58,
  0x2c59, 0x2c5a, 0x2c2b, 0x2c2c, 0x2c2d, 0x2c2e, 0x2c2f, 0x2c30,
  0x2c31, 0x2c32, 0x2c33, 0x2c34, 0x265a, 0x265b, 0x265c, 0x265d,
  0x265e, 0x265f, 0x2660, 0x2661, 0x2662, 0x2663,
  /* 0x2500 */
  0x2821, 0x282c, 0x2822, 0x282d, 0x2823, 0x282e, 0x2824, 0x282f,
  0x2826, 0x2831, 0x2825, 0x2830, 0x2827, 0x283c, 0x2837, 0x2832,
  0x2829, 0x283e, 0x2839, 0x2834, 0x2828, 0x2838, 0x283d, 0x2833,
  0x282a, 0x283a, 0x283f, 0x2835, 0x282b, 0x283b,
  /* 0x2540 */
  0x2840, 0x2836,
  /* 0x2580 */
  0x2223, 0x2222, 0x266d, 0x2225, 0x2224, 0x2322, 0x2321, 0x2227,
  0x2226,
  /* 0x25C0 */
  0x2324, 0x2323, 0x2221, 0x217e, 0x233b, 0x217b, 0x217d, 0x217c,
  0x2867, 0x2868, 0x2869, 0x286a, 0x233f, 0x227e,
  /* 0x2600 */
  0x2668, 0x2669, 0x266a, 0x266b, 0x217a, 0x2179, 0x2667, 0x2664,
  0x2665, 0x2d7e,
  /* 0x2640 */
  0x216a, 0x2169, 0x263a, 0x263d, 0x263b, 0x2640, 0x2639, 0x263e,
  0x263c, 0x263f, 0x266c, 0x227d, 0x2276, 0x227b, 0x227c, 0x2275,
  0x227a, 0x2274,
  /* 0x2700 */
  0x277b,
  /* 0x2740 */
  0x2d7d, 0x2c21, 0x2c22, 0x2c23, 0x2c24, 0x2c25, 0x2c26, 0x2c27,
  0x2c28, 0x2c29, 0x2c2a,
  /* 0x2900 */
  0x232e, 0x232f,
  /* 0x2980 */
  0x233a,
  /* 0x29C0 */
  0x237d, 0x237e,
  /* 0x3000 */
  0x2121, 0x2122, 0x2123, 0x2137, 0x2139, 0x213a, 0x213b, 0x2152,
  0x2153, 0x2154, 0x2155, 0x2156, 0x2157, 0x2158, 0x2159, 0x215a,
  0x215b, 0x2229, 0x222e, 0x214c, 0x214d, 0x225a, 0x225b, 0x2258,
  0x2259, 0x2141, 0x2d60, 0x2d61, 0x2666, 0x2233, 0x2234, 0x2235,
  0x2236, 0x2237, 0x233c,
  /* 0x3040 */
  0x2421, 0x2422, 0x2423, 0x2424, 0x2425, 0x2426, 0x2427, 0x2428,
  0x2429, 0x242a, 0x24ab, 0x242c, 0x24ad, 0x242e, 0x24af, 0x2430,
  0x24b1, 0x2432, 0x24b3, 0x2434, 0x2435, 0x2436, 0x2437, 0x2438,
  0x2439, 0x243a, 0x243b, 0x243c, 0x243d, 0x243e, 0x243f, 0x2440,
  0x2441, 0x2442, 0x2443, 0x2444, 0x2445, 0x2446, 0x2447, 0x2448,
  0x2449, 0x244a, 0x244b, 0x244c, 0x244d, 0x244e, 0x244f, 0x2450,
  0x2451, 0x2452, 0x2453, 0x2454, 0x2455, 0x2456, 0x2457, 0x2458,
  0x2459, 0x245a, 0x245b, 0x245c, 0x245d, 0x245e, 0x245f,
  /* 0x3080 */
  0x2460, 0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466, 0x2467,
  0x2468, 0x2469, 0x246a, 0x246b, 0x246c, 0x246d, 0x246e, 0x246f,
  0x2470, 0x2471, 0x2472, 0x2473, 0x2474, 0x2475, 0x2476, 0x212b,
  0x212c, 0x2135, 0x2136, 0x2239, 0x237b, 0x2521, 0x2522, 0x2523,
  0x2524, 0x2525, 0x2526, 0x2527, 0x2528, 0x2529, 0x252a, 0x25ab,
  0x252c, 0x25ad, 0x252e, 0x25af, 0x2530, 0x25b1, 0x2532, 0x25b3,
  0x2534, 0x2535, 0x2536, 0x2537, 0x2538, 0x2539, 0x253a, 0x25bb,
  0x253c, 0x253d, 0x253e, 0x253f,
  /* 0x30C0 */
  0x2540, 0x2541, 0x2542, 0x2543, 0x25c4, 0x2545, 0x2546, 0x2547,
  0x25c8, 0x2549, 0x254a, 0x254b, 0x254c, 0x254d, 0x254e, 0x254f,
  0x2550, 0x2551, 0x2552, 0x2553, 0x2554, 0x2555, 0x2556, 0x2557,
  0x2558, 0x2559, 0x255a, 0x255b, 0x255c, 0x255d, 0x255e, 0x255f,
  0x2560, 0x2561, 0x2562, 0x2563, 0x2564, 0x2565, 0x2566, 0x2567,
  0x2568, 0x2569, 0x256a, 0x256b, 0x256c, 0x256d, 0x256e, 0x256f,
  0x2570, 0x2571, 0x2572, 0x2573, 0x2574, 0x2575, 0x2576, 0x2772,
  0x2773, 0x2774, 0x2775, 0x2126, 0x213c, 0x2133, 0x2134, 0x2238,
  /* 0x31C0 */
  0x266e, 0x266f, 0x2670, 0x2671, 0x2672, 0x2673, 0x2674, 0x26f5,
  0x2676, 0x2677, 0x2679, 0x267a, 0x267b, 0x267c, 0x267d, 0x267e,
  /* 0x3200 */
  0x2d6a, 0x2d6b, 0x2d6c,
  /* 0x3240 */
  0x2841, 0x2842, 0x2843, 0x2844, 0x2845, 0x2846, 0x2847, 0x2848,
  0x2849, 0x284a, 0x284b, 0x284c, 0x284d, 0x284e, 0x284f,
  /* 0x3280 */
  0x2d65, 0x2d66, 0x2d67, 0x2d68, 0x2d69, 0x2850, 0x2851, 0x2852,
  0x2853, 0x2854, 0x2855, 0x2856, 0x2857, 0x2858, 0x2859, 0x285a,
  0x285b, 0x285c, 0x285d, 0x285e,
  /* 0x32C0 */
  0x2c5b, 0x2c5c, 0x2c5d, 0x2c5e, 0x2c5f, 0x2c60, 0x2c61, 0x2c62,
  0x2c63, 0x2c64, 0x2c65, 0x2c66, 0x2c67, 0x2c68, 0x2c69, 0x2c6a,
  0x2c6b, 0x2c6c, 0x2c6d, 0x2c6e, 0x2c71, 0x2c70, 0x2c73, 0x2c72,
  0x2c6f,
  /* 0x3300 */
  0x2d46, 0x2d4a, 0x2d41, 0x2d44, 0x2d42, 0x2d4c, 0x2d4b, 0x2d45,
  0x2d4d, 0x2d47, 0x2d4f,
  /* 0x3340 */
  0x2d40, 0x2d4e, 0x2d43, 0x2d48, 0x2d49, 0x2d5f, 0x2d6f, 0x2d6e,
  0x2d6d,
  /* 0x3380 */
  0x2d53, 0x2d54, 0x2d50, 0x2d51, 0x2d52, 0x2d56,
  /* 0x33C0 */
  0x2d55, 0x235e, 0x2d63,
  /* 0x3400 */
  0x2e23, 0xa12d, 0xa132, 0xa133,
  /* 0x3440 */
  0xa15e, 0xa156,
  /* 0x3480 */
  0xa17e, 0x2e53, 0xa32b,
  /* 0x34C0 */
  0xf468, 0xa32f, 0x2e5b,
  /* 0x3500 */
  0xa348,
  /* 0x3540 */
  0xa35d, 0xa35e, 0xa361, 0xa367,
  /* 0x3580 */
  0xa423, 0xa426,
  /* 0x35C0 */
  0xa42f, 0xa438, 0xa442,
  /* 0x3600 */
  0xa44a,
  /* 0x3640 */
  0xa479,
  /* 0x3680 */
  0xa53f, 0xa543, 0xa541,
  /* 0x36C0 */
  0xa557,
  /* 0x3740 */
  0xa823, 0xa825, 0xa829, 0xa828, 0xa82c,
  /* 0x3780 */
  0x4f5f,
  /* 0x37C0 */
  0xa83e, 0x4f6f, 0xa856, 0xa859, 0xa85c,
  /* 0x3800 */
  0xa85e, 0xa86f, 0xa871,
  /* 0x3840 */
  0xa874, 0xa879, 0xa87b,
  /* 0x38C0 */
  0xac3b,
  /* 0x3900 */
  0xac46, 0xac4a,
  /* 0x3940 */
  0xac60,
  /* 0x3A40 */
  0xad5b, 0xad5f,
  /* 0x3AC0 */
  0xad71, 0xae36, 0xad7c,
  /* 0x3B00 */
  0xae2e, 0xae32, 0xae34, 0x7549,
  /* 0x3B40 */
  0xae6d, 0xae65,
  /* 0x3B80 */
  0xaf28, 0xaf29, 0xaf2c, 0xaf34, 0x757e,
  /* 0x3BC0 */
  0x7621, 0xaf48, 0xaf5d,
  /* 0x3C00 */
  0x763a, 0xaf77,
  /* 0x3CC0 */
  0xee3b, 0xee42,
  /* 0x3D00 */
  0xee71, 0xee7e,
  /* 0x3D40 */
  0xef40,
  /* 0x3D80 */
  0xef54,
  /* 0x3DC0 */
  0xef70, 0xef77,
  /* 0x3E00 */
  0xf028, 0x7766,
  /* 0x3E40 */
  0xf03f, 0xf041, 0xf042,
  /* 0x3E80 */
  0xf049, 0xf050,
  /* 0x3F40 */
  0xf134, 0x784d, 0xf146, 0xf148,
  /* 0x3F80 */
  0xf15c,
  /* 0x3FC0 */
  0xf167, 0xf16c,
  /* 0x4000 */
  0xf222,
  /* 0x4040 */
  0xf22d,
  /* 0x4080 */
  0xf239,
  /* 0x4100 */
  0xf264,
  /* 0x4140 */
  0xf274, 0xf277, 0xf27d,
  /* 0x4180 */
  0xf333, 0xf337,
  /* 0x41C0 */
  0xf347, 0xf34b, 0xf348,
  /* 0x4200 */
  0xf353, 0xf357,
  /* 0x4240 */
  0x796d,
  /* 0x42C0 */
  0xf42b, 0xf436, 0xf43b,
  /* 0x4300 */
  0xf44e, 0xf45d,
  /* 0x4340 */
  0xf461,
  /* 0x43C0 */
  0xf53e, 0xf542,
  /* 0x4400 */
  0xf548, 0xf54a, 0xf54c, 0xf54f,
  /* 0x4440 */
  0x7a59, 0x7a5a, 0xf56c, 0xf56e,
  /* 0x4480 */
  0xf577, 0xf635, 0xf632,
  /* 0x44C0 */
  0xf634,
  /* 0x4500 */
  0xf659, 0xf654, 0xf66d,
  /* 0x4540 */
  0xf66e,
  /* 0x4580 */
  0x7b51, 0xf74f,
  /* 0x45C0 */
  0xf76c, 0x7b60,
  /* 0x4600 */
  0xf824,
  /* 0x4640 */
  0xf83a, 0xf843,
  /* 0x4680 */
  0xf84e, 0xf853,
  /* 0x4700 */
  0xf86b,
  /* 0x4740 */
  0xf929,
  /* 0x47C0 */
  0xf93f,
  /* 0x4800 */
  0xf949,
  /* 0x4840 */
  0x7c4b, 0xf95c,
  /* 0x4880 */
  0xfa27,
  /* 0x4980 */
  0x7d58,
  /* 0x49C0 */
  0xfb6a, 0xfb70,
  /* 0x4A00 */
  0xfb75, 0xfb78,
  /* 0x4A80 */
  0xfc37,
  /* 0x4B00 */
  0xfc55,
  /* 0x4BC0 */
  0xfd26, 0xfd28, 0xfd2a, 0xfd31,
  /* 0x4C00 */
  0x7e3e, 0xfd3f,
  /* 0x4CC0 */
  0xfe2a, 0xfe2d,
  /* 0x4D00 */
  0xfe4b,
  /* 0x4D40 */
  0xfe60,
  /* 0x4E00 */
  0x306c, 0x437a, 0xa122, 0x3c37, 0x4b7c, 0x3e66, 0x3b30, 0x3e65,
  0x323c, 0x4954, 0x4d3f, 0xa123, 0x5022, 0x312f, 0xa124, 0x336e,
  0x5023, 0x4024, 0x5242, 0x3556, 0x4a3a, 0x3e67, 0x4e3e, 0x4a42,
  0x2e24, 0xa125, 0x5024, 0xa126, 0xf02e, 0x4366, 0xa127, 0x2e25,
  0x2e26, 0x5025, 0x367a, 0x5026, 0x345d, 0x4330, 0x3c67, 0x5027,
  0x5028,
  /* 0x4E40 */
  0xa128, 0x5029, 0x4735, 0x3557, 0xa129, 0xa12a, 0x4737, 0x4663,
  0x3843, 0x4b33, 0xa12c, 0x6949, 0x502a, 0x3e68, 0x502b, 0x3235,
  0xa12f, 0x3665, 0x3870, 0x4c69, 0x5626, 0xa130, 0x4d70, 0x467d,
  0x3425,
  /* 0x4E80 */
  0x3535, 0x502c, 0x502d, 0x4e3b, 0x4d3d, 0x4168, 0x502f, 0x3b76,
  0x4673, 0x2e27, 0x5032, 0x313e, 0x385f, 0x385e, 0x3066, 0x4f4b,
  0x4f4a, 0x3a33, 0x3021, 0xa131, 0x5033, 0x5034, 0x5035, 0x4b34,
  0x5036, 0x3872, 0x3067, 0x4b72, 0x357c, 0x357d, 0x357e, 0x4462,
  0x4e3c, 0x5037, 0x5038, 0x5039, 0xa134, 0x3f4d, 0xa135, 0xa137,
  /* 0x4EC0 */
  0x3d3a, 0x3f4e, 0x503e, 0xa138, 0x503c, 0x503d, 0x3558, 0xa139,
  0x3a23, 0x3270, 0x503b, 0x503a, 0x4a29, 0xa13a, 0x3b46, 0x3b45,
  0x423e, 0x503f, 0x4955, 0x4067, 0xa13c, 0x2138, 0x5040, 0x5042,
  0x2e28, 0x4265, 0x4e61, 0x304a, 0xa13b, 0x5041, 0x323e, 0x3644,
  0xa13d, 0x4367, 0xa13e, 0x376f, 0x5043, 0x4724, 0x2e29, 0x2e2a,
  /* 0x4F00 */
  0xa13f, 0x346b, 0x2e2b, 0x5044, 0x304b, 0x2e2c, 0x3860, 0x346c,
  0x497a, 0x4832, 0x3559, 0xa140, 0x3271, 0x5067, 0x4541, 0x476c,
  0x5046, 0x483c, 0x4e62, 0xa142, 0x3f2d, 0x3b47, 0x3b77, 0x3240,
  0xa143,
  /* 0x4F40 */
  0x4451, 0x4322, 0x504a, 0x2e2e, 0x2e2f, 0x304c, 0x4463, 0x3d3b,
  0x3a34, 0x4d24, 0x424e, 0xa144, 0x323f, 0x2e30, 0x5049, 0xa145,
  0x4d3e, 0x5045, 0x5047, 0x3a6e, 0x5048, 0x5524, 0x2e31, 0x2e2d,
  0xa141, 0x5050, 0x2e32, 0x2e33, 0x5053, 0x5051, 0x3242, 0x4a3b,
  0x504b, 0xa147, 0xa148, 0xa149, 0x504f, 0x3873, 0xa14a, 0x2e34,
  0x3b48,
  /* 0x4F80 */
  0xa14b, 0x3426, 0xa14c, 0x5054, 0x504c, 0x2e35, 0x4e63, 0x3b78,
  0x504d, 0x5052, 0xa14d, 0x2e36, 0x5055, 0x2e37, 0x504e, 0xa14e,
  0x3621, 0x304d, 0x3622, 0x3241, 0x5525, 0x4b79, 0x496e, 0x3874,
  0xa150, 0x3f2f, 0x4e37, 0xa151, 0x4a58,
  /* 0x4FC0 */
  0x3738, 0x4225, 0x3264, 0xa152, 0x2e39, 0x3d53, 0xa153, 0x5059,
  0xa154, 0x505e, 0x505c, 0xa155, 0x5057, 0x422f, 0x505a, 0x505d,
  0x505b, 0x4a5d, 0x5058, 0x2e3a, 0x3f2e, 0x4b73, 0x505f, 0x5060,
  0xa14f, 0x3d24, 0x506d, 0x2e21, 0xa157, 0x4750, 0x4936, 0x5068,
  0x4a70, 0x3236, 0x506c,
  /* 0x5000 */
  0xa158, 0x2e3b, 0x2e3c, 0x5066, 0x506f, 0x4152, 0x3844, 0x475c,
  0x2e3d, 0x6047, 0xa159, 0x506e, 0x455d, 0xa15a, 0x5063, 0x3876,
  0x2e3e, 0x3875, 0x5061, 0xa15b, 0xa15c, 0x3c5a, 0x5069, 0xa15d,
  0x4a6f, 0x434d, 0x5065, 0x3771, 0x2e3f, 0x5062, 0x506a, 0x5064,
  0x4e51, 0x506b, 0x4f41, 0x2e40, 0x3666, 0x3770, 0x2e42,
  /* 0x5040 */
  0x2e41, 0x2e43, 0xa15f, 0x5070, 0xa160, 0x5071, 0x5075, 0x304e,
  0xa161, 0x4a50, 0x5074, 0xa162, 0x5073, 0x5077, 0xa163, 0x5076,
  0x4464, 0xa164, 0x3772, 0xa165, 0xa166, 0x5078, 0xa167, 0x3c45,
  0x4226, 0x4465, 0x3676, 0x5079, 0x3536,
  /* 0x5080 */
  0x507a, 0x507c, 0xa169, 0x4b35, 0x3766, 0xa16a, 0xa16b, 0x2e44,
  0xa16c, 0xa16d, 0x3b31, 0x4877, 0x507b, 0xa16e, 0xa168, 0xa16f,
  0x3a45, 0x4d43, 0xa171, 0x507e, 0x5123, 0x507d, 0x3a44, 0x3d7d,
  0xa172, 0xa173, 0x3739,
  /* 0x50C0 */
  0x5124, 0xa174, 0x364f, 0xa175, 0x5121, 0x5122, 0x2e45, 0x462f,
  0xa178, 0x417c, 0x2e47, 0x3623, 0xa17a, 0x4b4d, 0x5125, 0xa17b,
  0x4e3d, 0x5126, 0xa17c, 0x5129, 0x5127, 0x2e48, 0x414e, 0xa17d,
  0x5128, 0x512a, 0x2e46, 0xa176, 0x512c, 0x512b, 0x4a48,
  /* 0x5100 */
  0x3537, 0x512e, 0x512f, 0x2e4b, 0x322f, 0x2e4a, 0xa321, 0x512d,
  0x2e4c, 0x3c74, 0x5132, 0x5131, 0x5130, 0xa323, 0x5056, 0x5133,
  0xa324, 0x2e4d, 0x3d7e, 0x5134, 0x4d25, 0x4c59, 0x2e4e, 0x5136,
  0x5135, 0x5138, 0x5137, 0x5139,
  /* 0x5140 */
  0x513a, 0x3074, 0x3835, 0x373b, 0x3d3c, 0x437b, 0x3624, 0x4068,
  0x3877, 0x2e4f, 0x396e, 0x513c, 0x4c48, 0x4546, 0x3b79, 0x513b,
  0x513d, 0x2e51, 0x2e52, 0x455e, 0x3375, 0xa326, 0x513e, 0x467e,
  0x4134, 0x5140, 0x5141, 0x482c, 0x3878, 0x4f3b, 0x5142, 0x3626,
  0xa328, 0x4a3c, 0x4236, 0x3671, 0x4535, 0xf474, 0x3773,
  /* 0x5180 */
  0x5143, 0x5144, 0xa329, 0x4662, 0x315f, 0x5147, 0x3a7d, 0xa32a,
  0x5146, 0x3a46, 0x5148, 0x666e, 0x5149, 0x4b41, 0x514a, 0x514b,
  0x514c, 0x3e69, 0xa32c, 0x3c4c, 0x2e54, 0x3427, 0x514f, 0xa32d,
  0x514d, 0x4c3d, 0x514e, 0x495a, 0x5150, 0x5151, 0x5152, 0x455f,
  0xa32e, 0x5156, 0x5154, 0x5155, 0x5153, 0x3a63, 0x5157, 0x4c6a,
  0x4e64, 0xa330, 0x5158,
  /* 0x51C0 */
  0x2e55, 0x4028, 0x5159, 0x3d5a, 0x515a, 0x2e56, 0x437c, 0x4e3f,
  0x4560, 0x5245, 0x515b, 0x7425, 0x3645, 0x2e57, 0x515c, 0x4b5e,
  0x2e58, 0x3d68, 0x427c, 0x515e, 0x4664, 0x515f, 0x2e59, 0x5160,
  0x332e, 0xa333, 0xa334, 0x5161, 0x3627, 0x464c, 0x317a, 0x3d50,
  0x4821, 0x5162,
  /* 0x5200 */
  0x4561, 0x2e5a, 0xa335, 0x3f4f, 0x5163, 0x4a2c, 0x405a, 0x3422,
  0x3429, 0x5164, 0x5166, 0x373a, 0xa336, 0x2e5c, 0x5165, 0x2e5d,
  0xa337, 0x4e73, 0x3d69, 0x483d, 0x4a4c, 0x5167, 0x4d78, 0x5168,
  0x5169, 0x457e, 0x516a, 0x4029, 0x3a7e, 0x3774, 0x516b, 0x3b49,
  0x396f,
  /* 0x5240 */
  0x4466, 0x516d, 0x4227, 0x2e5e, 0x3a6f, 0x516e, 0x516f, 0x4130,
  0x516c, 0x5171, 0xa339, 0x4b36, 0x2e5f, 0x3964, 0xa33a, 0x2f7e,
  0x5170, 0x2e60, 0x3775, 0x3a5e, 0x476d, 0x5174, 0x5172, 0xa33b,
  0x497b, 0x3e6a, 0x517b, 0x3364, 0x5175, 0x5173, 0x414f, 0xa33c,
  0x5177, 0x5176,
  /* 0x5280 */
  0xa33e, 0x3344, 0xa33d, 0x3760, 0x517c, 0x4e2d, 0x5178, 0x517d,
  0x517a, 0x2e61, 0x5179, 0xa340, 0x4e4f, 0x3879, 0x3243, 0x4e74,
  0xa342, 0xa343, 0x3d75, 0x4558, 0x3965, 0x5222, 0x5223, 0xa344,
  0x4e65, 0x4f2b, 0x5225, 0x387a, 0xa345, 0xa346, 0x5224, 0x332f,
  /* 0x52C0 */
  0x5226, 0x4b56, 0x443c, 0x4d26, 0x2e62, 0x4a59, 0xa347, 0x2e64,
  0x5227, 0x2e65, 0xa349, 0x7055, 0x4630, 0x2e66, 0x5228, 0x342a,
  0x4c33, 0x2e67, 0x3e21, 0x5229, 0x4a67, 0x522d, 0x402a, 0x522a,
  0x3650, 0x522b, 0x342b, 0x2e69, 0x372e, 0x522e, 0x522f, 0xa34b,
  0x5230, 0x5231, 0x3c5b, 0x2e6a, 0x387b, 0x4c5e,
  /* 0x5300 */
  0x2e6b, 0x4c68, 0x4677, 0x4a71, 0x5232, 0x2e6c, 0x5233, 0xa34c,
  0xa34d, 0x5235, 0x5237, 0x5236, 0x5238, 0x323d, 0x4b4c, 0x3a7c,
  0x5239, 0x2e6d, 0x4159, 0x3e22, 0x3629, 0x523a, 0xa34e, 0x485b,
  0x523b, 0x523c, 0x523d, 0xa34f, 0x523e, 0x4924, 0x3668, 0x3065,
  0xa350, 0x463f,
  /* 0x5340 */
  0x523f, 0x3d3d, 0xa351, 0x4069, 0x5241, 0x5240, 0x3e23, 0x3861,
  0x5243, 0x483e, 0x5244, 0x485c, 0x4234, 0x426e, 0x3628, 0x466e,
  0x4331, 0x476e, 0x4b4e, 0x5246, 0x406a, 0x2e6f, 0x2e70, 0x3735,
  0xa354, 0x5247, 0xa355, 0x5248, 0x312c, 0x3075, 0x346d, 0x4228,
  0x3551, 0x4d71, 0x524b, 0x3237, 0xa356, 0x524a, 0x2e71, 0x362a,
  /* 0x5380 */
  0x524c, 0x4c71, 0x2e72, 0x524d, 0x4e52, 0x387c, 0x2e73, 0x3836,
  0x524e, 0xa357, 0x5250, 0x524f, 0x3f5f, 0x3139, 0x315e, 0x5251,
  0x5252, 0x2e74, 0x3837, 0xa358, 0x5253, 0xa35a, 0x356e,
  /* 0x53C0 */
  0xa35b, 0x3b32, 0x5254, 0x4b74, 0x3a35, 0x355a, 0x4d27, 0x4150,
  0x483f, 0x3c7d, 0x3d47, 0xa35f, 0x3c68, 0x3c75, 0x3d76, 0xa360,
  0x4840, 0x5257, 0x3143, 0x4151, 0x387d, 0x3845, 0x3667, 0x525b,
  0x4321, 0x427e, 0x362b, 0x3e24, 0x525c, 0x525a, 0x3244, 0x4266,
  0x3c38, 0x3b4b, 0x3126, 0xa362, 0xa363, 0x3370, 0x3966, 0x3b4a,
  0x525d,
  /* 0x5400 */
  0x525e, 0x3549, 0x3346, 0x3967, 0x3548, 0x445f, 0x3125, 0x4631,
  0x4c3e, 0x3921, 0x4d79, 0x4547, 0x387e, 0x2e75, 0x372f, 0x5267,
  0x4f7e, 0x3663, 0x4b4a, 0xa365, 0x485d, 0x2e76, 0xa366, 0x5266,
  0x345e, 0x5261, 0x5262, 0x5264, 0x5265, 0x355b, 0x3f61, 0x4a2d,
  0x5263, 0x525f, 0x3863,
  /* 0x5440 */
  0x5260, 0x4f24, 0xa368, 0x4a72, 0x4468, 0x3862, 0x3970, 0x2e77,
  0x5268, 0x465d, 0xa364, 0x526c, 0xa369, 0xa36a, 0x3c7e, 0x3c76,
  0x2e79, 0xa36b, 0x526f, 0x526d, 0x4c23, 0x2e7a, 0x526a, 0x5273,
  0x526e, 0x5271, 0x3846, 0x4c3f, 0x2e7b,
  /* 0x5480 */
  0x5272, 0x5274, 0x5276, 0x2e7c, 0xa36c, 0x3a70, 0x4f42, 0xa36d,
  0x526b, 0x5269, 0x5275, 0x5270, 0xa36e, 0x2e7d, 0x2e78, 0xa36f,
  0x2e7e, 0x5278, 0x5323, 0x527a, 0xa370, 0x527e, 0x2f21, 0x5321,
  0x527b, 0xa371, 0xa372, 0x533e, 0x3a69, 0x3331, 0xa373, 0x5279,
  0xa374, 0x5325, 0x3076, 0x5324, 0xa375,
  /* 0x54C0 */
  0x3025, 0x494a, 0x5322, 0xa376, 0x527c, 0x2f22, 0x5277, 0x527d,
  0x3a48, 0x5326, 0x3077, 0x532f, 0x5327, 0x5328, 0x3e25, 0x4b69,
  0xa378, 0x532d, 0x532c, 0xa379, 0xa37a, 0x452f, 0xa37b, 0x532e,
  0x532b, 0x2f23,
  /* 0x5500 */
  0xa37c, 0xa37d, 0x3134, 0x3a36, 0x3f30, 0xa37e, 0x2f24, 0x5329,
  0x4562, 0x532a, 0x3022, 0x2f25, 0x5334, 0x4d23, 0x3e27, 0x533a,
  0x2f26, 0x5339, 0x5330, 0xa421, 0x4243,
  /* 0x5540 */
  0x5331, 0xa422, 0x426f, 0x5336, 0x3e26, 0xa424, 0xa425, 0x5333,
  0x4c64, 0x2f27, 0x373c, 0x5337, 0x5338, 0x5335, 0x533b, 0x2f28,
  0xa427, 0xa428, 0x5332, 0xa429, 0x5341, 0x5346, 0xa42b, 0x5342,
  /* 0x5580 */
  0x533d, 0x2f29, 0xa42c, 0x5347, 0x4131, 0x2f2a, 0x5349, 0xa42d,
  0x3922, 0x533f, 0x437d, 0x2f2b, 0xa42e, 0x5343, 0x533c, 0x342d,
  0x346e, 0x3365, 0x5344, 0x5340, 0x3776, 0x534a, 0x5348, 0x4153,
  0x354a, 0x362c, 0x2f2d, 0x5345, 0x3674, 0x3144, 0xa433,
  /* 0x55C0 */
  0x534e, 0x534c, 0x5427, 0xa434, 0xa435, 0x2f2e, 0xa436, 0xa430,
  0x5351, 0x534b, 0x534f, 0xa437, 0x534d, 0xa439, 0x3b4c, 0x5350,
  0xa43b, 0x5353, 0x5358, 0x5356, 0x5355,
  /* 0x5600 */
  0x4332, 0xa43e, 0x2f30, 0x3245, 0x2f31, 0xa43f, 0x5352, 0x5354,
  0x3e28, 0x3133, 0x5357, 0xa43c, 0x325e, 0x5362, 0xa440, 0x3e7c,
  0x535e, 0x535c, 0x535d, 0xa441, 0x535f, 0x2f32, 0xa443, 0xa444,
  /* 0x5640 */
  0xa445, 0x313d, 0xa446, 0x2f33, 0x4139, 0x5359, 0x535a, 0x7427,
  0x337a, 0xa447, 0xa448, 0x5361, 0x2f35, 0x346f, 0x5364, 0x5360,
  0x5363, 0xa449, 0x2f37, 0x2f38, 0x2f39, 0x4a2e, 0x2f34, 0x4655,
  0x4838,
  /* 0x5680 */
  0x5366, 0x5365, 0x3345, 0xa44b, 0x5367, 0xa44c, 0x536a, 0x5369,
  0xa44d, 0x2f3a, 0xa44e, 0xa44f, 0x2f3b, 0x5368, 0x4739, 0x536b,
  0xa450, 0x2f3c, 0x2f3d, 0xa451, 0x536c, 0xa452, 0x2f3e, 0x536e,
  0x536d, 0x5370,
  /* 0x56C0 */
  0x5373, 0x5371, 0x536f, 0x5372, 0xa453, 0x5374, 0x2f3f, 0x2f40,
  0xa454, 0x5375, 0x5376, 0x5377, 0x5378, 0x5145, 0x3c7c, 0x3b4d,
  0x3273, 0xa455, 0x3078, 0x4344, 0xa456, 0x5379, 0x3a24, 0x304f,
  0x3f5e, 0xa457, 0xa458, 0x537a, 0x3847, 0x3971, 0x537c,
  /* 0x5700 */
  0x537b, 0x4a60, 0x537d, 0x5421, 0x537e, 0x2f41, 0x5422, 0x5423,
  0x3777, 0x3160, 0x5424, 0xa45a, 0x5426, 0x5425, 0x5428, 0x455a,
  0x2f43, 0xa45b, 0x5429, 0x3035, 0x3a5f, 0xa45d, 0x373d, 0x2f44,
  0x434f, 0x2f45, 0x2f46, 0x542a, 0x542b, 0x542d,
  /* 0x5740 */
  0x542e, 0x3a64, 0xa45f, 0xa460, 0x3651, 0x4b37, 0xa461, 0xa462,
  0x542c, 0x542f, 0x3a41, 0x3923, 0x5433, 0x3a25, 0x4333, 0xa464,
  0x5430, 0x445a, 0xa465, 0x2f47, 0xa466, 0xa467, 0xa468, 0x2f48,
  0xa469, 0x2f49, 0x5434,
  /* 0x5780 */
  0x3f62, 0x5432, 0x5435, 0x373f, 0x5436, 0xa46d, 0x2f4a, 0xa46e,
  0xa46f, 0x5437, 0x3924, 0x3340, 0x5439, 0xa470, 0x543a, 0xa46c,
  0x543b, 0x5438, 0x2f4d,
  /* 0x57C0 */
  0x5431, 0x543c, 0x543d, 0x2f4e, 0x2f4f, 0x4b64, 0xa473, 0x3e6b,
  0x2f50, 0x543f, 0x5440, 0x543e, 0x5442, 0xa471, 0x4738, 0xa476,
  0x3068, 0x4956, 0x5443, 0x2f51, 0xa477, 0x2f52, 0xa478, 0x3e7d,
  0x2f53, 0x2f54, 0x3c39, 0xa47a, 0x475d, 0x3470, 0xa47b, 0x3a6b,
  0xa47c, 0x2f55,
  /* 0x5800 */
  0x4b59, 0x4632, 0xa47d, 0x3778, 0x424f, 0x2f56, 0x5441, 0x5444,
  0x4244, 0x5445, 0x5446, 0xa47e, 0xa521, 0x5448, 0x4469, 0xa522,
  0x342e, 0x7421, 0x3161, 0x4a73, 0xa523, 0x3e6c, 0x4548, 0xa524,
  0x3a66, 0x544e,
  /* 0x5840 */
  0x4a3d, 0x4e5d, 0xa526, 0x3274, 0x544a, 0xa527, 0x413a, 0x544d,
  0x4563, 0x4549, 0x4564, 0x4839, 0x444d, 0x3a49, 0x2f58, 0x5449,
  0x2f59, 0xa528, 0x3176, 0x4536, 0x544b, 0x5447, 0x3f50, 0x544f,
  0x2f5b, 0x3d4e,
  /* 0x5880 */
  0x362d, 0x5450, 0x2f5c, 0xa529, 0xa52a, 0xa52b, 0xa52c, 0xa52d,
  0x4a68, 0xa52e, 0x417d, 0x4446, 0xa52f, 0x2f5d, 0x5452, 0x4b4f,
  0x2f5f, 0xa530, 0x5453, 0x5458, 0xa531, 0x4a2f, 0x5457, 0x5451,
  0x5454, 0x5456, 0x3a26,
  /* 0x58C0 */
  0x4a49, 0xa533, 0x5459, 0x4345, 0x3275, 0x3e6d, 0xa534, 0x2f62,
  0x545b, 0x2f61, 0x545a, 0x2f63, 0x3968, 0x545c, 0x545e, 0x545d,
  0x2f64, 0x5460, 0x5455, 0x5462, 0x2f65, 0xa535, 0x5461, 0x545f,
  0x2f66, 0x3b4e, 0x3f51, 0x4154, 0x5463, 0x403c, 0x306d, 0x4764,
  0xa536, 0xa537, 0x445b, 0x5465, 0x5464, 0x5466, 0x5467, 0x5468,
  /* 0x5900 */
  0x5469, 0xa538, 0xa539, 0x4a51, 0x546a, 0xa53a, 0x2f67, 0xa53b,
  0x3246, 0x546b, 0xa53c, 0x4d3c, 0x3330, 0x5249, 0x3d48, 0x423f,
  0x546c, 0x4c6b, 0x4c34, 0xa53d, 0x546e, 0x4267, 0x4537, 0x4240,
  0x4957, 0x546f, 0x5470, 0x317b, 0x3c3a, 0x5471, 0x3050, 0x5472,
  0xa540, 0x5473,
  /* 0x5940 */
  0x3162, 0xa542, 0x3471, 0x4660, 0x4a74, 0x5477, 0x4155, 0x5476,
  0x3740, 0x4b5b, 0x5475, 0x4565, 0x5479, 0x5478, 0xa545, 0x2f69,
  0xa546, 0x547b, 0x547a, 0x317c, 0x547c, 0x3e29, 0x547e, 0x4325,
  0x547d, 0x2f6a, 0x4a33, 0x3d77, 0x455b, 0xa548, 0xa549, 0x5521,
  0xa54a, 0x3925,
  /* 0x5980 */
  0x5522, 0x4721, 0x485e, 0x4c51, 0x4725, 0x2f6b, 0x552b, 0x2f6c,
  0x3538, 0x4d45, 0x4c2f, 0x562c, 0x5523, 0xa54b, 0x5526, 0x2f6d,
  0x4245, 0x4b38, 0x454a, 0xa54c, 0x5527, 0x4b65, 0x3a4a, 0xa54d,
  0x3e2a,
  /* 0x59C0 */
  0x2f6e, 0x5528, 0xa54e, 0x3b50, 0x3b4f, 0xa54f, 0x3039, 0x3848,
  0x2f6f, 0x402b, 0x3051, 0x552c, 0x552d, 0x552a, 0x2f70, 0xa550,
  0xa551, 0xa552, 0x3138, 0x342f, 0xa553, 0x5529, 0x4c45, 0x4931,
  0xa554, 0x3028, 0x7e7a, 0x3079, 0x3b51,
  /* 0x5A00 */
  0x3052, 0x3023, 0x5532, 0xa558, 0xa559, 0x5530, 0x2f71, 0xa55a,
  0x4c3c, 0x5533, 0x5531, 0x552f, 0x3f31, 0x2f72, 0x552e, 0xa55b,
  0x4a5a, 0xa55c, 0x3864, 0x5537, 0x5538, 0x3e2b,
  /* 0x5A40 */
  0x5534, 0x4f2c, 0x474c, 0x5536, 0xa55d, 0x3a27, 0x5539, 0xa55e,
  0x4958, 0x2f73, 0x553a, 0x5535, 0x2f74, 0x2f75, 0xa55f, 0x2f76,
  0x4c3b,
  /* 0x5A80 */
  0x2f77, 0xa560, 0x475e, 0x553b, 0x4932, 0xa561, 0x2f78, 0xa562,
  0xa563, 0xa564, 0x2f79, 0xa565, 0xa566, 0xa567, 0xa568, 0x553c,
  0x5540, 0x553d, 0xa569,
  /* 0x5AC0 */
  0x3247, 0x553f, 0x2f7a, 0x3c3b, 0x553e, 0x3779, 0x554c, 0x5545,
  0x5542, 0xa56a, 0xa56b, 0xa56c, 0x4364, 0x5541, 0xa56d, 0x5543,
  0x5544, 0xa56f, 0xa56e, 0xa570, 0x5546, 0x5547,
  /* 0x5B00 */
  0xa571, 0xa572, 0x3472, 0x5549, 0x5548, 0x554a, 0xa573, 0x2f7c,
  0x3e6e, 0x2f7d, 0x554d, 0x445c, 0xa575, 0x3145, 0x554b, 0xa574,
  0x554e, 0x554f,
  /* 0x5B40 */
  0x5552, 0x4f55, 0x5550, 0x5551, 0xa576, 0x3b52, 0x5553, 0xa577,
  0x3926, 0x5554, 0x4f56, 0x3b7a, 0x4238, 0x5555, 0x5556, 0x3b5a,
  0x3927, 0x4c52, 0x3528, 0x3849, 0x5557, 0x3358, 0xa578, 0x5558,
  0x4239, 0xa579, 0x5559, 0x5623, 0x555a, 0x555b, 0x555c, 0x555e,
  0xa57a, 0x4f57, 0xa57b,
  /* 0x5B80 */
  0x555f, 0xa57c, 0x5560, 0xa57d, 0x4270, 0x3127, 0x3c69, 0x3042,
  0x4157, 0x3430, 0x3c35, 0x3928, 0x4f58, 0x4566, 0xa821, 0x3d21,
  0x3431, 0x4368, 0x446a, 0x3038, 0x3539, 0x4a75, 0x3c42, 0x3552,
  0x406b, 0x3c3c, 0x4d28, 0x5561, 0xa822, 0x355c, 0x3a4b, 0x3332,
  0x3163, 0x3e2c, 0x3248, 0x5562, 0x4d46, 0x3d49,
  /* 0x5BC0 */
  0xa824, 0x3c64, 0x5563, 0x3473, 0x4652, 0x4c29, 0x5564, 0x5565,
  0x4959, 0xa826, 0x5567, 0x3428, 0x3677, 0x5566, 0xa827, 0x4f59,
  0x3432, 0x3f32, 0x556b, 0x3b21, 0x3249, 0x556a, 0x5568, 0x556c,
  0x5569, 0x472b, 0x5c4d, 0x3f33, 0x556d, 0x4f5a, 0x4e40, 0x556e,
  0xa82a, 0x5570, 0x437e, 0x556f, 0x4023, 0x3b7b, 0xa82b, 0x4250,
  0x3c77,
  /* 0x5C00 */
  0x4975, 0x406c, 0xa82d, 0x3c4d, 0x5571, 0x3e2d, 0x5572, 0x5573,
  0x3053, 0x423a, 0x3f52, 0x5574, 0x4633, 0x3e2e, 0x3e2f, 0x4f5b,
  0x5575, 0x406d, 0x3e30, 0x4f5c, 0x5576, 0x5577, 0x4f5d, 0x4c60,
  0x5578, 0xa82e, 0x4f5e, 0x3646, 0xa82f, 0x3d22, 0x5579, 0x557a,
  0x3c5c, 0x3f2c, 0x4674, 0x3f54, 0x4878, 0x4722,
  /* 0x5C40 */
  0x3649, 0x557b, 0x356f, 0x557c, 0x367e, 0x464f, 0x3230, 0x3b53,
  0x557d, 0x5622, 0x5621, 0x367d, 0x557e, 0x4538, 0x7e7b, 0x4230,
  0xa831, 0x454b, 0x3c48, 0x4f60, 0xa832, 0x4158, 0x4d7a, 0xa833,
  0xa834, 0xa835, 0x5624, 0x5625, 0x4656, 0xa836, 0x3b33, 0x5627,
  0x5628, 0x4f64, 0xa839,
  /* 0x5C80 */
  0xa83c, 0xa83d, 0x5629, 0x4f65, 0x3474, 0x562a, 0x562b, 0x4f66,
  0xa841, 0x322c, 0xa842, 0x4f67, 0xa843, 0xa844, 0x413b, 0x3464,
  0x4f68, 0x562d, 0x4c28, 0xa846, 0x4252, 0x3359, 0xa847, 0x562f,
  0x5631, 0x345f, 0x4f69, 0x562e, 0x5630, 0x5633,
  /* 0x5CC0 */
  0x5632, 0x5634, 0xa849, 0x4f6a, 0x4f6b, 0x4f6c, 0x5635, 0x463d,
  0x362e, 0x3265, 0x5636, 0x563b, 0x5639, 0x4a77, 0x4a76, 0x4f6d,
  0x4567, 0x5638, 0x3d54, 0x5637,
  /* 0x5D00 */
  0xa84c, 0x3f72, 0x563c, 0x4f70, 0x3a6a, 0xa84d, 0x5642, 0x5643,
  0x563d, 0x3333, 0x563e, 0x5647, 0x5646, 0x5645, 0x5641, 0xa84f,
  0x5640, 0xa850, 0x5644, 0xa851, 0xa852, 0x4f71, 0x4a78, 0xa84e,
  0xa853, 0xa854,
  /* 0x5D40 */
  0xa855, 0x4f73, 0x4f74, 0x4f76, 0x564b, 0x5648, 0x564a, 0x4d72,
  0x5649, 0x4f75, 0x563f, 0xa857, 0x3f73, 0xa858, 0x564c, 0x4f77,
  0x3a37, 0xa85a, 0x564d, 0x564e,
  /* 0x5D80 */
  0x4f78, 0x5651, 0x5650, 0x564f, 0xa85d, 0x4568, 0x563a, 0x5657,
  0xa85f, 0xa860, 0xa861, 0xa862, 0x5653, 0x4f79, 0x5652, 0x4f7a,
  0x4f7b, 0x5654, 0x5655, 0xa863, 0xa864, 0xa865, 0x5658, 0x4f7c,
  0xa867, 0x4e66, 0x5659, 0x5656,
  /* 0x5DC0 */
  0x565a, 0x4f7d, 0x3460, 0x565b, 0xa868, 0x565d, 0x565c, 0x565e,
  0xa869, 0xa86a, 0x565f, 0x406e, 0x3d23, 0xa86b, 0x3d64, 0x7428,
  0x4163, 0xa86d, 0x3929, 0x3a38, 0x392a, 0x3570, 0xa86e, 0x5660,
  0x3a39, 0x384a, 0x5661, 0x4c26, 0x4743, 0x5662, 0x392b, 0x342c,
  0x4327, 0x3652,
  /* 0x5E00 */
  0xa870, 0x3b54, 0x495b, 0x4841, 0x5663, 0x3475, 0x5666, 0xa872,
  0x7429, 0xa873, 0x4421, 0x742a, 0x5665, 0x5664, 0x5667, 0x446b,
  0xa875, 0x3f63, 0x3b55, 0x404a, 0xa876, 0x4253, 0x3522, 0x4422,
  0x5668, 0x5669, 0x3e6f, 0x4b39, 0xa877,
  /* 0x5E40 */
  0x566c, 0x566b, 0x566a, 0x497d, 0x5673, 0xa878, 0x4b5a, 0x566d,
  0x566f, 0x4b6b, 0xa87a, 0x566e, 0x742b, 0x742c, 0x5670, 0x4828,
  0x5671, 0x4a3e, 0x5672, 0xa87c, 0xa87d, 0xa87e, 0xac21, 0x3433,
  0x4a3f, 0x472f, 0x5674, 0x5675, 0x7e7c, 0x392c, 0x3434, 0x5676,
  0x3838, 0x4d44, 0x4d29, 0x3476, 0x5678,
  /* 0x5E80 */
  0x4423, 0x392d, 0x3e31, 0x485f, 0x3e32, 0x3d78, 0x446c, 0x4a79,
  0x4539, 0x392e, 0x495c, 0x5679, 0xac23, 0x4559, 0x3a42, 0xac24,
  0x384b, 0xac25, 0x446d, 0x3043, 0x3d6e, 0x392f, 0x4d47, 0xac26,
  0x742d, 0xac27,
  /* 0x5EC0 */
  0x567a, 0x567b, 0x4751, 0xac28, 0x567c, 0x4e77, 0x4f2d, 0x742f,
  0x567e, 0x567d, 0xac29, 0x3347, 0x5721, 0xac2a, 0x5724, 0x5725,
  0x5723, 0x4940, 0x3e33, 0x5727, 0x5726, 0x5722, 0x5728, 0x5729,
  0x572a, 0x572d, 0x572b, 0x572c, 0x572e, 0x3164, 0x446e, 0x572f,
  0x7430, 0x377a, 0x3276, 0x4736, 0xac2c, 0x5730, 0x467b,
  /* 0x5F00 */
  0x7431, 0x4a5b, 0x7432, 0x5731, 0x4f2e, 0x7433, 0xac2d, 0x5732,
  0x4a40, 0x5735, 0x5021, 0x5031, 0xac2e, 0x3c30, 0x4675, 0x5736,
  0x355d, 0x4424, 0x307a, 0x5737, 0x4a26, 0x3930, 0x4350, 0xac2f,
  0x7434, 0xac31, 0x446f, 0x7435, 0x4c6f, 0x3839, 0x384c, 0x5738,
  0x5739, 0x573f, 0x3c65, 0x7436, 0x4425, 0x7437, 0x362f, 0x573a,
  0x492b, 0x7438, 0x4346,
  /* 0x5F40 */
  0x7439, 0x573b, 0x743a, 0xac32, 0x573c, 0x3630, 0x573d, 0x573e,
  0x5740, 0x4576, 0x743b, 0x5741, 0x5742, 0x743c, 0x5743, 0x5734,
  0x5733, 0x5744, 0x3741, 0xac33, 0x743d, 0x4927, 0x743e, 0x3a4c,
  0x4937, 0x4426, 0x494b, 0x5745, 0x3e34, 0x3146, 0xac34, 0x5746,
  0x5747, 0x4c72, 0x4860, 0x743f, 0xac35, 0x574a,
  /* 0x5F80 */
  0x317d, 0x402c, 0x5749, 0x5748, 0x3742, 0x4254, 0x574e, 0x574c,
  0x7440, 0x574b, 0x4e27, 0x3865, 0xac36, 0x3d79, 0x574d, 0x454c,
  0x3d3e, 0x4640, 0x5751, 0x5750, 0x7441, 0x574f, 0x5752, 0x3866,
  0xac37, 0xac38, 0x7442, 0x5753, 0x497c, 0x3d5b, 0x5754, 0x4879,
  0x7443, 0x4641, 0x4427, 0x7444, 0x7445, 0xac39, 0x4530, 0x5755,
  0x352b,
  /* 0x5FC0 */
  0x3f34, 0xac3a, 0x492c, 0xac3c, 0x7446, 0xac3d, 0x3477, 0x4726,
  0xac3e, 0xac3f, 0xac40, 0x5756, 0x3b56, 0x4b3a, 0x4b3b, 0x317e,
  0x575b, 0x7447, 0x4369, 0x7448, 0xac41, 0x5758, 0x7449, 0x3277,
  0xac42, 0xac43, 0x582d, 0x575a, 0xac44, 0x4730, 0x5759, 0x5757,
  0xac45, 0x397a, 0x575d,
  /* 0x6000 */
  0x744a, 0x5763, 0x5769, 0x5761, 0x455c, 0x744b, 0x5766, 0x495d,
  0xac47, 0x744c, 0x5760, 0x5765, 0x4e67, 0x3b57, 0x4255, 0x575e,
  0xac48, 0xac49, 0x355e, 0x5768, 0x402d, 0x3165, 0x5762, 0x3278,
  0x5767, 0x3631, 0x5764, 0x744d, 0x744e, 0x576a,
  /* 0x6040 */
  0x576c, 0x5776, 0x5774, 0x5771, 0x744f, 0x5770, 0x4e78, 0xac4b,
  0x5772, 0x3632, 0x3931, 0x3d7a, 0x5779, 0x576b, 0x576f, 0x575f,
  0x327a, 0x5773, 0x5775, 0x4351, 0x3a28, 0x3238, 0x576d, 0x5778,
  0x5777, 0x3633, 0x4229, 0x3366, 0x3743, 0x576e, 0xac4c,
  /* 0x6080 */
  0x577a, 0x577d, 0x5821, 0x3c3d, 0xac4d, 0x5827, 0x4470, 0x577b,
  0x5825, 0x3279, 0xac4e, 0x5823, 0x5824, 0x577e, 0x5822, 0x7451,
  0x7452, 0x3867, 0x4d2a, 0x3435, 0x3159, 0x5826, 0xac4f, 0x473a,
  0x302d, 0xac51, 0xac52, 0x4861, 0x575c, 0x582c, 0x5830, 0x4c65,
  0x5829, 0x4569, 0x582e, 0xac53,
  /* 0x60C0 */
  0x3e70, 0x582f, 0x4657, 0xac54, 0x7453, 0x4f47, 0x582b, 0x7454,
  0x7455, 0x5831, 0xac55, 0x397b, 0xac56, 0x404b, 0x7456, 0x3054,
  0x582a, 0x5828, 0x415a, 0x577c, 0x3b34, 0xac57, 0x4246, 0x583d,
  0xac58, 0x415b, 0x5838, 0xac59, 0x5835, 0x5836, 0x7457, 0x3c66,
  0x5839, 0x583c,
  /* 0x6100 */
  0x5837, 0x3d25, 0x583a, 0x5834, 0x4c7c, 0x4c7b, 0x583e, 0x583f,
  0x3055, 0xac5a, 0xac5b, 0xac5c, 0x5833, 0xac5d, 0x3672, 0x3026,
  0x7458, 0xac5e, 0x3436, 0x583b, 0x5843, 0x5842, 0x7459, 0x5847,
  0x745a, 0x5848, 0x745b, 0xac5f, 0x5846, 0x5849, 0x5841, 0x5845,
  /* 0x6140 */
  0xac61, 0x584a, 0x584b, 0xac62, 0x5840, 0x3b7c, 0x5844, 0x4256,
  0x3932, 0x5832, 0x3f35, 0x5858, 0x4a69, 0x584e, 0x584f, 0x5850,
  0x5857, 0x5856, 0xac63, 0x4b7d, 0x3437, 0x5854, 0x3745, 0x3334,
  0x5851, 0x4e38, 0x5853, 0x3056, 0x5855, 0x584c, 0x5852, 0x5859,
  0x3744, 0x584d, 0xac64, 0x4d5d,
  /* 0x6180 */
  0x4d2b, 0x585c, 0x5860, 0x745d, 0x417e, 0x4e79, 0x5861, 0xac66,
  0xac67, 0x585e, 0x585b, 0xac68, 0xac69, 0x585a, 0x585f, 0x4a30,
  0xac6a, 0x4634, 0xac6b, 0x3746, 0x5862, 0x585d, 0xac6c, 0x5863,
  0x377b, 0x3231, 0x7460, 0x586b, 0x745f, 0x3438,
  /* 0x61C0 */
  0x5869, 0x586a, 0x3a29, 0x5868, 0x5866, 0x5865, 0x586c, 0x5864,
  0x586e, 0x327b, 0xac6e, 0xac6f, 0xac70, 0x5870, 0x586f, 0x4428,
  0x5873, 0xac71, 0x5871, 0x5867, 0x377c, 0x5872, 0x5876, 0x5875,
  0x5877, 0x5874,
  /* 0x6200 */
  0x5878, 0x5879, 0x587a, 0x4a6a, 0x587c, 0x587b, 0x3d3f, 0x402e,
  0x3266, 0x327c, 0x587d, 0xac73, 0x303f, 0x404c, 0x587e, 0x6c43,
  0x5921, 0x3761, 0x5922, 0x7462, 0xac74, 0x406f, 0xac75, 0x5923,
  0x5924, 0x353a, 0x5925, 0x5926, 0x5927, 0x4257, 0x384d, 0x4c61,
  0x7463, 0x4b3c,
  /* 0x6240 */
  0x3d6a, 0x5928, 0x7464, 0xac76, 0x4070, 0x6e3d, 0x4862, 0x3c6a,
  0xac77, 0x3a4d, 0x5929, 0xac78, 0xac79, 0x4247, 0x4a27, 0x7465,
  0x4271, 0x7466, 0x592c, 0x592a, 0x592d, 0xac7a, 0x592b, 0xac7b,
  0x592e, 0xac7d, 0x4a31, 0x7467, 0x3037, 0xac7e, 0x495e, 0x4863,
  0xac7c, 0x592f, 0x5932, 0x3e35,
  /* 0x6280 */
  0x353b, 0x5930, 0x5937, 0x3e36, 0x7468, 0x5931, 0x4744, 0x4d5e,
  0x5933, 0x5934, 0x5938, 0x456a, 0x5935, 0x3933, 0x405e, 0xad21,
  0x5946, 0x4834, 0x4272, 0xad22, 0x4864, 0x5a2d, 0x4a7a, 0x4471,
  0x4b75, 0x593b, 0x3221, 0x436a,
  /* 0x62C0 */
  0x5944, 0x7469, 0x4334, 0x593e, 0x5945, 0x5940, 0x5947, 0x5943,
  0x5942, 0x476f, 0x593c, 0x327d, 0x593a, 0x3571, 0x4273, 0x5936,
  0xad23, 0x746a, 0x5939, 0x3934, 0x405b, 0x3e37, 0x5941, 0x4752,
  0x3572, 0x3348, 0x3367, 0x3f21, 0x5949, 0x594e, 0x594a, 0x377d,
  0x594f, 0x3b22, 0x3969, 0x746b, 0xad25, 0x3d26, 0x593d,
  /* 0x6300 */
  0x3b7d, 0x594c, 0xad26, 0x3b58, 0x594d, 0x3044, 0x746c, 0x5948,
  0xad27, 0xad28, 0x4429, 0x746d, 0x3573, 0x3634, 0x594b, 0x3027,
  0x3a43, 0x3f36, 0xad2b, 0xad2c, 0x746e, 0x4472, 0xad2d, 0xad2e,
  0x4854, 0x5951, 0x415e,
  /* 0x6340 */
  0xad2f, 0x746f, 0xad30, 0x422a, 0x3b2b, 0x5952, 0xad31, 0x5954,
  0x5950, 0x4a61, 0x443d, 0xad33, 0x415c, 0x7470, 0x4a7b, 0x3c4e,
  0x5960, 0x595f, 0xad36, 0x3f78, 0x377e, 0x5959, 0x3e39, 0x4668,
  0x4731, 0x7471,
  /* 0x6380 */
  0x5957, 0x415d, 0xad37, 0x3c78, 0x595c, 0x3e38, 0x5956, 0x595b,
  0x4753, 0xad3a, 0x5955, 0x3721, 0xad38, 0x335d, 0x595d, 0x4e2b,
  0x3a4e, 0x4335, 0x595a, 0x405c, 0x3935, 0x3f64, 0x3166, 0x413c,
  0x5958, 0x3545, 0x3747, 0x444f, 0x595e, 0x415f, 0xad3b, 0x5961,
  /* 0x63C0 */
  0x5963, 0x4237, 0x5969, 0x5964, 0x5966, 0x4941, 0x4473, 0x5967,
  0xad3d, 0xad3e, 0x4d2c, 0x4d48, 0x3439, 0xad3f, 0xad40, 0x302e,
  0x5965, 0x7472, 0x5962, 0xad41, 0xad42, 0x7473, 0x3478, 0xad43,
  0x3167, 0x7474, 0x5968, 0xad3c, 0x4d49,
  /* 0x6400 */
  0x596c, 0xad44, 0x423b, 0x5973, 0x7475, 0x596d, 0x7476, 0x596a,
  0x5971, 0x5953, 0xad45, 0x7477, 0xad46, 0x596e, 0x5972, 0xad47,
  0x4842, 0x456b, 0xad48, 0x596b, 0x596f, 0x3748, 0x3a71,
  /* 0x6440 */
  0x405d, 0x5977, 0x7479, 0x4526, 0xad49, 0xad4a, 0xad4b, 0x747a,
  0x5974, 0x4b60, 0x747b, 0x5975, 0xad4c, 0x5976, 0x4c4e, 0x7478,
  0x4022, 0xad4d,
  /* 0x6480 */
  0x3762, 0xad4e, 0x597d, 0xad4f, 0x3b35, 0x597a, 0x5979, 0x4732,
  0xad50, 0x4635, 0xad51, 0x4531, 0x597b, 0x597c, 0x496f, 0x4745,
  0x3b23, 0x4071, 0x4b50, 0x3349, 0x5a25, 0x597e, 0x747d, 0x747e,
  /* 0x64C0 */
  0x4d4a, 0x5a27, 0x7521, 0x5a23, 0x5a24, 0x7522, 0xad52, 0xad53,
  0x4160, 0x747c, 0x7523, 0x5a22, 0x593f, 0xad54, 0xad55, 0x5a26,
  0x5a21, 0x5a2b, 0x5a2c, 0x4527, 0x5a2e, 0xad57, 0xad58, 0x3b24,
  0x5a29, 0x353c, 0x5a2f, 0x5a28, 0x5a33, 0x5a32, 0x5a31, 0x7524,
  0x5a34, 0x7525, 0x5a36, 0x3e71, 0xad59,
  /* 0x6500 */
  0x5a35, 0xad5a, 0x5a39, 0xad5c, 0xad5d, 0xad5e, 0x5a37, 0x5a38,
  0x5970, 0xad60, 0x7526, 0x5a3b, 0x5a3a, 0x7527, 0x5978, 0x5a3c,
  0x5a30, 0x3b59, 0xad61, 0x5a3d, 0x5a3e, 0x5a40, 0x5a3f, 0x5a41,
  0x327e, 0x3936, 0x4a7c, 0x402f,
  /* 0x6540 */
  0xad62, 0x384e, 0x5a43, 0x5a46, 0x4952, 0x355f, 0xad63, 0x5a45,
  0x5a44, 0x4754, 0x5a47, 0x3635, 0x5a49, 0x5a48, 0x343a, 0x3b36,
  0x4658, 0x7529, 0xad64, 0x3749, 0x3f74, 0x5a4a, 0x4030, 0x4528,
  0x495f, 0x5a4b, 0xad65,
  /* 0x6580 */
  0xad66, 0x5a4c, 0x5a4d, 0xad67, 0xad68, 0x4a38, 0x555d, 0x4046,
  0xad69, 0x494c, 0x3a58, 0x4865, 0x4843, 0x454d, 0x4e41, 0x5a4f,
  0x3c50, 0x752a, 0x5a50, 0x3036, 0x3654, 0x404d, 0x4960, 0x5a51,
  0x3b42, 0x4347, 0x3b5b, 0x3f37, 0xad6a, 0xad6b, 0x5a52, 0xad6c,
  0x4a7d, 0x3177, 0x3b5c, 0xad6d,
  /* 0x65C0 */
  0x5a55, 0xad6e, 0x5a53, 0x5a56, 0x4e39, 0x5a54, 0xad6f, 0x407b,
  0x5a57, 0x4232, 0x5a58, 0xad70, 0x347a, 0x5a5a, 0x5a59, 0x5a5b,
  0x5a5c, 0x347b, 0x467c, 0x4336, 0x356c, 0x3b5d, 0x4161, 0x3d5c,
  0x3030, 0x5a5d, 0xad72, 0xad73, 0x3222, 0x5a61, 0xad74,
  /* 0x6600 */
  0x752c, 0x3937, 0x5a60, 0xad75, 0x3a2b, 0x3e3a, 0xad76, 0x752d,
  0x5a5f, 0x3e3b, 0x4c40, 0x3a2a, 0x3057, 0x404e, 0x752e, 0x5a66,
  0x752f, 0x4031, 0x3147, 0xad77, 0x7531, 0x7532, 0x3d55, 0x4b66,
  0x3a72, 0xad78, 0x7533, 0x3e3c, 0x4027, 0x7534, 0x7535, 0x7536,
  0x5a65, 0x5a63, 0x5a64, 0x7530, 0x436b, 0x5b26,
  /* 0x6640 */
  0x5a6a, 0x3b7e, 0x3938, 0x5a68, 0xad79, 0x7538, 0x5a69, 0x3f38,
  0x7539, 0xad7b, 0x5a67, 0xad7a, 0x3b2f, 0xad7e, 0x753b, 0x753c,
  0xae21, 0x5a6c, 0x5a6b, 0x5a70, 0x753d, 0x5a71, 0xae22, 0x5a6d,
  0x753e, 0x3322, 0x5a6e, 0x5a6f, 0x4855, 0xae25, 0xae26, 0xae27,
  0xae28, 0x4961, 0x374a, 0x5a72, 0x753f, 0x4032, 0x3e3d, 0x7540,
  0x7541, 0x4352, 0xae29,
  /* 0x6680 */
  0xae2a, 0x3647, 0x5a73, 0x5a77, 0x324b, 0x5a74, 0x5a76, 0x7542,
  0x5a75, 0xae2b, 0x3d6b, 0xae2c, 0x4348, 0x3045, 0x5a78, 0xae2d,
  0x5a79, 0x7544, 0x442a, 0x4e71, 0x3b43, 0xae2f, 0x4a6b, 0xae30,
  0x7545, 0x4b3d, 0xae31, 0x5b22, 0x5a7b, 0x7546, 0x5a7e, 0x5a7d,
  0xae33,
  /* 0x66C0 */
  0x5a7a, 0x5b21, 0x7547, 0x465e, 0x7548, 0x5a7c, 0x5b23, 0x3d6c,
  0x5b24, 0x754a, 0x4d4b, 0x4778, 0x5b25, 0x5b27, 0x754b, 0x5b28,
  0xae35, 0x5b29, 0x364a, 0x3148, 0x3939, 0x5b2a, 0x5b2b, 0x3d71,
  0x4162, 0x754c, 0x7537, 0x5258, 0x413e, 0x413d, 0x4258,
  /* 0x6700 */
  0x3a47, 0xae37, 0x5072, 0xae38, 0x376e, 0x4d2d, 0x4a7e, 0x497e,
  0x5b2c, 0xae39, 0x754d, 0x3a73, 0x443f, 0x5b2d, 0x4f2f, 0xae3b,
  0x4b3e, 0x442b, 0x5b2e, 0x347c, 0x5b2f, 0x5b30, 0x4c5a, 0x4c24,
  0x4b76, 0x4b5c, 0x3b25, 0x5b32, 0x3c6b, 0x754f, 0x4b51, 0x5b34,
  0x5b37, 0x5b36, 0x3479, 0x3560, 0x5b33,
  /* 0x6740 */
  0x5b35, 0x5b38, 0x7551, 0x7552, 0x3f79, 0xae3e, 0xae3f, 0x4d7b,
  0x3049, 0x3a60, 0x423c, 0x3c5d, 0xae40, 0x3e73, 0x5b3b, 0x454e,
  0xae41, 0x5b39, 0x422b, 0x5b3a, 0x3e72, 0x4c5d, 0x5b3c, 0x5b3d,
  0x4d68, 0x7550, 0x5b42, 0x393a, 0x4755, 0x5b3f, 0x456c, 0x5a5e,
  0x5a62, 0xae45, 0x354f, 0xae46, 0x4747, 0x7553, 0x5b41, 0x3e3e,
  0x4844,
  /* 0x6780 */
  0x7554, 0x5b47, 0x487a, 0x5b3e, 0x5b44, 0x5b43, 0x404f, 0xae48,
  0x7555, 0x4b6d, 0x4e53, 0x7556, 0x4b67, 0x7557, 0x324c, 0x3b5e,
  0x4f48, 0x5b46, 0x3f75, 0x5b45, 0x5b40, 0x384f, 0xae4c, 0xae4d,
  0x5b4c, 0x5b4a, 0x324d, 0x5b48, 0x5b4e, 0x5b54, 0x7558,
  /* 0x67C0 */
  0x755a, 0x4248, 0xae4e, 0x4a41, 0x5b56, 0xae4f, 0x4922, 0x5b55,
  0x4770, 0x4b3f, 0x343b, 0xae50, 0x4077, 0x3d40, 0x755b, 0x4453,
  0xae51, 0x4d2e, 0xae52, 0x5b51, 0x5b50, 0x5b52, 0x5b4f, 0x5b57,
  0x5b4d, 0x5b4b, 0x5b53, 0x5b49, 0xae53, 0x436c, 0x4c78, 0x3c46,
  0x3a74, 0xae54, 0x7559, 0x3a3a, 0x755c, 0x4b6f, 0x3341,
  /* 0x6800 */
  0x755d, 0x444e, 0x464a, 0x3149, 0xae4b, 0x4072, 0x4034, 0x372a,
  0xae58, 0x755f, 0x5b59, 0xae59, 0x393b, 0x337c, 0x5b5b, 0x3374,
  0x5b61, 0x7560, 0xae5a, 0x7561, 0x5b5e, 0xae5c, 0x4073, 0x334b,
  0x3a2c, 0xae5d, 0x334a, 0x3a4f, 0xae5e,
  /* 0x6840 */
  0x5b5c, 0x3765, 0x374b, 0x456d, 0xae5f, 0xae60, 0x5b5a, 0x3046,
  0xae61, 0xae62, 0x5b5d, 0x5b5f, 0x364d, 0x372c, 0x755e, 0x343c,
  0x354b, 0xae63, 0xae64, 0x5b62, 0x7562, 0x3a79, 0x4b71, 0x3b37,
  0x5b63, 0x4930, 0xae66, 0xae67, 0x7563, 0x5b6f, 0x7564, 0x3233,
  0x5b64, 0xae68, 0xae69, 0x5b75, 0x5b65,
  /* 0x6880 */
  0x4e42, 0xae6a, 0x5b6c, 0x475f, 0x5b74, 0x5b67, 0xae6b, 0x3034,
  0x5b69, 0xae6c, 0x393c, 0xae6e, 0xae6f, 0xae70, 0x5b6b, 0xae71,
  0x5b6a, 0x5b66, 0x5b71, 0x3e3f, 0x7566, 0x7567, 0x546d, 0x3868,
  0x4d7c, 0xae72, 0xae73, 0x5b68, 0x4474, 0x3323, 0x3a2d, 0x7568,
  0x5b60, 0xae74, 0x5b70, 0x3361, 0x5b6e, 0x5b72, 0xae75, 0x456e,
  /* 0x68C0 */
  0xae7a, 0x347e, 0xae7b, 0x5c32, 0x7569, 0x4c49, 0x5b77, 0x347d,
  0xae7c, 0x5b7e, 0xae7d, 0x756a, 0x4b40, 0x5c21, 0x5c23, 0xae7e,
  0x5c27, 0x5b79, 0xaf21, 0x432a, 0x456f, 0x5c2b, 0x5b7c, 0x5c28,
  0xaf22, 0xaf23, 0x5c22, 0x756b, 0xaf24, 0x756c, 0x3f39, 0x5c2c,
  0x756d, 0x756e, 0x4033, 0xaf25, 0x5c2a, 0x343d, 0xae76, 0x756f,
  /* 0x6900 */
  0x4f50, 0x5b76, 0xaf26, 0x5c26, 0x3058, 0xaf27, 0x5b78, 0x7570,
  0x4c3a, 0x5b7d, 0x3f22, 0x4447, 0x5b73, 0x5c25, 0x3f7a, 0x5c2f,
  0x3371, 0x3821, 0x5c31, 0x5b7a, 0x5c30, 0x5c29, 0x5b7b, 0x5c2d,
  0x5c2e, 0x5c3f, 0x464e, 0x7573, 0x5c24, 0x5c3b, 0xaf2b, 0x5c3d,
  0x4458,
  /* 0x6940 */
  0x7574, 0xaf2d, 0x7571, 0x4d4c, 0x4976, 0x5c38, 0x424a, 0x7575,
  0x5c3e, 0x413f, 0x5c35, 0x5c42, 0x5c41, 0x466f, 0x5c40, 0x466a,
  0x7576, 0x7577, 0x7578, 0xaf2e, 0x5c44, 0x5c37, 0xaf2f, 0x3648,
  0x5c3a, 0x3d5d, 0xaf30, 0x4760, 0x5c3c, 0x364b, 0x5c34, 0x5c36,
  0x5c33, 0xaf31, 0x4f30, 0x335a, 0x5c39, 0xaf32,
  /* 0x6980 */
  0x7579, 0x5c43, 0x3335, 0x3a67, 0x315d, 0x5c54, 0xaf33, 0x4f31,
  0x5c57, 0xaf35, 0xaf36, 0x3f3a, 0x5c56, 0x5c55, 0x757b, 0xaf37,
  0x5c52, 0x757c, 0x5c46, 0xaf38, 0x5c63, 0x5c45, 0x5c58, 0xaf39,
  0xaf3a, 0x5c50, 0xaf3b, 0x5c4b, 0x5c48,
  /* 0x69C0 */
  0xaf3c, 0x5c49, 0x5c51, 0x7422, 0x5c4e, 0x393d, 0x4448, 0x4164,
  0x5c4c, 0x757d, 0x5c47, 0xaf3d, 0x5c4a, 0xaf3e, 0x4d4d, 0x4b6a,
  0x5c4f, 0x5c59, 0x7622, 0xaf44, 0x5c61, 0x5c5a, 0x7623, 0x7624,
  0x5c67, 0x5c65, 0xaf45, 0xaf46, 0x5c60, 0xaf47, 0xaf49, 0x7625,
  0x7626, 0x5c5f, 0x4450, 0x4165, 0xaf4a, 0x5c5d,
  /* 0x6A00 */
  0x5c5b, 0x5c62, 0x5c68, 0x4875, 0x5c6e, 0x7627, 0xaf4b, 0x5c69,
  0x5c6c, 0x5c66, 0x7628, 0x4374, 0x4938, 0xaf4c, 0x5c5c, 0xaf4d,
  0x5c64, 0x3e40, 0x4c4f, 0x5c78, 0x5c6b, 0x3822, 0x3223, 0x335f,
  0x5c53, 0xaf41, 0xaf4f, 0xaf50, 0xaf51, 0x3e41, 0x5c70, 0x5c77,
  0x3c79, 0x3372, 0x762a, 0x432e, 0x762b, 0xaf52,
  /* 0x6A40 */
  0x5c6d, 0x762c, 0xaf53, 0x5c72, 0x5c76, 0xaf54, 0x3636, 0xaf56,
  0x762d, 0xaf57, 0x762e, 0x354c, 0x5c74, 0x762f, 0x3521, 0x464b,
  0x5c73, 0xaf58, 0x5c75, 0x7630, 0x5c6f, 0x7631, 0x5c71, 0xaf55,
  0xaf5a, 0x3360,
  /* 0x6A80 */
  0x4349, 0xaf5b, 0x5c7c, 0x7633, 0xaf5c, 0x5c7a, 0x3869, 0x5c79,
  0xaf5e, 0x7634, 0x5d21, 0x5b58, 0x7635, 0x7636, 0xaf5f, 0x5c7b,
  0xaf60, 0x5c7d, 0x5c7e, 0x7637, 0x5d2c, 0xaf62, 0x5d28, 0x5b6d,
  0x5d27, 0x5d26, 0x5d23, 0xaf63,
  /* 0x6AC0 */
  0x5c6a, 0x5d25, 0x5d24, 0xaf64, 0xaf66, 0x5d2a, 0x4f26, 0xaf65,
  0x5d2d, 0x367b, 0xaf67, 0xaf68, 0x5d29, 0x5d2b, 0x7638, 0x7639,
  0x4827, 0x5d2e, 0xaf6b, 0xaf6c, 0xaf6d, 0xaf6e, 0x5d32, 0x5d2f,
  0xaf6f,
  /* 0x6B00 */
  0x4d73, 0x5d30, 0x5c5e, 0xaf71, 0xaf72, 0xaf73, 0xaf74, 0x5d33,
  0x5d34, 0xaf76, 0x763c, 0x3135, 0x763d, 0x5d36, 0x3767, 0x3c21,
  0x3655, 0x3224, 0x763e, 0xaf78, 0x4d5f, 0x763f, 0x5d38, 0x5d37,
  0x5d3a, 0x353d, 0x3656, 0x343e,
  /* 0x6B40 */
  0x5d3d, 0x7640, 0x5d3c, 0x5d3e, 0xaf79, 0x324e, 0x4337, 0x5d3f,
  0x343f, 0x5d41, 0x7641, 0xaf7a, 0x5d40, 0x5d42, 0x5d43, 0x7642,
  0x5d44, 0x3b5f, 0x4035, 0x3a21, 0x7643, 0x4970, 0x7644, 0x4a62,
  0x4f44, 0xaf7b, 0x3b75, 0x3a50, 0x4e72, 0xaf7c, 0x7645, 0x5d45,
  0x5d46, 0xaf7d, 0x3b60, 0x5d47,
  /* 0x6B80 */
  0x5d48, 0xaf7e, 0x7646, 0x5d4a, 0x5d49, 0x4b58, 0x3d5e, 0x3c6c,
  0x3b44, 0x5d4b, 0x5d4d, 0x3f23, 0x5d4c, 0xee21, 0x5d4e, 0x5d4f,
  0x7647, 0x5d50, 0x5d51, 0x7648, 0xee22, 0x5d52, 0x5d54, 0x5d53,
  0x5d55, 0x3225, 0x434a, 0x5d56, 0x3b26, 0x334c, 0x5d57, 0xee24,
  0xee25, 0x4542,
  /* 0x6BC0 */
  0x544c, 0x3523, 0x5d58, 0xee26, 0xee27, 0xee28, 0x5d59, 0x4a6c,
  0x4b68, 0x764a, 0x4647, 0x5d5a, 0x4866, 0x764b, 0x764c, 0x487b,
  0xee29, 0x4c53, 0x5d5b, 0xee2a, 0xee2b, 0x5d5d, 0x5d5c, 0xee2c,
  0x5d5f, 0xee2d, 0x5d5e, 0x764d,
  /* 0x6C00 */
  0xee2e, 0x764e, 0x5d61, 0xee2f, 0xee30, 0x3b61, 0x764f, 0x4c31,
  0x5d62, 0x5d63, 0x3524, 0x5d64, 0x5d66, 0x5d65, 0x7650, 0x3f65,
  0xee31, 0xee32, 0x4939, 0x314a, 0xee33, 0x4845, 0xee35,
  /* 0x6C40 */
  0x4475, 0x3d41, 0x3561, 0xee36, 0x4846, 0x3c2e, 0x5d68, 0x3440,
  0x7651, 0x3178, 0xee37, 0x7652, 0x4672, 0x5d67, 0x393e, 0x4353,
  0x5d69, 0xee4f, 0x5d71, 0x5d6a, 0xee38, 0x4241, 0x3562, 0x5d72,
  0x7654, 0x7655, 0x3768, 0x3525, 0x5d70,
  /* 0x6C80 */
  0x5d6e, 0x5d6b, 0x4d60, 0xee39, 0x7656, 0x7657, 0x4440, 0xee3a,
  0x4659, 0x5d6c, 0x5d74, 0x5d73, 0x3723, 0xee3c, 0xee3d, 0x322d,
  0xee3e, 0x7658, 0x3a3b, 0x5d6d, 0x5d6f, 0x7659, 0x4b57, 0x4274,
  0x7653, 0x4b77, 0xee3f, 0x5d7c, 0x5d7d, 0x324f, 0x4a28, 0x4c7d,
  0x5e21, 0x3c23, 0x3e42, 0x5d78, 0x5d7e, 0x3168,
  /* 0x6CC0 */
  0x3637, 0xee40, 0x5d75, 0x5d7a, 0x765b, 0x4074, 0x4771, 0x4867,
  0xee41, 0x5d77, 0x765c, 0x4b21, 0xee43, 0x5d79, 0x5e24, 0xee44,
  0x5e22, 0xee45, 0x5d7b, 0x765d, 0x4b22, 0x4748, 0x3563, 0x4525,
  0x436d, 0xee46, 0x5e25, 0x765e, 0xee47, 0xee48, 0x765f, 0x5e23,
  0x4259, 0x5d76, 0x314b, 0x765a,
  /* 0x6D00 */
  0xee4a, 0x7661, 0xee4b, 0x4d4e, 0x5e30, 0x7662, 0x5e2f, 0x4076,
  0x5e2c, 0x4d6c, 0x4636, 0x5e26, 0xee4c, 0x4445, 0xee4d, 0xee4e,
  0x314c, 0x393f, 0x5e29, 0x7663, 0xee50, 0x7664, 0x3d27, 0x5e2e,
  0xee65, 0x5e2d, 0x5e28, 0x5e2b, 0x7665, 0x3368, 0xee51, 0x5e2a,
  0x4749, 0x7666,
  /* 0x6D40 */
  0x4e2e, 0x3e74, 0x4075, 0x7667, 0x5e36, 0x5e34, 0xee52, 0x494d,
  0xee53, 0xee54, 0x5e31, 0x5e33, 0x7668, 0x313a, 0x3940, 0x4f32,
  0x333d, 0x4962, 0xee55, 0x4d61, 0x3324, 0x3f3b, 0x5e35,
  /* 0x6D80 */
  0xee56, 0xee57, 0x766a, 0x5e3a, 0x766b, 0x3e43, 0x766c, 0xee58,
  0x4d30, 0xee59, 0x5e37, 0xee5a, 0x5e32, 0x766d, 0x5e38, 0xee5b,
  0x4e5e, 0x4573, 0x4642, 0x766e, 0xee61, 0x766f, 0xee62, 0x3336,
  0x3155, 0xee63, 0x5e3e, 0x5e41, 0x4e43, 0x7670,
  /* 0x6DC0 */
  0x4d64, 0xee64, 0x7671, 0x5e48, 0x5e42, 0x5e3f, 0xee66, 0x4e54,
  0x5e45, 0xee67, 0xee68, 0xee69, 0x3d4a, 0x5e47, 0x5e4c, 0x7672,
  0x4571, 0x5e4a, 0x7673, 0x7674, 0x7675, 0x5e44, 0xee6a, 0x4338,
  0x5e4b, 0x5e40, 0x5e46, 0xee6b, 0x5e4d, 0x307c, 0x5e43, 0x5e4e,
  0x3f3c, 0x3d5f, 0x4a25, 0xee6c, 0x3a2e, 0x5e3b, 0x5e49, 0x453a,
  0x7676,
  /* 0x6E00 */
  0x4036, 0x3369, 0x3a51, 0x3e44, 0x5e3d, 0x3d42, 0x374c, 0x5e3c,
  0xee5d, 0x5e52, 0x3d6d, 0x383a, 0x5e61, 0xee6e, 0x5e5b, 0x3574,
  0x454f, 0xee6f, 0x5e56, 0x5e5f, 0x302f, 0x3132, 0xee70, 0x3239,
  0x5e58, 0x422c, 0x5e4f, 0x5e51, 0x3941, 0xee72, 0x7678, 0xee6d,
  0x5e62, 0x5e5d, 0xee73, 0x5e55,
  /* 0x6E40 */
  0x5e5c, 0x7679, 0xee74, 0xee75, 0x4c2b, 0xee76, 0xee77, 0x5e5a,
  0x5e5e, 0xee78, 0xee79, 0xee7a, 0xee7b, 0x3850, 0xee7c, 0x3e45,
  0x4339, 0x767a, 0x767b, 0x5e54, 0xee7d, 0x4d2f, 0x5e57, 0x5e50,
  0x4572, 0x5e53, 0x5e59, 0x4f51, 0x3c3e,
  /* 0x6E80 */
  0x4b7e, 0x5e63, 0x482e, 0x5e6f, 0x383b, 0xef21, 0x3d60, 0x5e65,
  0x4e2f, 0x3942, 0x5e72, 0x306e, 0x5e70, 0xef22, 0x5e64, 0x767c,
  0x5e6a, 0x767d, 0x5e6c, 0xef23, 0x4d4f, 0x5e67, 0x452e, 0x5e69,
  0xef24,
  /* 0x6EC0 */
  0x767e, 0x5e71, 0xef25, 0x5e6b, 0x4c47, 0x7721, 0x5e66, 0xef26,
  0x3c22, 0x5e7e, 0x7722, 0x336a, 0x5e68, 0x5e6d, 0x5e6e, 0xef27,
  0x426c, 0x425a, 0xef29, 0x5e76, 0x5e7c, 0x5e7a, 0x4529, 0x5f23,
  0x5e77, 0xef2a, 0xef2b, 0x5e78, 0x5e60,
  /* 0x6F00 */
  0x3579, 0x493a, 0x3c3f, 0x3977, 0xef2c, 0xef2d, 0x4f33, 0x7723,
  0x5e74, 0x5f22, 0x3169, 0x4166, 0xef2e, 0x7724, 0x4779, 0x3441,
  0x4e7a, 0xef2f, 0x7726, 0x4c21, 0x4452, 0x7727, 0x5e7b, 0x5e7d,
  0x7728, 0xef28, 0xef30, 0x4132, 0xef31, 0x5f21, 0x5e79,
  /* 0x6F40 */
  0x5e73, 0x3443, 0x7729, 0xef33, 0x3769, 0xef34, 0x5f2f, 0x772a,
  0xef35, 0x5f2a, 0x4078, 0x772b, 0x3363, 0xef36, 0x772c, 0x772d,
  0x3d61, 0x5f33, 0xef37, 0x5f2c, 0x442c, 0x5f29, 0x4459, 0x5f4c,
  0x5f26, 0x5f25, 0x5f2e, 0xef39, 0x772e,
  /* 0x6F80 */
  0x5f28, 0x5f27, 0x5f2d, 0x4021, 0x5f24, 0x772f, 0x7730, 0x7731,
  0x5f30, 0xef3a, 0x5f31, 0x7732, 0xef3b, 0x3442, 0xef38, 0xef3d,
  0x7733, 0x5f36, 0x5f35, 0x5f37, 0xef3e, 0x7734, 0x5f3a, 0xef3f,
  0x4543, 0x5f34, 0xef41, 0x7735, 0x5f38, 0x7736, 0xef3c,
  /* 0x6FC0 */
  0x3763, 0x4279, 0x5f32, 0x473b, 0x5f39, 0x7737, 0xef42, 0xef43,
  0x7738, 0x5f3e, 0x5f3c, 0x5f3f, 0xef44, 0x5f42, 0xef45, 0x5f3b,
  0x396a, 0x4728, 0x5e39, 0xef46, 0x4d74, 0x5f3d, 0x5f41, 0x4275,
  0x773a, 0x5f40, 0x5f2b, 0x773b, 0x6f69, 0x7739, 0x5f45, 0xef48,
  0x5f49,
  /* 0x7000 */
  0xef49, 0x5f47, 0x773c, 0x773d, 0xef4a, 0x5f43, 0xef4b, 0x5f44,
  0x5f48, 0x5f46, 0x494e, 0x5f4e, 0x5f4b, 0x5f4a, 0x5f4d, 0x4654,
  0x5f4f, 0xef4c, 0x4375, 0x426d, 0x773e, 0x4025, 0x5f50, 0x5f52,
  0xef4e, 0xef4f, 0xef50, 0x5f51,
  /* 0x7040 */
  0xef51, 0xef52, 0x773f, 0xef53, 0x5e75, 0x7742, 0x5f53, 0xef55,
  0x4667, 0x7740, 0x7741, 0x5f54, 0x7743, 0xef56, 0xef57, 0x3250,
  0xef58, 0xef59, 0x4574, 0x3325, 0x7744, 0xef5a, 0x3564, 0x3c5e,
  0x3a52, 0xef5b,
  /* 0x7080 */
  0xef5c, 0x7745, 0xef5d, 0x4f27, 0x3f66, 0x316a, 0x5f56, 0xef5e,
  0xef5f, 0x5f55, 0xef62, 0x7746, 0x7747, 0x5f59, 0x433a, 0x5f5c,
  0x5f57, 0xef63, 0x5f5b, 0x7748, 0x5f5a, 0x4540, 0x3059, 0xef60,
  /* 0x70C0 */
  0x4e75, 0xef66, 0x5f5e, 0x3128, 0xef67, 0xef68, 0x7749, 0x774a,
  0x5f60, 0xef69, 0x5f5f, 0x5f5d, 0x774b, 0xef65, 0x5f58, 0x4b23,
  0x5f62,
  /* 0x7100 */
  0xef6a, 0xef6b, 0xef6c, 0xef6d, 0xef6e, 0x5f61, 0xef6f, 0x774c,
  0x316b, 0x5f64, 0x4a32, 0x5f63, 0x774e, 0x774f, 0x4c35, 0x3e47,
  0x774d, 0x7750, 0xef71, 0x7751, 0xef72, 0x4133, 0x3e46,
  /* 0x7140 */
  0x7752, 0x7753, 0x4e7b, 0xef74, 0x5f6a, 0x4079, 0xef73, 0x7754,
  0x7756, 0xef75, 0x5f66, 0x5f6b, 0x316c, 0x7757, 0xef76, 0x7758,
  0x5f69, 0x4761, 0x5f65, 0x5f68, 0x3e48, 0x7759, 0x4851, 0x5f6c,
  0x3c51, 0x407a,
  /* 0x7180 */
  0xef79, 0x5f6f, 0x775b, 0x775c, 0x5f67, 0x3727, 0x5f6d, 0x775d,
  0x4d50, 0x5f70, 0xef78, 0x7426, 0xef7a, 0x3d4f, 0xef7b, 0xef7c,
  0x5f71, 0x5f72, 0xef7d, 0xef7e, 0x472e, 0xf021, 0x5f74, 0x775f,
  0x5f75,
  /* 0x71C0 */
  0x775e, 0x4733, 0x7760, 0x4575, 0x5f77, 0xf023, 0x5f79, 0x4e55,
  0x5f76, 0xf024, 0x5f78, 0x316d, 0x5f73, 0xf025, 0xf026, 0x535b,
  0x5f7a, 0x4167, 0x3b38, 0x5f7c, 0x5f7b, 0x3f24, 0x5259, 0x5f7d,
  0x6021, 0x5f6e, 0x5f7e, 0x7761, 0x6022,
  /* 0x7200 */
  0x7762, 0x477a, 0xf027, 0x6023, 0x6024, 0x7763, 0x6025, 0x6026,
  0x445e, 0xf02a, 0x6028, 0x6027, 0x6029, 0x602a, 0xf02b, 0x3c5f,
  0x4963, 0xf02c, 0xf02d, 0x4c6c, 0x602b, 0x602c, 0x4156, 0x3c24,
  0x602d,
  /* 0x7240 */
  0x602e, 0xf02f, 0x602f, 0x4a52, 0x4847, 0x6030, 0x4757, 0x442d,
  0xf030, 0x7764, 0x7765, 0xf031, 0x6031, 0x3267, 0x356d, 0x4c46,
  0x4c36, 0x3234, 0x4f34, 0xf032, 0x4b52, 0x4a2a, 0xf034, 0xf035,
  0x4037, 0x6032, 0xf036, 0x4643, 0x3823, 0x6033, 0xf037,
  /* 0x7280 */
  0x3a54, 0x6035, 0x6034, 0x6036, 0x7767, 0xf038, 0x6037, 0x6038,
  0x7768, 0x353e, 0x6039, 0x603a, 0x3824, 0xf03a, 0xf03b, 0x4848,
  0xf03c, 0xf03d, 0x603c, 0x3e75, 0x603b, 0x7769,
  /* 0x72C0 */
  0x776a, 0xf03e, 0x3638, 0x603d, 0x603f, 0x603e, 0xf040, 0x6040,
  0x3851, 0x6041, 0x3669, 0x4140, 0x397d, 0x6043, 0x6044, 0x6042,
  0x3c6d, 0x4648, 0x3639, 0xf043, 0x6046, 0x432c, 0x6045, 0xf044,
  0x776b, 0x4f35, 0x4762,
  /* 0x7300 */
  0xf045, 0x6049, 0xf046, 0x604b, 0x6048, 0xf047, 0xf048, 0x4c54,
  0x604a, 0x604c, 0x4e44, 0x6050, 0x776d, 0x776e, 0x604f, 0x4376,
  0x472d, 0xf04b, 0x3825, 0x604e, 0xf04c, 0xf04d, 0x604d, 0x4d31,
  0x4d32, 0xf04a, 0xf04e, 0x6051, 0x316e,
  /* 0x7340 */
  0x3976, 0x3b62, 0x6052, 0x6053, 0x7770, 0xf04f, 0x6055, 0x3d43,
  0x7771, 0x6057, 0x6056, 0xf051, 0xf052, 0xf054, 0xf055, 0x6058,
  0xf056, 0x334d, 0x605a, 0xf057, 0x6059, 0x605c, 0x605b, 0x7772,
  /* 0x7380 */
  0xf058, 0x383c, 0xf059, 0x4e28, 0x364c, 0xf05a, 0x3226, 0xf05b,
  0x7773, 0x366a, 0xf05c, 0xf05d, 0xf05e, 0x7774, 0x7775, 0x7776,
  0xf05f, 0x7777, 0xf060, 0x3461, 0x7778, 0x4e68, 0x605e, 0xf061,
  0xf062, 0xf063, 0x6060, 0xf064, 0xf065,
  /* 0x73C0 */
  0x6061, 0x3251, 0xf066, 0x605d, 0x7779, 0x3b39, 0xf067, 0x4441,
  0x605f, 0x777a, 0x777b, 0x777c, 0x6064, 0x3c6e, 0xf068, 0x777d,
  0x6062, 0xf069, 0x777e, 0x373e, 0x4849, 0x6063, 0x607e, 0x6069,
  0xf06a, 0xf06c, 0x383d,
  /* 0x7400 */
  0xf06d, 0x3565, 0x6066, 0x4d7d, 0x7821, 0x4e30, 0x7822, 0xf06b,
  0x7823, 0x7824, 0x4276, 0xf06e, 0x6068, 0x7826, 0x7827, 0x7828,
  0x7829, 0x782a, 0x782b, 0x782c, 0x782d, 0xf06f, 0x606a, 0x4e56,
  0x3657, 0x487c, 0x474a, 0xf070, 0x606b, 0x606d,
  /* 0x7440 */
  0xf072, 0x6070, 0xf073, 0x782e, 0x782f, 0x7830, 0x7831, 0xf074,
  0xf075, 0xf071, 0x606c, 0x7832, 0x606f, 0x386a, 0x314d, 0x6071,
  0xf076, 0x3f70, 0x606e, 0x4e5c, 0x7833, 0x6074, 0x7424, 0x6072,
  0x6075, 0x7834, 0x7835, 0x6067, 0x6073, 0xf077, 0x3a3c, 0x6076,
  0x6077,
  /* 0x7480 */
  0xf078, 0x4d7e, 0xf079, 0x7836, 0x7837, 0xf07a, 0x7838, 0x6078,
  0x783d, 0xf07c, 0xf07d, 0x7839, 0xf07e, 0x783a, 0x6079, 0x783b,
  0xf121, 0xf122, 0x6065, 0x783c, 0xf123, 0x783e, 0x607a, 0x783f,
  0x7840, 0xf124, 0xf125, 0x3444, 0x7841, 0xf126, 0xf128, 0xf127,
  0x3c25, 0x7842,
  /* 0x74C0 */
  0x7843, 0x7844, 0x607b, 0x607c, 0x607d, 0xf129, 0xf12a, 0x7845,
  0x313b, 0xf12b, 0x6121, 0x493b, 0x6122, 0x3424, 0x6123, 0x6124,
  0xf12d, 0x6125, 0xf12c, 0x6127, 0x6128, 0x6126, 0x4953, 0x612a,
  0x6129, 0xf12f, 0x7846,
  /* 0x7500 */
  0x7847, 0x612c, 0x612b, 0x612d, 0x612e, 0x6130, 0x612f, 0x3979,
  0x6132, 0x6131, 0x7848, 0x3445, 0x3f53, 0x453c, 0x6133, 0x4038,
  0xf131, 0x3b3a, 0xf132, 0x3179, 0x6134, 0x4d51, 0xf133, 0x4a63,
  0x6135, 0x7849, 0x4544, 0x4d33, 0x3943, 0x3f3d, 0x434b, 0x5234,
  0x442e, 0x3268, 0x6136, 0xf136, 0xf137,
  /* 0x7540 */
  0xf138, 0x6137, 0x613c, 0xf139, 0x613a, 0x6139, 0x5a42, 0x3326,
  0x6138, 0xf13a, 0x305a, 0xf13b, 0x482a, 0xf13c, 0x484a, 0x4e31,
  0x613d, 0x613b, 0x435c, 0x4026, 0x482b, 0x492d, 0x613f, 0x4e2c,
  0x374d, 0x6140, 0x613e, 0x4856, 0x6141, 0xf13d, 0x6142, 0x784a,
  0x305b, 0xf13f, 0xf13e, 0x3e76, 0x6147, 0x6144, 0x466d, 0x6143,
  0x784b, 0xf140, 0xf141, 0xf142, 0x3526,
  /* 0x7580 */
  0xf143, 0x614a, 0x6145, 0x6146, 0x6149, 0x6148, 0x4925, 0xf145,
  0x4142, 0x4141, 0x353f, 0x784c, 0x614b, 0x614c, 0x614d, 0xf147,
  0x614f, 0x614e, 0x3156, 0xf149, 0x6157, 0x4868, 0x6151, 0x6153,
  0xf14a, 0x6155, 0x3f3e, 0x6156, 0x6154, 0x3c40, 0xf14b,
  /* 0x75C0 */
  0xf14c, 0x6150, 0x6152, 0x4942, 0xf14d, 0x3e49, 0x6159, 0x6158,
  0x784e, 0xf14e, 0x615a, 0xf14f, 0x3c26, 0x3a2f, 0x4577, 0x615b,
  0x444b, 0xf150, 0x615d, 0xf151, 0xf152, 0x4e21, 0x615c, 0x784f,
  0xf153, 0x4169, 0xf154, 0xf155, 0x6162, 0xf156, 0x6164, 0x6165,
  0x4354, 0xf157, 0x6163, 0x6160, 0x615e, 0x615f,
  /* 0x7600 */
  0x7850, 0x6161, 0x7851, 0xf158, 0xf15a, 0x7852, 0x6168, 0x6166,
  0x6167, 0xf15b, 0xf15e, 0x7853, 0x7854, 0xf159, 0x7855, 0xf15f,
  0xf160, 0x7856, 0x6169, 0x616b, 0x616c, 0x616d, 0x616e, 0xf162,
  0x7e7d, 0x616a, 0xf163, 0x7857, 0x6170, 0xf165, 0x616f, 0x7858,
  0x6171, 0xf164,
  /* 0x7640 */
  0xf168, 0x4e45, 0x7859, 0x6174, 0x6172, 0x6173, 0xf16a, 0x785a,
  0x3462, 0x4c7e, 0xf16b, 0x4a4a, 0x6176, 0x6175, 0x6177, 0x6178,
  0x785b, 0x785c, 0x617c, 0x6179, 0x617a, 0x617b, 0x617d, 0x785d,
  0xf16d, 0x785e, 0x617e, 0x785f, 0x6221, 0x6222, 0x6223, 0x482f,
  0x4550, 0x6224, 0x4772, 0x4934,
  /* 0x7680 */
  0x6225, 0x7860, 0x6226, 0x452a, 0x3327, 0x3944, 0x6227, 0x6228,
  0x6229, 0x3b29, 0x622b, 0xf16e, 0x622a, 0x622c, 0x622d, 0x7861,
  0xf16f, 0x7862, 0x7863, 0xf171, 0xf170, 0x7864, 0xf172, 0xf173,
  0x7865, 0x4869, 0xf174, 0x622e, 0x622f, 0x7866, 0x7369, 0x6230,
  0x6231, 0x6232, 0x3b2e,
  /* 0x76C0 */
  0x6233, 0x4756, 0x7867, 0x4b5f, 0x314e, 0xf176, 0x3157, 0x7868,
  0x6234, 0x7869, 0x6236, 0x786a, 0x6235, 0x4570, 0x4039, 0x5d39,
  0x6237, 0x4c41, 0x6238, 0x3446, 0x4857, 0x6239, 0x786b, 0x623a,
  0xf178, 0x623b, 0xf179, 0x4c5c, 0x786c, 0x4c55, 0x443e, 0x416a,
  0x623d, 0x786d, 0x3d62,
  /* 0x7700 */
  0x3e4a, 0x6240, 0x623f, 0x623e, 0x487d, 0x786e, 0x3447, 0x3829,
  0xf17b, 0x786f, 0xf17c, 0x6246, 0x6243, 0x3f3f, 0x4c32, 0x6242,
  0x6244, 0x6245, 0x6241, 0xf17d, 0x7870, 0xf17e, 0x7871, 0x6247,
  0x6248, 0x442f, 0x3463,
  /* 0x7740 */
  0x4365, 0x7872, 0x6249, 0x7873, 0x7874, 0xf225, 0x624a, 0x624d,
  0x7875, 0x7876, 0xf226, 0x3f67, 0x7877, 0x4644, 0x624e, 0x4b53,
  0x624b, 0xf227, 0x624c, 0xf229, 0x6251, 0x7878, 0xf22a, 0xf22b,
  0x6250, 0x624f,
  /* 0x7780 */
  0x7879, 0x6253, 0x6252, 0x6254, 0x787a, 0xf22e, 0x6256, 0xf22f,
  0x6255, 0xf230, 0xf231, 0x4a4d, 0xf232, 0x787b, 0x3d56, 0x4e46,
  0x6257, 0x4637, 0x6258, 0x6259, 0x625d, 0x625b, 0x625c, 0x625a,
  /* 0x77C0 */
  0x625e, 0x625f, 0x6260, 0x6261, 0x4c37, 0x6262, 0xf233, 0xf234,
  0x787c, 0x4c70, 0x6263, 0xf235, 0x434e, 0xf236, 0x476a, 0x366b,
  0xf237, 0xf238, 0x433b, 0x6264, 0x363a, 0xf23a, 0x4050, 0xf23b,
  0xf23c, 0x6265,
  /* 0x7800 */
  0x3a3d, 0xf23e, 0xf23f, 0xf240, 0x6266, 0xf241, 0x6267, 0x3826,
  0x3a55, 0xf242, 0x6269, 0xf243, 0x4556, 0x3a56, 0x354e, 0xf244,
  0x787d, 0x4b24, 0x474b, 0x4557, 0x395c,
  /* 0x7840 */
  0x7921, 0x626b, 0xf245, 0x7922, 0x7923, 0x7924, 0x3e4b, 0xf246,
  0x7925, 0xf247, 0x4e32, 0x3945, 0x7926, 0x3827, 0x4823, 0x626d,
  0x626f,
  /* 0x7880 */
  0x386b, 0x626e, 0x4476, 0xf249, 0x6271, 0x3337, 0x626c, 0x486a,
  0x3130, 0xf24a, 0x3a6c, 0x4f52, 0x6270, 0xf24c, 0xf24d, 0xf24e,
  0x6272, 0xf24b, 0x4a4b, 0x4059, 0x6274, 0x792a, 0x6275, 0x7928,
  0x6273, 0x334e, 0xf24f, 0x627b, 0x627a,
  /* 0x78C0 */
  0x3c27, 0x627c, 0x6277, 0xf250, 0x627d, 0x6278, 0xf251, 0xf252,
  0x4858, 0x6276, 0x6279, 0xf253, 0x6322, 0xf254, 0xf255, 0x792b,
  0xf256, 0x6321, 0x4b61, 0x627e, 0x306b, 0x792c, 0x6324, 0x792e,
  0xf257, 0xf258, 0xf259, 0x6323, 0xf25a,
  /* 0x7900 */
  0x792d, 0x3e4c, 0x6325, 0x4143, 0xf25c, 0x6327, 0x6326, 0x6328,
  0xf25d, 0x792f, 0xf25f, 0x6268, 0x626a, 0x632a, 0x6329, 0x7930,
  0xf25e, 0x7931, 0x7932, 0x3c28, 0xf260, 0x4e69, 0x3c52,
  /* 0x7940 */
  0x632b, 0x3737, 0x7935, 0x7936, 0x3540, 0x3527, 0x3b63, 0xf261,
  0x4d34, 0x6331, 0x6330, 0x4144, 0x632d, 0xf262, 0x632f, 0xf263,
  0x793a, 0x3d4b, 0x3f40, 0x632e, 0x632c, 0x472a, 0x3e4d, 0xf265,
  0x493c, 0x3a57, 0xf266, 0x4578, 0x793e, 0x6332, 0x6333,
  /* 0x7980 */
  0x6349, 0x3658, 0x4f3d, 0x4135, 0x6334, 0x3252, 0x4477, 0x4a21,
  0xf267, 0xf268, 0xf269, 0x7942, 0xf26a, 0x6335, 0xf26b, 0x357a,
  0x6336, 0xf26c, 0x6338, 0x6339, 0x4729, 0x7943, 0x633a, 0xf26d,
  0x7944, 0x633b, 0x633c, 0xf26e, 0x3659, 0x3253, 0x4645,
  /* 0x79C0 */
  0x3d28, 0x3b64, 0xf26f, 0xf270, 0x7945, 0x633d, 0x7946, 0x3d29,
  0xf271, 0xf272, 0x324a, 0x4943, 0x7948, 0x633e, 0xf273, 0x486b,
  0x7949, 0x4145, 0x6341, 0x6342, 0x4769, 0x3f41, 0x633f, 0x4361,
  0x794a, 0x6340, 0x794b, 0x3e4e, 0x305c,
  /* 0x7A00 */
  0x3529, 0x794c, 0x6343, 0xf278, 0x4478, 0x6344, 0x4047, 0xf279,
  0x4c2d, 0xf27a, 0x4923, 0x6345, 0x6346, 0x4355, 0xf27b, 0x4e47,
  0xf27c, 0x6348, 0x6347, 0xf27e, 0x3c6f, 0x634a, 0x3070, 0x634d,
  0xf321, 0x794e, 0x634b, 0x3254, 0x374e, 0x634c, 0x3946,
  /* 0x7A40 */
  0x3972, 0x4a66, 0x634e, 0x4b54, 0xf322, 0x6350, 0xf323, 0x4051,
  0x314f, 0x323a, 0x302c, 0xf324, 0x634f, 0xf325, 0xf326, 0x794f,
  0xf327, 0xf328, 0x6351, 0x6352, 0x3e77, 0xf329, 0x6353, 0xf32a,
  0x334f, 0x7950, 0x6355, 0x376a, 0xf32b, 0x3566, 0xf32c, 0x6356,
  0x3675, 0x6357, 0x407c,
  /* 0x7A80 */
  0x464d, 0xf32d, 0x4060, 0x3a75, 0x7952, 0x6358, 0xf32e, 0xf32f,
  0x4362, 0x416b, 0x635a, 0x635c, 0x6359, 0x635b, 0x3722, 0x7953,
  0xf330, 0x635d, 0x3726, 0xf331, 0x3567, 0x4d52, 0x635f, 0x7955,
  0x6360, 0xf334, 0x312e, 0x7956, 0xf335, 0xf336, 0x6363,
  /* 0x7AC0 */
  0x3376, 0x6362, 0x6361, 0x6365, 0x635e, 0x6366, 0x4e29, 0xf338,
  0x6367, 0x7957, 0x6368, 0xf339, 0x5474, 0x636a, 0x6369, 0x636b,
  0x636c, 0x4e35, 0x636d, 0x706f, 0x3e4f, 0x636e, 0x636f, 0x3d57,
  0x4638, 0x6370, 0xf33a, 0xf33b, 0x4328, 0x7958, 0x6371, 0x433c,
  0x6372, 0xf33c, 0x3625, 0x513f, 0x435d, 0x3c33, 0x7959, 0x3448,
  /* 0x7B00 */
  0x6373, 0x6422, 0x6376, 0xf33f, 0x3568, 0x6375, 0x6424, 0x6374,
  0x3e50, 0x795a, 0x6378, 0x6379, 0x452b, 0x637a, 0x335e, 0x3f5a,
  0x4964, 0xf342, 0x637c, 0xf343, 0x4268, 0x795b, 0xf344, 0xf345,
  0xf346, 0x6377, 0x637b, 0x637d, 0x3a7b, 0x795c, 0xf341,
  /* 0x7B40 */
  0xf34a, 0x6426, 0x492e, 0x795d, 0x4826, 0x4579, 0x365a, 0x6425,
  0x6423, 0x795e, 0x4835, 0x637e, 0x435e, 0x457b, 0x457a, 0xf34c,
  0x3a76, 0x6438, 0x795f, 0xf34e, 0x6428, 0xf34f, 0x642a, 0xf350,
  0x642d, 0x7960, 0x642e, 0x7961, 0x642b, 0x642c, 0x7962, 0xf351,
  0x6429, 0x6427, 0xf34d, 0x6421, 0xf349,
  /* 0x7B80 */
  0x4a4f, 0x3255, 0x6435, 0x6432, 0x6437, 0xf354, 0xf355, 0x6436,
  0x4773, 0x4c27, 0x3b3b, 0x6430, 0x6439, 0x6434, 0xf356, 0x6433,
  0x642f, 0x7963, 0x6431, 0x3449, 0x433d, 0x407d, 0xf358, 0x4822,
  0x643e, 0xf359, 0x4824, 0xf35a,
  /* 0x7BC0 */
  0x4061, 0x643b, 0x484f, 0xf35b, 0x643f, 0x4a53, 0x435b, 0xf35c,
  0x643a, 0x643c, 0x643d, 0xf35f, 0xf360, 0x7965, 0x7966, 0xf361,
  0x6440, 0x3c44, 0x4646, 0x6445, 0x6444, 0x6441, 0xf362, 0x4f36,
  0xf363, 0x644a, 0x644e, 0x644b,
  /* 0x7C00 */
  0x6447, 0x7967, 0xf364, 0x6448, 0xf365, 0x644d, 0xf366, 0xf367,
  0x6442, 0x5255, 0x6449, 0x6443, 0x644c, 0x7969, 0x6452, 0x796a,
  0x344a, 0x644f, 0xf368, 0x6450, 0x6451, 0x6454, 0x7968, 0x796b,
  0x796c, 0x6453, 0x4876, 0x6455, 0x4e7c, 0x4a6d,
  /* 0x7C40 */
  0x645a, 0x6457, 0xf369, 0xf36a, 0x6456, 0x4052, 0x6459, 0x645b,
  0xf36b, 0x6458, 0x645f, 0xf36c, 0x645c, 0x796f, 0xf36d, 0x645d,
  0x6446, 0xf36e, 0x645e, 0x6460, 0xf36f, 0x6461, 0x7970, 0xf370,
  0xf371, 0xf372, 0x4a46, 0x6462, 0x7971, 0x4c62,
  /* 0x7C80 */
  0x364e, 0x3729, 0x6463, 0x4a34, 0x3f68, 0x4c30, 0x7972, 0x6464,
  0x4e33, 0x7973, 0x4774, 0x4146, 0x4734, 0x3d4d, 0x3040, 0x7974,
  0x6469, 0x6467, 0x6465, 0x3421, 0xf376, 0x3e51, 0x646a, 0x6468,
  0x6466, 0x646e, 0x646d, 0x646c, 0x646b, 0xf378, 0xf379, 0x646f,
  0x7975, 0x6470, 0x403a, 0xf37a,
  /* 0x7CC0 */
  0x6471, 0x6473, 0xf37c, 0x6472, 0xf37e, 0x3852, 0xf421, 0x4138,
  0x6475, 0x7976, 0x457c, 0xf423, 0x6474, 0x7977, 0x6476, 0x7978,
  0x4a35, 0x416c, 0x3947, 0x6477, 0xf425, 0x4e48, 0xf426, 0x6479,
  0x647a, 0x647b, 0xf428, 0x647c, 0x3b65, 0x647d, 0x374f, 0x356a,
  /* 0x7D00 */
  0x352a, 0x6521, 0xf429, 0x4c73, 0x3948, 0x647e, 0x7979, 0x797a,
  0xf42a, 0x6524, 0x4c66, 0x473c, 0x4933, 0xf42c, 0x797b, 0x3d63,
  0x6523, 0x3c53, 0x3949, 0x3b66, 0x3569, 0x4a36, 0x6522, 0x797c,
  0xf42d, 0x4147, 0x4b42, 0x3a77, 0x797d, 0x3b67, 0x445d, 0x6527,
  0x4e5f, 0x3a59, 0x797e, 0x6528, 0x3f42, 0x652a, 0x3e52, 0x3a30,
  0xf430, 0xf431, 0x6529,
  /* 0x7D40 */
  0xf432, 0x7a21, 0x3d2a, 0x383e, 0x4148, 0x6525, 0x652b, 0xf433,
  0x7a22, 0x6526, 0x3750, 0x652e, 0x6532, 0x376b, 0x7a23, 0x652d,
  0xf437, 0xf438, 0x6536, 0x7a24, 0x394a, 0x4d6d, 0x303c, 0x6533,
  0x356b, 0x6530, 0xf439, 0x6531, 0xf43a, 0x457d, 0x652f, 0x652c,
  0x3328, 0x4064, 0x3828, 0x7a25, 0x6538, 0xf43c,
  /* 0x7D80 */
  0x7a26, 0xf43e, 0xf43f, 0x6535, 0x7a27, 0xf440, 0x6537, 0x6534,
  0xf441, 0x3751, 0x4233, 0x6539, 0x416e, 0xf443, 0x6546, 0x7a28,
  0x6542, 0x653c, 0x7a29, 0xf444, 0xf445, 0x6540, 0x3c7a, 0x305d,
  0x653b, 0x6543, 0x6547, 0x394b, 0x4c56, 0x4456, 0x653d, 0xf446,
  0xf447, 0x6545, 0x653a, 0x433e, 0x653f, 0x303d, 0x4c4a,
  /* 0x7DC0 */
  0xf448, 0x7a2a, 0x653e, 0x365b, 0x486c, 0x7a2b, 0x416d, 0x4e50,
  0x3d6f, 0x656e, 0x7a2c, 0xf449, 0x6548, 0xf44a, 0x407e, 0x6544,
  0x6549, 0x654b, 0x4479, 0x654e, 0x7a2d, 0x654a, 0xf44b, 0x4a54,
  0x344b, 0x4c4b, 0x305e, 0xf44c, 0x654d, 0x4e7d, 0xf44d, 0x654c,
  /* 0x7E00 */
  0x316f, 0x466c, 0x654f, 0x7a30, 0x6556, 0x6550, 0x6557, 0xf451,
  0x7a31, 0x6553, 0x7a32, 0xf452, 0x477b, 0xf453, 0x3c4a, 0x6555,
  0xf454, 0x6552, 0x6558, 0x6551, 0x3d44, 0xf455, 0x7a2f, 0x4b25,
  0xf456, 0x3d4c, 0x6554, 0x6560, 0x655c, 0x655f, 0x655d, 0x6561,
  0x655b, 0x6541, 0x4053,
  /* 0x7E40 */
  0x484b, 0x655e, 0xf457, 0x6559, 0x7a34, 0x4121, 0x3752, 0x3d2b,
  0x7a35, 0x3f25, 0x4136, 0x6564, 0x6566, 0x6567, 0x6563, 0x6565,
  0x7a36, 0x655a, 0x6562, 0x656a, 0x6569, 0x7e7e, 0x4b7a, 0x372b,
  0xf458, 0xf459, 0x6568, 0x656c, 0x656b, 0x656f, 0xf45a, 0x6571,
  /* 0x7E80 */
  0x3b3c, 0x656d, 0xf45b, 0xf45c, 0x6572, 0x6573, 0x7a37, 0x6574,
  0x7a38, 0x657a, 0x453b, 0x6576, 0xf45e, 0x6575, 0x6577, 0x6578,
  0x6579, 0xf45f, 0xf460, 0x657b, 0x657c,
  /* 0x7F00 */
  0x344c, 0x657d, 0x657e, 0xf463, 0xf462, 0xf464,
  /* 0x7F40 */
  0xf465, 0xf466, 0x6621, 0x7a39, 0x6622, 0x6623, 0x6624, 0xf467,
  0x6625, 0x6626, 0xf46a, 0x6628, 0x6627, 0x6629, 0x662a, 0x662b,
  0xf46c, 0xf46d, 0xf46e, 0x662e, 0x662c, 0x662d, 0x3a61, 0x3753,
  0xf46f, 0x4356, 0x4833, 0x3d70, 0x474d, 0x486d, 0x662f, 0x586d,
  0xf470, 0xf471,
  /* 0x7F80 */
  0x6630, 0x6632, 0x4d65, 0x6631, 0x6634, 0x6633, 0x4d53, 0x6635,
  0x487e, 0xf473, 0x7a3b, 0x6636, 0xf476, 0x7a3c, 0x6639, 0xf477,
  0x6638, 0x6637, 0x663a, 0x3732, 0x4122, 0x3541, 0xf478, 0x663e,
  0x663b, 0x663c, 0x663f, 0x6640, 0x663d, 0x3129, 0x7a3d,
  /* 0x7FC0 */
  0x3227, 0xf47a, 0x6642, 0x6643, 0x6644, 0x4d62, 0x7a3e, 0xf47b,
  0x3d2c, 0x6646, 0x6645, 0x7a3f, 0x7a40, 0x3f69, 0x6647, 0xf47c,
  0xf47d, 0x6648, 0x6649, 0x3465, 0x7a41, 0x7a42, 0xf47e, 0x344d,
  0xf521, 0x664a, 0x664b, 0x7a43, 0x4b5d, 0x4d63,
  /* 0x8000 */
  0x4d54, 0x4f37, 0xf522, 0x394d, 0x664e, 0x3c54, 0x664d, 0xf524,
  0xf523, 0x664f, 0x3c29, 0xf525, 0x4251, 0xf526, 0x6650, 0x7a45,
  0x394c, 0xf527, 0x4c57, 0x6651, 0x6652, 0x6653, 0x6654, 0xf528,
  0x7a46, 0x6655, 0xf529, 0xf52a, 0x3c2a, 0x7a47, 0x4c6d, 0x7a48,
  0x6657, 0x7a49, 0x433f, 0x6656,
  /* 0x8040 */
  0xf52b, 0x6659, 0x6658, 0x665a, 0x403b, 0x665b, 0x665c, 0x4a39,
  0x665d, 0x416f, 0x665e, 0xf52c, 0x665f, 0x4e7e, 0x6662, 0xf52d,
  0x6661, 0x6660, 0x4430, 0xf52e, 0x6663, 0x3f26, 0x6664, 0xf52f,
  0x6665, 0x4f38, 0x6666,
  /* 0x8080 */
  0x6667, 0x6669, 0x6668, 0x4825, 0x4679, 0x4f3e, 0x4829, 0x666b,
  0x3e53, 0x492a, 0xf530, 0x666c, 0x666a, 0xf531, 0x344e, 0x3854,
  0x3b68, 0xf532, 0x486e, 0xf533, 0x382a, 0x4b43, 0x666f, 0x666d,
  0x394e, 0x394f, 0x3069, 0x3a68, 0xf534, 0x4759,
  /* 0x80C0 */
  0x305f, 0x6674, 0xf536, 0x4340, 0x7a4a, 0x4758, 0x425b, 0xf537,
  0x6676, 0x7a4b, 0xf538, 0x6672, 0x6675, 0x6670, 0x6673, 0x4b26,
  0x7a4c, 0x3855, 0x307d, 0x6671, 0xf539, 0x6678, 0x6679, 0x7a4d,
  0x4639, 0xf53c, 0x363b, 0xf53d, 0x6726, 0x473d,
  /* 0x8100 */
  0x3b69, 0x363c, 0x4048, 0x4f46, 0x4c2e, 0x6677, 0x4054, 0xf53b,
  0xf540, 0x7a4e, 0x3553, 0x667a, 0xf541, 0x667c, 0xf543, 0xf544,
  0x667b, 0xf545, 0x667d, 0x4326, 0x473e, 0xf53f, 0x4431, 0xf547,
  0x6723,
  /* 0x8140 */
  0x6722, 0x7a4f, 0x667e, 0x3f55, 0x4965, 0x6725, 0x6724, 0x3950,
  0x4f53, 0x6735, 0x7a50, 0x6729, 0x672a, 0x7a51, 0x7a52, 0xf549,
  0x3c70, 0x7a53, 0x6728, 0x3978, 0x6727, 0x672b, 0x4432, 0x4a22,
  0x4123, 0x425c,
  /* 0x8180 */
  0x672f, 0xf54b, 0x6730, 0x672c, 0xf54d, 0xf54e, 0x672d, 0x672e,
  0x3951, 0x6736, 0x6732, 0xf550, 0x4966, 0x4b6c, 0x4928, 0x6731,
  0x6734, 0x6733, 0x4b44, 0x6737, 0x6738, 0xf551, 0x4137, 0x6739,
  0x673b, 0x673f, 0x7a54, 0x673c, 0x673a, 0x473f,
  /* 0x81C0 */
  0x673d, 0xf552, 0x673e, 0xf553, 0x3232, 0x6745, 0x6740, 0x7a55,
  0x6741, 0x7a56, 0x6742, 0x4221, 0xf554, 0x7a57, 0x6744, 0x6743,
  0x6746, 0xf555, 0x6747, 0x6748, 0x3f43, 0xf557, 0x3269, 0x6749,
  0x4e57, 0x3c2b, 0xf559, 0x3d2d, 0x3b6a, 0x4357, 0x674a, 0x674b,
  0x3131, 0xf55b, 0x674c, 0xf55c,
  /* 0x8200 */
  0x674d, 0x674e, 0xf55e, 0x674f, 0x6750, 0x363d, 0x5a2a, 0x6751,
  0x4065, 0x6752, 0x3c4b, 0x6753, 0x5030, 0x6754, 0x4a5e, 0x345c,
  0xf560, 0x4124, 0x3d58, 0x4971, 0x3d2e, 0xf561, 0xf562, 0x6755,
  0x3952, 0x6756, 0x484c, 0x6764, 0xf564, 0x6758, 0xf565, 0x4249,
  0x4775, 0x383f, 0x6757, 0x4125, 0xf566,
  /* 0x8240 */
  0x6759, 0xf569, 0xf567, 0x447a, 0xf568, 0xf56b, 0xf56d, 0xf56f,
  0x675b, 0x675a, 0x675d, 0xf571, 0x675c, 0x675e, 0x7a5b, 0x6760,
  0xf572, 0x675f, 0x344f, 0x6761, 0x6762, 0x6763, 0x3a31, 0x4e49,
  0x6765, 0x3f27, 0x7a5c, 0x3170, 0x6766, 0x6767, 0xf576, 0xf578,
  0x6768, 0xf579,
  /* 0x8280 */
  0xf57a, 0xf57b, 0x3072, 0x6769, 0x7a5e, 0x676a, 0xf57c, 0x4967,
  0x3c47, 0x676c, 0x7a5f, 0x7a60, 0x7a61, 0x3329, 0x3032, 0xf57d,
  0xf57e, 0x7a62, 0x676b, 0x676e, 0x474e, 0x7a63, 0x3f44, 0x3256,
  0xf621, 0x4b27, 0xf622, 0x7a64, 0x375d, 0x365c, 0xf623, 0x676d,
  0xf624, 0x326a, 0x7a65, 0x7a66,
  /* 0x82C0 */
  0x3423, 0x7a67, 0x3171, 0x6772, 0x4e6a, 0x425d, 0x7a68, 0x4944,
  0x677e, 0x3257, 0x677c, 0x677a, 0x6771, 0x676f, 0xf625, 0x6770,
  0x3c63, 0x366c, 0x4377, 0xf626, 0x4651, 0x3151, 0x6774, 0x6773,
  0xf627, 0x6779, 0x6775, 0x6778, 0x7a69, 0x7a6a,
  /* 0x8300 */
  0x7a6b, 0x7a6c, 0x4c50, 0x6777, 0x3258, 0x337d, 0x677b, 0xf628,
  0xf629, 0x677d, 0xf62a, 0x3754, 0x6823, 0x682c, 0x682d, 0xf62c,
  0x302b, 0xf62d, 0x7a6e, 0x6834, 0x3071, 0x682b, 0x7a6f, 0x682a,
  0xf62e, 0x6825, 0x6824, 0x6822, 0x6821, 0x4363, 0x427b, 0x6827,
  0x7a70, 0xf62f,
  /* 0x8340 */
  0x6826, 0x7a71, 0xf630, 0x6829, 0x7a72, 0x4170, 0x3755, 0x3141,
  0x6828, 0x7a73, 0x3953, 0xf62b, 0x7a74, 0xf631, 0x4171, 0x7a6d,
  0xae4a, 0x683a, 0x683b, 0x3259, 0x322e, 0x6838, 0x7a75, 0xf633,
  /* 0x8380 */
  0x682e, 0x7a76, 0x6836, 0x683d, 0x6837, 0xf636, 0x6835, 0x7a77,
  0x6776, 0xf637, 0xf638, 0x6833, 0x7a78, 0x682f, 0xf639, 0xf63a,
  0x3450, 0x6831, 0x683c, 0x6832, 0x7a79, 0x683e, 0x7a7a, 0x6830,
  0x477c, 0x4d69, 0x6839, 0x684f, 0x7a7b,
  /* 0x83C0 */
  0x7a7c, 0x6847, 0x3f7b, 0x7a7d, 0xf63b, 0x3546, 0x365d, 0x6842,
  0x7a7e, 0xf63c, 0x7b21, 0x325b, 0xf63d, 0x3e54, 0x6845, 0x3a5a,
  0xf63e, 0x4551, 0x684a, 0x7b22, 0xf63f, 0x4a6e, 0x7b23, 0x6841,
  0x325a, 0x3856, 0x4929, 0x684b, 0x683f, 0x6848, 0xf640, 0x6852,
  0x6843,
  /* 0x8400 */
  0x7b24, 0x6844, 0x463a, 0x7b25, 0x6849, 0x7b26, 0x6846, 0x4b28,
  0x684c, 0x3060, 0xf641, 0xf642, 0x6840, 0xf643, 0xf645, 0x684e,
  0x684d, 0x476b, 0x6854, 0x685f, 0x337e, 0x6862, 0x6850, 0xf646,
  0x6855, 0x4d6e,
  /* 0x8440 */
  0x685e, 0x7b28, 0x4d55, 0xf647, 0x4e2a, 0xf648, 0xf649, 0xf64a,
  0x4378, 0xf64b, 0xf64c, 0x336b, 0xf64d, 0x7b29, 0x4972, 0x6864,
  0x4621, 0xf64f, 0x3031, 0x685d, 0x6859, 0x4172, 0x6853, 0x685b,
  0x6860, 0x7b2a, 0x472c, 0x7b2b, 0x302a, 0xf650, 0x6858, 0xf651,
  0x6861, 0x4978, 0xf652,
  /* 0x8480 */
  0xf653, 0x685c, 0x6857, 0x7b2c, 0x3e55, 0x3d2f, 0x3c2c, 0xf656,
  0x4c58, 0x4947, 0x7b2d, 0x6867, 0x6870, 0xf657, 0x685a, 0x7b2e,
  0x3377, 0x7b2f, 0x3e78, 0x6865, 0x7b30, 0x686a, 0x4173, 0xf658,
  0x6866,
  /* 0x84C0 */
  0x7b31, 0x686d, 0x7b32, 0x435f, 0x686e, 0x4d56, 0x6863, 0x3338,
  0x6869, 0xf65a, 0xf65b, 0x686c, 0x4c2c, 0xf65c, 0x686f, 0x6868,
  0x686b, 0xf655, 0xf65e, 0xf65f, 0x4b29, 0x4f21, 0xf660, 0xf661,
  0xf662, 0x6873, 0xf663, 0x687a, 0xf664, 0x6872,
  /* 0x8500 */
  0x3c43, 0x6851, 0xf665, 0x4a4e, 0x4c22, 0x6879, 0x6878, 0x6874,
  0x6875, 0x3136, 0xf666, 0x7b35, 0x6877, 0x6871, 0x7b36, 0xf667,
  0xf668, 0x4455, 0xf669, 0x6876, 0x307e, 0x7b37, 0x7b34, 0xf66a,
  0x4222, 0x4a43, 0xf66f,
  /* 0x8540 */
  0x687b, 0x6921, 0x4859, 0x687e, 0x3e56, 0x3c49, 0x6923, 0x363e,
  0xf66b, 0xf670, 0xf671, 0x6924, 0x4979, 0x687d, 0x7b38, 0x6856,
  0xf672, 0xf673, 0xf674, 0x687c, 0x7b39, 0x4f4f, 0x4622, 0x4973,
  0x692b, 0xf66c, 0x6931, 0x7b3c, 0xf676, 0xf677, 0x6932, 0xf678,
  /* 0x8580 */
  0x6925, 0xf679, 0x4776, 0xf67a, 0x692f, 0x6927, 0x6929, 0x7b3d,
  0x7b3e, 0x6933, 0x6928, 0xf67b, 0x692c, 0x3172, 0x4665, 0x692d,
  0x6930, 0xf67c, 0xf67d, 0x7b3f, 0x6926, 0x4126, 0x692a, 0x3b27,
  0x3f45, 0x3730, 0x4c74, 0x7b3b, 0x4c79, 0x3d72, 0x7b40, 0xf723,
  0x6937, 0x6935, 0xf724,
  /* 0x85C0 */
  0x4f4e, 0xf725, 0x6934, 0xf726, 0x7b41, 0x4d75, 0x7b42, 0x6936,
  0x6938, 0x6939, 0xf727, 0xf728, 0x693c, 0x693a, 0xf729, 0xf72a,
  0x4623, 0x693b, 0xf72b, 0x484d, 0x692e, 0x7b43, 0xf72c, 0x3d73,
  0x693d, 0x6942, 0x4174, 0x6941, 0x7b45,
  /* 0x8600 */
  0xf72d, 0x6922, 0x7b46, 0x7b47, 0x6943, 0x4149, 0x693e, 0x6940,
  0x7b48, 0xf72e, 0x7b44, 0x693f, 0x5d31, 0x5d22, 0x7b4a, 0x6945,
  0xf72f, 0xf730, 0x6944, 0xf731, 0xf732, 0x7b4b, 0x4d76, 0x623c,
  0x6946, 0x7b4c, 0xf734, 0xf735, 0x6947,
  /* 0x8640 */
  0xf737, 0x2f68, 0x6948, 0x3857, 0x3554, 0xf739, 0x694a, 0x515d,
  0xf73a, 0x7b4d, 0x3575, 0x7b4e, 0x4e3a, 0x3673, 0x694b, 0x7b50,
  0x694c, 0x436e, 0x7b52, 0xf73b, 0x694d, 0x7b53, 0xf73c, 0x467a,
  0xf73d, 0x303a,
  /* 0x8680 */
  0xf73e, 0xf73f, 0x3263, 0x6952, 0x6953, 0xf740, 0xf741, 0x694e,
  0x3b3d, 0x7b54, 0xf742, 0xf743, 0x694f, 0x4742, 0xf744, 0x6950,
  0x6951, 0x695b, 0x6955, 0x6958, 0xf746, 0xf747, 0x6954, 0x7b55,
  /* 0x86C0 */
  0xf748, 0xf749, 0x6956, 0x6957, 0x3c58, 0x6959, 0x4341, 0x3756,
  0x3342, 0xf74a, 0x695c, 0xf74b, 0xf74c, 0x333f, 0x6961, 0x695d,
  0x6960, 0xf74d, 0x483a, 0xf74e, 0x695e, 0x695f, 0x4948, 0x485a,
  0x6962, 0x427d, 0x696c, 0x7b56, 0x6968, 0x7b57, 0x7b58, 0x326b,
  /* 0x8700 */
  0x6966, 0x4b2a, 0x6967, 0xf750, 0x6964, 0xf751, 0x6965, 0x696a,
  0x696d, 0x7b59, 0x696b, 0xf752, 0xf753, 0x6969, 0x6963, 0xf754,
  0x4358, 0xf755, 0x6974, 0x4c2a, 0xf756, 0xf757, 0xf758, 0x6972,
  0x6973, 0xf759, 0x696e, 0x6970, 0xf75a, 0x6971, 0xf75b, 0x696f,
  /* 0x8740 */
  0xf75c, 0xf75d, 0x4066, 0x4f39, 0x6978, 0x6979, 0xf75e, 0x6a21,
  0x3f2a, 0x697b, 0xf75f, 0x697e, 0x6976, 0x6975, 0x6a22, 0xf760,
  0xf761, 0x325c, 0x697c, 0x6a23, 0x697d, 0x7b5a, 0xf762, 0x697a,
  0x4433, 0x6977, 0xf763, 0x4768,
  /* 0x8780 */
  0x6a27, 0x7b5b, 0x7b5c, 0xf767, 0xf768, 0x4d3b, 0xf769, 0x6a26,
  0xf76a, 0x6a25, 0xf766, 0x6a2e, 0x7b5d, 0x7b5e, 0x6a28, 0x6a30,
  0x7b5f, 0x4d66, 0x6a33, 0x6a2a, 0xf76d,
  /* 0x87C0 */
  0x6a2b, 0xf76f, 0x6a2f, 0x6a32, 0x6a31, 0x6a29, 0xf770, 0x6a2c,
  0x6a3d, 0x7b61, 0xf772, 0x6a36, 0xf774, 0xf775, 0xf776, 0xf777,
  0xf778, 0x7b62, 0xf779, 0x6a34, 0x6a35, 0xf771, 0x6a3a, 0x6a3b,
  0x332a, 0x3542, 0x6a39,
  /* 0x8800 */
  0xf77a, 0xf77b, 0x6a24, 0x7b63, 0x7b64, 0xf77c, 0x6a38, 0x6a3c,
  0x6a37, 0x7b65, 0x6a3e, 0xf77d, 0x7b66, 0x6a40, 0x6a3f, 0x7b67,
  0x6a42, 0x6a41, 0x695a, 0x6a46, 0xf77e, 0xf821, 0x6a43, 0xf822,
  0x6a44, 0x6a45, 0x6a47, 0xf823,
  /* 0x8840 */
  0x376c, 0x6a49, 0x6a48, 0x3d30, 0xf825, 0x3954, 0x5e27, 0x6a4a,
  0x3d51, 0x3339, 0xf826, 0x6a4b, 0x3152, 0x3e57, 0x6a4c, 0xf827,
  0x3955, 0x6a4d, 0x3061, 0xf828, 0x493d, 0xf82b, 0x6a4e, 0xf82d,
  0x3f6a, 0x6a55, 0x6a52, 0x436f, 0x6a53, 0x6a50, 0x365e,
  /* 0x8880 */
  0x6a4f, 0x6a56, 0x3736, 0x425e, 0x6a5c, 0x6a58, 0x4235, 0x6a57,
  0x7b68, 0x6a5a, 0x6a51, 0xf82e, 0x6a5b, 0x6a5d, 0x7b69, 0x486f,
  0x6a59, 0x6a5e, 0x6a60, 0x3853, 0x6a54, 0x3041, 0xf82f, 0xf830,
  0xf831, 0x6a5f,
  /* 0x88C0 */
  0xf832, 0x3a5b, 0x4e76, 0x6a61, 0x6a62, 0x4175, 0x7b6a, 0x7b6b,
  0x4e22, 0xf835, 0xf833, 0xf836, 0x6a63, 0x4d35, 0x6a64, 0x6a65,
  0xf837, 0x4a64, 0x6a66, 0x3a40, 0x4e23, 0x6a6b, 0xf838, 0xf839,
  0x6a6c, 0x3e58, 0x6a6a, 0x7b6d, 0x4d67, 0x6a67, 0x6a69, 0x403d,
  0x3f7e,
  /* 0x8900 */
  0xf83b, 0x6a68, 0x6a6d, 0x4a23, 0x6a6f, 0x6a6e, 0x336c, 0x4b2b,
  0x6a70, 0x7b70, 0x7b71, 0x7b72, 0x7b6e, 0x6a7c, 0x6a72, 0x6a73,
  0x7b73, 0x6a74, 0x6a75, 0x7b74, 0x7b75, 0x6a79, 0xf83d, 0x6a7a,
  0x7b76, 0x6a78,
  /* 0x8940 */
  0x7b77, 0x6a76, 0xf83f, 0x6a71, 0x6a77, 0xf840, 0xf841, 0x6a7b,
  0x7037, 0x3228, 0x6a7e, 0x365f, 0x6a7d, 0xf844, 0x6b22, 0x6b21,
  0x6b24, 0x6b23, 0x6b25, 0x3d31, 0x6b26, 0x6b27, 0x6b28, 0x403e,
  /* 0x8980 */
  0xf845, 0x4d57, 0x6b29, 0x4a24, 0x4746, 0x6b2a, 0xf846, 0x6b2b,
  0x382b, 0x352c, 0xf847, 0x6b2c, 0x7b78, 0x3b6b, 0x4741, 0x6b2d,
  0x3350, 0xf848, 0x6b2e, 0x6b30, 0x4d77, 0x6b2f, 0x3f46, 0x6b31,
  0x6b32, 0xf849, 0x6b33, 0x3451, 0xf84a, 0x6b34, 0x6b35, 0x6b36,
  /* 0x89C0 */
  0x6b37, 0x3351, 0x7b7a, 0xf84b, 0xf84c, 0x6b38, 0x6b39, 0x6b3a,
  0x3272, 0x7b7b, 0x3f28, 0x6b3b, 0xf84d, 0xf84f, 0xf850, 0x6b3c,
  0x7b7c, 0x6b3d, 0xf851, 0xf852,
  /* 0x8A00 */
  0x3840, 0x447b, 0x6b3e, 0x3757, 0x3f56, 0x6b41, 0x4624, 0x6b40,
  0xf854, 0x7b7d, 0x3731, 0xf855, 0x7b7e, 0x6b3f, 0x4277, 0x352d,
  0x6b42, 0x6b43, 0x3e59, 0xf857, 0x7c21, 0x376d, 0x6b44, 0x4b2c,
  0x405f, 0x3576, 0x4c75, 0x414a, 0xf858, 0x6b45, 0x7c22, 0x3f47,
  0x4370, 0x3e5a, 0xf859,
  /* 0x8A40 */
  0x6b46, 0xf85a, 0x6b49, 0x7c23, 0x6b4a, 0xf85b, 0x7c24, 0x3a3e,
  0x4242, 0x6b48, 0x3e5b, 0x493e, 0xf85c, 0x6b47, 0x7c25, 0x3b6c,
  0x3153, 0x7c26, 0x6b4e, 0x3758, 0x3b6e, 0x3b6d, 0x4f4d, 0x6b4d,
  0x6b4c, 0x4127, 0x354d, 0x4f43, 0x333a, 0x3e5c, 0x7c27, 0x7c28,
  0x6b4b,
  /* 0x8A80 */
  0x6b50, 0x6b51, 0x6b4f, 0x3858, 0x4d40, 0x3b6f, 0x4727, 0xf85e,
  0x6b54, 0x4040, 0x4342, 0x4d36, 0x6b57, 0x386c, 0x403f, 0x6b53,
  0x6b58, 0x386d, 0x6b55, 0x6b56, 0x7c29, 0x6b52, 0x4062, 0x4649,
  0xf85d, 0x432f, 0x325d, 0xf85f, 0x4870, 0x3543, 0xf860, 0x4434,
  /* 0x8AC0 */
  0x6b5b, 0x6b59, 0x434c, 0x4041, 0x3452, 0x6b5a, 0x3f5b, 0x7c2a,
  0x4e4a, 0x4f40, 0xf861, 0x6b5c, 0x6b67, 0x4435, 0x6b66, 0x7c2b,
  0x6b63, 0x6b6b, 0x6b64, 0x6b60, 0x447c, 0x6b5f, 0x6b5d, 0x4d21,
  0x3b70, 0x6b61, 0x6b5e, 0x7c2c, 0x7c2d, 0x6b65, 0x3d74, 0x3841,
  0xf862, 0x427a,
  /* 0x8B00 */
  0x4b45, 0x315a, 0x3062, 0x4625, 0xf865, 0x6b69, 0xf864, 0x6b68,
  0xf866, 0x4666, 0x6b6d, 0x6b62, 0x6b6c, 0x6b6e, 0x382c, 0x6b6a,
  0x3956, 0xf867, 0x3c55, 0xf868, 0x6b6f, 0x4d58, 0x6b72, 0x6b75,
  0x6b73, 0x4935, 0xf869, 0x6b70, 0x3660, 0x6b74,
  /* 0x8B40 */
  0x6b76, 0xf86a, 0x7c31, 0x6b7a, 0x6b77, 0x6b79, 0x6b78, 0xf86c,
  0x7c32, 0x6b7b, 0x3c31, 0x7c33, 0x6b7d, 0x6b7c, 0x4968, 0xf86d,
  0x6c21, 0x3759, 0x7c34, 0x6b7e, 0x6c22, 0x6c23, 0x3544, 0x6641,
  0x3e79, 0x6c24, 0xf86e, 0x386e, 0x6c25, 0xf86f,
  /* 0x8B80 */
  0x6c26, 0xf870, 0x3b3e, 0x5a4e, 0xf871, 0x6c27, 0x6c28, 0x3d32,
  0x6c29, 0x6c2a, 0xf872, 0xf873, 0x6c2b, 0x6c2c, 0x6c2d, 0xf874,
  0x7c35, 0xf875,
  /* 0x8C00 */
  0x432b, 0xf876, 0x6c2e, 0xf878, 0x6c30,
  /* 0x8C40 */
  0x6c2f, 0xf87b, 0x4626, 0xf87c, 0x6c31, 0x7c36, 0x4b2d, 0x6c32,
  0x6c33, 0xf87d, 0x6c34, 0xf87e, 0x6c35, 0xf921, 0x465a, 0x3e5d,
  0x6c36, 0x7c37, 0xf922, 0x396b, 0x502e, 0x6c37, 0xf923, 0xf924,
  0x6c38, 0x493f, 0x6c39, 0x6c41,
  /* 0x8C80 */
  0x6c3a, 0x6c3c, 0x6c3b, 0x6c3d, 0x4b46, 0x6c3e, 0x6c3f, 0xf927,
  0xf926, 0x6c40, 0x6c42, 0xf928, 0xf92a, 0x332d, 0x4467, 0x4969,
  0x3a62, 0x3957, 0xf92b, 0x494f, 0x325f, 0x484e, 0x6c45, 0x3453,
  0x4055, 0x6c44, 0x6c49, 0x4379, 0x4c63, 0x6c47, 0x6c48, 0x352e,
  0x6c4a, 0x4763, 0x425f, 0x4871, 0x453d, 0x6c46, 0x4b47,
  /* 0x8CC0 */
  0x326c, 0x6c4c, 0x4f28, 0x4442, 0x4f45, 0x3b71, 0x6c4b, 0x4231,
  0x6c5c, 0x4128, 0x4678, 0x4950, 0xf92d, 0xf92c, 0xf92e, 0x6c4f,
  0x3b3f, 0x3b72, 0x3e5e, 0x4765, 0x7c39, 0x382d, 0x6c4e, 0x6c4d,
  0x496a, 0x3c41, 0x4552, 0xf930, 0xf931, 0x7c3a, 0x7c3b, 0x6c51,
  0x6c52, 0x3958, 0x6c50, 0x7c3c,
  /* 0x8D00 */
  0x6c53, 0x6c54, 0x6c56, 0x4223, 0xf933, 0x6c55, 0x3466, 0x6c58,
  0xf934, 0x6c57, 0x6c59, 0x7c3e, 0x6c5b, 0x6c5d, 0x6c5e, 0x7c3f,
  /* 0x8D40 */
  0x4056, 0x3c4f, 0x6c5f, 0x3352, 0xf935, 0x6c60, 0x4176, 0x6c61,
  0x6c62, 0x496b, 0x352f,
  /* 0x8D80 */
  0x6c63, 0xf936, 0x4436, 0x315b, 0xf937, 0x6c64, 0x3c71, 0xf938,
  0x3f76, 0x7c40, 0x422d, 0x6c67, 0x6c66,
  /* 0x8DC0 */
  0x6c65, 0xf93a, 0xf93b, 0x6c6d, 0x6c6b, 0x7c41, 0x6c68, 0x7c42,
  0x6c6a, 0x7c43, 0xf93c, 0x6c69, 0x6c6c, 0x3577, 0x6c70, 0x4057,
  0x6c71, 0x3859, 0x6c6e, 0x6c6f, 0xf93d, 0x4f29, 0x4437, 0x4129,
  0x6c72, 0xf940, 0x6c75,
  /* 0x8E00 */
  0xf941, 0x6c73, 0x6c74, 0x4d59, 0xf93e, 0x4627, 0x6c78, 0xf943,
  0xf944, 0x6c76, 0x6c77, 0x6c79, 0x7c44, 0xf945, 0xf946, 0x7c45,
  0xf947, 0x6d29, 0x6c7c, 0x6c7d, 0x6c7b, 0xf94a, 0xf94b, 0x7c46,
  /* 0x8E40 */
  0x6c7a, 0x447d, 0x6d21, 0x6d25, 0x6d22, 0x6c7e, 0xf94c, 0x6d23,
  0x6d24, 0xf94d, 0x6d2b, 0x6d26, 0x4058, 0x6d28, 0xf94e, 0x6d2a,
  0x6d27, 0xf94f, 0xf950, 0xf951, 0x7c47, 0x6d2d, 0x3d33, 0x6d2c,
  0x7c48, 0x6d2e,
  /* 0x8E80 */
  0x6d2f, 0x6d32, 0x6d31, 0x6d30, 0x6d34, 0x6d33, 0x4c76, 0x6d36,
  0x6d35, 0x6d37, 0xf952, 0x6d38, 0xf953, 0x6d3a, 0x6d39, 0x3f48,
  0x6d3b, 0xf954, 0x366d, 0x6d3c, 0x6d3e, 0xf955, 0xf956, 0xf957,
  0xf958, 0x6d3f,
  /* 0x8EC0 */
  0x7c4a, 0x6d40, 0x6d3d, 0x6d41, 0x3c56, 0x6d42, 0x3530, 0x3733,
  0xf95a, 0x382e, 0xf95b, 0x6d43, 0x4670, 0x453e, 0x6d44, 0x6d47,
  0x3c34, 0xf95d, 0x7c4c, 0x6d46, 0x6d45, 0x375a, 0x6d48,
  /* 0x8F00 */
  0xf95f, 0x3353, 0x6d4a, 0xf960, 0x3a5c, 0x6d49, 0x6d52, 0x6d4c,
  0x6d4e, 0x4a65, 0x6d4b, 0xf961, 0x6d4d, 0x6d51, 0x6d4f, 0x3531,
  0x7c4d, 0x6d50, 0x6d53, 0x475a, 0x4e58, 0xf962, 0x7c4e, 0x3d34,
  0x6d54, 0x7c4f, 0x4d22, 0x6d56, 0x6d55, 0x6d59, 0x4d41,
  /* 0x8F40 */
  0xf963, 0x6d58, 0x336d, 0x6d57, 0x6d5c, 0x6d5b, 0xf964, 0x6d5a,
  0x4532, 0x6d5d, 0x7c50, 0x6d5e, 0xf965, 0x6d5f, 0x396c, 0x3725,
  0x6d60, 0x6d61, 0x6d62,
  /* 0x8F80 */
  0x3f49, 0x6d63, 0x3c2d, 0x6d64, 0x6d65, 0xf967, 0x7c52, 0x5221,
  0x517e, 0x6d66, 0x6570, 0x6d67, 0x4324, 0x3f2b, 0x4740, 0xf968,
  0x7c53, 0xf96a, 0x6d68, 0x4a55, 0x4454, 0x397e, 0x4329,
  /* 0x8FC0 */
  0xf96c, 0x312a, 0x4b78, 0x3f57, 0xf96d, 0xf96f, 0xf970, 0x375e,
  0x3661, 0xf971, 0x4a56, 0xf972, 0x6d69, 0xf973, 0x6d6b, 0x7c54,
  0x6d6a, 0x3260, 0x7c55, 0x4676, 0x6d6c, 0x4777, 0x4533, 0x7c56,
  0x6d6d, 0x3d52, 0xf974, 0x6d6f, 0xf975, 0x4c42, 0x6d7e, 0x6d71,
  0x6d72, 0xf976, 0x4449,
  /* 0x9000 */
  0x4260, 0x4177, 0xf977, 0x4628, 0x6d70, 0x3555, 0x7c57, 0x6d79,
  0xf978, 0x6d76, 0x6e25, 0x4629, 0x4360, 0x6d73, 0x447e, 0x4553,
  0x6d74, 0x6d78, 0x3f60, 0x4767, 0x444c, 0x4042, 0x6d77, 0x422e,
  0x4224, 0x6d75, 0x3029, 0x4f22, 0x6d7a, 0x7c58, 0x4261, 0x3d35,
  0x3f4a, 0x6d7c, 0x6d7b, 0xf979, 0x306f, 0x6d7d, 0x492f, 0x6e27,
  /* 0x9040 */
  0x465b, 0x3f6b, 0xf97b, 0xf97c, 0x4359, 0x3678, 0x6e26, 0x4d37,
  0x313f, 0x4a57, 0x3261, 0x6e21, 0x6e22, 0x6e23, 0x6e24, 0x463b,
  0x4323, 0x3063, 0x6e28, 0x6e29, 0x7423, 0x423d, 0xf97d, 0x6e2a,
  0x3173, 0x414c, 0x382f, 0x4d5a, 0x6e2b, 0x452c, 0x4178, 0x3c57,
  0x6e2c, 0x6e2f, 0x3d65, 0x6e2d, 0x412b, 0x412a, 0x3064, 0x4e4b,
  0x6e31, 0x4872,
  /* 0x9080 */
  0x6e33, 0x6e32, 0x6e30, 0x6364, 0x3454, 0xfa22, 0x6d6e, 0x7c5a,
  0x6e35, 0x6e34, 0xfa23, 0x6e36, 0xfa24, 0x4d38, 0x7c5b, 0x7c5c,
  0x7c5d, 0x7c5e, 0xfa26, 0x7c5f, 0x4661, 0x4b2e, 0x6e37, 0x3c59,
  0x6e38, 0xfa28, 0x6e39, 0x7c60, 0x6e3a, 0xfa29, 0x4521, 0x7c61,
  /* 0x90C0 */
  0x306a, 0xfa2a, 0x7c62, 0x7c63, 0x7c64, 0xfa2b, 0x3959, 0x4f3a,
  0x7c65, 0x6e3e, 0xfa2d, 0x7c66, 0x7c67, 0xfa2e, 0x3734, 0x6e3b,
  0x6e3c, 0x4974, 0xfa33, 0x3354, 0x7c68, 0xfa31, 0x7c69, 0x4d39,
  0xfa30, 0x363f, 0x4554, 0xfa34, 0xfa35,
  /* 0x9100 */
  0xfa32, 0x6e3f, 0xfa36, 0xfa37, 0x6e40, 0x7c6b, 0x7c6c, 0x7c6d,
  0xfa38, 0x6e41, 0xfa39, 0xfa3a, 0x7c6e, 0x7c6f, 0x7c70, 0x4522,
  0x7c71, 0x6e43, 0x7c72, 0x6e42, 0x7c73, 0xfa3b, 0xfa3c, 0xfa3d,
  0x7c74,
  /* 0x9140 */
  0xfa3e, 0xfa3f, 0x7c75, 0x4653, 0x6e44, 0x3d36, 0x3c60, 0x475b,
  0x4371, 0x3c72, 0x3f6c, 0x6e45, 0xfa40, 0x6e46, 0xfa41, 0x7c76,
  0xfa42, 0x3f5d, 0x6e47, 0xfa43, 0x6e48, 0x6e49, 0x4d6f, 0x3d37,
  0x6e4b, 0x6e4a, 0xfa44, 0x395a, 0x3973, 0x3b40, 0xfa45,
  /* 0x9180 */
  0x6e4e, 0x7c77, 0xfa46, 0x3d66, 0x6e4d, 0x6e4c, 0x4269, 0xfa47,
  0x386f, 0x4043, 0x4830, 0x3d39, 0x7c78, 0x6e4f, 0x3e5f, 0xfa48,
  0x6e52, 0x6e50, 0x7c79, 0xfa49, 0x6e51, 0x7c7a, 0xfa4a, 0x6e54,
  0x6e53, 0xfa4b, 0x3e7a, 0x6e55, 0x7c7b,
  /* 0x91C0 */
  0x6e56, 0x6e57, 0xfa4c, 0xfa4d, 0x4850, 0x3a53, 0x3c61, 0x6e58,
  0x6e59, 0x4e24, 0x3d45, 0x4c6e, 0x4e4c, 0x6e5a, 0x3662, 0x6e5b,
  0x7c7c, 0x4523, 0xfa4e, 0x6e5e, 0x3378, 0x3f4b, 0x6e5c, 0x6e5d,
  0x4460, 0x7c7e, 0x7d21, 0x4b55, 0x367c, 0xfa51, 0x7d22, 0xfa52,
  0x7d23, 0x6e60, 0x6e61, 0x7c7d, 0x6e5f, 0x6e63,
  /* 0x9200 */
  0xfa53, 0x7d24, 0xfa54, 0x465f, 0x3343, 0x7d25, 0x6e67, 0x6e64,
  0x6e66, 0xfa55, 0xfa56, 0x6e62, 0x6f4f, 0x6e65, 0xfa58, 0x4e6b,
  0x385a, 0x7d26, 0x7d27, 0x7d28, 0x7d29, 0x6e6f,
  /* 0x9240 */
  0x7d2a, 0xfa59, 0x7d2b, 0x4534, 0x6e6a, 0xfa5a, 0x6e6d, 0x6e6b,
  0xfa5b, 0x6e70, 0xfa5c, 0x7d2c, 0x6e71, 0xfa5d, 0xfa5e, 0x6e69,
  0xfa5f, 0x6e76, 0x3174, 0x6e68, 0xfa60, 0xfa61, 0x482d, 0x6e6c,
  0xfa62, 0x3e60, 0xfa63, 0xfa64, 0x395b, 0x7d2d, 0xfa67, 0xfa68,
  0x4b48, 0xfa69,
  /* 0x9280 */
  0x3664, 0x3d46, 0x463c, 0x7d2e, 0xfa6a, 0xfa6b, 0x412d, 0x6e74,
  0x6e6e, 0x6e73, 0xfa6c, 0x4c43, 0xfa6d, 0x4438, 0x6e75, 0x6e72,
  0xfa6e, 0xfa6f, 0xfa70, 0x412c, 0xfa73, 0x6e79, 0x6e78, 0xfa74,
  /* 0x92C0 */
  0xfa75, 0x7d2f, 0xfa76, 0x7d30, 0x7d31, 0xfa77, 0x6e77, 0xfa78,
  0x4b2f, 0x7d32, 0xfa79, 0xfa7a, 0x7d33, 0x3d7b, 0xfa7b, 0xfa7c,
  0x6e7a, 0x4a5f, 0x3154, 0x4946, 0x4372, 0xfb22, 0x3578, 0xfb23,
  0x6e7c, 0xfb24, 0x395d, 0x7d34,
  /* 0x9300 */
  0xfb25, 0x7d35, 0x3b2c, 0xfb26, 0x6e7b, 0x3f6d, 0xfa7d, 0xfb27,
  0x3f6e, 0x6f21, 0x6f23, 0xfb28, 0xfb29, 0x7d36, 0x3e7b, 0x7d37,
  0x6f22, 0x6f24, 0x7d38, 0x3653, 0xfb2a, 0x4945, 0xfb2b, 0x3c62,
  0x4f23, 0x6e7e, 0x3a78, 0x4f3f, 0x6f26, 0x6f25, 0x6f27,
  /* 0x9340 */
  0x6e7d, 0xfb2e, 0x7d39, 0x7d3a, 0x7d3b, 0x4669, 0x4555, 0xfb2f,
  0x4457, 0x6f2c, 0xfb30, 0xfb31, 0x4343, 0x6f28, 0x6f29, 0x7d3c,
  0x7d3d, 0x7d3e, 0xfb32, 0x372d, 0x6f2b, 0x7d3f, 0xfb33, 0xfb34,
  0x3830, 0x6f2a, 0x3e61,
  /* 0x9380 */
  0xfb38, 0xfb39, 0x3379, 0xfb3a, 0x6f30, 0x3a3f, 0x4179, 0x444a,
  0x7d40, 0xfb3b, 0xfb35, 0x7d41, 0x333b, 0x6f2e, 0x6f2f, 0x4443,
  0x6f2d, 0x6f31, 0x7d42,
  /* 0x93C0 */
  0xfb40, 0x6f37, 0x7d43, 0xfb41, 0x6f3a, 0x6f39, 0x452d, 0x6f32,
  0x6f33, 0x6f36, 0xfb42, 0x6f38, 0x7d44, 0x7d45, 0x3640, 0xfb43,
  0x6f3b, 0x6f35, 0xfb44, 0x6f34, 0xfb3f, 0xfb3c, 0xfb49, 0x7d47,
  /* 0x9400 */
  0x6f3f, 0x7d46, 0x6f40, 0xfb45, 0xfb46, 0x6f41, 0x6f3e, 0x6f3d,
  0xfb47, 0xfb48, 0x3e62, 0x462a, 0x6f3c, 0x6f45, 0x6f43, 0xfb4a,
  0x7d48, 0xfb4b, 0x6f44, 0x6f42, 0x4278, 0x6f46, 0xfb4c,
  /* 0x9440 */
  0x6f47, 0x6f49, 0xfb4d, 0x7d49, 0x3455, 0x6f48, 0x4c7a, 0x6f54,
  0x6f4a, 0x6f4d, 0x6f4b, 0x6f4c, 0x7d4a, 0x6f4e, 0x7d4b, 0xfb50,
  0xfb51, 0x6f50, 0x7d4c, 0x7d4d, 0x6f51, 0x6f52, 0x6f55, 0x6f53,
  0x6f56, 0x6f58,
  /* 0x9480 */
  0x6f57,
  /* 0x9540 */
  0x4439, 0xfb52, 0xfb53,
  /* 0x9580 */
  0x4c67, 0x6f59, 0x412e, 0xfb54, 0x6f5a, 0x4a44, 0x6f5b, 0x332b,
  0xfb55, 0xfb56, 0x7d4e, 0x313c, 0x3457, 0x3456, 0x6f5c, 0x6f5d,
  0x6f5e, 0x6f5f, 0x7d4f, 0x6f60, 0x3458, 0x3355, 0x395e, 0x4836,
  0x7d50, 0x6f62, 0x6f61, 0x7d51, 0xfb58, 0x7d52, 0x6f63, 0x315c,
  0xfb59, 0x7d53, 0x6f66, 0x6f65, 0x6f64, 0x7d54, 0x6f67,
  /* 0x95C0 */
  0x6f6a, 0x3047, 0xfb5b, 0x6f68, 0x7d55, 0x6f6c, 0x6f6b, 0x7d56,
  0x7d57, 0x6f6e, 0x6f6d, 0x6f6f, 0x462e, 0x7d59, 0x6f70, 0x7d5a,
  0x6f71, 0x6f73, 0x6f72,
  /* 0x9600 */
  0x496c, 0xfa25, 0x6f74, 0x6f75, 0x3a65, 0xfb5e, 0x6f76, 0x6f77,
  0x4b49, 0xfb5f, 0xfb60, 0x414b, 0xfb62, 0x3024,
  /* 0x9640 */
  0x424b, 0xfb63, 0x6f78, 0x496d, 0x6f7b, 0x6f79, 0x395f, 0x6f7a,
  0x3842, 0x7d5b, 0x4a45, 0x6f7d, 0x7021, 0x6f7e, 0x7022, 0xfb64,
  0x3121, 0x3f58, 0x3d7c, 0x3459, 0x7023, 0x4766, 0x7025, 0x3122,
  0x7024, 0x4444, 0x4e4d, 0x462b, 0x6f7c, 0x4e26, 0x3831, 0x4d5b,
  /* 0x9680 */
  0xfb66, 0x7d5c, 0x3679, 0x4e34, 0x3728, 0x4262, 0x6721, 0x7026,
  0x332c, 0x3f6f, 0x3356, 0x7028, 0x7029, 0x7027, 0x3764, 0xfb68,
  0x3a5d, 0x3e63, 0x7d5e, 0x3123, 0x4e59, 0x7d5f, 0x7d60, 0x702b,
  0x6e2e, 0xfb6b, 0x702a, 0xfb6c, 0x702e, 0x702c, 0x702d, 0xfb6d,
  0x702f, 0x7030, 0x4e6c, 0x7031, 0x7032, 0xfb6e, 0x4049, 0x483b,
  0xfb6f,
  /* 0x96C0 */
  0x3f7d, 0x3467, 0x4d3a, 0x326d, 0x3d38, 0x385b, 0x7035, 0x7034,
  0x3b73, 0x7036, 0x7033, 0x3b28, 0x7d61, 0x703a, 0x6a2d, 0xfb72,
  0x5256, 0xfb73, 0x3f77, 0x7038, 0xfb74, 0x7d62, 0x4e25, 0x4671,
  0x312b, 0x7d64, 0x4063, 0x3c36, 0x7d65, 0x4a37, 0x3140, 0x4e6d,
  0x4d6b, 0x703b, 0x4545,
  /* 0x9700 */
  0x3c7b, 0x703c, 0x703d, 0x3f4c, 0x703e, 0x4e6e, 0x7039, 0x7040,
  0x7042, 0x7041, 0x703f, 0xfb76, 0x7043, 0x7044, 0x417a, 0x3262,
  0xfb77, 0x7045, 0x4c38, 0x7046, 0x7047, 0x4f2a, 0x7d66, 0xfb79,
  0x5b31, 0x7048, 0x7d67, 0x7049, 0x704a,
  /* 0x9740 */
  0xfb7a, 0x704e, 0x704b, 0x704c, 0xfb7b, 0x704d, 0x704f, 0x7d68,
  0x7d69, 0x7d6a, 0x4044, 0xfb7c, 0x4c77, 0xfb7d, 0x4045, 0x7d6b,
  0xfb7e, 0x7050, 0x4873, 0x7051, 0x7353, 0x4c4c, 0x7052, 0x7053,
  0x7054, 0x3357, 0xfc21, 0x7056, 0x3f59, 0x7d6c, 0x7057, 0x7d6d,
  0x3724, 0x7058, 0x705c, 0x705a,
  /* 0x9780 */
  0x705b, 0x3373, 0x7059, 0x705d, 0x705e, 0x3048, 0x705f, 0x7060,
  0x7d6e, 0xfc24, 0x3e64, 0xfc25, 0x7061, 0xfc26, 0x3547, 0xfc27,
  0x7064, 0x7063, 0x7062, 0x6b71, 0x4a5c, 0x7d6f, 0xfc28, 0xfc29,
  0x7065, 0x7066, 0x7d70, 0xfc2a,
  /* 0x97C0 */
  0x7d71, 0x7067, 0x7068, 0x7069, 0x7d72, 0x706a, 0xfc2b, 0xfc2c,
  0x345a, 0xfc2d, 0xfc2e, 0xfc2f, 0x7d74, 0x706b, 0x7d73, 0xfc30,
  0x706c, 0x4723, 0xfc31, 0x706e, 0x323b, 0x7d75, 0x7071, 0x7070,
  0x3124, 0x3641,
  /* 0x9800 */
  0x4a47, 0x443a, 0x3a22, 0xfc32, 0x3960, 0x3d67, 0x3f5c, 0x7d77,
  0x7073, 0xfc33, 0xfc34, 0x7072, 0x4d42, 0x3468, 0x4852, 0x465c,
  0xfc35, 0xfc36, 0x3f7c, 0x4e4e, 0x375b, 0x7d78, 0x7076, 0xfc39,
  0x7075, 0xfc3c, 0x7d79, 0x4b4b, 0x462c, 0x7d7a, 0xfc3a, 0xfc3b,
  0x3150, 0x7077, 0x7074, 0x4951, 0x4d6a, 0x7078,
  /* 0x9840 */
  0x7079, 0xfc3d, 0x707b, 0x426a, 0x335b, 0x335c, 0x707a, 0x7d7c,
  0x7d7d, 0x3469, 0x3832, 0x7d7e, 0x7e21, 0x346a, 0x7e22, 0x7e23,
  0x453f, 0x4e60, 0x7e25, 0xfc3e, 0x385c, 0x707c, 0x7e26, 0x707d,
  0x707e, 0x7121, 0x7123, 0x7122,
  /* 0x9880 */
  0x4977, 0x7124, 0xfc3f, 0xfc40, 0x7125, 0xfc41, 0x7126, 0x7127,
  0xfc43, 0xfc44, 0x7e27, 0xfc45, 0xfc46, 0xfc47,
  /* 0x98C0 */
  0xfc48, 0x7129, 0x7128, 0x712a, 0xfc49, 0x7e28, 0xfc4a, 0x4874,
  0x664c, 0x3f29, 0xfc4b, 0xfc4d, 0x3532, 0xfc4e, 0xfc4f, 0x7e29,
  0x712b, 0xfc50, 0x712c, 0x522c, 0x5d3b, 0x4853, 0xfc51, 0xfc52,
  0x307b, 0xfc53, 0x303b, 0x3b74, 0x4b30, 0x3e7e,
  /* 0x9900 */
  0x712d, 0x4c5f, 0xfc54, 0x712e, 0x4d5c, 0x3142, 0x3b41, 0x712f,
  0x326e, 0x7130, 0xfc57, 0xfc58, 0x7131, 0xfc5a, 0xfc5b, 0xfc5c,
  0x7133, 0x7134, 0x7136, 0x7132, 0x7135, 0x345b, 0x7137, 0x7138,
  0xfc5e, 0xfc5f, 0xfc60, 0xfc61, 0xfc62, 0xfc63, 0x7139, 0x713a,
  /* 0x9940 */
  0xfc64, 0xfc65, 0x713b, 0x713d, 0xfc66, 0x713c, 0x713f, 0x7142,
  0xfc67, 0xfc68, 0x713e, 0x7140, 0x7141, 0x7143, 0x3642, 0x7e2a,
  0xfc69, 0xfc6a, 0xfc6b,
  /* 0x9980 */
  0x3c73, 0x7144, 0x7145, 0x3961, 0x7e2b, 0xfc6c, 0x7146, 0xfc6d,
  0x333e, 0x474f, 0x7147, 0x7148, 0x435a, 0x466b, 0xfc6e, 0x7149,
  0xfc6f, 0xfc70,
  /* 0x99C0 */
  0x477d, 0xfc71, 0x424c, 0x3158, 0x366e, 0x366f, 0xfc72, 0x4373,
  0x714e, 0x3670, 0xfc73, 0x326f, 0x714d, 0xfc74, 0x714b, 0x714c,
  0xfc75, 0x714a, 0x7158, 0x714f, 0x7150, 0xfc77, 0x7151, 0x7152,
  0x7154, 0xfc78, 0x7153, 0xfc79, 0x3d59,
  /* 0x9A00 */
  0x7155, 0x7e2c, 0x7e2d, 0x7157, 0xfc7a, 0x3533, 0x7156, 0xfc7b,
  0x417b, 0x3833, 0xfc7c, 0x7159, 0xfc7d, 0xfc7e, 0x7e2e, 0x424d,
  0x715a, 0x7e2f, 0x7e30, 0x462d, 0xfd21, 0xfd22, 0x715b, 0x7e31,
  0x7160,
  /* 0x9A40 */
  0x715e, 0x715d, 0x715f, 0xfd23, 0x715c, 0x7e32, 0xfd24, 0x7162,
  0x7e33, 0x7e34, 0x7161, 0x7164, 0xfd25, 0x3643, 0x7163, 0x7165,
  0x7166, 0x7168, 0x7167, 0x7169, 0x716b, 0x716a,
  /* 0x9A80 */
  0x397c, 0x716c, 0xfd27, 0x716d, 0x7e35, 0xfd29, 0x333c, 0xfd2b,
  0x716e,
  /* 0x9AC0 */
  0x716f, 0x7e36, 0x7e37, 0x3f71, 0xfd2d, 0x7e38, 0x7170, 0xfd2e,
  0x7171, 0xfd2f, 0x7172, 0x7173, 0xfd30, 0x7e39, 0x3962, 0xfd32,
  0x7174, 0x7175, 0xfd33, 0x7176, 0x7177, 0xfd34, 0x7178, 0xfd35,
  0x4831, 0x717a, 0x4926, 0x717b, 0x7179, 0x717d, 0x717c, 0x717e,
  0x7e3a, 0x7221,
  /* 0x9B00 */
  0x7e3b, 0xfd36, 0x7222, 0x7e3c, 0xfd37, 0xfd38, 0xfd39, 0xfd3a,
  0x7223, 0x7224, 0xfd3b, 0x7225, 0x7e3d, 0x7226, 0x7227, 0x7228,
  0x7229, 0x722a, 0x722b, 0x722c, 0xfd3c, 0x7e3f, 0x722d, 0x722e,
  0x5d35, 0x722f, 0xfd3d, 0x6478, 0x3534, 0xfd3e,
  /* 0x9B40 */
  0x3321, 0x3a32, 0x7231, 0x7230, 0x4c25, 0xfd40, 0x7233, 0x7234,
  0x7232, 0x7235, 0x4b62, 0x7236, 0x357b, 0x7e40, 0xfd41, 0xfd42,
  0x7e42, 0xfd43, 0xfd44, 0x4f25, 0x7e43, 0xfd45, 0x7237, 0x7e44,
  0xfd46, 0xfd47, 0x7e41,
  /* 0x9B80 */
  0x7239, 0x7e45, 0x7e46, 0x303e, 0x7e47, 0x723a, 0x4a2b, 0x7238,
  0x723b, 0x723c, 0x7e48, 0x723d, 0x723e, 0xfd48, 0x7e49, 0x723f,
  0x4b6e, 0x3b2d, 0xfd49, 0x3a7a, 0x412f, 0xfd4a, 0xfd4d, 0x7240,
  0xfd4e, 0x7243, 0xfd4f,
  /* 0x9BC0 */
  0x7241, 0x7e4a, 0x7244, 0xfd50, 0x3871, 0x7242, 0x7e4b, 0x7245,
  0x7246, 0x7247, 0x724b, 0x3b2a, 0xfd52, 0x4264, 0xfd53, 0x724c,
  0x7249, 0x7248, 0x724a, 0x7e4c, 0xfd54, 0x375f, 0xfd55, 0xfd56,
  0xfd58, 0xfd57, 0x7250, 0x724f, 0x724e, 0xfd51, 0x3033, 0xfd5c,
  0x7e4d, 0xfd5a, 0x7e4e,
  /* 0x9C00 */
  0x7e4f, 0x725a, 0x7256, 0x7257, 0x7253, 0x7259, 0x7255, 0x3362,
  0x4f4c, 0x7258, 0x7254, 0x7252, 0x7251, 0xfd5e, 0xfd5f, 0xfd60,
  0xfd61, 0x725c, 0xfd62, 0x725f, 0xfd63, 0x7e50, 0x725e, 0x725d,
  0xfd64, 0xfd65, 0xfd66, 0x4949, 0x725b, 0x3073, 0x7260, 0xfd68,
  0x7262, 0xfd69, 0xfd6a, 0x336f, 0x724d, 0x3137, 0x7264,
  /* 0x9C40 */
  0x7e51, 0xfd6b, 0x7263, 0x7261, 0x432d, 0xfd6e, 0xfd6f, 0x7e52,
  0x7e53, 0x4b70, 0x7e54, 0xfd71, 0x4e5a, 0xfd72, 0x7265, 0xfd73,
  0xfd6c, 0xfd74, 0xfd75, 0x7266, 0x7e55, 0x7e56, 0x7267, 0xfd76,
  0xfd77, 0xfd78, 0xfd79, 0xfd7a, 0xfd7b, 0xfd7c, 0xfd7d, 0x7268,
  0x7e57, 0x7269, 0xfd7e,
  /* 0x9CC0 */
  0x443b, 0xfe21, 0x726a, 0x4837, 0x726f, 0x726b, 0x726c, 0xfe22,
  0x4b31, 0x4c44, 0x4650,
  /* 0x9D00 */
  0xfe24, 0x7270, 0x7271, 0x463e, 0x726e, 0x726d, 0xfe23, 0x322a,
  0xfe26, 0x7279, 0x7278, 0xfe27, 0xfe28, 0x3175, 0x7e58, 0x7e59,
  0x7276, 0x7275, 0x7273, 0x337b, 0x7272, 0x3c32, 0x3229, 0xfe2c,
  0x3963, 0x727c, 0x727b,
  /* 0x9D40 */
  0x727a, 0xfe2e, 0x7e5a, 0x7277, 0x727d, 0x7e5b, 0x727e, 0xfe2f,
  0x7325, 0x7324, 0x7e5c, 0x7326, 0x312d, 0x7321, 0x7322, 0xfe30,
  0x3974, 0x4c39, 0xfe31, 0x7e5d, 0x7323, 0xfe33, 0xfe34, 0x4b32,
  0x732b, 0x7e5e, 0x7327, 0xfe36, 0xfe37, 0xfe38, 0x732c, 0x7e5f,
  0xfe39,
  /* 0x9D80 */
  0xfe3a, 0x7329, 0x7328, 0x7e60, 0xfe3b, 0x375c, 0x7e61, 0xfe3c,
  0x732d, 0xfe3d, 0x732e, 0x732f, 0x732a, 0x7e63, 0x7274, 0x7330,
  0x4461, 0xfe3f, 0x7334, 0xfe40, 0x7335, 0x7333, 0x7e64, 0xfe41,
  0xfe3e,
  /* 0x9DC0 */
  0x7e62, 0x7332, 0x7338, 0xfe42, 0x7331, 0x7336, 0xfe43, 0xfe44,
  0x7337, 0x733a, 0xfe45, 0x7e65, 0x7339, 0xfe46, 0xfe47, 0xfe48,
  0xfe49, 0x733c, 0x7e67, 0x733d, 0x733e, 0x4f49, 0xfe4a, 0x733b,
  0x426b, 0x3a6d, 0x733f,
  /* 0x9E00 */
  0xfe4d, 0x7e68, 0xfe4c, 0xfe4e, 0x7e69, 0xfe4f, 0x7340, 0x7341,
  0xfe50, 0xfe51, 0x7342,
  /* 0x9E40 */
  0x7343, 0x3834, 0x7344, 0xfe52, 0x7e6a, 0x7345, 0x3c2f,
  /* 0x9E80 */
  0xfe54, 0x7346, 0xfe55, 0x7347, 0x7348, 0x7349, 0x734c, 0x734a,
  0x4f3c, 0x734b, 0x4e6f, 0xfe56, 0x734d, 0x7e6b, 0x4e5b, 0x7e6c,
  0x734e, 0x477e, 0xfe57, 0x734f, 0x7351, 0x7e6d, 0x7352, 0x7e6e,
  0x7e6f, 0x7e70, 0x7350, 0x396d, 0x4c4d, 0x4b63, 0x5677, 0xfe59,
  0x5d60, 0x4b7b,
  /* 0x9EC0 */
  0x7e71, 0x322b, 0x7354, 0x3550, 0x7355, 0x7356, 0x7357, 0x7e72,
  0x3975, 0x7358, 0x6054, 0x4c5b, 0x4263, 0x7359, 0x735b, 0x735a,
  0xfe5b, 0x735c, 0x735d, 0xfe5c, 0x735e, 0xfe5d, 0x735f, 0x7360,
  0x7361, 0x7362, 0x7363, 0x7364, 0x7365, 0x7366, 0xfe5e,
  /* 0x9F00 */
  0xfe5f, 0xfe61, 0x7367, 0x7368, 0x4524, 0x7e73, 0x385d, 0x736a,
  0xfe62, 0xfe63, 0x414d, 0x736b, 0x736c, 0xfe64, 0xfe65, 0x7e74,
  0xfe66, 0x4921, 0xfe67, 0x736d,
  /* 0x9F40 */
  0xfe68, 0xfe69, 0xfe6a, 0x736e, 0x6337, 0x6c5a, 0x706d, 0x736f,
  0xfe6b, 0x7370, 0xfe6c, 0x7e75, 0xfe6d, 0xfe6f, 0x7372, 0x7373,
  0x7374, 0x4e70, 0x7371, 0x7375, 0x7376, 0xfe71, 0x7378, 0x7377,
  0xfe73, 0xfe74, 0x737a, 0xfe75, 0x737b, 0x7379,
  /* 0x9F80 */
  0x4e36, 0x7e76, 0x7e77, 0x737c, 0x7e78, 0x737d, 0x6354, 0x737e,
  0x7e79,
  /* 0xF900 */
  0x763b, 0x742e, 0x754e, 0x7b4f,
  /* 0xF940 */
  0x7649,
  /* 0xF9C0 */
  0x7e24, 0x7d5d,
  /* 0xFA00 */
  0x2f4b, 0x2f57, 0x4f72, 0xae79, 0x757a, 0x775a, 0x776f, 0x793c,
  0x793d, 0x7941, 0x7b3a, 0xf738, 0xf745, 0x7c2e, 0xf96e, 0x7c6a,
  0x2e38, 0x2e49, 0x2e50, 0x2e63, 0x2e68, 0x2e6e, 0x2f2c, 0x2f2f,
  0x2f36, 0x2f5a, 0x2f5e, 0x4f61, 0x4f62, 0x7450, 0x745c, 0x745e,
  /* 0xFA40 */
  0x7461, 0x7528, 0x752b, 0x7543, 0x7565, 0x7669, 0x7677, 0x7725,
  0x7755, 0xf029, 0x7825, 0x7927, 0x7933, 0x7934, 0x7937, 0x7938,
  0x7939, 0x793b, 0x793f, 0x7940, 0x794d, 0x7951, 0x7964, 0x7a2e,
  0xf450, 0x7a33, 0x7a3a, 0x7a44, 0x7a58, 0xf574, 0xf575, 0x7b27,
  0x7b6f, 0x7b79, 0x7c2f, 0x7c30, 0x7c38, 0x7c3d, 0xf969, 0x7c59,
  0x7d63, 0x7d76, 0x7d7b,
  /* 0xFE40 */
  0x233e, 0x233d,
  /* 0xFF00 */
  0x212a, 0x2230, 0x2174, 0x2170, 0x2173, 0x2175, 0x222f, 0x214a,
  0x214b, 0x2176, 0x215c, 0x2124, 0x2231, 0x2125, 0x213f, 0x2330,
  0x2331, 0x2332, 0x2333, 0x2334, 0x2335, 0x2336, 0x2337, 0x2338,
  0x2339, 0x2127, 0x2128, 0x2163, 0x2161, 0x2164, 0x2129, 0x2177,
  0x2341, 0x2342, 0x2343, 0x2344, 0x2345, 0x2346, 0x2347, 0x2348,
  0x2349, 0x234a, 0x234b, 0x234c, 0x234d, 0x234e, 0x234f, 0x2350,
  0x2351, 0x2352, 0x2353, 0x2354, 0x2355, 0x2356, 0x2357, 0x2358,
  0x2359, 0x235a, 0x214e, 0x2140, 0x214f, 0x2130, 0x2132,
  /* 0xFF40 */
  0x212e, 0x2361, 0x2362, 0x2363, 0x2364, 0x2365, 0x2366, 0x2367,
  0x2368, 0x2369, 0x236a, 0x236b, 0x236c, 0x236d, 0x236e, 0x236f,
  0x2370, 0x2371, 0x2372, 0x2373, 0x2374, 0x2375, 0x2376, 0x2377,
  0x2378, 0x2379, 0x237a, 0x2150, 0x2143, 0x2151, 0x2232, 0x2256,
  0x2257,
  /* 0xFFC0 */
  0x2131, 0x216f,
  /* 0x20000 */
  0x2e22,
  /* 0x20080 */
  0xa121, 0xa12b, 0xa12e,
  /* 0x20180 */
  0xa136,
  /* 0x20200 */
  0xa146,
  /* 0x20300 */
  0xa170,
  /* 0x20340 */
  0xa179,
  /* 0x20380 */
  0xa177,
  /* 0x203C0 */
  0xa322,
  /* 0x20440 */
  0xa325,
  /* 0x20500 */
  0xa327,
  /* 0x205C0 */
  0xa331,
  /* 0x20600 */
  0xa332,
  /* 0x20740 */
  0xa338,
  /* 0x20800 */
  0xa33f, 0xa341,
  /* 0x20880 */
  0xa34a,
  /* 0x20940 */
  0xa352,
  /* 0x20980 */
  0xa353,
  /* 0x20AC0 */
  0xa359,
  /* 0x20B00 */
  0xa35c,
  /* 0x20B80 */
  0x4f54,
  /* 0x20D40 */
  0xa377,
  /* 0x20DC0 */
  0xa42a,
  /* 0x20E40 */
  0xa43a, 0xa432,
  /* 0x20E80 */
  0xa431,
  /* 0x20F40 */
  0xa43d,
  /* 0x21200 */
  0xa459, 0x2f42,
  /* 0x21240 */
  0xa45c, 0xa463, 0xa45e,
  /* 0x212C0 */
  0xa46b, 0xa46a, 0xa472,
  /* 0x21300 */
  0x2f4c, 0xa474,
  /* 0x21340 */
  0xa475,
  /* 0x213C0 */
  0xa525,
  /* 0x21440 */
  0xa532, 0x2f60,
  /* 0x215C0 */
  0xa53e,
  /* 0x21640 */
  0xa547,
  /* 0x21680 */
  0x4f63,
  /* 0x21700 */
  0xa555,
  /* 0x21740 */
  0xa556,
  /* 0x21880 */
  0x2f7b,
  /* 0x219C0 */
  0xa57e,
  /* 0x21C40 */
  0xa830,
  /* 0x21D00 */
  0xa837,
  /* 0x21D40 */
  0xa838, 0xa83b, 0xa83a,
  /* 0x21D80 */
  0xa845, 0xa840, 0xa83f, 0xa848,
  /* 0x21DC0 */
  0xa84a,
  /* 0x21E00 */
  0xa84b, 0x4f6e,
  /* 0x21F00 */
  0xa85b,
  /* 0x21F40 */
  0xa866,
  /* 0x21FC0 */
  0xa86c,
  /* 0x22140 */
  0xac22,
  /* 0x22200 */
  0xfe53,
  /* 0x22300 */
  0xac2b,
  /* 0x22380 */
  0xac30,
  /* 0x226C0 */
  0xac50,
  /* 0x22840 */
  0xac65,
  /* 0x22880 */
  0xac6d,
  /* 0x22980 */
  0xac72,
  /* 0x22A80 */
  0xad24,
  /* 0x22B40 */
  0xad32, 0xad29, 0xad2a,
  /* 0x22B80 */
  0xad35,
  /* 0x22C00 */
  0xad34, 0xad39,
  /* 0x22DC0 */
  0xad56,
  /* 0x23180 */
  0xae24,
  /* 0x231C0 */
  0xad7d, 0x753a, 0xae23,
  /* 0x23340 */
  0xae3a,
  /* 0x233C0 */
  0xae42, 0xae3d, 0xae3c, 0xae44, 0xae47, 0xae49, 0xae43,
  /* 0x23440 */
  0xae55, 0xae57, 0xae56, 0xae5b,
  /* 0x234C0 */
  0xae77,
  /* 0x23540 */
  0xae78,
  /* 0x23580 */
  0xaf2a,
  /* 0x235C0 */
  0x7572,
  /* 0x23600 */
  0xaf42, 0xaf3f, 0xaf43,
  /* 0x23640 */
  0xaf40,
  /* 0x23700 */
  0xaf59, 0xaf4e, 0x7629,
  /* 0x23740 */
  0x7632, 0xaf61,
  /* 0x237C0 */
  0xaf6a, 0xaf69,
  /* 0x23800 */
  0xaf70, 0xaf75,
  /* 0x23A80 */
  0xee23,
  /* 0x23C40 */
  0xee34,
  /* 0x23CC0 */
  0x7660,
  /* 0x23D00 */
  0xee49, 0xf475,
  /* 0x23D40 */
  0xee5c,
  /* 0x23DC0 */
  0xee60, 0xee5f, 0xee5e,
  /* 0x23F40 */
  0xef32,
  /* 0x24080 */
  0xef47,
  /* 0x24100 */
  0xef4d,
  /* 0x241C0 */
  0xef61, 0xef64,
  /* 0x24380 */
  0xf022,
  /* 0x24600 */
  0xf033,
  /* 0x24680 */
  0xf039,
  /* 0x247C0 */
  0x776c,
  /* 0x24880 */
  0xf053,
  /* 0x24A40 */
  0xf07b,
  /* 0x24B40 */
  0xf12e, 0xf130,
  /* 0x24C00 */
  0xf135,
  /* 0x24D00 */
  0xf144,
  /* 0x24E00 */
  0xf15d, 0xf161,
  /* 0x24E40 */
  0xf166,
  /* 0x24E80 */
  0xf169,
  /* 0x25040 */
  0xf175, 0xf177,
  /* 0x25100 */
  0xf17a,
  /* 0x25180 */
  0xf221,
  /* 0x251C0 */
  0xf224, 0xf223,
  /* 0x25200 */
  0xf228,
  /* 0x25240 */
  0xf22c,
  /* 0x25400 */
  0xf23d,
  /* 0x25480 */
  0x787e,
  /* 0x254C0 */
  0xf248,
  /* 0x25500 */
  0x7929,
  /* 0x25580 */
  0xf25b,
  /* 0x25740 */
  0x7947,
  /* 0x25780 */
  0xf275, 0xf276,
  /* 0x259C0 */
  0x7954, 0xf332,
  /* 0x25AC0 */
  0xf33e, 0xf33d, 0xf340,
  /* 0x25B80 */
  0xf352,
  /* 0x25C40 */
  0xf35d, 0xf35e,
  /* 0x25D80 */
  0x796e,
  /* 0x25E00 */
  0xf373,
  /* 0x25E40 */
  0xf374, 0xf377, 0xf375,
  /* 0x25EC0 */
  0xf37d, 0xf37b, 0xf422,
  /* 0x25F00 */
  0xf424,
  /* 0x25F40 */
  0xf427,
  /* 0x25FC0 */
  0xf42f, 0xf42e, 0xf435,
  /* 0x26000 */
  0xf434, 0xf43d,
  /* 0x26040 */
  0xf442,
  /* 0x260C0 */
  0xf44f,
  /* 0x26240 */
  0xf469,
  /* 0x26280 */
  0xf46b,
  /* 0x26340 */
  0xf472,
  /* 0x26400 */
  0xf479,
  /* 0x26640 */
  0xf535,
  /* 0x26680 */
  0xf53a,
  /* 0x26700 */
  0xf546,
  /* 0x268C0 */
  0xf556, 0xf558,
  /* 0x26940 */
  0xf55a, 0xf55d,
  /* 0x269C0 */
  0xf55f,
  /* 0x26A00 */
  0xf563,
  /* 0x26A40 */
  0xf56a,
  /* 0x26A80 */
  0xf570, 0xf573,
  /* 0x26AC0 */
  0x7a5d,
  /* 0x26C00 */
  0xa544,
  /* 0x26C40 */
  0xf644,
  /* 0x26CC0 */
  0xf64e,
  /* 0x26E40 */
  0x7b33, 0xf65d,
  /* 0x26F80 */
  0xf675,
  /* 0x26FC0 */
  0xf721, 0xf722, 0xf67e,
  /* 0x270C0 */
  0x7b49,
  /* 0x27100 */
  0xf733, 0xf736,
  /* 0x273C0 */
  0xf765, 0xf764, 0xf76b,
  /* 0x27400 */
  0xf76e,
  /* 0x27440 */
  0xf773,
  /* 0x27600 */
  0xf82a, 0xf829, 0xf82c,
  /* 0x27680 */
  0x7b6c, 0xf834,
  /* 0x27700 */
  0xf83c, 0xf83e,
  /* 0x27740 */
  0xf842,
  /* 0x27980 */
  0xf856,
  /* 0x27A80 */
  0xf863,
  /* 0x27B80 */
  0xf877, 0xf879,
  /* 0x27BC0 */
  0xf87a,
  /* 0x27C80 */
  0xf925,
  /* 0x27D80 */
  0xf92f,
  /* 0x27E00 */
  0xf932,
  /* 0x27F80 */
  0xf939,
  /* 0x28080 */
  0xf942, 0xf948,
  /* 0x28240 */
  0x7c49,
  /* 0x28280 */
  0xf959,
  /* 0x282C0 */
  0xf95e,
  /* 0x283C0 */
  0x7c51,
  /* 0x28400 */
  0xf966,
  /* 0x28440 */
  0xf96b,
  /* 0x28540 */
  0xf97a,
  /* 0x285C0 */
  0xf97e, 0xfa21,
  /* 0x286C0 */
  0xfa2c, 0xfa2f,
  /* 0x28940 */
  0xfa50, 0xfa4f, 0xfa57,
  /* 0x28980 */
  0xfa65, 0xfa66, 0xfa71, 0xfa72,
  /* 0x28A00 */
  0xfa7e, 0xfb21,
  /* 0x28A40 */
  0xfb2d, 0xfb2c,
  /* 0x28A80 */
  0xfb36,
  /* 0x28AC0 */
  0xfb37, 0xfb3e, 0xfb3d,
  /* 0x28BC0 */
  0xfb4e, 0xfb4f,
  /* 0x28D00 */
  0xfb57,
  /* 0x28D40 */
  0xfb5a,
  /* 0x28DC0 */
  0xfb5c,
  /* 0x28E00 */
  0xfb5d, 0xfb61,
  /* 0x28E80 */
  0xfb65,
  /* 0x28EC0 */
  0xfb67,
  /* 0x28F00 */
  0xfb69,
  /* 0x28FC0 */
  0xfb71,
  /* 0x29280 */
  0xfc22, 0xfc23,
  /* 0x29480 */
  0xfc38,
  /* 0x295C0 */
  0xfc42,
  /* 0x29640 */
  0xfc4c,
  /* 0x296C0 */
  0xfc56,
  /* 0x29700 */
  0xfc59,
  /* 0x29740 */
  0xfc5d,
  /* 0x298C0 */
  0xfc76,
  /* 0x29A40 */
  0xfd2c,
  /* 0x29DC0 */
  0xfd4b,
  /* 0x29E00 */
  0xfd59, 0xfd4c,
  /* 0x29E40 */
  0xfd5d,
  /* 0x29E80 */
  0xfd5b,
  /* 0x29EC0 */
  0xfd67, 0xfd70, 0xfd6d,
  /* 0x29FC0 */
  0xfe25,
  /* 0x2A000 */
  0xfe2b, 0xfe29,
  /* 0x2A080 */
  0xfe35,
  /* 0x2A0C0 */
  0xfe32,
  /* 0x2A180 */
  0x7e66,
  /* 0x2A380 */
  0xfe58,
  /* 0x2A400 */
  0xfe5a,
  /* 0x2A5C0 */
  0xfe6e,
  /* 0x2A600 */
  0xfe70, 0xfe72,
  /* 0x2A680 */
  0xfe76,
};

static const Summary16 jisx0213_from_ucs_level2_2indx[] = {
  /* 0x0080 */
  {    0, 0x0000 }, {    0, 0x0000 }, {    0, 0xffdf }, {   15, 0xffdf },
  /* 0x00C0 */
  {   30, 0xffff }, {   46, 0xffff }, {   62, 0xffff }, {   78, 0xffff },
  /* 0x0100 */
  {   94, 0xf3ff }, {  108, 0x3f0e }, {  117, 0x0cb0 }, {  122, 0x6630 },
  /* 0x0140 */
  {  128, 0x399e }, {  137, 0xff3f }, {  151, 0xfc3f }, {  163, 0x7e03 },
  /* 0x0180 */
  {  171, 0x0000 }, {  171, 0x0008 }, {  172, 0x0000 }, {  172, 0x0000 },
  /* 0x01C0 */
  {  172, 0x6004 }, {  175, 0x1557 }, {  183, 0x0000 }, {  183, 0x2300 },
  /* 0x0240 */
  {  186, 0x0000 }, {  186, 0xd7ff }, {  200, 0xf1f3 }, {  211, 0x6e2f },
  /* 0x0280 */
  {  221, 0x7f1e }, {  232, 0x2137 }, {  239, 0x0006 }, {  241, 0x0000 },
  /* 0x02C0 */
  {  241, 0x1180 }, {  244, 0x6b03 }, {  251, 0x03e0 }, {  256, 0x0000 },
  /* 0x0300 */
  {  256, 0x995f }, {  266, 0xf700 }, {  273, 0x9631 }, {  280, 0x3e11 },
  /* 0x0340 */
  {  287, 0x0000 }, {  287, 0x0000 }, {  287, 0x0002 }, {  288, 0x0000 },
  /* 0x0380 */
  {  288, 0x0000 }, {  288, 0xfffe }, {  303, 0x03fb }, {  312, 0xfffe },
  /* 0x03C0 */
  {  327, 0x03ff }, {  337, 0x0000 }, {  337, 0x0000 }, {  337, 0x0000 },
  /* 0x0400 */
  {  337, 0x0002 }, {  338, 0xffff }, {  354, 0xffff }, {  370, 0xffff },
  /* 0x0440 */
  {  386, 0xffff }, {  402, 0x0002 }, {  403, 0x0000 }, {  403, 0x0000 },
  /* 0x1E00 */
  {  403, 0x0000 }, {  403, 0x0000 }, {  403, 0x0000 }, {  403, 0xc000 },
  /* 0x1F40 */
  {  405, 0x0000 }, {  405, 0x0000 }, {  405, 0x0000 }, {  405, 0x000f },
  /* 0x2000 */
  {  409, 0x0000 }, {  409, 0x3359 }, {  417, 0x0067 }, {  422, 0x980d },
  /* 0x2040 */
  {  428, 0x0384 }, {  432, 0x0002 }, {  433, 0x0000 }, {  433, 0x0000 },
  /* 0x2080 */
  {  433, 0x0000 }, {  433, 0x0000 }, {  433, 0x1000 }, {  434, 0x0000 },
  /* 0x2100 */
  {  434, 0x8008 }, {  436, 0x0048 }, {  438, 0x0882 }, {  441, 0x0020 },
  /* 0x2140 */
  {  442, 0x0000 }, {  442, 0x0038 }, {  445, 0x0fff }, {  457, 0x0fff },
  /* 0x2180 */
  {  469, 0x0000 }, {  469, 0x03df }, {  478, 0x0000 }, {  478, 0x0000 },
  /* 0x21C0 */
  {  478, 0x0010 }, {  479, 0x0014 }, {  481, 0x03c0 }, {  485, 0x0000 },
  /* 0x2200 */
  {  485, 0x0bad }, {  493, 0xe40c }, {  499, 0x5fe1 }, {  509, 0x2030 },
  /* 0x2240 */
  {  512, 0x0128 }, {  515, 0x0004 }, {  516, 0x0cc7 }, {  523, 0x00c0 },
  /* 0x2280 */
  {  525, 0x0cfc }, {  533, 0x00e0 }, {  536, 0x0020 }, {  537, 0x8000 },
  /* 0x22C0 */
  {  538, 0x0000 }, {  538, 0x0c00 }, {  540, 0x0000 }, {  540, 0x0000 },
  /* 0x2300 */
  {  540, 0x0060 }, {  542, 0x0104 }, {  544, 0x0000 }, {  544, 0x0000 },
  /* 0x2380 */
  {  544, 0x0000 }, {  544, 0x0000 }, {  544, 0x0000 }, {  544, 0xc000 },
  /* 0x23C0 */
  {  546, 0x5fff }, {  560, 0x0000 }, {  560, 0x0000 }, {  560, 0x0000 },
  /* 0x2400 */
  {  560, 0x0000 }, {  560, 0x0000 }, {  560, 0x0008 }, {  561, 0x0000 },
  /* 0x2440 */
  {  561, 0x0000 }, {  561, 0x0000 }, {  561, 0xffff }, {  577, 0x000f },
  /* 0x24C0 */
  {  581, 0x0000 }, {  581, 0xffff }, {  597, 0xfbff }, {  612, 0x7fff },
  /* 0x2500 */
  {  627, 0x900f }, {  633, 0x3999 }, {  641, 0x9939 }, {  649, 0x9999 },
  /* 0x2540 */
  {  657, 0x0804 }, {  659, 0x0000 }, {  659, 0x0000 }, {  659, 0x0000 },
  /* 0x2580 */
  {  659, 0x0000 }, {  659, 0x0000 }, {  659, 0x0003 }, {  661, 0x30ce },
  /* 0x25C0 */
  {  668, 0xcac3 }, {  676, 0x000f }, {  680, 0x8040 }, {  682, 0x0000 },
  /* 0x2600 */
  {  682, 0x406f }, {  689, 0x40c0 }, {  692, 0x0000 }, {  692, 0x0000 },
  /* 0x2640 */
  {  692, 0x0005 }, {  694, 0x0000 }, {  694, 0xffff }, {  710, 0x0000 },
  /* 0x2700 */
  {  710, 0x0000 }, {  710, 0x0008 }, {  711, 0x0000 }, {  711, 0x0000 },
  /* 0x2740 */
  {  711, 0x0000 }, {  711, 0x0040 }, {  712, 0x0000 }, {  712, 0xffc0 },
  /* 0x2900 */
  {  722, 0x0000 }, {  722, 0x0000 }, {  722, 0x0000 }, {  722, 0x0030 },
  /* 0x2980 */
  {  724, 0x0000 }, {  724, 0x0000 }, {  724, 0x0000 }, {  724, 0x8000 },
  /* 0x29C0 */
  {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0000 }, {  725, 0x0c00 },
  /* 0x3000 */
  {  727, 0xffef }, {  742, 0xb3ff }, {  755, 0x0001 }, {  756, 0x3838 },
  /* 0x3040 */
  {  762, 0xfffe }, {  777, 0xffff }, {  793, 0xffff }, {  809, 0xffff },
  /* 0x3080 */
  {  825, 0xffff }, {  841, 0xf87f }, {  853, 0xffff }, {  869, 0xffff },
  /* 0x30C0 */
  {  885, 0xffff }, {  901, 0xffff }, {  917, 0xffff }, {  933, 0xffff },
  /* 0x31C0 */
  {  949, 0x0000 }, {  949, 0x0000 }, {  949, 0x0000 }, {  949, 0xffff },
  /* 0x3200 */
  {  965, 0x0000 }, {  965, 0x0000 }, {  965, 0x0000 }, {  965, 0x0206 },
  /* 0x3240 */
  {  968, 0x0000 }, {  968, 0xfffe }, {  983, 0x0000 }, {  983, 0x0000 },
  /* 0x3280 */
  {  983, 0x0000 }, {  983, 0x0000 }, {  983, 0x01f0 }, {  988, 0xfffe },
  /* 0x32C0 */
  { 1003, 0x0000 }, { 1003, 0xffff }, { 1019, 0x322f }, { 1027, 0x0400 },
  /* 0x3300 */
  { 1028, 0x2008 }, { 1030, 0x0110 }, { 1032, 0x08cc }, { 1037, 0x0840 },
  /* 0x3340 */
  { 1039, 0x2600 }, { 1042, 0x0082 }, { 1044, 0x0000 }, { 1044, 0x7800 },
  /* 0x3380 */
  { 1048, 0xc000 }, { 1050, 0x7000 }, { 1053, 0x0002 }, { 1054, 0x0000 },
  /* 0x33C0 */
  { 1054, 0x2810 }, { 1057, 0x0000 }, { 1057, 0x0000 }, { 1057, 0x0000 },
  /* 0x3400 */
  { 1057, 0x0044 }, { 1059, 0x0000 }, { 1059, 0x5000 }, { 1061, 0x0000 },
  /* 0x3440 */
  { 1061, 0x0000 }, { 1061, 0x0000 }, { 1061, 0x0500 }, { 1063, 0x0000 },
  /* 0x3480 */
  { 1063, 0x0000 }, { 1063, 0x0004 }, { 1064, 0x0000 }, { 1064, 0x1020 },
  /* 0x34C0 */
  { 1066, 0x0082 }, { 1068, 0x0800 }, { 1069, 0x0000 }, { 1069, 0x0000 },
  /* 0x3500 */
  { 1069, 0x0000 }, { 1069, 0x8000 }, { 1070, 0x0000 }, { 1070, 0x0000 },
  /* 0x3540 */
  { 1070, 0x0000 }, { 1070, 0x6000 }, { 1072, 0x4008 }, { 1074, 0x0000 },
  /* 0x3580 */
  { 1074, 0x0000 }, { 1074, 0x0000 }, { 1074, 0x0140 }, { 1076, 0x0000 },
  /* 0x35C0 */
  { 1076, 0x0020 }, { 1077, 0x0400 }, { 1078, 0x0000 }, { 1078, 0x0010 },
  /* 0x3600 */
  { 1079, 0x0020 }, { 1080, 0x0000 }, { 1080, 0x0000 }, { 1080, 0x0000 },
  /* 0x3640 */
  { 1080, 0x0400 }, { 1081, 0x0000 }, { 1081, 0x0000 }, { 1081, 0x0000 },
  /* 0x3680 */
  { 1081, 0x0000 }, { 1081, 0x0242 }, { 1084, 0x0000 }, { 1084, 0x0000 },
  /* 0x36C0 */
  { 1084, 0x8000 }, { 1085, 0x0000 }, { 1085, 0x0000 }, { 1085, 0x0000 },
  /* 0x3740 */
  { 1085, 0x0000 }, { 1085, 0x0000 }, { 1085, 0x1806 }, { 1089, 0x0020 },
  /* 0x3780 */
  { 1090, 0x2000 }, { 1091, 0x0000 }, { 1091, 0x0000 }, { 1091, 0x0000 },
  /* 0x37C0 */
  { 1091, 0x0002 }, { 1092, 0x0000 }, { 1092, 0x0104 }, { 1094, 0x2010 },
  /* 0x3800 */
  { 1096, 0x0001 }, { 1097, 0x0000 }, { 1097, 0x8000 }, { 1098, 0x0040 },
  /* 0x3840 */
  { 1099, 0x0001 }, { 1100, 0x1000 }, { 1101, 0x0002 }, { 1102, 0x0000 },
  /* 0x38C0 */
  { 1102, 0x0000 }, { 1102, 0x0000 }, { 1102, 0x0000 }, { 1102, 0x0400 },
  /* 0x3900 */
  { 1103, 0x0000 }, { 1103, 0x0480 }, { 1105, 0x0000 }, { 1105, 0x0000 },
  /* 0x3940 */
  { 1105, 0x0000 }, { 1105, 0x0000 }, { 1105, 0x8000 }, { 1106, 0x0000 },
  /* 0x3A40 */
  { 1106, 0x0000 }, { 1106, 0x0000 }, { 1106, 0x4000 }, { 1107, 0x0008 },
  /* 0x3AC0 */
  { 1108, 0x0000 }, { 1108, 0x00c0 }, { 1110, 0x0400 }, { 1111, 0x0000 },
  /* 0x3B00 */
  { 1111, 0x4000 }, { 1112, 0x1400 }, { 1114, 0x0004 }, { 1115, 0x0000 },
  /* 0x3B40 */
  { 1115, 0x0000 }, { 1115, 0x0000 }, { 1115, 0x2000 }, { 1116, 0x0080 },
  /* 0x3B80 */
  { 1117, 0x2180 }, { 1120, 0x0000 }, { 1120, 0x0010 }, { 1121, 0x0040 },
  /* 0x3BC0 */
  { 1122, 0x2008 }, { 1124, 0x0000 }, { 1124, 0x0000 }, { 1124, 0x0001 },
  /* 0x3C00 */
  { 1125, 0x8000 }, { 1126, 0x0000 }, { 1126, 0x0040 }, { 1127, 0x0000 },
  /* 0x3CC0 */
  { 1127, 0x0008 }, { 1128, 0x0004 }, { 1129, 0x0000 }, { 1129, 0x0000 },
  /* 0x3D00 */
  { 1129, 0x0000 }, { 1129, 0x4002 }, { 1131, 0x0000 }, { 1131, 0x0000 },
  /* 0x3D40 */
  { 1131, 0x0000 }, { 1131, 0x0000 }, { 1131, 0x0010 }, { 1132, 0x0000 },
  /* 0x3D80 */
  { 1132, 0x0000 }, { 1132, 0x0400 }, { 1133, 0x0000 }, { 1133, 0x0000 },
  /* 0x3DC0 */
  { 1133, 0x0001 }, { 1134, 0x0010 }, { 1135, 0x0000 }, { 1135, 0x0000 },
  /* 0x3E00 */
  { 1135, 0x0020 }, { 1136, 0x0000 }, { 1136, 0x0000 }, { 1136, 0x8000 },
  /* 0x3E40 */
  { 1137, 0x0000 }, { 1137, 0x0000 }, { 1137, 0x0141 }, { 1140, 0x0000 },
  /* 0x3E80 */
  { 1140, 0x0008 }, { 1141, 0x0010 }, { 1142, 0x0000 }, { 1142, 0x0000 },
  /* 0x3F40 */
  { 1142, 0x0000 }, { 1142, 0x0080 }, { 1143, 0x0000 }, { 1143, 0x00a4 },
  /* 0x3F80 */
  { 1146, 0x0000 }, { 1146, 0x0000 }, { 1146, 0x4000 }, { 1147, 0x0000 },
  /* 0x3FC0 */
  { 1147, 0x0200 }, { 1148, 0x0080 }, { 1149, 0x0000 }, { 1149, 0x0000 },
  /* 0x4000 */
  { 1149, 0x0000 }, { 1149, 0x0000 }, { 1149, 0x0000 }, { 1149, 0x0200 },
  /* 0x4040 */
  { 1150, 0x0000 }, { 1150, 0x0100 }, { 1151, 0x0000 }, { 1151, 0x0000 },
  /* 0x4080 */
  { 1151, 0x0000 }, { 1151, 0x0008 }, { 1152, 0x0000 }, { 1152, 0x0000 },
  /* 0x4100 */
  { 1152, 0x0020 }, { 1153, 0x0000 }, { 1153, 0x0000 }, { 1153, 0x0000 },
  /* 0x4140 */
  { 1153, 0x8100 }, { 1155, 0x0000 }, { 1155, 0x0008 }, { 1156, 0x0000 },
  /* 0x4180 */
  { 1156, 0x0000 }, { 1156, 0x0000 }, { 1156, 0x0000 }, { 1156, 0x8010 },
  /* 0x41C0 */
  { 1158, 0x0000 }, { 1158, 0x0000 }, { 1158, 0x4040 }, { 1160, 0x0008 },
  /* 0x4200 */
  { 1161, 0x4080 }, { 1163, 0x0000 }, { 1163, 0x0000 }, { 1163, 0x0000 },
  /* 0x4240 */
  { 1163, 0x0000 }, { 1163, 0x0000 }, { 1163, 0x0010 }, { 1164, 0x0000 },
  /* 0x42C0 */
  { 1164, 0x0040 }, { 1165, 0x2040 }, { 1167, 0x0000 }, { 1167, 0x0000 },
  /* 0x4300 */
  { 1167, 0x0004 }, { 1168, 0x0000 }, { 1168, 0x0800 }, { 1169, 0x0000 },
  /* 0x4340 */
  { 1169, 0x0008 }, { 1170, 0x0000 }, { 1170, 0x0000 }, { 1170, 0x0000 },
  /* 0x43C0 */
  { 1170, 0x0000 }, { 1170, 0x0000 }, { 1170, 0x4000 }, { 1171, 0x0001 },
  /* 0x4400 */
  { 1172, 0x0100 }, { 1173, 0x1080 }, { 1175, 0x0004 }, { 1176, 0x0000 },
  /* 0x4440 */
  { 1176, 0x0000 }, { 1176, 0x0808 }, { 1178, 0x0000 }, { 1178, 0x0440 },
  /* 0x4480 */
  { 1180, 0x0000 }, { 1180, 0x0002 }, { 1181, 0x0000 }, { 1181, 0x4008 },
  /* 0x44C0 */
  { 1183, 0x0000 }, { 1183, 0x0010 }, { 1184, 0x0000 }, { 1184, 0x0000 },
  /* 0x4500 */
  { 1184, 0x2100 }, { 1186, 0x0000 }, { 1186, 0x0020 }, { 1187, 0x0000 },
  /* 0x4540 */
  { 1187, 0x0008 }, { 1188, 0x0000 }, { 1188, 0x0000 }, { 1188, 0x0000 },
  /* 0x4580 */
  { 1188, 0x0000 }, { 1188, 0x2000 }, { 1189, 0x0000 }, { 1189, 0x0100 },
  /* 0x45C0 */
  { 1190, 0x0000 }, { 1190, 0x0000 }, { 1190, 0x0420 }, { 1192, 0x0000 },
  /* 0x4600 */
  { 1192, 0x8000 }, { 1193, 0x0000 }, { 1193, 0x0000 }, { 1193, 0x0000 },
  /* 0x4640 */
  { 1193, 0x0002 }, { 1194, 0x0000 }, { 1194, 0x0020 }, { 1195, 0x0000 },
  /* 0x4680 */
  { 1195, 0x0000 }, { 1195, 0x0000 }, { 1195, 0x8002 }, { 1197, 0x0000 },
  /* 0x4700 */
  { 1197, 0x1000 }, { 1198, 0x0000 }, { 1198, 0x0000 }, { 1198, 0x0000 },
  /* 0x4740 */
  { 1198, 0x0000 }, { 1198, 0x0000 }, { 1198, 0x0010 }, { 1199, 0x0000 },
  /* 0x47C0 */
  { 1199, 0x0000 }, { 1199, 0x0000 }, { 1199, 0x0000 }, { 1199, 0x2000 },
  /* 0x4800 */
  { 1200, 0x0000 }, { 1200, 0x0040 }, { 1201, 0x0000 }, { 1201, 0x0000 },
  /* 0x4840 */
  { 1201, 0x4010 }, { 1203, 0x0000 }, { 1203, 0x0000 }, { 1203, 0x0000 },
  /* 0x4880 */
  { 1203, 0x0000 }, { 1203, 0x0000 }, { 1203, 0x0000 }, { 1203, 0x0020 },
  /* 0x4980 */
  { 1204, 0x0000 }, { 1204, 0x0000 }, { 1204, 0x0000 }, { 1204, 0x0001 },
  /* 0x49C0 */
  { 1205, 0x0000 }, { 1205, 0x0000 }, { 1205, 0x0080 }, { 1206, 0x0400 },
  /* 0x4A00 */
  { 1207, 0x0010 }, { 1208, 0x0000 }, { 1208, 0x0200 }, { 1209, 0x0000 },
  /* 0x4A80 */
  { 1209, 0x0000 }, { 1209, 0x0000 }, { 1209, 0x0000 }, { 1209, 0x1000 },
  /* 0x4B00 */
  { 1210, 0x0000 }, { 1210, 0x0000 }, { 1210, 0x0000 }, { 1210, 0x0800 },
  /* 0x4BC0 */
  { 1211, 0x0404 }, { 1213, 0x0004 }, { 1214, 0x0100 }, { 1215, 0x0000 },
  /* 0x4C00 */
  { 1215, 0x0000 }, { 1215, 0x0080 }, { 1216, 0x0001 }, { 1217, 0x0000 },
  /* 0x4CC0 */
  { 1217, 0x0010 }, { 1218, 0x0002 }, { 1219, 0x0000 }, { 1219, 0x0000 },
  /* 0x4D00 */
  { 1219, 0x0080 }, { 1220, 0x0000 }, { 1220, 0x0000 }, { 1220, 0x0000 },
  /* 0x4D40 */
  { 1220, 0x0000 }, { 1220, 0x0000 }, { 1220, 0x0000 }, { 1220, 0x0080 },
  /* 0x4E00 */
  { 1221, 0xef8f }, { 1233, 0x43f7 }, { 1243, 0xff42 }, { 1253, 0x9b47 },
  /* 0x4E40 */
  { 1262, 0xe9ad }, { 1272, 0xe7e2 }, { 1282, 0x0204 }, { 1284, 0x400a },
  /* 0x4E80 */
  { 1287, 0x7f65 }, { 1298, 0xfb36 }, { 1309, 0x7977 }, { 1320, 0x1e49 },
  /* 0x4EC0 */
  { 1327, 0xeddf }, { 1340, 0xe7f1 }, { 1351, 0x683a }, { 1358, 0xa8e7 },
  /* 0x4F00 */
  { 1367, 0xee0b }, { 1376, 0x3443 }, { 1382, 0x8000 }, { 1383, 0x75d1 },
  /* 0x4F40 */
  { 1392, 0xe3c8 }, { 1400, 0xfffb }, { 1415, 0x9611 }, { 1421, 0xfde9 },
  /* 0x4F80 */
  { 1433, 0xad6c }, { 1442, 0x2dd6 }, { 1451, 0xe803 }, { 1457, 0xc064 },
  /* 0x4FC0 */
  { 1462, 0xce3c }, { 1471, 0xad97 }, { 1481, 0xc07b }, { 1489, 0x456e },
  /* 0x5000 */
  { 1497, 0xea67 }, { 1507, 0xd75f }, { 1519, 0x7ffe }, { 1533, 0x0a40 },
  /* 0x5040 */
  { 1536, 0xc3cf }, { 1546, 0x14e9 }, { 1553, 0x1468 }, { 1558, 0x2175 },
  /* 0x5080 */
  { 1565, 0x2121 }, { 1569, 0x177e }, { 1579, 0x3408 }, { 1583, 0x4cbe },
  /* 0x50C0 */
  { 1592, 0xf6b4 }, { 1602, 0x4673 }, { 1610, 0x62ea }, { 1618, 0x0a2c },
  /* 0x5100 */
  { 1623, 0x0b5f }, { 1632, 0xcdf4 }, { 1642, 0x0402 }, { 1644, 0x9ca4 },
  /* 0x5140 */
  { 1651, 0x7ffb }, { 1665, 0x14b5 }, { 1672, 0x7f25 }, { 1682, 0x19ea },
  /* 0x5180 */
  { 1690, 0xbe6d }, { 1701, 0x23ef }, { 1711, 0x3f7d }, { 1723, 0x30ff },
  /* 0x51C0 */
  { 1733, 0x3e78 }, { 1742, 0x7840 }, { 1747, 0x66c7 }, { 1756, 0x677b },
  /* 0x5200 */
  { 1767, 0x4ddf }, { 1778, 0x20fe }, { 1786, 0x46b0 }, { 1792, 0x0fc9 },
  /* 0x5240 */
  { 1800, 0xbe98 }, { 1809, 0x78f0 }, { 1817, 0x963a }, { 1825, 0xa0bf },
  /* 0x5280 */
  { 1834, 0x239c }, { 1841, 0x891e }, { 1848, 0xbe59 }, { 1858, 0x5e32 },
  /* 0x52C0 */
  { 1866, 0x37aa }, { 1875, 0xebe7 }, { 1887, 0x00dd }, { 1893, 0xcfad },
  /* 0x5300 */
  { 1904, 0xade7 }, { 1915, 0x36e1 }, { 1923, 0x841b }, { 1929, 0xcf2a },
  /* 0x5340 */
  { 1938, 0x27ef }, { 1949, 0x559e }, { 1958, 0xd2cb }, { 1967, 0xadbb },
  /* 0x5380 */
  { 1978, 0x0014 }, { 1980, 0xa548 }, { 1986, 0x6371 }, { 1994, 0x08dd },
  /* 0x53C0 */
  { 2001, 0x7f0d }, { 2011, 0x8ef0 }, { 2019, 0xff3e }, { 2032, 0x05ff },
  /* 0x5400 */
  { 2042, 0xff1a }, { 2053, 0xe807 }, { 2060, 0x7bd1 }, { 2070, 0x7b40 },
  /* 0x5440 */
  { 2077, 0x674d }, { 2086, 0x8022 }, { 2089, 0x1d44 }, { 2095, 0xb8fb },
  /* 0x5480 */
  { 2106, 0xfd51 }, { 2116, 0x1065 }, { 2121, 0xfb77 }, { 2134, 0xf58c },
  /* 0x54C0 */
  { 2143, 0x03df }, { 2152, 0x0100 }, { 2153, 0xf366 }, { 2163, 0xa40e },
  /* 0x5500 */
  { 2169, 0xc2d3 }, { 2177, 0x0051 }, { 2180, 0xc800 }, { 2183, 0x532a },
  /* 0x5540 */
  { 2190, 0x94f3 }, { 2199, 0x70c9 }, { 2206, 0x001b }, { 2210, 0x7800 },
  /* 0x5580 */
  { 2214, 0x4fdf }, { 2226, 0xf702 }, { 2234, 0x7f80 }, { 2242, 0x8041 },
  /* 0x55C0 */
  { 2245, 0x52b0 }, { 2251, 0xb416 }, { 2258, 0x021c }, { 2262, 0x6280 },
  /* 0x5600 */
  { 2266, 0x43c0 }, { 2271, 0x09d1 }, { 2277, 0x8300 }, { 2280, 0xa9d7 },
  /* 0x5640 */
  { 2290, 0x5285 }, { 2296, 0x4809 }, { 2300, 0xbd51 }, { 2309, 0x0556 },
  /* 0x5680 */
  { 2315, 0x95c1 }, { 2322, 0x6630 }, { 2328, 0x7325 }, { 2336, 0x105c },
  /* 0x56C0 */
  { 2341, 0x672f }, { 2351, 0xcd8a }, { 2359, 0x4109 }, { 2363, 0xa6cd },
  /* 0x5700 */
  { 2372, 0xaf19 }, { 2381, 0x916c }, { 2388, 0xa3ca }, { 2396, 0x0999 },
  /* 0x5740 */
  { 2402, 0xf4e5 }, { 2412, 0x0003 }, { 2414, 0x8752 }, { 2421, 0x98b9 },
  /* 0x5780 */
  { 2429, 0x0b04 }, { 2433, 0x7408 }, { 2438, 0x151d }, { 2445, 0x0109 },
  /* 0x57C0 */
  { 2448, 0xd9c9 }, { 2457, 0xd0dc }, { 2465, 0x2059 }, { 2470, 0xbff1 },
  /* 0x5800 */
  { 2482, 0x0e75 }, { 2490, 0x6220 }, { 2494, 0x8493 }, { 2500, 0x2637 },
  /* 0x5840 */
  { 2508, 0x1e03 }, { 2514, 0x4796 }, { 2522, 0x0a96 }, { 2528, 0x5225 },
  /* 0x5880 */
  { 2534, 0xae28 }, { 2541, 0xf099 }, { 2549, 0x4f00 }, { 2554, 0x4f0a },
  /* 0x58C0 */
  { 2561, 0x74aa }, { 2569, 0xd7be }, { 2581, 0xda35 }, { 2590, 0x3e9f },
  /* 0x5900 */
  { 2601, 0xbe64 }, { 2610, 0x1f71 }, { 2619, 0x7eb4 }, { 2629, 0x6186 },
  /* 0x5940 */
  { 2635, 0xc3d0 }, { 2642, 0xadb3 }, { 2652, 0x77a5 }, { 2662, 0x3178 },
  /* 0x5980 */
  { 2669, 0x2c1e }, { 2676, 0xaa4c }, { 2683, 0x5138 }, { 2689, 0x5a04 },
  /* 0x59C0 */
  { 2694, 0x2b48 }, { 2700, 0x761f }, { 2710, 0x4df8 }, { 2719, 0x8940 },
  /* 0x5A00 */
  { 2723, 0x320a }, { 2728, 0x958a }, { 2735, 0xa2a9 }, { 2742, 0x1060 },
  /* 0x5A40 */
  { 2745, 0x0243 }, { 2749, 0x0420 }, { 2751, 0x34e4 }, { 2758, 0xc480 },
  /* 0x5A80 */
  { 2762, 0x0810 }, { 2764, 0xdc04 }, { 2770, 0x0085 }, { 2773, 0xf42a },
  /* 0x5AC0 */
  { 2781, 0x1a16 }, { 2787, 0x14c1 }, { 2792, 0x426b }, { 2799, 0x0c21 },
  /* 0x5B00 */
  { 2803, 0x1b01 }, { 2808, 0x02c0 }, { 2811, 0x3424 }, { 2816, 0x4055 },
  /* 0x5B40 */
  { 2821, 0x102b }, { 2826, 0xbdf7 }, { 2839, 0x8b78 }, { 2847, 0xb52b },
  /* 0x5B80 */
  { 2856, 0xbbbb }, { 2868, 0xbfe8 }, { 2879, 0x507c }, { 2886, 0x8379 },
  /* 0x5BC0 */
  { 2894, 0x52fd }, { 2904, 0xe95d }, { 2914, 0x5bf6 }, { 2925, 0xe56b },
  /* 0x5C00 */
  { 2935, 0xeffe }, { 2949, 0x444e }, { 2955, 0x2b1d }, { 2963, 0xff03 },
  /* 0x5C40 */
  { 2973, 0xed63 }, { 2983, 0xc82b }, { 2990, 0xd3bf }, { 3002, 0x1643 },
  /* 0x5C80 */
  { 3008, 0x9500 }, { 3012, 0x8013 }, { 3016, 0x3fcf }, { 3028, 0x5dea },
  /* 0x5CC0 */
  { 3038, 0x0aa0 }, { 3042, 0x0205 }, { 3045, 0xa703 }, { 3052, 0x2c51 },
  /* 0x5D00 */
  { 3058, 0x68c0 }, { 3063, 0xaff3 }, { 3075, 0x0ad5 }, { 3082, 0x0202 },
  /* 0x5D40 */
  { 3084, 0x5cc4 }, { 3091, 0x100d }, { 3095, 0xb602 }, { 3101, 0x0049 },
  /* 0x5D80 */
  { 3104, 0x1996 }, { 3111, 0x2295 }, { 3117, 0x5095 }, { 3123, 0x3795 },
  /* 0x5DC0 */
  { 3132, 0x3a00 }, { 3136, 0x69ce }, { 3145, 0x4bff }, { 3157, 0x68be },
  /* 0x5E00 */
  { 3166, 0x184d }, { 3172, 0xaf76 }, { 3183, 0xe820 }, { 3188, 0x61c9 },
  /* 0x5E40 */
  { 3195, 0x52b9 }, { 3203, 0xc1f0 }, { 3210, 0x781e }, { 3218, 0xfffc },
  /* 0x5E80 */
  { 3232, 0x849a }, { 3238, 0x14e0 }, { 3243, 0x3ce1 }, { 3251, 0xc3e0 },
  /* 0x5EC0 */
  { 3258, 0x8f4e }, { 3267, 0xae4d }, { 3276, 0x130f }, { 3283, 0xffdb },
  /* 0x5F00 */
  { 3297, 0xff9f }, { 3311, 0xf9fb }, { 3324, 0xa2e8 }, { 3331, 0x71f2 },
  /* 0x5F40 */
  { 3340, 0x55a3 }, { 3348, 0x33da }, { 3357, 0x3ede }, { 3368, 0xf28f },
  /* 0x5F80 */
  { 3378, 0x9fbf }, { 3391, 0x538f }, { 3400, 0xe797 }, { 3411, 0x33b8 },
  /* 0x5FC0 */
  { 3419, 0x3ab8 }, { 3427, 0x73dc }, { 3437, 0xca17 }, { 3445, 0xb92b },
  /* 0x6000 */
  { 3454, 0xe000 }, { 3457, 0x3bf5 }, { 3468, 0x8ff7 }, { 3480, 0x042a },
  /* 0x6040 */
  { 3484, 0x3cce }, { 3493, 0x8625 }, { 3499, 0xbf3d }, { 3511, 0x80a1 },
  /* 0x6080 */
  { 3515, 0x3e1a }, { 3523, 0xecf4 }, { 3533, 0x07c9 }, { 3540, 0x717f },
  /* 0x60C0 */
  { 3551, 0x09e0 }, { 3556, 0xbf3a }, { 3567, 0x418b }, { 3573, 0x0fff },
  /* 0x6100 */
  { 3585, 0xe34b }, { 3594, 0xde2d }, { 3604, 0x1982 }, { 3609, 0xf491 },
  /* 0x6140 */
  { 3617, 0x7dd6 }, { 3628, 0xa728 }, { 3635, 0xc9ad }, { 3644, 0x50fb },
  /* 0x6180 */
  { 3653, 0x6484 }, { 3658, 0x07df }, { 3668, 0x7bb0 }, { 3677, 0x5644 },
  /* 0x61C0 */
  { 3683, 0x3fc8 }, { 3692, 0xa021 }, { 3696, 0x0048 }, { 3698, 0xf5f4 },
  /* 0x6200 */
  { 3709, 0x7701 }, { 3716, 0xec77 }, { 3727, 0xc64e }, { 3735, 0xc91d },
  /* 0x6240 */
  { 3743, 0x7bcb }, { 3754, 0x4d6e }, { 3763, 0xe11b }, { 3771, 0xda4a },
  /* 0x6280 */
  { 3779, 0x063d }, { 3786, 0x5bfe }, { 3798, 0x1840 }, { 3801, 0x3a22 },
  /* 0x62C0 */
  { 3807, 0xb7f4 }, { 3818, 0x3bff }, { 3831, 0xf003 }, { 3837, 0xf0ea },
  /* 0x6300 */
  { 3846, 0x378e }, { 3855, 0x8303 }, { 3860, 0x8980 }, { 3864, 0xfe24 },
  /* 0x6340 */
  { 3873, 0xf21a }, { 3881, 0x12a1 }, { 3886, 0x5ba0 }, { 3893, 0x1cc4 },
  /* 0x6380 */
  { 3899, 0xd319 }, { 3907, 0x8b54 }, { 3914, 0x1faf }, { 3925, 0x6834 },
  /* 0x63C0 */
  { 3931, 0x8259 }, { 3937, 0x1c75 }, { 3945, 0x7a2b }, { 3954, 0x04f4 },
  /* 0x6400 */
  { 3960, 0xa240 }, { 3964, 0x50d9 }, { 3971, 0xb364 }, { 3979, 0x4450 },
  /* 0x6440 */
  { 3983, 0x4004 }, { 3985, 0x2d02 }, { 3990, 0xa281 }, { 3995, 0x2748 },
  /* 0x6480 */
  { 4001, 0x0188 }, { 4004, 0xe42e }, { 4012, 0x6a30 }, { 4018, 0xda05 },
  /* 0x64C0 */
  { 4025, 0x7cb6 }, { 4035, 0x05b5 }, { 4042, 0x90ff }, { 4052, 0xecd6 },
  /* 0x6500 */
  { 4062, 0x8031 }, { 4066, 0x7150 }, { 4072, 0x9e1c }, { 4080, 0xcbf4 },
  /* 0x6540 */
  { 4090, 0xa130 }, { 4095, 0x63f2 }, { 4104, 0x18cc }, { 4110, 0x05b5 },
  /* 0x6580 */
  { 4117, 0x57be }, { 4128, 0xba83 }, { 4136, 0xb8b2 }, { 4144, 0xb3a5 },
  /* 0x65C0 */
  { 4153, 0x9a7e }, { 4163, 0x0a94 }, { 4168, 0x33e7 }, { 4178, 0x1e06 },
  /* 0x6600 */
  { 4184, 0xd7dd }, { 4196, 0xd038 }, { 4202, 0xadb7 }, { 4213, 0x947b },
  /* 0x6640 */
  { 4222, 0xdb3e }, { 4233, 0xee86 }, { 4242, 0xfffe }, { 4257, 0x0dd9 },
  /* 0x6680 */
  { 4265, 0x639b }, { 4274, 0x23c7 }, { 4282, 0x6845 }, { 4288, 0xdb36 },
  /* 0x66C0 */
  { 4298, 0x03d2 }, { 4304, 0x3e40 }, { 4310, 0x1341 }, { 4315, 0xffbd },
  /* 0x6700 */
  { 4329, 0xab2b }, { 4338, 0xeafc }, { 4349, 0x7dc0 }, { 4357, 0xa5da },
  /* 0x6740 */
  { 4366, 0xf3c2 }, { 4375, 0xf25b }, { 4385, 0xa47f }, { 4395, 0xd8ff },
  /* 0x6780 */
  { 4407, 0x1aa2 }, { 4413, 0x3dad }, { 4423, 0x8247 }, { 4429, 0x0bdd },
  /* 0x67C0 */
  { 4438, 0xc55b }, { 4447, 0x6f9f }, { 4459, 0xd294 }, { 4466, 0xdabb },
  /* 0x6800 */
  { 4477, 0x001e }, { 4481, 0xe1c9 }, { 4489, 0x3e06 }, { 4496, 0x7b1e },
  /* 0x6840 */
  { 4506, 0x737f }, { 4518, 0xbabf }, { 4530, 0x4888 }, { 4534, 0xd4f4 },
  /* 0x6880 */
  { 4543, 0xa02e }, { 4549, 0xbfd9 }, { 4561, 0xaded }, { 4572, 0x1e7f },
  /* 0x68C0 */
  { 4583, 0xbf78 }, { 4594, 0x87f5 }, { 4604, 0xf1bb }, { 4615, 0x1e87 },
  /* 0x6900 */
  { 4623, 0xfdbb }, { 4636, 0x1e04 }, { 4641, 0x056e }, { 4648, 0xaa71 },
  /* 0x6940 */
  { 4656, 0x0644 }, { 4660, 0x76b8 }, { 4669, 0xff1f }, { 4682, 0xf7bc },
  /* 0x6980 */
  { 4694, 0x4407 }, { 4699, 0x1976 }, { 4707, 0x60e1 }, { 4713, 0xdc97 },
  /* 0x69C0 */
  { 4723, 0xfc8b }, { 4733, 0x634b }, { 4741, 0xef8c }, { 4751, 0xea7c },
  /* 0x6A00 */
  { 4761, 0x9c24 }, { 4767, 0xeebe }, { 4779, 0x4e0e }, { 4786, 0xef7d },
  /* 0x6A40 */
  { 4799, 0x4bf0 }, { 4807, 0x8b45 }, { 4814, 0x0856 }, { 4819, 0xc50c },
  /* 0x6A80 */
  { 4825, 0x6a19 }, { 4832, 0xf093 }, { 4840, 0x5c2f }, { 4849, 0x2908 },
  /* 0x6AC0 */
  { 4853, 0x004e }, { 4857, 0xfc1b }, { 4867, 0x1590 }, { 4872, 0x2c0e },
  /* 0x6B00 */
  { 4878, 0x8c30 }, { 4883, 0xe8c7 }, { 4892, 0x908b }, { 4898, 0x67a4 },
  /* 0x6B40 */
  { 4906, 0x56c8 }, { 4913, 0x8b59 }, { 4921, 0x96ff }, { 4933, 0x8fb8 },
  /* 0x6B80 */
  { 4942, 0x2e5f }, { 4952, 0x4960 }, { 4957, 0xee10 }, { 4964, 0xfcbe },
  /* 0x6BC0 */
  { 4976, 0xebe1 }, { 4986, 0x8ddc }, { 4995, 0xd8c0 }, { 5001, 0x800a },
  /* 0x6C00 */
  { 5004, 0xc524 }, { 5010, 0x089b }, { 5016, 0x0018 }, { 5018, 0xc5f8 },
  /* 0x6C40 */
  { 5027, 0x6007 }, { 5032, 0xfea1 }, { 5042, 0x2585 }, { 5048, 0x645d },
  /* 0x6C80 */
  { 5056, 0x337e }, { 5066, 0x1ffd }, { 5078, 0x6c06 }, { 5084, 0xff0a },
  /* 0x6CC0 */
  { 5094, 0x1676 }, { 5102, 0x3ef9 }, { 5113, 0xff2f }, { 5126, 0x080b },
  /* 0x6D00 */
  { 5130, 0x5c11 }, { 5136, 0xca84 }, { 5142, 0xcef0 }, { 5151, 0xfb7e },
  /* 0x6D40 */
  { 5164, 0x0032 }, { 5167, 0x5f00 }, { 5173, 0x5679 }, { 5182, 0x0391 },
  /* 0x6D80 */
  { 5187, 0x77a7 }, { 5198, 0x1b3a }, { 5206, 0xdc00 }, { 5211, 0x9134 },
  /* 0x6DC0 */
  { 5217, 0xd9f5 }, { 5228, 0xef67 }, { 5240, 0x5f52 }, { 5249, 0x1eea },
  /* 0x6E00 */
  { 5258, 0x0fa0 }, { 5264, 0xeea8 }, { 5273, 0xfaff }, { 5287, 0x5554 },
  /* 0x6E40 */
  { 5294, 0xff18 }, { 5304, 0xd9da }, { 5314, 0xc888 }, { 5319, 0xc044 },
  /* 0x6E80 */
  { 5323, 0x9005 }, { 5327, 0xb149 }, { 5334, 0x8ca4 }, { 5340, 0xa4d6 },
  /* 0x6EC0 */
  { 5348, 0x5ebe }, { 5359, 0x623a }, { 5366, 0x9800 }, { 5369, 0xcb94 },
  /* 0x6F00 */
  { 5377, 0x9646 }, { 5384, 0x053b }, { 5391, 0x9c2d }, { 5399, 0xd16e },
  /* 0x6F40 */
  { 5408, 0x0022 }, { 5410, 0xdf96 }, { 5421, 0xe157 }, { 5430, 0x7511 },
  /* 0x6F80 */
  { 5437, 0x7157 }, { 5446, 0x81d3 }, { 5453, 0x84bb }, { 5461, 0x526a },
  /* 0x6FC0 */
  { 5468, 0x07cf }, { 5477, 0xcd30 }, { 5484, 0xda13 }, { 5492, 0x566b },
  /* 0x7000 */
  { 5501, 0x8ee3 }, { 5510, 0xed22 }, { 5518, 0x11c8 }, { 5523, 0x5605 },
  /* 0x7040 */
  { 5529, 0x5c88 }, { 5535, 0x6112 }, { 5540, 0xda38 }, { 5548, 0x7161 },
  /* 0x7080 */
  { 5555, 0x4662 }, { 5561, 0x82a4 }, { 5566, 0xf810 }, { 5572, 0x0f8a },
  /* 0x70C0 */
  { 5579, 0x8d00 }, { 5583, 0xb31a }, { 5591, 0x1010 }, { 5593, 0x2202 },
  /* 0x7100 */
  { 5596, 0x93d8 }, { 5604, 0x5610 }, { 5609, 0xc843 }, { 5615, 0x1043 },
  /* 0x7140 */
  { 5619, 0x56c0 }, { 5625, 0x526f }, { 5634, 0x53f5 }, { 5644, 0x2000 },
  /* 0x7180 */
  { 5645, 0x85b1 }, { 5652, 0x8a74 }, { 5659, 0xd105 }, { 5665, 0x460a },
  /* 0x71C0 */
  { 5670, 0x4b1a }, { 5677, 0x92bd }, { 5686, 0x70e1 }, { 5693, 0xda20 },
  /* 0x7200 */
  { 5699, 0x20c1 }, { 5703, 0x0821 }, { 5706, 0x3d00 }, { 5711, 0xff75 },
  /* 0x7240 */
  { 5724, 0x19c5 }, { 5731, 0xabec }, { 5741, 0xc28e }, { 5748, 0xe314 },
  /* 0x7280 */
  { 5755, 0x6087 }, { 5761, 0x0844 }, { 5764, 0xf085 }, { 5771, 0x4247 },
  /* 0x72C0 */
  { 5777, 0x505f }, { 5785, 0x0a85 }, { 5790, 0x3207 }, { 5796, 0x3f88 },
  /* 0x7300 */
  { 5804, 0x0480 }, { 5806, 0xbbc4 }, { 5815, 0xdfa0 }, { 5824, 0xe2da },
  /* 0x7340 */
  { 5833, 0xc030 }, { 5837, 0x0085 }, { 5840, 0xdd48 }, { 5848, 0x1da7 },
  /* 0x7380 */
  { 5857, 0x0eb2 }, { 5864, 0xd170 }, { 5871, 0x0b65 }, { 5878, 0x9aac },
  /* 0x73C0 */
  { 5886, 0xef25 }, { 5896, 0x4240 }, { 5899, 0x66ab }, { 5908, 0x4702 },
  /* 0x7400 */
  { 5913, 0x06ea }, { 5920, 0x0c08 }, { 5923, 0xdd74 }, { 5933, 0x867f },
  /* 0x7440 */
  { 5943, 0x28db }, { 5951, 0xfeac }, { 5962, 0xae1d }, { 5971, 0x404b },
  /* 0x7480 */
  { 5976, 0x0bea }, { 5984, 0xd385 }, { 5992, 0x0fef }, { 6003, 0xae21 },
  /* 0x74C0 */
  { 6010, 0x8700 }, { 6014, 0x5550 }, { 6020, 0xcacd }, { 6029, 0x85c7 },
  /* 0x7500 */
  { 6037, 0x703a }, { 6044, 0xd5aa }, { 6053, 0x9d79 }, { 6063, 0x7d8f },
  /* 0x7540 */
  { 6074, 0xff51 }, { 6085, 0x3e17 }, { 6094, 0xbef5 }, { 6106, 0xe7df },
  /* 0x7580 */
  { 6119, 0xdec6 }, { 6129, 0x2416 }, { 6134, 0x082c }, { 6138, 0xf3af },
  /* 0x75C0 */
  { 6150, 0xe4ed }, { 6160, 0xeb3c }, { 6170, 0x529d }, { 6178, 0xd61f },
  /* 0x7600 */
  { 6188, 0xab8f }, { 6198, 0xdb68 }, { 6207, 0x21f7 }, { 6216, 0x1839 },
  /* 0x7640 */
  { 6222, 0x1bce }, { 6231, 0x1164 }, { 6236, 0xf7b6 }, { 6248, 0x7d47 },
  /* 0x7680 */
  { 6258, 0x49db }, { 6267, 0x7e69 }, { 6277, 0xc5c3 }, { 6285, 0x87d1 },
  /* 0x76C0 */
  { 6293, 0x776c }, { 6303, 0xd8d4 }, { 6311, 0x55fa }, { 6321, 0x5916 },
  /* 0x7700 */
  { 6328, 0x1f92 }, { 6336, 0xce80 }, { 6342, 0x2271 }, { 6348, 0x15f0 },
  /* 0x7740 */
  { 6355, 0x60c1 }, { 6360, 0x9d00 }, { 6365, 0x0d6f }, { 6374, 0xf604 },
  /* 0x7780 */
  { 6381, 0x4801 }, { 6384, 0xc412 }, { 6389, 0x3635 }, { 6397, 0xba49 },
  /* 0x77C0 */
  { 6405, 0x2080 }, { 6407, 0xdc80 }, { 6413, 0xf6fd }, { 6426, 0x1819 },
  /* 0x7800 */
  { 6431, 0x3264 }, { 6437, 0x0234 }, { 6441, 0x30e3 }, { 6448, 0x8414 },
  /* 0x7840 */
  { 6452, 0xc0a8 }, { 6457, 0x2002 }, { 6459, 0xdd10 }, { 6466, 0x1014 },
  /* 0x7880 */
  { 6469, 0x74c2 }, { 6476, 0xe4ba }, { 6485, 0xa698 }, { 6492, 0x5c21 },
  /* 0x78C0 */
  { 6498, 0x5d62 }, { 6506, 0x0433 }, { 6511, 0x91d3 }, { 6519, 0x6e94 },
  /* 0x7900 */
  { 6527, 0x4083 }, { 6531, 0x1a07 }, { 6537, 0x5c60 }, { 6543, 0x5c13 },
  /* 0x7940 */
  { 6550, 0x07e3 }, { 6558, 0xfde9 }, { 6570, 0x21a5 }, { 6576, 0x8684 },
  /* 0x7980 */
  { 6581, 0xe433 }, { 6589, 0x2970 }, { 6595, 0x46c2 }, { 6601, 0xef1b },
  /* 0x79C0 */
  { 6612, 0x3f87 }, { 6622, 0xc176 }, { 6630, 0x3ada }, { 6639, 0x0801 },
  /* 0x7A00 */
  { 6641, 0x6d09 }, { 6648, 0xdfb2 }, { 6659, 0x6001 }, { 6662, 0xfb86 },
  /* 0x7A40 */
  { 6672, 0xf2cd }, { 6682, 0xb2c1 }, { 6689, 0x2e8f }, { 6698, 0xa771 },
  /* 0x7A80 */
  { 6707, 0x053e }, { 6714, 0x81ed }, { 6722, 0xd609 }, { 6729, 0xde49 },
  /* 0x7AC0 */
  { 6738, 0xfdb8 }, { 6749, 0xb62e }, { 6758, 0xadef }, { 6770, 0xa751 },
  /* 0x7B00 */
  { 6778, 0x8dd4 }, { 6786, 0x4b06 }, { 6792, 0xf5e1 }, { 6802, 0x2a6a },
  /* 0x7B40 */
  { 6809, 0xfbe2 }, { 6820, 0x2077 }, { 6827, 0xf2f1 }, { 6837, 0x863f },
  /* 0x7B80 */
  { 6846, 0xa8c0 }, { 6851, 0xffb7 }, { 6865, 0xa402 }, { 6869, 0x1132 },
  /* 0x7BC0 */
  { 6874, 0x9ef3 }, { 6885, 0x26d0 }, { 6891, 0x2671 }, { 6898, 0x00c9 },
  /* 0x7C00 */
  { 6902, 0xe88b }, { 6910, 0xc09e }, { 6917, 0x0ccb }, { 6924, 0xe1ca },
  /* 0x7C40 */
  { 6932, 0xb429 }, { 6939, 0xc3d3 }, { 6948, 0xf233 }, { 6957, 0x4229 },
  /* 0x7C80 */
  { 6962, 0xaa0e }, { 6969, 0x89b5 }, { 6977, 0x69f7 }, { 6988, 0xf2ce },
  /* 0x7CC0 */
  { 6998, 0x6535 }, { 7006, 0xf3e4 }, { 7016, 0x88c5 }, { 7022, 0x4d74 },
  /* 0x7D00 */
  { 7030, 0x2ffd }, { 7042, 0x7fbd }, { 7055, 0xd80f }, { 7063, 0xe62f },
  /* 0x7D40 */
  { 7073, 0xd9ff }, { 7086, 0x5e49 }, { 7094, 0x454e }, { 7101, 0xa66f },
  /* 0x7D80 */
  { 7111, 0x9b48 }, { 7118, 0xbe88 }, { 7126, 0xfccd }, { 7137, 0xedf7 },
  /* 0x7DC0 */
  { 7150, 0x9c85 }, { 7157, 0x77e6 }, { 7168, 0x935b }, { 7177, 0x0a16 },
  /* 0x7E00 */
  { 7182, 0x0f32 }, { 7189, 0xe8a7 }, { 7198, 0x59cf }, { 7208, 0x6ea6 },
  /* 0x7E40 */
  { 7217, 0x2cea }, { 7225, 0x6674 }, { 7233, 0x2ec2 }, { 7240, 0xfa29 },
  /* 0x7E80 */
  { 7249, 0xf7cc }, { 7260, 0x1d5f }, { 7270, 0x0000 }, { 7270, 0x0000 },
  /* 0x7F00 */
  { 7270, 0x0000 }, { 7270, 0x0000 }, { 7270, 0x0000 }, { 7270, 0x5d40 },
  /* 0x7F40 */
  { 7276, 0xf0b8 }, { 7284, 0x8137 }, { 7291, 0x6f9b }, { 7302, 0x63a5 },
  /* 0x7F80 */
  { 7310, 0x55ec }, { 7319, 0x74d3 }, { 7328, 0xe318 }, { 7335, 0xa344 },
  /* 0x7FC0 */
  { 7341, 0xd46a }, { 7349, 0x8834 }, { 7354, 0xda6b }, { 7364, 0x1e0d },
  /* 0x8000 */
  { 7371, 0x5d7f }, { 7383, 0x13f7 }, { 7393, 0x1152 }, { 7398, 0xb8e9 },
  /* 0x8040 */
  { 7407, 0x0448 }, { 7410, 0xc544 }, { 7416, 0x8146 }, { 7421, 0xeaff },
  /* 0x8080 */
  { 7434, 0x1af0 }, { 7441, 0x3f48 }, { 7449, 0xb6b6 }, { 7459, 0x0516 },
  /* 0x80C0 */
  { 7464, 0x5478 }, { 7471, 0x6fe0 }, { 7480, 0x8073 }, { 7486, 0x393a },
  /* 0x8100 */
  { 7494, 0x27e4 }, { 7502, 0x4d40 }, { 7507, 0x9298 }, { 7513, 0x622a },
  /* 0x8140 */
  { 7519, 0x4c40 }, { 7523, 0x803b }, { 7529, 0x6be1 }, { 7538, 0x8713 },
  /* 0x8180 */
  { 7545, 0x853f }, { 7554, 0x3528 }, { 7560, 0x0319 }, { 7565, 0xed2d },
  /* 0x81C0 */
  { 7575, 0xa74f }, { 7585, 0x8fca }, { 7594, 0x35b9 }, { 7603, 0xfc18 },
  /* 0x8200 */
  { 7611, 0x77b6 }, { 7622, 0xdbc5 }, { 7632, 0x5e06 }, { 7639, 0x13fc },
  /* 0x8240 */
  { 7648, 0x8ae1 }, { 7655, 0xb780 }, { 7662, 0xcd5d }, { 7672, 0xe3d6 },
  /* 0x8280 */
  { 7682, 0x6c08 }, { 7687, 0xa20c }, { 7692, 0xfbfa }, { 7705, 0xff9e },
  /* 0x82C0 */
  { 7718, 0x0060 }, { 7720, 0xdabe }, { 7731, 0x09ee }, { 7739, 0x6e9a },
  /* 0x8300 */
  { 7748, 0x53ff }, { 7760, 0x39c0 }, { 7766, 0xa90c }, { 7772, 0x1777 },
  /* 0x8340 */
  { 7782, 0x86b9 }, { 7790, 0x01b7 }, { 7797, 0x000c }, { 7799, 0xb8a8 },
  /* 0x8380 */
  { 7806, 0x66e0 }, { 7813, 0xed7c }, { 7824, 0x0f85 }, { 7831, 0xa022 },
  /* 0x83C0 */
  { 7835, 0xd6a3 }, { 7844, 0xb15b }, { 7853, 0x8e23 }, { 7860, 0x2a97 },
  /* 0x8400 */
  { 7868, 0xfcda }, { 7879, 0x00aa }, { 7883, 0x1605 }, { 7888, 0x3322 },
  /* 0x8440 */
  { 7894, 0xc740 }, { 7900, 0x9e86 }, { 7908, 0xfa6e }, { 7919, 0x17eb },
  /* 0x8480 */
  { 7929, 0x0836 }, { 7934, 0xd291 }, { 7941, 0xa042 }, { 7945, 0xdf14 },
  /* 0x84C0 */
  { 7954, 0xee57 }, { 7965, 0x164b }, { 7972, 0xd480 }, { 7977, 0xb413 },
  /* 0x8500 */
  { 7984, 0x1041 }, { 7987, 0xcdba }, { 7997, 0xb87a }, { 8006, 0x6034 },
  /* 0x8540 */
  { 8011, 0xcf0b }, { 8020, 0x47aa }, { 8028, 0xa71e }, { 8037, 0xec80 },
  /* 0x8580 */
  { 8043, 0x95d3 }, { 8052, 0xba9b }, { 8062, 0xff54 }, { 8073, 0x1681 },
  /* 0x85C0 */
  { 8078, 0xee82 }, { 8086, 0xb321 }, { 8093, 0x2672 }, { 8100, 0xcec0 },
  /* 0x8600 */
  { 8107, 0x0cf5 }, { 8115, 0x45cf }, { 8124, 0xa296 }, { 8131, 0x9301 },
  /* 0x8640 */
  { 8136, 0x6003 }, { 8140, 0xdcf9 }, { 8151, 0x9884 }, { 8156, 0x0ea2 },
  /* 0x8680 */
  { 8162, 0x3e80 }, { 8168, 0x312a }, { 8174, 0x8f18 }, { 8181, 0x014b },
  /* 0x86C0 */
  { 8186, 0x6ada }, { 8195, 0xcab2 }, { 8203, 0xf258 }, { 8211, 0x7f00 },
  /* 0x8700 */
  { 8218, 0x6fed }, { 8230, 0x970f }, { 8239, 0x022a }, { 8243, 0xcc92 },
  /* 0x8740 */
  { 8250, 0x5a09 }, { 8256, 0x83aa }, { 8263, 0x4579 }, { 8271, 0x9156 },
  /* 0x8780 */
  { 8278, 0x2b84 }, { 8284, 0x8008 }, { 8286, 0xb885 }, { 8293, 0x6c28 },
  /* 0x87C0 */
  { 8299, 0x48d3 }, { 8306, 0x8045 }, { 8310, 0xbc69 }, { 8319, 0x4ae4 },
  /* 0x8800 */
  { 8326, 0xec6a }, { 8335, 0x807b }, { 8342, 0x418e }, { 8348, 0x1a46 },
  /* 0x8840 */
  { 8354, 0x3455 }, { 8361, 0xeb8c }, { 8370, 0x8b1e }, { 8378, 0xe0a5 },
  /* 0x8880 */
  { 8385, 0x2906 }, { 8390, 0x43c4 }, { 8396, 0x4c15 }, { 8402, 0xf0b3 },
  /* 0x88C0 */
  { 8411, 0xc43f }, { 8420, 0xbb3e }, { 8431, 0x0102 }, { 8433, 0x733f },
  /* 0x8900 */
  { 8444, 0x1496 }, { 8450, 0x770d }, { 8459, 0x0ca0 }, { 8463, 0x0bc5 },
  /* 0x8940 */
  { 8470, 0x323f }, { 8479, 0xc040 }, { 8482, 0xa455 }, { 8489, 0xc094 },
  /* 0x8980 */
  { 8494, 0x8fcb }, { 8504, 0x85d9 }, { 8512, 0x96c2 }, { 8519, 0xa48d },
  /* 0x89C0 */
  { 8526, 0x0001 }, { 8527, 0x3554 }, { 8534, 0x08e8 }, { 8539, 0xa15a },
  /* 0x8A00 */
  { 8546, 0x550d }, { 8553, 0xa9ff }, { 8565, 0x242e }, { 8571, 0x5cfa },
  /* 0x8A40 */
  { 8581, 0x61e2 }, { 8588, 0x6937 }, { 8597, 0x7a4f }, { 8607, 0x122f },
  /* 0x8A80 */
  { 8614, 0x32b4 }, { 8621, 0x452b }, { 8628, 0x71fb }, { 8639, 0xd285 },
  /* 0x8AC0 */
  { 8646, 0xb894 }, { 8653, 0xdcc5 }, { 8662, 0x68d7 }, { 8671, 0x55da },
  /* 0x8B00 */
  { 8680, 0x74b7 }, { 8690, 0xbed1 }, { 8700, 0x3943 }, { 8707, 0x4208 },
  /* 0x8B40 */
  { 8710, 0xd24a }, { 8717, 0xdf52 }, { 8727, 0x9a40 }, { 8732, 0xa0d7 },
  /* 0x8B80 */
  { 8740, 0x5c0b }, { 8747, 0x767d }, { 8758, 0x0000 }, { 8758, 0x0000 },
  /* 0x8C00 */
  { 8758, 0x0000 }, { 8758, 0x0000 }, { 8758, 0x0000 }, { 8758, 0xa680 },
  /* 0x8C40 */
  { 8763, 0xd7e2 }, { 8773, 0x04b1 }, { 8778, 0x3f06 }, { 8786, 0x1708 },
  /* 0x8C80 */
  { 8791, 0x7624 }, { 8798, 0x6b1c }, { 8806, 0xff97 }, { 8819, 0xb9dd },
  /* 0x8CC0 */
  { 8830, 0x659f }, { 8840, 0x5e6a }, { 8849, 0x245f }, { 8857, 0x7d13 },
  /* 0x8D00 */
  { 8866, 0xefb0 }, { 8876, 0x085d }, { 8882, 0x0000 }, { 8882, 0x0000 },
  /* 0x8D40 */
  { 8882, 0x0000 }, { 8882, 0x0000 }, { 8882, 0x38d0 }, { 8888, 0x009b },
  /* 0x8D80 */
  { 8893, 0x0432 }, { 8897, 0x0220 }, { 8899, 0x8148 }, { 8903, 0x4408 },
  /* 0x8DC0 */
  { 8906, 0xd944 }, { 8913, 0xaec2 }, { 8921, 0x9d0a }, { 8928, 0xb028 },
  /* 0x8E00 */
  { 8933, 0x9740 }, { 8939, 0xe051 }, { 8945, 0x048f }, { 8951, 0x2271 },
  /* 0x8E40 */
  { 8957, 0x1f94 }, { 8965, 0x8231 }, { 8970, 0xb01d }, { 8977, 0x1855 },
  /* 0x8E80 */
  { 8983, 0x2cb2 }, { 8990, 0x431a }, { 8996, 0xdc02 }, { 9002, 0x486b },
  /* 0x8EC0 */
  { 9009, 0x3d61 }, { 9017, 0x8816 }, { 9022, 0x080c }, { 9025, 0x7f00 },
  /* 0x8F00 */
  { 9032, 0x1729 }, { 9039, 0xfabc }, { 9050, 0xae40 }, { 9056, 0xcb48 },
  /* 0x8F40 */
  { 9063, 0x7675 }, { 9073, 0x9190 }, { 9078, 0x001e }, { 9082, 0x0000 },
  /* 0x8F80 */
  { 9082, 0x0000 }, { 9082, 0xd800 }, { 9086, 0xe1d8 }, { 9094, 0x9cf7 },
  /* 0x8FC0 */
  { 9105, 0x6476 }, { 9113, 0x043a }, { 9118, 0xef75 }, { 9130, 0x2fb3 },
  /* 0x9000 */
  { 9140, 0xf96f }, { 9152, 0xe6fb }, { 9164, 0x608f }, { 9171, 0x53e6 },
  /* 0x9040 */
  { 9180, 0xeebe }, { 9192, 0x737f }, { 9204, 0xe32b }, { 9213, 0xb5e4 },
  /* 0x9080 */
  { 9222, 0x97bf }, { 9234, 0x0aa3 }, { 9240, 0x854e }, { 9247, 0x416b },
  /* 0x90C0 */
  { 9254, 0x45ba }, { 9262, 0xf880 }, { 9268, 0xa916 }, { 9275, 0xe0f4 },
  /* 0x9100 */
  { 9283, 0x0055 }, { 9287, 0x5374 }, { 9295, 0xa08c }, { 9300, 0x2697 },
  /* 0x9140 */
  { 9308, 0x7fc0 }, { 9317, 0x0bd4 }, { 9324, 0x163e }, { 9332, 0x03bc },
  /* 0x9180 */
  { 9339, 0x6aac }, { 9347, 0x5085 }, { 9352, 0xdd14 }, { 9360, 0x157a },
  /* 0x91C0 */
  { 9368, 0xfbdb }, { 9381, 0xbdc3 }, { 9391, 0x70fa }, { 9400, 0x9862 },
  /* 0x9200 */
  { 9406, 0x6482 }, { 9411, 0x40f3 }, { 9418, 0x1200 }, { 9420, 0x9798 },
  /* 0x9240 */
  { 9428, 0xcfbd }, { 9440, 0x4ec3 }, { 9448, 0x01f7 }, { 9456, 0xf102 },
  /* 0x9280 */
  { 9462, 0x2329 }, { 9468, 0x9fea }, { 9479, 0x2880 }, { 9482, 0x8284 },
  /* 0x92C0 */
  { 9486, 0xd845 }, { 9493, 0x028d }, { 9498, 0x26b1 }, { 9505, 0x9f8c },
  /* 0x9300 */
  { 9514, 0xa054 }, { 9519, 0xe723 }, { 9528, 0xdbef }, { 9541, 0x0c24 },
  /* 0x9340 */
  { 9545, 0x2f90 }, { 9552, 0x1cd2 }, { 9559, 0x5c31 }, { 9566, 0x502b },
  /* 0x9380 */
  { 9572, 0x9900 }, { 9576, 0x4cd0 }, { 9582, 0x708a }, { 9588, 0x0601 },
  /* 0x93C0 */
  { 9591, 0x01ca }, { 9596, 0xf1c3 }, { 9605, 0x01b6 }, { 9611, 0x2822 },
  /* 0x9400 */
  { 9615, 0x8298 }, { 9620, 0x07d9 }, { 9628, 0x0802 }, { 9630, 0x0d7c },
  /* 0x9440 */
  { 9638, 0x0432 }, { 9642, 0x4c0e }, { 9648, 0xac0d }, { 9655, 0xf0a7 },
  /* 0x9480 */
  { 9664, 0x0002 }, { 9665, 0x0000 }, { 9665, 0x0000 }, { 9665, 0x0000 },
  /* 0x9540 */
  { 9665, 0x0000 }, { 9665, 0x0000 }, { 9665, 0x0000 }, { 9665, 0x0380 },
  /* 0x9580 */
  { 9668, 0xfecd }, { 9680, 0x835a }, { 9687, 0x3bfd }, { 9699, 0x7a54 },
  /* 0x95C0 */
  { 9707, 0x3d88 }, { 9714, 0x5579 }, { 9723, 0x0026 }, { 9726, 0x0000 },
  /* 0x9600 */
  { 9726, 0x0000 }, { 9726, 0x3000 }, { 9728, 0xd502 }, { 9734, 0x981c },
  /* 0x9640 */
  { 9740, 0xb817 }, { 9748, 0xf901 }, { 9755, 0x147e }, { 9763, 0x25ed },
  /* 0x9680 */
  { 9772, 0xed74 }, { 9782, 0x3fb0 }, { 9791, 0x87b9 }, { 9800, 0x3fdf },
  /* 0x96C0 */
  { 9813, 0x7af3 }, { 9824, 0x7f66 }, { 9835, 0x8f0c }, { 9842, 0x0ac5 },
  /* 0x9700 */
  { 9848, 0xe5d1 }, { 9857, 0x525a }, { 9864, 0x0498 }, { 9868, 0x6b4d },
  /* 0x9740 */
  { 9877, 0xe3d6 }, { 9887, 0x5ee4 }, { 9896, 0x6f57 }, { 9907, 0x161a },
  /* 0x9780 */
  { 9913, 0xa872 }, { 9920, 0x5561 }, { 9927, 0x694d }, { 9935, 0x441e },
  /* 0x97C0 */
  { 9941, 0x1b4a }, { 9948, 0x5b1a }, { 9956, 0x6002 }, { 9959, 0x887e },
  /* 0x9800 */
  { 9967, 0xf57e }, { 9979, 0x45df }, { 9989, 0x383a }, { 9996, 0x399d },
  /* 0x9840 */
  { 10005, 0xf8c0 }, { 10012, 0x4ffc }, { 10023, 0x98e0 }, { 10029, 0x001b },
  /* 0x9880 */
  { 10033, 0x0000 }, { 10033, 0x0000 }, { 10033, 0xad00 }, { 10038, 0x9dc3 },
  /* 0x98C0 */
  { 10047, 0x09dc }, { 10054, 0x9800 }, { 10057, 0xeeaf }, { 10069, 0x701f },
  /* 0x9900 */
  { 10077, 0x1728 }, { 10083, 0x7ddd }, { 10095, 0x5113 }, { 10101, 0x7c0e },
  /* 0x9940 */
  { 10109, 0x7a67 }, { 10119, 0x91a7 }, { 10127, 0x0001 }, { 10128, 0x0000 },
  /* 0x9980 */
  { 10128, 0x0000 }, { 10128, 0x43c0 }, { 10133, 0x7168 }, { 10140, 0xb218 },
  /* 0x99C0 */
  { 10146, 0x037a }, { 10153, 0xeb37 }, { 10164, 0x6004 }, { 10167, 0x9b07 },
  /* 0x9A00 */
  { 10175, 0xc42e }, { 10182, 0x064e }, { 10188, 0x6911 }, { 10194, 0x41c3 },
  /* 0x9A40 */
  { 10200, 0x743d }, { 10209, 0x8da4 }, { 10216, 0x0e34 }, { 10222, 0x0000 },
  /* 0x9A80 */
  { 10222, 0x0000 }, { 10222, 0x0000 }, { 10222, 0xa100 }, { 10225, 0x13c1 },
  /* 0x9AC0 */
  { 10231, 0xc05b }, { 10238, 0xd17f }, { 10249, 0xee6d }, { 10260, 0x0a92 },
  /* 0x9B00 */
  { 10265, 0x114c }, { 10270, 0x9545 }, { 10277, 0xefad }, { 10289, 0x380e },
  /* 0x9B40 */
  { 10295, 0xe83e }, { 10304, 0x4512 }, { 10309, 0x9868 }, { 10315, 0x02fc },
  /* 0x9B80 */
  { 10322, 0xc418 }, { 10327, 0xc0ce }, { 10334, 0x7dc1 }, { 10343, 0x4316 },
  /* 0x9BC0 */
  { 10349, 0xc6c3 }, { 10357, 0x2956 }, { 10364, 0xcdbe }, { 10375, 0x25af },
  /* 0x9C00 */
  { 10384, 0x3751 }, { 10392, 0x2f7d }, { 10403, 0xe6be }, { 10414, 0x4ec7 },
  /* 0x9C40 */
  { 10423, 0x87e2 }, { 10431, 0xbd9d }, { 10442, 0x6ea9 }, { 10451, 0x05e5 },
  /* 0x9CC0 */
  { 10458, 0x0000 }, { 10458, 0x0000 }, { 10458, 0x1ae0 }, { 10464, 0x005d },
  /* 0x9D00 */
  { 10469, 0x4bcc }, { 10477, 0xe9a6 }, { 10486, 0x1d48 }, { 10492, 0xc804 },
  /* 0x9D40 */
  { 10496, 0x05de }, { 10504, 0xf207 }, { 10512, 0x9a1f }, { 10521, 0x54cd },
  /* 0x9D80 */
  { 10529, 0xa690 }, { 10535, 0x0640 }, { 10538, 0x9a12 }, { 10544, 0xbf34 },
  /* 0x9DC0 */
  { 10554, 0x82df }, { 10563, 0x86c8 }, { 10569, 0xa0c9 }, { 10575, 0x2714 },
  /* 0x9E00 */
  { 10581, 0x2484 }, { 10585, 0x7e20 }, { 10592, 0x0000 }, { 10592, 0x0000 },
  /* 0x9E40 */
  { 10592, 0x0000 }, { 10592, 0x0000 }, { 10592, 0x0000 }, { 10592, 0xbb20 },
  /* 0x9E80 */
  { 10599, 0x1923 }, { 10605, 0xe8ae }, { 10614, 0xb770 }, { 10623, 0xff30 },
  /* 0x9EC0 */
  { 10633, 0xf018 }, { 10639, 0xfb17 }, { 10650, 0xc1a1 }, { 10656, 0xbad0 },
  /* 0x9F00 */
  { 10664, 0x418c }, { 10669, 0x02a9 }, { 10674, 0x9003 }, { 10678, 0x6e80 },
  /* 0x9F40 */
  { 10684, 0xcc62 }, { 10691, 0xa1bc }, { 10699, 0x36cf }, { 10709, 0x00e5 },
  /* 0x9F80 */
  { 10714, 0x2000 }, { 10715, 0x30b1 }, { 10721, 0x0005 }, { 10723, 0x0000 },
  /* 0xF900 */
  { 10723, 0x0000 }, { 10723, 0x2000 }, { 10724, 0x0300 }, { 10726, 0x0040 },
  /* 0xF940 */
  { 10727, 0x0000 }, { 10727, 0x0000 }, { 10727, 0x0000 }, { 10727, 0x0001 },
  /* 0xF9C0 */
  { 10728, 0x0000 }, { 10728, 0x1001 }, { 10730, 0x0000 }, { 10730, 0x0000 },
  /* 0xFA00 */
  { 10730, 0x8000 }, { 10731, 0x8e7b }, { 10741, 0x0057 }, { 10746, 0xffff },
  /* 0xFA40 */
  { 10762, 0xffff }, { 10778, 0xffff }, { 10794, 0x07ff }, { 10805, 0x0000 },
  /* 0xFE40 */
  { 10805, 0x0060 }, { 10807, 0x0000 }, { 10807, 0x0000 }, { 10807, 0x0000 },
  /* 0xFF00 */
  { 10807, 0xfffe }, { 10822, 0xffff }, { 10838, 0xffff }, { 10854, 0xffff },
  /* 0xFF40 */
  { 10870, 0xffff }, { 10886, 0xffff }, { 10902, 0x0001 }, { 10903, 0x0000 },
  /* 0xFFC0 */
  { 10903, 0x0000 }, { 10903, 0x0000 }, { 10903, 0x0028 }, { 10905, 0x0000 },
  /* 0x20000 */
  { 10905, 0x0800 }, { 10906, 0x0000 }, { 10906, 0x0000 }, { 10906, 0x0000 },
  /* 0x20080 */
  { 10906, 0x0200 }, { 10907, 0x0000 }, { 10907, 0x0014 }, { 10909, 0x0000 },
  /* 0x20180 */
  { 10909, 0x0000 }, { 10909, 0x0000 }, { 10909, 0x0004 }, { 10910, 0x0000 },
  /* 0x20200 */
  { 10910, 0x0000 }, { 10910, 0x0008 }, { 10911, 0x0000 }, { 10911, 0x0000 },
  /* 0x20300 */
  { 10911, 0x0000 }, { 10911, 0x0000 }, { 10911, 0x0800 }, { 10912, 0x0000 },
  /* 0x20340 */
  { 10912, 0x0000 }, { 10912, 0x0000 }, { 10912, 0x0000 }, { 10912, 0x0002 },
  /* 0x20380 */
  { 10913, 0x0002 }, { 10914, 0x0000 }, { 10914, 0x0000 }, { 10914, 0x0000 },
  /* 0x203C0 */
  { 10914, 0x0000 }, { 10914, 0x0000 }, { 10914, 0x0000 }, { 10914, 0x0200 },
  /* 0x20440 */
  { 10915, 0x0400 }, { 10916, 0x0000 }, { 10916, 0x0000 }, { 10916, 0x0000 },
  /* 0x20500 */
  { 10916, 0x0200 }, { 10917, 0x0000 }, { 10917, 0x0000 }, { 10917, 0x0000 },
  /* 0x205C0 */
  { 10917, 0x0000 }, { 10917, 0x0040 }, { 10918, 0x0000 }, { 10918, 0x0000 },
  /* 0x20600 */
  { 10918, 0x0000 }, { 10918, 0x0000 }, { 10918, 0x0100 }, { 10919, 0x0000 },
  /* 0x20740 */
  { 10919, 0x8000 }, { 10920, 0x0000 }, { 10920, 0x0000 }, { 10920, 0x0000 },
  /* 0x20800 */
  { 10920, 0x0080 }, { 10921, 0x0000 }, { 10921, 0x0000 }, { 10921, 0x0400 },
  /* 0x20880 */
  { 10922, 0x0000 }, { 10922, 0x0000 }, { 10922, 0x0000 }, { 10922, 0x0200 },
  /* 0x20940 */
  { 10923, 0x0000 }, { 10923, 0x0000 }, { 10923, 0x0000 }, { 10923, 0x1000 },
  /* 0x20980 */
  { 10924, 0x0000 }, { 10924, 0x2000 }, { 10925, 0x0000 }, { 10925, 0x0000 },
  /* 0x20AC0 */
  { 10925, 0x0000 }, { 10925, 0x0008 }, { 10926, 0x0000 }, { 10926, 0x0000 },
  /* 0x20B00 */
  { 10926, 0x0000 }, { 10926, 0x2000 }, { 10927, 0x0000 }, { 10927, 0x0000 },
  /* 0x20B80 */
  { 10927, 0x0000 }, { 10927, 0x8000 }, { 10928, 0x0000 }, { 10928, 0x0000 },
  /* 0x20D40 */
  { 10928, 0x0020 }, { 10929, 0x0000 }, { 10929, 0x0000 }, { 10929, 0x0000 },
  /* 0x20DC0 */
  { 10929, 0x0000 }, { 10929, 0x0000 }, { 10929, 0x0002 }, { 10930, 0x0000 },
  /* 0x20E40 */
  { 10930, 0x0000 }, { 10930, 0x0000 }, { 10930, 0x2010 }, { 10932, 0x0000 },
  /* 0x20E80 */
  { 10932, 0x0000 }, { 10932, 0x0020 }, { 10933, 0x0000 }, { 10933, 0x0000 },
  /* 0x20F40 */
  { 10933, 0x0000 }, { 10933, 0x8000 }, { 10934, 0x0000 }, { 10934, 0x0000 },
  /* 0x21200 */
  { 10934, 0x0002 }, { 10935, 0x0000 }, { 10935, 0x0000 }, { 10935, 0x2000 },
  /* 0x21240 */
  { 10936, 0x0000 }, { 10936, 0x0020 }, { 10937, 0x0000 }, { 10937, 0x0810 },
  /* 0x212C0 */
  { 10939, 0x0000 }, { 10939, 0x0080 }, { 10940, 0x0010 }, { 10941, 0x2000 },
  /* 0x21300 */
  { 10942, 0x0000 }, { 10942, 0x0800 }, { 10943, 0x0000 }, { 10943, 0x0040 },
  /* 0x21340 */
  { 10944, 0x0010 }, { 10945, 0x0000 }, { 10945, 0x0000 }, { 10945, 0x0000 },
  /* 0x213C0 */
  { 10945, 0x0010 }, { 10946, 0x0000 }, { 10946, 0x0000 }, { 10946, 0x0000 },
  /* 0x21440 */
  { 10946, 0x0000 }, { 10946, 0x0000 }, { 10946, 0x6000 }, { 10948, 0x0000 },
  /* 0x215C0 */
  { 10948, 0x0000 }, { 10948, 0x0080 }, { 10949, 0x0000 }, { 10949, 0x0000 },
  /* 0x21640 */
  { 10949, 0x0080 }, { 10950, 0x0000 }, { 10950, 0x0000 }, { 10950, 0x0000 },
  /* 0x21680 */
  { 10950, 0x0000 }, { 10950, 0x0000 }, { 10950, 0x0000 }, { 10950, 0x0010 },
  /* 0x21700 */
  { 10951, 0x0040 }, { 10952, 0x0000 }, { 10952, 0x0000 }, { 10952, 0x0000 },
  /* 0x21740 */
  { 10952, 0x0004 }, { 10953, 0x0000 }, { 10953, 0x0000 }, { 10953, 0x0000 },
  /* 0x21880 */
  { 10953, 0x0000 }, { 10953, 0x0000 }, { 10953, 0x0000 }, { 10953, 0x2000 },
  /* 0x219C0 */
  { 10954, 0x0008 }, { 10955, 0x0000 }, { 10955, 0x0000 }, { 10955, 0x0000 },
  /* 0x21C40 */
  { 10955, 0x0000 }, { 10955, 0x0040 }, { 10956, 0x0000 }, { 10956, 0x0000 },
  /* 0x21D00 */
  { 10956, 0x0000 }, { 10956, 0x0000 }, { 10956, 0x2000 }, { 10957, 0x0000 },
  /* 0x21D40 */
  { 10957, 0x0020 }, { 10958, 0x0000 }, { 10958, 0x0004 }, { 10959, 0x0100 },
  /* 0x21D80 */
  { 10960, 0x0000 }, { 10960, 0x1004 }, { 10962, 0x0002 }, { 10963, 0x0080 },
  /* 0x21DC0 */
  { 10964, 0x0000 }, { 10964, 0x0000 }, { 10964, 0x0001 }, { 10965, 0x0000 },
  /* 0x21E00 */
  { 10965, 0x0000 }, { 10965, 0x0000 }, { 10965, 0x0000 }, { 10965, 0x0018 },
  /* 0x21F00 */
  { 10967, 0x0000 }, { 10967, 0x4000 }, { 10968, 0x0000 }, { 10968, 0x0000 },
  /* 0x21F40 */
  { 10968, 0x0000 }, { 10968, 0x0000 }, { 10968, 0x0000 }, { 10968, 0x0040 },
  /* 0x21FC0 */
  { 10969, 0x0000 }, { 10969, 0x0000 }, { 10969, 0x0000 }, { 10969, 0x0400 },
  /* 0x22140 */
  { 10970, 0x0000 }, { 10970, 0x0000 }, { 10970, 0x0000 }, { 10970, 0x0800 },
  /* 0x22200 */
  { 10971, 0x0000 }, { 10971, 0x0100 }, { 10972, 0x0000 }, { 10972, 0x0000 },
  /* 0x22300 */
  { 10972, 0x0000 }, { 10972, 0x4000 }, { 10973, 0x0000 }, { 10973, 0x0000 },
  /* 0x22380 */
  { 10973, 0x0000 }, { 10973, 0x0000 }, { 10973, 0x2000 }, { 10974, 0x0000 },
  /* 0x226C0 */
  { 10974, 0x0000 }, { 10974, 0x0000 }, { 10974, 0x0000 }, { 10974, 0x0008 },
  /* 0x22840 */
  { 10975, 0x0000 }, { 10975, 0x0800 }, { 10976, 0x0000 }, { 10976, 0x0000 },
  /* 0x22880 */
  { 10976, 0x0000 }, { 10976, 0x0000 }, { 10976, 0x0800 }, { 10977, 0x0000 },
  /* 0x22980 */
  { 10977, 0x8000 }, { 10978, 0x0000 }, { 10978, 0x0000 }, { 10978, 0x0000 },
  /* 0x22A80 */
  { 10978, 0x0000 }, { 10978, 0x0000 }, { 10978, 0x0000 }, { 10978, 0x0100 },
  /* 0x22B40 */
  { 10979, 0x8040 }, { 10981, 0x0001 }, { 10982, 0x0000 }, { 10982, 0x0000 },
  /* 0x22B80 */
  { 10982, 0x0000 }, { 10982, 0x0000 }, { 10982, 0x0040 }, { 10983, 0x0000 },
  /* 0x22C00 */
  { 10983, 0x0000 }, { 10983, 0x2000 }, { 10984, 0x0010 }, { 10985, 0x0000 },
  /* 0x22DC0 */
  { 10985, 0x0000 }, { 10985, 0x0000 }, { 10985, 0x0002 }, { 10986, 0x0000 },
  /* 0x23180 */
  { 10986, 0x0000 }, { 10986, 0x0000 }, { 10986, 0x0000 }, { 10986, 0x0040 },
  /* 0x231C0 */
  { 10987, 0x0018 }, { 10989, 0x0000 }, { 10989, 0x0000 }, { 10989, 0x0020 },
  /* 0x23340 */
  { 10990, 0x0000 }, { 10990, 0x0000 }, { 10990, 0x0000 }, { 10990, 0x0004 },
  /* 0x233C0 */
  { 10991, 0x0000 }, { 10991, 0x842d }, { 10997, 0x0010 }, { 10998, 0x0000 },
  /* 0x23440 */
  { 10998, 0x0c00 }, { 11000, 0x0002 }, { 11001, 0x0020 }, { 11002, 0x0000 },
  /* 0x234C0 */
  { 11002, 0x0000 }, { 11002, 0x0000 }, { 11002, 0x0010 }, { 11003, 0x0000 },
  /* 0x23540 */
  { 11003, 0x0000 }, { 11003, 0x0400 }, { 11004, 0x0000 }, { 11004, 0x0000 },
  /* 0x23580 */
  { 11004, 0x0000 }, { 11004, 0x0010 }, { 11005, 0x0000 }, { 11005, 0x0000 },
  /* 0x235C0 */
  { 11005, 0x0010 }, { 11006, 0x0000 }, { 11006, 0x0000 }, { 11006, 0x0000 },
  /* 0x23600 */
  { 11006, 0x0000 }, { 11006, 0x0000 }, { 11006, 0x0000 }, { 11006, 0x0700 },
  /* 0x23640 */
  { 11009, 0x0080 }, { 11010, 0x0000 }, { 11010, 0x0000 }, { 11010, 0x0000 },
  /* 0x23700 */
  { 11010, 0x1000 }, { 11011, 0x1000 }, { 11012, 0x0000 }, { 11012, 0x8000 },
  /* 0x23740 */
  { 11013, 0x0000 }, { 11013, 0x0000 }, { 11013, 0x0018 }, { 11015, 0x0000 },
  /* 0x237C0 */
  { 11015, 0x0000 }, { 11015, 0x0000 }, { 11015, 0x0080 }, { 11016, 0x8000 },
  /* 0x23800 */
  { 11017, 0x0000 }, { 11017, 0x0000 }, { 11017, 0x0010 }, { 11018, 0x2000 },
  /* 0x23A80 */
  { 11019, 0x0000 }, { 11019, 0x0100 }, { 11020, 0x0000 }, { 11020, 0x0000 },
  /* 0x23C40 */
  { 11020, 0x0000 }, { 11020, 0x0000 }, { 11020, 0x0000 }, { 11020, 0x8000 },
  /* 0x23CC0 */
  { 11021, 0x0000 }, { 11021, 0x0000 }, { 11021, 0x0000 }, { 11021, 0x4000 },
  /* 0x23D00 */
  { 11022, 0x4001 }, { 11024, 0x0000 }, { 11024, 0x0000 }, { 11024, 0x0000 },
  /* 0x23D40 */
  { 11024, 0x0001 }, { 11025, 0x0000 }, { 11025, 0x0000 }, { 11025, 0x0000 },
  /* 0x23DC0 */
  { 11025, 0x0000 }, { 11025, 0x0008 }, { 11026, 0x0000 }, { 11026, 0x0600 },
  /* 0x23F40 */
  { 11028, 0x0000 }, { 11028, 0x0000 }, { 11028, 0x0000 }, { 11028, 0x4000 },
  /* 0x24080 */
  { 11029, 0x0000 }, { 11029, 0x0040 }, { 11030, 0x0000 }, { 11030, 0x0000 },
  /* 0x24100 */
  { 11030, 0x0008 }, { 11031, 0x0000 }, { 11031, 0x0000 }, { 11031, 0x0000 },
  /* 0x241C0 */
  { 11031, 0x0040 }, { 11032, 0x0000 }, { 11032, 0x0000 }, { 11032, 0x4000 },
  /* 0x24380 */
  { 11033, 0x0000 }, { 11033, 0x0000 }, { 11033, 0x0000 }, { 11033, 0x1000 },
  /* 0x24600 */
  { 11034, 0x0000 }, { 11034, 0x0000 }, { 11034, 0x0200 }, { 11035, 0x0000 },
  /* 0x24680 */
  { 11035, 0x0000 }, { 11035, 0x0000 }, { 11035, 0x0020 }, { 11036, 0x0000 },
  /* 0x247C0 */
  { 11036, 0x0000 }, { 11036, 0x0000 }, { 11036, 0x0000 }, { 11036, 0x0002 },
  /* 0x24880 */
  { 11037, 0x0000 }, { 11037, 0x0040 }, { 11038, 0x0000 }, { 11038, 0x0000 },
  /* 0x24A40 */
  { 11038, 0x2000 }, { 11039, 0x0000 }, { 11039, 0x0000 }, { 11039, 0x0000 },
  /* 0x24B40 */
  { 11039, 0x0000 }, { 11039, 0x0040 }, { 11040, 0x8000 }, { 11041, 0x0000 },
  /* 0x24C00 */
  { 11041, 0x0000 }, { 11041, 0x0040 }, { 11042, 0x0000 }, { 11042, 0x0000 },
  /* 0x24D00 */
  { 11042, 0x0000 }, { 11042, 0x0010 }, { 11043, 0x0000 }, { 11043, 0x0000 },
  /* 0x24E00 */
  { 11043, 0x4000 }, { 11044, 0x0000 }, { 11044, 0x0000 }, { 11044, 0x0080 },
  /* 0x24E40 */
  { 11045, 0x0000 }, { 11045, 0x0000 }, { 11045, 0x0400 }, { 11046, 0x0000 },
  /* 0x24E80 */
  { 11046, 0x0800 }, { 11047, 0x0000 }, { 11047, 0x0000 }, { 11047, 0x0000 },
  /* 0x25040 */
  { 11047, 0x0400 }, { 11048, 0x0020 }, { 11049, 0x0000 }, { 11049, 0x0000 },
  /* 0x25100 */
  { 11049, 0x0000 }, { 11049, 0x0000 }, { 11049, 0x0004 }, { 11050, 0x0000 },
  /* 0x25180 */
  { 11050, 0x0000 }, { 11050, 0x0000 }, { 11050, 0x0200 }, { 11051, 0x0000 },
  /* 0x251C0 */
  { 11051, 0x2000 }, { 11052, 0x0000 }, { 11052, 0x0020 }, { 11053, 0x0000 },
  /* 0x25200 */
  { 11053, 0x0000 }, { 11053, 0x4000 }, { 11054, 0x0000 }, { 11054, 0x0000 },
  /* 0x25240 */
  { 11054, 0x1000 }, { 11055, 0x0000 }, { 11055, 0x0000 }, { 11055, 0x0000 },
  /* 0x25400 */
  { 11055, 0x0000 }, { 11055, 0x0000 }, { 11055, 0x4000 }, { 11056, 0x0000 },
  /* 0x25480 */
  { 11056, 0x4000 }, { 11057, 0x0000 }, { 11057, 0x0000 }, { 11057, 0x0000 },
  /* 0x254C0 */
  { 11057, 0x0000 }, { 11057, 0x0200 }, { 11058, 0x0000 }, { 11058, 0x0000 },
  /* 0x25500 */
  { 11058, 0x4000 }, { 11059, 0x0000 }, { 11059, 0x0000 }, { 11059, 0x0000 },
  /* 0x25580 */
  { 11059, 0x0000 }, { 11059, 0x0000 }, { 11059, 0x0080 }, { 11060, 0x0000 },
  /* 0x25740 */
  { 11060, 0x0000 }, { 11060, 0x0000 }, { 11060, 0x0000 }, { 11060, 0x0002 },
  /* 0x25780 */
  { 11061, 0x0000 }, { 11061, 0x0000 }, { 11061, 0x0200 }, { 11062, 0x0010 },
  /* 0x259C0 */
  { 11063, 0x0010 }, { 11064, 0x0010 }, { 11065, 0x0000 }, { 11065, 0x0000 },
  /* 0x25AC0 */
  { 11065, 0x0000 }, { 11065, 0x0000 }, { 11065, 0x0018 }, { 11067, 0x0002 },
  /* 0x25B80 */
  { 11068, 0x0000 }, { 11068, 0x0000 }, { 11068, 0x0000 }, { 11068, 0x0004 },
  /* 0x25C40 */
  { 11069, 0x0800 }, { 11070, 0x0000 }, { 11070, 0x0010 }, { 11071, 0x0000 },
  /* 0x25D80 */
  { 11071, 0x0000 }, { 11071, 0x0000 }, { 11071, 0x0002 }, { 11072, 0x0000 },
  /* 0x25E00 */
  { 11072, 0x0000 }, { 11072, 0x0000 }, { 11072, 0x4000 }, { 11073, 0x0000 },
  /* 0x25E40 */
  { 11073, 0x0000 }, { 11073, 0x0040 }, { 11074, 0x0024 }, { 11076, 0x0000 },
  /* 0x25EC0 */
  { 11076, 0x0004 }, { 11077, 0x0100 }, { 11078, 0x0100 }, { 11079, 0x0000 },
  /* 0x25F00 */
  { 11079, 0x0000 }, { 11079, 0x0000 }, { 11079, 0x0008 }, { 11080, 0x0000 },
  /* 0x25F40 */
  { 11080, 0x0000 }, { 11080, 0x1000 }, { 11081, 0x0000 }, { 11081, 0x0000 },
  /* 0x25FC0 */
  { 11081, 0x0000 }, { 11081, 0x0010 }, { 11082, 0x0001 }, { 11083, 0x0800 },
  /* 0x26000 */
  { 11084, 0x1000 }, { 11085, 0x0080 }, { 11086, 0x0000 }, { 11086, 0x0000 },
  /* 0x26040 */
  { 11086, 0x0000 }, { 11086, 0x0000 }, { 11086, 0x0001 }, { 11087, 0x0000 },
  /* 0x260C0 */
  { 11087, 0x0000 }, { 11087, 0x0000 }, { 11087, 0x2000 }, { 11088, 0x0000 },
  /* 0x26240 */
  { 11088, 0x0000 }, { 11088, 0x0000 }, { 11088, 0x0000 }, { 11088, 0x0001 },
  /* 0x26280 */
  { 11089, 0x0040 }, { 11090, 0x0000 }, { 11090, 0x0000 }, { 11090, 0x0000 },
  /* 0x26340 */
  { 11090, 0x1000 }, { 11091, 0x0000 }, { 11091, 0x0000 }, { 11091, 0x0000 },
  /* 0x26400 */
  { 11091, 0x0004 }, { 11092, 0x0000 }, { 11092, 0x0000 }, { 11092, 0x0000 },
  /* 0x26640 */
  { 11092, 0x0000 }, { 11092, 0x0000 }, { 11092, 0x0000 }, { 11092, 0x4000 },
  /* 0x26680 */
  { 11093, 0x0000 }, { 11093, 0x0000 }, { 11093, 0x0000 }, { 11093, 0x0001 },
  /* 0x26700 */
  { 11094, 0x0000 }, { 11094, 0x2000 }, { 11095, 0x0000 }, { 11095, 0x0000 },
  /* 0x268C0 */
  { 11095, 0x0000 }, { 11095, 0x2000 }, { 11096, 0x0400 }, { 11097, 0x0000 },
  /* 0x26940 */
  { 11097, 0x0000 }, { 11097, 0x0002 }, { 11098, 0x8000 }, { 11099, 0x0000 },
  /* 0x269C0 */
  { 11099, 0x0000 }, { 11099, 0x2000 }, { 11100, 0x0000 }, { 11100, 0x0000 },
  /* 0x26A00 */
  { 11100, 0x0000 }, { 11100, 0x4000 }, { 11101, 0x0000 }, { 11101, 0x0000 },
  /* 0x26A40 */
  { 11101, 0x0000 }, { 11101, 0x0100 }, { 11102, 0x0000 }, { 11102, 0x0000 },
  /* 0x26A80 */
  { 11102, 0x1000 }, { 11103, 0x0000 }, { 11103, 0x0000 }, { 11103, 0x0080 },
  /* 0x26AC0 */
  { 11104, 0x0000 }, { 11104, 0x0000 }, { 11104, 0x0000 }, { 11104, 0x8000 },
  /* 0x26C00 */
  { 11105, 0x0000 }, { 11105, 0x0000 }, { 11105, 0x0200 }, { 11106, 0x0000 },
  /* 0x26C40 */
  { 11106, 0x0000 }, { 11106, 0x0000 }, { 11106, 0x0000 }, { 11106, 0x0008 },
  /* 0x26CC0 */
  { 11107, 0x0000 }, { 11107, 0x2000 }, { 11108, 0x0000 }, { 11108, 0x0000 },
  /* 0x26E40 */
  { 11108, 0x0001 }, { 11109, 0x0000 }, { 11109, 0x0020 }, { 11110, 0x0000 },
  /* 0x26F80 */
  { 11110, 0x0000 }, { 11110, 0x0010 }, { 11111, 0x0000 }, { 11111, 0x0000 },
  /* 0x26FC0 */
  { 11111, 0x0000 }, { 11111, 0x0000 }, { 11111, 0x0000 }, { 11111, 0x01c0 },
  /* 0x270C0 */
  { 11114, 0x0000 }, { 11114, 0x0000 }, { 11114, 0x0000 }, { 11114, 0x0010 },
  /* 0x27100 */
  { 11115, 0x2000 }, { 11116, 0x0000 }, { 11116, 0x0000 }, { 11116, 0x0200 },
  /* 0x273C0 */
  { 11117, 0x0000 }, { 11117, 0x0c00 }, { 11119, 0x0000 }, { 11119, 0x4000 },
  /* 0x27400 */
  { 11120, 0x0000 }, { 11120, 0x0001 }, { 11121, 0x0000 }, { 11121, 0x0000 },
  /* 0x27440 */
  { 11121, 0x0200 }, { 11122, 0x0000 }, { 11122, 0x0000 }, { 11122, 0x0000 },
  /* 0x27600 */
  { 11122, 0x0000 }, { 11122, 0x0030 }, { 11124, 0x0000 }, { 11124, 0x0002 },
  /* 0x27680 */
  { 11125, 0x0010 }, { 11126, 0x0008 }, { 11127, 0x0000 }, { 11127, 0x0000 },
  /* 0x27700 */
  { 11127, 0x4000 }, { 11128, 0x0000 }, { 11128, 0x0008 }, { 11129, 0x0000 },
  /* 0x27740 */
  { 11129, 0x0000 }, { 11129, 0x0004 }, { 11130, 0x0000 }, { 11130, 0x0000 },
  /* 0x27980 */
  { 11130, 0x0020 }, { 11131, 0x0000 }, { 11131, 0x0000 }, { 11131, 0x0000 },
  /* 0x27A80 */
  { 11131, 0x0010 }, { 11132, 0x0000 }, { 11132, 0x0000 }, { 11132, 0x0000 },
  /* 0x27B80 */
  { 11132, 0x0000 }, { 11132, 0x0000 }, { 11132, 0x0000 }, { 11132, 0x4008 },
  /* 0x27BC0 */
  { 11134, 0x0080 }, { 11135, 0x0000 }, { 11135, 0x0000 }, { 11135, 0x0000 },
  /* 0x27C80 */
  { 11135, 0x0000 }, { 11135, 0x0000 }, { 11135, 0x0000 }, { 11135, 0x0100 },
  /* 0x27D80 */
  { 11136, 0x0000 }, { 11136, 0x0000 }, { 11136, 0x0001 }, { 11137, 0x0000 },
  /* 0x27E00 */
  { 11137, 0x0000 }, { 11137, 0x0001 }, { 11138, 0x0000 }, { 11138, 0x0000 },
  /* 0x27F80 */
  { 11138, 0x0000 }, { 11138, 0x0000 }, { 11138, 0x0000 }, { 11138, 0x0080 },
  /* 0x28080 */
  { 11139, 0x0400 }, { 11140, 0x0000 }, { 11140, 0x0000 }, { 11140, 0x0800 },
  /* 0x28240 */
  { 11141, 0x0000 }, { 11141, 0x0000 }, { 11141, 0x0000 }, { 11141, 0x0080 },
  /* 0x28280 */
  { 11142, 0x0004 }, { 11143, 0x0000 }, { 11143, 0x0000 }, { 11143, 0x0000 },
  /* 0x282C0 */
  { 11143, 0x0000 }, { 11143, 0x0000 }, { 11143, 0x0000 }, { 11143, 0x0008 },
  /* 0x283C0 */
  { 11144, 0x2000 }, { 11145, 0x0000 }, { 11145, 0x0000 }, { 11145, 0x0000 },
  /* 0x28400 */
  { 11145, 0x1000 }, { 11146, 0x0000 }, { 11146, 0x0000 }, { 11146, 0x0000 },
  /* 0x28440 */
  { 11146, 0x0000 }, { 11146, 0x0020 }, { 11147, 0x0000 }, { 11147, 0x0000 },
  /* 0x28540 */
  { 11147, 0x0000 }, { 11147, 0x0000 }, { 11147, 0x0800 }, { 11148, 0x0000 },
  /* 0x285C0 */
  { 11148, 0x0300 }, { 11150, 0x0000 }, { 11150, 0x0000 }, { 11150, 0x0000 },
  /* 0x286C0 */
  { 11150, 0x0000 }, { 11150, 0x0080 }, { 11151, 0x0000 }, { 11151, 0x0400 },
  /* 0x28940 */
  { 11152, 0x0240 }, { 11154, 0x0000 }, { 11154, 0x0800 }, { 11155, 0x0000 },
  /* 0x28980 */
  { 11155, 0x0180 }, { 11157, 0x0000 }, { 11157, 0x0000 }, { 11157, 0x0c00 },
  /* 0x28A00 */
  { 11159, 0x0000 }, { 11159, 0x4000 }, { 11160, 0x0200 }, { 11161, 0x0000 },
  /* 0x28A40 */
  { 11161, 0x0008 }, { 11162, 0x0000 }, { 11162, 0x0000 }, { 11162, 0x0002 },
  /* 0x28A80 */
  { 11163, 0x0000 }, { 11163, 0x0200 }, { 11164, 0x0000 }, { 11164, 0x0000 },
  /* 0x28AC0 */
  { 11164, 0x2000 }, { 11165, 0x2000 }, { 11166, 0x0010 }, { 11167, 0x0000 },
  /* 0x28BC0 */
  { 11167, 0x0002 }, { 11168, 0x0000 }, { 11168, 0x8000 }, { 11169, 0x0000 },
  /* 0x28D00 */
  { 11169, 0x0000 }, { 11169, 0x0001 }, { 11170, 0x0000 }, { 11170, 0x0000 },
  /* 0x28D40 */
  { 11170, 0x0000 }, { 11170, 0x0000 }, { 11170, 0x0000 }, { 11170, 0x0002 },
  /* 0x28DC0 */
  { 11171, 0x0000 }, { 11171, 0x0000 }, { 11171, 0x0000 }, { 11171, 0x0800 },
  /* 0x28E00 */
  { 11172, 0x0000 }, { 11172, 0x8000 }, { 11173, 0x0000 }, { 11173, 0x0040 },
  /* 0x28E80 */
  { 11174, 0x0200 }, { 11175, 0x0000 }, { 11175, 0x0000 }, { 11175, 0x0000 },
  /* 0x28EC0 */
  { 11175, 0x0000 }, { 11175, 0x0000 }, { 11175, 0x0800 }, { 11176, 0x0000 },
  /* 0x28F00 */
  { 11176, 0x0000 }, { 11176, 0x0000 }, { 11176, 0x0000 }, { 11176, 0x0004 },
  /* 0x28FC0 */
  { 11177, 0x0000 }, { 11177, 0x0000 }, { 11177, 0x0000 }, { 11177, 0x0100 },
  /* 0x29280 */
  { 11178, 0x0000 }, { 11178, 0x0000 }, { 11178, 0x0001 }, { 11179, 0x0002 },
  /* 0x29480 */
  { 11180, 0x0000 }, { 11180, 0x0001 }, { 11181, 0x0000 }, { 11181, 0x0000 },
  /* 0x295C0 */
  { 11181, 0x8000 }, { 11182, 0x0000 }, { 11182, 0x0000 }, { 11182, 0x0000 },
  /* 0x29640 */
  { 11182, 0x0000 }, { 11182, 0x0000 }, { 11182, 0x0000 }, { 11182, 0x8000 },
  /* 0x296C0 */
  { 11183, 0x0000 }, { 11183, 0x0000 }, { 11183, 0x0000 }, { 11183, 0x0001 },
  /* 0x29700 */
  { 11184, 0x0000 }, { 11184, 0x0200 }, { 11185, 0x0000 }, { 11185, 0x0000 },
  /* 0x29740 */
  { 11185, 0x0000 }, { 11185, 0x0001 }, { 11186, 0x0000 }, { 11186, 0x0000 },
  /* 0x298C0 */
  { 11186, 0x0040 }, { 11187, 0x0000 }, { 11187, 0x0000 }, { 11187, 0x0000 },
  /* 0x29A40 */
  { 11187, 0x0000 }, { 11187, 0x0000 }, { 11187, 0x0000 }, { 11187, 0x0004 },
  /* 0x29DC0 */
  { 11188, 0x0000 }, { 11188, 0x0800 }, { 11189, 0x0000 }, { 11189, 0x0000 },
  /* 0x29E00 */
  { 11189, 0x0000 }, { 11189, 0x0020 }, { 11190, 0x0000 }, { 11190, 0x2000 },
  /* 0x29E40 */
  { 11191, 0x0200 }, { 11192, 0x0000 }, { 11192, 0x0000 }, { 11192, 0x0000 },
  /* 0x29E80 */
  { 11192, 0x0400 }, { 11193, 0x0000 }, { 11193, 0x0000 }, { 11193, 0x0000 },
  /* 0x29EC0 */
  { 11193, 0x0010 }, { 11194, 0x0800 }, { 11195, 0x0200 }, { 11196, 0x0000 },
  /* 0x29FC0 */
  { 11196, 0x4000 }, { 11197, 0x0000 }, { 11197, 0x0000 }, { 11197, 0x0000 },
  /* 0x2A000 */
  { 11197, 0x0000 }, { 11197, 0x0400 }, { 11198, 0x8000 }, { 11199, 0x0000 },
  /* 0x2A080 */
  { 11199, 0x0004 }, { 11200, 0x0000 }, { 11200, 0x0000 }, { 11200, 0x0000 },
  /* 0x2A0C0 */
  { 11200, 0x0000 }, { 11200, 0x0000 }, { 11200, 0x0000 }, { 11200, 0x0200 },
  /* 0x2A180 */
  { 11201, 0x0000 }, { 11201, 0x0001 }, { 11202, 0x0000 }, { 11202, 0x0000 },
  /* 0x2A380 */
  { 11202, 0x1000 }, { 11203, 0x0000 }, { 11203, 0x0000 }, { 11203, 0x0000 },
  /* 0x2A400 */
  { 11203, 0x0000 }, { 11203, 0x0000 }, { 11203, 0x0000 }, { 11203, 0x0080 },
  /* 0x2A5C0 */
  { 11204, 0x0000 }, { 11204, 0x0000 }, { 11204, 0x0000 }, { 11204, 0x0002 },
  /* 0x2A600 */
  { 11205, 0x0004 }, { 11206, 0x0400 }, { 11207, 0x0000 }, { 11207, 0x0000 },
  /* 0x2A680 */
  { 11207, 0x0000 }, { 11207, 0x0000 }, { 11207, 0x0000 }, { 11207, 0x0004 },
};

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
static ucs4_t jisx0213_to_ucs4 (unsigned int row, unsigned int col)
{
  ucs4_t val;

  if (row >= 0x121 && row <= 0x17e)
    row -= 289;
  else if (row == 0x221)
    row -= 451;
  else if (row >= 0x223 && row <= 0x225)
    row -= 452;
  else if (row == 0x228)
    row -= 454;
  else if (row >= 0x22c && row <= 0x22f)
    row -= 457;
  else if (row >= 0x26e && row <= 0x27e)
    row -= 519;
  else
    return 0x0000;

  if (col >= 0x21 && col <= 0x7e)
    col -= 0x21;
  else
    return 0x0000;

  val = jisx0213_to_ucs_main[row * 94 + col];
  val = jisx0213_to_ucs_pagestart[val >> 8] + (val & 0xff);
  if (val == 0xfffd)
    val = 0x0000;
  return val;
}

#ifdef __GNUC__
__inline
#else
#ifdef __cplusplus
inline
#endif
#endif
static unsigned short ucs4_to_jisx0213 (ucs4_t ucs)
{
  if (ucs < (sizeof(jisx0213_from_ucs_level1)/sizeof(jisx0213_from_ucs_level1[0])) << 6) {
    int index1 = jisx0213_from_ucs_level1[ucs >> 6];
    if (index1 >= 0) {
      const Summary16 *summary = &jisx0213_from_ucs_level2_2indx[((index1 << 6) + (ucs & 0x3f)) >> 4];
      unsigned short used = summary->used;
      unsigned int i = ucs & 0x0f;
      if (used & ((unsigned short) 1 << i)) {
        /* Keep in 'used' only the bits 0..i-1. */
        used &= ((unsigned short) 1 << i) - 1;
        /* Add 'summary->indx' and the number of bits set in 'used'. */
        used = (used & 0x5555) + ((used & 0xaaaa) >> 1);
        used = (used & 0x3333) + ((used & 0xcccc) >> 2);
        used = (used & 0x0f0f) + ((used & 0xf0f0) >> 4);
        used = (used & 0x00ff) + (used >> 8);
        return jisx0213_from_ucs_level2_data[summary->indx + used];
      };
    };
  }
  return 0x0000;
}

#endif /* _JISX0213_H */
