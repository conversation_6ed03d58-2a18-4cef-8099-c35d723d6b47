COMPILER = g++
CXXFLAGS = -Wall -pedantic -Wno-long-long
INCLUDEDIRS = -I../../include -I../../include/private

BACKENDLOADERDEFS = -DSOCI_LIB_PREFIX=\"libsoci_\" -DSOCI_LIB_SUFFIX=\".so\"

OBJS =  session.o statement.o row.o values.o \
	into-type.o use-type.o \
	blob.o rowid.o procedure.o ref-counted-prepare-info.o ref-counted-statement.o \
	once-temp-type.o prepare-temp-type.o error.o transaction.o backend-loader.o \
	connection-pool.o connection-parameters.o soci-simple.o


libsoci_core.a : generated ${OBJS}
	ar rv $@ ${OBJS}
	rm *.o

shared : generated ${OBJS}
	${COMPILER} -fPIC -c ${OBJS} ${CXXFLAGS} ${INCLUDEDIRS}
	${COMPILER} -shared -o libsoci_core.so ${OBJS}
	rm *.o

generated : ../../include/soci/soci-config.h ../../include/private/soci_backends_config.h

# Note: this file is generated without any configured variables,
# full configuration fill is generated by CMake.
../../include/soci/soci-config.h : ../../include/soci/soci-config.h.in
	grep -v CONFIGURED_VARIABLES $? > $@

# Note: this file is generated with a basic search path,
# full backends search path is generated by CMake.
../../include/private/soci_backends_config.h : soci_backends_config.h.in
	echo '#define DEFAULT_BACKENDS_PATH "."' > $@

session.o : session.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

statement.o : statement.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

row.o : row.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

values.o : values.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

into-type.o : into-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

use-type.o : use-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

blob.o : blob.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

error.o : error.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

rowid.o : rowid.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

procedure.o : procedure.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

ref-counted-prepare-info.o : ref-counted-prepare-info.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

ref-counted-statement.o : ref-counted-statement.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

once-temp-type.o : once-temp-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

prepare-temp-type.o : prepare-temp-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

transaction.o : transaction.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

backend-loader.o : backend-loader.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${BACKENDLOADERDEFS} ${INCLUDEDIRS}

connection-pool.o : connection-pool.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

connection-parameters.o : connection-parameters.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

soci-simple.o : soci-simple.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}


clean :
	rm -f libsoci_core.a libsoci_core.so ../../include/private/soci_backends_config.h
