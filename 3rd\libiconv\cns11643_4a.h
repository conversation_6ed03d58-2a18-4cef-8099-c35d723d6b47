/*
 * Copyright (C) 1999-2002 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 4 part a
 */

static const unsigned short cns11643_4a_2uni_page21[2914] = {
  /* 0x21 */
  0x5a86, 0x1840, 0x1841, 0x185a, 0x75e8, 0x1802, 0x1829, 0x5b0e,
  0x6027, 0x1c02, 0x013e, 0x27dc, 0x5f3c, 0x6075, 0xd128, 0x1d42,
  0x1d6a, 0x2552, 0x6f3c, 0xd175, 0xd178, 0x29c4, 0x2c4c, 0x39ad,
  0x1812, 0x182f, 0x1896, 0x18d0, 0x1b42, 0x1b83, 0xd119, 0x60a5,
  0x60aa, 0x630f, 0x640e, 0x1d83, 0x1db8, 0x659c, 0x659b, 0x6a3c,
  0x2328, 0x71c2, 0x2623, 0x2801, 0x2900, 0x87b4, 0x08b8, 0x376c,
  0x392b, 0x1b88, 0x4879, 0x51b6, 0x1817, 0x5a65, 0x000c, 0x5bb2,
  0x0030, 0x18e2, 0x18db, 0x5e77, 0x5f42, 0x1bad, 0x6033, 0x1bf7,
  0x00da, 0x60af, 0x6236, 0x0113, 0x1d1b, 0x1d88, 0x1d87, 0x6522,
  0x1dcf, 0x1dfd, 0x0163, 0x1de7, 0x20dc, 0x69a3, 0x20d9, 0x2125,
  0x2127, 0x2333, 0x2613, 0x7225, 0x7224, 0x2675, 0x7652, 0x7789,
  0x7abf, 0x05c4, 0x05c3, 0x2ff1, 0x87b5, 0xa24c,
  /* 0x22 */
  0x4552, 0xc714, 0xc712, 0x0001, 0x5aa3, 0x5aa2, 0x1851, 0x186a,
  0x5bb6, 0x190c, 0x5bb5, 0x5bb4, 0x18fe, 0x191b, 0x5bc2, 0x5bb8,
  0x003a, 0x5e79, 0x00ab, 0x1b73, 0x5f08, 0x1b8e, 0x5f7a, 0x5fb6,
  0x60bd, 0x60b7, 0x60bc, 0x00dd, 0x60c4, 0x60c9, 0x1ca5, 0x0115,
  0x1ca7, 0x1ca4, 0x6330, 0x6383, 0x6385, 0x6412, 0x6434, 0x1dbd,
  0x64e4, 0x64de, 0x652a, 0x1e02, 0x65af, 0x65b4, 0x65b3, 0x65b1,
  0x212b, 0x231b, 0x2335, 0x6cde, 0x02a7, 0x02a5, 0x6db8, 0x6db0,
  0x02a6, 0x6fc2, 0x59fd, 0x2617, 0x037c, 0x722a, 0x2670, 0x267d,
  0x03a9, 0x75e9, 0x27e9, 0x7657, 0x0434, 0x0435, 0x77b6, 0x77d7,
  0x77de, 0x04a8, 0x2919, 0x291c, 0x2975, 0x7ac2, 0x7bab, 0x04ff,
  0x29c8, 0x7e8f, 0x05c7, 0x05c6, 0x05c8, 0x7f6d, 0x82b5, 0x06d0,
  0x06d1, 0x87c0, 0x87bf, 0x0859, 0x8bb9, 0x8d1c,
  /* 0x23 */
  0x3412, 0x08bd, 0x8e80, 0x9184, 0x9185, 0x0a28, 0x39b3, 0x9524,
  0x0aa9, 0x3a90, 0x3c36, 0xaa4c, 0x0fcc, 0xb1f6, 0x4881, 0x51b8,
  0xc370, 0x14b4, 0xc375, 0xc717, 0x1823, 0x0016, 0x002c, 0x5bf1,
  0x192e, 0x5bda, 0x1b4f, 0x5fb9, 0x1bba, 0x00df, 0x00e0, 0x1c22,
  0x60ce, 0x60d2, 0x60d0, 0x0117, 0x6243, 0x1caf, 0x1cb0, 0x1cb1,
  0x624d, 0x6334, 0x012f, 0x63d9, 0x1d64, 0x6418, 0x1dd3, 0x6538,
  0x016a, 0x65d7, 0x0170, 0x016d, 0x65dc, 0x65cb, 0x65d6, 0x65d8,
  0x016e, 0x65d1, 0xd13e, 0xd13c, 0x65d2, 0x022b, 0x0228, 0x6a59,
  0x59fd, 0x233f, 0x6ce5, 0x6ce6, 0x0292, 0x6ce8, 0x238b, 0x6dbd,
  0x2391, 0x2395, 0x6dbe, 0x6f49, 0x033f, 0x6fcd, 0x258a, 0x034f,
  0x0374, 0x715e, 0x71d0, 0x037d, 0x7306, 0x03b7, 0x03a3, 0x03b0,
  0x03b1, 0x2687, 0x03ab, 0x737c, 0x7371, 0x75ef,
  /* 0x24 */
  0x043a, 0x0437, 0x280d, 0x0438, 0x0440, 0x778d, 0x77b7, 0x288e,
  0x049f, 0x7933, 0x797f, 0x297a, 0x7bf2, 0x0504, 0x0509, 0x0506,
  0x04fd, 0x050a, 0x0507, 0x7f2a, 0x7f8b, 0x05ca, 0x7f83, 0x7f8a,
  0x2c90, 0x05c9, 0x7f8e, 0x2c9a, 0x82b6, 0x2e3c, 0x2e3a, 0x067f,
  0x2e98, 0x84d8, 0x06d2, 0x8556, 0x87d2, 0x3065, 0xd1db, 0x0743,
  0x87c2, 0x8a87, 0x8a81, 0x8bc2, 0x8bbc, 0x8d23, 0x08c1, 0x8e93,
  0x08c5, 0x09a3, 0x918d, 0x918e, 0x0a2a, 0x0a5f, 0x953b, 0x954d,
  0x0a5d, 0x9534, 0x9531, 0x96f2, 0x96f0, 0x0b17, 0x9a15, 0x9b28,
  0x0b71, 0x0b72, 0x9c25, 0x9de7, 0x0c0f, 0x9de9, 0xa017, 0x40c2,
  0x0d91, 0xa96c, 0xa980, 0xaa4b, 0x0fb2, 0x0fcf, 0x0fce, 0x469e,
  0xafde, 0xaffb, 0x47eb, 0xb051, 0x4889, 0x1096, 0xb201, 0xb202,
  0x4896, 0xb205, 0x4887, 0xb203, 0x1097, 0xc151,
  /* 0x25 */
  0x51c0, 0x148b, 0x51c3, 0xc153, 0xc37a, 0xc378, 0xc376, 0xc379,
  0xc38f, 0xc37c, 0x5578, 0xc71b, 0xc71c, 0x5625, 0x8f92, 0x1875,
  0x1874, 0x5b2e, 0x5b2c, 0x002d, 0x5c0c, 0x1999, 0x5c0b, 0x0050,
  0x004b, 0x5c05, 0x004f, 0x004c, 0xd105, 0x1971, 0x1b53, 0x1bbf,
  0x5fc2, 0x5fc3, 0x1bc0, 0x6048, 0x1bee, 0x6046, 0x00e4, 0x00e3,
  0x60ea, 0x00e1, 0x60eb, 0x00e2, 0x60ee, 0x1c3d, 0x0119, 0x1cbd,
  0x1d0c, 0x62d8, 0x9a1e, 0x63df, 0x63de, 0x0141, 0x4537, 0x644f,
  0x1dc0, 0x015e, 0x660d, 0x6608, 0x0179, 0x6609, 0x1e6e, 0x1e83,
  0x6612, 0x6665, 0x1e5e, 0x1e5d, 0x217e, 0x2179, 0x6a89, 0x217a,
  0x216c, 0x6a84, 0x6a9e, 0x6a8b, 0x0232, 0x2187, 0x6c62, 0x231d,
  0x0294, 0x2346, 0x0297, 0x6cf9, 0x2343, 0x6cf7, 0x0296, 0x0298,
  0x6d06, 0x6ddd, 0x02b2, 0x6dd9, 0x6ddf, 0x6de2,
  /* 0x26 */
  0x02b9, 0x2561, 0x2566, 0x5af1, 0x2590, 0x0375, 0x037f, 0x037e,
  0x2629, 0x038f, 0x723f, 0x723e, 0x730b, 0x03bd, 0x26b2, 0x7383,
  0x03bb, 0x03bc, 0x7386, 0x26c0, 0x767c, 0x043d, 0x043e, 0x0474,
  0x77f7, 0x047a, 0x0476, 0x0478, 0x0475, 0x77fb, 0x793b, 0x04af,
  0x04b0, 0x04c7, 0x04cc, 0x7bf9, 0x7bca, 0x0516, 0x7bff, 0x0512,
  0x051d, 0x7c09, 0x7c03, 0x0515, 0x050f, 0x0514, 0x2a1f, 0x29e2,
  0x7c10, 0x7e9a, 0x7e9f, 0x05b0, 0x05bf, 0x05c0, 0x7f87, 0x05d2,
  0x05d9, 0x82b7, 0x067a, 0x8308, 0x82f9, 0x0681, 0x0682, 0x82f4,
  0x2f16, 0x2ef9, 0x06da, 0xad57, 0x3088, 0x87d6, 0x309b, 0xd1de,
  0x306e, 0x309e, 0x87d4, 0x0822, 0x081f, 0x8a90, 0x8a8d, 0x0821,
  0x3324, 0x8b60, 0x8bce, 0x8bcf, 0x085c, 0x337d, 0x8bd1, 0x087d,
  0x088d, 0x088f, 0x34e6, 0x8eca, 0x34cb, 0x08d0,
  /* 0x27 */
  0x8eb7, 0x08d8, 0x8eb2, 0x8ebb, 0x8eb9, 0x34b5, 0x09a7, 0x919e,
  0x3797, 0x91b2, 0x379b, 0x0a12, 0x936f, 0x93ac, 0x0a2f, 0x9423,
  0x396b, 0x0a2e, 0x0a2c, 0x0a5c, 0x956c, 0x39d5, 0x9532, 0x955e,
  0x0a62, 0x0a67, 0x0ab4, 0x96fb, 0x9929, 0x99bd, 0x9a1f, 0x3c43,
  0x7941, 0x9a1d, 0x3c9c, 0x9b31, 0x9b2f, 0x0bea, 0x9d43, 0x0bfb,
  0x9d41, 0x0c14, 0x9df5, 0x0c13, 0x0c12, 0x0c10, 0x0c11, 0x9df2,
  0x0c86, 0x3ee4, 0x0c98, 0xa01e, 0xa254, 0x0d2a, 0xa256, 0xa329,
  0xa327, 0xa328, 0x41ce, 0x0eb5, 0xa979, 0xa97e, 0xabb6, 0xabb7,
  0x4613, 0x0fd6, 0x0fd8, 0x46b7, 0x0fd9, 0x0fd4, 0x0fd7, 0xd281,
  0x46b9, 0x59fd, 0xad4d, 0x47e4, 0x47fd, 0x480f, 0x1060, 0xb109,
  0xb108, 0x109e, 0x10a1, 0xb21e, 0xb21a, 0xb223, 0xb215, 0xb219,
  0xb216, 0x48bf, 0x48ca, 0xb214, 0xb218, 0x48c1,
  /* 0x28 */
  0x10a0, 0xb51f, 0xb6dd, 0xb6df, 0xc169, 0x51d0, 0x59fd, 0xc16c,
  0x14b9, 0xc393, 0x52ae, 0xc395, 0xc394, 0xc391, 0xc397, 0xc730,
  0x15c1, 0x15c2, 0x5638, 0xd318, 0x001c, 0x5c31, 0x005e, 0x19bc,
  0x0059, 0x005c, 0x5c36, 0x005f, 0x19e9, 0x19bd, 0x19e2, 0x1b58,
  0x5fc9, 0x5fca, 0x00ce, 0x6118, 0x6112, 0x6119, 0x6110, 0x6116,
  0x1cc6, 0x625f, 0x1cc8, 0x62df, 0x6317, 0x1d28, 0x633c, 0x1d29,
  0x63ea, 0x63e9, 0x641c, 0x6458, 0x6464, 0x64f3, 0x015f, 0x6697,
  0x0185, 0x665a, 0x6659, 0x0186, 0x668e, 0x21b4, 0x6abc, 0x21a9,
  0x0287, 0x6d0d, 0x6cfc, 0x6d0c, 0x6e07, 0x6e0a, 0x02ca, 0x6e02,
  0x02c3, 0x6e26, 0x6e08, 0x6e1d, 0x02c2, 0x2568, 0x6f4e, 0x0341,
  0x6fed, 0x6fee, 0x6fef, 0x0380, 0x0381, 0x7250, 0x724c, 0x0393,
  0x0392, 0x724f, 0x03c5, 0x73d4, 0x59fd, 0x73b0,
  /* 0x29 */
  0x0446, 0x0441, 0x0445, 0x0442, 0x043f, 0x76a5, 0x7809, 0x0482,
  0x0481, 0x047f, 0x04a5, 0x292b, 0x04b3, 0x04b5, 0x79f3, 0x79d7,
  0x298d, 0x04da, 0x7adc, 0x04db, 0x050d, 0x2a18, 0x050e, 0x7c0b,
  0x051e, 0x0525, 0x0526, 0x051c, 0x0521, 0x2a57, 0x2a48, 0x0527,
  0x051a, 0x7c3a, 0x7c0d, 0x7c11, 0x2a38, 0x7c20, 0x7c01, 0x0524,
  0x7c37, 0xd1a3, 0x2a71, 0x7eaa, 0x05c1, 0x05e1, 0x7fe7, 0x7fe8,
  0x2d12, 0x05eb, 0x7ff5, 0x05e2, 0x05d7, 0x05e9, 0x8035, 0x7fe6,
  0x2d0a, 0x7ff8, 0x2d23, 0x7ff1, 0x0684, 0x8314, 0x8315, 0x8309,
  0x06b5, 0x06bc, 0x06dc, 0x06de, 0x06df, 0x857b, 0x2f2a, 0x857e,
  0x8766, 0x8767, 0x880a, 0x0754, 0x30e0, 0x30be, 0x0753, 0x0824,
  0x0825, 0x3329, 0x0828, 0x0827, 0x8a9c, 0x8b62, 0x8be1, 0x8bde,
  0x8bdf, 0x8be9, 0x088b, 0x8ee4, 0x8ef7, 0x08dc,
  /* 0x2a */
  0x3543, 0x8eea, 0x8ef6, 0x8f12, 0xd201, 0x37a6, 0x09b2, 0x37c0,
  0x91d9, 0x392f, 0x935f, 0x0a1b, 0x9373, 0x0a32, 0x3971, 0x943f,
  0x9578, 0x0a6b, 0x957d, 0x0a6c, 0x0a6d, 0x9712, 0x0ab9, 0x0aba,
  0x0b09, 0x0b0a, 0x992f, 0x3bea, 0x0b1b, 0x9930, 0x99db, 0x3c20,
  0x0b58, 0x9a29, 0x0b5a, 0xd236, 0x9b40, 0x0b77, 0x9b3d, 0x9b3e,
  0x0b79, 0x3ca9, 0x9b38, 0x3d85, 0x9c48, 0x0beb, 0x9cd0, 0x0bfd,
  0x0bfc, 0x3e06, 0x0c15, 0x0c18, 0x3df6, 0x0c16, 0x0c17, 0x0c19,
  0x3e00, 0x0c1b, 0x9e0e, 0x9e09, 0x3e02, 0x9e07, 0x0c87, 0x9f5f,
  0x9f60, 0x0c9c, 0xa02a, 0x0c9a, 0xa029, 0x0cff, 0x0cfe, 0xa14d,
  0x0d31, 0x0d2e, 0x0d30, 0x0d32, 0xa26c, 0x59fd, 0x0d2f, 0xa267,
  0x0d95, 0x0d96, 0xa339, 0xa334, 0xa459, 0x0dc5, 0x0e7a, 0xa923,
  0x0f42, 0xa982, 0x0f54, 0xa983, 0xa9a8, 0xaa55,
  /* 0x2b */
  0xaaf9, 0xaaf8, 0xaafc, 0xaafa, 0x4609, 0xd27a, 0x0f9f, 0x0fa0,
  0x0fa2, 0x0fe0, 0xad74, 0x0fe1, 0xad6b, 0xad71, 0x0fdf, 0xad76,
  0xb10e, 0x1062, 0x1061, 0xb110, 0xb10f, 0x10a7, 0xb238, 0xb239,
  0xb23a, 0x48da, 0xb237, 0xb23e, 0x490a, 0x1189, 0xb49c, 0xb52a,
  0xb53a, 0xb52b, 0xb528, 0xb6aa, 0xb70f, 0x121d, 0xb710, 0xb8e6,
  0x132a, 0xbd25, 0xbe8e, 0x13d3, 0x1442, 0x1443, 0xc18b, 0x1491,
  0xc18c, 0xc19a, 0x1490, 0x14bf, 0xc3b3, 0x14bc, 0xc3b2, 0x14c0,
  0x15c7, 0x15c5, 0x5655, 0xc744, 0x5652, 0x1835, 0x5b58, 0x5ca7,
  0x1a34, 0x1a01, 0x5c63, 0x1a0a, 0x0066, 0x5c6a, 0x5c65, 0x5c6b,
  0x00ad, 0x5eed, 0x5fd9, 0x613b, 0x6132, 0x1c58, 0x6135, 0x6131,
  0x613e, 0x6143, 0x6136, 0x626d, 0x011d, 0x62e4, 0x0131, 0x1d2b,
  0x63f4, 0x014c, 0x6469, 0x646b, 0x0195, 0x0191,
  /* 0x2c */
  0x0194, 0x66bf, 0x66c3, 0x66ae, 0x018f, 0x1eec, 0x66b1, 0x1f15,
  0x1efe, 0x66bb, 0x66af, 0x66b0, 0x1ee3, 0x1f16, 0x0240, 0x6aef,
  0x0241, 0x6aed, 0x21d3, 0x6af0, 0x6c93, 0x6d34, 0x6d2b, 0x2359,
  0x2427, 0x02d8, 0x02d2, 0x02da, 0x2428, 0x2410, 0x02d7, 0x240e,
  0x0342, 0x0357, 0x7004, 0x7003, 0x7002, 0x25af, 0x7005, 0x25ba,
  0x25b1, 0x7000, 0x7174, 0x0378, 0x0382, 0x0397, 0x7265, 0x7263,
  0x03c9, 0x73d7, 0x03c8, 0x03d6, 0x03cc, 0x73e8, 0x03d0, 0x73db,
  0x73d8, 0x03ce, 0x03c7, 0x26fc, 0x7409, 0x03cf, 0x03cb, 0x26f2,
  0x26fe, 0x73e5, 0x73e7, 0x27f8, 0x763f, 0x0447, 0x76c0, 0x0448,
  0x76b9, 0x76ba, 0x0483, 0x0485, 0x0484, 0x7820, 0x794f, 0x292c,
  0x04b8, 0x04bc, 0x7a91, 0x7a90, 0x7aee, 0x7af3, 0x7aec, 0x7af1,
  0x7aeb, 0x7af2, 0x7af4, 0x7aed, 0x7c41, 0x7c48,
  /* 0x2d */
  0x7c45, 0x0531, 0x0534, 0x0536, 0x2a82, 0x7c7f, 0x7c8b, 0x0523,
  0x7c3b, 0x7c4e, 0x053a, 0x2a91, 0x2a8f, 0x7c7c, 0x05b4, 0x05b5,
  0x05ed, 0x05ec, 0x05d8, 0x7ff0, 0x8036, 0x803f, 0x8043, 0x8031,
  0x8034, 0x8046, 0x05f6, 0x05e7, 0x802f, 0x82c3, 0x067c, 0x067b,
  0x8336, 0x0688, 0x2e47, 0x2e4c, 0x8321, 0xd1c8, 0x2e8a, 0x8473,
  0x06be, 0x84e6, 0x06e7, 0x06e5, 0x06ee, 0x30e1, 0x314a, 0x883f,
  0x0759, 0x075e, 0x886b, 0x075a, 0x313f, 0x0761, 0x0758, 0x075b,
  0x30bd, 0x8871, 0x075f, 0xd1e2, 0x082a, 0x082d, 0x8ab0, 0x0823,
  0x082b, 0x082c, 0x8bf3, 0x087e, 0xd1f5, 0x0893, 0x0899, 0x8d61,
  0x8d5f, 0x08b3, 0x08e7, 0x08ea, 0x8f28, 0x8f1e, 0x8f29, 0xd202,
  0x8eeb, 0x8f66, 0x09b4, 0x91f6, 0x37c9, 0x0a17, 0x9360, 0x9377,
  0x93bb, 0x0a21, 0x93bc, 0x0a38, 0x0a37, 0x0a74,
  /* 0x2e */
  0x0a73, 0x0a75, 0x95a2, 0x95b2, 0x959e, 0x0a76, 0x0a78, 0x973a,
  0x9738, 0x3aba, 0x0b0c, 0x0b20, 0x0b1e, 0x9a4d, 0x0b5b, 0x9a5e,
  0x0b5c, 0x9b17, 0x0b83, 0x3cc6, 0x0b80, 0x0b81, 0x0b7e, 0x0b88,
  0x0b85, 0x0b89, 0x0b7f, 0x0b8e, 0x9b64, 0x9b67, 0x0b84, 0x3cb7,
  0x3d8c, 0x9c59, 0x3d8d, 0x9cda, 0x0bee, 0x0bed, 0x0bfe, 0x9d51,
  0x0bff, 0x9d55, 0x9e3b, 0x9e34, 0x0c23, 0x9e37, 0x3e17, 0x9e31,
  0x3e1c, 0x0c1f, 0x9e3f, 0x9e59, 0x3e14, 0x9f61, 0x0c8f, 0x0c90,
  0xa058, 0xa062, 0xa050, 0x0ca0, 0xa051, 0x0ca6, 0x0c9f, 0xa046,
  0x0ca7, 0x0ca1, 0xa12d, 0x0d02, 0x0d36, 0xa348, 0xa351, 0xa34a,
  0xa34f, 0xa350, 0xa349, 0xa463, 0x0dc8, 0xa466, 0xa460, 0x0dd8,
  0x420c, 0x0ddd, 0x0ddc, 0xa4f1, 0x0dd7, 0xa507, 0x0dda, 0x0eba,
  0xa79b, 0xa7a3, 0xa79d, 0x0ebe, 0x0ec2, 0x0ebb,
  /* 0x2f */
  0x0ec0, 0xa7a9, 0xa7a7, 0xa7a4, 0x4423, 0xa7ba, 0x77c1, 0xa926,
  0x0f43, 0x0f55, 0xa994, 0xa993, 0x0f57, 0x0f68, 0x4598, 0x4590,
  0xab07, 0xab0d, 0xab02, 0xab0c, 0xab09, 0xab08, 0xab13, 0x0fa1,
  0xabd2, 0x463a, 0xac3e, 0xac3c, 0xad67, 0xad9e, 0x0fea, 0xadad,
  0x0fe7, 0xadd9, 0xad42, 0xada3, 0xada0, 0x0fe8, 0x0fe9, 0xad7c,
  0xb004, 0x1054, 0xb058, 0xb060, 0xb0c9, 0xb0c8, 0x4826, 0x1065,
  0xb11c, 0xb11a, 0x108a, 0xb274, 0x10b0, 0xb26e, 0xb26f, 0xb279,
  0x10bc, 0x492e, 0xb277, 0x4955, 0x491a, 0x10b8, 0x493d, 0xb27c,
  0x10b2, 0xb270, 0x4930, 0x10bd, 0xb27a, 0xb282, 0x118a, 0x4b51,
  0x11a1, 0xb544, 0x11a2, 0xb543, 0x4b88, 0xb545, 0x1215, 0xb725,
  0xb71f, 0x1220, 0x1273, 0xb8e8, 0x4d8e, 0x4d8d, 0xb909, 0xb9b7,
  0xb9b9, 0xba64, 0xba63, 0x4e09, 0x4e14, 0x12b1,
  /* 0x30 */
  0xba62, 0xba65, 0x132b, 0xbbac, 0x1345, 0xbd2a, 0x1397, 0x1398,
  0xbe96, 0x13d5, 0xbfc2, 0xd2de, 0x1493, 0x1496, 0x5207, 0x1494,
  0xc1aa, 0xc1b0, 0x14c7, 0x14c5, 0xc3d4, 0xc3cd, 0xc3d6, 0x14c4,
  0xc4f0, 0x5579, 0x5584, 0x15ce, 0x15ca, 0x15cc, 0x5657, 0x15c9,
  0x56ba, 0x59fd, 0xd04b, 0x5b62, 0x006e, 0x5cb7, 0x1a67, 0x5cac,
  0x5cab, 0x0071, 0x5cc1, 0x00bb, 0x00d3, 0x5fe8, 0x6169, 0x615d,
  0x615f, 0x00f3, 0x00ed, 0x00f5, 0x6164, 0x6162, 0x00f1, 0x00f2,
  0x00f6, 0x0120, 0x62f9, 0x0128, 0x62ee, 0x1d18, 0x1d2c, 0x1d59,
  0x63ab, 0x63ac, 0x63aa, 0x1d68, 0x1d7e, 0x6486, 0x647a, 0x1da1,
  0x6728, 0x01a1, 0x1f5b, 0x01aa, 0x01a9, 0x6731, 0x01b5, 0x01a5,
  0x01a8, 0x1f42, 0x01a7, 0x1f47, 0x672d, 0xd143, 0x1f3d, 0x672b,
  0x6732, 0x1f60, 0x21eb, 0x6b29, 0x6b26, 0x6b53,
  /* 0x31 */
  0x024d, 0x6b33, 0x6b34, 0x6c9d, 0x029c, 0x235f, 0x6d41, 0x02ea,
  0x02e5, 0x6e91, 0x6e69, 0x256f, 0x7022, 0x035e, 0x7034, 0xd16d,
  0x712e, 0x0386, 0x0384, 0x71f4, 0x265a, 0x7428, 0x03d9, 0x741c,
  0x03de, 0x7411, 0x7424, 0x7415, 0x03db, 0x7416, 0x7454, 0x7423,
  0x75ff, 0x0431, 0x76cf, 0x76d0, 0x044b, 0x76ce, 0x0449, 0x044a,
  0x044c, 0x77c7, 0x785c, 0x048a, 0x7836, 0x0489, 0x048b, 0x7843,
  0x04bb, 0x29a2, 0x299d, 0x04e4, 0x7b2a, 0x7b01, 0x29a3, 0x7b0b,
  0x7b0f, 0x053b, 0x052e, 0x053e, 0x0546, 0x0553, 0x7cdf, 0x0544,
  0x7cd2, 0x053f, 0x0542, 0x054f, 0x7ccd, 0x0552, 0x054a, 0x2ac2,
  0x7cdb, 0x055a, 0x2aa5, 0x0549, 0x7ccf, 0x2c1c, 0x7ec0, 0x2c1d,
  0x0603, 0x8081, 0x8082, 0x808a, 0x80a8, 0x808c, 0x2d95, 0x2d9a,
  0x0601, 0x0606, 0x05fb, 0x05f9, 0x808e, 0x0605,
  /* 0x32 */
  0x05fa, 0x808b, 0x2da6, 0x8096, 0x05fe, 0x80cc, 0x067d, 0x068c,
  0x834f, 0x834a, 0x2e50, 0x068e, 0x834b, 0x833d, 0x2e52, 0x8344,
  0x8349, 0x849e, 0x84f3, 0x2ec8, 0x84f5, 0x06f0, 0x06f2, 0x85b3,
  0x2f58, 0x06f1, 0x06e6, 0x85e5, 0x85b6, 0xd1d8, 0x3188, 0x8886,
  0x076f, 0x076d, 0x0769, 0x88b6, 0x8885, 0x076e, 0x88ab, 0x082f,
  0x0830, 0x0863, 0x8c0d, 0x8c8b, 0x8c8c, 0x33b8, 0x0880, 0x33b9,
  0x089a, 0x0894, 0x0896, 0x0895, 0x0897, 0x8d72, 0x08f4, 0x08fe,
  0x8f8f, 0x0901, 0x8f79, 0x0902, 0x8f77, 0x08f9, 0x8f90, 0x8f88,
  0x8f80, 0x8f9e, 0x08f6, 0x08f7, 0x8f82, 0x8f34, 0x8f89, 0x08ff,
  0x8f85, 0x8f7e, 0x8f7a, 0x8fa6, 0x360b, 0x8fb5, 0x91f4, 0x09bf,
  0x09bc, 0x3805, 0x9229, 0x9226, 0x922a, 0x09be, 0x09c0, 0x937e,
  0x0a3b, 0x0a39, 0x945b, 0x9461, 0x9460, 0x0a3c,
  /* 0x33 */
  0x959b, 0x3a14, 0x3a04, 0x95c3, 0x0a7d, 0x95cd, 0x0a7f, 0x0a7a,
  0x0a7c, 0x3a05, 0x0a7e, 0x3a15, 0x3a0d, 0x0a80, 0x0abf, 0x0ac3,
  0x9754, 0x9759, 0x0acc, 0x0b0e, 0x0b0d, 0x98f5, 0x0b26, 0x0b24,
  0x0b25, 0x0b23, 0x0b21, 0x0b29, 0x9a69, 0x9a65, 0x0b8f, 0x0b8d,
  0x9b7c, 0x0b8b, 0x0b92, 0x9b80, 0x0b90, 0x9c65, 0x0bef, 0x0bf0,
  0x9cdf, 0x9d60, 0x0c01, 0x9d5e, 0x0c2e, 0x0c2d, 0x3e2e, 0x0c28,
  0x0c29, 0x0c2c, 0x9e8f, 0x9e61, 0x9e5a, 0x3e41, 0x0c88, 0x9f67,
  0x0c92, 0x0c91, 0x3eea, 0x9fbb, 0x3f44, 0x0ca9, 0x0cac, 0x0cae,
  0x0caa, 0x0d06, 0x0d05, 0x0d4a, 0x0d3e, 0x0d3c, 0x0d3b, 0xa29b,
  0x0d42, 0x0d41, 0x0d43, 0xa2a9, 0x0d45, 0xa366, 0x0d9a, 0x0d9b,
  0x0d9f, 0x0d9e, 0xa472, 0xa476, 0xa514, 0x0dde, 0x0de2, 0x0de6,
  0xa50f, 0x4229, 0x0de3, 0x4227, 0x0ddf, 0xa641,
  /* 0x34 */
  0xa646, 0x439d, 0xa64b, 0xa643, 0x0e7e, 0x0ec9, 0x0ecc, 0xa7c6,
  0x0ed1, 0xa7c7, 0x0ed0, 0xa7ce, 0x0ecf, 0x0ec8, 0xa7c9, 0x0ecd,
  0xa7cb, 0xa7c5, 0x0f49, 0x0f47, 0x0f58, 0x0f6b, 0x0f6c, 0x0f6a,
  0xaa69, 0x0f80, 0x0f82, 0x0f84, 0x45c8, 0x0f83, 0xab1e, 0xabba,
  0x0fb6, 0xac52, 0xac51, 0xac53, 0xad9f, 0xaddb, 0x4726, 0x0ff1,
  0xade3, 0x0ff6, 0x0ff3, 0x0ff0, 0x471c, 0xadd7, 0xade9, 0x4728,
  0x0ff5, 0x0ff4, 0x0ff7, 0xadde, 0xaddc, 0xb03c, 0xd28b, 0x1055,
  0xb122, 0xb132, 0xb123, 0x108b, 0x10cb, 0x10c2, 0xb2d4, 0xb2c8,
  0xb2bc, 0x10ca, 0x10cc, 0xb2cd, 0x10c7, 0x10c9, 0x4970, 0xb2be,
  0x10c6, 0xb340, 0xb2d6, 0x10c3, 0x4982, 0xb2bd, 0x49ac, 0xb2ba,
  0x10c4, 0xb2c0, 0xd29b, 0xd29d, 0xb2c1, 0xb4a6, 0xb4a5, 0xb4a8,
  0x11a9, 0x4bad, 0x11a8, 0x11a6, 0xb55f, 0xb570,
  /* 0x35 */
  0xb56a, 0xb565, 0xb567, 0xb56f, 0xb587, 0x4bca, 0x4c51, 0xb73d,
  0xb743, 0x1222, 0xb740, 0x1226, 0x1224, 0x1225, 0x4c9d, 0x122a,
  0x1274, 0xb918, 0x1279, 0x4d90, 0x127a, 0xb919, 0xb9c1, 0x4dd8,
  0x4dd7, 0x1297, 0xb9bc, 0xb9c8, 0x4e2e, 0xba71, 0x12bc, 0xba6e,
  0x12b3, 0xba78, 0x12bf, 0x12b7, 0xd2cd, 0xba7a, 0xbbb1, 0xbbaf,
  0xbbb0, 0x1334, 0x1346, 0x1348, 0x4f59, 0xbca8, 0xbca6, 0x1356,
  0x1367, 0xbd48, 0xbd45, 0x1368, 0x1399, 0x139a, 0xbea0, 0xbea4,
  0x13d8, 0xbfd4, 0x13db, 0x13dc, 0x13dd, 0x13d7, 0xbfd2, 0xc022,
  0x1449, 0x144a, 0x50da, 0xc0b7, 0xc0cc, 0x5233, 0xc1e6, 0x5218,
  0x149a, 0xc1c8, 0x14cd, 0x14ca, 0xc3f4, 0x14cb, 0xc3ed, 0x14cf,
  0xc37e, 0xd2e3, 0x14cc, 0x14ea, 0x14ed, 0xc4f9, 0xc4fd, 0x14e9,
  0xc507, 0x151a, 0x53ef, 0x158d, 0x15d2, 0xc778,
  /* 0x36 */
  0xc77a, 0xc779, 0xc88a, 0x15f2, 0xc97b, 0xcad5, 0xcae9, 0x163d,
  0xcaeb, 0x163e, 0x16f8, 0x1723, 0x58d9, 0x18b4, 0x5b6c, 0x1aa0,
  0x1a90, 0x0075, 0x1a86, 0x1a84, 0x5cfa, 0x1a8a, 0x0076, 0x0073,
  0x1a9f, 0x1aa1, 0x5d18, 0x1a93, 0x00bd, 0x5ff6, 0x1bd5, 0x618a,
  0x6189, 0x00f9, 0x617f, 0x6188, 0x00fa, 0x6183, 0x6184, 0x6198,
  0x6163, 0x6187, 0x0121, 0xd127, 0x0129, 0x62f5, 0x6350, 0x0138,
  0x014e, 0x6487, 0x648a, 0x6565, 0x67b7, 0x67c1, 0x67c7, 0x01c8,
  0x01bc, 0x67c5, 0x67cb, 0x1f90, 0x67d1, 0x01bb, 0x01c2, 0x01c0,
  0x67b8, 0x67ca, 0x01ca, 0x67de, 0x01c9, 0x67ce, 0x01b8, 0x2110,
  0x2217, 0x6b68, 0x024e, 0x6b6b, 0x2244, 0x0250, 0x222b, 0x6b6a,
  0x2245, 0x6b66, 0x6b77, 0x6b96, 0x6b6e, 0xd156, 0x028a, 0x6d57,
  0x2365, 0x6d56, 0x6e9c, 0x6e9e, 0x02fc, 0x02f9,
  /* 0x37 */
  0x6ea1, 0x0363, 0x7042, 0x25cf, 0x7046, 0x703e, 0x7133, 0x0387,
  0x0388, 0x71fa, 0x039a, 0x7297, 0x729b, 0x72aa, 0x2756, 0x7473,
  0x747c, 0x03e9, 0x7486, 0x03ea, 0x2754, 0x0450, 0x76f3, 0x76f0,
  0x0456, 0x0452, 0x044f, 0x0454, 0x0451, 0x76ec, 0x78af, 0x048e,
  0x048f, 0x7864, 0x7868, 0x795a, 0x293d, 0x7b1f, 0x7b25, 0x04ed,
  0x04eb, 0x29a4, 0x7cc6, 0x7cd6, 0x7cc3, 0x0562, 0x7d2c, 0x055d,
  0x7d2e, 0x7d5e, 0x7d33, 0x0561, 0x0565, 0x055c, 0x7d2d, 0x7d46,
  0x055f, 0x7cc1, 0x7d3a, 0x7ecc, 0x809d, 0x8083, 0x80f6, 0x2dec,
  0x0616, 0x060a, 0x80f8, 0x060e, 0x0612, 0x80fe, 0x80f3, 0x0611,
  0x80eb, 0x80fa, 0x0610, 0x8107, 0x80fc, 0x0609, 0x2dfa, 0x0615,
  0x2dd4, 0x8372, 0x8373, 0x8374, 0x0691, 0x0695, 0x0693, 0x0692,
  0x068f, 0x835f, 0x8360, 0x84aa, 0x8534, 0x06f6,
  /* 0x38 */
  0x85b4, 0x06fb, 0x85f0, 0x2f75, 0x06f9, 0x860d, 0x85f3, 0x860f,
  0x301c, 0x077d, 0x88c9, 0x077a, 0x077f, 0x88c5, 0x0778, 0x88d7,
  0x88cc, 0x31d9, 0x88e7, 0x0770, 0x0782, 0x88c1, 0x0784, 0x88e8,
  0x0833, 0x8acb, 0x0832, 0x0836, 0x8ac8, 0x8b7a, 0x0856, 0x8b79,
  0x8b7e, 0x0867, 0x8c1b, 0x0865, 0x0864, 0x0866, 0x8c1f, 0x8c19,
  0x0881, 0x0882, 0x0883, 0x089e, 0x8d89, 0x33f1, 0x089d, 0x8d8b,
  0x090f, 0x0912, 0x9009, 0x8ffe, 0x9000, 0x0910, 0x0918, 0x900b,
  0x0914, 0x0919, 0x3637, 0x59fd, 0x904a, 0x367d, 0x3686, 0x09c8,
  0x09c4, 0x09c6, 0x9279, 0x09c7, 0x09c3, 0x926c, 0x9299, 0xd21b,
  0x9262, 0x9314, 0x0a19, 0x0a1c, 0x93c8, 0x93d7, 0x940c, 0x0a41,
  0x9470, 0x0a42, 0x0a43, 0x9471, 0x95df, 0x95e4, 0x0a82, 0x95e7,
  0x0a81, 0x0a94, 0x0a84, 0x9790, 0x0ad2, 0x0b0f,
  /* 0x39 */
  0x0b22, 0x994c, 0x0b27, 0x0b2a, 0x3bfa, 0x0b28, 0x994f, 0x0b60,
  0x9a7d, 0x9a7e, 0x9a7c, 0x3c72, 0x9a8d, 0x9b19, 0x0b9b, 0x0b9c,
  0x9bc0, 0x0b93, 0x0b94, 0x3cdc, 0x0ba0, 0x0b99, 0x9bb6, 0x0ba1,
  0x9ba8, 0x0bf1, 0x9ceb, 0x9ce7, 0x9d6f, 0x9ec7, 0x0c36, 0x9e9f,
  0x0c37, 0x0c3f, 0x0c3c, 0x9ec4, 0x0c34, 0x0c39, 0x0c3b, 0x0c35,
  0x0c30, 0x0c32, 0x0c38, 0x0c3e, 0x0c3a, 0x9e9b, 0x9e97, 0x9ec5,
  0x9f6b, 0x9fce, 0x0cb6, 0x3f67, 0xa0b1, 0xa0ae, 0xa0b0, 0x0cb3,
  0x0d09, 0x4077, 0xa23e, 0xa2b5, 0xa2ba, 0x0d4c, 0xa2b2, 0xa2b4,
  0x0d53, 0x0d4d, 0x0d51, 0x0d4f, 0x419b, 0xa377, 0x0da2, 0xa386,
  0xa37b, 0x0dcd, 0xa47e, 0xa52e, 0x0de7, 0xa52f, 0x0df0, 0xa537,
  0x0de9, 0x0dec, 0xa532, 0x0de8, 0x0dee, 0x0e02, 0xa536, 0xa539,
  0xa535, 0xa65c, 0x0e82, 0x0e83, 0x0e86, 0xa67b,
  /* 0x3a */
  0xa661, 0xa7ee, 0xa7eb, 0xa7ef, 0xa820, 0x442a, 0x4465, 0x0f4a,
  0xa930, 0x0f5a, 0x4564, 0xa9be, 0x0f6e, 0xaa67, 0xaa7c, 0x0f70,
  0x0f6f, 0xab24, 0x0f8a, 0x0f87, 0x0f88, 0xab29, 0xabef, 0x4620,
  0x0fb7, 0x0ffd, 0x4720, 0xae0d, 0x1005, 0x473c, 0x1008, 0x1003,
  0x1002, 0x1004, 0x0739, 0x1009, 0x0fff, 0xae64, 0x473f, 0xae15,
  0x0ffc, 0x1001, 0x100a, 0x47f0, 0x47f5, 0x106b, 0x106c, 0xb135,
  0xb136, 0xb134, 0xb137, 0xb347, 0xb32b, 0x10de, 0xb341, 0xb343,
  0x10db, 0xb342, 0x10dd, 0x10e3, 0xb332, 0x10e0, 0x10d9, 0x10d8,
  0x10e4, 0xb344, 0xb34a, 0x10da, 0x10ef, 0xd2a0, 0x4a15, 0x49be,
  0xb354, 0xb36e, 0xb352, 0x10d7, 0x11b3, 0x11bb, 0x4be5, 0x11b2,
  0x4bd2, 0x11ad, 0xb592, 0x11af, 0xd2b8, 0x4be0, 0xb5bf, 0x1216,
  0x1228, 0x1223, 0x4cb3, 0xb741, 0xb769, 0xb765,
  /* 0x3b */
  0x1275, 0x127e, 0x127c, 0xb922, 0xb91d, 0xb9d2, 0xb9da, 0xb9db,
  0x12ce, 0x12cd, 0x12cf, 0x4e53, 0xbaa4, 0xba9e, 0x4e37, 0x4e47,
  0x4e5c, 0xba9d, 0x12c4, 0x12cc, 0x12c8, 0x12c7, 0xbaad, 0xbaa6,
  0xbaa7, 0xbbb3, 0xbbe0, 0xbc35, 0xbc37, 0x135a, 0x136a, 0xbd4a,
  0x136b, 0x136d, 0x136f, 0xbe5e, 0x139e, 0xbec0, 0x13a4, 0x13a3,
  0x13e4, 0x13e8, 0x13e9, 0x13e0, 0x13e3, 0xbff9, 0x13ea, 0x13e1,
  0x13ed, 0x1434, 0x1435, 0x1451, 0x50f0, 0xc1f0, 0x149d, 0xc1f3,
  0xc21b, 0xc1f2, 0xc1fb, 0xc41c, 0xc413, 0x14d0, 0xc40f, 0x14ee,
  0xc516, 0xc511, 0xc512, 0x14f2, 0xc50e, 0x541d, 0x1588, 0xc667,
  0xc6f2, 0xc6da, 0x158f, 0xc6dc, 0x15d8, 0xc894, 0xc89b, 0xc892,
  0xc89a, 0xc988, 0xc986, 0x163f, 0xcaef, 0x1652, 0x576b, 0xcb5e,
  0x1650, 0xcc58, 0x16b1, 0xcc56, 0xcc54, 0x16f9,
  /* 0x3c */
  0xcd9b, 0xce96, 0xcea4, 0x1726, 0x1728, 0xce9a, 0xcf12, 0x0080,
  0x1ac0, 0x0081, 0x5d6b, 0x007e, 0x007f, 0x5d37, 0x5d3c, 0xd10a,
  0x5ef7, 0x00be, 0x5f66, 0x00d6, 0x61a9, 0x61ae, 0x61ad, 0x61c8,
  0x61a5, 0x61b0, 0x6295, 0x1ce5, 0x6325, 0x0134, 0x6499, 0x1daf,
  0x6574, 0x6570, 0x656f, 0x6841, 0x6854, 0x01d5, 0x01d8, 0x6840,
  0x6838, 0x01d4, 0x1fd8, 0x01d9, 0x6852, 0x683a, 0x6857, 0xd14a,
  0x6859, 0x2111, 0x2267, 0x6bb4, 0x6bc0, 0x025d, 0x2243, 0x025e,
  0x0259, 0x6b75, 0x025a, 0x02a0, 0x6d60, 0x6d47, 0x0305, 0x6ef0,
  0x0307, 0x6eef, 0x030e, 0x030c, 0x6eec, 0x6f83, 0x0345, 0x6f84,
  0x6f8f, 0x0364, 0x7061, 0x0365, 0x7069, 0x25dd, 0x0366, 0x7062,
  0x0389, 0x03ec, 0x03f1, 0x2770, 0x276a, 0x03f0, 0x03f8, 0x2774,
  0x275f, 0x74ae, 0x2761, 0x2773, 0x74b2, 0x03f2,
  /* 0x3d */
  0x03f4, 0x770b, 0x0458, 0x7710, 0x770d, 0x045a, 0x0459, 0x0457,
  0x045b, 0x2850, 0x787f, 0x7881, 0x04a6, 0x04c2, 0x04c1, 0x293f,
  0x7a0b, 0x7b4e, 0x04ef, 0x29b0, 0x7dea, 0x7d45, 0x0568, 0x2b35,
  0x2b2d, 0x0573, 0x056e, 0x0574, 0x2b02, 0x0566, 0x7d28, 0x7d5d,
  0x7edc, 0x05b9, 0x2c26, 0x7ed4, 0x060c, 0x8164, 0x8168, 0x0620,
  0x8162, 0x061d, 0x8161, 0x061c, 0x8166, 0x0621, 0x061a, 0x0619,
  0x80f2, 0x8169, 0x8167, 0x067e, 0x839a, 0x839b, 0x8385, 0x839c,
  0x069d, 0x83a4, 0x069e, 0x069c, 0x2e6e, 0x8399, 0x8386, 0x8390,
  0x8481, 0x84ae, 0x2eb1, 0x2ed4, 0x06cd, 0x8538, 0x070b, 0x070a,
  0x2f85, 0x893b, 0x078f, 0x3272, 0x0795, 0x0790, 0x0791, 0x894c,
  0x323a, 0x07b9, 0x8947, 0x8935, 0x0797, 0x079e, 0x8933, 0x078b,
  0x8982, 0x8940, 0x083b, 0x083a, 0x083c, 0x083d,
  /* 0x3e */
  0x0839, 0x083e, 0x086b, 0x086c, 0x8c36, 0x8d0e, 0x08a2, 0x08a1,
  0x089f, 0x8dad, 0x8daa, 0x9017, 0x092d, 0x9067, 0x0936, 0x092b,
  0x9072, 0x0937, 0xd20a, 0x9061, 0x90b0, 0x36ad, 0x0925, 0x092f,
  0x092c, 0x906e, 0x9064, 0x0932, 0x908c, 0x9066, 0x3695, 0x906b,
  0x905f, 0x9074, 0x9065, 0x92bb, 0x92be, 0x09d5, 0x92b9, 0x09d4,
  0x09d6, 0x92ef, 0x09d1, 0x3943, 0x93da, 0x0a46, 0x398f, 0x9490,
  0x95e9, 0x0a8c, 0x0a8a, 0x0a88, 0x9611, 0x960d, 0x95ed, 0x9621,
  0x0add, 0x9781, 0x97b1, 0x9901, 0x0b2d, 0x995e, 0x9962, 0x0b2e,
  0x0b2c, 0x0b2b, 0x0b30, 0x995b, 0x0b4e, 0x9a96, 0x9a93, 0x0b64,
  0x0b61, 0x9a92, 0x3c75, 0xd239, 0x0b70, 0x0ba6, 0x0ba4, 0x9bc4,
  0x9bc7, 0x9bc3, 0x0ba8, 0x0ba2, 0x9bc8, 0x0ba7, 0x3cec, 0x0ba5,
  0x9bca, 0x0ba9, 0x9bc5, 0x9bcf, 0x9bdc, 0x9c7c,
  /* 0x3f */
  0x9d01, 0x0c3d, 0x9ed3, 0x9edc, 0x0c44, 0x0c45, 0x0c46, 0x9ed4,
  0x3e57, 0x9ecc, 0x0c47, 0x0c48, 0x0c42, 0x9ed6, 0x9edb, 0x0c41,
  0x9ed5, 0x9fd9, 0x0c94, 0x9fdd, 0x9fdc, 0x9fe0, 0xa0cc, 0x0cc0,
  0x0cb8, 0x0cc1, 0x0cc2, 0x0cbb, 0x0cbd, 0x0cbf, 0x0cb9, 0x0cb7,
  0xa0d2, 0x0cc7, 0xa0d3, 0x0d0c, 0x0d0b, 0x407b, 0x0d10, 0xa18d,
  0x0d5d, 0x4121, 0x0d5a, 0x0d58, 0x0d56, 0xa2d8, 0x0d54, 0x4116,
  0xa2bc, 0x0da8, 0x0da7, 0x0dcf, 0x0dd0, 0xa48a, 0x41e8, 0xa48b,
  0xa48d, 0x0dd1, 0x0deb, 0xa553, 0x0dfb, 0x426a, 0xa559, 0x0dfd,
  0x0df8, 0x0df7, 0x0e00, 0xa556, 0xa557, 0x0df6, 0x425f, 0xa673,
  0xa81b, 0x0edf, 0xa821, 0xa816, 0xa818, 0x0ee2, 0x0ee4, 0xa844,
  0x4482, 0xa826, 0x0ee3, 0xa936, 0x0f59, 0x0f71, 0x0f8e, 0x0f8c,
  0xab3a, 0x0fa4, 0xabf4, 0x4655, 0x1014, 0xae62,
};

static const ucs4_t cns11643_4a_2uni_upages[212] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x04e00, 0x04f00, 0x05000, 0x05100, 0x05200, 0x05300, 0x05400, 0x05500,
  0x05600, 0x05700, 0x05800, 0x05900, 0x05a00, 0x05b00, 0x05c00, 0x05d00,
  0x05e00, 0x05f00, 0x06000, 0x06100, 0x06200, 0x06300, 0x06500, 0x06600,
  0x06700, 0x06800, 0x06900, 0x06b00, 0x06c00, 0x06d00, 0x06e00, 0x07000,
  0x07100, 0x07200, 0x07300, 0x07400, 0x07500, 0x07600, 0x07700, 0x07800,
  0x07900, 0x07a00, 0x07b00, 0x07c00, 0x07d00, 0x07f00, 0x08000, 0x08100,
  0x08200, 0x08300, 0x08400, 0x08600, 0x08800, 0x08900, 0x08a00, 0x08c00,
  0x08e00, 0x08f00, 0x09000, 0x09100, 0x09200, 0x09500, 0x09600, 0x09700,
  0x09a00, 0x0ff00, 0x20000, 0x20100, 0x20200, 0x20300, 0x20400, 0x20500,
  0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20c00, 0x20d00,
  0x20e00, 0x21100, 0x21200, 0x21300, 0x21500, 0x21600, 0x21700, 0x21900,
  0x21a00, 0x21b00, 0x21c00, 0x21d00, 0x21e00, 0x21f00, 0x22000, 0x22100,
  0x22200, 0x22300, 0x22400, 0x22500, 0x22600, 0x22700, 0x22900, 0x22a00,
  0x22b00, 0x22c00, 0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200, 0x23300,
  0x23400, 0x23500, 0x23800, 0x23900, 0x23a00, 0x23b00, 0x23c00, 0x23d00,
  0x23e00, 0x24100, 0x24200, 0x24500, 0x24600, 0x24700, 0x24800, 0x24900,
  0x24a00, 0x24b00, 0x24c00, 0x24d00, 0x24f00, 0x25000, 0x25100, 0x25300,
  0x25400, 0x25600, 0x25700, 0x25900, 0x25a00, 0x25b00, 0x25e00, 0x25f00,
  0x26000, 0x26200, 0x26300, 0x26400, 0x26500, 0x26600, 0x26700, 0x26800,
  0x26900, 0x26a00, 0x26b00, 0x26c00, 0x27100, 0x27200, 0x27500, 0x27600,
  0x27700, 0x27800, 0x27900, 0x27b00, 0x27c00, 0x27d00, 0x27e00, 0x27f00,
  0x28200, 0x28400, 0x28500, 0x28600, 0x28700, 0x28800, 0x28c00, 0x28e00,
  0x28f00, 0x29000, 0x29100, 0x29200, 0x29400, 0x29500, 0x29600, 0x29a00,
  0x29d00, 0x2f800, 0x2f900, 0x2fa00,
};

