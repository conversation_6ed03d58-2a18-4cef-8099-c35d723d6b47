/* output by ../../bin/lsgen.perl 0.41 ( 1998 May)
 * made Thu Aug 12 17:02:09 2004
 * user takugoto
 * host Goto
 * file /home/<USER>/xg/puk3/nr2/src/gmsv/./nrproto_serv.c
 * util ./nrproto_util.c , include/nrproto_util.h
 * src  /home/<USER>/xg/puk3/nr2/src/gmsv/../../doc/nrproto.html
 */
#define _NRPROTOSERV_C_
#include "nrproto_serv.h"
#include "ncash.h"
#include "common.h"

#include "net.h"	//为了conect结构可以使用在封包显示语句中
#include "language.h"
#include "version.h"

//0=韩服 1=台服5.5 2=台服7.0
#ifdef	VERSION_TW
#define PACKETVER 2
#else
#define PACKETVER 0
#endif


char PacketRecv[Packet_Ver_ALLNUM][RECV_ALLNUM][20] = {
	{
		"W",
		"w",
		"EV",
		"M",
		"EN",
		"DU",
		"EO",
		"BU",
		"JB",
		"LB",
		"B",
		"ID",
		"IDF",
		"PI",
		"DI",
		"DG",
		"DP",
		"MI",
		"IR",
		"MSG",
		"ALI",
		"ALN",
		"PMSG",
		"DAB",
		"AAB",
		"GI",
		"GT",
		"GMR",
		"BGT",
		"AGM",
		"LG",
		"RGM",
		"GML",
		"PGML",
		"GD",
		"PRV",
		"PRS",
		"PRD",
		"PRE",
		"PRM",
		"PRO",
		"L",
		"TK",
		"FS",
		"HL",
		"PR",
		"KS",
		"MP",
		"GFL",
		"GPD",
		"GFLI",
		"GPDI",
		"IH",
		"AC",
		"ACS",
		"MU",
		"TU",
		"TRPL",
		"TRS",
		"TROP",
		"TRCL",
		"TROC",
		"PS",
		"ST",
		"DT",
		"FT",
		"LVUP",
		"PLVUP",
		"SKSW",
		"PSSW",
		"POS",
		"KN",
		"WN",
		"SP",
		"ClientLogin",
		"CreateNewChar",
		"CharDelete",
		"CharLogin",
		"CharList",
		"CharLogout",
		"ProcGet",
		"PlayerNumGet",
		"Echo",
		"Shutdown",
		"FC",
		"CH",
		"CharLoginGate",
		"PVUP",
		"StallStart",//台服5.5 摆摊相关的4个命令
		"StallEnd",
		"StallBrowse",
		"StallBuy",
		"ProduceTime",//台服5.5 比韩服多的两个命令
		"CheckCommand",
		"rh",//台服7.1 出现的多页银行相关
        "Cpa", // 96 Cpa 0 9 2 1 -1 移动装备到宠物栏
        "SWITM",
        "SWITMP",
        "SWITMM",
	},
	{
		"fhQB",
		"sz",
		"Bj",
		"Kp",
		"Dc",
		"GN",
		"OWp",
		"NgMy",
		"cEj",
		"YK",
		"ZWdB",
		"pNG",
		"rmU",
		"xeDh",
		"Or",
		"HEVr",
		"WQc",
		"aet",
		"WsTP",
		"VUhO",
		"Cp",
		"LaR",
		"EEk",
		"LH",
		"OAM",
		"Mk",
		"LzMi",
		"CZ",
		"QFLo",
		"tMr",
		"XIsR",
		"SZJO",
		"uaE",
		"Uab",
		"nkda",
		"dSc",
		"DD",
		"yUe",
		"rN",
		"Gu",
		"dDT",
		"nB",
		"TC",
		"ytkA",
		"FVm",
		"yBsn",
		"naXP",
		"Vco",
		"WwJ",
		"QMH",
		"Bt",
		"Dhme",
		"CCou",
		"lru",
		"EI",
		"RXr",
		"jmOh",
		"yt",
		"upU",
		"OiJ",
		"bLVH",
		"Raht",
		"Flvp",
		"Rxm",
		"bk",
		"YBbl",
		"BOUo",
		"Datm",
		"XkeK",
		"Fe",
		"ZX",
		"wv",
		"dnu",
		"JlXw",
		"Gbjq",
		"cJ",
		"ZV",
		"MP",
		"euw",
		"olJ",
		"yp",
		"rvb",
		"Ao",
		"IcE",
		"FJfT",
		"tM",
		"Hh",
		"gcS",
		"pkAe",
		"pkAf",
		"pkAg",
		"pkAh",
		"uHWw",//台服5.5 比韩服多的两个命令 开始制作时发送的时间包
		"CCL",
        "rh",//台服7.1 出现的多页银行相关
        "Cpa", // 96 Cpa 0 9 2 1 -1 移动装备到宠物栏
        "SWITM",
        "SWITMP",
        "SWITMM",
	},
    {
        "zA",    //1
        "sz",    //2
        "pg",    //3
        "UUN",    //4
        "KeOX",    //5
        "ik",    //6
        "eL",    //7
        "NgMy",    //8
        "cEj",    //9
        "IMv",    //10
        "Rg",    //11
        "Ak",    //12
        "iVfo",    //13
        "fIM",    //14
        "QpfE",    //15
        "woz",    //16
        "FvWm",    //17
        "yi",    //18
        "fS",    //19
        "CV",    //20
        "kza",    //21
        "LaR",    //22
        "rMsv",    //23
        "FPkR",    //24
        "vcAz",    //25
        "Ufm",    //26
        "GY",    //27
        "CZ",    //28
        "uHb",    //29
        "npzi",    //30
        "XIsR",    //31
        "dbQC",    //32
        "uId",    //33
        "PfqV",    //34
        "nkda",    //35
        "HoW",    //36
        "Ot",    //37
        "TLKH",    //38
        "wD",    //39
        "GH",    //40
        "lz",    //41
        "nB",    //42
        "uSr",    //43
        "dcMv",    //44
        "FVm",    //45
        "zn",    //46
        "LKQy",    //47
        "Ukuy",    //48
        "iET",    //49
        "McVa",    //50
        "as",    //51
        "mjCv",    //52
        "BG",    //53
        "MIS",    //54
        "klF",    //55
        "RXr",    //56
        "sM",    //57
        "ixH",    //58
        "QeuC",    //59
        "aiAb",    //60
        "suI",    //61
        "OU",    //62
        "Flvp",    //63
        "zGp",    //64
        "tj",    //65
        "ACwB",    //66
        "IHw",    //67
        "kjSK",    //68
        "Noeb",    //69
        "HJQp",    //70
        "ih",    //71
        "ycX",    //72
        "xD",    //73
        "cZt",    //74
        "JFVf",    //75
        "aSEl",    //76
        "Godf",    //77
        "emRV",    //78
        "Gp",    //79
        "WA",    //80
        "yp",    //81
        "rvb",    //82
        "Or",    //83
        "IcE",    //84
        "DE",    //85
        "PGXE",    //86
        "lO",    //87
        "Xoee",    //88
        "ylGN",    //89
        "JMh",    //90
        "ElVN",    //91
        "wAo",    //92
        "IPy",  //开始制作时发送的时间	//93
        "CCL",    //94
        "rh",//台服7.1 出现的多页银行相关	//95
        "Cpa", // 96 Cpa 0 9 2 1 -1 移动装备到宠物栏
        "SWITM",
        "SWITMP",
        "SWITMM",
    }

};



char PacketSend[Packet_Ver_ALLNUM][SEND_ALLNUM][20] = {
	{
		"XYD",
		"MC",
		"M",
		"EV",
		"EP",
		"EN",
		"RS",
		"RD",
		"B",
		"IA",
		"I",
		"LI",
		"SI",
		"IR",
		"BT",
		"MSG",
		"AL",
		"ALI",
		"ALN",
		"ALO",
		"PME",
		"AB",
		"ABG",
		"ABI",
		"GC",
		"GI",
		"GT",
		"GM",
		"GMI",
		"RGM",
		"GML",
		"GD",
		"PRV",
		"PRL",
		"PRA",
		"PRD",
		"PRE",
		"PRM",
		"PRW",
		"PRAD",
		"TK",
		"STK",
		"CP",
		"CP2",
		"KP",
		"KP2",
		"PP",
		"C",
		"CN",
		"TITLE",
		"CA",
		"CD",
		"CJ",
		"CS",
		"CT",
		"PT",
		"S",
		"FS",
		"HL",
		"PR",
		"GFL",
		"GPD",
		"GFLI",
		"GPDI",
		"TU",
		"TRPL",
		"TRS",
		"TROP",
		"TRLI",
		"TRLG",
		"TRLP",
		"TRLPS",
		"TRCL",
		"TROC",
		"PS",
		"LVUP",
		"PLVUP",
		"POS",
		"WN",
		"EF",
		"SE",
		"BGMW",
		"PC",
		"SH",
		"PLAYSE",
		"ES",
		"MN",
		"CC",
		"ClientLogin",
		"CreateNewChar",
		"CharDelete",
		"CharLogin",
		"CharList",
		"CharLogout",
		"ProcGet",
		"PlayerNumGet",
		"Echo",
		"IP",
		"PV",
		"PVUP",
		"MAC",
		"Expire",
		"StallStart",
		"StallEnd",
		"StallBrowse",
		"StallBuy"
	},
	{
		"uL",
		"uRhV",
		"Uy",
		"XM",
		"Mu",
		"pDe",
		"dq",
		"LdwJ",
		"joLh",
		"PY",
		"CgNB",
		"RQK",
		"vs",
		"vIkw",
		"oFxZ",
		"me",
		"nJan",
		"Ryfj",
		"Ftps",
		"IY",
		"sO",
		"xisQ",
		"QZD",
		"ho",
		"GC",
		"iV",
		"sYRu",
		"tN",
		"Wt",
		"Nlm",
		"tLTB",
		"zxMt",
		"xgBR",
		"cqg",
		"ZE",
		"oSEp",
		"ec",
		"GDBg",
		"Tbo",
		"xrNE",
		"ijeb",
		"OEu",
		"yTeO",
		"xwd",
		"keoj",
		"YNK",
		"UW",
		"Ak",
		"boUU",
		"nL",
		"AF",
		"Ic",
		"TAj",
		"qkd",
		"Lcp",
		"Orrz",
		"OuX",
		"FQ",
		"MwCY",
		"npgT",
		"QQmo",
		"beHf",
		"YPV",
		"CwyS",
		"Fx",
		"txV",
		"tNjQ",
		"QPfM",
		"Xy",
		"nkb",
		"yUet",
		"RG",
		"sTT",
		"Tglt",
		"PlFa",
		"dy",
		"XtQZ",
		"yP",
		"ONoX",
		"nTbg",
		"bJn",
		"MD",
		"SVB",
		"Hak",
		"eTN",
		"hgGk",
		"MekK",
		"it",
		"jY",
		"CMPt",
		"VH",
		"SUxh",
		"tDg",
		"weZ",
		"ehR",
		"fhfx",
		"jE",
		"nPP",
		"SQzZ",
		"gjE",
		"hf",
		"Expire",
		"pkAa",
		"pkAb",
		"pkAc",
		"pkAd"
	},
	{
        "QBt",	//1
        "jmb",	//2
        "aBS",	//3
        "EZ",	//4
        "OaHi",	//5
        "dAgg",	//6
        "Gn",	//7
        "hB",	//8
        "LYIm",	//9
        "ELoy",	//10
        "rU",	//11
        "PudO",	//12
        "vWpn",	//13
        "xmgQ",	//14
        "MTg",	//15
        "Mhbt",	//16
        "Ilz",	//17
        "dM",	//18
        "Dn",	//19
        "IYo",	//20
        "lDuJ",	//21
        "gnxd",	//22
        "PhdQ",	//23
        "vI",	//24
        "GC",	//25
        "aDn",	//26
        "znv",	//27
        "yuD",	//28
        "Kka",	//29
        "oRut",	//30
        "Rll",	//31
        "frQp",	//32
        "HNP",	//33
        "eZT",	//34
        "IUn",	//35
        "DpSY",	//36
        "cNB",	//37
        "ow",	//38
        "DyAG",	//39
        "KyN",	//40
        "zdr",	//41
        "Zd",	//42
        "aPc",	//43
        "UU",	//44
        "IsSy",	//45
        "AU",	//46
        "TNT",	//47
        "nsY",	//48
        "JJQj",	//49
        "Sc",	//50
        "NI",	//51
        "uSB",	//52
        "jv",	//53
        "cNE",	//54
        "Rspn",	//55
        "OBee",	//56
        "Yo",	//57
        "SkBD",	//58
        "sZWu",	//59
        "bNY",	//60
        "ysVc",	//61
        "ND",	//62
        "LY",	//63
        "nFV",	//64
        "JgO",	//65
        "Cxo",	//66
        "ms",	//67
        "NdM",	//68
        "nUbJ",	//69
        "hJq",	//70
        "RwK",	//71
        "Yq",	//72
        "xEg",	//73
        "We",	//74
        "BHe",	//75
        "kg",	//76
        "sA",	//77
        "Ov",	//78
        "BZ",	//79
        "zzd",	//80
        "TFCu",	//81
        "wiB",	//82
        "bJg",	//83
        "PVfz",	//84
        "murD",	//85
        "ftaN",	//86
        "TeD",	//87
        "GmNM",	//88
        "whV",	//89
        "DZ",	//90
        "oy",	//91
        "hPUd",	//92
        "Daj",	//93
        "HMNy",	//94
        "cJzI",	//95
        "Bi",	//96
        "LOh",	//97
        "lSZ",	//98
        "PfM",	//99
        "ed",	//100
        "tq",	//101
        "Expire",	//102
        "Tmrx",	//103
        "KXhH",	//104
        "ft",	//105
        "Yx",	//106
        "iKe",	//107  宠物装备， iKe 宠物序号 正常物品封包
        "kiG",	//108 交换宠物装备 kiG 宠物编号(1-5) 物品位置(1-28) 宠物编号(1-5) 物品位置(1-28)
        "TgX", //109 套装说明 TgX xxxName,skillName,flg(1/0),物品数值x24
    }

};

int nrproto_ServerDispatchMessage( int fd , char *encoded )
{
	unsigned int msgid;
	char funcname[1024];

	// 记录接收的封包
	log_packet(fd, "recv", encoded);

#ifdef _DEBUG
    print("\n[C]:%s: %s",Connect[fd].cdkey,encoded);
#endif
	nrproto_splitString( encoded );
	nrproto_GetMessageInfo((int*)&msgid , funcname ,sizeof(funcname), nrproto.token_list );

    //print("fd %d %s %s\n", fd, funcname, encoded);


    if (HookDispatchFn(fd, funcname)) {
        return 0;
    }

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_W] ) == 0 ){
		int x;
		int y;
		char* direction;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		direction = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
#ifdef VERSION_TW
		int warpnum = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_W_recv( fd,x,y,direction,warpnum);
#else
		nrproto_W_recv( fd,x,y,direction);
#endif
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_w] ) == 0 ){
		int x;
		int y;
		char* direction;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		direction = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_w_recv( fd,x,y,direction);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_EV] ) == 0 ){
		int event;
		int seqno;
		int x;
		int y;
		int dir;
		event = nrproto_demkstr_int( nrproto.token_list[1] );
		seqno = nrproto_demkstr_int( nrproto.token_list[2] );
		x = nrproto_demkstr_int( nrproto.token_list[3] );
		y = nrproto_demkstr_int( nrproto.token_list[4] );
		dir = nrproto_demkstr_int( nrproto.token_list[5] );
		nrproto_EV_recv( fd,event,seqno,x,y,dir);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_M] ) == 0 ){
		int id;
		int fl;
		int x1;
		int y1;
		int x2;
		int y2;
		id = nrproto_demkstr_int( nrproto.token_list[1] );
		fl = nrproto_demkstr_int( nrproto.token_list[2] );
		x1 = nrproto_demkstr_int( nrproto.token_list[3] );
		y1 = nrproto_demkstr_int( nrproto.token_list[4] );
		x2 = nrproto_demkstr_int( nrproto.token_list[5] );
		y2 = nrproto_demkstr_int( nrproto.token_list[6] );
		nrproto_M_recv( fd,id,fl,x1,y1,x2,y2);
		return 0;
	}

	/*台服遇敌修改到服务端判断 所以取消由客户端发来的遇敌

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_EN] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_EN_recv( fd,x,y);
		return 0;
	}

	Add By Zack */

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DU] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_DU_recv( fd,x,y);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_EO] ) == 0 ){
		int dummy;
		dummy = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_EO_recv( fd,dummy);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_BU] ) == 0 ){
		int dummy;
		dummy = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_BU_recv( fd,dummy);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_JB] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_JB_recv( fd,x,y);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_LB] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_LB_recv( fd,x,y);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_B] ) == 0 ){
		char* command;
		command = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_B_recv( fd,command);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ID] ) == 0 ){
		int x;
		int y;
		int haveitemindex;
		int toindex;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		haveitemindex = nrproto_demkstr_int( nrproto.token_list[3] );
		toindex = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_ID_recv( fd,x,y,haveitemindex,toindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_IDF] ) == 0 ){
		int x;
		int y;
		int haveitemindex;
		int toindex;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		haveitemindex = nrproto_demkstr_int( nrproto.token_list[3] );
		toindex = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_IDF_recv( fd,x,y,haveitemindex,toindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PI] ) == 0 ){
		int x;
		int y;
		int dir;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		dir = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_PI_recv( fd,x,y,dir);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DI] ) == 0 ){
		int x;
		int y;
		int from;
		int itemindex;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
        from = nrproto_demkstr_int( nrproto.token_list[3] );
		//为了兼容台服7.0新增的一个参数0 如果所丢的道具为0-5 或者其他一个条件在
#ifdef	VERSION_TW
		itemindex = nrproto_demkstr_int( nrproto.token_list[4] );
#else
		itemindex = nrproto_demkstr_int( nrproto.token_list[3] );
#endif
		//Add By Zack
        nrproto_DI_recv(fd, x, y, from, itemindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DG] ) == 0 ){
		int x;
		int y;
		int amount;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		amount = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_DG_recv( fd,x,y,amount);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DP] ) == 0 ){
		int x;
		int y;
		int petindex;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		petindex = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_DP_recv( fd,x,y,petindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_MI] ) == 0 ){
		int fromindex;
		int toindex;
		int num;
		fromindex = nrproto_demkstr_int( nrproto.token_list[1] );
		toindex = nrproto_demkstr_int( nrproto.token_list[2] );
		num = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_MI_recv( fd,fromindex,toindex,num);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_IR] ) == 0 ){
		int haveskillindex;
		haveskillindex = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_IR_recv( fd,haveskillindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_MSG] ) == 0 ){
		int index;
		char* message;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_MSG_recv( fd,index,message,color);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ALI] ) == 0 ){
		int albumid;
		albumid = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_ALI_recv( fd,albumid);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ALN] ) == 0 ){
		int albumid;
		albumid = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_ALN_recv( fd,albumid);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PMSG] ) == 0 ){
		int index;
		int petindex;
		int itemindex;
		char* message;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		petindex = nrproto_demkstr_int( nrproto.token_list[2] );
		itemindex = nrproto_demkstr_int( nrproto.token_list[3] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		color = nrproto_demkstr_int( nrproto.token_list[5] );
		nrproto_PMSG_recv( fd,index,petindex,itemindex,message,color);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DAB] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_DAB_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_AAB] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_AAB_recv( fd,x,y);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GI] ) == 0 ){
		int index;
		char* data;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_GI_recv( fd,index,data);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GT] ) == 0 ){
		int index;
		int bitflag;
		char* data;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		bitflag = nrproto_demkstr_int( nrproto.token_list[2] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_GT_recv( fd,index,bitflag,data);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GMR] ) == 0 ){
		int time;
		time = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GMR_recv( fd,time);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_BGT] ) == 0 ){
		int index;
		int titleID;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		titleID = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_BGT_recv( fd,index,titleID);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_AGM] ) == 0 ){
		int x;
		int y;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_AGM_recv( fd,x,y);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_LG] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_LG_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_RGM] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_RGM_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GML] ) == 0 ){
		int index;
		char* message;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		color = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_GML_recv( fd,index,message,color);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PGML] ) == 0 ){
		int index;
		int petindex;
		int itemindex;
		char* message;
		int color;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		petindex = nrproto_demkstr_int( nrproto.token_list[2] );
		itemindex = nrproto_demkstr_int( nrproto.token_list[3] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		color = nrproto_demkstr_int( nrproto.token_list[5] );
		nrproto_PGML_recv( fd,index,petindex,itemindex,message,color);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GD] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GD_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRV] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_PRV_recv( fd,flg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRS] ) == 0 ){
		int sid;
		char* smsg;
		int bid;
		char* bmsg;
		int aid;
		char* amsg;
		char* pmsg;
		sid = nrproto_demkstr_int( nrproto.token_list[1] );
		smsg = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		bid = nrproto_demkstr_int( nrproto.token_list[3] );
		bmsg = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		aid = nrproto_demkstr_int( nrproto.token_list[5] );
		amsg = nrproto_wrapStringAddr( nrproto_stringwrapper[6] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[6] ));
		pmsg = nrproto_wrapStringAddr( nrproto_stringwrapper[7] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[7] ));
		nrproto_PRS_recv( fd,sid,smsg,bid,bmsg,aid,amsg,pmsg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRD] ) == 0 ){
		int flg;
		int index;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		index = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PRD_recv( fd,flg,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRE] ) == 0 ){
		int index;
		char* name;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		name = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_PRE_recv( fd,index,name);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRM] ) == 0 ){
		char* data;
		int color;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		color = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PRM_recv( fd,data,color);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PRO] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_PRO_recv( fd,flg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_L] ) == 0 ){
		int dir;
		dir = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_L_recv( fd,dir);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TK] ) == 0 ){
		int x;
		int y;
		char* message;
		int color;
		int area;
		int fontsize;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		message = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		color = nrproto_demkstr_int( nrproto.token_list[4] );
		area = nrproto_demkstr_int( nrproto.token_list[5] );
		fontsize = nrproto_demkstr_int( nrproto.token_list[6] );
		nrproto_TK_recv( fd,x,y,message,color,area,fontsize);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_FS] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_FS_recv( fd,flg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_HL] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_HL_recv( fd,flg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PR] ) == 0 ){
		int x;
		int y;
		int request;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		request = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_PR_recv( fd,x,y,request);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_KS] ) == 0 ){
		int pet1;
		int pet2;
		int pet3;
		int pet4;
		int pet5;
		pet1 = nrproto_demkstr_int( nrproto.token_list[1] );
		pet2 = nrproto_demkstr_int( nrproto.token_list[2] );
		pet3 = nrproto_demkstr_int( nrproto.token_list[3] );
		pet4 = nrproto_demkstr_int( nrproto.token_list[4] );
		pet5 = nrproto_demkstr_int( nrproto.token_list[5] );
		nrproto_KS_recv( fd,pet1,pet2,pet3,pet4,pet5);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_MP] ) == 0 ){
		int fromindex;
		int toindex;
		fromindex = nrproto_demkstr_int( nrproto.token_list[1] );
		toindex = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_MP_recv( fd,fromindex,toindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GFL] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GFL_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GPD] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GPD_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GFLI] ) == 0 ){

		nrproto_GFLI_recv( fd);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_GPDI] ) == 0 ){
		int index;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_GPDI_recv( fd,index);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_IH] ) == 0 ){
		int index;
		int hankoindex;
		index = nrproto_demkstr_int( nrproto.token_list[1] );
		hankoindex = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_IH_recv( fd,index,hankoindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_AC] ) == 0 ){
		int x;
		int y;
		int actionno;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		actionno = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_AC_recv( fd,x,y,actionno);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ACS] ) == 0 ){
		int x;
		int y;
		int skillno;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		skillno = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_ACS_recv( fd,x,y,skillno);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_MU] ) == 0 ){
		int x;
		int y;
		int array;
		int toindex;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		array = nrproto_demkstr_int( nrproto.token_list[3] );
		toindex = nrproto_demkstr_int( nrproto.token_list[4] );
		nrproto_MU_recv( fd,x,y,array,toindex);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TU] ) == 0 ){
		int haveskillindex;
		int havetechindex;
		int toindex;
		char* data;
		haveskillindex = nrproto_demkstr_int( nrproto.token_list[1] );
		havetechindex = nrproto_demkstr_int( nrproto.token_list[2] );
		toindex = nrproto_demkstr_int( nrproto.token_list[3] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		nrproto_TU_recv( fd,haveskillindex,havetechindex,toindex,data);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TRPL] ) == 0 ){

		nrproto_TRPL_recv( fd);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TRS] ) == 0 ){
		int playernum;
		playernum = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_TRS_recv( fd,playernum);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TROP] ) == 0 ){
		char* items;
		char* pets;
		int gold;
		items = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		pets = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		gold = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_TROP_recv( fd,items,pets,gold);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TRCL] ) == 0 ){
#ifdef	VERSION_TW	//台服中撤回交易会直接交易终止
		nrproto_TROC_recv( fd,0);
#else
		nrproto_TRCL_recv( fd);
#endif
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_TROC] ) == 0 ){
		int flg;
		flg = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_TROC_recv( fd,flg);
		return 0;
	}

	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PS] ) == 0 ){
		int havepetindex;
		int havepetskill;
		int toindex;
		char* data;
		havepetindex = nrproto_demkstr_int( nrproto.token_list[1] );
		havepetskill = nrproto_demkstr_int( nrproto.token_list[2] );
		toindex = nrproto_demkstr_int( nrproto.token_list[3] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));
		nrproto_PS_recv( fd,havepetindex,havepetskill,toindex,data);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ST] ) == 0 ){
		int titleindex;
		titleindex = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_ST_recv( fd,titleindex);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_DT] ) == 0 ){
		int titleindex;
		titleindex = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_DT_recv( fd,titleindex);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_FT] ) == 0 ){
		char* data;
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_FT_recv( fd,data);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_LVUP] ) == 0 ){
		int param;
		param = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_LVUP_recv( fd,param);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PLVUP] ) == 0 ){
		int pethave;
		int param;
		pethave = nrproto_demkstr_int( nrproto.token_list[1] );
		param = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_PLVUP_recv( fd,pethave,param);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_SKSW] ) == 0 ){
		int srcindex;
		int dstindex;
		srcindex = nrproto_demkstr_int( nrproto.token_list[1] );
		dstindex = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_SKSW_recv( fd,srcindex,dstindex);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PSSW] ) == 0 ){
		int havepetindex;
		int srcindex;
		int dstindex;
		havepetindex = nrproto_demkstr_int( nrproto.token_list[1] );
		srcindex = nrproto_demkstr_int( nrproto.token_list[2] );
		dstindex = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_PSSW_recv( fd,havepetindex,srcindex,dstindex);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_POS] ) == 0 ){

		nrproto_POS_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_KN] ) == 0 ){
		int havepetindex;
		char* data;
		havepetindex = nrproto_demkstr_int( nrproto.token_list[1] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_KN_recv( fd,havepetindex,data);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_WN] ) == 0 ){
		int x;
		int y;
		int seqno;
		int objindex;
		int select;
		char* data;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		seqno = nrproto_demkstr_int( nrproto.token_list[3] );
		objindex = nrproto_demkstr_int( nrproto.token_list[4] );
		select = nrproto_demkstr_int( nrproto.token_list[5] );
		data = nrproto_wrapStringAddr( nrproto_stringwrapper[6] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[6] ));
		nrproto_WN_recv( fd,x,y,seqno,objindex,select,data);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_SP] ) == 0 ){
		int x;
		int y;
		int dir;
		x = nrproto_demkstr_int( nrproto.token_list[1] );
		y = nrproto_demkstr_int( nrproto.token_list[2] );
		dir = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_SP_recv( fd,x,y,dir);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ClientLogin] ) == 0 ){
		char* acountid;
		char* passwd;
		char* cdkey;
		char* client_check_code;

		acountid = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		passwd = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		cdkey = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		client_check_code = nrproto_wrapStringAddr( nrproto_stringwrapper[4] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[4] ));

		nrproto_ClientLogin_recv( fd,acountid,passwd,cdkey,client_check_code);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CreateNewChar] ) == 0 ){
		int dataplacenum;
		char* charname;
		int imgno;
		int faceimgno;
		int vital;
		int str;
		int tgh;
		int quick;
		int magic;
		int earth;
		int water;
		int fire;
		int wind;
		dataplacenum = nrproto_demkstr_int( nrproto.token_list[1] );
		charname = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		imgno = nrproto_demkstr_int( nrproto.token_list[3] );
		faceimgno = nrproto_demkstr_int( nrproto.token_list[4] );
		vital = nrproto_demkstr_int( nrproto.token_list[5] );
		str = nrproto_demkstr_int( nrproto.token_list[6] );
		tgh = nrproto_demkstr_int( nrproto.token_list[7] );
		quick = nrproto_demkstr_int( nrproto.token_list[8] );
		magic = nrproto_demkstr_int( nrproto.token_list[9] );
		earth = nrproto_demkstr_int( nrproto.token_list[10] );
		water = nrproto_demkstr_int( nrproto.token_list[11] );
		fire = nrproto_demkstr_int( nrproto.token_list[12] );
		wind = nrproto_demkstr_int( nrproto.token_list[13] );
		nrproto_CreateNewChar_recv( fd,dataplacenum,charname,imgno,faceimgno,vital,str,tgh,quick,magic,earth,water,fire,wind);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CharDelete] ) == 0 ){
		int registnumber;
		registnumber = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_CharDelete_recv( fd,registnumber);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CharLogin] ) == 0 ){
		int registnumber;
		int renewal;
		int face;
		registnumber = nrproto_demkstr_int( nrproto.token_list[1] );
		renewal = nrproto_demkstr_int( nrproto.token_list[2] );
		face = nrproto_demkstr_int( nrproto.token_list[3] );
		nrproto_CharLogin_recv( fd,registnumber,renewal,face);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CharList] ) == 0 ){

		nrproto_CharList_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CharLogout] ) == 0 ){

		nrproto_CharLogout_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_ProcGet] ) == 0 ){

		nrproto_ProcGet_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PlayerNumGet] ) == 0 ){

		nrproto_PlayerNumGet_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_Echo] ) == 0 ){
		char* test;
		test = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		nrproto_Echo_recv( fd,test);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_Shutdown] ) == 0 ){
		char* passwd;
		int min;
		passwd = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		min = nrproto_demkstr_int( nrproto.token_list[2] );
		nrproto_Shutdown_recv( fd,passwd,min);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_FC] ) == 0 ){

		nrproto_FC_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CH] ) == 0 ){
		int num;
		num = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_CH_recv( fd,num);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_CharLoginGate] ) == 0 ){

		nrproto_CharLoginGate_recv( fd);
		return 0;
	}


	if( strcmp( funcname , PacketRecv[PACKETVER][RECV_PVUP] ) == 0 ){
		int kind;
		char* productkey;
		kind = nrproto_demkstr_int( nrproto.token_list[1] );
		productkey = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		nrproto_PVUP_recv( fd,kind,productkey);
		return 0;
	}

	if( strcmp( funcname,PacketRecv[PACKETVER][RECV_StallStart] ) ==0){
		char *stallname = nrproto_wrapStringAddr( nrproto_stringwrapper[1] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[1] ));
		char *namedesc = nrproto_wrapStringAddr( nrproto_stringwrapper[2] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[2] ));
		char *price = nrproto_wrapStringAddr( nrproto_stringwrapper[3] , nrproto.workbufsize , nrproto_demkstr_string( nrproto.token_list[3] ));
		nrproto_StallStart_recv(fd,stallname,namedesc,price);
		return 0;
	}

	if( strcmp( funcname, PacketRecv[PACKETVER][RECV_StallEnd] ) ==0){
		nrproto_StallEnd_recv(fd);
		return 0;
	}

	if( strcmp( funcname, PacketRecv[PACKETVER][RECV_StallBrowse] ) ==0){
		int index = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_StallBrowse_recv(fd,index);
		return 0;
	}

	if( strcmp( funcname, PacketRecv[PACKETVER][RECV_StallBuy] ) ==0){
		int position = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_StallBuy_recv(fd,position);
		return 0;
	}

#ifdef ANIT_CHEATING_SET
#ifdef VERSION_TW	//这是台服才有的函数 客户端有对应的函数
	if( strcmp( funcname, PacketRecv[PACKETVER][RECV_ProduceTime] ) ==0){
		int time = nrproto_demkstr_int( nrproto.token_list[1] );
		nrproto_ProduceTime_recv(fd,time);
		return 0;
	}
#endif //VERSION_TW
#endif //ANIT_CHEATING_SET

    if (strcmp(funcname, PacketRecv[PACKETVER][RECV_BankPage]) == 0) {
        int page = nrproto_demkstr_int(nrproto.token_list[1]);
        nrproto_BankPage_recv(fd, page);
        return 0;
    }
    if (strcmp(funcname, PacketRecv[PACKETVER][RECV_BagPage]) == 0) {
        int page = nrproto_demkstr_int(nrproto.token_list[1]);
        nrproto_BagPage_recv(fd, page);
        return 0;
    }
    if (strcmp(funcname, PacketRecv[PACKETVER][RECV_BagPageMove]) == 0) {
        int slot = nrproto_demkstr_int(nrproto.token_list[1]);
        int page = nrproto_demkstr_int(nrproto.token_list[2]);
        int toSlot = nrproto_demkstr_int(nrproto.token_list[3]);
        nrproto_BagPageMove_recv(fd, slot, page, toSlot);
        return 0;
    }
    if (strcmp(funcname, PacketRecv[PACKETVER][RECV_BagPageMgr]) == 0) {
        nrproto_BagPageMgr_recv(fd);
        return 0;
    }
#ifdef PET_EQUIPITEM
    //Cpa 0 9 2 1 -1 移动装备到宠物栏
    if (strcmp(funcname, PacketRecv[PACKETVER][RECV_Cpa]) == 0) {
        int petSlotForm = nrproto_demkstr_int(nrproto.token_list[1]);
        int petItemSlotFrom = nrproto_demkstr_int(nrproto.token_list[2]);
        int petSlotTo = nrproto_demkstr_int(nrproto.token_list[3]);
        int petItemSlotTo = nrproto_demkstr_int(nrproto.token_list[4]);
        nrproto_Cpa_recv(fd, petSlotForm, petItemSlotFrom, petSlotTo, petItemSlotTo);
        return 0;
    }
#endif
	return -1;
}


void nrproto_XYD_send( int fd,int x,int y,int dir )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_XYD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_MC_send( int fd,int id,int fl,int x1,int y1,int x2,int y2,int tilesum,int objsum,int eventsum )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_MC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( id ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fl ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( tilesum ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( objsum ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( eventsum ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_M_send( int fd,int id,int fl,int x1,int y1,int x2,int y2,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_M] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( id ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fl ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y1 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y2 ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_EV_send( int fd,int seqno,int result )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_EV] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_EP_send( int fd,int min,int max )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_EP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( min ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( max ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_EN_send( int fd,int result,int field )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_EN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( field ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_RS_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_RS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_RD_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_RD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_B_send( int fd,char* command )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_B] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( command ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_IA_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_IA] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_I_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_I] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_LI_send( int fd,int index,int servertime,int servernumber,int adjusttime )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_LI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( servertime ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( servernumber ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( adjusttime ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_SI_send( int fd,int fromindex,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_SI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fromindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_IR_send( int fd,int haveskillindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_IR] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( haveskillindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_BT_send( int fd,int time,int flag )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_BT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( time ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flag ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_MSG_send( int fd,int aindex,char* text,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_MSG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( aindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( text ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_AL_send( int fd,int albumversion,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_AL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumversion ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ALI_send( int fd,int albumid,int imgno,int catchflg,int rare,int type,int vital,int str,int tgh,int quick,int magic,int earth,int water,int fire,int wind,int slot,char* comment,char* name )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ALI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( imgno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( catchflg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( rare ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( type ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( vital ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( str ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( tgh ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( quick ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( magic ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( earth ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( water ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fire ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( wind ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( slot ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( comment ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( name ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ALN_send( int fd,int albumid,char* name )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ALN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( name ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ALO_send( int fd,int albumid )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ALO] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( albumid ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PME_send( int fd,int objindex,int graphicsno,int x,int y,int dir,int flg,int no,char* cdata )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PME] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( objindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( graphicsno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( dir ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( no ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( cdata ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_AB_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_AB] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ABG_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ABG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ABI_send( int fd,int num,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ABI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( num ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GC_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GC] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GI_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GT_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GM_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GMI_send( int fd,int num,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GMI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( num ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_RGM_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_RGM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GML_send( int fd,int index,char* text,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GML] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( text ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GD_send( int fd,int index )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRV_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRV] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRL_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRA_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRA] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRD_send( int fd,char* name,int lv,char* job,char* title,char* guildname,int graphic,int sid,char* smsg,int bid,char* bmsg,int aid,char* amsg,char* pmsg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( name ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( lv ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( job ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( title ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( guildname ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( graphic ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( sid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( smsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( bmsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( aid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( amsg ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( pmsg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRE_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRE] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRM_send( int fd,int index,char* text,int color )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRM] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( text ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRW_send( int fd,int windowtype,int buttontype,int seqno,int objindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRW] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( windowtype ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( buttontype ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( objindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PRAD_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PRAD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TK_send( int fd,int index,char* message,int color,int fontsize )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TK] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( color ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( fontsize ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_STK_send( int fd,char* message )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_STK] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( message ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CP_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CP2_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CP2] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_KP_send( int fd,int number,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_KP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( number ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_KP2_send( int fd,int number,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_KP2] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( number ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PP_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_C_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_C] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CN_send( int fd,int number,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( number ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TITLE_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TITLE] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CA_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CA] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CD_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CJ_send( int fd,int jobassort,char* jobname )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CJ] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( jobassort ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( jobname ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CS_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CT_send( int fd,int skillindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( skillindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PT_send( int fd,int petindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PT] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_S_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_S] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

void nrproto_FS_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_FS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_HL_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_HL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PR_send( int fd,int request,int result )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PR] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( request ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GFL_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GFL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GPD_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GPD] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GFLI_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GFLI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_GPDI_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_GPDI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TU_send( int fd,int result,int type,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TU] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( type ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRPL_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRPL] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRS_send( int fd,char* name,int level )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( name ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( level ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TROP_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TROP] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRLI_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRLI] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRLG_send( int fd,int gold )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRLG] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( gold ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRLP_send( int fd,int petplace,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRLP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petplace ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRLPS_send( int fd,int petplace,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRLPS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( petplace ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TRCL_send( int fd )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TRCL] );
	nrproto_strcatsafe( nrproto.work , "" ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_TROC_send( int fd,int flg )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_TROC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flg ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PS_send( int fd,int result,int havepetindex,int havepetskill,int toindex )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( havepetskill ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( toindex ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_LVUP_send( int fd,int point )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_LVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( point ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PLVUP_send( int fd,int pethave,int point )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PLVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pethave ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( point ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_POS_send( int fd,int pos )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_POS] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( pos ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_WN_send( int fd,int windowtype,int buttontype,int seqno,int objindex,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_WN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( windowtype ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( buttontype ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( objindex ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_EF_send( int fd,int effect,int level,char* option )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_EF] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( effect ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( level ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( option ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_SE_send( int fd,int senumber )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_SE] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( senumber ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_BGMW_send( int fd,int sw )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_BGMW] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( sw ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PC_send( int fd,int palnumber,int frame_cnt )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( palnumber ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( frame_cnt ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_SH_send( int fd,int action,int mapid,int floor,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_SH] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( action ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( mapid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( floor ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PLAYSE_send( int fd,int seno,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PLAYSE] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ES_send( int fd,int seno,int x,int y )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ES] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_MN_send( int fd,char* mapname )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_MN] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( mapname ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

#ifdef VERSION_TW
void nrproto_CC_send( int fd,int mapid,int floor,int maxx,int maxy,int x,int y,int cfgid,int seqno,int var,int bgm,int cut,int warpnum )
#else
void nrproto_CC_send( int fd,int mapid,int floor,int maxx,int maxy,int x,int y,int cfgid,int seqno,int var,int bgm,int cut )
#endif
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( mapid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( floor ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( maxx ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( maxy ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( x ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( y ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( cfgid ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( seqno ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( var ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bgm ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( cut ) ,nrproto.workbufsize );
#ifdef	VERSION_TW
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( warpnum ) ,nrproto.workbufsize );
#endif
	nrproto_Send( fd , nrproto.work );
}

#ifdef NCASH_BILLING
void nrproto_ClientLogin_send( int fd,int result,char* data, int bill )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ClientLogin] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bill ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
#else
#ifdef CEN_BILLING
void nrproto_ClientLogin_send( int fd,int result,char* data, int bill )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ClientLogin] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( bill ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
#else
void nrproto_ClientLogin_send( int fd,int result,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ClientLogin] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
#endif
#endif


void nrproto_CreateNewChar_send( int fd,char* result,int registnumber,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CreateNewChar] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( registnumber ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CharDelete_send( int fd,char* result,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CharDelete] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CharLogin_send( int fd,char* result,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CharLogin] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CharList_send( int fd,int result,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CharList] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_CharLogout_send( int fd,char* result,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_CharLogout] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_ProcGet_send( int fd,char* data )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_ProcGet] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( data ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PlayerNumGet_send( int fd,int logincount,int player )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PlayerNumGet] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( logincount ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( player ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_Echo_send( int fd,char* test )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_Echo] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( test ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_IP_send( int fd,char* ip )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_IP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( ip ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PV_send( int fd,int ver )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PV] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( ver ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_PVUP_send( int fd,int result )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_PVUP] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}


void nrproto_MAC_send( int fd,int listmax,int count )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_MAC] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( listmax ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( count ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

void nrproto_SetServerLogFiles( char *r , char *w )
{
	nrproto_strcpysafe( nrproto_writelogfilename , w , sizeof(nrproto_writelogfilename ));
	nrproto_strcpysafe( nrproto_readlogfilename , r , sizeof(nrproto_readlogfilename ));
}
int nrproto_InitServer(	int (*writefunc)(int,char*,int) ,	int worksiz )
{
	int i;
	if( (void*) writefunc == NULL){nrproto.write_func = nrproto_default_write_wrap;} else {nrproto.write_func = writefunc;}
	nrproto_AllocateCommonWork(worksiz);
	nrproto_stringwrapper = (char**) malloc(sizeof(char*) * MAXLSRPCARGS);
	if(nrproto_stringwrapper ==NULL) return -1;
	memset( nrproto_stringwrapper , 0, sizeof(char*) *MAXLSRPCARGS);
	for(i=0;i<MAXLSRPCARGS;i++){
		nrproto_stringwrapper[i] = (char*) malloc( worksiz );
		if( nrproto_stringwrapper[i] == NULL){
			for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);return -1;}
		}
	}
	nrproto.ret_work = (char*) malloc(sizeof( worksiz ));
	if( nrproto.ret_work == NULL ){ return -1; }
	return 0;
}

void nrproto_CleanupServer( void )
{
	int i;
	free( nrproto.work );
	free( nrproto.arraywork);
	free( nrproto.escapework );
	free( nrproto.val_str);
	free( nrproto.token_list );
	for(i=0;i<MAXLSRPCARGS;i++){free( nrproto_stringwrapper[i]);}
	free( nrproto_stringwrapper );
	free( nrproto.ret_work );
}

#ifdef NCASH_BILLING
void nrproto_Expire_send( int fd, int flag )
{
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_Expire] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( flag ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}
#endif

/*nrproto_serv.c*/
void nrproto_StallStart_send (int fd , int result ){
    if (fd < 0) return;
	nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_StallStart] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

void nrproto_StallEnd_send (int fd , int result ){
    if (fd < 0) return;
    nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_StallEnd] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

void nrproto_StallBrowse_send (int fd , int result , int char_index , const char *stallname , const char *namedesc , const char *itemdata , const char *petdata ){
    if (fd < 0) return;
    nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_StallBrowse] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( char_index ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( stallname ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( namedesc ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( itemdata ) ,nrproto.workbufsize );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_string( petdata ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

void nrproto_StallBuy_send (int fd , int result ){
    if (fd < 0) return;
    nrproto_CreateHeader( nrproto.work , PacketSend[PACKETVER][SEND_StallBuy] );
	nrproto_strcatsafe( nrproto.work , nrproto_mkstr_int( result ) ,nrproto.workbufsize );
	nrproto_Send( fd , nrproto.work );
}

#ifdef PET_EQUIPITEM

void nrproto_iKe_send(int fd, int petSlot, const char *buff) {
    nrproto_CreateHeader(nrproto.work, PacketSend[PACKETVER][SEND_iKe]);
    nrproto_strcatsafe(nrproto.work, nrproto_mkstr_int(petSlot), nrproto.workbufsize);
    nrproto_strcatsafe(nrproto.work, nrproto_mkstr_string((char *) buff), nrproto.workbufsize);
    nrproto_Send(fd, nrproto.work);
}

#endif

/* end of the generated server code */

