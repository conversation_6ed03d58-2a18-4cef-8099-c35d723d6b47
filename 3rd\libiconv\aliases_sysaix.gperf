struct alias { int name; unsigned int encoding_index; };
%struct-type
%language=ANSI-C
%define hash-function-name aliases_hash
%define lookup-function-name aliases_lookup
%7bit
%readonly-tables
%global-table
%define word-array-name aliases
%pic
%%
US-ASCII, ei_ascii
ASCII, ei_ascii
ISO646-US, ei_ascii
ISO_646.IRV:1991, ei_ascii
ISO-IR-6, ei_ascii
ANSI_X3.4-1968, ei_ascii
ANSI_X3.4-1986, ei_ascii
CP367, ei_ascii
IBM367, ei_ascii
US, ei_ascii
CSASCII, ei_ascii
UTF-8, ei_utf8
UCS-2, ei_ucs2
ISO-10646-UCS-2, ei_ucs2
CSUNICODE, ei_ucs2
UCS-2BE, ei_ucs2be
UNICODEBIG, ei_ucs2be
UNICODE-1-1, ei_ucs2be
CSUNICODE11, ei_ucs2be
UCS-2LE, ei_ucs2le
UNICODELITTLE, ei_ucs2le
UCS-4, ei_ucs4
ISO-10646-UCS-4, ei_ucs4
CSUCS4, ei_ucs4
UCS-4BE, ei_ucs4be
UCS-4LE, ei_ucs4le
UTF-16, ei_utf16
UTF-16BE, ei_utf16be
UTF-16LE, ei_utf16le
UTF-32, ei_utf32
UTF-32BE, ei_utf32be
UTF-32LE, ei_utf32le
UTF-7, ei_utf7
UNICODE-1-1-UTF-7, ei_utf7
CSUNICODE11UTF7, ei_utf7
UCS-2-INTERNAL, ei_ucs2internal
UCS-2-SWAPPED, ei_ucs2swapped
UCS-4-INTERNAL, ei_ucs4internal
UCS-4-SWAPPED, ei_ucs4swapped
C99, ei_c99
JAVA, ei_java
ISO-8859-1, ei_iso8859_1
ISO_8859-1, ei_iso8859_1
ISO_8859-1:1987, ei_iso8859_1
ISO-IR-100, ei_iso8859_1
CP819, ei_iso8859_1
IBM819, ei_iso8859_1
LATIN1, ei_iso8859_1
L1, ei_iso8859_1
CSISOLATIN1, ei_iso8859_1
ISO8859-1, ei_iso8859_1
ISO-8859-2, ei_iso8859_2
ISO_8859-2, ei_iso8859_2
ISO_8859-2:1987, ei_iso8859_2
ISO-IR-101, ei_iso8859_2
LATIN2, ei_iso8859_2
L2, ei_iso8859_2
CSISOLATIN2, ei_iso8859_2
ISO8859-2, ei_iso8859_2
ISO-8859-3, ei_iso8859_3
ISO_8859-3, ei_iso8859_3
ISO_8859-3:1988, ei_iso8859_3
ISO-IR-109, ei_iso8859_3
LATIN3, ei_iso8859_3
L3, ei_iso8859_3
CSISOLATIN3, ei_iso8859_3
ISO8859-3, ei_iso8859_3
ISO-8859-4, ei_iso8859_4
ISO_8859-4, ei_iso8859_4
ISO_8859-4:1988, ei_iso8859_4
ISO-IR-110, ei_iso8859_4
LATIN4, ei_iso8859_4
L4, ei_iso8859_4
CSISOLATIN4, ei_iso8859_4
ISO8859-4, ei_iso8859_4
ISO-8859-5, ei_iso8859_5
ISO_8859-5, ei_iso8859_5
ISO_8859-5:1988, ei_iso8859_5
ISO-IR-144, ei_iso8859_5
CYRILLIC, ei_iso8859_5
CSISOLATINCYRILLIC, ei_iso8859_5
ISO8859-5, ei_iso8859_5
ISO-8859-6, ei_iso8859_6
ISO_8859-6, ei_iso8859_6
ISO_8859-6:1987, ei_iso8859_6
ISO-IR-127, ei_iso8859_6
ECMA-114, ei_iso8859_6
ASMO-708, ei_iso8859_6
ARABIC, ei_iso8859_6
CSISOLATINARABIC, ei_iso8859_6
ISO8859-6, ei_iso8859_6
ISO-8859-7, ei_iso8859_7
ISO_8859-7, ei_iso8859_7
ISO_8859-7:1987, ei_iso8859_7
ISO_8859-7:2003, ei_iso8859_7
ISO-IR-126, ei_iso8859_7
ECMA-118, ei_iso8859_7
ELOT_928, ei_iso8859_7
GREEK8, ei_iso8859_7
GREEK, ei_iso8859_7
CSISOLATINGREEK, ei_iso8859_7
ISO8859-7, ei_iso8859_7
ISO-8859-8, ei_iso8859_8
ISO_8859-8, ei_iso8859_8
ISO_8859-8:1988, ei_iso8859_8
ISO-IR-138, ei_iso8859_8
HEBREW, ei_iso8859_8
CSISOLATINHEBREW, ei_iso8859_8
ISO8859-8, ei_iso8859_8
ISO-8859-9, ei_iso8859_9
ISO_8859-9, ei_iso8859_9
ISO_8859-9:1989, ei_iso8859_9
ISO-IR-148, ei_iso8859_9
LATIN5, ei_iso8859_9
L5, ei_iso8859_9
CSISOLATIN5, ei_iso8859_9
ISO8859-9, ei_iso8859_9
ISO-8859-10, ei_iso8859_10
ISO_8859-10, ei_iso8859_10
ISO_8859-10:1992, ei_iso8859_10
ISO-IR-157, ei_iso8859_10
LATIN6, ei_iso8859_10
L6, ei_iso8859_10
CSISOLATIN6, ei_iso8859_10
ISO8859-10, ei_iso8859_10
ISO-8859-11, ei_iso8859_11
ISO_8859-11, ei_iso8859_11
ISO8859-11, ei_iso8859_11
ISO-8859-13, ei_iso8859_13
ISO_8859-13, ei_iso8859_13
ISO-IR-179, ei_iso8859_13
LATIN7, ei_iso8859_13
L7, ei_iso8859_13
ISO8859-13, ei_iso8859_13
IBM-921, ei_iso8859_13
ISO-8859-14, ei_iso8859_14
ISO_8859-14, ei_iso8859_14
ISO_8859-14:1998, ei_iso8859_14
ISO-IR-199, ei_iso8859_14
LATIN8, ei_iso8859_14
L8, ei_iso8859_14
ISO-CELTIC, ei_iso8859_14
ISO8859-14, ei_iso8859_14
ISO-8859-15, ei_iso8859_15
ISO_8859-15, ei_iso8859_15
ISO_8859-15:1998, ei_iso8859_15
ISO-IR-203, ei_iso8859_15
LATIN-9, ei_iso8859_15
ISO8859-15, ei_iso8859_15
ISO-8859-16, ei_iso8859_16
ISO_8859-16, ei_iso8859_16
ISO_8859-16:2001, ei_iso8859_16
ISO-IR-226, ei_iso8859_16
LATIN10, ei_iso8859_16
L10, ei_iso8859_16
ISO8859-16, ei_iso8859_16
KOI8-R, ei_koi8_r
CSKOI8R, ei_koi8_r
KOI8-U, ei_koi8_u
KOI8-RU, ei_koi8_ru
CP1250, ei_cp1250
WINDOWS-1250, ei_cp1250
MS-EE, ei_cp1250
CP1251, ei_cp1251
WINDOWS-1251, ei_cp1251
MS-CYRL, ei_cp1251
CP1252, ei_cp1252
WINDOWS-1252, ei_cp1252
MS-ANSI, ei_cp1252
IBM-1252, ei_cp1252
CP1253, ei_cp1253
WINDOWS-1253, ei_cp1253
MS-GREEK, ei_cp1253
CP1254, ei_cp1254
WINDOWS-1254, ei_cp1254
MS-TURK, ei_cp1254
CP1255, ei_cp1255
WINDOWS-1255, ei_cp1255
MS-HEBR, ei_cp1255
CP1256, ei_cp1256
WINDOWS-1256, ei_cp1256
MS-ARAB, ei_cp1256
CP1257, ei_cp1257
WINDOWS-1257, ei_cp1257
WINBALTRIM, ei_cp1257
CP1258, ei_cp1258
WINDOWS-1258, ei_cp1258
CP850, ei_cp850
IBM850, ei_cp850
850, ei_cp850
CSPC850MULTILINGUAL, ei_cp850
IBM-850, ei_cp850
CP862, ei_cp862
IBM862, ei_cp862
862, ei_cp862
CSPC862LATINHEBREW, ei_cp862
CP866, ei_cp866
IBM866, ei_cp866
866, ei_cp866
CSIBM866, ei_cp866
CP1131, ei_cp1131
IBM-1131, ei_cp1131
MACROMAN, ei_mac_roman
MACINTOSH, ei_mac_roman
MAC, ei_mac_roman
CSMACINTOSH, ei_mac_roman
MACCENTRALEUROPE, ei_mac_centraleurope
MACICELAND, ei_mac_iceland
MACCROATIAN, ei_mac_croatian
MACROMANIA, ei_mac_romania
MACCYRILLIC, ei_mac_cyrillic
MACUKRAINE, ei_mac_ukraine
MACGREEK, ei_mac_greek
MACTURKISH, ei_mac_turkish
MACHEBREW, ei_mac_hebrew
MACARABIC, ei_mac_arabic
MACTHAI, ei_mac_thai
HP-ROMAN8, ei_hp_roman8
ROMAN8, ei_hp_roman8
R8, ei_hp_roman8
CSHPROMAN8, ei_hp_roman8
NEXTSTEP, ei_nextstep
ARMSCII-8, ei_armscii_8
GEORGIAN-ACADEMY, ei_georgian_academy
GEORGIAN-PS, ei_georgian_ps
KOI8-T, ei_koi8_t
PT154, ei_pt154
PTCP154, ei_pt154
CP154, ei_pt154
CYRILLIC-ASIAN, ei_pt154
CSPTCP154, ei_pt154
RK1048, ei_rk1048
STRK1048-2002, ei_rk1048
KZ-1048, ei_rk1048
CSKZ1048, ei_rk1048
MULELAO-1, ei_mulelao
CP1133, ei_cp1133
IBM-CP1133, ei_cp1133
TIS-620, ei_tis620
TIS620, ei_tis620
TIS620-0, ei_tis620
TIS620.2529-1, ei_tis620
TIS620.2533-0, ei_tis620
TIS620.2533-1, ei_tis620
ISO-IR-166, ei_tis620
CP874, ei_cp874
WINDOWS-874, ei_cp874
VISCII, ei_viscii
VISCII1.1-1, ei_viscii
CSVISCII, ei_viscii
TCVN, ei_tcvn
TCVN-5712, ei_tcvn
TCVN5712-1, ei_tcvn
TCVN5712-1:1993, ei_tcvn
JIS_C6220-1969-RO, ei_iso646_jp
ISO646-JP, ei_iso646_jp
ISO-IR-14, ei_iso646_jp
JP, ei_iso646_jp
CSISO14JISC6220RO, ei_iso646_jp
JIS_X0201, ei_jisx0201
JISX0201-1976, ei_jisx0201
X0201, ei_jisx0201
CSHALFWIDTHKATAKANA, ei_jisx0201
JIS_X0208, ei_jisx0208
JIS_X0208-1983, ei_jisx0208
JIS_X0208-1990, ei_jisx0208
JIS0208, ei_jisx0208
X0208, ei_jisx0208
ISO-IR-87, ei_jisx0208
JIS_C6226-1983, ei_jisx0208
CSISO87JISX0208, ei_jisx0208
JIS_X0212, ei_jisx0212
JIS_X0212.1990-0, ei_jisx0212
JIS_X0212-1990, ei_jisx0212
X0212, ei_jisx0212
ISO-IR-159, ei_jisx0212
CSISO159JISX02121990, ei_jisx0212
GB_1988-80, ei_iso646_cn
ISO646-CN, ei_iso646_cn
ISO-IR-57, ei_iso646_cn
CN, ei_iso646_cn
CSISO57GB1988, ei_iso646_cn
GB_2312-80, ei_gb2312
ISO-IR-58, ei_gb2312
CSISO58GB231280, ei_gb2312
CHINESE, ei_gb2312
ISO-IR-165, ei_isoir165
CN-GB-ISOIR165, ei_isoir165
KSC_5601, ei_ksc5601
KS_C_5601-1987, ei_ksc5601
KS_C_5601-1989, ei_ksc5601
ISO-IR-149, ei_ksc5601
CSKSC56011987, ei_ksc5601
KOREAN, ei_ksc5601
EUC-JP, ei_euc_jp
EUCJP, ei_euc_jp
EXTENDED_UNIX_CODE_PACKED_FORMAT_FOR_JAPANESE, ei_euc_jp
CSEUCPKDFMTJAPANESE, ei_euc_jp
IBM-EUCJP, ei_euc_jp
SHIFT_JIS, ei_sjis
SHIFT-JIS, ei_sjis
SJIS, ei_sjis
MS_KANJI, ei_sjis
CSSHIFTJIS, ei_sjis
CP932, ei_cp932
IBM-932, ei_cp932
ISO-2022-JP, ei_iso2022_jp
CSISO2022JP, ei_iso2022_jp
ISO-2022-JP-1, ei_iso2022_jp1
ISO-2022-JP-2, ei_iso2022_jp2
CSISO2022JP2, ei_iso2022_jp2
ISO-2022-JP-MS, ei_iso2022_jpms
CP50221, ei_iso2022_jpms
EUC-CN, ei_euc_cn
EUCCN, ei_euc_cn
GB2312, ei_euc_cn
CN-GB, ei_euc_cn
CSGB2312, ei_euc_cn
IBM-EUCCN, ei_euc_cn
GBK, ei_ces_gbk
CP936, ei_cp936
MS936, ei_cp936
WINDOWS-936, ei_cp936
GB18030, ei_gb18030
ISO-2022-CN, ei_iso2022_cn
CSISO2022CN, ei_iso2022_cn
ISO-2022-CN-EXT, ei_iso2022_cn_ext
HZ, ei_hz
HZ-GB-2312, ei_hz
EUC-TW, ei_euc_tw
EUCTW, ei_euc_tw
CSEUCTW, ei_euc_tw
IBM-EUCTW, ei_euc_tw
BIG5, ei_ces_big5
BIG-5, ei_ces_big5
BIG-FIVE, ei_ces_big5
BIGFIVE, ei_ces_big5
CN-BIG5, ei_ces_big5
CSBIG5, ei_ces_big5
CP950, ei_cp950
BIG5-HKSCS:1999, ei_big5hkscs1999
BIG5-HKSCS:2001, ei_big5hkscs2001
BIG5-HKSCS:2004, ei_big5hkscs2004
BIG5-HKSCS, ei_big5hkscs2008
BIG5HKSCS, ei_big5hkscs2008
BIG5-HKSCS:2008, ei_big5hkscs2008
EUC-KR, ei_euc_kr
EUCKR, ei_euc_kr
CSEUCKR, ei_euc_kr
IBM-EUCKR, ei_euc_kr
CP949, ei_cp949
UHC, ei_cp949
JOHAB, ei_johab
CP1361, ei_johab
ISO-2022-KR, ei_iso2022_kr
CSISO2022KR, ei_iso2022_kr
CHAR, ei_local_char
WCHAR_T, ei_local_wchar_t
