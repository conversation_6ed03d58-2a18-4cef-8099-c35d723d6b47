COMPILER = g++
CXXFLAGS = -Wall -pedantic -Wno-long-long
INCLUDEDIRS = -I../../include -I../../include/private -I.. -I/usr/include/postgresql
LIBDIRS = -L../../src/core -L../../src/backends/postgresql -L/usr/lib/x86_64-linux-gnu
LIBS = -lsoci_postgresql -lsoci_core -ldl -lpq

test-postgresql : test-postgresql.cpp
	${COMPILER} $? -o $@ ${INCLUDEDIRS} ${LIBDIRS} ${LIBS}


clean :
	rm -f test-postgresql
