#ifndef _NRPROTOUTIL_H_
#define _NRPROTOUTIL_H_
#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <strings.h>
#include <unistd.h>
#endif

#include "buf.h"
#include "version.h"

#ifdef	VERSION_TW
//#define nrproto__ENCRYPT
#else
#define nrproto__ENCRYPT
#endif

int HookDispatchFn(int fd, const char *header);
void log_packet(int fd, const char* direction, const char* packet);
#define nrproto__NODEBUG
struct nrproto_ {
	int (*write_func)(int,char*,int) ;    /* write function */
	size_t workbufsize;                   /* size of work area */
    char  *work,*arraywork,*escapework,*val_str,*ret_work;      /* work areas which have same size  */
	char *cryptwork,*jencodecopy,*jencodeout,*compresswork;     /* these work has bigger size (3times)*/
    char** token_list;                    /* token list */
    unsigned long message_id; /*= 1,  */       /* for generating message IDs */
	void (*log_callback)(char*);
};
//#ifdef _NRPROTOUTIL_C_
//struct nrproto_ nrproto = {
//	NULL,
//	0,
//	NULL,NULL,NULL,NULL,NULL,
//	NULL,NULL,NULL,NULL,
//	NULL,
//	1,
//	NULL,
//};
//char **nrproto_stringwrapper;
//char nrproto_readlogfilename[1024];
//char nrproto_writelogfilename[1024];
//#else
extern char **nrproto_stringwrapper;
extern struct nrproto_ nrproto;
extern char nrproto_readlogfilename[1024];
extern char nrproto_writelogfilename[1024];
//#endif
char* nrproto_escapeString(const char*a );
char* nrproto_descapeString(const char*a );
void nrproto_splitString( char *src  );
void nrproto_strcpysafe( char *dest, char *src, int len );
void nrproto_strcatsafe( char *dest , char *src , int maxlen );
char*nrproto_mkstr_int( int i );
char*nrproto_mkstr_u_int( unsigned int i );
char*nrproto_mkstr_long( long l );
char*nrproto_mkstr_u_long( unsigned long l );
char*nrproto_mkstr_short( short s );
char*nrproto_mkstr_u_short( short s );
char*nrproto_mkstr_char( char c );
char*nrproto_mkstr_u_char( char c);
char*nrproto_mkstr_string(const char*a );
char*nrproto_mkstr_float( float f );
char*nrproto_mkstr_double( double d );
char*nrproto_mkstr_int_array( int size , int *array );
char*nrproto_mkstr_u_int_array( int size , int *array );
char*nrproto_mkstr_short_array( int size , short *array );
char*nrproto_mkstr_u_short_array(int size , short *array );
char *nrproto_mkstr_char_array( int size , char *array );
char *nrproto_mkstr_u_char_array( int size , unsigned char *array );
char *nrproto_mkstr_float_array( int size , float *array );
char *nrproto_mkstr_double_array( int size , double *array );
int nrproto_demkstr_int(const char*a );
unsigned int nrproto_demkstr_u_int( char*a );
long nrproto_demkstr_long( char*a );
unsigned long nrproto_demkstr_u_long(char*a );
short nrproto_demkstr_short( char*a );
unsigned short nrproto_demkstr_u_short( char*a );
char nrproto_demkstr_char( char*a );
unsigned char nrproto_demkstr_u_char( char*a );
float nrproto_demkstr_float( char*a );
double nrproto_demkstr_double(char*a );
char* nrproto_demkstr_string( char*a);
int *nrproto_demkstr_int_array( char**tk ,int *buf  ,int start , int size );
int *nrproto_demkstr_u_int_array( char **tk , int *buf , int start , int size );
unsigned int *nrproto_demkstr_long_array(
	char **tk , unsigned int *buf , int start , int size );
unsigned long *nrproto_demkstr_u_long_array(
	char **tk , unsigned long *buf , int start , int size );
short *nrproto_demkstr_short_array( char **tk , short *buf , int start , int size );
unsigned short*nrproto_demkstr_u_short_array(
	char **tk , unsigned short *buf , int start , int size );
char *nrproto_demkstr_char_array( char **tk , char *buf , int start , int size );
unsigned char *nrproto_demkstr_u_char_array(
	char **tk , unsigned char*buf , int start , int size );
float *nrproto_demkstr_float_array( char **tk , float *buf , int start , int size );
double *nrproto_demkstr_u_double_array( char **tk , double *buf , int start , int size );
char *nrproto_wrapStringAddr( char *copy , int maxcopylen , char*src );

void nrproto_GetMessageInfo( int *id , char *funcname , int len,char **tk );
int nrproto_ClientRead(void);
void nrproto_consumeLine(char *buf , int ofs );
void nrproto_copyLine( char*src , char *out , int maxoutlen );
void nrproto_Send( int fd , char *msg );
int nrproto_AllocateCommonWork(int bufsiz);
unsigned int nrproto_GetNewMessageID(void);
void nrproto_CreateHeader(char*out, char *fname );
void nrproto_CreateHeaderID( char *out,unsigned long msgid , char *fname );
int nrproto_default_write_wrap( int fd , char *buf , int size );
void nrproto_bzero( char *b , int siz );
void nrproto_bcopy(char*s , char *d , int siz );
char *nrproto_Ltoa( long v );
char *nrproto_Ultoa( unsigned long v );
void nrproto_DebugSend( int fd , char *msg );
/* Modified by ringo to fasten int type transfer */
char *nrproto_cnv10to62( int a, char *out, int outlen );
int nrproto_a62toi(const char *a );
extern int nrproto_NoCompressFlg;

#endif


