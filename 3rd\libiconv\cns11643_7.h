/*
 * Copyright (C) 1999-2002, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 7
 */

static const unsigned short cns11643_7_2uni_page21[6539] = {
  /* 0x21 */
  0x2b55, 0x2c82, 0x2c89, 0x2c87, 0x2dbe, 0x2dbd, 0x2dca, 0x2dd4,
  0x2dbc, 0x2dc4, 0x2dc1, 0x2dc2, 0x2dd7, 0x2d70, 0x2dba, 0x2de3,
  0x2dbb, 0x2eb1, 0x2eb6, 0x2eb0, 0x2f6c, 0x300d, 0x3007, 0x31f1,
  0x31f5, 0x31ed, 0x31ef, 0x31eb, 0x31ec, 0x31ee, 0x3207, 0x33c4,
  0x3408, 0x34b3, 0x34ad, 0x34b0, 0x3511, 0x2c8a, 0x3683, 0x367a,
  0x3682, 0x3668, 0x3671, 0x36b0, 0x36af, 0x01f6, 0x366c, 0x366f,
  0x3662, 0x3665, 0x3681, 0x367f, 0x3664, 0x3673, 0x366e, 0x3667,
  0x3674, 0x367d, 0x3678, 0x3685, 0x36aa, 0x3688, 0x36a6, 0xc34e,
  0x368a, 0x3684, 0x1a55, 0x3924, 0x3922, 0x3923, 0x3a32, 0x3a5b,
  0x3a4f, 0x0267, 0x3a26, 0x3a3e, 0x3a42, 0x3a47, 0x3a22, 0x309b,
  0x3a4a, 0x3a1f, 0x3a49, 0x3a2b, 0x3a33, 0xc358, 0x3a2a, 0x3a28,
  0x3b3d, 0x3b3c, 0x3bbf, 0x3c79, 0x3c80, 0x3c7c,
  /* 0x22 */
  0x3c7a, 0x3c78, 0x3d82, 0x3d5c, 0x3d5a, 0x3d4b, 0x3d7c, 0x3d7f,
  0x3d1f, 0x3d89, 0x3d8b, 0x3d57, 0x3d7e, 0x3d7d, 0x3d53, 0x3f9f,
  0x3fbc, 0x3fa5, 0x3f86, 0x3fdc, 0x3fa4, 0x3fb6, 0x3fa2, 0x3fb4,
  0x4048, 0x4046, 0x40a7, 0x4114, 0x4112, 0x4111, 0x41e1, 0x41d0,
  0x4226, 0x4228, 0x4229, 0x432f, 0x430d, 0x4325, 0x4314, 0x433c,
  0x430b, 0x4311, 0x4330, 0x4318, 0x4319, 0x4336, 0x4317, 0x4310,
  0x4315, 0x4403, 0x4429, 0x444c, 0x453f, 0x453e, 0x4534, 0x4542,
  0x4535, 0x31f3, 0x45cd, 0x45d1, 0x45d2, 0x46ad, 0x46ba, 0x46c1,
  0x46bb, 0x46b4, 0x46bc, 0x476c, 0x4792, 0x4821, 0x4824, 0x5f19,
  0x48ad, 0x48ae, 0x48ab, 0x48ac, 0x4970, 0x4976, 0x4971, 0x4977,
  0x1c7f, 0x4aec, 0x4b1c, 0x4afb, 0x4aee, 0x4be2, 0x4afd, 0x4b1e,
  0x4b3b, 0x4b4a, 0xc3ae, 0x0592, 0x4b7d, 0x4b56,
  /* 0x23 */
  0x4b44, 0x4b4b, 0x4cfa, 0x4ce8, 0x4cf8, 0x4cff, 0x4cf7, 0x4cf6,
  0x4cfe, 0x4d07, 0x4d5d, 0x4ed4, 0xc3c3, 0x4ec8, 0x4ec5, 0xc3c0,
  0x4ec9, 0x4f4d, 0x4f20, 0x4f36, 0x4f35, 0x4f88, 0x4f25, 0x4f21,
  0x4f26, 0x0645, 0x4f3f, 0x4f3b, 0x4f24, 0x4f43, 0x4f4e, 0x4f4f,
  0x4f44, 0x4f40, 0x4f41, 0x4f39, 0x4f2b, 0x50dd, 0x50de, 0x51cd,
  0x51cb, 0x51db, 0x51d8, 0x51d9, 0x51d2, 0x51ca, 0x51d1, 0x51d4,
  0x51f9, 0x51d0, 0x525e, 0x525d, 0x528a, 0x5314, 0x5483, 0x5484,
  0x549b, 0x5482, 0x547d, 0x547c, 0x5499, 0x547e, 0x549a, 0x5495,
  0x547b, 0x5486, 0x5548, 0x5592, 0x56b2, 0x5742, 0x5772, 0x5715,
  0x5730, 0x5743, 0x575b, 0x571d, 0x5773, 0x572d, 0x07c9, 0x5721,
  0x571c, 0xc3ea, 0x5729, 0x571e, 0x5733, 0x5a10, 0x5a0e, 0xc3f1,
  0x5a0d, 0x5a11, 0x5a12, 0x5a17, 0x5a09, 0x5a0f,
  /* 0x24 */
  0x5a98, 0x5a94, 0x5a96, 0x5a99, 0x5a95, 0x5a97, 0x5b50, 0x5b52,
  0x5b4d, 0x5b57, 0x5b53, 0x5b56, 0x5bb8, 0x5c12, 0x5cf1, 0x5cf0,
  0x5cee, 0x5cef, 0x5ce5, 0x5ceb, 0x5ce7, 0x5cf2, 0x5ce6, 0x5cf7,
  0x5d09, 0x5d6b, 0x5d6a, 0x5e79, 0x5ecd, 0x5eef, 0x5ee4, 0x5ee3,
  0x5f6c, 0x5f67, 0x5f62, 0x5f58, 0x5f69, 0x5fab, 0x5f57, 0x5fad,
  0x5f54, 0x5fae, 0x5f76, 0x5f49, 0x5f45, 0x5f4f, 0xc40f, 0x5f50,
  0x5f7d, 0x5f44, 0x5f66, 0x5f48, 0x5fa3, 0x5f51, 0x5f53, 0xc410,
  0x5f60, 0x5f47, 0x5f5e, 0x5f85, 0x5fac, 0x5f6d, 0x5f75, 0x5fa8,
  0x0955, 0x5f43, 0x5f4e, 0x5f5c, 0x5f56, 0x5f6e, 0x5f63, 0x5f6a,
  0x6256, 0x6212, 0x625c, 0x6258, 0x6255, 0x627e, 0x62a8, 0x6211,
  0x6259, 0x625a, 0x6267, 0x6254, 0x625b, 0x62f0, 0x6429, 0x642c,
  0x642a, 0x6427, 0x6467, 0x6468, 0x6493, 0x6495,
  /* 0x25 */
  0x649a, 0x64ee, 0x64f1, 0x64ed, 0x64e9, 0x65cb, 0x65df, 0x65db,
  0x673c, 0x674e, 0x676c, 0x677b, 0x677c, 0x6783, 0x676b, 0x6766,
  0x6763, 0x67a0, 0x6785, 0x6768, 0x67a2, 0x68ec, 0x0ae8, 0x68e7,
  0x6910, 0x6918, 0x693a, 0x691a, 0x6938, 0x6941, 0x691b, 0x6914,
  0x691c, 0x691d, 0x6a12, 0x6a0d, 0x6a07, 0x6a7e, 0x6a7f, 0x34b9,
  0x6a80, 0x6a7a, 0x6af3, 0x6bb9, 0x6bbb, 0x6bba, 0x6bbd, 0x6bb7,
  0x6bbf, 0x6c21, 0x6d3a, 0x6d3d, 0x6d36, 0x6d49, 0x6d81, 0x0bb6,
  0x6d3f, 0x6d4d, 0x6d3e, 0x0bbd, 0x6d30, 0x6d4f, 0x6e37, 0x6e98,
  0x6e99, 0x6f11, 0x6f0d, 0x6f88, 0x6f8a, 0x701c, 0x7055, 0x7056,
  0x7062, 0x7057, 0x7042, 0x7010, 0x704d, 0x705a, 0x7066, 0x7046,
  0x4b7e, 0x704f, 0x80f9, 0x7187, 0x7184, 0x71f4, 0x71f5, 0x71f2,
  0x71f7, 0x7345, 0xc450, 0x7349, 0x7335, 0x72fd,
  /* 0x26 */
  0x7330, 0x7343, 0x7346, 0x0cd6, 0x7333, 0x74c6, 0xc456, 0x74d4,
  0x8dd8, 0x74e0, 0x36a7, 0x7544, 0x2d98, 0x7654, 0x763e, 0x7635,
  0x763a, 0x0d71, 0x7634, 0x7639, 0x7646, 0x765a, 0x765b, 0x763c,
  0x77dc, 0x77d6, 0x77d0, 0xc45e, 0x78ac, 0xc45d, 0x78a5, 0x79f4,
  0x7a24, 0x79e0, 0x79fd, 0x79f2, 0x79fc, 0x7a02, 0x79f1, 0x79ff,
  0x79d2, 0x79d8, 0x79d3, 0x79f9, 0xc463, 0x79ed, 0x79f7, 0x79f0,
  0x79d7, 0x7a09, 0x79f5, 0x7cc2, 0x7cc5, 0x7ccf, 0x0e98, 0x7cc1,
  0x7cc6, 0x7cd4, 0x7cce, 0x7e9b, 0x7e99, 0x7e8f, 0x7eb7, 0x7e82,
  0x7e93, 0x7eb4, 0x7ec1, 0x7e90, 0x7e9a, 0x7e94, 0x7e9c, 0x7eb5,
  0xc46f, 0xc46e, 0x7e9f, 0x7e8e, 0x7e9e, 0x803c, 0x8047, 0x8041,
  0x80eb, 0x80ec, 0x80ed, 0x80ee, 0x80df, 0x80f2, 0x810d, 0x810e,
  0x2c86, 0x81a9, 0x81a4, 0x81a5, 0x825f, 0x825a,
  /* 0x27 */
  0x8266, 0x8268, 0x826a, 0x8264, 0x8265, 0x82c1, 0x82d8, 0xc47b,
  0x82d9, 0x8309, 0x83aa, 0x83a3, 0x83b9, 0x77e2, 0x83a5, 0x83ab,
  0x8420, 0x8589, 0x858b, 0x85db, 0x85e4, 0x85ef, 0x85e2, 0x85e9,
  0x85f0, 0x85f3, 0x85dd, 0x861e, 0x871d, 0x8746, 0x877c, 0x8780,
  0x8781, 0x8782, 0x8787, 0x8788, 0x878a, 0x878c, 0x87e7, 0x87eb,
  0x87ea, 0x8868, 0x886b, 0x8869, 0x8866, 0x886a, 0x8865, 0x88e5,
  0x88e4, 0x88e6, 0x8918, 0x8a24, 0x8a48, 0x8a2f, 0x8a7e, 0x8989,
  0x8a1e, 0x8a49, 0x8a26, 0x8a09, 0x8a34, 0x8a2d, 0x8a4a, 0x8a15,
  0xc4a9, 0x8a33, 0x8a28, 0x8a27, 0x8a20, 0x8a2b, 0x8a29, 0x8a6a,
  0x8a0b, 0x8a0e, 0x8a1c, 0x89ff, 0xc4aa, 0x8a35, 0x8a11, 0x8a4b,
  0x8a4c, 0x8a1f, 0x8a0f, 0x8a39, 0x8a68, 0x8a1d, 0x8a08, 0x8a0c,
  0x8a0d, 0x8a62, 0x8aaf, 0x8a32, 0x8a2c, 0x8a64,
  /* 0x28 */
  0x8a04, 0x8a16, 0x8a4d, 0x8a07, 0x8aae, 0x8dd4, 0x8dd1, 0x8dd5,
  0x8dd0, 0x8f71, 0x8f5d, 0x8f5b, 0x8f7a, 0xc4bd, 0x8f7f, 0x8f79,
  0x8f67, 0x8f9e, 0x8f94, 0x8f64, 0x8f5e, 0x8f81, 0x8f5a, 0x8f57,
  0x8f7c, 0x8f98, 0x8f66, 0x8f7e, 0x8f82, 0x8f68, 0x8f5f, 0x8f63,
  0x8f97, 0x11cd, 0x8f53, 0x8f7b, 0x8f7d, 0x8f78, 0x9006, 0x91c0,
  0x91c2, 0x91c3, 0x92dc, 0x92e6, 0x92ec, 0x92f2, 0x92e8, 0x92eb,
  0x92ea, 0x92e5, 0x92e0, 0x92d0, 0x92d8, 0x92d5, 0x92d3, 0x92e4,
  0x92f3, 0x92db, 0x932f, 0x93f9, 0x945d, 0x945b, 0x944c, 0x9458,
  0x9460, 0x9453, 0x9450, 0x9507, 0x9508, 0x94f4, 0x94f6, 0x9504,
  0x94fd, 0x9505, 0x9628, 0x9656, 0x9642, 0x965c, 0x963d, 0x962f,
  0x962b, 0x9658, 0x9661, 0x962c, 0x9651, 0x9650, 0x963c, 0x9636,
  0x12f6, 0x9660, 0x965b, 0x962e, 0x9640, 0x965f,
  /* 0x29 */
  0x9626, 0x962d, 0x965e, 0x963b, 0x97ef, 0x97f3, 0x97ee, 0x97ed,
  0x985a, 0x9862, 0x985b, 0x985f, 0x985e, 0x9864, 0x98e0, 0x98e1,
  0x98e5, 0x99a5, 0x999b, 0x999f, 0x999c, 0x137a, 0x99aa, 0x99a4,
  0x99a3, 0x9a67, 0x9b26, 0x9b1a, 0x13b8, 0x9b1c, 0x9b15, 0x9b1b,
  0x9b18, 0x9b1e, 0x9b25, 0xc4dc, 0x9c7b, 0x9c75, 0x9c93, 0x9c8f,
  0x9c7c, 0x9c9b, 0x9c88, 0x9c91, 0x9c7e, 0x1401, 0x9c86, 0x9e5b,
  0x9e54, 0x9f35, 0x9f40, 0x9f37, 0x9f29, 0x9f26, 0x9f23, 0x9f30,
  0x9f20, 0x9f28, 0x9f32, 0x9f2a, 0x9f31, 0x9f24, 0x9f41, 0x9f42,
  0x9f43, 0x9f25, 0x9f38, 0xa01c, 0xa01d, 0xa01e, 0xa0f6, 0xa1a5,
  0xa189, 0xa19d, 0xa18a, 0xa187, 0xa1ab, 0xa186, 0xa19e, 0xa193,
  0xa1a1, 0xa188, 0xa1a3, 0xa19f, 0xa1a0, 0xa2ff, 0xa300, 0xa302,
  0xa2f8, 0xa2f7, 0xa301, 0xa387, 0xa389, 0xa376,
  /* 0x2a */
  0xa36e, 0xa377, 0xa382, 0xa385, 0xa383, 0xa384, 0xa457, 0xa44b,
  0xa456, 0xa45d, 0xa460, 0xa44d, 0xa455, 0xa454, 0xa453, 0xa450,
  0xa463, 0xa462, 0xa517, 0xa516, 0xa52d, 0xa5d9, 0xa5bf, 0x152f,
  0xa5b2, 0xa5ac, 0xa5b0, 0xa5cf, 0xa5b8, 0x1531, 0xa5d3, 0xa5b1,
  0xa5af, 0xa5ce, 0xa5b4, 0xa5b7, 0xa5d5, 0x159c, 0xa938, 0xa928,
  0xa93f, 0xa940, 0xa941, 0xa933, 0xa92b, 0xa92e, 0xab11, 0xab18,
  0xab17, 0xab19, 0xab16, 0x15e6, 0xab0d, 0xab26, 0xab79, 0xabd8,
  0xabf3, 0xabd7, 0xabcc, 0xabcf, 0xabcb, 0xabd1, 0xabce, 0xabd4,
  0xabd5, 0xabd3, 0xabd6, 0xabcd, 0xabda, 0xabd2, 0xabde, 0xaccc,
  0xacb8, 0xacb7, 0xacc2, 0xacc3, 0xadfa, 0xadf9, 0xae1f, 0xae1d,
  0x6282, 0xaea6, 0xae90, 0xae9e, 0xae98, 0xae93, 0xae92, 0xaea5,
  0xae95, 0xafa9, 0xafa8, 0xafaa, 0xafab, 0xafac,
  /* 0x2b */
  0xb01f, 0xb022, 0xb0aa, 0xb096, 0xb091, 0xb0ab, 0xb0a2, 0xb0a4,
  0xb09d, 0xb097, 0xb07d, 0xb09f, 0xb09c, 0xb099, 0xb0a6, 0xb092,
  0xb0a7, 0xb1c9, 0xb1c8, 0xb1c7, 0xb1cb, 0xb274, 0xb2c9, 0x1738,
  0xb2d2, 0xb2db, 0xb2eb, 0xb2e9, 0xb2ef, 0xb2ee, 0xb2f9, 0xb2de,
  0xb416, 0xb445, 0xb47f, 0xb498, 0xb49f, 0xb486, 0xb490, 0xb49a,
  0xb49d, 0xb48d, 0xb49c, 0xb4a0, 0xb4a7, 0xb48f, 0xb641, 0xb635,
  0xb637, 0xb630, 0xb63a, 0xb636, 0xc508, 0xb6ca, 0xb712, 0xb715,
  0xb722, 0xb713, 0xb718, 0xb721, 0xb810, 0xb820, 0xb82f, 0xb8a5,
  0xb8a3, 0xb8b6, 0xb8ab, 0xb968, 0xb967, 0xb96f, 0xb977, 0xb964,
  0xb978, 0x183a, 0xb976, 0xb96e, 0x18b1, 0xbbd0, 0xbbb3, 0xbbcb,
  0xbbd6, 0xbbb1, 0xbbb4, 0xbbd7, 0xbbc6, 0xbbba, 0xbbd1, 0xbbd2,
  0xbbb5, 0xbbc4, 0xbbcc, 0xbbbb, 0xbbb2, 0xbe5b,
  /* 0x2c */
  0xbe5a, 0xbe59, 0xbe99, 0xbe94, 0xbe96, 0xbe93, 0xbe91, 0xbe8f,
  0xbe98, 0xbf0c, 0xbf0a, 0xbf0e, 0xbf12, 0xbf11, 0xbf0b, 0xbf9a,
  0x1947, 0xbfb4, 0x1951, 0xc01c, 0xc020, 0xc021, 0xc0d3, 0xc0d2,
  0xc138, 0x2afd, 0x6bcf, 0x2c1d, 0x2c92, 0x2c8f, 0x2c8c, 0x2dea,
  0x2eb9, 0x2f6e, 0x2fa0, 0xaddd, 0x3012, 0x3017, 0x306d, 0x3205,
  0x31fb, 0x31fd, 0x3206, 0x3200, 0x31ff, 0x31fe, 0x32b6, 0x3305,
  0x3307, 0x34bc, 0x34bb, 0x358b, 0x3587, 0x97fb, 0x36e7, 0x36e6,
  0x36e2, 0xa1d3, 0x36d9, 0x36ca, 0x3712, 0x3710, 0x36dc, 0x36d0,
  0x368b, 0x3713, 0x4d10, 0x36da, 0xc34f, 0x36fd, 0x36e5, 0x36cc,
  0x3739, 0x36c7, 0x36d8, 0x3a60, 0x3a64, 0x3a86, 0x3a5e, 0x3a66,
  0x3a5f, 0x3a77, 0x3a82, 0x3a96, 0x3b3e, 0x3b79, 0x3b7a, 0x3c88,
  0x3c89, 0x3c8c, 0x62e0, 0x3c7b, 0x3c86, 0x3d94,
  /* 0x2d */
  0x3d97, 0x3db7, 0x3db5, 0x3db8, 0x3d93, 0x3db2, 0x3db4, 0x3d99,
  0x3dc3, 0x3d9d, 0x3d9b, 0x3da3, 0x0347, 0x3e9f, 0x3e9e, 0x3ea3,
  0x3ea0, 0x3ea1, 0x3fc5, 0x3fc6, 0x3fbe, 0x3fc4, 0x3fbf, 0x404c,
  0x404a, 0x40b1, 0x4115, 0x4117, 0x4110, 0x4118, 0x41e0, 0x41df,
  0x4227, 0x4343, 0x4348, 0x431d, 0x4350, 0x4358, 0x4347, 0x4354,
  0x4353, 0x4340, 0x4355, 0x0417, 0x435a, 0x4870, 0x455b, 0x454d,
  0x4556, 0x454f, 0xc387, 0x4559, 0xc388, 0x4554, 0x4553, 0x4550,
  0x46b3, 0x476e, 0x04a3, 0x476f, 0x4793, 0x4829, 0x4825, 0x4828,
  0x486f, 0x54b8, 0xc397, 0x48b3, 0x4979, 0x497e, 0x497c, 0x4983,
  0xc3ac, 0x4b00, 0x4b34, 0x4b65, 0x4b36, 0x4ba7, 0x4b59, 0x4b58,
  0x4b31, 0x4b62, 0x4b38, 0x4b73, 0x4b3e, 0x4b55, 0x4b54, 0x4b95,
  0xc3ad, 0x4ba5, 0x4b94, 0x4b9a, 0x4b9b, 0x4b99,
  /* 0x2e */
  0x4b9f, 0x4b53, 0x4d12, 0x4d0a, 0x4d09, 0x4d0c, 0x4d0b, 0x4d0e,
  0x4d0d, 0x4d08, 0x4f38, 0x4f3a, 0x4f37, 0x4f3d, 0x4f2d, 0x4f82,
  0x4f95, 0x4f87, 0x4f9d, 0x4fb5, 0x4f81, 0x4fc7, 0x4f9b, 0x4f98,
  0x4f94, 0x4f86, 0x4f90, 0x4f34, 0x4f8e, 0x4f85, 0x4fa6, 0x4f96,
  0x51ea, 0x51df, 0x54bd, 0x51f7, 0x51eb, 0x51e0, 0x51e8, 0x51e1,
  0x51e9, 0x51ee, 0x51e5, 0x51ec, 0x5263, 0x52c6, 0x52c2, 0x52c1,
  0x52c0, 0x52c3, 0x5318, 0x54ae, 0x54a3, 0x54c5, 0x54a0, 0x54b4,
  0x54a5, 0x071e, 0x54a4, 0x54a8, 0x54a6, 0x57a8, 0x578d, 0x5782,
  0x579e, 0x5789, 0x5783, 0x5791, 0x57a6, 0x579a, 0x5790, 0x5785,
  0x577d, 0xc3ec, 0x579b, 0x57a5, 0x57f0, 0x5796, 0x5788, 0x57d6,
  0x57d8, 0x5786, 0x57a4, 0x5797, 0x5a1c, 0x5a19, 0x5a9e, 0x5a9f,
  0x5a9d, 0x5a9c, 0x5b5d, 0x5b5e, 0x5b73, 0x5b63,
  /* 0x2f */
  0x5bbe, 0x5bc1, 0x5bbc, 0x5bbf, 0x5bbb, 0x5bbd, 0x5c13, 0x5c14,
  0x5cf8, 0x5cfa, 0x5d06, 0x5cfe, 0x5d51, 0x600b, 0x5ff1, 0x5fa9,
  0x5fd5, 0x5fdc, 0x5fcb, 0x5ff2, 0x605f, 0x5fdb, 0x5fd6, 0x5fd9,
  0x5fd1, 0x5fcf, 0x5fd8, 0x5fe0, 0x5fd4, 0x5ff3, 0x6005, 0x5fce,
  0x5ff4, 0xc412, 0x5fda, 0x600e, 0x6006, 0x5fd7, 0x5fcd, 0x6007,
  0x5fe1, 0x6008, 0x62be, 0x62ef, 0x62d9, 0x62da, 0x1fd7, 0x62a0,
  0x62df, 0x629e, 0x62bf, 0x62af, 0x62a7, 0x62aa, 0x62f4, 0x62ae,
  0x62b5, 0x62b8, 0x62db, 0x62c0, 0x62b7, 0x62a2, 0x62dd, 0x62a1,
  0x62a5, 0x62b4, 0x62a6, 0x62ab, 0x62ac, 0x629f, 0x62b1, 0x62fc,
  0x642e, 0x6430, 0x6499, 0x64f8, 0x64f6, 0x65f4, 0x65eb, 0x65e1,
  0x65e4, 0x6775, 0x6769, 0x0a9a, 0x6799, 0x679e, 0x6794, 0x6793,
  0x6791, 0x6919, 0x6917, 0x6949, 0x6947, 0x6948,
  /* 0x30 */
  0x6952, 0x6940, 0x6963, 0x6946, 0x695f, 0x6a0e, 0x6a85, 0x6a87,
  0x6acb, 0x6acc, 0x6ac9, 0x6bcb, 0x41e7, 0x6c20, 0x6d5f, 0x6d60,
  0x6d6a, 0x6d76, 0x6d73, 0x6d71, 0x6d66, 0x0bc2, 0x6d6b, 0x6d79,
  0x6d7b, 0x6e3c, 0x6e9e, 0x6e9c, 0x6f1c, 0x6f1e, 0x6f21, 0x6f96,
  0x6fa1, 0x6fb6, 0x6fa0, 0x6f94, 0x6f97, 0x7081, 0x7086, 0x70c0,
  0x708a, 0xc44b, 0x7085, 0x7095, 0x7049, 0x7082, 0x7084, 0x4b78,
  0x7090, 0x70b1, 0x71fe, 0x71fb, 0x7350, 0x7356, 0x735a, 0x734c,
  0x0cdc, 0x7357, 0x74e1, 0x74d9, 0x74db, 0x74f4, 0x7545, 0x7547,
  0x7674, 0x766b, 0x7668, 0x7669, 0x7679, 0xc45b, 0x7680, 0x7681,
  0x7661, 0x7670, 0x766f, 0x7673, 0x765d, 0x77fa, 0x0db3, 0x77f3,
  0x780a, 0x7817, 0xc45f, 0x78b0, 0x7a3e, 0x7a46, 0x7a4f, 0x7a44,
  0x7a69, 0x7a33, 0x7a2f, 0x7a4e, 0x7a3a, 0x7a2b,
  /* 0x31 */
  0x7aa9, 0x7a50, 0x7a45, 0x7a37, 0x7a25, 0x7a2c, 0x7a3f, 0x7a34,
  0x7a29, 0x7a1e, 0x7a3d, 0x7ced, 0xc466, 0x7cf3, 0x7cea, 0x7ceb,
  0x7ecc, 0x7ece, 0x7ed0, 0x7ee3, 0x7ee0, 0x7ed1, 0x7edc, 0x7edd,
  0x7ef0, 0x7edb, 0x7ee5, 0x7ef1, 0x7ec9, 0x7ee8, 0x7ee7, 0x7ec8,
  0x7ede, 0x7ecd, 0x7ec5, 0x7ec6, 0x7ee4, 0x7ec3, 0x80fb, 0x80fc,
  0x80fe, 0x8103, 0x8100, 0x80fd, 0x8105, 0x8113, 0x81a7, 0x81b4,
  0x8270, 0x8274, 0x8271, 0x8275, 0x827d, 0x8273, 0x82c4, 0x82c3,
  0x82de, 0x82dd, 0x8311, 0x830f, 0x8310, 0x83c3, 0x83c2, 0x83ca,
  0x83c1, 0x8423, 0x85b1, 0x8624, 0x8611, 0x8625, 0x860a, 0x861f,
  0x8620, 0x8614, 0x8628, 0x8603, 0x8612, 0x860b, 0x8617, 0x4d11,
  0x8749, 0x8789, 0x8790, 0x878f, 0x8796, 0x8795, 0x8793, 0x87ed,
  0x87f0, 0x62de, 0x8885, 0x8877, 0x887a, 0x8884,
  /* 0x32 */
  0x8879, 0x887d, 0x887b, 0x88ed, 0x88ec, 0x89f6, 0x899b, 0x8a9d,
  0x8b11, 0x8ac4, 0x8ac8, 0x8adf, 0x8abf, 0x8ab3, 0x8aba, 0x8b10,
  0x8b06, 0x8a88, 0x8a9a, 0x8ae0, 0x8acc, 0x8ab5, 0x8ae1, 0x8abc,
  0x8ac6, 0x8b0b, 0x8aa4, 0x8a95, 0x8aa3, 0x8ae2, 0x8acd, 0x8ae3,
  0x8aab, 0x8acb, 0x8a8f, 0xc4ab, 0x8aa9, 0x8b24, 0x8ae4, 0x8b12,
  0x8ae5, 0x8b67, 0x8aaa, 0x8aa0, 0x8ae6, 0x8ac1, 0x8ae7, 0x8b0d,
  0x8a86, 0x8ab0, 0x8a8b, 0x8ae8, 0x8ac9, 0x8b19, 0x8ac0, 0x8b0c,
  0x8ae9, 0x8aea, 0x8ded, 0x8de3, 0x8de4, 0x8de8, 0x8dd2, 0x8de2,
  0x2466, 0x8fcf, 0x8fd1, 0x8fc3, 0x8fc9, 0x8fea, 0x8fb4, 0x8fdc,
  0x8fbd, 0x8fe6, 0x8fc8, 0x8fec, 0x8fb2, 0x8fa9, 0x8fd3, 0x8fc0,
  0x8fe9, 0x8fd5, 0x11d9, 0x8fc5, 0x11dd, 0x8fcb, 0x8fd0, 0x8fd2,
  0x8fe4, 0x8fe8, 0x8fcd, 0x8fb6, 0x8faa, 0x8fd4,
  /* 0x33 */
  0xa049, 0x8fc1, 0x8fdd, 0x8fce, 0x91c9, 0x91ca, 0x92fd, 0x92d9,
  0x92ff, 0x9304, 0x92fa, 0x9306, 0x9315, 0x9311, 0x9307, 0x930b,
  0x93fc, 0x93fd, 0x946d, 0x9465, 0x9514, 0x9513, 0x950c, 0x950b,
  0x9518, 0x9522, 0x967d, 0x966f, 0x9675, 0x967b, 0x9680, 0x967f,
  0x9696, 0x966d, 0x966b, 0x9686, 0x9673, 0x9662, 0x9677, 0x9681,
  0x9669, 0x9682, 0x9697, 0x9684, 0x12fc, 0x9678, 0xc4d0, 0x967a,
  0x966a, 0x9665, 0x967e, 0x9694, 0x97c2, 0x97c1, 0x97f9, 0x9871,
  0x986b, 0x986d, 0x986f, 0x986e, 0x98f2, 0x98e8, 0x98ef, 0x98e9,
  0x98ea, 0x98ed, 0x98f3, 0x98e6, 0x99c9, 0x99b4, 0x99b3, 0x99b9,
  0x99ca, 0x99b1, 0x99b6, 0x99c7, 0x99c4, 0x99b7, 0x9a6f, 0x9b33,
  0x9b32, 0x9b1f, 0xc4d9, 0x9b2b, 0x9b30, 0x9b36, 0x9b42, 0x9b41,
  0xc4d8, 0x9ca5, 0x9cab, 0x9ca9, 0x9cb3, 0x9ca7,
  /* 0x34 */
  0x9ca0, 0x9cd2, 0x9ca8, 0x9cb6, 0x9cac, 0x9cae, 0x9ce6, 0x9e65,
  0x9f47, 0x9f63, 0x9f4d, 0x9f5f, 0x9f4b, 0x9f60, 0x9f49, 0x9f53,
  0xc4df, 0x263a, 0x9f57, 0x9f4e, 0x9f52, 0x9f54, 0xa020, 0xa022,
  0xa1c1, 0xa1d7, 0xa1d5, 0xa1c4, 0xa1d0, 0xa1bb, 0xa1e2, 0xa1cb,
  0xa1b8, 0xa1bf, 0xa1d8, 0xa1c0, 0xa1ba, 0xa1b4, 0xa1bc, 0xa1d4,
  0xa1ed, 0xa1c2, 0xa1d9, 0xa1cc, 0xa32f, 0xa323, 0xa396, 0xa38d,
  0xa39e, 0xa399, 0xa483, 0x1507, 0xa46a, 0xa469, 0xa475, 0xa46c,
  0xa480, 0xa46e, 0xa481, 0xa477, 0xa476, 0xa473, 0xa470, 0xa484,
  0xa519, 0xa5f2, 0x273d, 0xa5ec, 0xa5eb, 0xa5f6, 0xa5ef, 0xa5f4,
  0xa5ee, 0xa5f3, 0xa5ed, 0xa89b, 0xa898, 0xa894, 0x3a7a, 0xa89a,
  0xc4f0, 0xa94c, 0x15a8, 0xa957, 0xa951, 0xa962, 0xa952, 0xa95c,
  0xa953, 0xc4ef, 0xaa85, 0xab2d, 0xab3e, 0xab30,
  /* 0x35 */
  0xab7d, 0xabe6, 0xabf6, 0xabe4, 0xabe9, 0xabec, 0xabf2, 0xabe8,
  0xabe3, 0xabeb, 0xabf0, 0xabea, 0xabe7, 0xabfd, 0xabe5, 0xabee,
  0xabf5, 0xabf9, 0xabf1, 0xabf4, 0xc4f4, 0xac11, 0xacde, 0xacd8,
  0xacdd, 0xacdb, 0xacd3, 0xace1, 0xadde, 0xadfc, 0xae28, 0xaeb7,
  0xaeb8, 0xaec7, 0xaeb2, 0xaea9, 0xaeb9, 0xaebc, 0xaeb3, 0xaed1,
  0xafb0, 0xafb1, 0xafb6, 0xb004, 0xb02c, 0xb0c1, 0xb09e, 0xb0ba,
  0x16ca, 0xb0d1, 0xb0d3, 0xb0d4, 0xb0d5, 0xb0c5, 0xb0b6, 0xb0b2,
  0xb0b5, 0xb0c3, 0xb0b9, 0xb0c6, 0xb1d6, 0xb1e0, 0xb1db, 0xb1d7,
  0xb1dd, 0xb315, 0xb30c, 0xb30f, 0xb30e, 0xb2fe, 0xb304, 0xb30b,
  0xb302, 0xb2ff, 0xb308, 0xb310, 0xb317, 0xb313, 0xb306, 0xb309,
  0xb424, 0xb426, 0xb425, 0xb448, 0xb4b5, 0xb4d2, 0xb4d5, 0xb4c4,
  0xb4af, 0xb4ad, 0xb4c1, 0xb4c0, 0xb4cc, 0xb4cd,
  /* 0x36 */
  0xb4c3, 0xb4c8, 0xb4c5, 0xb4ba, 0xb4d0, 0xb4c2, 0xb4ce, 0x178d,
  0xb643, 0xb642, 0xb640, 0xb631, 0xb6cf, 0xb6ce, 0xb730, 0xb734,
  0xb732, 0xb743, 0xb73c, 0xb811, 0xb835, 0xb834, 0xb837, 0xb831,
  0xb8bf, 0xb8bc, 0xb8c2, 0xb8c9, 0xb983, 0xb98b, 0xb9a0, 0xb98d,
  0xb98c, 0xb99a, 0xb98a, 0xb991, 0xbbf1, 0xbc0f, 0xbc01, 0xbc07,
  0xbc0c, 0x18c0, 0xbbdc, 0xbbee, 0xbbf7, 0xbbf2, 0xbbf8, 0xbbeb,
  0x18ca, 0xbbe6, 0xbbed, 0xbbe9, 0xbc08, 0xbc00, 0xbbe5, 0xbbfc,
  0xbe61, 0xbe5e, 0xbe5f, 0x1925, 0xbe9b, 0xbf16, 0xbf15, 0xbf20,
  0xbf14, 0xbf1a, 0xbf17, 0xbf9c, 0x194a, 0xbff1, 0x1953, 0xbff0,
  0xc028, 0xc024, 0xc02a, 0xc02b, 0xc0d5, 0xc104, 0xc105, 0xc191,
  0xc192, 0xc2a6, 0x2c09, 0x2e22, 0x2e1a, 0x2e0e, 0x2e1b, 0x2e08,
  0x6fb3, 0xc31c, 0x309d, 0x3212, 0x321e, 0x32bb,
  /* 0x37 */
  0x32bf, 0x32bc, 0x3308, 0x3309, 0x3363, 0x33c8, 0x5828, 0x358e,
  0x358d, 0x3743, 0x374d, 0x376d, 0x3742, 0x3752, 0x3751, 0x3769,
  0x3750, 0x3756, 0x376c, 0x3744, 0x3745, 0x376b, 0x0205, 0x3768,
  0x3757, 0x392e, 0x3931, 0x392d, 0x3a8b, 0x3ab2, 0x3a8d, 0x3aa3,
  0x3aa4, 0x3a90, 0x3a89, 0x3a8e, 0x3a92, 0x3b7b, 0x3c90, 0x3c8f,
  0x3de9, 0x3dcd, 0x3dc5, 0x3dd0, 0x3dc9, 0x3dd1, 0x3dc7, 0x3dd2,
  0x3d5f, 0x3de1, 0x3dcc, 0x3dc6, 0x3de4, 0x3ea9, 0x3fd3, 0x3fda,
  0x3fd2, 0x3fdb, 0x404d, 0x404e, 0x411c, 0x41e8, 0x436c, 0x435f,
  0x4366, 0x4364, 0x4378, 0x4365, 0x436d, 0x4361, 0x437a, 0x4407,
  0x4566, 0x4568, 0x4562, 0x46d3, 0x46d4, 0x46d1, 0x46dc, 0x4773,
  0x4772, 0x482d, 0x482c, 0x482f, 0x4872, 0x48b4, 0x4989, 0x4bcd,
  0x4ba6, 0x4ba2, 0x4ba0, 0x4b46, 0x4ba1, 0x8657,
  /* 0x38 */
  0x4ba3, 0x4bb1, 0x4bdb, 0x4bf6, 0x4bdc, 0x4bd6, 0x4d13, 0x4f9c,
  0x4f97, 0x4fd8, 0x4fe4, 0x4fd4, 0x4fe5, 0x4fdb, 0x4fd0, 0x4fda,
  0x4fcc, 0x4fdc, 0x4fed, 0x4fd3, 0x4fd1, 0x4fce, 0x4fd9, 0x4fdd,
  0xc3c6, 0x50e6, 0x50e5, 0x50e9, 0x51ff, 0x51fe, 0x520e, 0x528c,
  0x52ca, 0x52cb, 0x531a, 0x54d7, 0x54cf, 0x54d0, 0x54c9, 0x54cc,
  0x54d3, 0x55a4, 0x55a3, 0x5781, 0x5826, 0x57f3, 0x5827, 0x57f2,
  0x57ff, 0x57f5, 0x57fc, 0x580e, 0x07f0, 0x57f6, 0x5800, 0x5823,
  0x5805, 0x5825, 0x5808, 0x5850, 0x5a25, 0x5a20, 0x5a23, 0x5a21,
  0x5aa1, 0x5b69, 0x5b6c, 0x5b68, 0x5b6b, 0x5bcc, 0x5bcd, 0x5d10,
  0x5d0d, 0x5d0a, 0x5d16, 0x5d14, 0x5d52, 0x6028, 0x602e, 0x602c,
  0x604d, 0x6049, 0x6031, 0x6030, 0x6033, 0x602d, 0x6036, 0x603e,
  0x602f, 0x6027, 0x6034, 0x604c, 0x62fe, 0x6312,
  /* 0x39 */
  0x631f, 0x6317, 0x62f5, 0x6315, 0x62f7, 0x0a02, 0x437d, 0x62fa,
  0x62f9, 0x634b, 0x649c, 0x649d, 0x0a23, 0x65f0, 0x65f6, 0x65ef,
  0x679d, 0x679a, 0x67a7, 0x67af, 0x67aa, 0x6964, 0x6986, 0x6975,
  0x6970, 0x6984, 0x696b, 0x6985, 0x696c, 0x6a17, 0x6a94, 0x6a93,
  0x6bd2, 0x6bd7, 0x6bd4, 0x6da6, 0x6da7, 0x6d9c, 0x6d8b, 0x6d8d,
  0x6d98, 0x6db9, 0x6d9b, 0x6d9d, 0x6d99, 0x6da8, 0x6d91, 0x6d87,
  0x6d9a, 0x6ea6, 0x6f1f, 0x6fa7, 0x6fb1, 0x6fb2, 0x6fb7, 0x70d0,
  0x70b3, 0x70b5, 0x70c4, 0x70c3, 0x70bc, 0x70b2, 0x70ba, 0x70bb,
  0x70c2, 0x70cd, 0x70be, 0x70b7, 0x718f, 0x7203, 0x7204, 0x7371,
  0x7377, 0x7374, 0x738b, 0x737a, 0xc451, 0x738c, 0x7373, 0x74ff,
  0x74fb, 0x74fd, 0x74f0, 0x74f3, 0x74fc, 0x74f2, 0x7692, 0x769e,
  0x76ae, 0x7696, 0x7814, 0x7812, 0x7813, 0x7816,
  /* 0x3a */
  0x780f, 0x78b6, 0x78bd, 0x7a8c, 0x7aae, 0x7aac, 0x7aab, 0x7a99,
  0x7a92, 0x7abb, 0x7a9e, 0x7a7e, 0x7aaf, 0x7abc, 0x7a98, 0x7d01,
  0x7d09, 0x7d06, 0xc467, 0x7d07, 0x7d08, 0x7ecf, 0x7f0e, 0x7f32,
  0x0f15, 0x7f12, 0x7f16, 0x7f17, 0x7f1b, 0x7f15, 0x7f31, 0x7f18,
  0x7f1a, 0x7f10, 0x7f0a, 0x7f09, 0x804f, 0xc473, 0x810f, 0x8110,
  0xc475, 0x8128, 0x8111, 0x8116, 0x8117, 0x8102, 0x81bb, 0x81ba,
  0x81c3, 0x81bc, 0x828a, 0x8284, 0x8286, 0x82e0, 0x8317, 0x8318,
  0x831e, 0x8315, 0x83d3, 0x83da, 0x83d9, 0x85e6, 0x85f4, 0x85e1,
  0x8669, 0x8640, 0x8658, 0x866c, 0x864d, 0x8721, 0x8799, 0x87f1,
  0x5f68, 0x8886, 0x8b5b, 0x8b5c, 0x8b77, 0x8b2c, 0xc4ad, 0x8b58,
  0x8b64, 0x8b61, 0x8b48, 0x8b97, 0x8b59, 0x8b29, 0x8b62, 0x8b2e,
  0x8b68, 0x8b90, 0x8b3a, 0x8b3d, 0x8b5e, 0x8b46,
  /* 0x3b */
  0x8b69, 0x8b65, 0x8b3e, 0x8b49, 0x8b56, 0x8be1, 0x8b78, 0x8b79,
  0x8b66, 0x8b4a, 0x8b35, 0x8b7a, 0x8b92, 0x8b60, 0x8b36, 0x8b51,
  0x8b42, 0x115d, 0x8b3f, 0x8b7b, 0x8b5d, 0x8b94, 0x8b6a, 0xc4b5,
  0x8df2, 0x8fbb, 0x901b, 0x901a, 0x9033, 0x9017, 0x900a, 0x9015,
  0x9012, 0x9001, 0x902d, 0x8ffd, 0x9023, 0x9005, 0x9011, 0x9000,
  0x901c, 0x9035, 0x902e, 0x9036, 0x34bf, 0x902f, 0x900c, 0x9009,
  0x9031, 0x8ffc, 0x900f, 0x9018, 0x9002, 0x9200, 0x931f, 0x9337,
  0x125a, 0x9338, 0x932b, 0x932e, 0x9321, 0x9330, 0x9329, 0x9331,
  0xc4c9, 0x9301, 0x932c, 0x9322, 0x93ff, 0x9477, 0x9467, 0x947f,
  0x947d, 0x947b, 0x947e, 0x951e, 0x951c, 0x9521, 0x9526, 0x9527,
  0x9529, 0x952c, 0x951d, 0x952b, 0x96bf, 0x96a4, 0x96aa, 0x96ae,
  0x969f, 0x96d0, 0x96b1, 0x96ad, 0x969b, 0x96b2,
  /* 0x3c */
  0x96a9, 0x96b3, 0x96b4, 0x96ba, 0x96a5, 0x96b7, 0x96ac, 0x96cb,
  0x96cf, 0x97c6, 0x9801, 0x97ff, 0x97fd, 0x9877, 0x9878, 0x9876,
  0x98f7, 0x99cc, 0x1385, 0x99d4, 0x99d7, 0x99d5, 0x99d6, 0x99d3,
  0x9b5d, 0x9b55, 0x9ce0, 0x9d48, 0x9cee, 0x9cdb, 0x9ce7, 0x9cd6,
  0x9ce5, 0x9ce1, 0x9cdd, 0x9ce2, 0x9e70, 0x9e66, 0x9e6f, 0x9e6e,
  0x9f81, 0x9f69, 0x9f6e, 0x9f6d, 0x9f6c, 0x9f84, 0x9f85, 0x9f71,
  0x9f73, 0x9f6a, 0x9f6f, 0x9f7b, 0xa16a, 0xa17c, 0xa17d, 0xa181,
  0xa1fa, 0xa205, 0xa1eb, 0xa1fb, 0xa1e9, 0xa1ef, 0xa1fc, 0xa1e7,
  0xc4e1, 0xa1ee, 0xa1fd, 0xa332, 0xa3a7, 0xa3b5, 0xa3b1, 0xa3b9,
  0xa3a8, 0xa3b3, 0xc4e6, 0xa48a, 0xa491, 0xa48d, 0xa499, 0x150b,
  0xa49a, 0xa49b, 0xa492, 0xa48f, 0xa4ab, 0x6bdb, 0x1539, 0xa675,
  0xa631, 0xa638, 0x1537, 0xa635, 0xa669, 0xa63b,
  /* 0x3d */
  0xa63d, 0xa66c, 0xa679, 0xa63c, 0xa63e, 0xa897, 0xa8a5, 0xa8a2,
  0xa89d, 0xa8a1, 0xa968, 0xa96f, 0xa96d, 0xa972, 0xa975, 0xa977,
  0xa979, 0xaab5, 0xaaea, 0xaaab, 0xab43, 0xab41, 0xab42, 0xac09,
  0xac08, 0xac06, 0xac01, 0xac03, 0xac00, 0xac04, 0xac0a, 0xac0e,
  0xac0d, 0xac07, 0xac0f, 0xac14, 0xac02, 0xac15, 0xac0c, 0xac10,
  0xac05, 0xacfd, 0xacff, 0xad04, 0xad00, 0xad09, 0xae2b, 0xc4f7,
  0xae31, 0xaedb, 0xaec5, 0xaed3, 0xaece, 0x166b, 0xaec9, 0xaebf,
  0xaecb, 0xaec0, 0xaed0, 0xaed4, 0xafc1, 0xafb9, 0xafbb, 0xafc3,
  0xafc9, 0xb007, 0xb02d, 0xb0f8, 0xb0e1, 0xb0fa, 0xb0ef, 0xb0fd,
  0x16cd, 0xb0eb, 0xb0f1, 0xb0ed, 0xb0fe, 0xb1f8, 0xb203, 0xb1ee,
  0xb1e8, 0xb201, 0xb2ec, 0xb322, 0xb314, 0xb334, 0xb32f, 0xb339,
  0xb341, 0xb33c, 0xb349, 0xb358, 0xb33a, 0xb342,
  /* 0x3e */
  0xb33f, 0xb422, 0xb423, 0xb44a, 0xb4dc, 0xb4d9, 0xb4db, 0xb4e2,
  0xc507, 0xb4df, 0xb4e0, 0xb4d7, 0xb64f, 0xb646, 0xb653, 0xb655,
  0xb64e, 0xb64a, 0xb64c, 0xb663, 0xb751, 0xb753, 0xb758, 0xb74d,
  0xb75a, 0xb749, 0xb75d, 0xb812, 0xb83c, 0xb8d1, 0xb8df, 0xb8d6,
  0xb8d8, 0xb8e0, 0xb8d9, 0xb9b1, 0xb9ac, 0xb9aa, 0xb9ee, 0xb9bd,
  0x184d, 0xb9c3, 0xb9a8, 0xb9ae, 0xb9ab, 0xbc1d, 0xbc27, 0xbc38,
  0xbc12, 0xbc48, 0xbc2b, 0xbc16, 0xbc19, 0xbc3d, 0xbc23, 0xbc2a,
  0xbe64, 0xbead, 0xbeac, 0xc514, 0xbeb1, 0xbeaf, 0xbf2c, 0xbf24,
  0xbf25, 0xbf28, 0xbff9, 0xbff7, 0xbffd, 0xbffe, 0xc039, 0xc033,
  0xc0d7, 0xc0d8, 0xc0e4, 0x3aa1, 0xc10e, 0xc13b, 0xc144, 0xc142,
  0xc194, 0xc193, 0xc1d5, 0xc2a7, 0x2e31, 0x2e23, 0x2e28, 0x2e27,
  0x2ec6, 0x2fa3, 0x3021, 0x321b, 0x0110, 0x32c1,
  /* 0x3f */
  0x32c3, 0x332a, 0x3369, 0x3427, 0x37b6, 0x37a7, 0x37a4, 0x37a6,
  0x3790, 0x379e, 0x3794, 0x37a8, 0x37a5, 0x37a2, 0x3791, 0x027b,
  0x3abc, 0x3abd, 0x3ab4, 0x3ab0, 0x3ae4, 0x3b45, 0x3b4b, 0x3b7e,
  0x3b7f, 0x3b7d, 0x3bc3, 0x3dfc, 0x3df7, 0x3df0, 0x3ded, 0x3df1,
  0x3df8, 0x3fe9, 0x41eb, 0x041d, 0x4390, 0x438d, 0x4386, 0x4391,
  0x438a, 0x4408, 0x4450, 0x46ea, 0x46e6, 0x46e2, 0x46e7, 0x46ed,
  0x46e1, 0x4834, 0x4876, 0x4875, 0x4873, 0x48b5, 0x4990, 0x4992,
  0x4be1, 0x4bdf, 0x4bd5, 0x4bf2, 0x4bfe, 0x4c13, 0x4c2e, 0x4d19,
  0x5008, 0x1df5, 0x5005, 0x5009, 0x5006, 0x5003, 0x4ffd, 0x4ffc,
  0x5002, 0x5042, 0x521a, 0x5211, 0x5215, 0x5216, 0x52cc, 0x52cf,
  0x52d0, 0x5322, 0x531e, 0x5321, 0x54e5, 0x0727, 0x5554, 0x54ef,
  0x5553, 0x5551, 0x55ad, 0x5867, 0x5868, 0x58a4,
  /* 0x40 */
  0x5877, 0x5889, 0x5844, 0x588b, 0x5879, 0x585b, 0x5843, 0x5857,
  0x584a, 0x587c, 0x5846, 0x587b, 0x5856, 0x5aa8, 0x5b76, 0x5b72,
  0x5bd6, 0x5bd8, 0x5bd1, 0x5d22, 0x5d20, 0x5d23, 0x5d1e, 0x5d6e,
  0x60a3, 0x6077, 0x60a6, 0x606d, 0x60a2, 0x607c, 0x6084, 0x6068,
  0x6074, 0x6086, 0x60a5, 0x607b, 0x607a, 0x6069, 0x6072, 0x6076,
  0x634a, 0x6337, 0x632a, 0x632d, 0x6346, 0x6328, 0x6326, 0x6342,
  0x632c, 0x6338, 0x632b, 0x6333, 0x6345, 0x6439, 0x65f9, 0x65fa,
  0x67b8, 0x67b7, 0x67bb, 0x67b9, 0x67b4, 0x696f, 0x6987, 0x698f,
  0x69a2, 0x69a3, 0xc431, 0x6a9b, 0x6a9d, 0x6ace, 0x0bcf, 0x6dbd,
  0x6dbf, 0x6d92, 0x0bcd, 0x6def, 0x6dc9, 0x6ea4, 0x6ea8, 0x6eaa,
  0x6f28, 0x6f24, 0x6f25, 0x6f26, 0x6fa9, 0x6fba, 0x6fbe, 0x6fbc,
  0x6fc0, 0x70f0, 0x70df, 0x70e0, 0x70ed, 0x70db,
  /* 0x41 */
  0x70fb, 0x70b9, 0x70da, 0x70eb, 0x70ec, 0x739a, 0x739f, 0x739b,
  0x7397, 0x73a1, 0x750f, 0x7505, 0x7548, 0x0d82, 0x76bc, 0x76ba,
  0x78bf, 0x7b01, 0x7ae8, 0x7aef, 0x7ae4, 0x7ae6, 0x7b02, 0x7aeb,
  0x7ae0, 0x7aed, 0x7ad9, 0xc464, 0x7b14, 0x7aee, 0x0e52, 0x7b13,
  0x7af9, 0x7af8, 0x7d25, 0xc469, 0x7d19, 0x7d20, 0x7d43, 0x7d3f,
  0x7f45, 0x7f4c, 0x7f49, 0x7f4f, 0x7f41, 0x7f3e, 0x7f4d, 0x7f52,
  0x7f4a, 0x7f4e, 0x7f73, 0x7f42, 0x7f51, 0x7f55, 0x7f50, 0x7f6c,
  0x2afd, 0x7f6a, 0x7f53, 0x7f68, 0x8055, 0x8056, 0x811c, 0x811d,
  0x2280, 0x811e, 0x8123, 0x811f, 0x81e1, 0x81cd, 0x81cb, 0x81cc,
  0x81c8, 0x81c9, 0x829b, 0x8294, 0x8292, 0x8296, 0x8293, 0x8295,
  0x828f, 0x831d, 0x8322, 0x8321, 0x83e9, 0x83ef, 0x83e0, 0x83e6,
  0x83e4, 0x8629, 0x862c, 0x8676, 0x8683, 0x8678,
  /* 0x42 */
  0x863c, 0x6343, 0x867a, 0x1051, 0x86f2, 0x879e, 0x879b, 0x879a,
  0x87f6, 0x87f5, 0x88a5, 0x8893, 0x88a4, 0x8a82, 0x8ac7, 0x8bb7,
  0x8c1d, 0x8be2, 0x8bd7, 0x8be3, 0x8be4, 0x8bbc, 0x8bd3, 0x115a,
  0x8b5a, 0x8bd2, 0x8b2d, 0xc4af, 0x8bc4, 0x8bd0, 0x8be5, 0x8c05,
  0x8c07, 0x8be6, 0x8c1b, 0x8be7, 0x8bd8, 0x8bbe, 0x8c17, 0x8bb4,
  0x8bd9, 0x8be8, 0x8bad, 0x8baf, 0x8bc8, 0x8be9, 0x8bea, 0x8dfe,
  0x8dfb, 0x8e00, 0x9072, 0x9070, 0x9046, 0x9059, 0x905e, 0x9048,
  0x904f, 0x9071, 0x9060, 0x905f, 0x906e, 0x9073, 0xc4c0, 0xc4bf,
  0x9047, 0x906d, 0x906f, 0x9081, 0x906c, 0x9078, 0x9083, 0x9049,
  0x9068, 0x9074, 0x9063, 0x906a, 0x8685, 0x9065, 0x9062, 0x90c8,
  0x91d0, 0x91d4, 0x91d1, 0x9203, 0x9342, 0x9363, 0x9356, 0x935b,
  0x9355, 0x9350, 0x932d, 0x9344, 0x9348, 0x9345,
  /* 0x43 */
  0x9382, 0x1265, 0x9362, 0x9485, 0x948d, 0x9536, 0x952f, 0x9531,
  0x9537, 0x96a7, 0x96d9, 0x96f0, 0x96f2, 0x96fd, 0x96e8, 0x96eb,
  0x96ee, 0x96e0, 0x96e9, 0x96ed, 0x96d6, 0x96f8, 0x96d4, 0x96df,
  0x96e7, 0x96d8, 0x96e3, 0x96ef, 0x970f, 0x97ca, 0x3b46, 0x9805,
  0x980c, 0x980d, 0x987f, 0x9880, 0x9881, 0x9901, 0x9903, 0x99f2,
  0x99e2, 0x99e3, 0x99de, 0x99e9, 0x99e8, 0x99e0, 0x9a01, 0x99f5,
  0x99e4, 0x2501, 0x9a77, 0x9b74, 0x9b6f, 0x9b62, 0x9b61, 0x9b6d,
  0x9b73, 0x9b6a, 0x9b69, 0x9d12, 0x9d2d, 0x9d14, 0x9d0f, 0x9d29,
  0x9d16, 0x9d03, 0x9d46, 0x9d5c, 0x9d11, 0x9d06, 0x9cdc, 0x9d2b,
  0x9d2a, 0x9d2c, 0x9d27, 0x9e7a, 0x9f9c, 0x9f99, 0x9f95, 0x9f8b,
  0x9f98, 0x9f96, 0xa032, 0xa1a4, 0xa1aa, 0xa21b, 0x14af, 0xa20d,
  0xa21c, 0xa20a, 0xa220, 0xa208, 0xa21a, 0xa213,
  /* 0x44 */
  0xa211, 0xa35d, 0xa35f, 0xa35e, 0xa360, 0xa3bb, 0xa3bc, 0xa3c1,
  0xa3c0, 0xa3c8, 0xa3ce, 0xa4a7, 0xa4b2, 0xa4b6, 0xa4a5, 0xa4ba,
  0xa4b5, 0xa4ad, 0xa4a4, 0xa4d3, 0xa4b0, 0xa4b1, 0xa51d, 0xa68d,
  0x1541, 0xa691, 0xa6b6, 0xa6b7, 0xa6bd, 0xa6bc, 0xa696, 0xa694,
  0xa6a0, 0xa8a8, 0xa8a6, 0xa984, 0xa996, 0xa988, 0xa99a, 0xaad1,
  0xaacf, 0xab50, 0xab51, 0xab4e, 0xab80, 0xab81, 0xac1b, 0xac17,
  0xac20, 0xac19, 0xac1a, 0xac21, 0xac1e, 0xac18, 0xac1d, 0x1629,
  0xad2d, 0xad24, 0xad27, 0xad2e, 0xad25, 0xad1c, 0xad19, 0x162a,
  0xad23, 0xad1f, 0xad1a, 0xad2b, 0xad1e, 0xade0, 0xae33, 0xaee6,
  0xaefc, 0xaee5, 0xaef8, 0xaef6, 0xaeea, 0xaef2, 0xaeed, 0xaeeb,
  0xaef0, 0xaef1, 0xafc6, 0xafc8, 0xafce, 0xafc5, 0x1696, 0xafcb,
  0xb113, 0xb114, 0xb107, 0xb10c, 0xb21a, 0x1712,
  /* 0x45 */
  0xb217, 0xb206, 0xb216, 0xb207, 0xb210, 0xb209, 0xb219, 0xb215,
  0xb36e, 0xb33b, 0xb33e, 0xb36c, 0xb365, 0xb364, 0xb359, 0xb37c,
  0xb370, 0xb379, 0xb42c, 0xb452, 0xb451, 0xb44c, 0xb500, 0xb510,
  0xb513, 0xb4ff, 0xb4fe, 0xb4ed, 0xb65a, 0xb658, 0xb65c, 0xb6da,
  0xb778, 0xb75e, 0xb767, 0xb764, 0xb813, 0xb823, 0xb841, 0xb83f,
  0xb840, 0xb8ed, 0xb8e3, 0xb8ea, 0xb8f0, 0xb8e6, 0xb8e9, 0xb8f1,
  0xb8ee, 0xb9d4, 0xb9d1, 0xb9dc, 0xb9ec, 0xbc69, 0xbc6d, 0xbc57,
  0xbc66, 0xbcf9, 0xbc4a, 0xbc60, 0xbc56, 0xbc59, 0xbc4c, 0xbc6a,
  0xbc62, 0xbc63, 0xbc70, 0xbc5f, 0xc50d, 0xbc64, 0xbc5d, 0xbc68,
  0xbc9f, 0xbeba, 0xbeb8, 0xbebc, 0xbeb9, 0xbeb4, 0xbf3b, 0xbf2d,
  0xbf38, 0xbf2f, 0xbf32, 0xc041, 0xc0cb, 0xc0de, 0xc0dd, 0xc0da,
  0xc0dc, 0xc110, 0xc14f, 0xc149, 0xc198, 0xc196,
  /* 0x46 */
  0xc197, 0xc1c7, 0x2c9c, 0xc1da, 0xc1d8, 0xc2a8, 0x2c0a, 0x2c9d,
  0x2ecb, 0x2f38, 0x2f39, 0x2fa6, 0x3223, 0x3222, 0x3221, 0x33ce,
  0x3592, 0x3591, 0x37ec, 0x37e0, 0x37ed, 0x3808, 0x37e5, 0x37ee,
  0x37e4, 0x37eb, 0x37e3, 0x37ea, 0x380a, 0xc359, 0x3ad1, 0x3ae3,
  0x3ad4, 0x3ad0, 0x3ad9, 0x027e, 0x1be1, 0x3ada, 0x3ad3, 0x3b4c,
  0x3b4d, 0x3b7c, 0x3b80, 0x3bcc, 0x3dff, 0x3e08, 0xc108, 0x3e01,
  0xc36b, 0x3e00, 0x3fed, 0x3ff3, 0x3fee, 0x3ff1, 0x3ff0, 0x3fde,
  0x4051, 0x4382, 0x43a9, 0x4398, 0x439d, 0x439a, 0x439e, 0x439f,
  0x43a6, 0x43a7, 0x4409, 0x442f, 0x4571, 0x456d, 0x4572, 0x46ef,
  0x46f0, 0x483b, 0x4839, 0x483c, 0x4838, 0x6afd, 0x483a, 0x4878,
  0x4879, 0x4877, 0x4998, 0x499c, 0x4999, 0x499a, 0x4c11, 0x4c0a,
  0x4bfd, 0x4c0f, 0x4c19, 0x4c03, 0x4c15, 0x4c0c,
  /* 0x47 */
  0x4c09, 0x4c12, 0x4c34, 0x4c2a, 0x4c08, 0x4c2d, 0x4c28, 0xc3b1,
  0x4c2c, 0x4c26, 0x4c33, 0x05a7, 0x4d1a, 0x4d1e, 0x5007, 0x502c,
  0x5032, 0x5028, 0x5031, 0x5029, 0x5030, 0x502a, 0x5044, 0x502e,
  0x52d1, 0x5324, 0x54f7, 0x54f4, 0x54f3, 0x54f8, 0x58b5, 0x5896,
  0x5898, 0x5895, 0x5891, 0x58b2, 0x589e, 0x5859, 0x58a3, 0x589a,
  0x589b, 0x0f20, 0x7f83, 0x5bda, 0x5bdf, 0x5c16, 0x5d1f, 0x5d2d,
  0x5d2e, 0x5d2b, 0x60b8, 0x60bb, 0x60bf, 0x60ba, 0x60d5, 0x60e3,
  0x60c1, 0x60be, 0x60bd, 0x60b4, 0x60c2, 0x60a1, 0x6087, 0x60d7,
  0x60ca, 0x60b5, 0x60da, 0x60d9, 0x60b3, 0x60d8, 0x6367, 0x6371,
  0x6362, 0x635c, 0x6368, 0x6352, 0x6356, 0x3809, 0x2e42, 0x64a0,
  0x6600, 0x65fe, 0x65ff, 0x67cb, 0xc428, 0x67ca, 0x67a9, 0x67c8,
  0x69b4, 0x69ac, 0x69aa, 0x69a9, 0x6b0e, 0x6be9,
  /* 0x48 */
  0x6bed, 0x6bf2, 0x6beb, 0x6bee, 0x6de8, 0x6ddb, 0x6dd7, 0x6de3,
  0x6de5, 0x6dee, 0x6dd5, 0x6eb3, 0x6f2d, 0x6fc1, 0x6fc3, 0x710c,
  0x710e, 0x7107, 0x7117, 0x7109, 0x7116, 0x719a, 0x719c, 0x73b4,
  0x73b7, 0x73b3, 0x3b4e, 0x7513, 0x7514, 0x76e6, 0x76dc, 0x76e8,
  0x76e5, 0x782e, 0x782c, 0x782b, 0x78cd, 0x7b3d, 0x7b32, 0x7b2d,
  0x7b45, 0x7b3e, 0x7b50, 0x7b25, 0x7b53, 0x7b23, 0x7d37, 0x7d38,
  0x7d47, 0x7d3d, 0x7d3e, 0x7d49, 0x7d4a, 0x7d1d, 0x21e9, 0x7fa5,
  0x7f8c, 0x7f8d, 0x7f89, 0x7f96, 0x7f85, 0x7f8f, 0x7f77, 0x7f8e,
  0x7f82, 0x7f8a, 0x7f88, 0x7f7b, 0x7f97, 0x7f7d, 0x7f79, 0x8059,
  0x8124, 0x812d, 0x812e, 0x812b, 0xc476, 0x81da, 0x81d8, 0x81d6,
  0x8287, 0x82a0, 0x8328, 0x8325, 0x831f, 0x83f3, 0x83f7, 0x83f6,
  0x862b, 0x865b, 0x8648, 0x23cb, 0x865c, 0x866d,
  /* 0x49 */
  0x869d, 0x8699, 0x868c, 0x8691, 0x869b, 0x869a, 0x869c, 0x8695,
  0x868d, 0x8696, 0x86a5, 0x872a, 0x87a1, 0x87a4, 0x87ad, 0x88a9,
  0x88ae, 0x88b0, 0x8c0d, 0x8b63, 0x8b71, 0x8c51, 0x8c54, 0x8c2a,
  0x8c44, 0x8c55, 0x8c99, 0x8c39, 0x8c3f, 0x8c3e, 0x8c4f, 0x8c4d,
  0x8c35, 0x8c40, 0x8c31, 0x8bd5, 0x8c2b, 0x8c33, 0x8c41, 0x8c56,
  0x8c4c, 0x8c46, 0x8c3c, 0x8c45, 0x8c43, 0x8c3d, 0x8c70, 0x8c57,
  0x8c38, 0x8c58, 0x1165, 0x8c37, 0x8e07, 0x8e06, 0x8e09, 0x90ab,
  0x9090, 0x9093, 0x90bc, 0x90a9, 0x909e, 0x90bf, 0x90aa, 0x9091,
  0x90a4, 0x909a, 0x90a7, 0x90a1, 0x909c, 0x90a2, 0x909b, 0x909f,
  0x9094, 0x908f, 0x8ef0, 0x9092, 0x9095, 0x90a5, 0x90a6, 0x9204,
  0x939c, 0x9379, 0x937a, 0x937e, 0x937b, 0x9371, 0x9381, 0x937f,
  0x937c, 0x937d, 0x9375, 0x9376, 0x948e, 0x948f,
  /* 0x4a */
  0x953e, 0x953f, 0x9540, 0x9541, 0x1304, 0x970d, 0x9717, 0x9710,
  0x970e, 0x96ea, 0x971d, 0x9703, 0x9722, 0x9704, 0x9700, 0x9720,
  0x9721, 0x9723, 0x9713, 0x9709, 0x9711, 0x97cb, 0x97ce, 0x97d0,
  0x97cc, 0x97d4, 0x3adb, 0x9809, 0x980b, 0x9885, 0x9906, 0x990d,
  0x1364, 0x9914, 0x990f, 0x9a09, 0x9a14, 0x9a0b, 0x99fc, 0x9a04,
  0x9a0a, 0x9a00, 0x99fd, 0x9a07, 0x9a06, 0x9a11, 0x9a79, 0x9a78,
  0x9b88, 0x9b80, 0x9b8b, 0x9d59, 0x9d61, 0x9d75, 0x1423, 0x9d55,
  0x9d5b, 0x9d5f, 0x9d52, 0x9d62, 0x9d72, 0x9d5d, 0x9d68, 0x9d71,
  0x9d65, 0x9d66, 0x9d67, 0x9d76, 0x9d4c, 0x9d60, 0x9d74, 0x9d50,
  0x9e8a, 0x9e81, 0x9e86, 0x9e7f, 0x9e80, 0x9fa5, 0x9fa7, 0x9fa8,
  0x9fa6, 0x9faf, 0x7f95, 0x9fb1, 0xa035, 0xa039, 0xa1c3, 0xa230,
  0xa22a, 0xa22b, 0xa22d, 0xa22e, 0xa22c, 0xa223,
  /* 0x4b */
  0xa221, 0xa222, 0xa36c, 0xa381, 0xa38f, 0xa380, 0xa3d0, 0xa3cd,
  0xa3d5, 0xa3d4, 0xa4d1, 0xa4be, 0xa4cb, 0xa4ce, 0xa4bd, 0xa4d0,
  0xa704, 0xa6d5, 0xa6d0, 0xa6d3, 0xa6fb, 0xa6d8, 0xa6d1, 0xa6fd,
  0xa6d9, 0xa6d6, 0xa6e6, 0xa6f9, 0xa9a1, 0xa99d, 0xa99e, 0x28d9,
  0xaaff, 0xab5f, 0xab57, 0xab60, 0xab59, 0xac2c, 0xac25, 0xac27,
  0xac30, 0xac24, 0xac26, 0xac2d, 0xac2e, 0xac29, 0xac31, 0xad45,
  0xad47, 0xad52, 0xad4a, 0xad50, 0xad46, 0xad4f, 0xad4e, 0xad53,
  0xaf21, 0xaf09, 0xaf1a, 0xaf1b, 0x7115, 0xaf10, 0xc4f9, 0xaf14,
  0xaf0e, 0xaf12, 0xaf0b, 0xafcf, 0xafd2, 0xafd0, 0xafd4, 0xafd3,
  0xafd1, 0x3518, 0xc4fb, 0xb009, 0xb11c, 0xb127, 0xb125, 0xb11b,
  0xb129, 0xb11f, 0xb130, 0xb124, 0xb128, 0xb119, 0xb12f, 0xb224,
  0xb221, 0xb225, 0xb226, 0xb227, 0xb276, 0xb366,
  /* 0x4c */
  0xb375, 0xb369, 0xb37e, 0xb38f, 0xb374, 0x8e10, 0xb3ad, 0xb42b,
  0xb42a, 0xb458, 0xb522, 0xb51d, 0xb52b, 0xb52c, 0xb52d, 0xb533,
  0xb51b, 0xb527, 0xb52a, 0xb528, 0xb53b, 0xb67e, 0xb671, 0xb679,
  0xb678, 0xb670, 0xb66d, 0xb67d, 0xb675, 0xb676, 0xb6de, 0xb766,
  0xb783, 0xb787, 0xb77b, 0xb789, 0xb786, 0xb782, 0xb77c, 0xb781,
  0xb843, 0xb845, 0xb8f7, 0xb8f9, 0xb8fa, 0xba05, 0xb9fe, 0xba0f,
  0xb9ff, 0xb9fa, 0xba09, 0xba20, 0xba0c, 0xba3c, 0xba22, 0xb9f8,
  0xba0a, 0xba08, 0xb9f7, 0xbc8e, 0xbc77, 0xbc8b, 0xbcb4, 0xbc8a,
  0xbc9a, 0xbc79, 0xbc83, 0xbc7f, 0xbca1, 0xbc8f, 0xbca3, 0xbc81,
  0xbc94, 0xbc7e, 0xbc82, 0xbc90, 0xbca5, 0xbcad, 0xbc9d, 0xbe67,
  0xbe69, 0xbecb, 0xbec8, 0xbed1, 0xbf40, 0xbf4b, 0xbf49, 0xbf46,
  0xbf3e, 0xbf43, 0xbf3f, 0xbfa5, 0xbfa7, 0xc04e,
  /* 0x4d */
  0xc04d, 0x499b, 0xc0e5, 0xc0e1, 0xc0e2, 0xc116, 0xc114, 0xc51b,
  0xc159, 0xc151, 0xc15f, 0xc14a, 0xc157, 0xc158, 0xc1ca, 0xc1db,
  0xc1de, 0xc1e0, 0xc1e1, 0xc1df, 0xc1e2, 0xc1e3, 0xc292, 0xc2bf,
  0xc2be, 0x2c0b, 0x2e52, 0x2e4e, 0x00b9, 0xc313, 0x2fa7, 0x3226,
  0x3227, 0x32c6, 0x330b, 0x336a, 0x3378, 0x381a, 0x3816, 0x3819,
  0x3817, 0x381b, 0x3818, 0x3820, 0x3937, 0x3aec, 0x3b81, 0xae3d,
  0x3e0f, 0x3ead, 0x3ffb, 0x4052, 0x43af, 0x43b7, 0x43b2, 0x4578,
  0x45ac, 0x4700, 0x46fe, 0x4702, 0x46fd, 0x4703, 0x4840, 0x4843,
  0x4842, 0x48b7, 0x49a2, 0x4c00, 0x4c35, 0x4c41, 0x4c05, 0x2e53,
  0x4c50, 0x4c4e, 0x4c53, 0x5053, 0x5050, 0x5057, 0x505f, 0x5055,
  0x50ea, 0x5226, 0xb430, 0x522a, 0x5228, 0x522c, 0x522d, 0x52d4,
  0x5507, 0x5558, 0x5559, 0x58c5, 0x58cd, 0x58c7,
  /* 0x4e */
  0x58e8, 0x084b, 0x5a32, 0xc297, 0x5bde, 0x5d32, 0x34c8, 0xc415,
  0x60f1, 0x60f0, 0x60ec, 0x6109, 0x60f9, 0x60f5, 0x60fe, 0x6374,
  0x6381, 0x637c, 0x6375, 0x6389, 0x6382, 0x6397, 0x6386, 0x637d,
  0x6393, 0x639c, 0x6376, 0x6380, 0x6445, 0x30a1, 0x6603, 0x67c9,
  0x67cd, 0x67d0, 0x69ad, 0x69c5, 0x6aa2, 0x6bec, 0x6bf6, 0x6bf3,
  0x6df3, 0x6dfa, 0x6df9, 0x6df5, 0x6df4, 0x6df8, 0x6eb6, 0x6eb4,
  0x6f32, 0x6fcd, 0x6fc8, 0x6fce, 0x6fca, 0x712a, 0x7121, 0x711d,
  0x73bd, 0x73be, 0x73c2, 0x0cf1, 0x73c9, 0x751f, 0x76f1, 0x76ed,
  0x76f2, 0x76e0, 0x76f7, 0x7830, 0x7837, 0x7831, 0x7836, 0x78c8,
  0x7b6d, 0x7b69, 0x7b7d, 0x7b61, 0x7b70, 0x7b71, 0x7b73, 0x7b76,
  0x7b75, 0x7b78, 0x7b79, 0x7b64, 0x7b6e, 0x7d51, 0x7d4f, 0x7d22,
  0x7faf, 0x7faa, 0x7fa3, 0x7f9d, 0x7f9c, 0x7fa1,
  /* 0x4f */
  0x7fb6, 0x7fac, 0x7fa2, 0x7fa7, 0x7fb0, 0x7fa9, 0x7fc3, 0x8131,
  0x8132, 0x8133, 0x8134, 0x8137, 0x813c, 0x81d9, 0x81dd, 0x81de,
  0x81df, 0x81e0, 0x82a5, 0x82aa, 0x82a2, 0x82a3, 0x8404, 0x8403,
  0x83fe, 0x8428, 0x86af, 0x86ad, 0x86a6, 0x87ac, 0x87a5, 0x87b0,
  0x87b1, 0x8801, 0x88b2, 0x88d2, 0x88f1, 0x8bd1, 0x8c47, 0x8cc9,
  0x8ca7, 0x8cc8, 0x8c95, 0x8c8e, 0x8c91, 0x8c7d, 0x8cee, 0x8c8d,
  0x8c8c, 0x8cb0, 0x8c96, 0x8c42, 0x8c7c, 0x8cb1, 0x8cb2, 0x8c84,
  0x8c9d, 0x8ca1, 0x8c98, 0x8cb3, 0x8c22, 0x8c7b, 0x8c8a, 0x8cce,
  0x8c80, 0x8c97, 0x8cb4, 0x8cb5, 0x8c9a, 0x8c9f, 0x8c93, 0x8e12,
  0x8e0b, 0x8e0e, 0x90a3, 0x90cc, 0x90dc, 0x90e1, 0x90de, 0x90d2,
  0x90db, 0x90d9, 0x90d7, 0x90d4, 0x90c9, 0x90eb, 0x90da, 0x90d1,
  0x9104, 0x90ca, 0x90e2, 0x91d7, 0x938c, 0x9399,
  /* 0x50 */
  0x93a2, 0x9396, 0x9394, 0x939f, 0x1267, 0x938e, 0x9403, 0x9494,
  0x9493, 0x9544, 0x972f, 0x9735, 0x972b, 0x9732, 0x972d, 0x9736,
  0x1314, 0x9731, 0x9712, 0x9733, 0x971f, 0x9734, 0x9740, 0x973f,
  0x9741, 0x97d3, 0x9889, 0x9918, 0x9910, 0x9a1a, 0x9a25, 0x9a1e,
  0x9b92, 0x9b95, 0x9b93, 0x9d84, 0x9d9a, 0x9d89, 0x9d8d, 0x9d88,
  0x9d91, 0x9d9b, 0x9d9c, 0xb148, 0x9e8e, 0x9e92, 0x9fc5, 0x9fc1,
  0x9fb8, 0x9fbe, 0x9fb5, 0x9fc7, 0xa03c, 0x1489, 0xa1ec, 0xa23f,
  0xa239, 0xa237, 0xa3a1, 0xa394, 0xa3a0, 0xa3de, 0xa3db, 0xa3df,
  0xa3dc, 0xa4d6, 0xa4dc, 0xa4dd, 0xa4e0, 0xa4e3, 0xa4e1, 0xa718,
  0xa719, 0xa753, 0xc4ec, 0xa744, 0xa70e, 0xa70f, 0xa747, 0xa717,
  0xa71d, 0xa711, 0xa8b4, 0xa8b6, 0xa9b7, 0xa9be, 0xa9c2, 0xa9b4,
  0xab31, 0xab15, 0xab83, 0xac3b, 0xac36, 0xac42,
  /* 0x51 */
  0xac50, 0xac40, 0xac34, 0xac38, 0xac3d, 0xac3e, 0xac35, 0xac3a,
  0xac46, 0xac37, 0xac39, 0xac45, 0xad77, 0xad5d, 0xad6a, 0xad76,
  0xad6b, 0xad6c, 0xad65, 0xad64, 0xad71, 0xad5f, 0xad72, 0xadfe,
  0xadff, 0xae3e, 0xaf2b, 0xaf36, 0xaf2d, 0xaf39, 0xaf3f, 0xaf3b,
  0xaf33, 0xaf42, 0xaf3a, 0xafd5, 0xafd8, 0xafd9, 0xb00d, 0xb00a,
  0xb039, 0xb03a, 0xb13d, 0xb145, 0xb13a, 0xb137, 0xb13e, 0xb142,
  0xb387, 0xb38c, 0xb382, 0xb36b, 0xb3a0, 0xb39a, 0xb390, 0xb38e,
  0xb3a1, 0xb3bd, 0xb3b2, 0xb3b5, 0xb3b7, 0xb3aa, 0xb3a2, 0xb3a5,
  0xb3ae, 0xb3ab, 0xb3bc, 0xb432, 0xb45a, 0xb564, 0xb55c, 0xb54d,
  0xb53f, 0xb53e, 0xb552, 0xb558, 0xb557, 0xb55e, 0xb553, 0xb554,
  0xb556, 0xab65, 0xb684, 0xb685, 0xb686, 0xb797, 0xb7a1, 0xb7a2,
  0x180b, 0xc50a, 0xb7a3, 0xb7a6, 0x1817, 0xb815,
  /* 0x52 */
  0xb824, 0xb84a, 0xb849, 0xb848, 0xb84b, 0xb90e, 0xb562, 0xb90b,
  0xb90a, 0xb908, 0xb906, 0xba43, 0xba47, 0xba3f, 0xba46, 0xba50,
  0x186d, 0xba4b, 0x1870, 0xba52, 0xbcd7, 0xbcbf, 0xbcd8, 0xbce0,
  0xbce7, 0xbcb8, 0xbcd5, 0xbcef, 0xbce6, 0xbce4, 0xbcd4, 0xbcd6,
  0xbcea, 0x18ed, 0xbcbb, 0xbce9, 0xc510, 0xbe6d, 0xbe70, 0xbe73,
  0xbe72, 0xbed4, 0xbece, 0xbed5, 0xbf5a, 0xbf58, 0xbf52, 0xbf50,
  0xbf55, 0xbf4e, 0xbf4d, 0xbfcb, 0xbfcc, 0xbfcd, 0xbfd1, 0xc058,
  0xc063, 0xc05e, 0xc054, 0xc05b, 0xc0e9, 0xc0e7, 0xc0e8, 0xc11d,
  0xc167, 0xc15a, 0xc15c, 0xc15b, 0xc161, 0xc1a1, 0x198d, 0xc1a4,
  0xc1e9, 0xc1ef, 0xc1e5, 0xc1f5, 0xc1eb, 0xc1ed, 0xc296, 0xc295,
  0xc2b3, 0xc2b5, 0xc2b1, 0x00a7, 0x2e4d, 0x3024, 0x322b, 0x33cf,
  0x34c9, 0x3836, 0x3831, 0x3854, 0x383a, 0x3838,
  /* 0x53 */
  0x3939, 0x3938, 0x3af4, 0x3af3, 0x3af6, 0x3afc, 0x3af5, 0x3af1,
  0x3c9c, 0x3e18, 0x3e1a, 0x3ffc, 0x3ffe, 0x4003, 0x4053, 0x422b,
  0x43c6, 0x43c1, 0x457b, 0x4706, 0x4849, 0x48b8, 0x49a3, 0x4c52,
  0x4c4d, 0x4c5f, 0x4c5e, 0x4c61, 0x4d23, 0x508c, 0x506f, 0x5075,
  0x5074, 0x5071, 0x5070, 0x506c, 0x5326, 0x5508, 0x1e07, 0x58f0,
  0x58ef, 0x58fb, 0x5910, 0x590c, 0x58f6, 0x58fe, 0x5b7c, 0x5be1,
  0x5d38, 0x5d6f, 0x6118, 0x6115, 0x611c, 0x6110, 0x6135, 0xc417,
  0x6117, 0x611d, 0x6126, 0x6128, 0x6129, 0x612a, 0x611a, 0xc416,
  0x4707, 0x63ab, 0x63ac, 0x63a1, 0x63ae, 0x63a3, 0x63a7, 0x6448,
  0x6504, 0x65fd, 0x0a5a, 0x6608, 0x67d2, 0x69c6, 0x69be, 0x6a1c,
  0x6aa6, 0x6aa7, 0x6aab, 0x6b00, 0x6bfb, 0x6bfc, 0x6bf9, 0x6c01,
  0x6e06, 0x6e04, 0xc43c, 0x6f34, 0x7136, 0x7132,
  /* 0x54 */
  0x7142, 0x712d, 0x7135, 0x73d8, 0x7523, 0x7520, 0x7701, 0x7700,
  0x7703, 0xc2bc, 0x783c, 0x7841, 0x7835, 0x78c9, 0x7b8e, 0x7b9e,
  0x7b99, 0x7bb4, 0x7baa, 0x7b9f, 0x7b96, 0x7b9d, 0x7bc3, 0x7b74,
  0x7bab, 0x0eaf, 0x7d63, 0x7d5b, 0x7d5a, 0x7fc5, 0x7fc4, 0x7fcf,
  0x7fc8, 0x7fa4, 0x7fbd, 0x7fd3, 0x8060, 0x813b, 0x81e3, 0x81e7,
  0x82a8, 0x82ac, 0x82a9, 0x832a, 0x8408, 0x8409, 0x86b9, 0x88c1,
  0x88c2, 0x88b8, 0x8ce1, 0x8ceb, 0x8ce5, 0x8cfa, 0x8ce4, 0x8d0b,
  0x8cd7, 0x8cef, 0x8ce0, 0x8cec, 0x8cfb, 0xc4b0, 0x8cd3, 0x8ce6,
  0x8cfc, 0x8ce3, 0x8ccf, 0x8cda, 0x8cdc, 0x8cd2, 0x8ca4, 0x116b,
  0x8e17, 0x8e16, 0x90f2, 0x90fc, 0x9118, 0x90f6, 0x90fe, 0x90f3,
  0x90f7, 0x9101, 0x90f9, 0x9106, 0x90f5, 0x9110, 0x90df, 0x9103,
  0x9108, 0x91d8, 0x9205, 0x9397, 0x93b3, 0x93ae,
  /* 0x55 */
  0x93af, 0x93a7, 0x93b1, 0x93a8, 0x93ac, 0x93ab, 0x9404, 0x949a,
  0x954a, 0x9742, 0x9758, 0x974b, 0x9745, 0x9749, 0x974c, 0x9759,
  0x9756, 0x131b, 0x9746, 0x9744, 0x975b, 0x9769, 0x988e, 0x988f,
  0x991e, 0x86bc, 0x9a2f, 0x9b9e, 0x9b9d, 0x9b9f, 0x9b9c, 0x9db4,
  0x9dae, 0x9dab, 0x9db3, 0x9daf, 0x9dc2, 0x9e93, 0x9e95, 0x9e96,
  0x9e97, 0x9fcf, 0x9fce, 0x9fcb, 0xa04b, 0xa246, 0xa243, 0xa245,
  0xa251, 0xa3ae, 0xa3af, 0xa3b0, 0xa3b8, 0xa3e2, 0xa3e3, 0xa3e6,
  0xa4ed, 0xa4ea, 0xa53a, 0xa759, 0xa784, 0xa75f, 0xa77c, 0xa75c,
  0xa758, 0xa755, 0xa75d, 0xa77e, 0xa780, 0xa783, 0xa757, 0x1563,
  0xa75e, 0xa8ba, 0xa9d5, 0xab58, 0xab68, 0xab67, 0xac4a, 0xac4c,
  0xac52, 0xac49, 0xac4e, 0xac47, 0xac4d, 0xac4b, 0xac4f, 0xad7e,
  0xad87, 0xad83, 0xad89, 0x69ca, 0xad86, 0xad88,
  /* 0x56 */
  0xae47, 0xae42, 0xae49, 0xae48, 0x1680, 0x1684, 0x167f, 0xaf44,
  0xaf51, 0xaf46, 0xaf47, 0xafe4, 0xb00f, 0xb03f, 0xb14b, 0xb157,
  0xb152, 0x16f1, 0xb151, 0xb158, 0xb15e, 0xb153, 0xb15d, 0xb14d,
  0xb23c, 0xb23f, 0xb246, 0xb23e, 0xb244, 0xb245, 0xb241, 0xb238,
  0xb242, 0xb243, 0xb27a, 0xb3a3, 0xb3ba, 0xb3c0, 0xb3c4, 0xb3c6,
  0xb3cb, 0xb461, 0xb57a, 0xb573, 0xb572, 0xb574, 0xb580, 0xb581,
  0x2947, 0xb695, 0xb68f, 0xb690, 0xb692, 0xb694, 0xb68b, 0xb6e6,
  0xb7b2, 0xb7b8, 0xb7bd, 0xb7be, 0xb7ce, 0xb7ba, 0xb816, 0xb826,
  0xb825, 0xb84c, 0xb850, 0xb84e, 0xb851, 0xb852, 0xb914, 0xb915,
  0xb91b, 0xba82, 0xba99, 0xba9a, 0xba7d, 0xba85, 0xba86, 0xba9c,
  0xba79, 0xba7b, 0xba80, 0xba83, 0xba81, 0xbd1e, 0xbd1b, 0xbd2a,
  0xbcfb, 0xbd05, 0xbd20, 0xbd11, 0xbd04, 0xbcfd,
  /* 0x57 */
  0xbd03, 0xbd10, 0xbd18, 0xbd0a, 0xbd4e, 0xbd09, 0xbd07, 0xbd1c,
  0x191c, 0xbe77, 0xbe76, 0xbed8, 0xbed9, 0xbf61, 0xbf5c, 0xbf5e,
  0xbf60, 0xbfaa, 0xbfd6, 0xbfd8, 0xc009, 0xc008, 0xc06b, 0xc065,
  0xc073, 0xc074, 0xc0ed, 0xc124, 0xc125, 0xc16a, 0xc1a7, 0xc1a8,
  0xc20b, 0xc1fa, 0xc1f9, 0xc1ff, 0xc204, 0xc1f6, 0xc205, 0xc299,
  0xc2ab, 0xc2bd, 0xc2b8, 0x00a8, 0x2e64, 0x2e5a, 0x2f72, 0x337a,
  0x3595, 0x385f, 0x3861, 0x385e, 0x385a, 0x385c, 0x385d, 0x386e,
  0x3857, 0x3858, 0x3b02, 0x3b0b, 0x3b08, 0x3b51, 0x3e25, 0x3e28,
  0x3e23, 0x3e21, 0x3e24, 0x3e29, 0x4006, 0x400a, 0x43ca, 0x43cc,
  0x43cb, 0x43cf, 0x457f, 0x457c, 0x45d6, 0x4709, 0x470b, 0x4776,
  0x487d, 0x49a5, 0x4c5b, 0x4c5c, 0x4c5d, 0x4c65, 0x506d, 0x5082,
  0x5083, 0x5087, 0x5095, 0x508a, 0x52d6, 0x5328,
  /* 0x58 */
  0x550d, 0x592e, 0xc3ee, 0x592d, 0x5921, 0x5919, 0x5a3b, 0x5a3c,
  0x5a3a, 0x5b7e, 0x5d3b, 0x6147, 0x6139, 0x6134, 0x6136, 0x6146,
  0x613b, 0x6141, 0x6145, 0x63c0, 0x63c4, 0x63ba, 0x63bd, 0x63be,
  0x64a3, 0x660c, 0x67d9, 0x69cd, 0x6aae, 0x6bff, 0x6c24, 0x6ebb,
  0x6ebc, 0x6f36, 0x6fd5, 0x6fd3, 0x6fd6, 0x713c, 0x713f, 0x73de,
  0x73e3, 0x7527, 0x7529, 0x0d8b, 0x7705, 0x7707, 0x770c, 0x78d0,
  0x7bbe, 0x7bbc, 0x7bd0, 0x7bc2, 0x7bb5, 0x7bc9, 0x7d66, 0x0f2b,
  0x7fd5, 0x7fe2, 0x7fdc, 0x7fe3, 0x7fda, 0x7fc2, 0x7fe8, 0x81e9,
  0x82af, 0x82ad, 0x82ae, 0x840b, 0x86c1, 0x87b6, 0x87b9, 0x88c0,
  0x8ca5, 0x8d28, 0x8d22, 0x8d29, 0x8d18, 0x8d1f, 0x8d1c, 0x8d12,
  0x8d2a, 0x117a, 0x8d21, 0x8d2b, 0x8d17, 0x8cf0, 0x8d16, 0x8d23,
  0x912b, 0x9126, 0x913d, 0x9122, 0x913a, 0x9131,
  /* 0x59 */
  0x9132, 0x9154, 0x9121, 0x9135, 0x1209, 0x912e, 0x9130, 0x912f,
  0x9136, 0x91da, 0x91d9, 0x93bb, 0x93bc, 0x93b7, 0x93c2, 0x93bd,
  0x93b2, 0x126d, 0x7144, 0x7bd1, 0x9752, 0x976b, 0x9767, 0x131f,
  0x9761, 0x976c, 0x9751, 0x9774, 0x9777, 0x976f, 0x976d, 0x9768,
  0xc4d1, 0x9784, 0x9890, 0x9892, 0x9893, 0x991f, 0x9a31, 0x9a38,
  0x9a39, 0x9a37, 0x9bab, 0x9dc3, 0x9dc8, 0x9dcb, 0x9dcf, 0x9e98,
  0x9fd4, 0x9fd3, 0x9fd8, 0x9fd9, 0x9fdd, 0x9fd1, 0x9fd6, 0xa03e,
  0xa258, 0xa257, 0xa255, 0xa3c4, 0xa3e4, 0xa4ee, 0xa4ef, 0xa4f3,
  0xa4f2, 0xa4f0, 0xa7ab, 0xa79a, 0xa7af, 0xa797, 0x156a, 0x156c,
  0xa7bf, 0xa794, 0xa793, 0xa8be, 0xa8bb, 0xa8bc, 0xa9d9, 0xab6c,
  0xac53, 0xac54, 0xac5b, 0xac58, 0xac56, 0xac57, 0xad9f, 0xad94,
  0xad96, 0xad97, 0xae4a, 0xae4b, 0xaf55, 0xaf5a,
  /* 0x5a */
  0xaf5e, 0xaf5f, 0xaf59, 0xaf5b, 0xaf58, 0xaf54, 0xafe8, 0xafeb,
  0xafec, 0xb013, 0xb166, 0xb16b, 0xb162, 0xb169, 0xb163, 0xb15f,
  0xb14e, 0xb248, 0xb24a, 0xb3e3, 0xb3db, 0xb3d8, 0xb3d6, 0xb586,
  0xb590, 0xb591, 0xb588, 0xb594, 0xb583, 0x17b2, 0xb59c, 0xb58d,
  0xb585, 0xb698, 0xb69a, 0xb69c, 0xb6e7, 0xb7c5, 0xb7d0, 0xb7d1,
  0xb819, 0xb827, 0x181d, 0xb854, 0xb92d, 0xb922, 0x182d, 0xb91f,
  0xbabd, 0xbaae, 0xbabb, 0xbaad, 0xbabc, 0xbab9, 0xbab4, 0xbacb,
  0xbab7, 0xbab3, 0xbaba, 0xbab6, 0xbacd, 0xbabe, 0xbac9, 0xc50b,
  0xbd5f, 0xbd3b, 0xbd61, 0xbd5c, 0xbd8a, 0xbd5a, 0xbd4d, 0xbd46,
  0xbd44, 0xbd3d, 0xbd40, 0xbd3c, 0xbd8c, 0xbd41, 0xbd4c, 0xbd3e,
  0xbd4a, 0xbe7c, 0xbe7a, 0xbf65, 0xbf6e, 0xbf69, 0xbf6a, 0xbf6f,
  0xbf6c, 0xbf70, 0xbf68, 0xbf6b, 0x1945, 0xbfac,
  /* 0x5b */
  0xbfde, 0xbfdd, 0xbfdc, 0x63c5, 0xc08c, 0xc083, 0xc082, 0xc088,
  0xc085, 0xc081, 0xc0f5, 0xc0ef, 0xc0f4, 0xc0f2, 0xc0f6, 0xc0f3,
  0xc0f0, 0xc0f1, 0xc12b, 0xc127, 0xc128, 0xc16c, 0xc1ae, 0xc20e,
  0xc21b, 0xc216, 0xc21f, 0xc222, 0xc220, 0xc221, 0xc214, 0xc213,
  0xc29d, 0xc29c, 0xc29e, 0xc29f, 0x2e6b, 0x32c8, 0x3878, 0x3876,
  0x3870, 0x3871, 0x3b0a, 0x3e2c, 0x4711, 0x487e, 0x4c57, 0x4c66,
  0x4c69, 0x4c67, 0x4c68, 0x4c71, 0x4c6f, 0xbfae, 0x508b, 0x5096,
  0x5235, 0x523a, 0x526b, 0x5516, 0x5943, 0x5946, 0x593f, 0x593b,
  0x593d, 0x5ab1, 0x5ab2, 0x5be4, 0x5d40, 0x615d, 0x6151, 0x614d,
  0x614c, 0x615b, 0x63d4, 0x63d2, 0x63ca, 0x63c8, 0x63d0, 0x63c9,
  0x6449, 0x64a4, 0x6612, 0x660f, 0x6611, 0x67db, 0x67dd, 0x67dc,
  0x69d4, 0x6a21, 0x6ab2, 0x6c04, 0x6c02, 0x6e11,
  /* 0x5c */
  0x6e16, 0x6e10, 0x6ebe, 0x8e1a, 0x714c, 0x714a, 0x73f2, 0x73f1,
  0x752a, 0x752c, 0x752f, 0x7531, 0x7711, 0x7712, 0x784b, 0x7bdb,
  0x7bd6, 0x7bdd, 0x7be2, 0x7be4, 0x7be0, 0x7bdf, 0x7be3, 0x7d6e,
  0x7d71, 0x7fe9, 0x7fea, 0x8063, 0x81eb, 0x81ea, 0x86bd, 0x86bb,
  0x86c6, 0x86cc, 0x86c8, 0x63cf, 0x86c9, 0x86ca, 0x86cf, 0x86d0,
  0x87ba, 0x87fb, 0x8803, 0x88c4, 0x8d49, 0x8d53, 0x8d36, 0x8d4a,
  0x8d41, 0x8d4e, 0x8d19, 0x8d4d, 0x8d45, 0x8d4c, 0x8d47, 0x8d48,
  0x8d4f, 0x8d37, 0x8d42, 0x914a, 0x9146, 0x120a, 0x9149, 0x914f,
  0x9151, 0x914c, 0x120c, 0x9206, 0x9551, 0x977a, 0x9783, 0x977e,
  0x977f, 0x9780, 0x6ab5, 0x9891, 0x9894, 0x9895, 0x9921, 0x9920,
  0x9a3d, 0x9a40, 0x9a46, 0x9a84, 0x9bac, 0x9bad, 0x142e, 0x9dda,
  0x9dd9, 0x9fe2, 0x9fe1, 0x9fe3, 0x9fe4, 0x9fde,
  /* 0x5d */
  0x9fdf, 0xa241, 0xa259, 0xa25c, 0xa25a, 0xa3e8, 0xa4f6, 0xa4fc,
  0xa4f7, 0xa4fa, 0xa4f9, 0xa7c4, 0xa7be, 0xa7d2, 0xa7bd, 0xa795,
  0xa7d4, 0xa9e4, 0xac61, 0xac62, 0xac63, 0xac64, 0xac60, 0xac5c,
  0xac5d, 0xac5e, 0xada5, 0xada6, 0xae4c, 0xaf68, 0xaf6e, 0xaf71,
  0xaf6b, 0xaf6f, 0xafee, 0xaff1, 0xaff0, 0xafef, 0xb015, 0xb014,
  0xab6e, 0xb047, 0xb17c, 0xb17a, 0xb174, 0xb176, 0xb16e, 0xb178,
  0xb16d, 0xb16c, 0xb24e, 0xb3d7, 0xb3ea, 0xb3e5, 0xb464, 0xb5b3,
  0xb5a3, 0xb5a5, 0xb5a7, 0xb5a2, 0xb59f, 0xb5a6, 0xb59e, 0xb5a8,
  0xb6a9, 0xb6a6, 0xb6aa, 0xb6ab, 0xb6a0, 0xb6a1, 0xb6a8, 0xb6e8,
  0xb6e9, 0xb6ea, 0xb7e4, 0xb7df, 0xb7e0, 0xb828, 0xb85d, 0xb85b,
  0xb856, 0xb857, 0xb85f, 0xb862, 0xbae1, 0xbae3, 0xbade, 0xbad9,
  0xbae8, 0xbaf2, 0xbaf6, 0xbae6, 0xbaf4, 0xbaf5,
  /* 0x5e */
  0xbae5, 0xbae2, 0x188d, 0xbd96, 0xbdaa, 0xbd97, 0xbd70, 0xbda1,
  0xbd9d, 0xbda9, 0xbd6f, 0xbd7e, 0xbd94, 0xbd9a, 0xbd73, 0xbd87,
  0xbd71, 0xbd77, 0xbd88, 0xbd8d, 0xbd85, 0xbd78, 0xbdad, 0xbe80,
  0xbe81, 0xbee5, 0xbee7, 0xbf7c, 0xbfaf, 0xbfe1, 0xc096, 0xc0a3,
  0xc090, 0xc0f8, 0xc12e, 0xc175, 0xc17e, 0xc17d, 0xc17b, 0xc178,
  0xc1b0, 0xc234, 0xc236, 0xc230, 0xc51d, 0xc22e, 0xc237, 0x34ce,
  0x3597, 0x3598, 0x387c, 0x387e, 0x387d, 0x387f, 0x3b0f, 0x3ca4,
  0x3e31, 0x3e2e, 0x3e2f, 0x3e32, 0x422c, 0x43d4, 0x43dc, 0x43d8,
  0x440e, 0x4583, 0x4584, 0x4712, 0x4c72, 0x4c7c, 0x4c7e, 0x50a6,
  0x50a0, 0x509e, 0x50a2, 0x532a, 0x5518, 0x594d, 0x5958, 0x595b,
  0x7714, 0xc3f2, 0x5be6, 0x6164, 0x6168, 0x6160, 0x6162, 0x63d7,
  0x644b, 0x67e0, 0x6a22, 0x6c05, 0x6e19, 0x6e1a,
  /* 0x5f */
  0x6ec3, 0x6fd8, 0x6fdc, 0x714f, 0x73f7, 0x73f4, 0x73f8, 0x7713,
  0x7850, 0x7bf0, 0x7be9, 0x7bef, 0x7bed, 0x7bea, 0x7bf8, 0x7c05,
  0x7bf2, 0x7d72, 0x0f31, 0x7ff9, 0x7ff3, 0x7ff6, 0x7ff2, 0x7ff7,
  0x8066, 0x8065, 0x8140, 0xc477, 0x86d1, 0x86d3, 0x8804, 0x8d59,
  0x8d60, 0x8d5b, 0x8d5d, 0x8d5e, 0x8d69, 0x8d5c, 0x8d61, 0x8d6a,
  0x8d5f, 0x914e, 0x915c, 0x9160, 0x9163, 0x91db, 0x93ca, 0x93c9,
  0x93c8, 0x94a5, 0x94a3, 0x978e, 0x9787, 0x9789, 0x9785, 0x9786,
  0x978f, 0x978a, 0x9790, 0x9898, 0x989b, 0x9a47, 0x9a49, 0x9a48,
  0x9de5, 0x9dea, 0x9ded, 0x9ff0, 0x9fef, 0x9ff2, 0x9fec, 0xa040,
  0xa260, 0xa25f, 0xa3eb, 0xa3ec, 0xa500, 0xa501, 0xa7e2, 0xa7df,
  0xa7e0, 0xa7e1, 0xa7e3, 0xa8c3, 0xa9eb, 0xa9ea, 0xab61, 0xab71,
  0xac6b, 0xac68, 0xac69, 0xac67, 0xadb0, 0xadb1,
  /* 0x60 */
  0xadb2, 0xae51, 0xaf74, 0xb17f, 0xb184, 0xb253, 0xb254, 0xb3f0,
  0xb3f4, 0xb3f1, 0xb437, 0xb5bf, 0x17bc, 0x17bb, 0xb5bd, 0xb5be,
  0xb5b7, 0xb5c0, 0xb5ba, 0xb5b8, 0xb5bc, 0xb5bb, 0xb6eb, 0xb7e7,
  0xb81d, 0xb81c, 0xb863, 0x484b, 0xb938, 0xb936, 0xb934, 0xb937,
  0xbb06, 0xbb1c, 0xbb02, 0xbb1d, 0xbb1e, 0xbae0, 0xbb11, 0xbb18,
  0xbb21, 0xbb20, 0xbb10, 0xbdbd, 0xbdae, 0xbdb5, 0xbdb8, 0xbdb9,
  0xbdbe, 0xbdc4, 0xbdbc, 0xbdba, 0xbe83, 0xbeea, 0xbeec, 0xbf7e,
  0xbf7b, 0xbfe5, 0xc0a7, 0xc09e, 0xc09a, 0xc12f, 0xc131, 0xc183,
  0xc1b5, 0xc246, 0xc241, 0xc243, 0xc23d, 0xc242, 0xc23b, 0xc247,
  0x336b, 0x33d0, 0x388e, 0x4011, 0xc371, 0xc389, 0x484c, 0x532b,
  0x594f, 0x595e, 0x5963, 0x596b, 0x5a3f, 0x5be9, 0x616d, 0x616b,
  0x616a, 0xbb2a, 0x63e0, 0x63dd, 0x63e1, 0x63de,
  /* 0x61 */
  0x63dc, 0x644d, 0x6616, 0x67e3, 0x69e5, 0x69e4, 0x6e1d, 0x754b,
  0xc2c1, 0x784e, 0x78d6, 0x7bfd, 0x7c07, 0x7bfe, 0x7c03, 0x7c0b,
  0x7bff, 0x7d7a, 0x7d77, 0x7ffb, 0x8143, 0x81ed, 0x87bc, 0x63df,
  0x8805, 0x88c6, 0x88c5, 0x8d74, 0x8d73, 0x8d72, 0x8d78, 0x9173,
  0x917a, 0x6e1c, 0x9176, 0x9175, 0x9177, 0x93cf, 0x93ce, 0x93cd,
  0x94a8, 0x9798, 0x9792, 0x9794, 0x989a, 0x9bb4, 0x9deb, 0x9df4,
  0x9df3, 0x9dee, 0x9df2, 0x9df0, 0xa264, 0xa805, 0xa7fb, 0xa7fc,
  0xa9f3, 0xac6c, 0xadba, 0xaf79, 0xaf7e, 0xaf78, 0xaff4, 0xb016,
  0xb257, 0xb5c8, 0xb5c3, 0xb5ce, 0xb6b3, 0xb6ed, 0xb6ee, 0xb7f1,
  0xb7f8, 0x1814, 0xb864, 0xb865, 0xbb35, 0xbb29, 0xbb2c, 0xbb31,
  0xbb2b, 0xbb2e, 0xbb25, 0xbdda, 0xbde0, 0xbdd4, 0xbde1, 0xbddd,
  0xbfe7, 0xc012, 0xc0ae, 0xc0af, 0xc186, 0xc185,
  /* 0x62 */
  0xc1d1, 0xc258, 0xc23f, 0xc252, 0xc24b, 0xc253, 0xc250, 0xc256,
  0xc257, 0xc2cd, 0xc2cb, 0x3231, 0x3230, 0x33d1, 0x021c, 0x3892,
  0x3890, 0x388f, 0x3893, 0x3891, 0x0372, 0x4713, 0x487f, 0x50ac,
  0x6170, 0x63e5, 0x6a23, 0x6a24, 0x6c08, 0x6c07, 0x6e1f, 0x6e20,
  0x6e21, 0x6fe1, 0x7154, 0x7157, 0x7155, 0x73fa, 0x7538, 0x8d86,
  0x7537, 0x7853, 0x7d7e, 0x7d7b, 0xc471, 0x7ffd, 0x7ffc, 0x8146,
  0x8732, 0x88c7, 0x8d71, 0x8d83, 0x8d6f, 0x8d7e, 0x8d7d, 0x8d81,
  0x8d7c, 0x918a, 0x917e, 0x9180, 0x917d, 0x917f, 0x9182, 0x93d4,
  0x93d0, 0x93d2, 0x9555, 0x979b, 0x979a, 0x9a4e, 0x9df1, 0x9ff8,
  0x9ffd, 0xa25e, 0xa266, 0xa505, 0xa80a, 0xa80b, 0xa80e, 0xa80d,
  0xa811, 0xa809, 0xa810, 0xa80c, 0xa812, 0xa8c4, 0xa9f7, 0xa9f8,
  0xab6a, 0xab6b, 0xadc0, 0xadc2, 0xaf85, 0xaf80,
  /* 0x63 */
  0xaf84, 0xaf81, 0xadc4, 0xb18b, 0xb18d, 0xb18e, 0xb6b7, 0xb6b9,
  0xb6ef, 0xb7fb, 0xb7ff, 0xb867, 0xb868, 0xb869, 0xb93f, 0xbb3c,
  0xbb4c, 0xbb3d, 0xbb3e, 0xbb3f, 0xbb3b, 0xbdff, 0x190e, 0xbdf6,
  0xbdee, 0xbdfc, 0xbdf8, 0xbe01, 0xbdfa, 0xbe88, 0xbf80, 0xc011,
  0xc0fe, 0xc100, 0xc135, 0x6f3c, 0xc1bd, 0xc1bb, 0xc25c, 0xc25a,
  0xc2d1, 0xc2d0, 0x340b, 0x5973, 0x3e39, 0x401a, 0x43e3, 0x4587,
  0x4777, 0x4778, 0x50b1, 0x596a, 0x5974, 0x5beb, 0x617b, 0x64a5,
  0x67e4, 0x6c0b, 0x6c0e, 0x6c0c, 0x7539, 0x7c10, 0x7c11, 0x7c16,
  0x7d81, 0x7d80, 0x7ffe, 0x8001, 0x8000, 0x8147, 0x81ef, 0x8d88,
  0x918b, 0x918d, 0x9187, 0x9185, 0x918f, 0x9184, 0x9188, 0x918e,
  0x918c, 0x93d7, 0x93d6, 0x979f, 0x4588, 0x9e03, 0x9ffe, 0xa3ef,
  0xa509, 0xa508, 0xa820, 0xa824, 0xa81f, 0xac70,
  /* 0x64 */
  0xae56, 0xaff7, 0xaff8, 0xaff6, 0xb190, 0xb25e, 0xb3f7, 0xb5d6,
  0xb5d5, 0xb6bb, 0xb6f0, 0xb801, 0xb86c, 0xb941, 0xb942, 0xbb4f,
  0xbb53, 0xbb58, 0xbe12, 0xbe04, 0xbe13, 0xbe05, 0xbe0d, 0xbf82,
  0xbf81, 0xc0b5, 0xc0ba, 0xc1be, 0xc265, 0xc263, 0xc26e, 0xc26a,
  0xc26c, 0xc2c4, 0x336c, 0x97a5, 0x4012, 0x484d, 0x551f, 0x5977,
  0x5978, 0x5d44, 0x617c, 0x63eb, 0x63ea, 0x63ec, 0x64a7, 0x6619,
  0x6e23, 0x2074, 0x7401, 0x7c1c, 0x8003, 0x8148, 0x86da, 0x8d91,
  0x8d92, 0x9196, 0x9197, 0x9191, 0x9193, 0x93d8, 0x93d5, 0x9557,
  0x9558, 0x97a2, 0x9e05, 0x9ffa, 0xa50c, 0xa50a, 0xa82e, 0xa829,
  0xa82f, 0xa8c5, 0xac72, 0xadc8, 0xae5a, 0xae59, 0xaf89, 0xaf88,
  0xb5db, 0xb5d9, 0xb5da, 0xb6bc, 0x17ec, 0xb806, 0xb805, 0xb86f,
  0xb86d, 0xb870, 0xbb60, 0xbb5e, 0xbb63, 0xbb5a,
  /* 0x65 */
  0xbb5f, 0xbe19, 0xbe1d, 0xbe1b, 0xbe22, 0xbe1c, 0xbe1e, 0xbef7,
  0xbf84, 0xc015, 0xc0b8, 0xc0c1, 0xc101, 0xc10a, 0xc1d2, 0xc275,
  0xc274, 0xc272, 0x34d0, 0x401b, 0x4410, 0x4779, 0x63ed, 0x6ab9,
  0x6e24, 0x6ec5, 0x7403, 0x814a, 0x86f7, 0x87c1, 0x87c2, 0x8d94,
  0x9199, 0x919a, 0x955a, 0x97a8, 0x9825, 0x989e, 0xa041, 0xa832,
  0xa833, 0xadcb, 0xadca, 0xadcc, 0xb193, 0xb5e0, 0xb871, 0xbb6b,
  0xbe2b, 0xbe29, 0xbe1a, 0xbe26, 0xbe27, 0xbe2a, 0xbef8, 0x6f3d,
  0xc276, 0x3b18, 0x597a, 0x617f, 0x0e79, 0x81f0, 0x8d9a, 0x8d96,
  0x919e, 0x919d, 0x91dc, 0x93da, 0x9e0b, 0xa002, 0xa042, 0xa267,
  0xaf8b, 0xb5e2, 0xbb71, 0xbe2f, 0xbe2e, 0xc0c6, 0xc18e, 0xc27b,
  0xc277, 0xc278, 0xc27c, 0x597c, 0x8007, 0x8d97, 0x97d9, 0xa50d,
  0xa50e, 0xb5e3, 0xb5e4, 0xb6f3, 0xb875, 0xbe37,
  /* 0x66 */
  0xbe35, 0xc18f, 0xc280, 0xc27f, 0xc2d4, 0x50b4, 0xbfec, 0xab75,
  0x6c10, 0x8069, 0x91a4, 0x93db, 0xadd1, 0xaf8d, 0xbb76, 0xbe39,
  0xc284, 0xc282, 0x34d2, 0x63ee, 0x6c11, 0x7d84, 0xab74, 0xaf8f,
  0xaf8e, 0xbe3b, 0x32cb, 0xc288, 0xc286, 0x555c, 0x71a4, 0xac75,
  0xc28b, 0x3b19, 0x989f, 0xb5e5, 0xbe40, 0x6c12, 0xbefb, 0xc28c,
  0x71a5, 0xb877, 0xb878, 0xc2d6, 0x93df, 0xadd2, 0x3b1a, 0x97a9,
  0xadd3, 0xc0ca, 0x87c4, 0x94b1, 0xb264,
};

static const ucs4_t cns11643_7_2uni_upages[198] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x04c00, 0x04d00, 0x05600, 0x05800, 0x06100, 0x06400, 0x06700, 0x07100,
  0x07600, 0x07c00, 0x07f00, 0x08100, 0x08600, 0x08d00, 0x08f00, 0x09300,
  0x09500, 0x09a00, 0x0ff00, 0x20000, 0x20100, 0x20300, 0x20400, 0x20500,
  0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20f00, 0x21000,
  0x21100, 0x21200, 0x21400, 0x21500, 0x21600, 0x21800, 0x21900, 0x21a00,
  0x21b00, 0x21c00, 0x21d00, 0x21f00, 0x22000, 0x22100, 0x22200, 0x22300,
  0x22400, 0x22500, 0x22700, 0x22800, 0x22900, 0x22a00, 0x22c00, 0x22d00,
  0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200, 0x23300, 0x23500, 0x23600,
  0x23700, 0x23800, 0x23900, 0x23a00, 0x23b00, 0x23c00, 0x23e00, 0x23f00,
  0x24000, 0x24100, 0x24300, 0x24400, 0x24500, 0x24600, 0x24700, 0x24800,
  0x24900, 0x24a00, 0x24b00, 0x24c00, 0x24d00, 0x24e00, 0x24f00, 0x25000,
  0x25200, 0x25300, 0x25400, 0x25500, 0x25600, 0x25700, 0x25800, 0x25900,
  0x25a00, 0x25b00, 0x25c00, 0x25d00, 0x25e00, 0x25f00, 0x26000, 0x26100,
  0x26200, 0x26300, 0x26400, 0x26500, 0x26600, 0x26700, 0x26800, 0x26900,
  0x26a00, 0x26d00, 0x26e00, 0x26f00, 0x27000, 0x27100, 0x27200, 0x27300,
  0x27400, 0x27500, 0x27600, 0x27700, 0x27800, 0x27900, 0x27a00, 0x27b00,
  0x27c00, 0x27d00, 0x27e00, 0x27f00, 0x28000, 0x28100, 0x28200, 0x28300,
  0x28400, 0x28500, 0x28600, 0x28700, 0x28800, 0x28900, 0x28a00, 0x28b00,
  0x28c00, 0x28d00, 0x28e00, 0x28f00, 0x29000, 0x29100, 0x29200, 0x29300,
  0x29400, 0x29500, 0x29600, 0x29700, 0x29800, 0x29900, 0x29a00, 0x29b00,
  0x29c00, 0x29d00, 0x29e00, 0x29f00, 0x2a000, 0x2a100, 0x2a200, 0x2a300,
  0x2a400, 0x2a500, 0x2a600, 0x2f800, 0x2f900, 0x2fa00,
};

static int
cns11643_7_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x66)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        {
          if (i < 6539)
            swc = cns11643_7_2uni_page21[i],
            wc = cns11643_7_2uni_upages[swc>>8] | (swc & 0xff);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

