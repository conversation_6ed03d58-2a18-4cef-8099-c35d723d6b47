/*
 * イ?ントnpc(悸挛)の?〖ス
 *
 */

#include <stdio.h>
#ifdef PUK2
#include <string.h>
#endif

#include "common.h"
#include "char_base.h"
#include "char.h"
#include "util.h"
#include "npcutil.h"
#include "npc_event.h"
#include "messages.h"
#include "log.h"
#include "readmap.h"
#include "jobs_ancestry.h"
#include "enemy.h"
#include "pet.h"
#include "nrproto_serv.h"
#include "item.h"
#include "language.h"
#include "handletime.h"
#include "npccreate.h"
#include "title.h"
#include "item_recipe.h"
#include "house.h"
#include "ship.h"

#include "npc_bankman.h"

#include "version.h"
#include "configfile.h"


#include "../puk2/puk2.h"

#include "item_event.h"
#ifdef _ENABLE_ALBUM_ITEMS
#include "album.h"
#endif /* _ENABLE_ALBUM_ITEMS */

#ifdef PUK2
#include "char_event.h"
#include "../puk2/guild/guild.h"
#include "../ext/script.C.h"
#include "../ext/itemExt.h"

#endif

/* 年眶 ****************************************************/
#ifdef CHAR_EXPANSION
#define MAX_EVENT_FLG	INT32_MAX	//フラグの呵络
#else
#define MAX_EVENT_FLG	255	//フラグの呵络
#endif


/* コ?ンドテ〖ブル ****************************************************/
static char *block_com[]={
	"level",
	"lp",
	"fp",
	"gold",
	"imagetype",
	"jobancestry",
	"jobrank",
	"job",
	"charm",
	"bimage",
	"bbimage",
	"metamocount",
	"trueimage",
	"maxlp",
	"maxfp",
	"fame",
	"floor",
	"xpos",
	"ypos",
	"houseid",
	"luck",
	"trialflg",
	"",						//ここから黎は改侍コ?ンド
	"nowevent",				//1
	"endevent",				//2
	"haveitem",				//3
	"itemspace",			//4
	"itemcreatetime",		//5
	"itemdurper",			//6
	"itemdur",				//7
	"item",					//8
	"group",				//9
	"rank",					//10
	"skilllevel",			//11
	"petspace",				//12
	"petlevel",				//13
	"leak0item",			//14
	"leak1item",			//15
	"haveleak0item",		//16
	"haveleak1item",		//17
	"stackitemspace",		//18
	"strcmpchat",			//19
	"strstrchat",			//20
	"havetitle",			//21
	"weapon",				//22
	"nowtime",				//23
	"visitor",				//24
	"drugdish",				//25
	"equipitem",			//26
	"houselimit",			//27
	"shipchardown",			//28
	"shipchartake",			//29
	"shipgetstoptime",		//30
	"globaltimer",			//31
	"checkrealtime",		//32
#ifdef _USE_STAMP_ITEM
	"stampcount",			//33
#endif /* _USE_STAMP_ITEM */
	"checkitemcategory",	//34
#ifdef PUK2
	"globalflag",			//35
	"localflag",			//36
	"localcount",			//37
	"skill",				//38
	"bankitem",				//39
	"random",				//40
	"guildmember",			//41
#endif 
	"albumitemflg",			//42
	"getalbumflgtotal",		//43
	"recipeflg",			//44
	"feverflg",				//45
	"feversec",				//46
#ifdef ITEMCOMMAND
	"hitem",		// 47
	"pitem",		// 48
	"phitem",		// 49
	"pequipitem",		// 50
#endif

#ifdef GLOBALFLAG_T
	"tglobalflag",		//51
#endif /* GLOBALFLAG_T */

#ifdef ADDSKILLSLOT
	"checkskillslot",	//52
#endif	/* ADDSKILLSLOT */

#ifdef CHECK_GENDER
	"sex",				//53
#endif	/* CHECK_GENDER */

#ifdef GLOBALFLAG_GA
	"gaglobalflag",		//54
#endif	/* GLOBALFLAG_GA */

#ifdef ADD_ITEM_COMMAND
	"pallitem",			//55
#endif
#ifdef ADD_GLOBALFLAG_COMMAND
	"pallgflag",		//56
	"pgflag",			//57
#endif
#ifdef DB_SCRIPT
	"getplayer",
	"getpet",
	"getitem",
#endif
    "luac",
};

#define BLOCK_COMMAND						\
int *block_com_ch[] = {						\
	&ch->i.Lv,								\
	&ch->i.Hp,								\
	&ch->i.ForcePoint,						\
	&ch->iu.player.Gold,					\
	&ch->i.ImageType,						\
	&ch->iu.player.JobAncestry,				\
	&ch->iu.player.JobRank,					\
	&ch->iu.player.Job,						\
	&ch->iu.player.Charm,					\
	&ch->i.BaseImageNumber,					\
	&ch->i.BaseBaseImageNumber,				\
	&ch->wu.player.Metamo_Count,			\
	&ch->iu.player.OriginalImageNumber,		\
	&ch->w.MaxHp,							\
	&ch->w.MaxForcePoint,					\
	&ch->iu.player.Fame,					\
	&ch->i.Floor,							\
	&ch->i.X,								\
	&ch->i.Y,								\
	&ch->iu.player.HouseId,					\
	&ch->i.Luck,							\
	&ch->wu.player.TrialFlg,				\
	(int *)0,								\
};

/*-------------------------------------------
wu.player.WindowBuffer	借妄したライン眶瘦赂脱
wu.player.WindowBuffer2	NPIで慌屯
wu.player.WindowBuffer3	??ンの硷梧
i.Lv					i.BaseImageNumberバッ?アップ
i.Hp					山绩フラグ
i.ForcePoint			丸狄?ウント
i.Vital					链数羹フラグ
-------------------------------------------*/

//ch->i.Lv = 0;
//ch->i.Hp = 0;
//ch->block_com_ch[ LV ] = 0;
//ch->lv = 0;
//block_com_ch[ 0 ][ 0 ];
//*(((int *)&ch->i)+8)

/***************************************************************/

/* コ?ンドエラ〖テ〖ブル ****************************************************/
static int com_err[]={
	LANG_MSG_EVENT_ERR_000,		//"error:イ?ント肋年ファイルのオ〖プンに己窃"
	LANG_MSG_EVENT_ERR_001,		//"error:blockコ?ンドの今及エラ〖"
	LANG_MSG_EVENT_ERR_002,		//"error:Messageコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_003,		//"error:EventFlgコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_004,		//"error:HaveItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_005,		//"error:ItemSpaceコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_006,		//"error:Itemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_007,		//"error:ImageTypeコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_008,		//"error:Groupコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_009,		//"error:GiveItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_010,		//"error:KillItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_011,		//"error:AddGoldコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_012,		//"error:AddGoldLevelコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_013,		//"error:SetLevelコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_014,		//"error:Warpコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_015,		//"error:ワ〖プできません"
	LANG_MSG_EVENT_ERR_016,		//"error:SkillLevelコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_017,		//"error:PetLevelコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_018,		//"error:GivePetコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_019,		//"error:?ットを侯喇できません"
	LANG_MSG_EVENT_ERR_020,		//"error:KillPetHコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_021,		//"error:?ットを猴近できません"
	LANG_MSG_EVENT_ERR_022,		//"error:KillPetLコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_023,		//"error:Windowコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_024,		//"error:办米するラ?ルが斧つかりません"
	LANG_MSG_EVENT_ERR_025,		//"error:痰跟なアイテ?です"
	LANG_MSG_EVENT_ERR_026,		//"error:アイテ?を猴近できません"
	LANG_MSG_EVENT_ERR_027,		//"error:Leak0Itemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_028,		//"error:Leak1Itemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_029,		//"error:HaveLeak0Itemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_030,		//"error:HaveLeak1Itemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_031,		//"error:StackItemSpaceコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_032,		//"error:ス?ッ?アイテ?ではありません"
	LANG_MSG_EVENT_ERR_033,		//"error:GiveStackItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_034,		//"error:StrCmpChatコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_035,		//"error:StrStrChatコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_036,		//"error:ラン??联买バグだ"
	LANG_MSG_EVENT_ERR_037,		//"error:SetCharmコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_038,		//"error:痰跟な喀度です"
	LANG_MSG_EVENT_ERR_039,		//"error:SetDurコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_040,		//"error:ItemCreateTimeコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_041,		//"error:GiveItem2コ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_042,		//"error:SetVisitorコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_043,		//"error:ChangePalコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_044,		//"error:KillDrugDishコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_045,		//"error:ItemDurコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_046,		//"error:EquipItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_047,		//"error:ItemDurPerコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_048,		//"error:GiveReciperコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_049,		//"error:SetHouseLimitコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_050,		//"error:SetHouseコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_051,		//"error:HouseId痰跟エラ〖"
	LANG_MSG_EVENT_ERR_052,		//"error:AddLuckコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_053,		//"error:KillItemAllコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_054,		//"error:SetActionコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_055,		//"error:ShipCharDownコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_056,		//"error:ShipCharTakeコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_057,		//"error:ShipGetStopTimeコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_058,		//"error:PlaySeコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_059,		//"error:GlobalTimerコ?ンドエラ〖"
#ifdef _USE_STAMP_ITEM
	LANG_MSG_EVENT_ERR_060,		//"error:StampCountコ?ンドエラ〖"
#endif /* _USE_STAMP_ITEM */
	LANG_MSG_EVENT_ERR_061,		//"error:ItemCategoryコ?ンドエラ〖"
#ifdef PUK2
	LANG_MSG_EVENT_ERR_062,		//"error:AlbumItemFlgコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_063,		//"error:globalFlagコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_064,		//"error:localFlagコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_065,		//"error:localCounterコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_066,		//"error:ifBlockコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_067,		//"error:getSkillコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_068,		//"error:bankItemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_069,		//"error:randomコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_070,		//"error:giveitemexコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_071,		//"error:movetoコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_072,		//"error:shapeコ?ンドエラ〖"
#endif
	LANG_MSG_EVENT_ERR_073,		//"error:AlbumItemFlgコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_074,		//"error:GetAlbumFlgTotalコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_075,		//"error:RecipeFlgコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_076,		//"error:AddFeverSecコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_077,		//"error:SetFeverFlgコ?ンドエラ〖"
#ifdef ITEMCOMMAND
	LANG_MSG_EVENT_ERR_078,		//"error:Hitemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_079,		//"error:Pitemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_080,		//"error:PHitemコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_081,		//"error:PEquipItemコ?ンドエラ〖"
#endif
#ifdef GLOBALFLAG_T
	LANG_MSG_EVENT_ERR_082,		//"error:tglobalFlagコ?ンドエラ≥"
#endif /* GLOBALFLAG_T */
#ifdef CHECK_GENDER
	LANG_MSG_EVENT_ERR_083,		//"error:sexコ?ンドエラ〖"
#endif	/* CHECK_GENDER */
#ifdef GIVE_STAMP_ITEM
	LANG_MSG_EVENT_ERR_084,		//"error:givestampitemコ?ンドエラ〖"
#endif	/* GIVE_STAMP_ITEM */
#ifdef GLOBALFLAG_GA
	LANG_MSG_EVENT_ERR_085,		//"error:globalFlagGAコ?ンドエラ〖"
#endif	/* GLOBALFLAG_GA */
#ifdef DISPWINDOW_GA
	LANG_MSG_EVENT_ERR_086,		//"error:gawindowコ?ンドエラ〖"
#endif	/* DISPWINDOW_GA */

#ifdef ADD_ITEM_COMMAND
	LANG_MSG_EVENT_ERR_087,		//"error:PallItemコ?ンドエラ〖"
#endif

#ifdef ADD_GLOBALFLAG_COMMAND
	LANG_MSG_EVENT_ERR_088,		//"error:Pallgflagコ?ンドエラ〖"
	LANG_MSG_EVENT_ERR_089,		//"error:Pgflagコ?ンドエラ〖"
#endif
};

/* ス?ティッ?恃眶 ****************************************************/
static int read_line;
static int get_line_cnt;
static char file_name[128];
static int check_data[ 24 ];
static int check_cnt;

static BOOL check_num( char *moji );
static int get_num2( char **moji );

#ifdef DB_SCRIPT
static BOOL get_fieldstr(char *dest, char **src);
static BOOL get_operstr(char *dest, char **src);
static BOOL getplayerint(Char *ch, char *fieldname, int *value);
static BOOL setplayerint(Char *ch, char *fieldname, int *value);
static BOOL getpetint(Char *ch, char *fieldname, int *value);
static BOOL setpetint(Char *ch, char *fieldname, int *value);
static BOOL getitemint(Char *ch, char *fieldname, int *value);
static BOOL setitemint(Char *ch, char *fieldname, int *value);

static BOOL setplayer( Char *talker, char **buf);
static BOOL setpet( Char *talker, char **buf);
static BOOL setitem( Char *talker, char **buf);

static BOOL getplayer( Char *talker, char **buf);
static BOOL getpet( Char *talker, char **buf);
static BOOL getitem( Char *talker, char **buf);


char *playerstr[] = {
	"whichtype",
	"baseimagenumber",
	"basebaseimagenumber",
	"mapid",
	"floor",
	"x",
	"y",
	"dir",
	"lv",
	"hp",
	"forcepoint",
	"vital",
	"str",
	"tough",
	"quick",
	"magic",
	"luck",
	"tribe",
	"attribearth",
	"attribwater",
	"attribfire",
	"attribwind",
	"poison",
	"sleep",
	"stone",
	"drunk",
	"confusion",
	"amnesia",
	"critical",
	"counter",
	"hitrate",
	"avoid",
	"itemlimit",
	"haveskilllimit",
	"deadcount",
	"damagecount",
	"killpetcount",
	"lasttimesetluck",
	"injury",
	"walkinterval",
	"loopinterval",
	"exp",
	"leveluppoint",
	"imagetype",
	"namecolor",
	"allocpoint",
	"eattime",
#ifdef VERSION_TW
	"thankflower",
#endif
	"dataplacenumber",
	"registnumber",
	"job",
	"jobrank",
	"jobancestry",
	"faceimagenumber",
	"gold",
	"poolgold",
	"stamina",
	"dex",
	"intelligence",
	"charm",
	"fame",
	"equiptitle",
	"savepoint",
	"defaultpet",
	"chatvolume",
	"helppoint",
	"logincount",
	"talkcount",
	"getpetcount",
	"sendmailcount",
	"mergeitemcount",
	"walkcount",
	"deadpetcount",
	"resurrectpetcount",
	"healpetcount",
	"capturecount",
	"otherflg",
	"famegetvalue",
	"famegettime",
	"fameautodowntime",
	"sicklevel",
	"renewalflg",
	"endevent1",
	"endevent2",
	"endevent3",
	"endevent4",
	"endevent5",
	"endevent6",
	"endevent7",
	"endevent8",
#ifdef CHAR_EXPANSION
	"endevent9",
	"endevent10",
	"endevent11",
	"endevent12",
	"endevent13",
	"endevent14",
	"endevent15",
	"endevent16",
	"endevent17",
	"endevent18",
	"endevent19",
	"endevent20",
	"endevent21",
	"endevent22",
	"endevent23",
	"endevent24",
	"endevent25",
	"endevent26",
	"endevent27",
	"endevent28",
	"endevent29",
	"endevent30",
	"endevent31",
	"endevent32",
#endif
	"nowevent1",
	"nowevent2",
	"nowevent3",
	"nowevent4",
	"nowevent5",
	"nowevent6",
	"nowevent7",
	"nowevent8",
#ifdef CHAR_EXPANSION
	"nowevent9",
	"nowevent10",
	"nowevent11",
	"nowevent12",
	"nowevent13",
	"nowevent14",
	"nowevent15",
	"nowevent16",
	"nowevent17",
	"nowevent18",
	"nowevent19",
	"nowevent20",
	"nowevent21",
	"nowevent22",
	"nowevent23",
	"nowevent24",
	"nowevent25",
	"nowevent26",
	"nowevent27",
	"nowevent28",
	"nowevent29",
	"nowevent30",
	"nowevent31",
	"nowevent32",
#endif
	"recipe1",
	"recipe2",
	"recipe3",
	"recipe4",
	"recipe5",
	"recipe6",
	"recipe7",
	"recipe8",
	"recipe9",
	"recipe10",
	"recipe11",
	"recipe12",
	"recipe13",
	"recipe14",
	"recipe15",
	"recipe16",
	"recipe17",
	"recipe18",
	"recipe19",
	"recipe20",
	"recipe21",
	"recipe22",
	"recipe23",
	"recipe24",
	"recipe25",
	"recipe26",
	"recipe27",
	"recipe28",
	"recipe29",
	"recipe30",
	"recipe31",
	"recipe32",
#ifdef CHAR_EXPANSION
	"recipe33",
	"recipe34",
	"recipe35",
	"recipe36",
	"recipe37",
	"recipe38",
	"recipe39",
	"recipe40",
	"recipe41",
	"recipe42",
	"recipe43",
	"recipe44",
	"recipe45",
	"recipe46",
	"recipe47",
	"recipe48",
	"recipe49",
	"recipe50",
	"recipe51",
	"recipe52",
	"recipe53",
	"recipe54",
	"recipe55",
	"recipe56",
	"recipe57",
	"recipe58",
	"recipe59",
	"recipe60",
	"recipe61",
	"recipe62",
	"recipe63",
	"recipe64",
#endif
	"album1",
	"album2",
	"album3",
	"album4",
	"album5",
	"album6",
	"album7",
	"album8",
	"album9",
	"album10",
#ifdef CHAR_EXPANSION
	"album11",
	"album12",
	"album13",
	"album14",
	"album15",
	"album16",
	"album17",
	"album18",
	"album19",
	"album20",
	"album21",
	"album22",
	"album23",
	"album24",
	"album25",
	"album26",
	"album27",
	"album28",
	"album29",
	"album30",
	"album31",
	"album32",
#endif
	"maxpoolpethavelimit",
	"maxpoolitemhavelimit",
	"duelpoint",
	"popupnamecolor",
	"position",
	"debugger",
	"dungeonclr1",
	"dungeonclr2",
	"dungeonclr3",
	"lastsavetime",
	"rankdownflg",
	"penalty",
	"loginpoint",
	"feverresettime",
	"feverhavetime",
	"houseid",
	"houselimit",
	"timeoutdungeonid",
	"timeoutservernumber",
	"originalimagenumber"
};

char *petstr[] = {
	"whichtype",
	"baseimagenumber",
	"basebaseimagenumber",
	"mapid",
	"floor",
	"x",
	"y",
	"dir",
	"lv",
	"hp",
	"forcepoint",
	"vital",
	"str",
	"tough",
	"quick",
	"magic",
	"luck",
	"tribe",
	"attribearth",
	"attribwater",
	"attribfire",
	"attribwind",
	"poison",
	"sleep",
	"stone",
	"drunk",
	"confusion",
	"amnesia",
	"critical",
	"counter",
	"hitrate",
	"avoid",
	"itemlimit",
	"haveskilllimit",
	"deadcount",
	"damagecount",
	"killpetcount",
	"lasttimesetluck",
	"injury",
	"walkinterval",
	"loopinterval",
	"exp",
	"leveluppoint",
	"imagetype",
	"namecolor",
	"allocpoint",
#ifdef VERSION_TW
	"eattime",
	"thankflower",
#else
	"eattime"
#endif
};

char *itemstr[] = {
	"id",
	"baseimagenumber",
	"cost",
	"type",
	"otherflg",
	"equipbothhand",
	"ableusefield",
	"ableusebattle",
	"target",
	"remain",
	"maxremain",
	"level",
	"basefailedprob",
	"maxdurability",
	"attacknum_min",
	"attacknum_max",
	"ableeffectbetweenhave",
	"modifymodflg",
	"modifyattack",
	"modifydefence",
	"modifyagility",
	"modifymagic",
	"modifyrecovery",
	"modifycritical",
	"modifycounter",
	"modifyhitrate",
	"modifyavoid",
	"modifyhp",
	"modifyforcepoint",
	"modifyluck",
	"modifycharisma",
	"modifycharm",
	"modifyattrib",
	"modifyattrib2",
	"modifyattribvalue",
	"modifyattribvalue2",
	"modifystamina",
	"modifydex",
	"modifyintelligence",
	"poison",
	"sleep",
	"stone",
	"drunk",
	"confusion",
	"amnesia",
	"specialeffect",
	"specialeffectvalue",
	"specialeffectvalue2",
	"material_weapon",
	"material_armour",
	"material_accessory",
	"useaction",
	"dropatlogout",
	"vanishatdrop",
	"canpetmail",
	"rss",
	"cansell",
	"explanation1",
	"explanation2",
	"leakedprob",
	"rare_flg",
	"inbox_flg",
	"puttime",
	"leaklevel",
	"mergeflg",
	"durability",
	"sellunit",
	"createtime",
	"var1",
	"var2",
	"var3",
	"var4",
	"adm",
	"housex",
	"housey",
	"monsterattrib"
};
#endif

//=========================================================================
//矢机?ン?リング
//=========================================================================
void Event_centering( char *sendbuf )
{
	char buf[1024];
	char moji[256];
	char *p_read;
	int space_cnt;
	int ii,jj;
	int buf_cnt;

	//?ウン?〖?リア
	buf_cnt = 0;
	//姜位コ〖ド?ット
	buf[ 0 ] = 0;
	//コピ〖傅?イン?〖コピ〖
	p_read = sendbuf;
	
	//ル〖プ
	while(1){
		//まず矢机までコピ〖
		while(1){
			//姜位なら
			if( *p_read == 0 ){
				goto Event_centering_100;
			}
			//猖乖でなければ
			if( !( *p_read == '\\' && *(p_read+1) == 'n' ) ){
				break;
			}
			//２矢机コピ〖
			buf[ buf_cnt++ ] = *p_read++;
			buf[ buf_cnt++ ] = *p_read++;
		}
		//メッ?〖ジ艰り叫し
		ii = 0;
		while(1){
			//姜位なら
			if( *p_read == 0 ){
				break;
			}
			//猖乖なら
			if( *p_read == '\\' && *(p_read+1) == 'n' ){
				break;
			}
			//１矢机コピ〖
			moji[ ii++ ] = *p_read++;
		}
		//鄂なら
		if( ii == 0 ){
			//?ン?ングしない
			continue;
		}
		//姜位コ〖ド?ット
		moji[ ii ] = 0;
		//矢机眶呵络なら
		if( ii >= 46 ){
			//?ン?ングしない
			//メッ?〖ジ纳裁
			buf[ buf_cnt ] = 0;
			strcat( buf, moji );
			//矢机眶裁换
			buf_cnt += ii;
			continue;
		}
		//ス?〖ス?ウント?ット
		space_cnt = ii / 2;
		//瘩眶なら
//		if( space_cnt & 1 ){
//			//饿眶にする
//			space_cnt--;
//		}
		//ス?〖ス赁掐眶?ット
		space_cnt = 46/2 - space_cnt;
		//ス?〖スで虽める
		for( jj = 0; jj < space_cnt; jj++ ){
			buf[ buf_cnt++ ] = 0x20;
		}
		//姜位コ〖ド?ット
		buf[ buf_cnt ] = 0;
		//メッ?〖ジ纳裁
		strcat( buf, moji );
		//矢机眶裁换
		buf_cnt += ii;
	}

Event_centering_100:
	//姜位コ〖ド?ット
	buf[ buf_cnt ] = 0;
	//?ン?リング稿矢机误コピ〖
	strcpy( sendbuf, buf );
}


//-------------------------------------------------------------------------
//叹涟赁掐
// 苞眶¨
// 		*talker
// 		*mes  赁掐涟矢机误
// 		*buf  赁掐稿矢机误
// 提り猛¨
// 		1:?ン?リング
//-------------------------------------------------------------------------
int Event_insert_name( Char *talker, char *mes, char *buf )
{
	int cnt;
	char data;
	int centering_flg = 0;

	int day;
	int hour;
	int minutes;
	int disp_cnt = 3;
	char moji[256];
	int limit;

	//コピ〖倡幌
	while( 1 ){
		data = *mes++;
		//姜位なら
		if( data == 0 ){
			*buf = 0;
			break;
		}
		//链逞なら
		if( data < 0 ){
			*buf++ = data;
			*buf++ = *mes++;
			continue;
		}
		//'@'コ?ンドなら
		if( data == '@' ){
			//'n'コ?ンドなら
			if( *mes == 'n' || *mes == 'N' ){
				//叹涟コピ〖
				cnt = 0;
				while( 1 ){
					data = *( talker->c.Name + cnt );
					//姜位なら
					if( data == 0 ){
						//'n'ス?ップ
						mes++;
						break;
					}
					*buf++ = data;
					cnt++;
				}
				continue;
			} else
			//'c'コ?ンドなら
			if( *mes == 'c' || *mes == 'C' ){
				//?ン?リングＯＮ
				centering_flg = 1;
				//'c'ス?ップ
				mes++;
				continue;
			} else
			//'h'コ?ンドなら
			if( *mes == 'h' || *mes == 'H' ){
				//?ンションリ?ット艰り叫し
				limit = talker->iu.player.HouseLimit - NowTime.tv_sec;
				//荒り泣にち?ット
				day = limit / ( 24 * 60 * 60 );
				//泣にち痰いなら
				if( !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= day * 24 * 60 * 60;
				//荒り箕粗?ット
				hour = limit / ( 60 * 60 );
				//泣にち痰いなら
				if( !hour && !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= hour * 60 * 60;
				//荒り尸?ット
				minutes = limit / 60;
				//尸痰いなら
				if( !hour && !day && !minutes ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				switch( disp_cnt ){
				case 0:
					translate_format(moji, 0, LANG_MSG_EVENT_009, limit );
					break;
				case 1:
					translate_format(moji, 0, LANG_MSG_EVENT_010, minutes );
					break;
				case 2:
					translate_format(moji, 0, LANG_MSG_EVENT_011, hour, minutes );
					break;
				case 3:
					translate_format(moji, 0, LANG_MSG_EVENT_012, day, hour, minutes );
					break;
				}
				//袋嘎磊れでなければ
//				if( disp_cnt ){
					//矢机误コピ〖
					cnt = 0;
					while( moji[ cnt ] ){
						*buf++ = moji[ cnt++ ];
					}
//				}
				//'h'ス?ップ
				mes++;
				continue;
			} else
			//'t'コ?ンドなら
			if( *mes == 't' || *mes == 'T' ){
				int ship,harbor,ret;
				//'t'ス?ップ
				mes++;
				//肌の矢机が眶猛でなければ
				if( check_num( mes ) == FALSE ){
					//エラ〖
					continue;
				}
				//隶ＩＤ艰り叫し
				ship = get_num2( &mes );
				//?パレ〖?でなければ
				if( *mes != ',' ){
					//エラ〖
					continue;
				}
				//','ス?ップ
				mes++;
				//肌の矢机が眶猛でなければ
				if( check_num( mes ) == FALSE ){
					//エラ〖
					continue;
				}
				//沽戎规艰り叫し
				harbor = get_num2( &mes );
				//隶の叫券箕癸艰り叫し∈擅∷
				limit = SHIP_GetStartTime( ship, harbor, &ret );
				//荒り泣にち?ット
				day = limit / ( 24 * 60 * 60 );
				//泣にち痰いなら
				if( !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= day * 24 * 60 * 60;
				//荒り箕粗?ット
				hour = limit / ( 60 * 60 );
				//泣にち痰いなら
				if( !hour && !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= hour * 60 * 60;
				//荒り尸?ット
				minutes = limit / 60;
				//尸痰いなら
//				if( !hour && !day && !minutes ){
//					//山绩改眶?イナス
//					disp_cnt--;
//				}
				switch( disp_cnt ){
				case 0:
					translate_format(moji, 0, LANG_MSG_EVENT_013, limit );
					break;
				case 1:
					translate_format(moji, 0, LANG_MSG_EVENT_014, minutes, limit %= 60 );
					break;
				case 2:
					translate_format(moji, 0, LANG_MSG_EVENT_015, hour, minutes );
					break;
				case 3:
					translate_format(moji, 0, LANG_MSG_EVENT_016, day, hour, minutes );
					break;
				}
				//矢机误コピ〖
				cnt = 0;
				while( moji[ cnt ] ){
					*buf++ = moji[ cnt++ ];
				}
				continue;
			} else
			//'d'コ?ンドなら
			if( *mes == 'd' || *mes == 'D' ){
				int ship;
				//'d'ス?ップ
				mes++;
				//肌の矢机が眶猛でなければ
				if( check_num( mes ) == FALSE ){
					//エラ〖
					continue;
				}
				//隶ＩＤ艰り叫し
				ship = get_num2( &mes );
				//肌の掐沽箕癸艰り叫し∈擅∷
				limit = SHIP_GetStopTime( ship );
				//荒り泣にち?ット
				day = limit / ( 24 * 60 * 60 );
				//泣にち痰いなら
				if( !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= day * 24 * 60 * 60;
				//荒り箕粗?ット
				hour = limit / ( 60 * 60 );
				//泣にち痰いなら
				if( !hour && !day ){
					//山绩改眶?イナス
					disp_cnt--;
				}
				limit -= hour * 60 * 60;
				//荒り尸?ット
				minutes = limit / 60;
				switch( disp_cnt ){
				case 0:
					translate_format(moji, 0, LANG_MSG_EVENT_013, limit );
					break;
				case 1:
					translate_format(moji, 0, LANG_MSG_EVENT_014, minutes, limit %= 60 );
					break;
				case 2:
					translate_format(moji, 0, LANG_MSG_EVENT_015, hour, minutes );
					break;
				case 3:
					translate_format(moji, 0, LANG_MSG_EVENT_016, day, hour, minutes );
					break;
				}
				//矢机误コピ〖
				cnt = 0;
				while( moji[ cnt ] ){
					*buf++ = moji[ cnt++ ];
				}
				continue;
			} else
			//'i'コ?ンドなら
			if( *mes == 'i' || *mes == 'I'){
				int num;
				int itemindex;
				char *itemname;
				//'i'ス?ップ
				mes++;
				//肌の矢机が眶猛でなければ
				if( check_num( mes ) == FALSE ){
					//エラ〖
					continue;
				}
				//アイテ?腕のどこを回年しているか粕み艰る
				num = get_num2( &mes);
				if( num < 0 || num >= CHAR_MAXITEMHAVE){
					// アイテ?腕を回年する铜跟な眶猛ではない
					continue;
				}
				itemindex = CHAR_getItemIndex( talker, num);
				itemname = ITEM_getIncuseName( itemindex);
				//矢机误コピ〖
				cnt = 0;
                while (itemname && itemname[cnt]) {
                    *buf++ = itemname[cnt++];
                }
				continue;
			} else
			//'s'コ?ンドなら
			if( *mes == 's' || *mes == 'S'){
				int i;
				int itemindex;
				int itemid;
				char count[32];
				mes++;
				// 拇べるアイテ?のIDを艰评する
				if( !check_num( mes)) continue;
				itemid = get_num2( &mes);
				// アイテ?を积っているかどうかチェッ?する
				for( i = 0; i < CHAR_MAXITEMHAVE; i++){
					itemindex = CHAR_getItemIndex( talker, i);
					if( itemindex < 0) continue;
					if( ITEM_getInt( itemindex, ITEM_ID) == itemid) break;
				}
				if( i >= CHAR_MAXITEMHAVE) continue;
				// ス?ンプアイテ?笆嘲稍材
				if( ITEM_getInt( itemindex, ITEM_TYPE) != ITEM_STAMP) continue;
				// ス?ンプアイテ?の?ウントを艰评する。
				snprintf( count, sizeof( count), "%d", ITEM_getInt( itemindex, ITEM_VAR2));
				i = 0;
				// 矢机误コピ〖
				while( count[i]){
					*buf++ = count[i];
					i++;
				}
				continue;
			}
		}
		*buf++ = data;
	}
	return centering_flg;
}


//-------------------------------------------------------------------------
// 矢机误孺秤
// 苞眶¨
// 		*moji  矢机误１の黎片アドレス
// 		*moji2 矢机误２の黎片アドレス
// 提り猛¨
// 		喇根=0  己窃=1
//-------------------------------------------------------------------------
static int strncmp2( char *moji, char *moji2, int cnt )
{
	int ii;
	for( ii = 0; ii < cnt; ii++ ){
		if( *moji++ != *moji2++ ){
			return 1;
		}
	}
	return 0;
}


//-------------------------------------------------------------------------
// 矢机误眶猛チェッ?
// 苞眶¨
// 		*moji  矢机误の黎片アドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL check_num( char *moji )
{
	//プラスか?イナスなら
	if( *moji == '+' || *moji == '-' ){
		moji++;
	}
	//眶猛でなければ
	if( *moji < '0' || *moji > '9' ){
		return FALSE;
	}
	return TRUE;
}


#if 1
//-------------------------------------------------------------------------
// 矢机误眶猛恃垂
// 苞眶¨
// 		*moji  矢机误の黎片アドレス
// 提り猛¨
// 		眶猛
//-------------------------------------------------------------------------
static int get_num( char *moji )
{
	int ii;
	int total = 0;
	int fugou = 1;

	//プラスか?イナスなら
	if( *moji == '+' || *moji == '-' ){
		//?イナスなら
		if( *moji == '-' ){
			fugou = -1;
		}
		moji++;
	}
	while( 1 ){
		//１矢机艰り叫し
		ii = *moji++;
		//姜位なら
		if( ii < '0' || ii > '9' ){
			break;
		}
		//圭纷裁换
		total *= 10;
		total += ii - '0';
	}
	return total * fugou;
}
#endif


//-------------------------------------------------------------------------
// 矢机误眶猛恃垂２∈矢机误のアドレスも构糠する∷
// 苞眶¨
// 		**moji  矢机误の黎片アドレス
// 提り猛¨
// 		眶猛
//-------------------------------------------------------------------------
static int get_num2( char **moji )
{
	int ii;
	int total = 0;
	char *moji2;
	int fugou = 1;

	moji2 = *moji;
	//プラスか?イナスなら
	if( *moji2 == '+' || *moji2 == '-' ){
		//?イナスなら
		if( *moji2 == '-' ){
			fugou = -1;
		}
		moji2++;
	}
	while( 1 ){
		//１矢机艰り叫し
		ii = *moji2++;
		//姜位なら
		if( ii < '0' || ii > '9' ){
			break;
		}
		//圭纷裁换
		total *= 10;
		total += ii - '0';
	}
	//乖き册ぎた尸提す
	*moji = moji2 - 1;
	return total * fugou;
}

//-------------------------------------------------------------------------
// １バイト粕み哈み
// 苞眶¨
// 		*fp  ファイル?イン?
// 提り猛¨
// 		矢机コ〖ド
//-------------------------------------------------------------------------
static int f_get(FILE *fp)
{
	int d0;
	while( 1 ){
		d0 = fgetc( fp );		//トラッ?戎规艰り叫し
		if( d0 == EOF )
			return d0;
		if( d0 == '\t' )		//?ブか
			continue;
		if( d0 == 0x0d )		//０ｘ０ｄなら
			continue;
		if( d0 == '_' )			//〔なら
			continue;
		if( d0 != 0x20 )		//ス?〖スか
			break;
	}
	return d0;
}


//-------------------------------------------------------------------------
// １乖艰り叫し∈?ブ、ス?〖ス、〔、コメント、猖乖コ〖ドは猴近する∷
// 苞眶¨
// 		*fp  ファイル?イン?
// 		*buf 啪流黎アドレス
// 提り猛¨
// 		粕み哈んだバイト眶
//-------------------------------------------------------------------------
static int get_line( FILE *fp, char *buf )
{
	int read_cnt = 0;
	int d0;

	//悸乖借妄乖眶裁换
	get_line_cnt++;
	while( 1 ){
		//乖の黎片まで１バイト艰り叫し
		d0 = f_get( fp );
		//ＥＯＦなら
		if( d0 == EOF ){
			return read_cnt;
		}
		//猖乖なら
		if( d0 == 0x0a ){
			//借妄乖眶裁换
			read_line++;
			continue;
		}

		//コメントなら
		if( d0 == '#' ){
			//办乖ス?ップ
			while(1){
				//１バイト艰り叫し
				d0 = f_get( fp );
				//ＥＯＦなら
				if( d0 == EOF ){
					return read_cnt;
				}
				//猖乖なら
				if( d0 == 0x0a ){
					//借妄乖眶裁换
					read_line++;
					break;
				}
			}
			continue;
		}
		break;
	}

	//链逞なら
	if( d0 < 0 ){
		//２バイト啪流
		*buf++ = d0;
		d0 = f_get( fp );
		*buf++ = d0;
		read_cnt += 2;
	} else {
		//络矢机ⅹ井矢机恃垂
		if(d0 >= 'A' && d0 <= 'Z'){
			d0 += 0x20;
		}
		//１バイト啪流
		*buf++ = d0;
		read_cnt++;
	}

	//１乖艰り叫し
	while(1){
		//肌の１バイト艰り叫し
		d0 = f_get( fp );
		//ＥＯＦか 0x0a なら
		if( d0 == EOF || d0 == 0x0a ){
			//姜位
			break;
		}
		//コメントなら
		if( d0 == '#' ){
			//乖の姜眉までス?ップ
			while(1){
				//肌の１バイト艰り叫し
				d0 = f_get( fp );
				//ＥＯＦか 0x0a なら
				if( d0 == EOF || d0 == 0x0a ){
					//却ける
					break;
				}
			}
			//姜位
			break;
		}
		//链逞なら
		if( d0 < 0 ){
			//もう１バイト啪流
			d0 = f_get( fp );
			*buf++ = d0;
			read_cnt += 2;
		} else {
			//络矢机ⅹ井矢机恃垂
			if(d0 >= 'A' && d0 <= 'Z'){
				d0 += 0x20;
			}
			*buf++ = d0;
			read_cnt++;
		}
	}

	//姜位コ〖ド?ット
	*buf = 0;
	//借妄乖眶裁换
	read_line++;
	return read_cnt;
}


//-------------------------------------------------------------------------
//バッファに呈羌されている泣烧矢机误を眶猛に恃垂する
// 畔す矢机误の今及は涩ず[YYYY/MM/DD,HH:MM:SS]のような今及で痰ければならない
//-------------------------------------------------------------------------
static time_t datestr2time_t( char **buf)
{
	char *buf2;
	struct tm t;

	// アドレスコピ〖
	buf2 = *buf;

	memset( &t, 0, sizeof( t));

	// 矢机误を箕粗に恃垂する
	t.tm_year = get_num2( &buf2) - 1900;	// 钳
	if( *buf2 != '/') return 0;
	else buf2++;	// 惰磊り矢机が痰い
	t.tm_mon = get_num2( &buf2) - 1;			// 奉
	if( *buf2 != '/') return 0;
	else buf2++;	// 惰磊り矢机が痰い
	t.tm_mday = get_num2( &buf2);			// 泣
	// ?ン?をス?ップ
	if( *buf2 != ',') return 0;
	else buf2++;
	t.tm_hour = get_num2( &buf2);			// 箕
	if( *buf2 != ':') return 0;
	else buf2++;	// 惰磊り矢机が痰い
	t.tm_min = get_num2( &buf2);			// 尸
	if( *buf2 != ':') return 0;
	else buf2++;	// 惰磊り矢机が痰い
	t.tm_sec = get_num2( &buf2);			// 擅

	// アドレス构糠
	*buf = buf2;

	// 箕粗を手す
	return mktime( &t);
}

#ifdef _USE_STAMP_ITEM
//-------------------------------------------------------------------------
// 回年されたアイテ?IDのアイテ?を积っている眷疥を手す
// 积っていなければ-1を手す
//-------------------------------------------------------------------------
static int check_haveitemid_place( Char *ch, int itemid)
{
	int i;
	int itemindex;

	for( i = CHAR_STARTITEMARRAY; i < CHAR_MAXITEMHAVE; i++){
		// アイテ?インデッ?スを艰评
		itemindex = CHAR_getItemIndex( ch, i);
		// アイテ?がない
		if( itemindex < 0) continue;
		// アイテ?IDをチェッ?
		if( ITEM_getInt( itemindex, ITEM_ID) == itemid) return i;
	}

	return -1;
}
#endif /* _USE_STAMP_ITEM */
//-------------------------------------------------------------------------
// メッ?〖ジ山绩
// 苞眶¨
// 		*talker
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL disp_mes( Char *talker, Char *my, char **buf )
{
	int mes_id;
	char *buf2,*p_token;
	char moji[1024];

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 2 ]) );
		return FALSE;
	}
	//メッ?〖ジＩＤ艰り叫し
	mes_id = get_num2( &buf2 );
	//アドレス构糠
	*buf = buf2;
	// メッ?〖ジ艰评
	p_token = MSG_getMessage( mes_id );
	//喇根なら
	if( p_token ){
		//叹涟赁掐へ
		Event_insert_name( talker, p_token, moji );
		//メッ?〖ジ流慨
		CHAR_talkToCli( talker, my, moji, CHAR_COLORWHITE);
	} else {
		return FALSE;
	}
	return TRUE;
}


//-------------------------------------------------------------------------
// イ?ントフラグ?ット
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		flg_kind フラグの硷梧(0:Now 1:End)
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_event_flg( Char *talker, char **buf, int kind )
{
	int no,flg;
	char *buf2;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
		return FALSE;
	}
	//フラグ戎规艰り叫し
	no = get_num2( &buf2 );
	//０×ＭＡＸ笆嘲なら
	if( no < 0 || no > MAX_EVENT_FLG ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
		return FALSE;
	}
	//♂でなければ
	if( *buf2 != '=' ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
		return FALSE;
	}
	//♂ス?ップ
	buf2++;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
		return FALSE;
	}
	//フラグ艰り叫し
	flg = get_num2( &buf2 );
	//NowEvent フラグなら
	if( kind == 0 ){
		//フラグオフなら
		if( flg == 0 ){
			NPC_NowEventClsFlg( talker, no );
		} else
		//フラグオンなら
		if( flg == 1 ){
			NPC_NowEventSetFlg( talker, no );
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
			return FALSE;
		}
	} else {
		//フラグオフなら
		if( flg == 0 ){
			NPC_EndEventClsFlg( talker, no );
		} else
		//フラグオンなら
		if( flg == 1 ){
			NPC_EventSetFlg( talker, no );
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 3 ]) );
			return FALSE;
		}
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

#ifdef PUK2
static void regenerateSendData (Char* ch, char* sendData)
{
	char param[256];
	char tmp[256];
	char* ptr = sendData;
	int i;

	if (!sendData)
		return;

	i = 0;
	while ((*ptr != '\0') && (*ptr != '|')) {
		param[i++] = *ptr;
		++ptr;
	}
	param[i] = '\0';

	for (i = 0; i < 2; ++i) {
		while ((*ptr != '\0') && (*ptr != '|'))
			++ptr;
		++ptr;
	}

	snprintf (tmp, sizeof(tmp), "%s|%d|%d|%s", param, ch->i.X, ch->i.Y, ptr);
	strcpy (sendData, tmp);

	
		
}

static void set_check_data( int ii, Char *my, Char *ch, char **buf, char *msg );
static BOOL setFlagEx ( Char *talker, char **buf, int kind )
{
	int no,flg;
	char *buf2;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 63 ]) );
		return FALSE;
	}
	//フラグ戎规艰り叫し
	no = get_num2( &buf2 );
	//０×ＭＡＸ笆嘲なら
	if ((no < 0) || ((kind == 0) && (no >= MAX_GLOBAL_FLAG)) || ((kind == 1) && (no > MAX_LOCAL_FLAG))) {
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 63 ]) );
		return FALSE;
	}
	//♂でなければ
	if( *buf2 != '=' ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 63 ]) );
		return FALSE;
	}
	//♂ス?ップ
	buf2++;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 63 ]) );
		return FALSE;
	}
	//フラグ艰り叫し
	flg = get_num2( &buf2 );
	if( kind == 0 ){
		//globalFlag 
		//フラグオフなら
		if( flg == 0 ){
			NPC_clearGlobalFlag ( talker, no );
		} else
		//フラグオンなら
		if( flg == 1 ){
			NPC_setGlobalFlag ( talker, no );
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 63 ]) );
			return FALSE;
		}
	} else {
		//フラグオフなら
		if( flg == 0 ){
			NPC_clearLocalFlag ( talker, no );
		} else
		//フラグオンなら
		if( flg == 1 ){
			NPC_setLocalFlag ( talker, no );
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 64 ]) );
			return FALSE;
		}
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

#ifdef GLOBALFLAG_T
static BOOL setFlagEx_T ( Char *talker, char **buf, int kind )
{
        int no,flg;
        char *buf2;

        //アドレスコピ〖
        buf2 = *buf;
        //肌の矢机が眶猛でなければ
        if( check_num( buf2 ) == FALSE ){
                //エラ〖
                print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );
                return FALSE;
        }
        //フラグ戎规艰り叫し
        no = get_num2( &buf2 );
        //０×ＭＡＸ笆嘲なら
        if ((no < 0) || ((kind == 0) && (no >= MAX_GLOBAL_FLAG_T)) || ((kind == 1) && (no > MAX_LOCAL_FLAG))) {
                //エラ〖
                print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );
                return FALSE;
        }
        //♂でなければ
        if( *buf2 != '=' ){
                //エラ〖
                print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );
                return FALSE;
        }
        //♂ス?ップ
        buf2++;
        //肌の矢机が眶猛でなければ
        if( check_num( buf2 ) == FALSE ){
                //エラ〖
                print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );
                return FALSE;
        }
	 //フラグ艰り叫し
        flg = get_num2( &buf2 );
        if( kind == 0 ){
                //globalFlag
                //フラグオフなら
                if( flg == 0 ){
                        NPC_clearGlobalFlag_T ( talker, no );
                } else
                //フラグオンなら
                if( flg == 1 ){
                        NPC_setGlobalFlag_T ( talker, no );
                } else {
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );                        return FALSE;
                }
        } else {
                //フラグオフなら
                if( flg == 0 ){
                        NPC_clearLocalFlag ( talker, no );
                } else
                //フラグオンなら
                if( flg == 1 ){
                        NPC_setLocalFlag ( talker, no );
                } else {
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 82 ]) );                        return FALSE;
                }
        }
        //アドレス构糠
        *buf = buf2;
        return TRUE;
}
#endif /* GLOBALFLAG_T */

static BOOL setLocalCounter (Char* my, Char* ch, char** buf, char* msg)
{
	int no,val;
	char *buf2;
	int kind;
	int comMode = 0;
	int i;

	BLOCK_COMMAND

	buf2 = *buf;
	if( check_num( buf2 ) == FALSE ){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
		return FALSE;
	}
	//フラグ戎规艰り叫し
	no = get_num2( &buf2 );
	if( no < 0 || no > MAX_LOCAL_COUNTER ){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
		return FALSE;
	}
	switch (*buf2) {
		case '=' :
			kind = 0;
			break;
		case '+' :
			++buf2;
			if (*buf2 != '=') {
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
				return FALSE;
			}
			kind = 1;
			break;
		case '-' :
			++buf2;
			if (*buf2 != '=') {
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
				return FALSE;
			}
			kind = 2;
			break;
		default :
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
			return FALSE;
			
	}
	//♂ス?ップ
	buf2++;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		for (i = 0; i < arraysizeof (block_com); ++i) {
			if (block_com[i][0] == 0) {
				comMode = i;
				continue;
			}
			if (strncmp2 (buf2, block_com[i], strlen (block_com[i])) == 0) 
				break;
		}
		if (i == arraysizeof (block_com)) {
			if (buf2) {
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 65 ]) );
				return FALSE;
			}
		}
		buf2 += strlen (block_com[i]);
		if (comMode) {
			set_check_data (i - comMode, my, ch, &buf2, msg);
			val = check_data[0];
		}
		else
			val = *block_com_ch[i];
	}
	else
		val = get_num2( &buf2 );
	switch (kind) {
		case 0 :	// localCount = NN
			NPC_setLocalCounter (ch, no, val);
			break;
		case 1 :	// localCount += NN
			NPC_addLocalCounter (ch, no, val);
			break;
		case 2 :	// localCount -= NN
			NPC_subLocalCounter (ch, no, val);
			break;
	}	
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}



		
#endif

typedef struct{
	int	no;
	int kakuritsu;
} RND_ITEM_EQU;
//-------------------------------------------------------------------------
// ラン??で联买
// 苞眶¨
// 		**buf  コ?ンド矢机误のアドレス
// 		*no
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL select_rnd( char **buf, int *no )
{
	char *buf2;
	int random_cnt = 0;
	int kakuritsu = 0;
	RND_ITEM_EQU rnd_item[ 256 ];
	int ii;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??アイテ??ット
	while( 1 ){
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		rnd_item[ random_cnt ].no = get_num2( &buf2 );
		//肌が'%'なら
		if( *buf2 == '%' ){
			//１バイトス?ップ
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				return FALSE;
			}
			//澄唯艰り叫し
			rnd_item[ random_cnt ].kakuritsu = get_num2( &buf2 );
		} else {
			rnd_item[ random_cnt ].kakuritsu = 1;
		}
		//澄唯裁换
		rnd_item[ random_cnt ].kakuritsu += kakuritsu;
		//呵络澄唯
		kakuritsu = rnd_item[ random_cnt ].kakuritsu;
		//肌へ
		random_cnt++;
		//姜位なら
		if( *buf2 == ')' ){
			break;
		}
		//肌が','でなければ
		if( *buf2 != ',' ){
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
	}
	//１バイトス?ップ
	buf2++;
	//ラン??
	kakuritsu = RAND( 1, kakuritsu );
	//澄唯で联买する
	for( ii = 0; ii < random_cnt; ii++ ){
		//このアイテ?に疯年なら
		if( rnd_item[ ii ].kakuritsu >= kakuritsu ){
			//?ットする
			*no = rnd_item[ ii ].no;
			break;
		}
	}
	//エラ〖なら
	if( ii == random_cnt ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 36 ]) );
		return FALSE;
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


#if 0	//踏蝗脱
typedef struct{
	int	no;
	int kakuritsu;
	int num;
} RND_ITEM2_EQU;
//-------------------------------------------------------------------------
// ラン??で联买２∈踏蝗脱∷
// 苞眶¨
// 		**buf  コ?ンド矢机误のアドレス
// 		*no
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL select_rnd2( char **buf, int *no, int *give_cnt )
{
	char *buf2;
	int random_cnt = 0;
	int kakuritsu = 0;
	RND_ITEM2_EQU rnd_item[ 256 ];
	int ii;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??アイテ??ット
	while( 1 ){
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		rnd_item[ random_cnt ].no = get_num2( &buf2 );
		//肌が'%'なら
		if( *buf2 == '%' ){
			//１バイトス?ップ
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				return FALSE;
			}
			//澄唯艰り叫し
			rnd_item[ random_cnt ].kakuritsu = get_num2( &buf2 );
		} else {
			rnd_item[ random_cnt ].kakuritsu = 1;
		}
		//澄唯裁换
		rnd_item[ random_cnt ].kakuritsu += kakuritsu;
		//呵络澄唯
		kakuritsu = rnd_item[ random_cnt ].kakuritsu;
		//肌が'$'なら
		if( *buf2 == '$' ){
			//１バイトス?ップ
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				return FALSE;
			}
			//改眶艰り叫し
			rnd_item[ random_cnt ].num = get_num2( &buf2 );
		} else {
			rnd_item[ random_cnt ].num = 1;
		}
		//肌へ
		random_cnt++;

		//姜位なら
		if( *buf2 == ')' ){
			//１バイトス?ップ
			buf2++;
			break;
		}
		//肌が','でなければ
		if( *buf2 != ',' ){
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
	}
	//ラン??
	kakuritsu = RAND( 1, kakuritsu );
	//澄唯で联买する
	for( ii = 0; ii < random_cnt; ii++ ){
		//このアイテ?に疯年なら
		if( rnd_item[ ii ].kakuritsu >= kakuritsu ){
			//?ットする
			*no = rnd_item[ ii ].no;
			*give_cnt = rnd_item[ ii ].num;
			break;
		}
	}
	//エラ〖なら
	if( ii == random_cnt ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 36 ]) );
		return FALSE;
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}
#endif


typedef struct{
	int	no;
	int kakuritsu;
	int num;
} RND_ITEM2_EQU;
//-------------------------------------------------------------------------
// ラン??で联买３∈２≤链挛改眶回年∷
// 苞眶¨
// 		**buf  コ?ンド矢机误のアドレス
// 		*no
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL select_rnd3( char **buf, int *no, int *give_cnt )
{
	char *buf2;
	int random_cnt = 0;
	int kakuritsu = 0;
	RND_ITEM2_EQU rnd_item[ 256 ];
	int ii;
	int num;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??アイテ??ット
	while( 1 ){
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		rnd_item[ random_cnt ].no = get_num2( &buf2 );
		//肌が'%'なら
		if( *buf2 == '%' ){
			//１バイトス?ップ
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				return FALSE;
			}
			//澄唯艰り叫し
			rnd_item[ random_cnt ].kakuritsu = get_num2( &buf2 );
		} else {
			rnd_item[ random_cnt ].kakuritsu = 1;
		}
		//澄唯裁换
		rnd_item[ random_cnt ].kakuritsu += kakuritsu;
		//呵络澄唯
		kakuritsu = rnd_item[ random_cnt ].kakuritsu;
		//肌が'$'なら
		if( *buf2 == '$' ){
			//１バイトス?ップ
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				return FALSE;
			}
			//改眶艰り叫し
			rnd_item[ random_cnt ].num = get_num2( &buf2 );
		} else {
			rnd_item[ random_cnt ].num = 1;
		}
		//肌へ
		random_cnt++;

		//姜位なら
		if( *buf2 == ')' ){
			//１バイトス?ップ
			buf2++;
			break;
		}
		//肌が','でなければ
		if( *buf2 != ',' ){
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
	}

	//链挛改眶回年なら
	if( *buf2 == ',' && *(buf2+1) != '(' ){
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 36 ]) );
			return FALSE;
		}
		//畔す改眶艰り叫し
		num = get_num2( &buf2 );
		//畔す改眶恃构
		for( ii = 0; ii < random_cnt; ii++ ){
			rnd_item[ ii ].num = num;
		}
	}

	//ラン??
	kakuritsu = RAND( 1, kakuritsu );
	//澄唯で联买する
	for( ii = 0; ii < random_cnt; ii++ ){
		//このアイテ?に疯年なら
		if( rnd_item[ ii ].kakuritsu >= kakuritsu ){
			//?ットする
			*no = rnd_item[ ii ].no;
			*give_cnt = rnd_item[ ii ].num;
			break;
		}
	}
	//エラ〖なら
	if( ii == random_cnt ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 36 ]) );
		return FALSE;
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// アイテ??ット
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_item( Char *talker, char **buf, int leak_flg )
{
	int no,ii,itemindex;
	char *buf2;
	int leak_level;
	int give_cnt = 1;
	int remain_max;
	int give_max;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??回年なら
	if( *buf2 == '(' ){
		buf2++;
		//ラン??で联买
		if( select_rnd3( &buf2, &no, &give_cnt ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
	} else {
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		no = get_num2( &buf2 );
		//改眶回年なら
		if( *buf2 == ',' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
				return FALSE;
			}
			//畔す改眶艰り叫し
			give_cnt = get_num2( &buf2 );
		}
	}
#if 0
#if 0
	//改眶回年なら
	if( *buf2 == ',' ){
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
		//畔す改眶艰り叫し
		give_cnt = get_num2( &buf2 );
	}
#else
	while( 1 ){
		//改眶回年なら
		if( *buf2 == ',' ){
			buf2++;
			//メッ?〖ジ回年回年なら
			if( *buf2 == '(' ){
				buf2++;
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
					return FALSE;
				}
				//メッ?〖ジ戎规艰り叫し
				mes_id = get_num2( &buf2 );
				//肌がメッ?〖ジ回年姜位でなければ
				if( *buf2 != ')' ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
					return FALSE;
				}
				buf2++;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
					return FALSE;
				}
				//畔す改眶艰り叫し
				give_cnt = get_num2( &buf2 );
			}
		} else {
			break;
		}
	}
#endif
#endif
	//メッ?〖ジ回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
	}

	//畔す改眶尸ル〖プ
	while( give_cnt > 0 ){
		//鄂いてる眷疥玫す
		ii = CHAR_findEmptyItemBox( talker );
		//エラ〖なら
		if( ii < 0 ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
		//アイテ?侯喇
		itemindex = ITEM_makeItemAndRegist( no );
		//痰跟なアイテ?なら
		if( !ITEM_CHECKINDEX( itemindex)){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
			return FALSE;
		}
		//凑年涟なら
		if( leak_flg == 1 ){
			//凑年涟?ット
			ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 0 );
		} else
		//凑年稿なら
		if( leak_flg == 2 ){
			//凑年稿?ット
			ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 1 );
		}
		//凑年レ?ル?ット
		leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
		//凑年稿なら
		if( leak_level == 1 ){
			//アイテ?叹?ット
			item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
		} else {
			//アイテ?叹?ット
			item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
		}
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈アイテ?缄に掐れた∷
			translate_format(moji, 0, LANG_MSG_EVENT_002, item_name );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), item_name );
		}
		SYSTEMMSG( talker, moji );
		//剩眶畔しで凑年稿でストッ?アイテ?なら
//		if( give_cnt >= 2 && leak_level == 1 && ITEM_getInt( itemindex ,ITEM_MAXREMAIN ) >= 2 ){
		//凑年稿でストッ?アイテ?なら
		if( leak_level == 1 && ITEM_getInt( itemindex ,ITEM_MAXREMAIN ) >= 2 ){
			//ストッ?呵络眶艰り叫し
			remain_max = ITEM_getInt( itemindex ,ITEM_MAXREMAIN );
			//畔す呵络眶?ット
			give_max = give_cnt;
			//呵络眶亩えてるなら
			if( give_max > remain_max ){
				//呵络に圭わせる
				give_max = remain_max;
			}
			//剩眶ストッ?する
			ITEM_setInt( itemindex ,ITEM_REMAIN, give_max );
			give_cnt -= give_max;
		} else {
			give_cnt--;
		}
		//アイテ??ット。积ち肩の?イン?もここで?ットする。
		CHAR_setItemIndex( talker, ii, itemindex );
		/*  Workデ〖?を肋年    */
		ITEM_setWorkInt( itemindex, ITEM_WORKOBJINDEX, -1 );
//		ITEM_setWorkInt( itemindex,ITEM_WORKCHARAINDEX, talker->CharaIndex);
//		ITEM_setInt( itemindex, ITEM_LEAKLEVEL,1);
//		ITEM_setCharPointer( itemindex, talker );
		CHAR_sendItemDataOne( talker, ii );
		LogItem(
			talker->cu.player.CdKey,
			talker->c.Name,
			no,			  /* アイテ?戎规 */
			"npc_event.c:GiveItem",
			talker->i.MapId,
			talker->i.Floor, talker->i.X, talker->i.Y
		);
		// "%s を侯喇した"
//		translate_format(msgbuf, sizeof( msgbuf), LANG_MSG_CHATMAGIC_C_009,
//											ITEM_getChar( itemindex, ITEM_FIRSTNAME));
//		CHAR_talkToCli( talker, NULL, msgbuf, CHAR_COLORWHITE);
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

#ifdef GIVE_STAMP_ITEM
//====================================================
//
// 癸磅烧きアイテ?侯喇
//
//====================================================
static BOOL set_itemstamp( Char *talker, char **buf )
{
    int no,ii,itemindex;
    char *buf2;
	char carved_seal[256];
    int give_cnt = 1;
    int otherflg = 0;

    // アドレスコピ〖
    buf2 = *buf;
    // 肌の矢机が眶猛でなければ
    if( check_num( buf2 ) == FALSE )
    {
        // エラ〖
        print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
        return FALSE;
    }
    // アイテ?ＩＤ艰り叫し
    no = get_num2( &buf2 );
    // 舱眶回年なら
    if( *buf2 == ',' )
    {
        buf2++;
        // 肌の矢机が眶猛でなければ
        if( check_num( buf2 ) == FALSE )
        {
            // エラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }
        // 畔す舱眶艰り叫し
        give_cnt = get_num2( &buf2 );
    }
    // メッ?〖ジ回年なら
    if( *buf2 == ',' )
    {
        buf2++;
		// 肌の矢机が眶猛ならば
		if( check_num( buf2 ) == TRUE )
		{
			// エラ〖
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
			return FALSE;
		}
		// 癸磅する矢机纽叫
		strcpy( carved_seal, buf2 );
    }

    // 畔す改眶尸ル〖プ
    while( give_cnt > 0 )
    {
        // 鄂いている眷疥を玫す
        ii = CHAR_findEmptyItemBox( talker );
        // エラ〖なら
        if( ii < 0 )
        {
            // エラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }
        // アイテ?侯喇
        itemindex = ITEM_makeItemAndRegist( no );
        // 痰跟なアイテ?なら
        if( !ITEM_CHECKINDEX( itemindex ) )
        {
            // エラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }
        // ストッ?アイテ?の眷圭、エラ〖
        if( ITEM_getInt(itemindex, ITEM_MAXREMAIN) > 1 )
        {
            // エラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }
        // 凑年レ?ル?ット
        // 凑年稿にする
        ITEM_setInt( itemindex, ITEM_LEAKLEVEL, 1 );

        if( !ITEM_getInt( itemindex, ITEM_LEAKLEVEL ) )
        {
            // 凑年稿になっていなかったらエラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }
        // ありえないけど、癸磅貉みだったらエラ〖
        if( (ITEM_getInt(itemindex,ITEM_OTHERFLG) & ITEM_OTHERFLG_INCUSE) &&
            strlen( ITEM_getChar( itemindex, ITEM_MEMO ) ) != 0 )
        {
            // エラ〖
            print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 84 ]) );
            return FALSE;
        }

        // 癸磅を?ットする
        ITEM_setChar( itemindex, ITEM_MEMO, carved_seal );
        // 癸磅フラグを惟てる
        otherflg = ITEM_getInt( itemindex, ITEM_OTHERFLG );
        otherflg |= ITEM_OTHERFLG_INCUSE;
        // ハンコフラグを久す
        otherflg = otherflg &~(ITEM_OTHERFLG_HANKO);
        ITEM_setInt( itemindex, ITEM_OTHERFLG, otherflg );
        // ＣＤＫＥＹを?ットする
        ITEM_setChar( itemindex, ITEM_RENAMECDKEY, talker->cu.player.CdKey );

        give_cnt--;

        // アイテ??ット。积ち肩の?イン?もここで?ットする。
        CHAR_setItemIndex( talker, ii, itemindex );
        // Workデ〖?を肋年
        ITEM_setWorkInt( itemindex, ITEM_WORKOBJINDEX, -1 );
        CHAR_sendItemDataOne( talker, ii );
        LogItem(
                talker->cu.player.CdKey,
                talker->c.Name,
                no,
                "npc_event.c:GiveStampItem",
                talker->i.MapId,
                talker->i.Floor, talker->i.X, talker->i.Y
        );
    }
    // ?ライアントに流慨
    CHAR_complianceParameter( talker );
    CHAR_send_CP_String( talker, CHAR_CP_ALL );
    CHAR_sendCToArroundCharacter( talker->w.ObjIndex );
    // アドレス构糠
    *buf = buf2;
    return TRUE;
}
#endif	/* GIVE_STAMP_ITEM */

//-------------------------------------------------------------------------
// アイテ?猴近
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
#ifdef PUK2
BOOL kill_item( Char *talker, char **buf, int leak_flg )
#else
static BOOL kill_item( Char *talker, char **buf, int leak_flg )
#endif
{
	int itemid,itemindex;
	char *buf2;
	int	equipplace;
	int del_cnt = 1;
	int stock_cnt;
	int leak_level;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 10 ]) );
		return FALSE;
	}
	//アイテ?戎规艰り叫し
	itemid = get_num2( &buf2 );

	while( 1 ){
		//改眶回年なら
		if( *buf2 == ',' ){
			buf2++;
			//メッ?〖ジ回年回年なら
			if( *buf2 == '(' ){
				buf2++;
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 10 ]) );
					return FALSE;
				}
				//メッ?〖ジ戎规艰り叫し
				mes_id = get_num2( &buf2 );
				//肌がメッ?〖ジ回年姜位でなければ
				if( *buf2 != ')' ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 10 ]) );
					return FALSE;
				}
				buf2++;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 10 ]) );
					return FALSE;
				}
				//猴近改眶艰り叫し
				del_cnt = get_num2( &buf2 );
			}
		} else {
			break;
		}
	}

	//猴近搀眶尸ル〖プ
	while( del_cnt > 0 ){
		//积ってるか拇べる
		for( equipplace = 0; equipplace < CHAR_MAXITEMHAVE; equipplace++ ){
			//アイテ?のインデッ?ス艰り叫し
			itemindex = CHAR_getItemIndex( talker, equipplace);
			//部か积ってるなら
			if( itemindex != -1){
				//このアイテ?なら
				if( itemid == ITEM_getInt( itemindex, ITEM_ID)) {
					//凑年レ?ル?ット
					leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
					//凑年涟なら
					if( leak_flg == 1 ){
						//凑年稿なら
						if( leak_level == 1 ){
							continue;
						}
					} else
					//凑年稿なら
					if( leak_flg == 2 ){
						//凑年涟なら
						if( leak_level == 0 ){
							continue;
						}
					}
					//凑年稿なら
					if( ITEM_getInt( itemindex ,ITEM_LEAKLEVEL ) == 1 ){
						//アイテ?叹?ット
						item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
					} else {
						//アイテ?叹?ット
						item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
					}
					//盖年メッ?〖ジなら
					if( mes_id == -1 ){
						//システ?メッ?〖ジ山绩∈アイテ?缄畔した∷
						translate_format(moji, 0, LANG_MSG_EVENT_003, item_name );
					} else {
						//システ?メッ?〖ジ山绩∈アイテ?缄畔した∷
						sprintf( moji, MSG_getMessage( mes_id ), item_name );
					}
					SYSTEMMSG( talker, moji );
					//ストッ?眶艰り叫し
					stock_cnt = ITEM_getInt( itemindex ,ITEM_REMAIN );
					//ストッ?してるなら
					if( stock_cnt >= 2 ){
						//猴近眶より驴いなら
						if( stock_cnt > del_cnt ){
							//ストッ?から负らす
							ITEM_setInt( itemindex ,ITEM_REMAIN, stock_cnt - del_cnt );
							//?ライアントに流慨
		    	   			CHAR_sendItemDataOne( talker, equipplace);
							del_cnt = 0;
							break;
						} else {
							del_cnt -= stock_cnt;
						}
					} else {
						del_cnt--;
					}
					/* 咐晤で猴近 */
					LogItem(
						talker->cu.player.CdKey,
						talker->c.Name,
			       		ITEM_getInt( itemindex, ITEM_ID ),  /* アイテ?戎规 */
						"npc_event.c:DelItem",
						talker->i.MapId,
						talker->i.Floor, talker->i.X, talker->i.Y
					);
					// "%sを久した。"
//					snprintf( buf, sizeof( buf),TMSG(LANG_MSG_CHATMAGIC_C_011),  ITEM_getAppropriateName(itemindex));
//					CHAR_talkToCli( talker, NULL, buf, CHAR_COLORWHITE);
					CHAR_unsetItem( talker, equipplace );
    	   			CHAR_sendItemDataOne( talker, equipplace);
					//CHAR_sendStatusString(ch, "I"); /* アイテ?构糠 */
					/* 坤肠から久殿 */
					ITEM_endExistItemsOne(itemindex);
					break;
				}
			}
		}
		//姜位なら
		if( del_cnt <= 0 ){
			break;
		}
		//もう积ってないなら
		if( equipplace == CHAR_MAXITEMHAVE ){
			//猴近しきってないなら
			if( del_cnt > 0 ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 26 ]) );
			}
			break;
		}
	}
	//疚规チェッ?∈アイテ?∷
	if( TITLE_TitleCheck( talker, TITLE_CHECK_EQUIPEVENT)){
		CHAR_sendTitle( talker);
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_send_CP2_String( talker, CHAR_CP2_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

//-------------------------------------------------------------------------
// ス?ッ?アイテ??ット
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_stack_item( Char *talker, char **buf )
{
	int no,ii,itemindex = -1;
	char *buf2;
	int leak_level;
	int give_cnt = 1;
	int remain_max;
	int give_max;
	int stack_max;
	int stack_cnt;
	int stack_space;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??回年なら
	if( *buf2 == '(' ){
		buf2++;
		//ラン??で联买
		if( select_rnd3( &buf2, &no, &give_cnt ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
			return FALSE;
		}
	} else {
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		no = get_num2( &buf2 );
		//改眶回年なら
		if( *buf2 == ',' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
				return FALSE;
			}
			//畔す改眶艰り叫し
			give_cnt = get_num2( &buf2 );
		}
	}
	// 呵络ス?ッ?眶を拇べる。
	stack_max = ITEM_getMaxRemainFromITEMtabl( no );
	//エラ〖なら
	if( stack_max == -1 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
		return FALSE;
	}
	//ス?ッ?アイテ?でなければ
	if( stack_max < 2 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 32 ]) );
		return FALSE;
	}
#if 0
#if 0
	//改眶回年なら
	if( *buf2 == ',' ){
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
			return FALSE;
		}
				//畔す改眶艰り叫し
		//猴近改眶艰り叫し
		give_cnt = get_num2( &buf2 );
	}
#else
	while( 1 ){
		//改眶回年なら
		if( *buf2 == ',' ){
			buf2++;
			//メッ?〖ジ回年回年なら
			if( *buf2 == '(' ){
				buf2++;
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
					return FALSE;
				}
				//メッ?〖ジ戎规艰り叫し
				mes_id = get_num2( &buf2 );
				//肌がメッ?〖ジ回年姜位でなければ
				if( *buf2 != ')' ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
					return FALSE;
				}
				buf2++;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
					return FALSE;
				}
				//畔す改眶艰り叫し
				give_cnt = get_num2( &buf2 );
			}
		} else {
			break;
		}
	}
#endif
#endif
	//メッ?〖ジ回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 33 ]) );
			return FALSE;
		}
	}

	//票じアイテ?の凑年稿ス?ッ?アイテ?に纳裁
	for( ii = CHAR_STARTITEMARRAY; ii < CHAR_MAXITEMHAVE; ii ++ ){
		//アイテ?ＩＮＤＥＸ艰り叫し
		itemindex = CHAR_getItemIndex( talker, ii);
		//部か积ってるなら
		if( itemindex != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( itemindex, ITEM_ID)) {
				//凑年涟なら
				if( ITEM_getInt( itemindex ,ITEM_LEAKLEVEL ) == 0 ){
					continue;
				}
				//ストッ?眶艰り叫し
				stack_cnt = ITEM_getInt( itemindex ,ITEM_REMAIN );
				//ストッ?鄂き眶?ット
				stack_space = stack_max - stack_cnt;
				//ストッ?に纳裁できるなら
				if( stack_space ){
					//链婶ストッ?できるなら
					if( stack_space >= give_cnt ){
						//链婶ストッ?
						ITEM_setInt( itemindex ,ITEM_REMAIN, stack_cnt + give_cnt );
						give_cnt = 0;
						//?ライアントに流慨
						CHAR_sendItemDataOne( talker, ii);
						break;
					} else {
						//艰り圭えず塔?ンにする
						ITEM_setInt( itemindex ,ITEM_REMAIN, stack_max );
						//畔した改眶尸?イナス
						give_cnt -= stack_space;
						//?ライアントに流慨
						CHAR_sendItemDataOne( talker, ii);
					}
				}
			}
		}
	}










	//畔す改眶尸ル〖プ
	while( give_cnt > 0 ){
		//鄂いてる眷疥玫す
		ii = CHAR_findEmptyItemBox( talker );
		//エラ〖なら
		if( ii < 0 ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 9 ]) );
			return FALSE;
		}
		//アイテ?侯喇
		itemindex = ITEM_makeItemAndRegist( no );
		//痰跟なアイテ?なら
		if( !ITEM_CHECKINDEX( itemindex)){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
			return FALSE;
		}
		//凑年稿?ット
		ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 1 );
		//凑年レ?ル?ット
		leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
		//剩眶畔しで凑年稿でストッ?アイテ?なら
		if( give_cnt >= 2 && leak_level == 1 && ITEM_getInt( itemindex ,ITEM_MAXREMAIN ) >= 2 ){
			//ストッ?呵络眶艰り叫し
			remain_max = ITEM_getInt( itemindex ,ITEM_MAXREMAIN );
			//畔す呵络眶?ット
			give_max = give_cnt;
			//呵络眶亩えてるなら
			if( give_max > remain_max ){
				//呵络に圭わせる
				give_max = remain_max;
			}
			//剩眶ストッ?する
			ITEM_setInt( itemindex ,ITEM_REMAIN, give_max );
			give_cnt -= give_max;
		} else {
			give_cnt--;
		}
		//アイテ??ット、积ち肩の?イン?もここでやる。
		CHAR_setItemIndex( talker, ii, itemindex );
		/*  Workデ〖?を肋年    */
		ITEM_setWorkInt( itemindex, ITEM_WORKOBJINDEX, -1 );
//		ITEM_setInt( itemindex, ITEM_LEAKLEVEL,1);
//		ITEM_setCharPointer( itemindex, talker );
//		ITEM_setWorkInt( itemindex, ITEM_WORKCHARAINDEX, talker->CharaIndex );
		CHAR_sendItemDataOne( talker, ii );
		LogItem(
			talker->cu.player.CdKey,
			talker->c.Name,
			no,			  /* アイテ?戎规 */
			"npc_event.c:GiveStackItem",
			talker->i.MapId,
			talker->i.Floor, talker->i.X, talker->i.Y
		);
		// "%s を侯喇した"
//		translate_format(msgbuf, sizeof( msgbuf), LANG_MSG_CHATMAGIC_C_009,
//											ITEM_getChar( itemindex, ITEM_FIRSTNAME));
//		CHAR_talkToCli( talker, NULL, msgbuf, CHAR_COLORWHITE);
	}
	//盖年メッ?〖ジなら
	if( mes_id == -1 ){
		//システ?メッ?〖ジ山绩∈アイテ?缄に掐れた∷
		translate_format(moji, 0, LANG_MSG_EVENT_002, ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
	} else {
		//システ?メッ?〖ジ山绩
		sprintf( moji, MSG_getMessage( mes_id ), ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
	}
	SYSTEMMSG( talker, moji );
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


#if 0	//踏蝗脱
//-------------------------------------------------------------------------
// アイテ??ット２∈踏蝗脱∷
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_item2( Char *talker, char **buf, int leak_flg )
{
	int no,ii,itemindex;
	char *buf2;
	int leak_level;
	int give_cnt = 1;
	int remain_max;
	int give_max;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??回年なら
	if( *buf2 == '(' ){
		buf2++;
		//ラン??で联买
		if( select_rnd2( &buf2, &no, &give_cnt ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
			return FALSE;
		}
	} else {
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
			return FALSE;
		}
		//アイテ?戎规艰り叫し
		no = get_num2( &buf2 );
	}

	//メッ?〖ジ回年回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
			return FALSE;
		}
	}

	//畔す改眶尸ル〖プ
	while( give_cnt > 0 ){
		//鄂いてる眷疥玫す
		ii = CHAR_findEmptyItemBox( talker );
		//エラ〖なら
		if( ii < 0 ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 41 ]) );
			return FALSE;
		}
		//アイテ?侯喇
		itemindex = ITEM_makeItemAndRegist( no );
		//痰跟なアイテ?なら
		if( !ITEM_CHECKINDEX( itemindex)){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
			return FALSE;
		}
		//凑年涟なら
		if( leak_flg == 1 ){
			//凑年涟?ット
			ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 0 );
		} else
		//凑年稿なら
		if( leak_flg == 2 ){
			//凑年稿?ット
			ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 1 );
		}
		//凑年レ?ル?ット
		leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
		//凑年稿なら
		if( leak_level == 1 ){
			//アイテ?叹?ット
			item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
		} else {
			//アイテ?叹?ット
			item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
		}
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈アイテ?缄に掐れた∷
			translate_format(moji, 0, LANG_MSG_EVENT_002, item_name );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), item_name );
		}
		SYSTEMMSG( talker, moji );
		//剩眶畔しで凑年稿でストッ?アイテ?なら
		if( give_cnt >= 2 && leak_level == 1 && ITEM_getInt( itemindex ,ITEM_MAXREMAIN ) >= 2 ){
			//ストッ?呵络眶艰り叫し
			remain_max = ITEM_getInt( itemindex ,ITEM_MAXREMAIN );
			//畔す呵络眶?ット
			give_max = give_cnt;
			//呵络眶亩えてるなら
			if( give_max > remain_max ){
				//呵络に圭わせる
				give_max = remain_max;
			}
			//剩眶ストッ?する
			ITEM_setInt( itemindex ,ITEM_REMAIN, give_max );
			give_cnt -= give_max;
		} else {
			give_cnt--;
		}
		//アイテ??ット。积ち肩の?イン?もここで?ットする。
		CHAR_setItemIndex( talker, ii, itemindex );
		/*  Workデ〖?を肋年    */
		ITEM_setWorkInt( itemindex, ITEM_WORKOBJINDEX, -1 );
//		ITEM_setWorkInt( itemindex,ITEM_WORKCHARAINDEX, talker->CharaIndex);
//		ITEM_setInt( itemindex, ITEM_LEAKLEVEL,1);
//		ITEM_setCharPointer( itemindex, talker );
		CHAR_sendItemDataOne( talker, ii );
		LogItem(
			talker->cu.player.CdKey,
			talker->c.Name,
			no,			  /* アイテ?戎规 */
			"npc_event.c:GiveItem",
			talker->i.MapId,
			talker->i.Floor, talker->i.X, talker->i.Y
		);
		// "%s を侯喇した"
//		translate_format(msgbuf, sizeof( msgbuf), LANG_MSG_CHATMAGIC_C_009,
//											ITEM_getChar( itemindex, ITEM_FIRSTNAME));
//		CHAR_talkToCli( talker, NULL, msgbuf, CHAR_COLORWHITE);
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}
#endif


//-------------------------------------------------------------------------
// 疥积垛笼负
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL add_gold( Char *talker, char **buf )
{
	char *buf2;
	int gold;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
		return FALSE;
	}
	//垛驰艰り叫し
	gold = get_num2( &buf2 );
	//メッ?〖ジ回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
			return FALSE;
		}
	}
	//エラ〖なら
	if( talker->iu.player.Gold + gold < 0 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
		return FALSE;
	}
	//エラ〖なら
	if( talker->iu.player.Gold + gold > CHAR_MAXGOLDHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 11 ]) );
		return FALSE;
	}
	//疥积垛恃构
	talker->iu.player.Gold += gold;
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker , CHAR_CP_GOLD );
	//笼えたなら
	if( gold >= 0 ){
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈ゴ〖ルド缄に掐れた∷
			translate_format(moji, 0, LANG_MSG_EVENT_000, gold );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), gold );
		}
		//システ?メッ?〖ジ山绩
		SYSTEMMSG( talker, moji );
	} else {
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈ゴ〖ルド缄畔した∷
			translate_format(moji, 0, LANG_MSG_EVENT_001, -gold );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), -gold );
		}
		SYSTEMMSG( talker, moji );
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// レ?ル■疥积垛笼负
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL add_gold_level( Char *talker, char **buf )
{
	char *buf2;
	int gold;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
		return FALSE;
	}
	//垛驰艰り叫し
	gold = get_num2( &buf2 );
	//メッ?〖ジ回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
			return FALSE;
		}
	}
	//垛驰■レ?ル
	gold *= talker->i.Lv;
	//エラ〖なら
	if( talker->iu.player.Gold + gold < 0 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
		return FALSE;
	}
	//エラ〖なら
	if( talker->iu.player.Gold + gold > CHAR_MAXGOLDHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 12 ]) );
		return FALSE;
	}
	//疥积垛恃构
	talker->iu.player.Gold += gold;
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker , CHAR_CP_GOLD );
	//笼えたなら
	if( gold >= 0 ){
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈ゴ〖ルド缄に掐れた∷
			translate_format(moji, 0, LANG_MSG_EVENT_000, gold );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), gold );
		}
		SYSTEMMSG( talker, moji );
	} else {
		//盖年メッ?〖ジなら
		if( mes_id == -1 ){
			//システ?メッ?〖ジ山绩∈ゴ〖ルド缄畔した∷
			translate_format(moji, 0, LANG_MSG_EVENT_001, -gold );
		} else {
			//システ?メッ?〖ジ山绩
			sprintf( moji, MSG_getMessage( mes_id ), -gold );
		}
		SYSTEMMSG( talker, moji );
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// レ?ル恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_level( Char *talker, char **buf )
{
	char *buf2;
	int level;
	int add_flg = 0;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 13 ]) );
		return FALSE;
	}
	//プラスか?イナスなら
	if( *buf2 == '+' || *buf2 == '-' ){
		//陵滦回年
		add_flg = 1;
	}
	//レ?ル艰り叫し
	level = get_num2( &buf2 );
	//陵滦回年なら
	if( add_flg ){
		//レ?ル恃构
		talker->i.Lv += level;
		//レ?ル０笆布なら
		if( talker->i.Lv <= 0 ){
			//レ?ル１にする
			talker->i.Lv = 1;
		}
	} else {
		talker->i.Lv = level;
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker , CHAR_CP_LV );
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// ワ〖プ
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL warp( Char *talker, char **buf )
{
	char *buf2;
	int mapid[16],fl[16],x[16],y[16];
	int ii;
	Char *ch;
	int rnd_cnt;

	//アドレスコピ〖
	buf2 = *buf;
	for( rnd_cnt = 0; rnd_cnt < 16; rnd_cnt++ ){
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//?ップＩＤ艰り叫し
		mapid[ rnd_cnt ] = get_num2( &buf2 );
	
		//肌は','でなければ
		if( *buf2 != ',' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//フロア艰り叫し
		fl[ rnd_cnt ] = get_num2( &buf2 );
	
		//肌は','でなければ
		if( *buf2 != ',' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//Ｘ郝筛艰り叫し
		x[ rnd_cnt ] = get_num2( &buf2 );
	
		//肌は','でなければ
		if( *buf2 != ',' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 14 ]) );
			return FALSE;
		}
		//Ｙ郝筛艰り叫し
		y[ rnd_cnt ] = get_num2( &buf2 );

		//肌は','でなければ
		if( *buf2 != ',' ){
			//ラン??姜位
			break;
		}
		//１バイトス?ップ
		buf2++;
	}

	//ラン??回年なら
	if( rnd_cnt ){
		rnd_cnt = RAND( 0, rnd_cnt );
	}

	/*  フロアチェッ?する  */
	MAP_setprintErrorMessage( FALSE);
	if( !MAP_IsValidCoordinate( mapid[ rnd_cnt ], fl[ rnd_cnt ], x[ rnd_cnt ], y[ rnd_cnt ] )) {
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 15 ]) );
		return FALSE;
	}
	else {
#ifdef _DEBUG
	print ("\nNPC_EVENT(warp) :: mapId : %d / floor : %d / X : %d / Y : %d\n", mapid[rnd_cnt], fl[rnd_cnt], x[rnd_cnt],y[rnd_cnt]);
#endif

		//まず极尸ワ〖プ
		CHAR_warpToSpecificPoint( talker, mapid[ rnd_cnt ], fl[ rnd_cnt ], x[ rnd_cnt ], y[ rnd_cnt ] );
		//パ〖ティ〖なら
		if( talker->wu.player.PartyMode != CHAR_PARTY_NONE ){
			Char *lch;
			//パ〖ティ〖のＬなら
			if( talker->wu.player.PartyMode == CHAR_PARTY_LEADER ){
				lch = talker;
			} else {
				lch = talker->wu.player.PartyChar[ 0 ];
			}
			//パ〖ティ〖もワ〖プさせる
			for( ii=0; ii<CHAR_PARTYMAX; ii++ ){
				//灰の?イン?〖艰り叫し
				ch = lch->wu.player.PartyChar[ ii ];
				// ?イン?〖が铜跟なら
				if( CHAR_CheckCharPointer( ch ) == TRUE ){
					//极尸でなければ
					if( ch != talker ){
						//戮客もワ〖プ
						CHAR_warpToSpecificPoint( ch, mapid[ rnd_cnt ], fl[ rnd_cnt ], x[ rnd_cnt ], y[ rnd_cnt ] );
					}
				}
			}
#ifdef PUK2
			// 呵稿に、ワ〖プした?ャラからまとめてパ〖ティ攫鼠流慨
			CHAR_sendPartyParamAll( talker );
#endif
		}
	}
	MAP_setprintErrorMessage( FALSE);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// ?ット侯喇
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL make_pet( Char *ch, char **buf )
{
	char *buf2;
//	char msgbuf[64];
	int	enemynum;
	int	enemyid=0;
	int		i;
	Char *work_ch;
	Char *pet;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//ラン??回年なら
	if( *buf2 == '(' ){
		buf2++;
		//ラン??で联买
		if( select_rnd( &buf2, &enemyid ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 18 ]) );
			return FALSE;
		}
	} else {
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 18 ]) );
			return FALSE;
		}
		/* 苞眶のIDからindexを艰评 */
		enemyid = get_num2( &buf2 );
	}
	//メッ?〖ジ回年回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 18 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 18 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 18 ]) );
			return FALSE;
		}
	}
	enemynum = ENEMY_getEnemyNum();
	for( i = 0; i < enemynum; i ++ ) {
		if( ENEMY_getInt( i, ENEMY_ID) == enemyid) {
			break;
		}
	}
	if( i == enemynum ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 19 ]) );
		return FALSE;
	}
	work_ch = ENEMY_createEnemy(-1, i, RAND( ENEMY_getInt( i, ENEMY_LV_MIN), ENEMY_getInt( i, ENEMY_LV_MAX)),0);
	
	if( !work_ch) {
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 19 ]) );
		return FALSE;
	}
	pet = PET_createPetFromCharaIndex( ch, work_ch);
	if( pet == NULL){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 19 ]) );
		return FALSE;
	}
	// 稿幌琐
	/*  ?ャラの猴近    */
	CHAR_endCharOneArray( work_ch );
//	snprintf( msgbuf,sizeof( msgbuf), "Pet を侯喇した。index[%d]",pet->CharaIndex);
//	CHAR_talkToCli( ch, NULL, msgbuf,  CHAR_COLORWHITE);
	//ゲットレ?ル?ット
	pet->iu.pet.PetGetLv = pet->i.Lv;

	/******************/
	/* ?ット攫鼠流る */
	/******************/
	// どこにはいったかな
	for( i = 0; i < CHAR_MAXPETHAVE; i ++ ){
		if( CHAR_getHavePetCharPointer( ch, i ) == pet )break;
	}
	CHAR_complianceParameter( pet );
	CHAR_PetLevelupsend( ch, i, TRUE);

	// ?ットを侯喇したログを艰る
	LogPet(
		ch->cu.player.CdKey,
		ch->c.Name,
		pet->c.Name,
		pet->cu.pet.UserPetName, 
		pet->i.Lv,
		"npc_event.c:EventAdd",
		ch->i.MapId,
		ch->i.Floor,	// 郝筛
		ch->i.X,
		ch->i.Y
	);

	//盖年メッ?〖ジなら
	if( mes_id == -1 ){
		//システ?メッ?〖ジ山绩∈?ット缄に掐れた∷
		translate_format(moji, 0, LANG_MSG_EVENT_002, CHAR_getUseName( pet ) );
	} else {
		//システ?メッ?〖ジ山绩
		sprintf( moji, MSG_getMessage( mes_id ), CHAR_getUseName( pet ) );
	}
	SYSTEMMSG( ch, moji );

	// ?ットデ〖?流る
	CHAR_send_KP_String( ch, i, CHAR_KP_ALL);
	CHAR_send_KP2_String( ch, i, CHAR_KP2_ALL);
	CHAR_sendPetTechData( ch, i);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// レ?ル笆惧の?ット缩沪
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL kill_pet_h( Char *tch, char **buf )
{
	char *buf2;
	Char *pch = NULL;
	int petid;
	int havepetindex;
	int petlevel = 1;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 20 ]) );
		return FALSE;
	}
	//?ットＩＤ艰り叫し
	petid = get_num2( &buf2 );
	while( 1 ){
		//肌は','なら
		if( *buf2 == ',' ){
			//１バイトス?ップ
			buf2++;
			//メッ?〖ジ回年回年なら
			if( *buf2 == '(' ){
				buf2++;
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 20 ]) );
					return FALSE;
				}
				//メッ?〖ジ戎规艰り叫し
				mes_id = get_num2( &buf2 );
				//肌がメッ?〖ジ回年姜位でなければ
				if( *buf2 != ')' ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 20 ]) );
					return FALSE;
				}
				buf2++;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 20 ]) );
					return FALSE;
				}
				//?ットレ?ル艰り叫し
				petlevel = get_num2( &buf2 );
			}
		} else {
			break;
		}
	}

	//积ってる?ット眶える
	for( havepetindex = 0; havepetindex < CHAR_MAXPETHAVE; havepetindex++ ){
		// ?ットのインデッ?ス
		pch = (Char *)tch->player_addon->PetPointer[ havepetindex ];
		// ?イン?〖が铜跟なら
		if( CHAR_CheckCharPointer( pch ) == TRUE ){
			//この?ットなら
			if( pch->iu.pet.PetId == petid ){
				//レ?ルがあるか
				if( pch->i.Lv >= petlevel ){
					break;
				}
			}
		}
	}
	//?ット积ってないなら
	if( havepetindex == CHAR_MAXPETHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 21 ]) );
		return FALSE;
	}

#ifdef PET_FIELD
	// フィ〖ルド弥きのチェッ?
	if( pch->iu.pet.DepartureBattleStatus == CHAR_PET_FIELD ){
		// フィ〖ルド弥き豺近
		PET_stopFieldPet( tch, havepetindex );
	}
#endif
#ifdef PET_RIDE
	// ライド面の?ット々
	if( tch->w.walk.ridePet == havepetindex ){
		// ライドを动扩姜位
		PET_stopRidePet( tch );
	}
#endif

	// ?ットを久したログを艰る
	LogPet(
		tch->cu.player.CdKey,
		tch->c.Name,
		pch->c.Name,
		pch->cu.pet.UserPetName, 
		pch->i.Lv,
		"npc_event.c:EventDel",
		tch->i.MapId,
		tch->i.Floor,	// 郝筛
		tch->i.X,
		tch->i.Y
	);

	//盖年メッ?〖ジなら
	if( mes_id == -1 ){
		//システ?メッ?〖ジ山绩∈?ット缄畔した∷
		translate_format(moji, 0, LANG_MSG_EVENT_003, CHAR_getUseName( pch ) );
	} else {
		//システ?メッ?〖ジ山绩
		sprintf( moji, MSG_getMessage( mes_id ), CHAR_getUseName( pch ) );
	}
	SYSTEMMSG( tch, moji );

    /* ?ット腕から久す */
    CHAR_setHavePetCharPointer( tch, havepetindex, NULL );
	//その?ットが里飘觉轮だったら黎睡?ット攫鼠を久す
#ifdef PUK2
	if((pch->iu.pet.DepartureBattleStatus&0x0f) == CHAR_PET_BATTLE_FIRST)
#else
	if(pch->iu.pet.DepartureBattleStatus == CHAR_PET_BATTLE_FIRST)
#endif
	{
		//プレイヤの数も恃构
		tch->iu.player.DefaultPet = -1;
	}
#ifdef PUK2
	// その?ットがつれ殊いている?ットだったら、息れ殊き攫鼠を久す
	if( pch->iu.pet.DepartureBattleStatus & CHAR_PET_WALK ){
		tch->w.walk.walkPet = -1;
		// 馋りに梦らせてやる
		CHAR_sendCToArroundCharacter( tch->w.ObjIndex );
	}
#endif
	/* 联买した?ットから嘲れたと?ライアントに流慨 */
	CHAR_send_KP_String( tch, havepetindex, CHAR_KP_DEPARTUREBATTLESTATUS );
	//?ット缩沪
    CHAR_endCharOneArray( pch );	
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// レ?ル笆布の?ット缩沪
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL kill_pet_l( Char *tch, char **buf )
{
	char *buf2;
	Char *pch = NULL;
	int petid;
	int havepetindex;
	int petlevel = 9999;
	char moji[ 256 ];
	int mes_id = -1;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 22 ]) );
		return FALSE;
	}
	//?ットＩＤ艰り叫し
	petid = get_num2( &buf2 );
	while( 1 ){
		//肌は','なら
		if( *buf2 == ',' ){
			//１バイトス?ップ
			buf2++;
			//メッ?〖ジ回年回年なら
			if( *buf2 == '(' ){
				buf2++;
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 22 ]) );
					return FALSE;
				}
				//メッ?〖ジ戎规艰り叫し
				mes_id = get_num2( &buf2 );
				//肌がメッ?〖ジ回年姜位でなければ
				if( *buf2 != ')' ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 22 ]) );
					return FALSE;
				}
				buf2++;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 22 ]) );
					return FALSE;
				}
				//?ットレ?ル艰り叫し
				petlevel = get_num2( &buf2 );
			}
		} else {
			break;
		}
	}

	//积ってる?ット眶える
	for( havepetindex = 0; havepetindex < CHAR_MAXPETHAVE; havepetindex++ ){
		// ?ットのインデッ?ス
		pch = (Char *)tch->player_addon->PetPointer[ havepetindex ];
		// ?イン?〖が铜跟なら
		if( CHAR_CheckCharPointer( pch ) == TRUE ){
			//この?ットなら
			if( pch->iu.pet.PetId == petid ){
				//レ?ルがあるか
				if( pch->i.Lv <= petlevel ){
					break;
				}
			}
		}
	}
	//?ット积ってないなら
	if( havepetindex == CHAR_MAXPETHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 21 ]) );
		return FALSE;
	}

#ifdef PET_FIELD
	// フィ〖ルド弥きのチェッ?
	if( pch->iu.pet.DepartureBattleStatus == CHAR_PET_FIELD ){
		// フィ〖ルド弥き豺近
		PET_stopFieldPet( tch, havepetindex );
	}
#endif
#ifdef PET_RIDE
	// ライド面の?ット々
	if( tch->w.walk.ridePet == havepetindex ){
		// ライドを动扩姜位
		PET_stopRidePet( tch );
	}
#endif

	// ?ットを久したログを艰る
	LogPet(
		tch->cu.player.CdKey,
		tch->c.Name,
		pch->c.Name,
		pch->cu.pet.UserPetName, 
		pch->i.Lv,
		"npc_event.c:EventDel",
		tch->i.MapId,
		tch->i.Floor,	// 郝筛
		tch->i.X,
		tch->i.Y
	);

	//盖年メッ?〖ジなら
	if( mes_id == -1 ){
		//システ?メッ?〖ジ山绩∈?ット缄畔した∷
		translate_format(moji, 0, LANG_MSG_EVENT_003, CHAR_getUseName( pch ) );
	} else {
		//システ?メッ?〖ジ山绩
		sprintf( moji, MSG_getMessage( mes_id ), CHAR_getUseName( pch ) );
	}
	SYSTEMMSG( tch, moji );

    /* ?ット腕から久す */
    CHAR_setHavePetCharPointer( tch, havepetindex, NULL );
	//その?ットが里飘觉轮だったら黎睡?ット攫鼠を久す
#ifdef PUK2
	if((pch->iu.pet.DepartureBattleStatus&0x0f) == CHAR_PET_BATTLE_FIRST)
#else
	if(pch->iu.pet.DepartureBattleStatus == CHAR_PET_BATTLE_FIRST)
#endif
	{
		//プレイヤの数も恃构
		tch->iu.player.DefaultPet = -1;
	}
#ifdef PUK2
	// その?ットがつれ殊いている?ットだったら、息れ殊き攫鼠を久す
	if( pch->iu.pet.DepartureBattleStatus & CHAR_PET_WALK ){
		tch->w.walk.walkPet = -1;
		// 馋りに梦らせてやる
		CHAR_sendCToArroundCharacter( tch->w.ObjIndex );
	}
#endif
	/* 联买した?ットから嘲れたと?ライアントに流慨 */
	CHAR_send_KP_String( tch, havepetindex, CHAR_KP_DEPARTUREBATTLESTATUS );
	//?ット缩沪
    CHAR_endCharOneArray( pch );	
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}
#ifdef ADDSKILLSLOT
static BOOL add_skill_slot( Char *talker )
{
//	char buf[128];

	// ス?ルスロットを１笼裁
	if( talker->i.HaveSkillLimit < CHAR_MAXSKILLHAVE )
	{
		++talker->i.HaveSkillLimit;
	}
	// ?ライアントに流慨
	CHAR_send_CP2_String( talker, CHAR_CP2_SKILLSLOT );
	// システ?メッ?〖ジ山绩
//	sprintf( buf, "ス?ルスロットが %d になった。", talker->i.HaveSkillLimit );
//	SYSTEMMSG( talker, buf );

	return TRUE;
}
#endif	/* ADDSKILLSLOT */

//-------------------------------------------------------------------------
// ウインドウ山绩
// 苞眶¨
// 		*my
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		end_flg  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL disp_window( Char *talker, Char *my, char **buf, int end_flg )
{
	int fd;
	int mes_id;
	char *buf2;
	char sendbuf[1024];
	int buttontype = 0;
	char sendbuf2[1024];
	int centering_flg;
	int ii;
	char c;

	//ト〖?〖のＦＤ艰り叫し
	fd = getfdFromChar( talker);
	//ＦＤが痰跟なら
	if( fd <= -1 ){
		return FALSE;
	}

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//矢机误木儡回年でなければ
		if( *buf2 != '"' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
			return FALSE;
		}
		//'"'ス?ップ
		buf2++;
		//メッ?〖ジ艰り叫し
		ii = 0;
		while( 1 ){
			c = *buf2;
			//姜位なら
			if( c == '"' || c == 0x0a || c == 0 ){
				sendbuf[ ii ] = 0;
				//姜位コ〖ド笆嘲なら
				if( c ){
					//１バイトス?ップ
					buf2++;
				}
				break;
			}
			sendbuf[ ii++ ] = c;
			buf2++;
		}
	} else {
		//メッ?〖ジＩＤ艰り叫し
		mes_id = get_num2( &buf2 );
		//メッ?〖ジ?ット
		snprintf( sendbuf, sizeof( sendbuf ), "%s", MSG_getMessage( mes_id ) );
	}
	//ウインドウパラメ〖?艰り叫し
	while( 1 ){
		//肌が','でなければ
		if( *buf2 != ',' ){
			break;
		}
		//１バイトス?ップ
		buf2++;
		//肌が"cancel"なら
		if( strncmp2( buf2, "cancel", 6 ) == 0 ){
			//バッファ渴める
			buf2 += 6;
			buttontype |= WINDOW_BUTTONTYPE_CANCEL;
			continue;
		}
		//肌が"ok"なら
		if( strncmp2( buf2, "ok", 2 ) == 0 ){
			//バッファ渴める
			buf2 += 2;
			buttontype |= WINDOW_BUTTONTYPE_OK;
			continue;
		}
		//肌が"yes"なら
		if( strncmp2( buf2, "yes", 3 ) == 0 ){
			//バッファ渴める
			buf2 += 3;
			buttontype |= WINDOW_BUTTONTYPE_YES;
			continue;
		}
		//肌が"no"なら
		if( strncmp2( buf2, "no", 2 ) == 0 ){
			//バッファ渴める
			buf2 += 2;
			buttontype |= WINDOW_BUTTONTYPE_NO;
			continue;
		}
		//肌が"prev"なら
		if( strncmp2( buf2, "prev", 4 ) == 0 ){
			//バッファ渴める
			buf2 += 4;
			buttontype |= WINDOW_BUTTONTYPE_PREV;
			continue;
		}
		//肌が"next"なら
		if( strncmp2( buf2, "next", 4 ) == 0 ){
			//バッファ渴める
			buf2 += 4;
			buttontype |= WINDOW_BUTTONTYPE_NEXT;
			continue;
		}
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
		return FALSE;
	}
	//ＥｎｄＷｉｎｄｏｗなら
	if( end_flg ){
		//ウインドウ山绩で借妄虑ち磊り
		talker->wu.player.WindowBuffer = -1;
		//ＯＫ??ン盖年
		buttontype = WINDOW_BUTTONTYPE_OK;
	} else {
		//附哼の悸乖借妄乖眶瘦赂
		talker->wu.player.WindowBuffer = get_line_cnt;
	}
	//??ン攫鼠瘦赂
	talker->wu.player.WindowBuffer3 = buttontype;
	//叹涟赁掐へ
	centering_flg = Event_insert_name( talker, sendbuf, sendbuf2 );
	//?ン?リングなら
	if( centering_flg ){
		//矢机?ン?リング
		Event_centering( sendbuf2 );
	}
	//绕脱ウインドウ山绩
	nrproto_WN_send( fd, WINDOW_MESSAGETYPE_MESSAGE,
					 buttontype,
					 CHAR_WINDOWTYPE_EVENT_NPC,
					 my->w.ObjIndex,
					 sendbuf2);
					 
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

#ifdef PUK2
static BOOL shapeOn (Char* my, Char* talker, char** buf)
{
	Char* party[CHAR_PARTYMAX];
	char mes[1024];
	int member;
	int i;

	member = CHAR_getPartyArray (talker, party);

	if (CHAR_makeCAOPT1String (my->w.ObjIndex, mes, sizeof (mes), 60, my->i.BaseImageNumber)) {
		for (i = 0; i < member; ++i) 
			CONNECT_appendCAbuf (getfdFromChar (party[i]), mes, strlen (mes));
	}

	return TRUE;
}

static BOOL shapeOff (Char* my, Char* talker, char** buf)
{
	Char* party[CHAR_PARTYMAX];
	char mes[1024];
	int member;
	int i;

	member = CHAR_getPartyArray (talker, party);

	if (CHAR_makeCAOPT1String (my->w.ObjIndex, mes, sizeof (mes), 60, 0)) {
		for (i = 0; i < member; ++i) 
			CONNECT_appendCAbuf (getfdFromChar (party[i]), mes, strlen (mes));
	}
	return TRUE;
}

static BOOL shapeNext (Char* my, Char* talker, char** buf)
{
	Char* party[CHAR_PARTYMAX];
	char mes[1024];
	char* buf2 = *buf;
	int i;
	int member;
	int no;

	if (check_num (buf2) == FALSE) {
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 72 ]) );
		return FALSE;
	}
	no = get_num2 (&buf2);

	member = CHAR_getPartyArray (talker, party);
	if (CHAR_makeCAOPT1String (my->w.ObjIndex, mes, sizeof (mes), 60, no)) {
		for (i = 0; i < member; ++i)
			CONNECT_appendCAbuf (getfdFromChar (party[i]), mes, strlen (mes));
	}

	*buf = buf2;
	return TRUE;
}

#ifdef CSHAPENEXT
static BOOL C_shapeNext( Char *my, Char *talker, char **buf )
{
	char msg[1024];
	char *buf2 = *buf;
	int no;

	if( check_num( buf2 ) == FALSE )
	{
		print("\n%s:%d %s\n", file_name, read_line, TMSG(com_err[72]) );
		return FALSE;
	}
	no = get_num2( &buf2 );

	if( CHAR_makeCAOPT1String( my->w.ObjIndex, msg, sizeof( msg ), 60, no ) )
	{
		CONNECT_appendCAbuf( getfdFromChar( talker ), msg, strlen( msg ) );
	}

	*buf = buf2;
	return TRUE;
}
#endif	/* CSHAPENEXT */

static BOOL moveTo (Char* my, Char* talker, char** buf)
{
	Char* party[CHAR_PARTYMAX];
	char* buf2 = *buf;
	char mes[1024], tmp[64];
	int member, index;
	int x, y;
	int i;

	if (check_num (buf2) == FALSE) {
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 71 ]) );
		return FALSE;
	}
	x = get_num2 (&buf2);

	if( *buf2 != ',' ){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 71 ]) );
		return FALSE;
	}
	++buf2;
	
	if (check_num (buf2) == FALSE) {
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 71 ]) );
		return FALSE;
	}
	y = get_num2 (&buf2);

	member = CHAR_getPartyArray (talker, party);
	index = my->w.ObjIndex;

	*buf = buf2;
	if (CHECKOBJECT (index) == FALSE)
		return TRUE;
	if (OBJECT_getType (index) != OBJTYPE_CHARA)
		return TRUE;
	snprintf (mes,sizeof (mes),"%s|%d|%d|%d|%d|%d",
		cnv10to62 (index, tmp, sizeof (tmp)),
		OBJECT_getX (index) + x, OBJECT_getY (index) + y,
		51, my->i.Dir, 0);
	
	for (i = 0; i < member; ++i)
		CONNECT_appendCAbuf (getfdFromChar (party[i]), mes, strlen (mes));

	return TRUE;
}

static BOOL giveItemEx (Char* talker, Char* my, char** buf)
{
	char* buf2 = *buf;
	char* tmp;
	int no0, no1;
	int i;
	
	check_data[0] = 0;
	if (check_num (buf2) == FALSE) {
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 70 ]) );
		return FALSE;
	}
	no0 = get_num2 (&buf2);

	if( *buf2 != ',' ){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 70 ]) );
		return FALSE;
	}
	++buf2;
	
	if (check_num (buf2) == FALSE) {
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 70 ]) );
		return FALSE;
	}
	no1 = get_num2 (&buf2);

	if( *buf2 != ',' ){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 70 ]) );
		return FALSE;
	}
	++buf2;

	for (i = CHAR_STARTITEMARRAY; i < CHAR_MAXITEMHAVE; ++i) {
		if (CHAR_getItemIndex (talker, i) == -1)
			++check_data[0];
	}

	if (check_data[0] >= no1) {
		if ((tmp = strrchr (*buf, ',')))
			*tmp = '\0';
		set_item (talker, buf, 0);
	}
	else
		disp_window(talker, my, &buf2, 1);
	
	return TRUE;
}
#endif

#ifdef ITEM_LIMITTIME
static BOOL givetimeitem( Char *talker, char **buf)
{
	int no,ii,itemindex;
	char *buf2;
	int leak_level;
	int give_cnt = 1;
	int remain_max;
	int give_max;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;
	int expmin = 0;

	buf2 = *buf;
	if( *buf2 == '(' ){
		buf2++;
		if( select_rnd3( &buf2, &no, &give_cnt ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
			return FALSE;
		}
	} else {
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
			return FALSE;
		}
		no = get_num2( &buf2 );
		if( *buf2 == ',' ){
			buf2++;
			if( check_num( buf2 ) == FALSE ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
				return FALSE;
			}
			give_cnt = get_num2( &buf2 );
		}
		if( *buf2 == ',' ){
			buf2++;
			if( check_num( buf2 ) == FALSE ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
				return FALSE;
			}
			expmin = get_num2( &buf2 );
		}
	}

	if( *buf2 == ',' ){
		buf2++;
		if( *buf2 == '(' ){
			buf2++;
			if( check_num( buf2 ) == FALSE ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
				return FALSE;
			}
			mes_id = get_num2( &buf2 );
			if( *buf2 != ')' ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
				return FALSE;
			}
			buf2++;
		} else {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
			return FALSE;
		}
	}

	while( give_cnt > 0 ){
		ii = CHAR_findEmptyItemBox( talker );
		if( ii < 0 ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_EVENT_ERR_090) );
			return FALSE;
		}
		itemindex = ITEM_makeItemAndRegist( no );
		if( !ITEM_CHECKINDEX( itemindex)){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
			return FALSE;
		}

		ITEM_setInt( itemindex ,ITEM_LEAKLEVEL, 1 );
		ITEM_setInt( itemindex ,ITEM_TIMELIMIT, 1 );
		ITEM_setInt( itemindex ,ITEM_ENDTIME, NowTime.tv_sec + 60 * expmin );

		leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
		if( leak_level == 1 ){
			item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
		} else {
			item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
		}
		if( mes_id == -1 ){
			translate_format(moji, 0, LANG_MSG_EVENT_002, item_name );
		} else {
			sprintf( moji, MSG_getMessage( mes_id ), item_name );
		}
		SYSTEMMSG( talker, moji );
		if( leak_level == 1 && ITEM_getInt( itemindex ,ITEM_MAXREMAIN ) >= 2 ){
			remain_max = ITEM_getInt( itemindex ,ITEM_MAXREMAIN );
			give_max = give_cnt;
			if( give_max > remain_max ){
				give_max = remain_max;
			}
			ITEM_setInt( itemindex ,ITEM_REMAIN, give_max );
			give_cnt -= give_max;
		} else {
			give_cnt--;
		}
		CHAR_setItemIndex( talker, ii, itemindex );
		ITEM_setWorkInt( itemindex, ITEM_WORKOBJINDEX, -1 );
		CHAR_sendItemDataOne( talker, ii );
		LogItem(
			talker->cu.player.CdKey,
			talker->c.Name,
			no,
			"npc_event.c:GiveTimeItem",
			talker->i.MapId,
			talker->i.Floor, talker->i.X, talker->i.Y
		);
	}

	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);

	*buf = buf2;
	return TRUE;
}
#endif

//-------------------------------------------------------------------------
// アイテ?箕粗艰り叫し
// 苞眶¨
// 		no　アイテ?戎规
// 		*talker
// 提り猛¨
// 		侯喇されてからの沸册箕粗∈尸∷
//-------------------------------------------------------------------------
static int get_item_create_time( int no, Char *talker )
{
	int jj;
	int index = -1;
	int time;

	for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
		//アイテ?ＩＩＮＤＥＸ艰り叫し
		index = CHAR_getItemIndex( talker, jj);
		//部か积ってるなら
		if( index != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( index, ITEM_ID)) {
				break;
			}
		}
	}
	//积ってなかったなら
	if( jj == CHAR_MAXITEMHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 40 ]) );
		return 0;
	}
	//侯喇箕粗艰り叫し
	time = ITEM_getInt( index, ITEM_CREATETIME);
	time = ( NowTime.tv_sec - time ) / 60;
	return time;
}


//-------------------------------------------------------------------------
// アイテ??ウント
// 苞眶¨
// 		no　アイテ?戎规
// 		*talker
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		改眶
//-------------------------------------------------------------------------
static int count_item( int no, Char *talker, int leak_flg )
{
	int jj;
	int index;
	int cnt = 0;
	int leak_level;

	for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
		//アイテ?ＩＩＮＤＥＸ艰り叫し
		index = CHAR_getItemIndex( talker, jj);
		//部か积ってるなら
		if( index != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( index, ITEM_ID)) {
				//凑年レ?ル?ット
				leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
				//凑年涟なら
				if( leak_flg == 1 ){
					//凑年稿なら
					if( leak_level == 1 ){
						continue;
					}
				} else
				//凑年稿なら
				if( leak_flg == 2 ){
					//凑年涟なら
					if( leak_level == 0 ){
						continue;
					}
				}
				//改眶裁换
				cnt += ITEM_getInt( index ,ITEM_REMAIN );
			}
		}
	}

	return cnt;
}

#ifdef ITEMCOMMAND
//-------------------------------------------------------------------------
// アイテ??ウント
// 苞眶¨
//              no　アイテ?戎规
//              *talker
//              leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
//              改眶
//-------------------------------------------------------------------------
static int count_item2( int no, Char *talker, int leak_flg )
{
        int jj;
        int index;
        int cnt = 0;
        int leak_level;

        for( jj = CHAR_STARTITEMARRAY; jj < CHAR_MAXITEMHAVE; jj ++ ){
                //アイテ?ＩＩＮＤＥＸ艰り叫し
                index = CHAR_getItemIndex( talker, jj);
                //部か积ってるなら
                if( index != -1){
                        //このアイテ?なら
                        if( no == ITEM_getInt( index, ITEM_ID)) {
                                //凑年レ?ル?ット
                                leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                //凑年涟なら
                                if( leak_flg == 1 ){
                                        //凑年稿なら
                                        if( leak_level == 1 ){
                                                continue;
                                        }
                                } else
                                //凑年稿なら
                                if( leak_flg == 2 ){
                                        //凑年涟なら
                                        if( leak_level == 0 ){
                                                continue;
                                        }
                                }
                                //改眶裁换
                                cnt += ITEM_getInt( index ,ITEM_REMAIN );
                        }
                }
        }

	return cnt;
}

//-------------------------------------------------------------------------
// アイテ??ウント
// 苞眶¨
//              no　アイテ?戎规
//              *talker
//              leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
//              改眶
//-------------------------------------------------------------------------
static int count_item3( int no, int no2, Char *talker, int leak_flg )
{
        int jj;
        int index;
        int cnt = 0;
        int leak_level;
        int iPartyNum;
        int i;
        Char *apPartyTbl[CHAR_PARTYMAX];

        iPartyNum = CHAR_getPartyArray( talker, apPartyTbl );

	if( no2 == 0)
	{
	        for( i = 0; i < iPartyNum; i++ )
    		{
        	        for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
                	        //アイテ?ＩＩＮＤＥＸ艰り叫し
                	        index = CHAR_getItemIndex( apPartyTbl[i], jj);
                	        //部か积ってるなら
                	        if( index != -1){
                	                //このアイテ?なら
                       		        if( no == ITEM_getInt( index, ITEM_ID)) {
                                	        //凑年レ?ル?ット
                                	        leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                	        //凑年涟なら
                                	        if( leak_flg == 1 ){
                                	                //凑年稿なら
                               		                if( leak_level == 1 ){
                                       		                continue;
                                       		        }
                                       		} else
                 	                       //凑年稿なら
                        	                if( leak_flg == 2 ){
                                	                //凑年涟なら
                                        	        if( leak_level == 0 ){
                	                                        continue;
                        	                        }
                                	        }
                 	                       //改眶裁换
                        	                cnt += ITEM_getInt( index ,ITEM_REMAIN );
                        	        }
                        	}
                	}
        	}
	} else {
	// 2004/04/12 饯赖
		if( iPartyNum < no2 )
		{
			print_t(LANG_MSG_AUTOMATIC_216);
			return -1;
		}
	// ここまで

		i = no2 - 1;
                for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
                       //アイテ?ＩＩＮＤＥＸ艰り叫し
                       index = CHAR_getItemIndex( apPartyTbl[i], jj);
                       //部か积ってるなら
                       if( index != -1){
                                 //このアイテ?なら
                                 if( no == ITEM_getInt( index, ITEM_ID)) {
                                 //凑年レ?ル?ット
                                 leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                        //凑年涟なら
                                        if( leak_flg == 1 ){
                                                //凑年稿なら
                                                if( leak_level == 1 ){
                                                        continue;
                                                }
                                        } else
                                        //凑年稿なら
                                        if( leak_flg == 2 ){
                                                //凑年涟なら
                                                if( leak_level == 0 ){
                                                        continue;
                                                }
                                        }
                                        //改眶裁换
                                        cnt += ITEM_getInt( index ,ITEM_REMAIN );
                                }
                        }
                }
	}
	return cnt;
}

//-------------------------------------------------------------------------
// アイテ??ウント
// 苞眶¨
//              no　アイテ?戎规
//              *talker
//              leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
//              改眶
//-------------------------------------------------------------------------
static int count_item4( int no, int no2, Char *talker, int leak_flg )
{
        int jj;
        int index;
        int cnt = 0;
        int leak_level;
        int iPartyNum;
        int i;
        Char *apPartyTbl[CHAR_PARTYMAX];

        iPartyNum = CHAR_getPartyArray( talker, apPartyTbl );

	if( no2 == 0 )
	{
	        for( i = 0; i < iPartyNum; i++ )
       		{
                	for( jj = CHAR_STARTITEMARRAY; jj < CHAR_MAXITEMHAVE; jj ++ ){
               		        //アイテ?ＩＩＮＤＥＸ艰り叫し
                       		index = CHAR_getItemIndex( apPartyTbl[i], jj);
                        	//部か积ってるなら
                        	if( index != -1){
                        	        //このアイテ?なら
                                	if( no == ITEM_getInt( index, ITEM_ID)) {
                                	        //凑年レ?ル?ット
                                	        leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                	        //凑年涟なら
                                	        if( leak_flg == 1 ){
                                	                //凑年稿なら
                                	                if( leak_level == 1 ){
                                	                        continue;
                                        	        }
                                        	} else
                                        	//凑年稿なら
                                        	if( leak_flg == 2 ){
                                        	        //凑年涟なら
                                        	        if( leak_level == 0 ){
                                        	                continue;
                                       		        }
                                        	}
                                        	//改眶裁换
                                        	cnt += ITEM_getInt( index ,ITEM_REMAIN );
                  	        	}
                  		}
			}
		}
	} else {
	// 2004/04/12 饯赖
		if( iPartyNum < no2 )
		{
			print_t(LANG_MSG_AUTOMATIC_217);
			return -1;
		}
	// ここまで
		
		i = no2 - 1;
                for( jj = CHAR_STARTITEMARRAY; jj < CHAR_MAXITEMHAVE; jj ++ ){
                       //アイテ?ＩＩＮＤＥＸ艰り叫し
                       index = CHAR_getItemIndex( apPartyTbl[i], jj);
                       //部か积ってるなら
                       if( index != -1){
                                 //このアイテ?なら
                                 if( no == ITEM_getInt( index, ITEM_ID)) {
                                 //凑年レ?ル?ット
                                 leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                        //凑年涟なら
                                        if( leak_flg == 1 ){
                                                //凑年稿なら
                                                if( leak_level == 1 ){
                                                        continue;
                                                }
                                        } else
                                        //凑年稿なら
                                        if( leak_flg == 2 ){
                                                //凑年涟なら
                                                if( leak_level == 0 ){
                                                        continue;
                                                }
                                        }
                                        //改眶裁换
                                        cnt += ITEM_getInt( index ,ITEM_REMAIN );
                                }
                        }
                }
	}

	return cnt;
}

//-------------------------------------------------------------------------
// アイテ??ウント
// 苞眶¨
//              no　アイテ?戎规
//              *talker
//              leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
//              改眶
//-------------------------------------------------------------------------
static int count_item5( int no, int no2, Char *talker, int leak_flg )
{
        int jj;
        int index;
        int cnt = 0;
        int leak_level;
        int iPartyNum;
        int i;
        Char *apPartyTbl[CHAR_PARTYMAX];

        iPartyNum = CHAR_getPartyArray( talker, apPartyTbl );

        if( no2 == 0 )
        {
                for( i = 0; i < iPartyNum; i++ )
                {
                        for( jj = 0; jj < CHAR_STARTITEMARRAY; jj ++ ){
                                //アイテ?ＩＩＮＤＥＸ艰り叫し
                                index = CHAR_getItemIndex( apPartyTbl[i], jj);
                                //部か积ってるなら
                                if( index != -1){
                                        //このアイテ?なら
                                        if( no == ITEM_getInt( index, ITEM_ID)) {
                                                //凑年レ?ル?ット
                                                leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                                //凑年涟なら
                                                if( leak_flg == 1 ){
                                                        //凑年稿なら
                                                        if( leak_level == 1 ){
                                                                continue;
                                                        }
                                                } else
                                                //凑年稿なら
                                                if( leak_flg == 2 ){
                                                        //凑年涟なら
                                                        if( leak_level == 0 ){
                                                                continue;
                                                        }
                                                }
                                                //改眶裁换
                                                cnt += ITEM_getInt( index ,ITEM_REMAIN );
                                        }
                                }
                        }
                }
        } else {
	// 2004/04/12 饯赖
		if( iPartyNum < no2 )
		{
			print_t(LANG_MSG_AUTOMATIC_218);
			return -1;
		}
	// ここまで

                i = no2 - 1;
                for( jj = 0; jj < CHAR_STARTITEMARRAY; jj ++ ){
                       //アイテ?ＩＩＮＤＥＸ艰り叫し
                       index = CHAR_getItemIndex( apPartyTbl[i], jj);
                       //部か积ってるなら
                       if( index != -1){
                                 //このアイテ?なら
                                 if( no == ITEM_getInt( index, ITEM_ID)) {
                                 //凑年レ?ル?ット
                                 leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
                                        //凑年涟なら
                                        if( leak_flg == 1 ){
                                                //凑年稿なら
                                                if( leak_level == 1 ){
                                                        continue;
                                                }
                                        } else
                                        //凑年稿なら
                                        if( leak_flg == 2 ){
                                                //凑年涟なら
                                                if( leak_level == 0 ){
                                                        continue;
                                                }
                                        }
                                        //改眶裁换
                                        cnt += ITEM_getInt( index ,ITEM_REMAIN );
                                }
                        }
                }
        }

        return cnt;
}
#endif

//-------------------------------------------------------------------------
// アイテ?积ってるかチェッ?
// 苞眶¨
// 		no　アイテ?戎规
// 		*talker
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL check_have_item( int no, Char *talker, int leak_flg )
{
	int jj;
	int index;
	int leak_level;

	for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
		//アイテ?ＩＩＮＤＥＸ艰り叫し
		index = CHAR_getItemIndex( talker, jj);
		//部か积ってるなら
		if( index != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( index, ITEM_ID)) {
				//凑年レ?ル?ット
				leak_level = ITEM_getInt( index ,ITEM_LEAKLEVEL );
				//凑年涟なら
				if( leak_flg == 1 ){
					//凑年稿なら
					if( leak_level == 1 ){
						continue;
					}
				} else
				//凑年稿なら
				if( leak_flg == 2 ){
					//凑年涟なら
					if( leak_level == 0 ){
						continue;
					}
				}
				//积ってる
				return TRUE;
			}
		}
	}
	return FALSE;
}


//-------------------------------------------------------------------------
// アイテ?刘洒腕に积ってるかチェッ?
// 苞眶¨
// 		no　アイテ?戎规
// 		*talker
// 		leak_flg  ０¨凑年痰浑　１¨凑年涟　２¨凑年稿
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL check_equip_item( int no, Char *talker )
{
	int jj;
	int index;

	for( jj = 0; jj < CHAR_EQUIPPLACENUM; jj ++ ){
		//アイテ?ＩＩＮＤＥＸ艰り叫し
		index = CHAR_getItemIndex( talker, jj);
		//部か积ってるなら
		if( index != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( index, ITEM_ID)) {
				//刘洒してる
				return TRUE;
			}
		}
	}
	return FALSE;
}


//-------------------------------------------------------------------------
// ス?ッ?アイテ??ウント
// 苞眶¨
// 		no　アイテ?戎规
// 		*talker
// 		max_remain
// 提り猛¨
// 		改眶
//-------------------------------------------------------------------------
static int count_stack_item( int no, Char *talker, int max_remain )
{
	int jj;
	int index;
	int cnt = 0;

	for( jj = CHAR_STARTITEMARRAY; jj < CHAR_MAXITEMHAVE; jj ++ ){
		//アイテ?ＩＮＤＥＸ艰り叫し
		index = CHAR_getItemIndex( talker, jj);
		//部か积ってるなら
		if( index != -1){
			//このアイテ?なら
			if( no == ITEM_getInt( index, ITEM_ID)) {
				//凑年涟なら
				if( ITEM_getInt( index ,ITEM_LEAKLEVEL ) == 0 ){
					continue;
				}
				//改眶裁换
				cnt += max_remain - ITEM_getInt( index ,ITEM_REMAIN );
			}
		} else {
			//改眶裁换
			cnt += max_remain;
		}
	}
	return cnt;
}


//-------------------------------------------------------------------------
// 胎蜗恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_charm( Char *talker, char **buf )
{
	char *buf2;
	int charm;
	int add_flg = 0;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 37 ]) );
		return FALSE;
	}
	//プラスか?イナスなら
	if( *buf2 == '+' || *buf2 == '-' ){
		//陵滦回年
		add_flg = 1;
	}
	//胎蜗艰り叫し
	charm = get_num2( &buf2 );
	//陵滦回年なら
	if( add_flg ){
		//胎蜗恃构
		talker->iu.player.Charm += charm;
	} else {
		talker->iu.player.Charm = charm;
	}
	//胎蜗０踏塔なら
	if( talker->iu.player.Charm < 0 ){
		//胎蜗０にする
		talker->iu.player.Charm = 0;
	} else
	//胎蜗１００亩えてるなら
	if( talker->iu.player.Charm > 100 ){
		//胎蜗１００にする
		talker->iu.player.Charm = 100;
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker , CHAR_CP_CHARM );
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 丸狄眶恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_visitor( Char *my, char **buf )
{
	char *buf2;
	int visitor;
	int add_flg = 0;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 42 ]) );
		return FALSE;
	}
	//プラスか?イナスなら
	if( *buf2 == '+' || *buf2 == '-' ){
		//陵滦回年
		add_flg = 1;
	}
	//丸狄眶艰り叫し
	visitor = get_num2( &buf2 );
	//陵滦回年なら
	if( add_flg ){
		//丸狄眶恃构
		my->i.ForcePoint += visitor;
	} else {
		my->i.ForcePoint = visitor;
	}
	//丸狄眶０踏塔なら
	if( my->i.ForcePoint < 0 ){
		//丸狄眶０にする
		my->i.ForcePoint = 0;
	} else
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 卵底刨恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_dur( Char *talker, char **buf )
{
	char *buf2;
	int dur;
	int add_flg = 0;
	int itemid,itemindex = -1;
	int equipplace;
	int maxdurability;
	int durability;
	char moji[256];
	int leak_level;
	char *item_name;

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 39 ]) );
		return FALSE;
	}
	//アイテ?戎规艰り叫し
	itemid = get_num2( &buf2 );
	//肌が','でなければ
	if( *buf2 != ',' ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 39 ]) );
		return FALSE;
	}
	//１バイトス?ップ
	buf2++;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 39 ]) );
		return FALSE;
	}
	//プラスか?イナスなら
	if( *buf2 == '+' || *buf2 == '-' ){
		//陵滦回年
		add_flg = 1;
	}
	//纳裁卵底刨艰り叫し
	dur = get_num2( &buf2 );
	//积ってるか拇べる
	for( equipplace = 0; equipplace < CHAR_MAXITEMHAVE; equipplace++ ){
		//アイテ?のインデッ?ス艰り叫し
		itemindex = CHAR_getItemIndex( talker, equipplace);
		//部か积ってるなら
		if( itemindex != -1){
			//このアイテ?なら
			if( itemid == ITEM_getInt( itemindex, ITEM_ID)) {
				break;
			}
		}
	}
	//积って痰かったなら
	if( equipplace == CHAR_MAXITEMHAVE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 39 ]) );
		return FALSE;
	}
	//附哼の卵底刨艰り叫し
	durability = ITEM_getInt( itemindex ,ITEM_DURABILITY );
	//ＭＡＸ卵底刨艰り叫し
	maxdurability = ITEM_getInt( itemindex ,ITEM_MAXDURABILITY );
	//そのまま?ットなら
	if( add_flg == 0 ){
		//木儡卵底刨?ット
		durability = dur;
	} else {
		//卵底刨裁换
		durability += dur;
	}
	//凑年レ?ル?ット
	leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
	//凑年稿なら
	if( leak_level == 1 ){
		//アイテ?叹?ット
		item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
	} else {
		//アイテ?叹?ット
		item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
	}
	//肝俱なら
	if( durability <= 0 ){
		//アイテ?が蝉れた
		translate_format(moji, 0, LANG_MSG_EVENT_007, item_name );
		SYSTEMMSG( talker, moji );
		/* 咐晤で猴近 */
		LogItem(
			talker->cu.player.CdKey,
			talker->c.Name,
       		ITEM_getInt( itemindex, ITEM_ID ),  /* アイテ?戎规 */
			"npc_event.c:DelItem",
			talker->i.MapId,
			talker->i.Floor, talker->i.X, talker->i.Y
		);
		// "%sを久した。"
//		snprintf( buf, sizeof( buf),TMSG(LANG_MSG_CHATMAGIC_C_011),  ITEM_getAppropriateName(itemindex));
//		CHAR_talkToCli( talker, NULL, buf, CHAR_COLORWHITE);
		CHAR_unsetItem( talker, equipplace );
 	   			CHAR_sendItemDataOne( talker, equipplace);
		//CHAR_sendStatusString(ch, "I"); /* アイテ?构糠 */
		/* 坤肠から久殿 */
		ITEM_endExistItemsOne(itemindex);
		//疚规チェッ?∈アイテ?∷
		if( TITLE_TitleCheck( talker, TITLE_CHECK_EQUIPEVENT)){
			CHAR_sendTitle( talker);
		}
		return TRUE;
	}
	//呵络亩えてるなら
	if( durability > maxdurability ){
		durability = maxdurability;
	}
	//卵底刨恃构
	ITEM_setInt( itemindex, ITEM_DURABILITY, durability );
	//?ライアントに流慨
	CHAR_sendItemDataOne( talker, equipplace );
	//陵滦回年で?イナスなら
	if( add_flg && dur < 0 ){
		dur *= -1;
		//稿１搀で蝉れるなら
		if( durability <= dur ){
			//アイテ?が蝉れそうだ
			translate_format(moji, 0, LANG_MSG_EVENT_006, item_name );
			SYSTEMMSG( talker, moji );
		}
	}
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// パレット恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL send_palette( Char *talker, char **buf )
{
	char *buf2;
	int pal_no;
	int frame = 1;
	int fd;

	//アドレスコピ〖
	buf2 = *buf;

	//ト〖?〖のＦＤ艰り叫し
	fd = getfdFromChar( talker);
	//ＦＤが痰跟なら
	if( fd <= -1 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 43 ]) );
		return FALSE;
	}

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 43 ]) );
		return FALSE;
	}
	//パレット戎规艰り叫し
	pal_no = get_num2( &buf2 );

	//フレ〖?眶回年なら
	if( *buf2 == ',' ){
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 43 ]) );
			return FALSE;
		}
		//フレ〖?眶艰り叫し
		frame = get_num2( &buf2 );
	}

	//パレット流慨
	nrproto_PC_send( fd, pal_no, frame );
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 挑フ〖ド猴近
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL kill_drugitem( Char *talker, char **buf )
{
	int itemindex;
	char *buf2;
	int	equipplace;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;
	int item_type;
	int leak_level;

	//アドレスコピ〖
	buf2 = *buf;
	//メッ?〖ジ回年回年なら
	if( *buf2 == '(' ){
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 44 ]) );
			return FALSE;
		}
		//メッ?〖ジ戎规艰り叫し
		mes_id = get_num2( &buf2 );
		//肌がメッ?〖ジ回年姜位でなければ
		if( *buf2 != ')' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 44 ]) );
			return FALSE;
		}
		buf2++;
	}

	//积ってるか拇べる
	for( equipplace = CHAR_STARTITEMARRAY; equipplace < CHAR_MAXITEMHAVE; equipplace++ ){
		//アイテ?のインデッ?ス艰り叫し
		itemindex = CHAR_getItemIndex( talker, equipplace);
		//部か积ってるなら
		if( itemindex != -1){
			//?イプ艰り叫し
			item_type = ITEM_getInt( itemindex, ITEM_TYPE );
			//挑かフ〖ドなら
			if( item_type == ITEM_DRUG || item_type == ITEM_DISH ){
				//凑年レ?ル?ット
				leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
				//凑年稿なら
				if( leak_level == 1 ){
					//アイテ?叹?ット
					item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
				} else {
					//アイテ?叹?ット
					item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
				}
				//盖年メッ?〖ジなら
				if( mes_id == -1 ){
					//システ?メッ?〖ジ山绩∈アイテ?缄畔した∷
					translate_format(moji, 0, LANG_MSG_EVENT_003, ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
				} else {
					//システ?メッ?〖ジ山绩
					sprintf( moji, MSG_getMessage( mes_id ), ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
				}
				SYSTEMMSG( talker, moji );
				/* 咐晤で猴近 */
				LogItem(
					talker->cu.player.CdKey,
					talker->c.Name,
		       		ITEM_getInt( itemindex, ITEM_ID ),  /* アイテ?戎规 */
					"npc_event.c:kill_drugitem",
					talker->i.MapId,
					talker->i.Floor, talker->i.X, talker->i.Y
				);
				// "%sを久した。"
//				snprintf( buf, sizeof( buf),TMSG(LANG_MSG_CHATMAGIC_C_011),  ITEM_getAppropriateName(itemindex));
//				CHAR_talkToCli( talker, NULL, buf, CHAR_COLORWHITE);
				CHAR_unsetItem( talker, equipplace );
	   			CHAR_sendItemDataOne( talker, equipplace);
				//CHAR_sendStatusString(ch, "I"); /* アイテ?构糠 */
				/* 坤肠から久殿 */
				ITEM_endExistItemsOne(itemindex);
			}
		}
	}
	//疚规チェッ?∈アイテ?∷
	if( TITLE_TitleCheck( talker, TITLE_CHECK_EQUIPEVENT)){
		CHAR_sendTitle( talker);
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_send_CP2_String( talker, CHAR_CP2_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 积っているアイテ?猴近
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL kill_item_all( Char *talker, char **buf )
{
	int itemindex;
	char *buf2;
	int	equipplace;
	char moji[ 256 ];
	char *item_name;
	int mes_id = -1;
	int leak_level;
	int item_id;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 53 ]) );
		return FALSE;
	}
	//アイテ?戎规艰りだし
	item_id = get_num2( &buf2 );

	//メッ?〖ジ回年なら
	if( *buf2 == ',' ){
		buf2++;
		//メッ?〖ジ回年回年なら
		if( *buf2 == '(' ){
			buf2++;
			//肌の矢机が眶猛でなければ
			if( check_num( buf2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 53 ]) );
				return FALSE;
			}
			//メッ?〖ジ戎规艰り叫し
			mes_id = get_num2( &buf2 );
			//肌がメッ?〖ジ回年姜位でなければ
			if( *buf2 != ')' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 53 ]) );
				return FALSE;
			}
			buf2++;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 53 ]) );
			return FALSE;
		}
	}

	//积ってるか拇べる
	for( equipplace = CHAR_STARTITEMARRAY; equipplace < CHAR_MAXITEMHAVE; equipplace++ ){
		//アイテ?のインデッ?ス艰り叫し
		itemindex = CHAR_getItemIndex( talker, equipplace);
		//部か积ってるなら
		if( itemindex != -1){
			//これなら
			if( item_id == ITEM_getInt( itemindex, ITEM_ID ) ){
				//凑年レ?ル?ット
				leak_level = ITEM_getInt( itemindex ,ITEM_LEAKLEVEL );
				//凑年稿なら
				if( leak_level == 1 ){
					//アイテ?叹?ット
					item_name = ITEM_getChar( itemindex ,ITEM_TRUENAME );
				} else {
					//アイテ?叹?ット
					item_name = ITEM_getChar( itemindex ,ITEM_FIRSTNAME );
				}
				//盖年メッ?〖ジなら
				if( mes_id == -1 ){
					//システ?メッ?〖ジ山绩∈アイテ?缄畔した∷
					translate_format(moji, 0, LANG_MSG_EVENT_003, ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
				} else {
					//システ?メッ?〖ジ山绩
					sprintf( moji, MSG_getMessage( mes_id ), ITEM_getChar( itemindex ,ITEM_TRUENAME ) );
				}
				SYSTEMMSG( talker, moji );
				/* 咐晤で猴近 */
				LogItem(
					talker->cu.player.CdKey,
					talker->c.Name,
		       		ITEM_getInt( itemindex, ITEM_ID ),  /* アイテ?戎规 */
					"npc_event.c:kill_drugitem",
					talker->i.MapId,
					talker->i.Floor, talker->i.X, talker->i.Y
				);
				// "%sを久した。"
//				snprintf( buf, sizeof( buf),TMSG(LANG_MSG_CHATMAGIC_C_011),  ITEM_getAppropriateName(itemindex));
//				CHAR_talkToCli( talker, NULL, buf, CHAR_COLORWHITE);
				CHAR_unsetItem( talker, equipplace );
	   			CHAR_sendItemDataOne( talker, equipplace);
				//CHAR_sendStatusString(ch, "I"); /* アイテ?构糠 */
				/* 坤肠から久殿 */
				ITEM_endExistItemsOne(itemindex);
			}
		}
	}
	//疚规チェッ?∈アイテ?∷
	if( TITLE_TitleCheck( talker, TITLE_CHECK_EQUIPEVENT)){
		CHAR_sendTitle( talker);
	}
	//?ライアントに流慨
	CHAR_complianceParameter( talker );
	CHAR_send_CP_String( talker, CHAR_CP_ALL);
	CHAR_send_CP2_String( talker, CHAR_CP2_ALL);
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex);
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 宝收BLOCK_COMMAND艰り叫し
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL get_ch_com( Char *ch, char **buf, int *num )
{
	char *buf2;
	int ii;

	//コ?ンド??ロ
	BLOCK_COMMAND

	//アドレスコピ〖
	buf2 = *buf;

	for( ii = 0; ii < arraysizeof( block_com ); ii++ ){
		//改侍コ?ンド磊り仑えなら
		if( block_com[ ii ][ 0 ] == 0 ){
			//己窃
			return FALSE;
		}
		//コ?ンド券斧なら
		if( strncmp2( buf2, block_com[ ii ], strlen( block_com[ ii ] ) ) == 0 ){
			break;
		}
	}
	//コ?ンド尸ス?ップ
	buf2 += strlen( block_com[ ii ] );
	//ＣＨデ〖?艰り叫し
	*num = *block_com_ch[ ii ];
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}

#ifdef GLOBALFLAG_GA
static BOOL setFlagExGA( Char* talker, char** buf )
{
	int no,flg;
	char* buf2;

	// アドレスコピ〖
	buf2 = *buf;
	// 肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE )
	{
		// エラ〖
		print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
		return FALSE;
	}
	// フラグ戎规艰り叫し
	no = get_num2( &buf2 );
	// ０×ＭＡＸ笆嘲なら
	if( no < 0 || no >= MAX_GLOBAL_FLAG )
	{
		// エラ〖
		print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
		return FALSE;
	}
	// ♂でなければ
	if( *buf2 != '=' )
	{
		// エラ〖
		print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
		return FALSE;
	}
	// ♂ス?ップ
	buf2++;
	// 肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE )
	{
		// エラ〖
		print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
		return FALSE;
	}
	// フラグ艰り叫し
	flg = get_num2( &buf2 );
	// フラグオフなら
	if( flg == 0 )
	{
		NPC_clearGlobalFlagGA( talker, no );
	} else
	// フラグオンなら
	if( flg == 1 )
	{
		NPC_setGlobalFlagGA( talker, no );
	} else {
		// エラ〖
		print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
		return FALSE;
	}

	// アドレス构糠
	*buf = buf2;
	return TRUE;
}
#endif	/* GLOBALFLAG_GA */

#ifdef DISPWINDOW_GA
//-------------------------------
// ウインドウ山绩(パ〖ティ〖脱)
//-------------------------------
static BOOL disp_windowGA(Char* talker, Char* my, char** buf, int end_flgga )
{
	int fd[CHAR_PARTYMAX];
	int tk_fd;
	int mes_idga;
	char* buf2;
	char sendbuf[1024];
	char sendbuf2[1024];
	int buttontypega = 0;
	int centering_flgga;
	int ii, j;
	char cga;
	Char* pParty[CHAR_PARTYMAX];
	int iPartyNum;

	iPartyNum = CHAR_getPartyArray( talker, pParty );

	// ＦＤの艰り叫し
	for( j = 0; j < iPartyNum; j++ )
	{
		fd[j] = getfdFromChar( pParty[j] );
	}
	tk_fd = getfdFromChar( talker );
	// ＦＤが铜跟なら
	if( tk_fd <= -1 ) return FALSE;


	// アドレスコピ〖
	buf2 = *buf;
	// 肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE )
	{
		// 矢机误木儡回年で痰ければ
		if( *buf2 != '"' )
		{
			// エラ〖
			print("\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 86 ]) );
			return FALSE;
		}
		//'"'尸ス?ップ
		buf2++;
		// メッ?〖ジ艰り叫し
		ii = 0;
		while( 1 )
		{
			cga = *buf2;
			// 姜位なら
			if( cga == '"' || cga == 0x0a || cga == 0 )
			{
				sendbuf[ii] = 0;
				// 姜位コ〖ド笆嘲なら
				if( cga ) buf2++; //１バイトス?ップ
				break;
			}
			sendbuf[ ii++ ] = cga;
			buf2++;
		}
	} else {
		// メッ?〖ジＩＤ艰り叫し
		mes_idga = get_num2( &buf2 );
		// メッ?〖ジ?ット
		snprintf( sendbuf, sizeof( sendbuf ), "%s", MSG_getMessage( mes_idga ) );
	}
	// ウインドウパラメ〖?艰り叫し
	while( 1 )
	{
		// 肌が','でなければ
		if( *buf2 != ',' ) break;

		// １バイトス?ップ
		buf2++;
		// 肌が"cancel"なら
		if( strncmp2( buf2, "cancel", 6 ) == 0 )
		{
			// バッファを渴める
			buf2 += 6;
			buttontypega |= WINDOW_BUTTONTYPE_CANCEL;
			continue;
		} 
		// 肌が"ok"なら
		if( strncmp2( buf2, "ok", 2 ) == 0 )
		{
			// バッファを渴める
			buf2 += 2;
			buttontypega |= WINDOW_BUTTONTYPE_OK;
			continue;
		} 
		// 肌が"yes"なら
		if( strncmp2( buf2, "yes", 3 ) == 0 )
		{
			// バッファを渴める
			buf2 += 3;
			buttontypega |= WINDOW_BUTTONTYPE_YES;
			continue;
		} 
		// 肌が"no"なら
		if( strncmp2( buf2, "no", 2 ) == 0 )
		{
			// バッファを渴める
			buf2 += 2;
			buttontypega |= WINDOW_BUTTONTYPE_NO;
			continue;
		} 
		// 肌が"prev"なら
		if( strncmp2( buf2, "prev", 4 ) == 0 )
		{
			// バッファを渴める
			buf2 += 4;
			buttontypega |= WINDOW_BUTTONTYPE_PREV;
			continue;
		} 
		// 肌が"next"なら
		if( strncmp2( buf2, "next", 4 ) == 0 )
		{
			// バッファを渴める
			buf2 += 4;
			buttontypega |= WINDOW_BUTTONTYPE_NEXT;
			continue;
		} 
		// エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 86 ]) );
		return FALSE;
	}

	//ＥｎｄＷｉｎｄｏｗなら
	if( end_flgga )
	{
		// ウインドウ山绩借妄姜位
		talker->wu.player.WindowBuffer = -1;
		// ＯＫ??ン盖年
		buttontypega = WINDOW_BUTTONTYPE_OK;
	} else {
		// 附哼の悸乖借妄乖眶瘦赂
		talker->wu.player.WindowBuffer = get_line_cnt;
	}
	// ??ン攫鼠瘦赂
	talker->wu.player.WindowBuffer3 = buttontypega;
	// 叹涟赁掐へ
	centering_flgga = Event_insert_name( talker, sendbuf, sendbuf2 );
	// ?ン?リングなら
	if( centering_flgga )
	{
		// 矢机?ン?リング
		Event_centering( sendbuf2 );
	}

	// ト〖?〖笆嘲のウインドウ?ロ〖?借妄フラグ
	if( buttontypega > 0 ) talker->wu.player.script_cmd_flg[GAWINDOW] |= GAWINDOW_FLAG_ON;

	// ウインドウ山绩
	// talkerなら
	nrproto_WN_send( tk_fd, WINDOW_MESSAGETYPE_MESSAGE,
					 buttontypega,
					 CHAR_WINDOWTYPE_EVENT_NPC,
					 my->w.ObjIndex,
					 sendbuf2 );

	// その戮のパ〖ティ〖なら
	for( j = 0; j < iPartyNum; j++ )
	{
		// talkerなら借妄をしない
		if( fd[j] == tk_fd ) continue;
		nrproto_WN_send( fd[j], WINDOW_MESSAGETYPE_MESSAGE,
						 WINDOW_BUTTONTYPE_NONE,
						 CHAR_WINDOWTYPE_EVENT_NPC,
						 my->w.ObjIndex,
						 sendbuf2 );
	}


	// アドレス恃构
	*buf = buf2;
	return TRUE;
}
#endif	/* DISPWINDOW_GA */

#ifdef ADD_ITEM_COMMAND
//------------------------------------------------------------//
// パ〖ティ〖链镑が回年したアイテ?を积っているかチェッ?する //
//------------------------------------------------------------//
BOOL check_haveitem_partyall( Char* ch, int no )
{
	Char*	pParty[CHAR_PARTYMAX];
	int		iPartyNum;
	int		i;
	BOOL	isHave;

	iPartyNum = CHAR_getPartyArray( ch, pParty );
	isHave = TRUE;
	
	// パ〖ティ〖链镑のアイテ?を浮瑚
	for( i=0; i<iPartyNum; ++i)
	{
		if( check_have_item( no, pParty[i], 0 ) == FALSE )
		{
			// 回年したアイテ?を积っていなかった
			isHave = FALSE;
			break;
		}
	}
	return isHave;
}
#endif	/* ADD_ITEM_COMMAND */

#ifdef ADD_GLOBALFLAG_COMMAND
//------------------------------------------//
// パ〖ティ〖柒のグロ〖バルフラグのチェッ? //
// ∈チェッ?认跋はパ〖ティ〖链てと改客∷   //
//------------------------------------------//
int check_globalflg( Char* ch, int flgid, int member )
{
	Char* 	pParty[CHAR_PARTYMAX];
	int		iPartyNum;
	int		i;
	BOOL	isGlobalFlg;
	
	iPartyNum = CHAR_getPartyArray( ch, pParty );
	isGlobalFlg = FALSE;

	// memberの眶猛をチェッ?
	if( iPartyNum < member )
	{
		// パ〖ティ〖の客眶を亩えていたらエラ〖
		print_t(LANG_MSG_AUTOMATIC_219);
		return -1;
	}

	// パ〖ティ〖链镑浮瑚の眷圭
	if( member == 0 )
	{
		for( i=0; i<iPartyNum; ++i )
		{
			// 回年したグロ〖バルフラグＩＤのステ〖?スを艰评
			isGlobalFlg = NPC_getGlobalFlag( pParty[i], flgid );
			// フラグが惟っていたら
			if( isGlobalFlg != 0 )
			{
				 return 1;
			}
		}
	}
	// 改客浮瑚の眷圭
	else
	{
		// 回年したグロ〖バルフラグＩＤのステ〖?スを艰评
		// メンバ〖の回年が１からなので-1する
		isGlobalFlg = NPC_getGlobalFlag( pParty[member-1], flgid );
		// フラグが惟っていたら
		if( isGlobalFlg != 0 )
		{
			return 1;
		}
	}
	return 0;
}
//--------------------------------------------//
// パ〖ティ〖链镑のグロ〖バルフラグのチェッ? //
//--------------------------------------------//
int check_globalflg_partyall( Char* ch, int flgid )
{
	Char*	pParty[CHAR_PARTYMAX];
	int		iPartyNum;
	int		i;
	BOOL	isGlobalFlg;

	iPartyNum = CHAR_getPartyArray( ch, pParty );
	isGlobalFlg = TRUE;

	// パ〖ティ〖链镑浮瑚
	for( i=0; i<iPartyNum; ++i )
	{
		// 回年したグロ〖バルフラグＩＤのステ〖?スを艰评
		isGlobalFlg = NPC_getGlobalFlag( pParty[i], flgid );
		// フラグが惟っていなかったら
		if( isGlobalFlg == 0 )
		{
			return 0;
		}
	}
	return 1;
}	
#endif	/* ADD_GLOBALFLAG_COMMAND */ 

//-------------------------------------------------------------------------
// 改侍デ〖?艰り叫し
// 苞眶¨
// 		int  芹误戎规
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 		*msg
// 提り猛¨
// 		なし
//-------------------------------------------------------------------------
static void set_check_data( int ii, Char *my, Char *ch, char **buf, char *msg )
{
	char *buf2;
	int no,no2;
	int jj;
	Char *ch2;
	NRTIME		nownrtime;
	int index = -1;

	//チェッ?搀眶１搀?ット
	check_cnt = 1;
	check_data[ 0 ] = 0;
	//アドレスコピ〖
	buf2 = *buf;
	switch( ii ){
	//nowevent
	case 1:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			break;
		}
		//フラグ戎规艰り叫し
		no = get_num2( &buf2 );
		//０×ＭＡＸ笆嘲なら
		if( no < 0 || no > MAX_EVENT_FLG ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			break;
		}
		//NowEvent がＯＮなら
		if( NPC_NowEventCheckFlg( ch, no ) ){
			check_data[ 0 ] = 1;
		}
		break;
	//endevent
	case 2:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			break;
		}
		//フラグ戎规艰り叫し
		no = get_num2( &buf2 );
		//０×ＭＡＸ笆嘲なら
		if( no < 0 || no > MAX_EVENT_FLG ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			break;
		}
		//EndEvent がＯＮなら
		if( NPC_EndEventCheckFlg( ch, no ) ){
			check_data[ 0 ] = 1;
		}
		break;
	//haveitem
	case 3:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '='){
			//肌の矢机が眶猛でなければ
			if( check_num( buf2+2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 4 ]) );
				break;
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 4 ]) );
			break;
		}
		//アイテ?戎规艰り叫し
		no = get_num( buf2+2);
		//アイテ?积ってるかチェッ?
		if( check_have_item( no, ch, 0 ) == TRUE ){
			//积ってる
			check_data[ 0 ] = no;
		} else {
			//积ってない、チェッ?搀眶?リア
			check_cnt = 0;
		}
		break;
	//itemspace
	case 4:
		//积ってるアイテ?の改眶拇べる
    {
        int MAX = min(ch->i.ItemLimit, CHAR_MAXITEMHAVE);
        for (jj = CHAR_STARTITEMARRAY; jj < MAX; jj++) {
            //アイテ?ＩＩＮＤＥＸ艰り叫し
            index = CHAR_getItemIndex(ch, jj);
            //部か积ってるなら
            if (index != -1) {
                check_data[0]++;
            }
        }
        //鄂き眶?ット
        check_data[0] = MAX - CHAR_STARTITEMARRAY - check_data[0];
        break;
    }
	//itemcreatetime
	case 5:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 40 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ?箕粗艰り叫し
		check_data[ 0 ] = get_item_create_time( no, ch );
		break;
	//itemdurper
	case 6:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 47 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ?玫す
		for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
			//アイテ?ＩＩＮＤＥＸ艰り叫し
			index = CHAR_getItemIndex( ch, jj);
			//部か积ってるなら
			if( index != -1){
				//このアイテ?なら
				if( no == ITEM_getInt( index, ITEM_ID)) {
					break;
				}
			}
		}
		//アイテ?积ってなかったら
		if( jj == CHAR_MAXITEMHAVE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 47 ]) );
			break;
		}
		//附哼の卵底刨艰り叫し
		check_data[ 0 ] = ITEM_getInt( index ,ITEM_DURABILITY );
		//呵络卵底刨艰り叫し
		jj = ITEM_getInt( index ,ITEM_MAXDURABILITY );
		//充圭?ット
		check_data[ 0 ] = ( check_data[ 0 ] * 100 ) / jj;
		break;
	//itemdur
	case 7:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 45 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ?玫す
		for( jj = 0; jj < CHAR_MAXITEMHAVE; jj ++ ){
			//アイテ?ＩＩＮＤＥＸ艰り叫し
			index = CHAR_getItemIndex( ch, jj);
			//部か积ってるなら
			if( index != -1){
				//このアイテ?なら
				if( no == ITEM_getInt( index, ITEM_ID)) {
					break;
				}
			}
		}
		//アイテ?积ってなかったら
		if( jj == CHAR_MAXITEMHAVE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 45 ]) );
			break;
		}
		//附哼の卵底刨艰り叫し
		check_data[ 0 ] = ITEM_getInt( index ,ITEM_DURABILITY );
		break;
	//item
case 8:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 6 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ??ウント
		check_data[ 0 ] = count_item( no, ch, 0 );
		break;
	//group
	case 9:
#if 0
		//极尸の尸裁换
		check_data[ 0 ]++;
		//パ〖ティ〖链镑
		for( ii=0; ii<CHAR_PARTYMAX; ii++ ){
			//灰の?イン?〖艰り叫し
			Char *ch2 = ch->wu.player.PartyChar[ ii ];
			// ?イン?〖が铜跟なら
			if( CHAR_CheckCharPointer( ch2) == TRUE ){
				//极尸でなければ
				if( ch != ch2 ){
					//客眶裁换
					check_data[ 0 ]++;
				}
			}
		}
#else
		//极尸の尸裁换
		check_data[ 0 ]++;
		//パ〖ティ〖なら
		if( ch->wu.player.PartyMode != CHAR_PARTY_NONE ){
			//パ〖ティ〖のＬなら
			if( ch->wu.player.PartyMode == CHAR_PARTY_LEADER ){
				ch2 = ch;
			} else {
				ch2 = ch->wu.player.PartyChar[ 0 ];
			}
			//パ〖ティ〖链镑
			for( jj=0; jj<CHAR_PARTYMAX; jj++ ){
				//灰の?イン?〖艰り叫し
				Char *ch3 = ch2->wu.player.PartyChar[ jj ];
				// ?イン?〖が铜跟なら
				if( CHAR_CheckCharPointer( ch3) == TRUE ){
					//极尸でなければ
					if( ch != ch3 ){
						//客眶裁换
						check_data[ 0 ]++;
					}
				}
			}
		}
#endif
		break;
	//rank
	case 10:
		//ラン?艰り叫し
		jj = JOBS_ANCESTRY_getIndexFromJobsId( ch->iu.player.Job, &no );
		//エラ〖でなければ
		if( jj != -1 ) {
			//ラン??ット
			check_data[ 0 ] = no;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 38 ]) );
		}
		break;
	//skilllevel
	case 11:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 16 ]) );
			break;
		}
		//ス?ルＩＤ艰り叫し
		no = get_num2( &buf2 );
		//ス?ル积ってる尸ル〖プする
		for( jj = 0; jj < CHAR_MAXSKILLHAVE; jj++ ){
			//ス?ル积ってるなら
			if( ch->player_addon->Skill[ jj].SkillId != -1 ){
				//このス?ルなら
				if( ch->player_addon->Skill[ jj ].SkillId == no ){
					//ス?ルレ?ル艰り叫し
					check_data[ 0 ] = ch->player_addon->Skill[ jj ].SkillLevel;
					break;
				}
			}
		}
		//チェッ?しないなら
		if( jj == CHAR_MAXSKILLHAVE ){
			//チェッ?搀眶?リア
			check_data[ 0 ] = 0;
		}
		break;
	//petspace
	case 12:
		//积ってる?ット眶える
		for( jj = 0; jj < CHAR_MAXPETHAVE; jj++ ){
			// ?ットのインデッ?ス
			Char *pch = (Char *)ch->player_addon->PetPointer[ jj ];
			// ?イン?〖が铜跟なら
			if( CHAR_CheckCharPointer( pch ) == TRUE ){
				//?ット眶裁换
				check_data[ 0 ]++;
			}
		}
		//鄂き眶に恃垂
		check_data[ 0 ] = CHAR_MAXPETHAVE - check_data[ 0 ];
		break;
	//petlevel
	case 13:
		//チェッ?搀眶?リア
		check_cnt = 0;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 17 ]) );
			break;
		}
		//?ット戎规艰り叫し
		no = get_num2( &buf2 );
		//积ってる?ット眶える
		for( jj = 0; jj < CHAR_MAXPETHAVE; jj++ ){
			// ?ットのインデッ?ス
			Char *pch = (Char *)ch->player_addon->PetPointer[ jj ];
			// ?イン?〖が铜跟なら
			if( CHAR_CheckCharPointer( pch ) == TRUE ){
				//この?ットなら
				if( pch->iu.pet.PetId == no ){
					//レ?ル艰り叫し
					check_data[ check_cnt++ ] = pch->i.Lv;
				}
			}
		}
		break;
	//leak0item
	case 14:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 27 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ??ウント
		check_data[ 0 ] = count_item( no, ch, 1 );
		break;
	//leak1item
	case 15:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 28 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		//アイテ??ウント
		check_data[ 0 ] = count_item( no, ch, 2 );
		break;
	//haveleak0item
	case 16:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '='){
			//肌の矢机が眶猛でなければ
			if( check_num( buf2+2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 29 ]) );
				break;
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 29 ]) );
			break;
		}
		//アイテ?戎规艰り叫し
		no = get_num( buf2+2);
		//アイテ?积ってるかチェッ?
		if( check_have_item( no, ch, 1 ) == TRUE ){
			//积ってる
			check_data[ 0 ] = no;
		} else {
			//积ってない、チェッ?搀眶?リア
			check_cnt = 0;
		}
		break;
	//haveleak1item
	case 17:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '='){
			//肌の矢机が眶猛でなければ
			if( check_num( buf2+2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 30 ]) );
				break;
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 30 ]) );
			break;
		}
		//アイテ?戎规艰り叫し
		no = get_num( buf2+2);
		//アイテ?积ってるかチェッ?
		if( check_have_item( no, ch, 2 ) == TRUE ){
			//积ってる
			check_data[ 0 ] = no;
		} else {
			//积ってない、チェッ?搀眶?リア∈バグ々∷
//			check_cnt = 0;
			//积ってない
			check_data[ 0 ] = -1;
		}
		break;
	//stackitemspace
	case 18:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 31 ]) );
			break;
		}
		//アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );
		// 呵络ス?ッ?眶を拇べる。
		jj = ITEM_getMaxRemainFromITEMtabl( no );
		//エラ〖なら
		if( jj == -1 ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 25 ]) );
			break;
		}
		//ス?ッ?アイテ?でなければ
		if( jj < 2 ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 32 ]) );
			break;
		}
		//アイテ??ウント
		check_data[ 0 ] = count_stack_item( no, ch, jj );
		break;
	//strcmpchat
	case 19:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '='){
			//宝?リッ?と孺秤なら
			if( *(buf2+2) == 'r' ){
				//宝?リッ?なら
				if( *(msg) == 0){
					check_data[ 0 ] = 0;
				} else {
					check_data[ 0 ] = -1;
				}
				*(buf2+2) = '0';
				break;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2+2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 34 ]) );
					break;
				}
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 34 ]) );
			break;
		}
		//メッ?〖ジＩＤ规艰り叫し
		no = get_num( buf2+2);
		//メッ?〖ジが办米なら
		if( strcmp( msg, MSG_getMessage( no ) ) == 0 ){
			check_data[ 0 ] = no;
		} else {
			check_data[ 0 ] = -1;
		}
		break;
	//strstrchat
	case 20:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '='){
			//宝?リッ?と孺秤なら
			if( *(buf2+2) == 'r' ){
				//宝?リッ?なら
				if( *(msg) == 0){
					check_data[ 0 ] = 0;
				} else {
					check_data[ 0 ] = -1;
				}
				*(buf2+2) = '0';
				break;
			} else {
				//肌の矢机が眶猛でなければ
				if( check_num( buf2+2 ) == FALSE ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 35 ]) );
					break;
				}
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 35 ]) );
			break;
		}
		//メッ?〖ジＩＤ规艰り叫し
		no = get_num( buf2+2);
		//メッ?〖ジが办米なら
		if( strstr( MSG_getMessage( no ), msg ) ){
			check_data[ 0 ] = no;
		} else {
			check_data[ 0 ] = -1;
		}
		break;
	//havetitle
	case 21:
		//チェッ?搀眶?リア
		check_cnt = 0;
		//积ってる疚规拇べる
		for( jj = 0; jj < CHAR_TITLEMAXHAVE; jj++ ){
			//疚规ＩＮＤＥＸ艰り叫し
			no = ch->player_addon->indexOfHaveTitle[ jj ];
			//积ってるなら
			if( no != -1 ){
				//?ット
				check_data[ check_cnt++ ] = no;
			}
		}
		break;
	//weapon
	case 22:
		//嫌１に刘洒しているアイテ?インデッ?ス?ット
		no = CHAR_getItemIndex( ch, CHAR_ARM1 );
		//部か积ってるなら
		if( no!= -1 ){
			//嫌１が绅达なら
			jj = ITEM_getInt( no, ITEM_TYPE );
			if( (jj >= ITEM_SWORD && jj <= ITEM_BOOMERANG) || ITEM_IsExtItemWeapon(jj)){
				//绅达戎规?ット
				check_data[ 0 ] = jj;
				break;
			}
		}
		//嫌２に刘洒しているアイテ?インデッ?ス?ット
		no = CHAR_getItemIndex( ch, CHAR_ARM2 );
		//部か积ってるなら
		if( no!= -1 ){
			//嫌２が绅达なら
			jj = ITEM_getInt( no, ITEM_TYPE );
			if( (jj >= ITEM_SWORD && jj <= ITEM_BOOMERANG) || ITEM_IsExtItemWeapon(jj)){
				//绅达戎规?ット
				check_data[ 0 ] = jj;
				break;
			}
		}
		//燎缄?ット
		check_data[ 0 ] = 7;
		break;
	//nowtime
	case 23:
		// 附哼の箕粗掠の猛。
		RealTimeToNRTime( NowTime.tv_sec, &nownrtime);
		no = getNRTime( &nownrtime ) ; // ０×３が手ってくる
		switch( no ){
		case NR_MORNING:	// 墨×赖羔
			check_data[ 0 ] = 0;
			break;
		case NR_NOON:		// 赖羔×图数
			check_data[ 0 ] = 1;
			break;
		case NR_EVENING:	// 图数×屉面
			check_data[ 0 ] = 2;
			break;
		default :			// 屉面×墨
			check_data[ 0 ] = 3;
			break;
		}
		break;
	//visitor
	case 24:
		//丸狄眶?ット
		check_data[ 0 ] = my->i.ForcePoint;
		break;
	//drugdish
	case 25:
		//积ってるアイテ?の改眶拇べる
		for( jj = CHAR_STARTITEMARRAY; jj < CHAR_MAXITEMHAVE; jj ++ ){
			//アイテ?ＩＩＮＤＥＸ艰り叫し
			index = CHAR_getItemIndex( ch, jj);
			//部か积ってるなら
			if( index != -1){
				//?イプ艰り叫し
				no = ITEM_getInt( index, ITEM_TYPE );
				//挑かフ〖ドなら
				if( no == ITEM_DRUG || no == ITEM_DISH ){
					//アイテ?眶裁换
					check_data[ 0 ] += ITEM_getInt( index ,ITEM_REMAIN );
				}
			}
		}
		break;
	//equipitem
	case 26:
		//==か!=なら
		if( ( *(buf2) == '=' || *(buf2) == '!' ) && *(buf2+1) == '=' ){
			//肌の矢机が眶猛でなければ
			if( check_num( buf2+2 ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 46 ]) );
				break;
			}
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 46 ]) );
			break;
		}
		//アイテ?戎规艰り叫し
		no = get_num( buf2+2 );
		//アイテ?积ってるかチェッ?
		if( check_equip_item( no, ch ) == TRUE ){
			//刘洒してる
			check_data[ 0 ] = no;
		} else {
			//刘洒してない
			check_data[ 0 ] = -1;
		}
		break;
	//houselimit
	case 27:
		//袋嘎艰り叫し
		check_data[ 0 ] = ch->iu.player.HouseLimit - NowTime.tv_sec;
		break;
	//shipchardown
	case 28:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 55 ]) );
			break;
		}
		//隶のＩＤ艰り叫し
		no = get_num2( &buf2 );
		//隶から惯りれるかチェッ?
		if( SHIP_CharDown( ch, no, 0 ) == 0 ){
			//惯りれる
			check_data[ 0 ] = 1;
		} else {
			//惯りれない
			check_data[ 0 ] = 0;
		}
		break;
	//shipchartake
	case 29:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
			break;
		}
		//隶のＩＤ艰り叫し
		no = get_num2( &buf2 );
		//?パレ〖?でなければ
		if( *buf2 != ',' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
			break;
		}
		//?パレ〖?ス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
			break;
		}
		//沽の戎规艰り叫し
		no2 = get_num2( &buf2 );
		//隶に捐れるかチェッ?
		if( SHIP_CharTake( ch, no, no2, 0 ) == 0 ){
			//捐れる
			check_data[ 0 ] = 1;
		} else {
			//捐れない
			check_data[ 0 ] = 0;
		}
		break;
	//shipgetstoptime
	case 30:
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 57 ]) );
			break;
		}
		//隶のＩＤ艰り叫し
		no = get_num2( &buf2 );
		//肌の掐沽までの箕粗?ット
		check_data[ 0 ] = SHIP_GetStopTime( no );
		break;
	//globaltimer
	case 31:
		//グロ〖バル?イ?〖构糠
		my->wu.npc.GlobalTimerA += ( signed int)( ( time( NULL)) - ( ( unsigned int)my->wu.npc.GlobalTimerTime));
		my->wu.npc.GlobalTimerB += ( signed int)( ( time( NULL)) - ( ( unsigned int)my->wu.npc.GlobalTimerTime));
		//( unsigned int)my->wu.npc.GlobalTimerTime = time( NULL);
		my->wu.npc.GlobalTimerTime = time( NULL);
		//どちらの?イ?〖を蝗脱するか
		if( *buf2 == 'a'){
			buf2++;
			check_data[0] = my->wu.npc.GlobalTimerA;
		}
		else if( *buf2 == 'b'){
			buf2++;
			check_data[0] = my->wu.npc.GlobalTimerB;
		}
		else {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 59 ]) );
		}
		break;
	// checkrealtime
	case 32:
		// 回年されている泣烧を豺老する
		{
			time_t nowtime;
			time_t checktime;

			// 附哼の箕癸を艰评
			nowtime = ((unsigned int)time(NULL));
			// 钳/奉/泣,箕:尸:擅を豺老
			checktime = datestr2time_t( &buf2);

			// datestr2time_tが0を手したらエラ〖だ
			if( checktime == 0){
				check_data[ 0 ] = 2;
				break;
			}

			// checktimeより涟なら0、笆惯なら1を?ットする
			if( nowtime < checktime) check_data[ 0 ] = 0;
			else check_data[ 0 ] = 1;
		}
		break;
#ifdef _USE_STAMP_ITEM
	// stampcount
	case 33:
		// ス?ンプアイテ?の?ウントを艰评する
		{
			int stampcount;
			int itemid;
			int itemindex;
			int itemplace;

			// アイテ?IDを艰评する
			if( !check_num( buf2)){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 60 ]) );
				break;
			}
			itemid = get_num2( &buf2);
			// 回年されたアイテ?IDのアイテ?を弥いてある疤弥を拇べる
			itemplace = check_haveitemid_place( ch, itemid);
			// 积っていない眷圭は-1を手す
			if( itemplace < 0){
				check_data[0] = -1;
				break;
			}
			// アイテ?インデッ?スを艰评
			itemindex = CHAR_getItemIndex( ch, itemplace);
			// ス?ンプアイテ?かどうか拇べる
			if( ITEM_getInt( itemindex, ITEM_TYPE) != ITEM_STAMP){
				check_data[0] = -1;
			}
			// ス?ンプ?ウントを艰评
			stampcount = ITEM_getInt( itemindex, ITEM_VAR2);
			// ス?ンプ?ウントを手す
			check_data[0] = stampcount;
		}
		break;
#endif /* _USE_STAMP_ITEM */
	// checkitemcategory
	case 34:
		// 回年されたアイテ??テゴリのアイテ?を积っているかどうかチェッ?する
		{
			int i;
			int itemcategory;
			int itemindex;
			int num;

			// チェッ?するアイテ?の?テゴリを艰评する
			if( !check_num( buf2)){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 61 ]) );
				break;
			}
			itemcategory = get_num2( &buf2);
			check_data[0] = FALSE;
			// 积っているアイテ?を链て拇べて、回年された?テゴリと办米するか拇べる
			for( i = 0; i < CHAR_MAXITEMHAVE; i++){
				itemindex = CHAR_getItemIndex( ch, i);
				if( itemindex < 0) continue;
				num = ITEM_getInt( itemindex, ITEM_TYPE);
				if( num < 0 || num >= ITEM_CATEGORYNUM) continue;
				if( itemcategory == num){
					check_data[0] = TRUE;
					break;
				}
			}
		}
		break;
#ifdef PUK2
	case 35 :	// globalFlag
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[63]) );
			break;
		}
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_GLOBAL_FLAG ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[63]) );
			break;
		}
		check_data[0] = (NPC_getGlobalFlag (ch, no) == 0) ? 0 : 1;
		break;
	case 36 :	// localFlag
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[64]) );
			break;
		}
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_LOCAL_FLAG ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[64]) );
			break;
		}
		check_data[0] = (NPC_getLocalFlag (ch, no) == 0) ? 0 : 1;
		break;
	case 37 :	// localCounter
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[65]) );
			break;
		}
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_LOCAL_COUNTER ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[65]) );
			break;
		}
		check_data[0] = NPC_getLocalCounter (ch, no);
		break;
	case 38 :	// getSkill
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 67 ]) );
			break;
		}
		no = get_num2( &buf2 );
		check_data[ 0 ] = NPC_doHaveSkill (ch, no);
		break;
	case 39 :	// bankitem
		if( check_num( buf2 ) == FALSE ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 68 ]) );
			break;
		}
		no = get_num2( &buf2 );
		check_data[ 0 ] = NPC_getBankItemCount(ch, no );
		break;
	case 40 :	// random
		if (check_num (buf2) == FALSE) {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 69 ]) );
			break;
		}
		no = get_num2 (&buf2);
		if( *buf2 != ',' ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 69 ]) );
			break;
		}
		++buf2;
		if (check_num (buf2) == FALSE) {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 69 ]) );
			break;
		}
		no2 = get_num2 (&buf2);
		check_data[0] = NPC_getRandom (ch, 1, no, no2);
		break;

	case 41 :		// guildMember
		if (ch->guildInfo->guildID == -1) {
			check_data[0] = 0;
			break;
		}
		if (!ch->guild) {
			check_data[0] = 0;
			eprint ("guildInfoError\n");
			break;
		}

		if ((((GUILD*)(ch->guild))->regNo == ch->iu.player.RegistNumber)
			&& (strcmp (((GUILD*)(ch->guild))->cdKey, ch->cu.player.CdKey) == 0))
			check_data[0] = 1;
		else
			check_data[0] = 2;
		break;

#endif 
#ifdef _ENABLE_ALBUM_ITEMS
	// albumitemflg
	case 42:
		// アルバ?アイテ?のフラグを拇べる
		check_data[0] = ALBUM_checkAlbumItemFlg( ch, &buf2);
		if( check_data[0] == -1){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 73 ]) );
		}
		break;
	// getalbumflgtotal
	case 43:
		// 回年认跋のアルバ?フラグの圭纷を艰评する
		check_data[0] = ALBUM_getFlgTotal( ch, &buf2);
		if( check_data[0] == -1){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 74 ]) );
		}
		break;
#endif /* _ENABLE_ALBUM_ITEMS */
	// recipeflg
	case 44:
		{
			int recipeid;

			// レシピ戎规粕み艰り
			if( !check_num( buf2)){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 75 ]) );
				break;
			}
			recipeid = get_num2( &buf2);
			// そのレシピを承えているか
			if( CHAR_checkRecipeFlg( ch, recipeid)){
				check_data[0] = 1;
			}
			else check_data[0] = 0;
		}
		break;
	// feverflg
	case 45:
		check_data[0] = CHAR_IsFeverTime( ch);
		break;

	// feversec
	case 46:
		check_data[0] = CHAR_FeverHaveTime( ch);
		break;

#ifdef ITEMCOMMAND
	// Hitem
	case 47:
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 78 ]) );
                        break;
                }
                //アイテ?ＩＤ艰り叫し
	        no = get_num2( &buf2 );
                //アイテ??ウント
                check_data[ 0 ] = count_item2( no, ch, 0 );
		break;
	// Pitem
	case 48:
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 79 ]) );
                        break;
                }
                //アイテ?ＩＤ艰り叫し
                no = get_num2( &buf2 );
		//?パレ〖?でなければ
		if( *buf2 != ',' ){
			// エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 79 ]) );
			break;
		}
		//?パレ〖?ス?ップ
		buf2++;
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 79 ]) );
                        break;
                }
		// パ〖ティ〖ＮＯ艰り叫し
		no2 = get_num2( &buf2 );
		//アイテ??ウント
		check_data[ 0 ] = count_item3( no, no2, ch, 0 );
		break;	
	// PHitem
	case 49:
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 80 ]) );
                        break;
                }
                //アイテ?ＩＤ艰り叫し
                no = get_num2( &buf2 );
                //?パレ〖?でなければ
                if( *buf2 != ',' ){
                        // エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 80 ]) );
                        break;
                }
                //?パレ〖?ス?ップ
                buf2++;
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 80 ]) );
                        break;
                }
                // パ〖ティ〖ＮＯ艰り叫し
                no2 = get_num2( &buf2 );
                //アイテ??ウント
                check_data[ 0 ] = count_item4( no, no2, ch, 0 );
                break;
	// PEquipItem
	case 50:
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 81 ]) );
                        break;
                }
                //アイテ?ＩＤ艰り叫し
                no = get_num2( &buf2 );
                //?パレ〖?でなければ
                if( *buf2 != ',' ){
                        // エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 81 ]) );
                        break;
                }
                //?パレ〖?ス?ップ
                buf2++;
                //肌の矢机が眶猛でなければ
                if( check_num( buf2 ) == FALSE ){
                        //エラ〖
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 81 ]) );
                        break;
                }
                // パ〖ティ〖ＮＯ艰り叫し
                no2 = get_num2( &buf2 );
                //アイテ??ウント
                check_data[ 0 ] = count_item5( no, no2, ch, 0 );
                break;
#endif
#ifdef GLOBALFLAG_T
	case 51:	// globalFlagt
                if( check_num( buf2 ) == FALSE ){
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[82]) );
                        break;
                }
                no = get_num2( &buf2 );
                if( no < 0 || no > MAX_GLOBAL_FLAG_T ){
                        print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[82]) );
                        break;
                }
                check_data[0] = (NPC_getGlobalFlag_T (ch, no) == 0) ? 0 : 1;
                break;
#endif /* GLOBALFLAG */

#ifdef ADDSKILLSLOT
	case 52:	//checkskillslot
		check_data[0] = ch->i.HaveSkillLimit;
		break;
#endif	/* ADDSKILLSLOT */

#ifdef CHECK_GENDER
	case 53:	//sex
		{
			int sex;
			sex = CHAR_getCharGender( ch );
				// 盟、谨笆嘲ならエラ〖を叫蜗
				if( sex == -1) {
					print(" \n%s:%d %s\n",file_name, read_line, TMSG(com_err[83]) );
					break;
				}
			check_data[0] = sex;
		}
		break;
#endif	/* CHECK_GENDER */

#ifdef GLOBALFLAG_GA
	case 54:	//globalFlagGA
		if( check_num( buf2 ) == FALSE )
		{
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
			break;
		}
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_GLOBAL_FLAG )
		{
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[ 85 ]));
			break;
		}
		check_data[0] = (NPC_getGlobalFlagGA(ch, no) == 0) ? 0 : 1;
		break;
#endif	/* GLOBALFLAG_GA */

#ifdef ADD_ITEM_COMMAND
	case 55:	//PallItem

		// 眶猛チェッ?
		if( check_num( buf2 ) == FALSE )
		{
			// エラ〖をログに叫蜗
			print( "\n%s:%d %s\n",file_name, read_line, TMSG(com_err[87]) );
			check_data[0] = -1;
			break;
		}

		// アイテ?ＩＤ艰り叫し
		no = get_num2( &buf2 );

		// アイテ?疥积の材容∈パ〖ティ〖链镑积っているかチェッ?∷
		check_data[0] = (check_haveitem_partyall( ch, no ) == FALSE) ? 0 : 1;

		break;
#endif /* ADD_ITEM_COMMAND */

#ifdef ADD_GLOBALFLAG_COMMAND
	case 56:	// Pallgflag

		// 眶猛チェッ?
		if( check_num( buf2 ) == FALSE )
		{
			// エラ〖ログ叫蜗
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[88]) );
			check_data[0] = -1;
			break;
		}

		// グロ〖バルフラグＩＤの艰评
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_GLOBAL_FLAG )
		{
			// エラ〖叫蜗
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[88]) );
			check_data[0] = -1;
			break;
		}

		// パ〖ティ〖链镑のグロ〖バルフラグをチェッ?
		check_data[0] = check_globalflg_partyall( ch, no );

		break;

	case 57:	//Pgflag

		// 眶猛チェッ?
		if( check_num( buf2 ) == FALSE )
		{
			// エラ〖ログ叫蜗
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[89]) );
			check_data[0] = -1;
			break;
		}

		// グロ〖バルフラグＩＤの艰评
		no = get_num2( &buf2 );
		if( no < 0 || no > MAX_GLOBAL_FLAG )
		{
			print("\n%s:%d %s\n",file_name, read_line, TMSG(com_err[89]) );
			check_data[0] = -1;
			break;
		}

		//?パレ〖?でなければ
		if( *buf2 != ',' )
		{
			// エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[89]) );
			check_data[0] = -1;
			break;
		}

		//?パレ〖?ス?ップ
		buf2++;

		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE )
		{
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[89]) );
			check_data[0] = -1;
			break;
		}
		
		// パ〖ティ〖ＮＯ艰り叫し
		no2 = get_num2( &buf2 );

		// パ〖ティ〖柒のグロ〖バルフラグチェッ?
		check_data[0] = check_globalflg( ch, no, no2 );

		break;

#endif	/* ADD_GLOBALFLAG_COMMAND */
//        case 58:
//            check_data[0] = getplayer( ch, &buf2);
//            break;
//        case 59:
//            check_data[0] = getpet( ch, &buf2);
//            break;
//        case 60:
//            check_data[0] = getitem( ch, &buf2);
//            break;
        case 61:         /*luac*/
            check_data[0] = EmitScriptCallInline(my, ch, &buf2, msg);
            break;
	default:
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
	}

	//アドレス构糠
	*buf = buf2;
}


static char *window_flg_tbl[]={"windowok","windowyes","windowno"};
//-------------------------------------------------------------------------
// ウインドウラ?ルチェッ?
// 苞眶¨
// 		*buf コ?ンド矢机误
// 		*ii  芹误戎规を呈羌するアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL check_window_label( char *buf, int *ii )
{
	int jj;

	for( jj = 0; jj < arraysizeof( window_flg_tbl ); jj++ ){
		//ウインドウラ?ルなら
		if( strncmp2( buf, window_flg_tbl[ jj ], strlen( window_flg_tbl[ jj ] ) ) == 0 ){
			*ii = jj;
			return TRUE;
		}
	}
	return FALSE;
}


//-------------------------------------------------------------------------
// ブロッ?豺老
// 苞眶¨
// 		*ch  ト〖?〖の?イン?〖
// 		*buf コ?ンド矢机误
// 		*msg
// 		window_flg　０¨ノ〖?ル　１¨ＯＫ　２¨ＹＥＳ　３¨ＮＯ
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL analyse_block( Char *my, Char *ch, char *buf, char *msg, int window_flg )
{
	int ii;
	int pattern = 0;
	int num;
	int flg;
	int com_mode;

#ifdef DB_SCRIPT
	BOOL ret = FALSE;
	BOOL getflg = FALSE;
#endif

	//コ?ンド??ロ
	BLOCK_COMMAND

	//ウインドウフラグなら
	if( window_flg ){
		//ウインドウラ?ルなら
		if( check_window_label( buf, &ii ) == TRUE ){
			//ラ?ルス?ップ
			buf += strlen( window_flg_tbl[ ii ] );
		} else {
			//己窃
			return FALSE;
		}
	} else {
		//ブロッ?なら
		if( strncmp2( buf, "block", 5 ) == 0 ){
			//blockス?ップ
			buf += 5;
		} else {
			//己窃
			return FALSE;
		}
	}
	//掘凤尸帆り手す
	while( 1 ){
		//改侍コ?ンドフラグ?リア
		com_mode = 0;
		for( ii = 0; ii < arraysizeof( block_com ); ii++ ){
			//改侍コ?ンド磊り仑えなら
			if( block_com[ ii ][ 0 ] == 0 ){
				//改侍コ?ンドフラグ?ット
				com_mode = ii;
				continue;
			}
			//コ?ンド券斧なら
			if( strncmp2( buf, block_com[ ii ], strlen( block_com[ ii ] ) ) == 0 ){
				break;
			}
		}
		//斧つからなかったら
		if( ii == arraysizeof( block_com ) ){
			//鄂でなければ
			if( *buf ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
				//己窃
				return FALSE;
			}
			break;
		}
		//コ?ンド尸ス?ップ
		buf += strlen( block_com[ ii ] );

#ifdef DB_SCRIPT
		getflg = FALSE;
		if( strncmp2( block_com[ ii ], "getplayer", 9) == 0){
			getflg = TRUE;
			ret = getplayer( ch, &buf);
		}
		else if( strncmp2( block_com[ ii ], "getpet", 6) == 0){
			getflg = TRUE;
			ret = getpet( ch, &buf);

		}
		else if( strncmp2( block_com[ ii ], "getitem", 7) == 0){
			getflg = TRUE;
			ret = getitem( ch, &buf);

        }

		if(getflg){
			if( ret ){
				if( *buf == '&' && *(buf+1) == '&' ){
					buf += 2;
					continue;
				} else {
					if( *buf == 0 ){
						return TRUE;
					}
					if( *buf == '|' || *(buf+1) == '|' ){
						return TRUE;
					} else {
						print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
						return FALSE;
					}
				}
			} else {
				while( 1 ){
					if( *buf == 0 ){
						return FALSE;
					} else {
						if( *buf == '|' && *(buf+1) == '|' ){
							buf += 2;
							break;
						}
					}
					buf++;
				}
			}
		continue;
		}

#endif

        if (strncmp2(block_com[ii], "luac", 4) == 0) {
            check_data[0] = EmitScriptCallInline(my, ch, &buf, msg);
            check_cnt = 1;
        } else {
            //改侍コ?ンドなら
            if (com_mode) {
//			check_data = get_check_data( (int)block_com_ch[ ii ], ch, &buf );
                set_check_data(ii - com_mode, my, ch, &buf, msg);
            } else {
                //ＣＨデ〖?艰り叫し
                check_data[0] = *block_com_ch[ii];
                //チェッ?搀眶１搀?ット
                check_cnt = 1;
            }
        }

		//掘凤艰り叫し
		if( *(buf+1) == '=' ){
			if( *buf == '=' ){
				//♂♂
				pattern = 0;
				buf += 2;
			} else
			if( *buf == '!' ){
				//—♂
				pattern = 1;
				buf += 2;
			} else
			if( *buf == '>' ){
				//′♂
				pattern = 2;
				buf += 2;
			} else
			if( *buf == '<' ){
				//°♂
				pattern = 3;
				buf += 2;
			}
		} else
		if( *buf == '>' ){
			//′
			pattern = 4;
			buf += 1;
		} else
		if( *buf == '<' ){
			//°
			pattern = 5;
			buf += 1;
		} else {
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			//己窃
			return FALSE;
		}
#if 0
		//肌の矢机が眶猛でなければ
		if( check_num( buf ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
			//己窃
			return FALSE;
		}
		//掘凤眶猛艰り叫し
		num = get_num2( &buf );
#else
		//肌の矢机が眶猛でなければ
		if( check_num( buf ) == FALSE ){
			//肌の矢机がブロッ?コ?ンドなら
			if( get_ch_com( ch, &buf, &num ) == FALSE ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
				//己窃
				return FALSE;
			}
		} else {
			//掘凤眶猛艰り叫し
			num = get_num2( &buf );
		}
#endif
		//掘凤フラグ?リア
		flg = 0;
		//チェッ?搀眶尸ル〖プ
		for( ii = 0; ii < check_cnt ;ii++ ){
			//掘凤チェッ?
			switch( pattern ){
			//♂♂
			case 0:
				//掘凤办米なら
				if( check_data[ ii ] == num ){
					flg = 1;
				}
				break;
			//—♂
			case 1:
				//掘凤办米なら
				if( check_data[ ii ] != num ){
					flg = 1;
				}
				break;
				//′♂
				case 2:
				//掘凤办米なら
				if( check_data[ ii ] >= num ){
					flg = 1;
				}
				break;
			//°♂
			case 3:
				//掘凤办米なら
				if( check_data[ ii ] <= num ){
					flg = 1;
				}
				break;
			//′
			case 4:
				//掘凤办米なら
				if( check_data[ ii ] > num ){
					flg = 1;
				}
				break;
			//°
			case 5:
				//掘凤办米なら
				if( check_data[ ii ] < num ){
					flg = 1;
				}
				break;
			}
			//掘凤办米したなら
			if( flg ){
				break;
			}
		}
		//掘凤办米したなら
		if( flg ){
			//□□あるなら
			if( *buf == '&' && *(buf+1) == '&' ){
				//肌もチェッ?
				buf += 2;
			} else {
				//姜位コ〖ドなら
				if( *buf == 0 ){
					//姜位
					break;
				}
				//∶∶なら
				if( *buf == '|' || *(buf+1) == '|' ){
					//姜位
					break;
				} else {
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
					//己窃
					return FALSE;
				}
			}
		//掘凤办米しなかったなら
		} else {
			//∶∶があるか拇べる
			while( 1 ){
				//姜位コ〖ドなら
				if( *buf == 0 ){
					//己窃
					return FALSE;
				} else {
					//∶∶なら
					if( *buf == '|' && *(buf+1) == '|' ){
						//∶∶笆惯浩刨チェッ?
						buf += 2;
						break;
					}
				}
				buf++;
			}
		}
	}
	return TRUE;
}
#ifdef GOTOCOMMAND
BOOL gotoF( FILE* fp, char* buf, Char *talker )
{
	// 2004/04/09/ バッファ翁饯赖
	// 	       static猴近
	char label1[64];
	char label2[1024];
	
	sprintf( label1, "*" );
	strcat( label1, buf );
	strcat( label1, "*" );

	while( get_line( fp, label2 ) )
	{
		if( strncmp2( label2, label1, strlen( label1 ) ) == 0 )
		{
			return TRUE;
		}
	}
	return FALSE;
}
BOOL gotoB( FILE *fp, char* buf, Char *talker )
{
	// 2004/04/09 バッファ翁饯赖
	//	      static猴近
	char label1[64];
	char label2[1024];

	sprintf( label1, "*" );
	strcat( label1, buf );
	strcat( label1, "*" );

	read_line = 0;		// 借妄乖眶?リア
	get_line_cnt = 0;	// 悸乖借妄乖眶?リア

	rewind( fp );
	while( get_line( fp, label2 ) )
	{
		if( strncmp2( label2, label1, strlen( label1 ) ) == 0 )
		{
			return TRUE;
		}
	}
	return FALSE;
}
#endif

#ifdef GO_SUB
BOOL gosub( FILE *fp, char* buf, Char *talker )
{
	char gslabel1[64];
	char gslabel2[1024];

	if( talker->wu.player.go_cnt < CHAR_NESTMAX ) 
	{
		talker->wu.player.line_point[talker->wu.player.go_cnt] = get_line_cnt;
		talker->wu.player.str_point[talker->wu.player.go_cnt] = ftell( fp )+2;
		talker->wu.player.gosub_flg[talker->wu.player.go_cnt] = TRUE;
		++talker->wu.player.go_cnt;
	}else 
	{
			int i;
			print_t(LANG_MSG_AUTOMATIC_220,talker->wu.player.go_cnt);
			read_line = 0;
			get_line_cnt = 0;
			talker->wu.player.go_cnt = 0;
			for( i = 0; i < CHAR_NESTMAX; ++i )
			{
				talker->wu.player.line_point[i] = 0;
				talker->wu.player.str_point[i] = 0;
			}
			rewind( fp );
			return FALSE;
	}	

	if( talker->wu.player.go_cnt != CHAR_NESTMAX )
	{
		sprintf( gslabel1, "$" );
		strcat( gslabel1, buf );
		strcat( gslabel1, "$" );
	
		while( get_line( fp, gslabel2 ) )
		{
			if( strncmp2( gslabel2, gslabel1, strlen( gslabel1 ) ) == 0 )
			{
				return TRUE;
			}
		}
	}
	return FALSE;
}
BOOL gosub_return( FILE *fp, Char *talker, Char *my )
{
	if( talker->wu.player.go_cnt < 0 || talker->wu.player.go_cnt >= CHAR_NESTMAX )
	{
		int i;
		print_t(LANG_MSG_AUTOMATIC_221,talker->wu.player.go_cnt);
		read_line = 0;
		get_line_cnt = 0;
		talker->wu.player.go_cnt = 0;
		for( i = 0; i < CHAR_NESTMAX; ++i )
		{
			talker->wu.player.line_point[i] = 0;
			talker->wu.player.str_point[i] = 0;
		}
		rewind( fp );
		return FALSE;
	}
	fseek( fp, talker->wu.player.str_point[talker->wu.player.go_cnt-1], SEEK_SET );
	get_line_cnt = talker->wu.player.line_point[talker->wu.player.go_cnt-1];
	talker->wu.player.gosub_flg[talker->wu.player.go_cnt-1] = FALSE;
	--talker->wu.player.go_cnt;
	return TRUE;

}
#endif	/* GO_SUB */

#ifdef PUK2
BOOL skipToNextBlock (FILE* fp)
{
/*
	char buf[1024];
	int ccElse = 0;
	int ccEndif = 0;

	while (get_line( fp, buf )) {
		if (strncmp2 (buf, "if", 2) == 0) {
			++ccElse;
			++ccEndif;
			continue;
		}
		if (strncmp2 (buf, "else", 4) == 0) {
			if (ccElse == 0)
				return;
			else
				--ccElse;
			continue;
		}

		if (strncmp2 (buf, "endif", 5) == 0) {
			if (ccEndif == 0)
				return;
			else {
				--ccEndif;
				--ccElse;
			}
			continue;
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
	fclose (fp);
*/
	char buf[1024];
	int cc = 0;


	while (get_line (fp, buf)) {
		if (strncmp2 (buf, "else", 4) == 0) 
			return TRUE;
		if (strncmp2 (buf, "endif", 5) == 0)
			return TRUE;
		if (strncmp2 (buf, "if", 2) == 0) {
			++cc;
			while (get_line (fp, buf)) {
				if (strncmp2 (buf, "if", 2) == 0) {
					++cc;
					continue;
				}
				if (strncmp2 (buf, "endif", 5) == 0) {
					--cc;
					if (cc == 0)
						break;
				}
			}
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
#ifndef MODIFY_COMMAND1
	fclose (fp);
#endif	/* MODIFY_COMMAND */
	return FALSE;
}

// (return value == 0) ? false : (retur value == 1) ? true : error
static int analyse_if ( Char *my, Char *ch, char **buf, char *msg, int window_flg, FILE* fp)
{
	int ii;
	int pattern = 0;
	int num;
	int flg;
	int com_mode;
	char* buf2;
	enum {
		false,
		true,
		error
	};

#ifdef DB_SCRIPT
		BOOL ret = FALSE;
		BOOL getflg = FALSE;
#endif

	BLOCK_COMMAND

	buf2 = *buf;

	while( 1 ){
		com_mode = 0;
		for( ii = 0; ii < arraysizeof( block_com ); ii++ ){
			if( block_com[ ii ][ 0 ] == 0 ){
				com_mode = ii;
				continue;
			}
			if( strncmp2( buf2, block_com[ ii ], strlen( block_com[ ii ] ) ) == 0 )
				break;
		}
		if( ii == arraysizeof( block_com ) ){
			if( *buf2 ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
				return error;
			}
			break;
		}
		//コ?ンド尸ス?ップ
		buf2 += strlen( block_com[ ii ] );

#ifdef DB_SCRIPT
				getflg = FALSE;
				if( strncmp2( block_com[ ii ], "getplayer", 9) == 0){
					getflg = TRUE;
					ret = getplayer( ch, &buf2);
				}
				else if( strncmp2( block_com[ ii ], "getpet", 6) == 0){
					getflg = TRUE;
					ret = getpet( ch, &buf2);
		
				}
				else if( strncmp2( block_com[ ii ], "getitem", 7) == 0){
					getflg = TRUE;
					ret = getitem( ch, &buf2);
		
				}
		
				if(getflg){
					if( ret ){
						if( *buf2 == '&' && *(buf2+1) == '&' ){
							buf2 += 2;
							continue;
						} else {
							if( *buf2 == 0 ){
								return TRUE;
							}
							if( *buf2 == '|' || *(buf2+1) == '|' ){
								return TRUE;
							} else {
								print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
								return FALSE;
							}
						}
					} else {
						while( 1 ){
							if( *buf2 == 0 ){
								return FALSE;
							} else {
								if( *buf2 == '|' && *(buf2+1) == '|' ){
									buf2 += 2;
									break;
								}
							}
							buf2++;
						}
					}
				continue;
				}
		
#endif

		//改侍コ?ンドなら
		if( com_mode ) {
			check_data[1] = check_data[0];
			set_check_data( ii - com_mode, my, ch, &buf2, msg );
			if (pattern == 6) 
				check_data[0] += check_data[1];
			else if (pattern == 7)
				check_data[0] = check_data[1] - check_data[0];
		}
		else {
			if (pattern == 6)
				check_data[0] += *block_com_ch[ii];
			else if (pattern == 7)
				check_data[0] -= *block_com_ch[ii];
			else
				check_data[0] = *block_com_ch[ii];
			//チェッ?搀眶１搀?ット
			check_cnt = 1;
		}
/*
		switch (*(buf2 + 1)) {
			case '=': 
				pattern = (*buf2 == '=') ? 0 : (*buf2 == '!') ? 1 : (*buf2 == '>') ? 2 : 3;
				buf2 += 2;
				break;
			case '>' :
				pattern = 4;
				++buf2;
				break;
			case '<' :
				pattern = 5;
				++buf2;
				break;
			case '+' :
				pattern = 6;
				++buf2;
				break;
			case '-' :
				pattern = 7;
				++buf2;
				break;
			default :
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
				*buf = buf2;
				return error;
		}
*/


		if (*(buf2 + 1) == '=') {
				pattern = (*buf2 == '=') ? 0 : (*buf2 == '!') ? 1 : (*buf2 == '>') ? 2 : 3;
				buf2 += 2;
		}
		else {
			switch (*buf2) {
				case '>' :
					pattern = 4;
					++buf2;
					break;
				case '<' :
					pattern = 5;
					++buf2;
					break;
				case '+' :
					pattern = 6;
					++buf2;
					break;
				case '-' :
					pattern = 7;
					++buf2;
					break;
				default :
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
					*buf = buf2;
					return error;
			}
		}


		if ((pattern == 6) || (pattern == 7))
			continue;
//			break;

		if( check_num( buf2 ) == FALSE ){		 //肌の矢机がブロッ?コ?ンドなら
			if( get_ch_com( ch, &buf2, &num ) == FALSE ){
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
				return error;
			}
		} else 
			num = get_num2( &buf2 );

		flg = 0;
		for( ii = 0; ii < check_cnt ;ii++ ){
			switch( pattern ) {						 	//掘凤チェッ?
				case 0:									// == 
					flg = (check_data[ii] == num);
					break;
				case 1:									// != 
					flg = (check_data[ii] != num);
					break;
				case 2:									// >=
					flg = (check_data[ii] >= num);
					break;
				case 3:									// <=
					flg = (check_data[ii] <= num);
					break;
				case 4:									// >
					flg = (check_data[ii] > num);
					break;
				case 5:									// <
					flg = (check_data[ii] < num);
					break;
			}
			if( flg )
				break;
		}


		if( flg ){							 //掘凤办米したなら
			if( *buf2 == '&' && *(buf2+1) == '&' ){		// &&
				buf2 += 2;
			} else {
				if( *buf2 == 0 ){
					break;
				}
				if( *buf2 == '|' || *(buf2+1) == '|' ){	// ||
					break;
				} else {
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[66]) );
					return error;
				}
			}
		} else {							 //掘凤办米しなかったなら
			while( 1 ){						 //||があるか拇べる
				if( *buf2 == 0 ){
					return skipToNextBlock (fp);
//					skipToNextBlock (fp);
//					return FALSE;
				} else {
					if( *buf2 == '|' && *(buf2+1) == '|' ){	// ||
						buf2 += 2;
						break;
					}
				}
				buf2++;
			}
		}
	}
	*buf = buf2;
	return TRUE;
}

#endif



static BOOL Event_StandEnemyInit( Char *pMyBody, char *pArg );
static BOOL Event_StandEnemyTalked( Char *pMyBody, Char *pTalker, char *msg, int color );
//-------------------------------------------------------------------------
// エン?ウント倡幌
// 苞眶¨
// 		*my
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
BOOL encount_event_battle( Char *my, Char *talker, char **buf )
{
	char *buf2;
	char arg[ 4096 ];

	//アドレスコピ〖
	buf2 = *buf;

	//ス?ンドエネ?〖の苞眶コピ〖
    _STRNCPYSAFE( arg, buf2, sizeof( arg ) );
	//苞眶痰いなら
	if( arg[ 0 ] == 0 ){
		return FALSE;
	}
	//盖年浓介袋步
	Event_StandEnemyInit( my, arg );
	//盖年浓里飘倡幌
	if( Event_StandEnemyTalked( my, talker, NULL, 0 ) == FALSE ){
		// 里飘が券栏しなかった
		return FALSE;
	}

	//アドレス渴める
	buf2 += strlen( arg );
	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// レシピ畔す
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL give_recipe( Char *talker, char **buf )
{
	char *buf2;
	int recipeid;
	char moji[ 256 ];
	int ii;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 48 ]) );
		return FALSE;
	}
	//メッ?〖ジＩＤ艰り叫し
	recipeid = get_num2( &buf2 );
	//レシピ积ってないなら
	if( CHAR_checkRecipeFlg( talker, recipeid ) == FALSE ){
//		if( recipeid >= arraysizeof( talker->iu.player.Recipe ) * sizeof( unsigned int) * 8 ){
//	        // "レシピのIDの认跋を臂えています。"
//			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_CHATMAGIC_C_022) );
//			return FALSE;
//		}
		if( ITEM_RECIPE_getIndex( recipeid ) == -1 ){
	        // "レシピのIDの认跋を臂えています。"
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_CHATMAGIC_C_023) );
			return FALSE;
		}
		//レシピ?ット
		CHAR_setRecipeFlg( talker, recipeid );
		//链ス?ル流慨
		for( ii = 0; ii < CHAR_MAXSKILLHAVE; ii++ ){
			CHAR_sendItemRecipe( talker, ii );
		}
		//システ?メッ?〖ジ山绩∈レシピを缄に掐れた∷
		translate_format(moji, sizeof( moji ), LANG_MSG_EVENT_008, ITEM_RECIPE_getName( recipeid ) );
		SYSTEMMSG( talker, moji );
	}

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// ?ンション袋嘎恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_houselimit( Char *talker, char **buf )
{
	char *buf2;
	int secound;
	int add_flg = 0;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 49 ]) );
		return FALSE;
	}
	//プラスか?イナスなら
	if( *buf2 == '+' || *buf2 == '-' ){
		//陵滦回年
		add_flg = 1;
	}
	//泣にち艰り叫し
	secound = get_num2( &buf2 );
	//擅に恃垂
	secound = secound * 24 * 60 * 60;
	//陵滦回年なら
	if( add_flg ){
		//荒り?イ??ット
		talker->iu.player.HouseLimit += secound;
	} else {
		//荒り?イ??ット
		talker->iu.player.HouseLimit = secound + NowTime.tv_sec;
	}

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// ?ンション关掐
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_house( Char *talker, char **buf )
{
	char *buf2;
	int id;
	int day = 14;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 50 ]) );
		return FALSE;
	}
	//ＩＤ艰り叫し
	id = get_num2( &buf2 );
	//泣にち回年なら
	if( *buf2 == ',' ){
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 50 ]) );
			return FALSE;
		}
		//泣にち艰り叫し
		day = get_num2( &buf2 );
		//擅に恃垂
		day = day * 24 * 60 * 60;
	}
	//袋嘎を?ット
	if( HOUSE_setHouseLimit( talker, id, NowTime.tv_sec + day ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 51 ]) );
		return FALSE;
	}

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 笨恃构
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_luck( Char *talker, char **buf )
{
	char *buf2;
	int num1,num2;
	int add_data;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 52 ]) );
		return FALSE;
	}
	//ラン??１艰り叫し
	num1 = get_num2( &buf2 );
	//ラン??回年なら
	if( *buf2 == ',' ){
		//１バイトス?ップ
		buf2++;
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 52 ]) );
			return FALSE;
		}
		//ラン??２艰り叫し
		num2 = get_num2( &buf2 );
		//ラン??１の数が络きいなら
		if( num1 > num2 ){
			//ラン??１、２掐れ仑え
			add_data = num1;
			num1 = num2;
			num2 = add_data;
		}
		add_data = RAND( num1, num2 );
	} else {
		add_data = num1;
	}
	//笨恃构
	talker->i.Luck += add_data;
	//リ?ットチェッ?
	if( talker->i.Luck < 1 ){
		talker->i.Luck = 1;
	} else
	if( talker->i.Luck > 5 ){
		talker->i.Luck = 5;
	}
	CHAR_complianceParameter( talker );

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// ア?ション恃构
// 苞眶¨
// 		*talker
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_action( Char *talker, Char *my, char **buf )
{
	char *buf2;
	int data;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 54 ]) );
		return FALSE;
	}
	//ア?ション戎规艰り叫し
	data = get_num2( &buf2 );
	//エラ〖なら
	if( data < 0 || data > CHAR_ACTPAA ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 54 ]) );
		return FALSE;
	}
	//ア?ション恃构
	CHAR_sendWatchEvent( my->w.ObjIndex, data, NULL, 0, TRUE );

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 隶から惯りる
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL ship_char_down( Char *talker, char **buf )
{
	char *buf2;
	int ship;
	int ret;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 55 ]) );
		return FALSE;
	}
	//隶ＩＤ艰り叫し
	ship = get_num2( &buf2 );
	//隶から惯りる
	ret = SHIP_CharDown( talker, ship, 1 );
	//エラ〖なら
	if( ret != 0 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 55 ]) );
		return FALSE;
	}

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 隶に捐る
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL ship_char_take( Char *talker, char **buf )
{
	char *buf2;
	int ship,harbor;
	int ret;

	//アドレスコピ〖
	buf2 = *buf;

	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
		return FALSE;
	}
	//隶ＩＤ艰り叫し
	ship = get_num2( &buf2 );
	//?パレ〖?でなければ
	if( *buf2 != ',' ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
		return FALSE;
	}
	//?パレ〖?ス?ップ
	buf2++;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
		return FALSE;
	}
	//沽戎规艰り叫し
	harbor = get_num2( &buf2 );
	//隶に捐る
	ret = SHIP_CharTake( talker, ship, harbor, 1 );
	//エラ〖なら
	if( ret != 0 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 56 ]) );
		return FALSE;
	}

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// 跟蔡不浩栏
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL play_se( Char *talker, char **buf )
{
	char *buf2;
	int ii;
	int data[3];
	int fd;

	//アドレスコピ〖
	buf2 = *buf;

	//ト〖?〖のＦＤ艰り叫し
	fd = getfdFromChar( talker);
	//ＦＤが痰跟なら
	if( fd <= -1 ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 58 ]) );
		return FALSE;
	}

	//ＳＥ戎规、Ｘ郝筛、Ｙ郝筛艰り叫し
	for( ii = 0; ii < 3; ii++ ){
		//肌の矢机が眶猛でなければ
		if( check_num( buf2 ) == FALSE ){
			//エラ〖
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 58 ]) );
			return FALSE;
		}
		//デ〖?艰り叫し
		data[ ii ] = get_num2( &buf2 );
		//３つ誊でなければ
		if( ii <= 1 ){
			//?パレ〖?でなければ
			if( *buf2 != ',' ){
				//エラ〖
				print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 58 ]) );
				return FALSE;
			}
			//?パレ〖?ス?ップ
			buf2++;
		}
	}

	//ＳＥ戎规流慨
	nrproto_PLAYSE_send( fd, data[ 0 ], data[ 1 ], data[ 2 ] );

	//アドレス构糠
	*buf = buf2;
	return TRUE;
}


//-------------------------------------------------------------------------
// グロ〖バル?イ?〖肋年
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL SetGlobalTimer( Char *my, char **buf)
{
	char *buf2;
	unsigned int *settimer;
	unsigned int setnum;

	//アドレスコピ〖
	buf2 = *buf;

	//グロ〖バル?イ?〖构糠
	my->wu.npc.GlobalTimerA += ( signed int)( ( time( NULL)) - ( ( unsigned int)my->wu.npc.GlobalTimerTime));
	my->wu.npc.GlobalTimerB += ( signed int)( ( time( NULL)) - ( ( unsigned int)my->wu.npc.GlobalTimerTime));
	//( unsigned int)my->wu.npc.GlobalTimerTime = time( NULL);
	my->wu.npc.GlobalTimerTime = time( NULL);

	//企つのグロ〖バル?イ?〖のうちどちらを回年しているか
	if( *buf2 == 'a'){
		settimer = (unsigned int*)&my->wu.npc.GlobalTimerA;
	}
	else if( *buf2== 'b'){
		settimer = (unsigned int*)&my->wu.npc.GlobalTimerB;
	}
	else return FALSE;	// 回年が痰い眷圭はエラ〖を手す

	buf2++;
	//イコ〖ルは粕み若ばし。痰い眷圭は恃构せず
	if( *buf2 == '=') buf2++;

	//グロ〖バル?イ?〖を肋年する
	setnum = (unsigned int)get_num2( &buf2);
	*settimer = setnum;

	//アドレス构糠
	*buf = buf2;

	return TRUE;
}

//-------------------------------------------------------------------------
// オブジェ?ト拎侯
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_object( Char *talker, char **buf)
{
	int i;
	int mapid, floorid, x ,y;
	int obj;
	int *nums[] = {
		&mapid, &floorid, &x, &y, &obj
	};
	char *buf2;

	//アドレスコピ〖
	buf2 = *buf;

	//デ〖?粕み叫し
	for( i = 0; i < arraysizeof( nums); i++){
		*nums[i] = get_num2( &buf2);
		if( i < arraysizeof( nums) - 1){
			if( *buf2 == ',') buf2++;
			else return FALSE;
		}
	}

	//オブジェ?ト肋弥
	MAP_setTileAndObjData( mapid,floorid, x, y, -1, obj);
	//件りの?ライアントに奶梦する
	MAP_sendAroundMapdata( mapid,floorid, x, y);

	//アドレス构糠
	*buf = buf2;

	return TRUE;
}

//-------------------------------------------------------------------------
// NPCの谎を恃える
// 苞眶¨
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL npc_metamo( Char *my, char **buf)
{
	int gid;
	char *buf2;

	buf2 = *buf;

	gid = get_num2( &buf2);

	my->i.BaseImageNumber = my->i.BaseBaseImageNumber = gid;

	CHAR_complianceParameter( my );
	CHAR_sendCToArroundCharacter( my->w.ObjIndex);

	*buf = buf2;

	return TRUE;
}

//-------------------------------------------------------------------------
// ログイン?イントを肋年する
// 苞眶¨
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL set_loginpoint( Char *talker, char **buf)
{
	int p;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// プレイヤ〖のみ
	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}
	// 肌の矢机は眶猛でなければならない
	if( !check_num( buf2)) return FALSE;

	p = get_num2( &buf2);

	talker->iu.player.LoginPoint = p;

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}
#ifdef VERSION_TW
static BOOL set_thankflower( Char *talker, char **buf){
	if(talker->i.WhichType!=CHAR_TYPEPLAYER) return FALSE;
	++talker->i.ThankFlower;
	SYSTEMMSG(talker, TMSG(LANG_MSG_NPC_EVENT_C_016));
	return TRUE;
}
/*-------------------------------------------------------------------------
   随身银行 BY moonboy
   修订记录：
-------------------------------------------------------------------------*/
static BOOL set_portalbank( Char *talker, char **buf)
{
	if(talker->i.WhichType!=CHAR_TYPEPLAYER) return FALSE;
	NPC_BankmanTalked(talker,talker,"",3);
	return TRUE;
}
#endif
//-------------------------------------------------------------------------
// プレイヤ〖のLPを恃步させる
// 苞眶¨
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL setlp_char( Char *talker, char **buf)
{
	BOOL add_flg = FALSE;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// プレイヤ〖のみ
	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}
	if( *buf2 =='+' || *buf2 == '-'){
		add_flg = TRUE;
	}
	// 肌の矢机は眶猛でなければならない
	if( !check_num( buf2)) return FALSE;

	if( add_flg){
		talker->i.Hp += get_num2( &buf2);
	}
	else {
		talker->i.Hp = get_num2( &buf2);
	}

	// 呵络猛と呵井猛をチェッ?する
	if( talker->i.Hp > talker->w.MaxHp){
		talker->i.Hp = talker->w.MaxHp;
	}
	else if( talker->i.Hp < 1){
		talker->i.Hp = 1;
	}

	// 攫鼠流慨
	CHAR_send_CP_String( talker , CHAR_CP_HP );

#ifdef PUK2
	// パ〖ティメンバ〖にも攫鼠を流る
	CHAR_sendPartyParam( talker );
#endif
	// アドレス构糠
	*buf = buf2;

	return TRUE;
}

static BOOL setfp_char( Char *talker, char **buf)
{
	BOOL add_flg = FALSE;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// プレイヤ〖のみ
	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}
	if( *buf2 =='+' || *buf2 == '-'){
		add_flg = TRUE;
	}
	// 肌の矢机は眶猛でなければならない
	if( !check_num( buf2)) return FALSE;

	if( add_flg){
		talker->i.ForcePoint += get_num2( &buf2);
	}
	else {
		talker->i.ForcePoint = get_num2( &buf2);
	}

	// 呵络猛と呵井猛をチェッ?する
	if( talker->i.ForcePoint > talker->w.MaxForcePoint){
		talker->i.ForcePoint = talker->w.MaxForcePoint;
	}
	else if( talker->i.ForcePoint < 1){
		talker->i.ForcePoint = 1;
	}

	// 攫鼠流慨
	CHAR_send_CP_String( talker , CHAR_CP_FORCEPOINT );

#ifdef PUK2
	// パ〖ティメンバ〖にも攫鼠を流る
	CHAR_sendPartyParam( talker );
#endif
	// アドレス构糠
	*buf = buf2;

	return TRUE;
}

#ifdef _USE_STAMP_ITEM
//-------------------------------------------------------------------------
// ス?ンプアイテ?の?ウントを肋年する
// 苞眶¨
// 		*my
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL add_stamp_count( Char *talker, char **buf)
{
	int stampcount;
	int itemid;
	int itemindex;
	int itemplace;
	int addnum = 1;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// アイテ?IDを艰り叫す
	if( !check_num( buf2)) return FALSE;
	itemid = get_num2( &buf2);
	// ?ン?で惰磊られている眷圭は、恃步させる猛が回年されている。
	if( *buf2 == ','){
		buf2++;
		if( !check_num( buf2)) return FALSE;
		addnum = get_num2( &buf2);
	}
	// ?ウントするアイテ?のある眷疥を拇べる
	itemplace = check_haveitemid_place( talker, itemid);
	// 回年されたアイテ?を积っていない眷圭はエラ〖
	if( itemplace < 0){
		return FALSE;
	}
	// アイテ?インデッ?スを艰评する
	itemindex = CHAR_getItemIndex( talker, itemplace);
	// ス?ンプアイテ?かどうか拇べる
	if( ITEM_getInt( itemindex, ITEM_TYPE) != ITEM_STAMP) return FALSE;
	// 附哼のス?ンプ?ウントを艰评する
	stampcount = ITEM_getInt( itemindex, ITEM_VAR2);

	// ス?ンプ?ウントを构糠する
	stampcount += addnum;
	ITEM_setInt( itemindex, ITEM_VAR2, stampcount);
	// アイテ?の侯喇泣箕を构糠する
	ITEM_setInt( itemindex, ITEM_CREATETIME, NowTime.tv_sec);

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}
#endif /* _USE_STAMP_ITEM */

//-------------------------------------------------------------------------
// システ?メッ?〖ジ山绩
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL disp_sysmessage( Char *talker, char **buf )
{
	int mes_id;
	char *buf2,*p_token;
	char moji[1024];

	//アドレスコピ〖
	buf2 = *buf;
	//肌の矢机が眶猛でなければ
	if( check_num( buf2 ) == FALSE ){
		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 2 ]) );
		return FALSE;
	}
	//メッ?〖ジＩＤ艰り叫し
	mes_id = get_num2( &buf2 );
	//アドレス构糠
	*buf = buf2;
	// メッ?〖ジ艰评
	p_token = MSG_getMessage( mes_id );
	//喇根なら
	if( p_token ){
		//叹涟赁掐へ
		Event_insert_name( talker, p_token, moji );
		//メッ?〖ジ流慨
		SYSTEMMSG( talker, moji);
	} else {
		return FALSE;
	}
	return TRUE;
}

//发送公告脚本
#ifdef ANNOUNCE_SCRIPT
//发送全服公告
static BOOL sendmsgtoall( Char *talker, char **buf )
{
	int mes_id,ii;
	char *buf2,*p_token;
	char sendbuf[1024];
	char c;
	int TM_Playernum,TM_iLoop;

	buf2 = *buf;
	if( check_num( buf2 ) == FALSE ){
		if( *buf2 != '"' ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
			return FALSE;
		}
		buf2++;
		ii = 0;
		while( 1 ){
			c = *buf2;
			if( c == '"' || c == 0x0a || c == 0 ){
				sendbuf[ ii ] = 0;
				if( c ){
					buf2++;
				}
				break;
			}
			sendbuf[ ii++ ] = c;
			buf2++;
		}
	}else {
		mes_id = get_num2( &buf2 );
		p_token = MSG_getMessage( mes_id );

		if( p_token ){
			Event_insert_name( talker, p_token, sendbuf );
		} else {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
			return FALSE;
		}
	}

	*buf = buf2;

	TM_Playernum = CHAR_getEnablePlayerMaxNum();
	TM_iLoop = 0;

	for( TM_iLoop = 0 ; TM_iLoop < TM_Playernum ; TM_iLoop++)
	{
		Char* ch = CHAR_getCharPointer(TM_iLoop);
		if(CHAR_CheckCharPointer(ch) && ch->w.State == CHAR_STATE_USABLE )
		{
			SYSTEMMSG(ch, sendbuf);
		}
	}
	
	return TRUE;

}

//发送当前地图公告
static BOOL sendmsgtomap( Char *talker, char **buf )
{
	int mes_id,ii;
	char *buf2,*p_token;
	char sendbuf[1024];
	char c;
	int TM_Playernum,TM_iLoop;
	int MapId,Floor;


	buf2 = *buf;
	if( check_num( buf2 ) == FALSE ){
		if( *buf2 != '"' ){
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
			return FALSE;
		}
		buf2++;
		ii = 0;
		while( 1 ){
			c = *buf2;
			if( c == '"' || c == 0x0a || c == 0 ){
				sendbuf[ ii ] = 0;
				if( c ){
					buf2++;
				}
				break;
			}
			sendbuf[ ii++ ] = c;
			buf2++;
		}
	}else {
		mes_id = get_num2( &buf2 );
		p_token = MSG_getMessage( mes_id );

		if( p_token ){
			Event_insert_name( talker, p_token, sendbuf );
		} else {
			print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 23 ]) );
			return FALSE;
		}
	}

	*buf = buf2;

	TM_Playernum = CHAR_getEnablePlayerMaxNum();
	TM_iLoop = 0;
	MapId = talker->i.MapId;
	Floor = talker->i.Floor;

	for( TM_iLoop = 0 ; TM_iLoop < TM_Playernum ; TM_iLoop++)
	{
		Char* ch = CHAR_getCharPointer(TM_iLoop);
		if(CHAR_CheckCharPointer(ch) && 
			ch->w.State == CHAR_STATE_USABLE && 
			ch->i.MapId == MapId && 
			ch->i.Floor == Floor )
		{
			SYSTEMMSG(ch, sendbuf);
		}
	}

	return TRUE;
}
#endif

#ifdef _ENABLE_ALBUM_ITEMS
//-------------------------------------------------------------------------
// アルバ?フラグ肋年
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL setalbumitemflag( Char *talker, char **buf)
{
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// アルバ?フラグ肋年
	if( !ALBUM_setAlbumItemFlg( talker, &buf2)) return FALSE;

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}
#endif /* _ENABLE_ALBUM_ITEMS */

//-------------------------------------------------------------------------
// パラメ〖?肋年
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL setparameter_char( Char *talker, char **buf )
{
	char flg = 0;
	int paramno;
	int num;
	int *params[] = {
		&talker->i.Hp, &talker->i.ForcePoint
	};
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// どのパラメ〖?を肋年するか回年する
	if( !check_num( buf2)) return FALSE;
	paramno = get_num2( &buf2);
	// 稍赖な戎规が回年されている
	if( ( sizeof( params) / sizeof( params[0])) <= paramno) return FALSE;
	if( *buf2 != ',') return FALSE;
	// 肋年猛を艰评
	if( !check_num( buf2)) return FALSE;
	if( *buf2 == '+' || *buf2 == '-') flg = 1;
	num = get_num2( &buf2);

	// 猛を肋年する
	if( flg) *params[paramno] += num;
	else *params[paramno] = num;

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}

/*
//-------------------------------------------------------------------------
// 缄积ちアイテ?の宝布にあるアイテ?を久殿する
// 苞眶¨
// 		*talker
// 		**buf  コ?ンド矢机误のアドレス
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
static BOOL item_trash( Char *talker, char **buf)
{
	int i = 0;
	int itemindex;
	int p, l;
	char msgbuf[256];
	char randitembuf[1024];
	char *buf2;
	char *buf3;

	buf2 = *buf;

	// 嘉てるアイテ?のIndexを艰评
	itemindex = CHAR_getItemIndex( talker, CHAR_MAXITEMHAVE - 1);
	if( itemindex != -1){
		// メッ?〖ジを流慨
		translate_format(msgbuf, sizeof( msgbuf), LANG_MSG_EVENT_017, ITEM_getAppropriateName( itemindex));
		SYSTEMMSG( talker, msgbuf);
		// アイテ?腕から久殿する
		CHAR_unsetItem( talker, CHAR_MAXITEMHAVE - 1);
		// アイテ?を久殿する
		ITEM_endExistItemsOne(itemindex);
		// 攫鼠を?ライアントに奶梦
		CHAR_sendItemDataOne( talker, CHAR_MAXITEMHAVE - 1);
		// 糠しいアイテ?を畔す澄唯を艰评
		p = get_num2( &buf2);
		// アイテ??ットを网脱するかどうかチェッ?
		if( *buf2 == '('){
			// アイテ??ットを艰评する
			for( i = 0; i < 1024; i++){
				randitembuf[i] = *buf2;
				if( *buf2 == ')') break;
				buf2++;
			}
			buf2++;
			// アイテ??ットのあとに、凑年フラグが?ットされているか
			if( *buf2 == ','){
				buf2++;
				l = get_num2( &buf2);
			}
			else l = 0;		// されていない眷圭は、デフォルトで
			buf3 = randitembuf;
			if( RAND( 0, 100) <= p){
				// アイテ?を?ットする
				set_item_last( talker, &buf3, l);
			}
		}
		// ラン??アイテ?を畔す眷圭
		else if( *buf2 == ','){
			// 凑年貉みにするかどうか回年されていれば
			buf2++;
			l = get_num2( &buf2);
			if( RAND( 0, 100) <= p){
				// そのオプションを回年してアイテ?を栏喇させる
				if( !make_random_item( talker, l)){
					print_tr(LANG_MSG_EVENT_ERR_059);
					return FALSE;
				}
			}
		}
		else {
			// 痰ければデフォルトで
			if( RAND( 0, 100) <= p){
				if( !make_random_item( talker, 0)){
					print_tr(LANG_MSG_EVENT_ERR_059);
					return FALSE;
				}
			}
		}
	}
	else {
		print_tr(LANG_MSG_EVENT_ERR_059);
		return FALSE;
	}
	*buf = buf2;
	return TRUE;
}
*/

//-------------------------------------------------------------------------
// フィ〖バ〖箕粗を恃构する
//-------------------------------------------------------------------------
static BOOL setfeverhavetime( Char *talker, char**buf)
{
	BOOL add_flg = FALSE;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// プレイヤ〖のみ
	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}
	if( *buf2 =='+' || *buf2 == '-'){
		add_flg = TRUE;
	}
	// 肌の矢机は眶猛でなければならない
	if( !check_num( buf2)){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 76 ]) );
		return FALSE;
	}

	if( add_flg){
		if( CHAR_IsFeverTime( talker ) == FALSE ){
			talker->iu.player.FeverHaveTime += get_num2(&buf2);
		}
		else {
			talker->wu.player.FeverEndTime += get_num2(&buf2);
		}
	}
	else {
		if( CHAR_IsFeverTime( talker ) == FALSE ){
			talker->iu.player.FeverHaveTime = get_num2(&buf2);
		}
		else {
			talker->wu.player.FeverEndTime = get_num2(&buf2) + NowTime.tv_sec;
		}
	}

	// 呵络猛と呵井猛をチェッ?する
	if( CHAR_IsFeverTime( talker ) == FALSE ){
		if( talker->iu.player.FeverHaveTime > CHAR_FEVER_MAX){
			talker->iu.player.FeverHaveTime = CHAR_FEVER_MAX;
		}
		else if( talker->iu.player.FeverHaveTime < 0){
			talker->iu.player.FeverHaveTime = 0;
		}
	}
	else {
		if( talker->wu.player.FeverEndTime - NowTime.tv_sec > CHAR_FEVER_MAX){
			talker->wu.player.FeverEndTime = CHAR_FEVER_MAX + NowTime.tv_sec;
		}
		else if( talker->wu.player.FeverEndTime - NowTime.tv_sec < 0){
			talker->wu.player.FeverEndTime = NowTime.tv_sec;
		}
	}
	if( talker->i.Hp > talker->w.MaxHp){
		talker->i.Hp = talker->w.MaxHp;
	}
	else if( talker->i.Hp < 1){
		talker->i.Hp = 1;
	}

	// 攫鼠流慨
	CHAR_send_CP_String( talker , CHAR_CP_HP );
#ifdef PUK2
	// バ〖スト攫鼠も流慨
	CHAR_sendFeverStatus( talker );
#endif

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}

//-------------------------------------------------------------------------
// フィ〖バ〖フラグを恃构する
//-------------------------------------------------------------------------
static BOOL setfeverflg( Char *talker, char **buf)
{
	int p;
	char *buf2;

	// アドレスコピ〖
	buf2 = *buf;

	// プレイヤ〖のみ
	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}
	// 肌の矢机は眶猛でなければならない
	if( !check_num( buf2)){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 77 ]) );
		return FALSE;
	}

	p = get_num2( &buf2);

	if( p ){
		CHAR_FeverTimeStart( talker );
	}
	else {
		CHAR_FeverTimeStop( talker );
	}

	// アドレス构糠
	*buf = buf2;

	return TRUE;
}

#ifdef DB_SCRIPT
static BOOL get_fieldstr(char *dest, char **src) {

    int i, x = 0;
    for (i = 0; i < strlen(*src); i++) {
        if (*(*src + i) == ',') {
            x = 1;
            break;
        } else if (*(*src + i) == '=' || *(*src + i) == '+' || *(*src + i) == '-' || *(*src + i) == '*' || *(*src + i) == '/' || *(*src + i) == '!' || *(*src + i) == '>' || *(*src + i) == '<')
            break;
    }
    if (i == strlen(*src))
        return FALSE;
    else {
        _STRNCPYSAFE(dest, *src, i + 1);
        if (x == 0)
            *src += i;
        else
            *src += i + 1;
        return TRUE;
    }
}

static BOOL get_operstr(char *dest, char **src)
{
	if(**src == '=' || **src == '+' || **src == '-' || **src == '*' || **src == '/' || **src == '!' || **src == '>' || **src == '<'){
		int i = 0;
		if(*(*src+1) != '=') {
            i = 1;
            dest[0] = **src;
        }
		else {
            i = 2;
            dest[0] = **src;
            dest[1] = *(*src+1);
        }
		*src+=i;
        dest[i] = 0;
		return TRUE;
	}
	else
		return FALSE;

}

static BOOL getplayerint(Char *ch, char *fieldname, int *value)
{
	int i;
	for(i=0;i<sizeof(playerstr)/sizeof(playerstr[0]);i++){
		if( strcmp( fieldname, playerstr[i]) == 0 ){
			*value = *((int*)ch+i+3);
			return TRUE;
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	return FALSE;
}




static BOOL setplayerint(Char *ch, char *fieldname, int *value)
{
 	int i;
	for(i=0;i<sizeof(playerstr)/sizeof(playerstr[0]);i++){
		if( strcmp( fieldname, playerstr[i]) == 0 ){
			*((int*)ch+i+3) = *value;
			return TRUE;
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	return FALSE;

}

static BOOL getpetint(Char *ch, char *fieldname, int *value)
{
	int i;
	for(i=0;i<sizeof(petstr)/sizeof(petstr[0]);i++){
		if( strcmp( fieldname, petstr[i]) == 0 ){
			*value = *((int*)ch+i+3);
			return TRUE;
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	return FALSE;
}




static BOOL setpetint(Char *ch, char *fieldname, int *value)
{
 	int i;
	for(i=0;i<sizeof(petstr)/sizeof(petstr[0]);i++){
		if( strcmp( fieldname, petstr[i]) == 0 ){
			*((int*)ch+i+3) = *value;
			return TRUE;
		}
	}
	print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	return FALSE;

}

static BOOL getitemint(Char *ch, char *fieldname, int *value)
{
	int i,itemindex;
	itemindex = CHAR_getItemIndex( ch, 8);
	if( itemindex < 0){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_002));
		return FALSE;

	}
	else{
		for(i=0;i<sizeof(itemstr)/sizeof(itemstr[0]);i++){
			if( strcmp( fieldname, itemstr[i]) == 0 ){
				*value = ITEM_getInt( itemindex, i);
				return TRUE;
			}
		}
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
		return FALSE;
	}
}

static BOOL setitemint(Char *ch, char *fieldname, int *value)
{
 	int i,itemindex;
	itemindex = CHAR_getItemIndex( ch, 8);
	if( itemindex < 0){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_002));
		return FALSE;

	}
	else{
		for(i=0;i<sizeof(itemstr)/sizeof(itemstr[0]);i++){
			if( strcmp( fieldname, itemstr[i]) == 0 ){
				ITEM_setInt( itemindex, i, *value); ;
				return TRUE;
			}
		}
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
		return FALSE;
	}
}

//-------------------------------------------------------------------------
// 万能脚本 setplayer
//-------------------------------------------------------------------------
static BOOL setplayer( Char *talker, char **buf)
{

	char *buf2;
	buf2 = *buf;
	
	char fields[20]={0};
	char oper[5]={0};
	int limit,newvalue,oldvalue,ret=0;

	if( talker->i.WhichType != CHAR_TYPEPLAYER){
		return FALSE;
	}

	if(!get_fieldstr(fields,&buf2)){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
		return FALSE;
	}


	if( check_num( buf2))
		limit = get_num2( &buf2);
	else
		limit = -1;

	
	if(!get_operstr(oper,&buf2)){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
		return FALSE;
	}
		

	if( !check_num( buf2)){
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
		return FALSE;
	}

	newvalue = get_num2( &buf2);
	if(!getplayerint(talker,fields,&oldvalue))
		return FALSE;
	

	if( strncmp2( oper, "=", 1 ) == 0 ){
		if(limit < 0){
			ret = setplayerint(talker,fields,&newvalue);
		}
		else if(limit == 0){
			newvalue = rand() % newvalue;
			ret = setplayerint(talker,fields,&newvalue);
		}else{
			if(newvalue > limit)
				newvalue = limit;
			ret = setplayerint(talker,fields,&newvalue);
		}
	}
	else if( strncmp2( oper, "+=", 2 ) == 0 ){
		if(limit < 0){
			newvalue += oldvalue;
			ret = setplayerint(talker,fields,&newvalue );
		}
		else if(limit == 0){
			newvalue = rand() % newvalue;
			newvalue += oldvalue;
			ret = setplayerint(talker,fields,&newvalue );
		}else{
			newvalue += oldvalue;
			if(newvalue > limit)
				ret = setplayerint(talker,fields,&limit);
			else
				ret = setplayerint(talker,fields,&newvalue );
		}
	}
	else if( strncmp2( oper, "-=", 2 ) == 0 ){
		if(limit < 0){
			newvalue = oldvalue - newvalue;
			ret = setplayerint(talker,fields,&newvalue);
		}
		else if(limit == 0){
			newvalue = rand() % newvalue;
			newvalue = oldvalue - newvalue;
			ret = setplayerint(talker,fields,&newvalue );
		}else{
			newvalue = oldvalue - newvalue;
			if(newvalue > limit)
				ret = setplayerint(talker,fields,&limit);
			else
				ret = setplayerint(talker,fields,&newvalue );
		}
	}
	else if( strncmp2( oper, "*=", 2 ) == 0 ){
		if(limit < 0){
			newvalue *= oldvalue;
			ret = setplayerint(talker,fields,&newvalue);
		}
		else if(limit == 0){
			newvalue = rand() % newvalue;
			newvalue *=oldvalue;
			ret = setplayerint(talker,fields,&newvalue );
		}else{
			newvalue *=oldvalue;
			if(newvalue > limit)
				ret = setplayerint(talker,fields,&limit);
			else			
				ret = setplayerint(talker,fields,&newvalue );
		}
	}
	else if( strncmp2( oper, "/=", 2 ) == 0 ){
		if(limit < 0){
			newvalue /= oldvalue;
			ret = setplayerint(talker,fields,&newvalue);
		}
		else if(limit == 0){
			newvalue = rand() % newvalue;
			newvalue = oldvalue / newvalue;
			ret = setplayerint(talker,fields,&newvalue );
		}else{
			newvalue = oldvalue / newvalue;
			if(newvalue > limit)
				ret = setplayerint(talker,fields,&limit);
			else
				ret = setplayerint(talker,fields,&newvalue );
		}
	}else{
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_004) );
		return FALSE;
	}

	if(ret != 0){
		*buf = buf2;

		CHAR_Levelupsend(talker);
		CHAR_complianceParameter( talker );
		CHAR_sendJobsData( talker);
		CHAR_send_CP_String( talker, CHAR_CP_ALL);
		CHAR_send_CP2_String( talker, CHAR_CP2_ALL);
		CHAR_sendPartyParam(talker);
		CHAR_sendAlbumFlgData(talker);
		CHAR_sendCToArroundCharacter( talker->w.ObjIndex);

		/*
		if(CHAR_LevelUpCheck(talker))
			CHAR_Levelupsend(talker);
		CHAR_complianceParameter(TM_Ptr);
		CHAR_complianceTech(TM_Ptr,1);
		CHAR_send_CP_String(TM_Ptr,0xFFFFFFFF);
		CHAR_send_CP2_String(TM_Ptr,0xFFFFFFFF);
		CHAR_sendPartyParam(TM_Ptr);
		CHAR_sendAlbumFlgData(TM_Ptr);
		CHAR_sendJobsData(TM_Ptr);
		CHAR_sendSkillDataAll(TM_Ptr);
		CHAR_sendTitle(TM_Ptr);
		CHAR_sendFeverStatus(TM_Ptr);
		CHAR_sendInjuryEffect(TM_Ptr);
		*/
		return TRUE;
	}
	else{
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_005) );
		return FALSE;
	}
}


static BOOL setpet( Char *talker, char **buf)
{
  char *buf2;
  Char *pch = NULL;
  buf2 = *buf;


  char fields[20]={0};
  char oper[5]={0};
  int limit,newvalue,oldvalue,ret=0;

  pch = talker->player_addon->PetPointer[ 0 ];

  if( CHAR_CheckCharPointer( pch ) != TRUE ){
		SYSTEMMSG( talker, TMSG(LANG_MSG_NPC_EVENT_C_006));
		return FALSE;
  }

  if(!get_fieldstr(fields,&buf2)){
  	print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
    return FALSE;
  }

  if( check_num( buf2))
	  limit = get_num2( &buf2);
  else
	  limit = -1;


  if(!get_operstr(oper,&buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
    return FALSE;
  }
    
  if( !check_num( buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
    return FALSE;
  }

  newvalue = get_num2( &buf2);
  if(!getpetint(pch,fields,&oldvalue))
    return FALSE;

  if( strncmp2( oper, "=", 1 ) == 0 ){
    if(limit < 0){
      ret = setpetint(pch,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      ret = setpetint(pch,fields,&newvalue);
    }else{
      if(newvalue > limit)
        newvalue = limit;
      ret = setpetint(pch,fields,&newvalue);
    }
  }
  else if( strncmp2( oper, "+=", 2 ) == 0 ){
    if(limit < 0){
      newvalue += oldvalue;
      ret = setpetint(pch,fields,&newvalue );
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue += oldvalue;
      ret = setpetint(pch,fields,&newvalue );
    }else{
      newvalue += oldvalue;
      if(newvalue > limit)
        ret = setpetint(pch,fields,&limit);
      else
        ret = setpetint(pch,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "-=", 2 ) == 0 ){
    if(limit < 0){
      newvalue = oldvalue - newvalue;
      ret = setpetint(pch,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue = oldvalue - newvalue;
      ret = setpetint(pch,fields,&newvalue );
    }else{
      newvalue = oldvalue - newvalue;
      if(newvalue > limit)
        ret = setpetint(pch,fields,&limit);
      else
        ret = setpetint(pch,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "*=", 2 ) == 0 ){
    if(limit < 0){
      newvalue *= oldvalue;
      ret = setpetint(pch,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue *=oldvalue;
      ret = setpetint(pch,fields,&newvalue );
    }else{
      newvalue *=oldvalue;
      if(newvalue > limit)
        ret = setpetint(pch,fields,&limit);
      else      
        ret = setpetint(pch,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "/=", 2 ) == 0 ){
    if(limit < 0){
      newvalue /= oldvalue;
      ret = setpetint(pch,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue = oldvalue / newvalue;
      ret = setpetint(pch,fields,&newvalue );
    }else{
      newvalue = oldvalue / newvalue;
      if(newvalue > limit)
        ret = setpetint(pch,fields,&limit);
      else
        ret = setpetint(pch,fields,&newvalue );
    }
  }else{
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_007) );
    return FALSE;
  }

  if(ret != 0){
    *buf = buf2;

	CHAR_PetLevelupsend( talker, 0, TRUE);	  
	CHAR_complianceParameter( pch );
	CHAR_sendCToArroundCharacter( talker->w.ObjIndex );
	CHAR_send_KP_String( talker, 0, CHAR_KP_ALL);
	CHAR_send_KP2_String( talker, 0, CHAR_KP2_ALL);
	CHAR_sendPetTechData( talker, 0);
	//CHAR_endCharOneArray( pch );  

    return TRUE;
  }
  else{
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_008) );
    return FALSE;
  }


}

static BOOL setitem( Char *talker, char **buf)
{
  char *buf2;
  buf2 = *buf;
  
  char fields[20]={0};
  char oper[5]={0};
  int limit,newvalue,oldvalue,ret=0;
  

  if( CHAR_getItemIndex( talker, 8) < 0){
	 SYSTEMMSG( talker, TMSG(LANG_MSG_NPC_EVENT_C_009));
	 return FALSE;
  }

  if(!get_fieldstr(fields,&buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
    return FALSE;
  }


  if( check_num( buf2))
	  limit = get_num2( &buf2);
  else
	  limit = -1;

  if(!get_operstr(oper,&buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
	  return FALSE;
  }

  if( !check_num( buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
    return FALSE;
  }

  newvalue = get_num2( &buf2);
  if(!getitemint(talker,fields,&oldvalue))
    return FALSE;
  

  if( strncmp2( oper, "=", 1 ) == 0 ){
    if(limit < 0){
      ret = setitemint(talker,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      ret = setitemint(talker,fields,&newvalue);
    }else{
      if(newvalue > limit)
        newvalue = limit;
      ret = setitemint(talker,fields,&newvalue);
    }
  }
  else if( strncmp2( oper, "+=", 2 ) == 0 ){
    if(limit < 0){
      newvalue += oldvalue;
      ret = setitemint(talker,fields,&newvalue );
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue += oldvalue;
      ret = setitemint(talker,fields,&newvalue );
    }else{
      newvalue += oldvalue;
      if(newvalue > limit)
        ret = setitemint(talker,fields,&limit);
      else
        ret = setitemint(talker,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "-=", 2 ) == 0 ){
    if(limit < 0){
      newvalue = oldvalue - newvalue;
      ret = setitemint(talker,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue = oldvalue - newvalue;
      ret = setitemint(talker,fields,&newvalue );
    }else{
      newvalue = oldvalue - newvalue;
      if(newvalue > limit)
        ret = setitemint(talker,fields,&limit);
      else
        ret = setitemint(talker,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "*=", 2 ) == 0 ){
    if(limit < 0){
      newvalue *= oldvalue;
      ret = setitemint(talker,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue *=oldvalue;
      ret = setitemint(talker,fields,&newvalue );
    }else{
      newvalue *=oldvalue;
      if(newvalue > limit)
        ret = setitemint(talker,fields,&limit);
      else      
        ret = setitemint(talker,fields,&newvalue );
    }
  }
  else if( strncmp2( oper, "/=", 2 ) == 0 ){
    if(limit < 0){
      newvalue /= oldvalue;
      ret = setitemint(talker,fields,&newvalue);
    }
    else if(limit == 0){
      newvalue = rand() % newvalue;
      newvalue = oldvalue / newvalue;
      ret = setitemint(talker,fields,&newvalue );
    }else{
      newvalue = oldvalue / newvalue;
      if(newvalue > limit)
        ret = setitemint(talker,fields,&limit);
      else
        ret = setitemint(talker,fields,&newvalue );
    }
  }else{
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_010) );
    return FALSE;
  }

  if(ret != 0){
    *buf = buf2;

  CHAR_sendItemDataOne( talker, 8 );
  CHAR_complianceParameter( talker );
  CHAR_send_CP_String( talker, CHAR_CP_ALL);
  CHAR_send_CP2_String( talker, CHAR_CP2_ALL);
  CHAR_sendCToArroundCharacter( talker->w.ObjIndex);


    return TRUE;
  }
  else{
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_011) );
    return FALSE;
  }
}

static BOOL getplayer( Char *talker, char **buf)
{

  char *buf2;
  buf2 = *buf;
  
  char fields[20]={0};
  char oper[5]={0};
  int newvalue,oldvalue,ret=0;


  if( talker->i.WhichType != CHAR_TYPEPLAYER){
    return FALSE;
  }

  if(!get_fieldstr(fields,&buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
    return FALSE;
  }
  
  if(!get_operstr(oper,&buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
    return FALSE;
  }
    

  if( !check_num( buf2)){
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
    return FALSE;
  }

  newvalue = get_num2( &buf2);
  if(!getplayerint(talker,fields,&oldvalue))
    return FALSE;


  if( strncmp2( oper, "==", 2 ) == 0 ){
    if(oldvalue == newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }
  else if( strncmp2( oper, "!=", 2 ) == 0 ){
    if(oldvalue != newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }
  else if( strncmp2( oper, ">=", 2 ) == 0 ){
    if(oldvalue >= newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }
  else if( strncmp2( oper, "<=", 2 ) == 0 ){
    if(oldvalue <= newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }
  else if( strncmp2( oper, ">", 1 ) == 0 ){
    if(oldvalue > newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }
  else if( strncmp2( oper, "<", 1 ) == 0 ){
    if(oldvalue < newvalue)
      ret = TRUE;
    else
      ret = FALSE;
  }

  else{
    print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_012) );
    ret = FALSE;
  }
  
  *buf = buf2;
  if(ret == TRUE)
    return TRUE;
  else
    return FALSE;
}


static BOOL getpet( Char *talker, char **buf)
{
	char *buf2;
	Char *pch = NULL;
	buf2 = *buf;
	
	char fields[20]={0};
	char oper[5]={0};
	int newvalue,oldvalue,ret=0;
	
	pch = talker->player_addon->PetPointer[ 0 ];
	
	if( CHAR_CheckCharPointer( pch ) != TRUE ){
		  SYSTEMMSG( talker, TMSG(LANG_MSG_NPC_EVENT_C_006));
		  return FALSE;
	}
	
	if(!get_fieldstr(fields,&buf2)){
	 print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	  return FALSE;
	}
	
	if(!get_operstr(oper,&buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
	  return FALSE;
	}
	  
	
	if( !check_num( buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
	  return FALSE;
	}
	
	newvalue = get_num2( &buf2);
	if(!getpetint(pch,fields,&oldvalue))
	  return FALSE;
	
	
	if( strncmp2( oper, "==", 2 ) == 0 ){
	  if(oldvalue == newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "!=", 2 ) == 0 ){
	  if(oldvalue != newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, ">=", 2 ) == 0 ){
	  if(oldvalue >= newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "<=", 2 ) == 0 ){
	  if(oldvalue <= newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, ">", 1 ) == 0 ){
	  if(oldvalue > newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "<", 1 ) == 0 ){
	  if(oldvalue < newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	
	else{
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_013) );
	  ret = FALSE;
	}
	
	*buf = buf2;
	if(ret == TRUE)
	  return TRUE;
	else
	  return FALSE;

}

static BOOL getitem( Char *talker, char **buf)
{
	char *buf2;
	buf2 = *buf;
	
	char fields[20]={0};
	char oper[5]={0};
	int newvalue,oldvalue,ret=0;

	
	if( CHAR_getItemIndex( talker, 8) < 0){
	   SYSTEMMSG( talker, TMSG(LANG_MSG_NPC_EVENT_C_009));
	   return FALSE;
	}
	
	if(!get_fieldstr(fields,&buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_001));
	  return FALSE;
	}
	
	if(!get_operstr(oper,&buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_015));
	  return FALSE;
	}
	  
	
	if( !check_num( buf2)){
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_003) );
	  return FALSE;
	}
	
	newvalue = get_num2( &buf2);
	if(!getitemint(talker,fields,&oldvalue))
	  return FALSE;
	
	
	if( strncmp2( oper, "==", 2 ) == 0 ){
	  if(oldvalue == newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "!=", 2 ) == 0 ){
	  if(oldvalue != newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, ">=", 2 ) == 0 ){
	  if(oldvalue >= newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "<=", 2 ) == 0 ){
	  if(oldvalue <= newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, ">", 1 ) == 0 ){
	  if(oldvalue > newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	else if( strncmp2( oper, "<", 1 ) == 0 ){
	  if(oldvalue < newvalue)
		ret = TRUE;
	  else
		ret = FALSE;
	}
	
	else{
	  print( "\n%s:%d %s\n", file_name, read_line, TMSG(LANG_MSG_NPC_EVENT_C_014) );
	  ret = FALSE;
	}
	
	*buf = buf2;
	if(ret == TRUE)
	  return TRUE;
	else
	  return FALSE;



}


#endif
//aaa
//-------------------------------------------------------------------------
// コ?ンド豺老
// 苞眶¨
// 		*buf  コ?ンド矢机误
// 提り猛¨
// 		喇根=TRUE  己窃=FALSE
//-------------------------------------------------------------------------
#ifdef PUK2
static BOOL analyse_command ( Char *talker, Char *my, char *buf, char *msg, int window_flg, FILE* fp, char* sendData)
#else
static BOOL analyse_command( Char *talker, Char *my, char *buf )
#endif
{
	int ret;

	//１乖尸借妄する
	while( 1 ){
		//姜位なら
		if( *buf == 0 ){
			break;
		}

        if (strncmp2(buf, "luac", 4) == 0) {
            int n = EmitScriptCallBlock(my, talker, buf, msg);
            buf += n;
            continue;
        }
		//メッ?〖ジ山绩なら
		if( strncmp2( buf, "message", 7 ) == 0 ){
			//バッファ渴める
			buf += 7;
			//メッ?〖ジ山绩
			ret = disp_mes( talker, my, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//ナウイ?ントフラグ?ットなら
		if( strncmp2( buf, "nowevent", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//イ?ントフラグ?ット
			ret = set_event_flg( talker, &buf, 0 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//エントイ?ントフラグ?ットなら
		if( strncmp2( buf, "endevent", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//イ?ントフラグ?ット
			ret = set_event_flg( talker, &buf, 1 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
#if 0
		//アイテ?畔すなら
		if( strncmp2( buf, "give2item", 9 ) == 0 ){
			//バッファ渴める
			buf += 9;
			//アイテ??ット
			ret = set_item2( talker, &buf, 0 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年涟のアイテ?畔すなら
		if( strncmp2( buf, "give2leak0item", 14 ) == 0 ){
			//バッファ渴める
			buf += 14;
			//アイテ??ット
			ret = set_item2( talker, &buf, 1 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年稿のアイテ?畔すなら
		if( strncmp2( buf, "give2leak1item", 14 ) == 0 ){
			//バッファ渴める
			buf += 14;
			//アイテ??ット
			ret = set_item2( talker, &buf, 2 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
#endif
		//アイテ?畔すなら
#ifdef PUK2
		if (strncmp2 (buf, "giveitemex", 10) == 0) {
			buf += 10;
			return giveItemEx (talker, my, &buf);
		}
#endif
		if( strncmp2( buf, "giveitem", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//アイテ??ット
			ret = set_item( talker, &buf, 0 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年涟のアイテ?畔すなら
		if( strncmp2( buf, "giveleak0item", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//アイテ??ット
			ret = set_item( talker, &buf, 1 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年稿のアイテ?畔すなら
		if( strncmp2( buf, "giveleak1item", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//アイテ??ット
			ret = set_item( talker, &buf, 2 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
#ifdef ITEM_LIMITTIME
		if( strncmp2( buf, "givetimeitem", 12 ) == 0 ){
			buf += 12;
			ret = givetimeitem( talker, &buf );
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
#endif
		//积っているアイテ?链て久すなら
		if( strncmp2( buf, "killitemall", 11 ) == 0 ){
			//バッファ渴める
			buf += 11;
			//アイテ?猴近
			ret = kill_item_all( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//アイテ?氓うなら
		if( strncmp2( buf, "killitem", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//アイテ?猴近
			ret = kill_item( talker, &buf, 0 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年涟のアイテ?氓うなら
		if( strncmp2( buf, "killleak0item", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//アイテ?猴近
			ret = kill_item( talker, &buf, 1 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//凑年稿のアイテ?氓うなら
		if( strncmp2( buf, "killleak1item", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//アイテ?猴近
			ret = kill_item( talker, &buf, 2 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//レ?ル■疥积垛笼负なら
		if( strncmp2( buf, "addgoldlevel", 12 ) == 0 ){
			//バッファ渴める
			buf += 12;
			//レ?ル■疥积垛笼负
			ret = add_gold_level( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//疥积垛笼负なら
		if( strncmp2( buf, "addgold", 7 ) == 0 ){
			//バッファ渴める
			buf += 7;
			//疥积垛笼负
			ret = add_gold( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//レ?ル恃构なら
		if( strncmp2( buf, "setlevel", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//疥积垛笼负
			ret = set_level( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//ワ〖プなら
		if( strncmp2( buf, "warp", 4 ) == 0 ){
			//バッファ渴める
			buf += 4;
			//ワ〖プする
			ret = warp( talker, &buf );
#ifdef PUK2
// char/char.c:CHAR_sendWatchEventでsendするCAの郝筛攫鼠を动扩弄に
// warp稿のものに今き垂えてやる
			regenerateSendData (talker, sendData);
#endif
#ifndef PUK2
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
#else
// warp稿いろいろtroubleを栏むのでscriptは动扩姜位
			return FALSE;
#endif

		}
		//?ット侯喇なら
		if( strncmp2( buf, "givepet", 7 ) == 0 ){
			//バッファ渴める
			buf += 7;
			//?ット畔す
			ret = make_pet( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//レ?ル笆惧の?ット猴近なら
		if( strncmp2( buf, "killpeth", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//?ット缩沪する
			ret = kill_pet_h( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//レ?ル笆布の?ット猴近なら
		if( strncmp2( buf, "killpetl", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//?ット缩沪する
			ret = kill_pet_l( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//ウインドウ山绩なら
		if( strncmp2( buf, "window", 6 ) == 0 ){
			//バッファ渴める
			buf += 6;
			//绕脱ウインド山绩
			ret = disp_window( talker, my, &buf, 0 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			//ウインドウ山绩で借妄虑ち磊り
			return FALSE;
		}
		//姜位ウインドウ山绩なら
		if( strncmp2( buf, "endwindow", 9 ) == 0 ){
			//バッファ渴める
			buf += 9;
			//绕脱ウインド山绩
			ret = disp_window( talker, my, &buf, 1 );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
#ifdef GO_SUB
			if( talker->wu.player.go_cnt > 0 )
			{
				if(talker->wu.player.gosub_flg[talker->wu.player.go_cnt-1] == TRUE )
				return TRUE;
			}
#endif	/* GO_SUB */
			return FALSE;
		}
		//ス?ッ?アイテ?畔すなら
		if( strncmp2( buf, "givestackitem", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//アイテ??ット
			ret = set_stack_item( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//胎蜗恃构なら
		if( strncmp2( buf, "setcharm", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//胎蜗笼负
			ret = set_charm( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//卵底刨恃构なら
		if( strncmp2( buf, "setdur", 6 ) == 0 ){
			//バッファ渴める
			buf += 6;
			//卵底刨笼负
			ret = set_dur( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//丸狄眶构糠なら
		if( strncmp2( buf, "setvisitor", 10 ) == 0 ){
			//バッファ渴める
			buf += 10;
			//丸狄眶笼负
			ret = set_visitor( my, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//パ〖ティ〖豺欢なら
		if( strncmp2( buf, "leavegroup", 10 ) == 0 ){
			//バッファ渴める
			buf += 10;
			//パ〖ティ〖なら
			if( talker->wu.player.PartyMode != CHAR_PARTY_NONE ){
				//パ〖ティから却ける
				ret = CHAR_DischargeParty( talker );
				//エラ〖なら
				if( ret == FALSE ){
					return FALSE;
				}
			}
			continue;
		}
		//パレットチェンジなら
		if( strncmp2( buf, "changepal", 9 ) == 0 ){
			//バッファ渴める
			buf += 9;
			//パレットチェンジ?ット
			ret = send_palette( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//挑フ〖ド久すなら
		if( strncmp2( buf, "killdrugdish", 12 ) == 0 ){
			//バッファ渴める
			buf += 12;
			//挑フ〖ド猴近
			ret = kill_drugitem( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//?イトルチェッ?なら
		if( strncmp2( buf, "checktitle", 10 ) == 0 ){
			//バッファ渴める
			buf += 10;
			//疚规チェッ?
			ret = TITLE_TitleCheck( talker, TITLE_CHECK_ALL );
			//喇根なら
			if( ret ) {
				//?ライアントに流慨
				CHAR_sendTitle( talker );
			}
			continue;
		}
		//エン?ウントなら
		if( strncmp2( buf, "encount", 7 ) == 0 ){
			//バッファ渴める
			buf += 7;
			//エン?ウント倡幌
			ret = encount_event_battle( my, talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//レシピ畔すなら
		if( strncmp2( buf, "giverecipe", 10 ) == 0 ){
			//バッファ渴める
			buf += 10;
			//レシピ畔す
			ret = give_recipe( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//?ンション袋嘎恃构なら
		if( strncmp2( buf, "sethouselimit", 13 ) == 0 ){
			//バッファ渴める
			buf += 13;
			//?ンション袋嘎恃构
			ret = set_houselimit( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//?ンション恃构なら
		if( strncmp2( buf, "sethouse", 8 ) == 0 ){
			//バッファ渴める
			buf += 8;
			//?ンション
			ret = set_house( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//笨恃构なら
		if( strncmp2( buf, "addluck", 7 ) == 0 ){
			//バッファ渴める
			buf += 7;
			//笨恃构
			ret = set_luck( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//ア?ション恃构なら
		if( strncmp2( buf, "setaction", 9 ) == 0 ){
			//バッファ渴める
			buf += 9;
			//ア?ション?ット
			ret = set_action( talker, my, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//隶から惯りるなら
		if( strncmp2( buf, "shipchardown", 12 ) == 0 ){
			//バッファ渴める
			buf += 12;
			//隶から惯りる
			ret = ship_char_down( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//隶に捐るなら
		if( strncmp2( buf, "shipchartake", 12 ) == 0 ){
			//バッファ渴める
			buf += 12;
			//隶に捐る
			ret = ship_char_take( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//跟蔡不浩栏なら
		if( strncmp2( buf, "playse", 6 ) == 0 ){
			//バッファ渴める
			buf += 6;
			//隶に捐る
			ret = play_se( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
/*		//アイテ?を嘉てるのなら
		if( strncmp2( buf, "itemtrash", 9) == 0){
			//バッファ渴める
			buf += 9;
			//アイテ?を嘉てる
			ret = item_trash( talker, &buf );
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}*/
		//グロ〖バル?イ?〖肋年なら
		if( strncmp2( buf, "globaltimer", 11) == 0){
			//バッファ渴める
			buf += 11;
			//グロ〖バル?イ?〖を肋年する
			ret = SetGlobalTimer( my, &buf);
			//エラ〖なら
			if( ret == FALSE ){
				return FALSE;
			}
			continue;
		}
		//オブジェ?ト拎侯なら
		if( strncmp2( buf, "setobject", 9) == 0){
			//バッファ渴める
			buf += 9;
			//オブジェ?トを拎侯する
			ret = set_object( my, &buf);
			//エラ〖なら
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		//NPC恃咳なら
		if( strncmp2( buf, "metamo", 6) == 0){
			//バッファ渴める
			buf += 6;
			//恃咳させる
			ret = npc_metamo( my, &buf);
			//エラ〖なら
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		//ログイン?イント肋年なら
		if( strncmp2( buf, "setloginpoint", 13) == 0){
			buf += 13;
			//ログイン?イントを肋年する
			ret = set_loginpoint( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
#ifdef VERSION_TW
		//暂时不清楚 好像是宠物好感度
		if( strncmp2( buf, "set_thankflower", 10) == 0){
			buf += 10;
			ret = set_thankflower( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		//随身银行
		if( strncmp2( buf, "portalbank", 10) == 0){
			buf += 10;
			ret = set_portalbank( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
#endif
		//LP肋年なら
		if( strncmp2( buf, "setlp", 5) == 0){
			buf += 5;
			//LPを肋年する
			ret = setlp_char( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		if( strncmp2( buf, "setfp", 5) == 0){
			buf += 5;
			//LPを肋年する
			ret = setfp_char( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}

#ifdef _USE_STAMP_ITEM
		//ス?ンプ?ウント肋年なら
		if( strncmp2( buf, "addstampcount", 13) == 0){
			buf += 13;
			// ス?ンプ?ウントを肋年する
			ret = add_stamp_count( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
#endif /* _USE_STAMP_ITEM */
		//システ?メッ?〖ジ山绩なら
		if( strncmp2( buf, "systemmessage", 13) == 0){
			buf += 13;
			// システ?メッ?〖ジを山绩する
			ret = disp_sysmessage( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		//发送公告脚本
#ifdef ANNOUNCE_SCRIPT
		if( strncmp2( buf, "sendmsgtoall", 12) == 0){
			buf += 12;
			ret = sendmsgtoall( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		if( strncmp2( buf, "sendmsgtomap", 12) == 0){
			buf += 12;
			ret = sendmsgtomap( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}

#endif

		//パラメ〖?肋年なら
		if( strncmp2( buf, "setparameter", 12) == 0){
			buf += 12;
			// パラメ〖?を肋年する
			ret = setparameter_char( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
#ifdef _ENABLE_ALBUM_ITEMS
		//アルバ?アイテ?フラグ肋年なら
		if( strncmp2( buf, "setalbumitemflg", 15) == 0){
			buf += 15;
			// アルバ?アイテ?フラグを肋年する
			ret = setalbumitemflag( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
#endif /* _ENABLE_ALBUM_ITEMS */
		if( strncmp2( buf, "addfeversec", 11) == 0){
			buf += 11;
			// フィ〖バ〖箕粗を恃构する
			ret = setfeverhavetime( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}
		if( strncmp2( buf, "setfeverflg", 11) == 0){
			buf += 11;
			// フィ〖バ〖フラグを肋年する
			ret = setfeverflg( talker, &buf);
			if( ret == FALSE){
				return FALSE;
			}
			continue;
		}

#ifdef DB_SCRIPT
		if( strncmp2( buf, "setplayer", 9) == 0){
			buf += 9;
			ret = setplayer( talker, &buf);
			if( ret == FALSE)
				return FALSE;
			continue;
		}
		if( strncmp2( buf, "setpet", 6) == 0){
			buf += 6;
			ret = setpet( talker, &buf);
			if( ret == FALSE)
				return FALSE;
			continue;
		}
		if( strncmp2( buf, "setitem", 7) == 0){
			buf += 7;
			ret = setitem( talker, &buf);
			if( ret == FALSE)
				return FALSE;
			continue;
		}
#endif
#ifdef PUK2
		if (strncmp2 (buf, "globalflag", 10) == 0) {
			buf += 10;
			if (!setFlagEx (talker, &buf, 0))
				return FALSE;
			continue;
		}
		if (strncmp2 (buf, "localflag", 9) == 0) {
			buf += 9;
			if (!setFlagEx (talker, &buf, 1))
				return FALSE;
			continue;
		}

		if (strncmp2 (buf, "localcount", 10) == 0) {
			buf += 10;
			if (!setLocalCounter (my, talker, &buf, msg))
				return FALSE;
			continue;
		}
		if (strncmp2 (buf, "if", 2) == 0) {
#ifdef MODIFY_COMMAND1
			enum{
				false,
				true,
				error
			};
			int ret=0;
			buf += 2;
			ret = analyse_if (my, talker, &buf, msg, window_flg, fp);
			if( ret == false || ret == error ) return FALSE;
			break;
#else
			buf += 2;
			analyse_if (my, talker, &buf, msg, window_flg, fp);
			break;
#endif	/* MODIFY_COMMAND1 */
		}
		if (strncmp2 (buf, "else", 4) == 0) {
#ifdef MODIFY_COMMAND1
			int ret=0;
			buf += 4;
			ret = skipToNextBlock (fp);
			if( ret == FALSE ) return FALSE;
			break;
#else
			buf += 4;
			skipToNextBlock (fp);
			break;
#endif	/* MODIFY_COMMAND1 */
		}
		if (strncmp2 (buf, "endif", 5) == 0) {
			buf += 5;
			break;
		}
		if (strncmp2 (buf, "shapeon", 7) == 0) {
			buf += 7;
			shapeOn (my, talker, &buf);
			continue;
		}
		if (strncmp2 (buf, "shapeoff", 8) == 0) {
			buf += 8;
			shapeOff (my, talker, &buf);
			continue;
		}
		if (strncmp2 (buf, "shapenext", 9) == 0) {
			buf += 9;
			shapeNext (my, talker, &buf);
			continue;
		}
		if (strncmp2 (buf, "moveto", 6) == 0) {
			buf += 6;
			moveTo (my, talker, &buf);
			continue;
		}
		if (strncmp2 (buf, "endblock", 8) == 0)  
			return FALSE;
		
#endif
#ifdef GOTOCOMMAND
		if (strncmp2 (buf, "gotof", 5) == 0){
			buf += 5;
			gotoF( fp, buf, talker );
			break;
		}	
		if (strncmp2 (buf, "gotob", 5) == 0){
			buf += 5;
			gotoB( fp, buf, talker );
			break;
		}
		// ラ?ル离咐なら
		if ( buf[0] == '*' && buf[strlen(buf)-1] == '*' ){
			buf += strlen(buf); 
			break;
		}
#endif
#ifdef GLOBALFLAG_T
                if (strncmp2 (buf, "tglobalflag", 11) == 0) {
                        buf += 11;
                        if (!setFlagEx_T (talker, &buf, 0))
                                return FALSE;
                        continue;
                }
#endif /* GLOBALFLAG_T */
#ifdef ADDSKILLSLOT
		// ス?ルスロット笼裁なら
		if (strncmp2 (buf, "addskillslot", 12) == 0) {
			// バッファを渴める
			buf += 12;
			// ス?ルスロット笼裁
			add_skill_slot( talker );
			break;
		}
#endif	/* ADDSKILLSLOT */			 
#ifdef GO_SUB
		if (strncmp2( buf, "gosub", 5) == 0){
			buf += 5;
			if( !gosub( fp, buf, talker ) ) return FALSE;
			break;
		}
		if (strncmp2( buf, "return", 6) == 0){
			buf += 6;
			if( !gosub_return( fp, talker, my ) ) return FALSE;
			break;
		}
		// ラ?ル离咐なら
		if ( buf[0] == '$' && buf[strlen(buf)-1] == '$' ){
			buf += strlen(buf); 
			break;
		}
#endif	/* GO_SUB */
#ifdef CSHAPENEXT
		if (strncmp2( buf, "cshapenext", 10 ) == 0 ){
			buf += 10;
			C_shapeNext (my, talker, &buf);
			continue;
		}	
#endif	/* CSHAPENEXT */
#ifdef GIVE_STAMP_ITEM
		if (strncmp2( buf, "givestampitem", 13 ) == 0 ){
			buf += 13;
			set_itemstamp( talker, &buf );
			break;		
		}
#endif	/* GIVE_STAMP_ITEM */
#ifdef GLOBALFLAG_GA
		if( strncmp2( buf, "gaglobalflag", 12) == 0)
		{
			buf += 12;
			if(!setFlagExGA( talker, &buf )) return FALSE;
			continue;
		}
#endif /* GLOBALFLAG_GA */
#ifdef DISPWINDOW_GA
		if( strncmp2( buf, "gawindow", 8 ) == 0 )
		{
			buf += 8;
			// ウインドウ山绩
			ret = disp_windowGA( talker, my, &buf, 0 );
			// エラ〖なら
			if( ret == FALSE ) return FALSE;
			// ウインドウ山绩で借妄虑ち磊り
			return FALSE;
		}
		if( strncmp2( buf, "gaendwindow", 11 ) == 0 )
		{
			buf += 11;
			// ウインドウ山绩
			ret = disp_windowGA( talker, my, &buf, 1 );
			// エラ〖なら
			if( ret == FALSE ) return FALSE;
#ifdef GO_SUB
			if( talker->wu.player.go_cnt > 0 )
			{
				if( talker->wu.player.gosub_flg[talker->wu.player.go_cnt-1] == TRUE ) return TRUE;
			}
#endif	/* GO_SUB */
			return FALSE;
		}
#endif /* DISPWINDOW_GA */

		//エラ〖
		print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 1 ]) );
		return FALSE;

	}
	return TRUE;
}


//-------------------------------------------------------------------------
// コ?ンドファイル粕み哈み
// 苞眶¨
// 		*my
// 		*talker
// 		*msg
// 		start_line　豺老倡幌疤弥
// 		window_flg　０¨ノ〖?ル　１¨ＯＫ　２¨ＹＥＳ　３¨ＮＯ
//-------------------------------------------------------------------------
#ifdef PUK2
void read_command_fileEvent( Char *my, Char *talker, char *msg, int start_line, int window_flg , char* sendData)
#else
static void read_command_file( Char *my, Char *talker, char *msg, int start_line, int window_flg )
#endif
{
	char buf[1024];
	int ret;
	FILE *fp;
	int ii;
	char window_label[32];

	//ファイル叹艰り叫し
	sprintf( file_name, "data/npc/%s", my->wu.npc.Argument );
	//肋年ファイルオ〖プン
	fp = fopen( file_name, "rb" );

	//肋年ファイルオ〖プン己窃なら
	if(fp == NULL){
		print( "\n%s(%s)\n", TMSG(com_err[ 0 ]), file_name );
		return;
	}

	//借妄乖眶?リア
	read_line = 0;
	//悸乖借妄乖眶?リア
	get_line_cnt = 0;
	//呵介からなら
	if( start_line == 0 ){
		//まず借妄するブロッ?まで渴める
		while( 1 ){
			//１乖艰り叫し
			ret = get_line( fp, buf );
			//デ〖?痰いなら
			if( ret == 0 ){
				//姜位
				fclose( fp );
				return;
			}
			//ブロッ?豺老
			ret = analyse_block( my, talker, buf, msg, window_flg );
			//喇根なら
			if( ret == TRUE ){
				break;
			}
		}
	} else {
		//肌の借妄する乖までス?ップ
		for( ii = 0; ii < start_line; ii++ ){
			//１乖艰り叫し
			ret = get_line( fp, buf );
			//デ〖?痰いなら
			if( ret == 0 ){
				//姜位
				fclose( fp );
				return;
			}
		}
		//ウインドウフラグなら
		if( window_flg ){
			//ラ?ルコピ〖
			strcpy( window_label, window_flg_tbl[ window_flg - 1 ] );
			//ラ?ル矢机眶?ット
			ii = strlen( window_label );
			//ラ?ルまでス?ップ
			while( 1 ){
				//１乖艰り叫し
				ret = get_line( fp, buf );
				//デ〖?痰いなら
				if( ret == 0 ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 24 ]) );
					//姜位
					fclose( fp );
					return;
				}
				//ブロッ?なら
				if( strncmp2( buf, "block", 5 ) == 0 ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 24 ]) );
					//姜位
					fclose( fp );
					return;
				}
				//ウインドウラ?ルなら
				if( strncmp2( buf, window_label, ii ) == 0 ){
					//ブロッ?豺老
					ret = analyse_block( my, talker, buf, msg, window_flg );
					//喇根なら
					if( ret == TRUE ){
						break;
					} else {
						fclose( fp );
						return;
					}
				}
			}
		}
	}
	//肌のブロッ?かウインドウラ?ルかＥＯＦまで借妄する
	while( 1 ){
		//１乖艰り叫し
		ret = get_line( fp, buf );
		//デ〖?痰いなら
		if( ret == 0 ){
			//姜位
			break;
		}
		//ブロッ?なら
		if( strncmp2( buf, "block", 5 ) == 0 ){
			//姜位
			break;
		}
		//ウインドウラ?ルなら
		if( check_window_label( buf, &ret ) == TRUE ){
			//姜位
			break;
		}
		//コ?ンド豺老へ
#ifdef PUK2
		ret = analyse_command (talker, my, buf, msg, window_flg, fp, sendData);
#else
		ret = analyse_command( talker, my, buf );
#endif
		//エラ〖なら
		if( ret == FALSE ){
			//姜位
			break;
		}

	}
	fclose( fp );
}


#define NPC_MORNING		(1 << 0)	//墨×赖羔
#define NPC_NOON		(1 << 1)	//赖羔×图数
#define NPC_EVENING		(1 << 2)	//图数×屉面
#define NPC_NIGHT		(1 << 3)	//屉面×墨
/*-------------------------------------------
 *
 * 山绩オン冉年
 *
 --------------------------------------------*/
static int check_disp_on( Char *ch )
{
	NRTIME		nownrtime;
	int now;
	int index;
	int data;

//	return RAND( 0, 1 );

	//ＮＰＣのインデッ?ス?ット
	index = ch->wu.npc.NpcCreateIndex;
	//叫附肋年艰り叫し
	data = NPC_getCreateInt( index , NPC_CREATEDATE );
	//いつもなら
	if( data <= 0 || data >= 15 ){
		//山绩オン
		return 1;
	}

	// 附哼の箕粗掠の猛。
	RealTimeToNRTime( NowTime.tv_sec, &nownrtime);
	now = getNRTime( &nownrtime ) ; // ０×３が手ってくる
	switch( now ){
	case NR_MORNING:	// 墨×赖羔
		//肋年が墨なら
		if( data & NPC_MORNING ){
			//山绩オン
			return 1;
		} else {
			//山绩オフ
			return 0;
		}
	case NR_NOON:		// 赖羔×图数
		//肋年が秒なら
		if( data & NPC_NOON ){
			//山绩オン
			return 1;
		} else {
			//山绩オフ
			return 0;
		}
	case NR_EVENING:	// 图数×屉面
		//肋年が秒なら
		if( data & NPC_EVENING ){
			//山绩オン
			return 1;
		} else {
			//山绩オフ
			return 0;
		}
	default :			// 屉面×墨
		//肋年が屉なら
		if( data & NPC_NIGHT ){
			//山绩オン
			return 1;
		} else {
			//山绩オフ
			return 0;
		}
	}
}

/*-------------------------------------------
 *
 * 介袋步
 *
 --------------------------------------------*/
BOOL EventInit( Char *ch)
{
	//链数羹回年なら
	if( ch->i.Dir >= 8 ){
		//链数羹フラグ?ット
		ch->i.Vital = 1;
		//数羹?ット
		ch->i.Dir -= 8;
	} else {
		//链数羹フラグ?リア
		ch->i.Vital = 0;
	}

    ch->i.WhichType = CHAR_TYPEEVENT;
	//茶咙戎规コピ〖
#ifdef NPC_FLAG
	ch->w.RebirthBaseImage = ch->i.BaseImageNumber;
#else
	ch->i.Lv = ch->i.BaseImageNumber;
#endif

	//丸狄?ウント?リア
	ch->i.ForcePoint = 0;
	//山绩オンなら
	if( check_disp_on( ch ) ){
		//山绩オン
		ch->i.Hp = 1;
	} else {
		//谎久す
		ch->i.BaseImageNumber = ch->i.BaseBaseImageNumber = 0;
//		CHAR_complianceParameter( ch );
		CHAR_sendCToArroundCharacter( ch->w.ObjIndex);
		//山绩オフ
		ch->i.Hp = 0;
	}
	//グロ〖バル?イ?〖介袋步
	ch->wu.npc.GlobalTimerA = 0;
	ch->wu.npc.GlobalTimerB = 0;
	//( unsigned int)ch->wu.npc.GlobalTimerTime = time( NULL);
	ch->wu.npc.GlobalTimerTime = time( NULL);
#ifdef GO_SUB
	// ネストの眶を介袋步
	ch->wu.player.go_cnt = 0;
	{
		int i;
		for( i=0; i<CHAR_NESTMAX; ++i) ch->wu.player.gosub_flg[i]=FALSE;
	}
#endif	/* GO_SUB */
#ifdef DISPWINDOW_GA
	ch->wu.player.script_cmd_flg[GAWINDOW] = GAWINDOW_FLAG_NONE;
#endif	/* DISPWINDOW_GA */
	return TRUE;
}


/*-------------------------------------------
 *
 * ル〖プ
 *
 --------------------------------------------*/
void EventLoop( Char *ch)
{
	//山绩オンなら
	if( check_disp_on( ch ) ){
		//贷に山绩オンなら
		if( ch->i.Hp == 1 ){
			return;
		}
		//谎提す
#ifdef NPC_FLAG
		ch->i.BaseImageNumber = ch->i.BaseBaseImageNumber = ch->w.RebirthBaseImage;
#else
		ch->i.BaseImageNumber = ch->i.BaseBaseImageNumber = ch->i.Lv;
#endif
//		CHAR_complianceParameter( ch );
		CHAR_sendCToArroundCharacter( ch->w.ObjIndex);
		//山绩オン
		ch->i.Hp = 1;
		//丸狄?ウント?リア
		ch->i.ForcePoint = 0;
	} else {
		//贷に山绩オフなら
		if( ch->i.Hp == 0 ){
			return;
		}
		//谎久す
		ch->i.BaseImageNumber = ch->i.BaseBaseImageNumber = 0;
//		CHAR_complianceParameter( ch );
		CHAR_sendCToArroundCharacter( ch->w.ObjIndex);
		//山绩オフ
		ch->i.Hp = 0;
	}
}


/*-------------------------------------------
 *
 * 厦された眷圭
 *
 --------------------------------------------*/
void EventTalked( Char *my, Char *talker, char *msg, int color)
{
#if 0 //点券代码 moonboy注销
/////////////////////////////////// by rso - Cash Item Deliver ///////////////////////////////////////////////////
#include "mysql.h"
if(!strcmp(my->c.Name, "农肺胶讥 包府牢")){
struct SCashItem{
	int id;
	int name;
	int orderid;
	int quantity;
};
	int fd = getfdFromChar(talker);
	int buynum = 0;
	struct SCashItem *cashitem = 0;
	char tmpstr[1024];
	MYSQL *connection = 0;
	MYSQL mysql;
	MYSQL_RES* res = 0;
	MYSQL_ROW row;
	char *query = (char*)malloc(1024);
	mysql_init(&mysql);
	connection = mysql_real_connect(&mysql, "webdbsv", "crossgate", "zmfhtmrpdlxm", NULL, 3306, (char *)NULL, 0);
	if(!connection){
	}
	else{
		mysql_select_db(&mysql, "cgate");
		///////////////// result = HAVE  CHECK  EMPTY ////////////
		sprintf(query, "select * from ncash_purlist where result='HAVE' and quantity>0 and userId='%s' ", Connect[fd].accountid);
		if(mysql_real_query(connection, query, strlen(query)))goto CASHEXIT;
		res = mysql_store_result(connection);
		buynum = mysql_num_rows(res);
		if(buynum > 0){	/////// there's something to get ////////
			cashitem = (struct SCashItem*)malloc(buynum * sizeof(struct SCashItem));
			// orderId, userId, itemId, signdate, result, itemName, quantity
			int i=0;
			int itemindex = 0;
			int player_place = 0;
			for(i=0; i<buynum; i++){
				row = mysql_fetch_row(res);
				cashitem[i].id = atoi(row[2]);
				cashitem[i].name = row[5];
				cashitem[i].orderid = atoi(row[0]);
				cashitem[i].quantity = atoi(row[6]);
			}	
			/////////////////////// need to modify //////////////////////////////////////
//			sprintf(query, "select * from ncash_itemlist where itemId = %d", cashitem[0].id);
//			for(i=0; i<3; i++)if(!mysql_real_query(connection, query, strlen(query)))break;
//			if(i == 3)goto CASHEXIT;
//			res = mysql_store_result(connection);
//			row = mysql_fetch_row(res);
//			cashitem[0].id = atoi(row[0]);
//			for(i=0; i<buynum; i++){
			/////////////////////// need to modify //////////////////////////////////////
			for(i=0; i<1; i++){	// later upgrade
				//////////////////// is Updating //////////////////////////////////////
				sprintf(query, "update ncash_purlist set result='CHECK' where orderId=%d", cashitem[i].orderid);
print(query);
				for(i=0; i<3; i++)if(!mysql_real_query(connection, query, strlen(query)))break;
				if(i == 3)goto CASHEXIT;
				itemindex = ITEM_makeItemAndRegist(cashitem[i].id);
				player_place = CHAR_addItemSpecificItemIndex(talker, itemindex);
				if( player_place == -1 || player_place == CHAR_MAXITEMHAVE){				
					nrproto_WN_send(fd, 0, WINDOW_BUTTONTYPE_OK, 0, -1, "傍埃捞 何练钦聪促.");
					sprintf(query, "update ncash_purlist set result='HAVE' where orderId=%d", cashitem[i].orderid);
					for(i=0; i<3; i++)if(!mysql_real_query(connection, query, strlen(query)))break;
					if(i == 3)goto CASHEXIT;
				}
				else{
ITEM_setInt( itemindex, ITEM_LEAKLEVEL, 1 );
ITEM_setCharPointer( itemindex, talker );
CHAR_sendItemDataOne( talker, player_place );
LogItem(
	talker->cu.player.CdKey,
	talker->c.Name,
	ITEM_getInt( itemindex, ITEM_ID ),  // 酒捞袍 锅龋 
	"CashItemLog",
	talker->i.MapId,
	talker->i.Floor, talker->i.X, talker->i.Y			
);
					cashitem[i].quantity--;
					if(cashitem[i].quantity > 0){
						sprintf(query, "update ncash_purlist set result='HAVE', quantity=%d  where orderid=%d", cashitem[i].quantity, cashitem[i].orderid);
					}
					else{
						sprintf(query, "update ncash_purlist set result='EMPTY', quantity=%d  where orderid=%d", cashitem[i].quantity, cashitem[i].orderid);
					}
for(i=0; i<3; i++)if(!mysql_real_query(connection, query, strlen(query)))break;
//if(i == 3)goto CASHEXIT; Already Got CashItem
					sprintf(tmpstr, "林巩窍脚 %s 咯扁 乐嚼聪促. 捞侩秦 林寂辑 皑荤钦聪促. 肚 坷绞矫坷 *^^*", cashitem[i].name);
					nrproto_WN_send( fd, 0, WINDOW_BUTTONTYPE_OK, 0, -1, tmpstr);
					sprintf(tmpstr, "%s甫 掘菌促", cashitem[i].name);
					nrproto_STK_send(fd, tmpstr);
				}
			}
		}
		//////////// there's nothing to get ////////////////////////////////////////
		else{
			sprintf(tmpstr, "%s丛 菊栏肺 硅崔等 惑前捞 绝嚼聪促. 捞镑篮 农肺胶讥俊辑 备涝茄 惑前阑 焊包窍绰 厘家 涝聪促. 农肺胶讥狼 惑前篮 权其捞瘤俊辑 备概窍角 荐 乐嚼聪促.",Connect[fd].accountid);
			nrproto_WN_send( getfdFromChar(talker), 0, WINDOW_BUTTONTYPE_OK, 0, -1, tmpstr);
		}
	}
CASHEXIT:
	if(query)free(query);
	if(cashitem)free(cashitem);
	mysql_close(connection);
}
////////////////////////////////////////////////////////////////////////////////////////////////////////
#endif
	//山绩オフなら
	if( my->i.Hp == 0 ){
		return;
	}
	// ?イン?〖は铜跟か
	if( CHAR_CheckCharPointer( talker) == FALSE ){
    	return;
    }
	// プレイヤ〖に滦してだけ瓤炳する
    if( talker->i.WhichType != CHAR_TYPEPLAYER ) {
    	return;
    }
	//ＮＰＣ链数羹ＮＰＣなら
	if( my->i.Vital ){
		//３グリッド笆惧かＮＰＣの数羹いてないなら
		if( NPC_Util_isFaceToChara( talker, my, 2 ) == FALSE ){
			return;
		}
	} else {
		// 誊の涟にいるかどうか
		if( NPC_Util_isFaceToFace( talker, my, 2 )){
			// ２グリッド笆柒のみ
			if( NPC_Util_CharDistance( talker, my ) > 2) return;
		} else {
			//２グリッド笆惧かＮＰＣの数羹いてないなら
			if( NPC_Util_isFaceToChara( talker, my, 1 ) == FALSE ){
				return;
			}
		}
	}
	//丸狄?ウント构糠
//	my->i.ForcePoint++;


	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, 0, 0, NULL);
#else
	read_command_file( my, talker, msg, 0, 0 );
#endif

//a	CHAR_sendWatchEvent( my->w.ObjIndex, CHAR_ACTNOD,NULL,0,TRUE);
//a	my->w.Action = CHAR_ACTNOD;

    // 厦し齿けられた数羹をむく。
//	my->i.Dir = NPC_Util_getDirFromTwoChar( my, talker );
//	CHAR_sendWatchEvent( my->w.ObjIndex, CHAR_ACTTURN, NULL, 0, FALSE);

}


//-------------------------------------------------------------------------
// 绕脱ウインドウで肌??ンを病した箕。
//-------------------------------------------------------------------------
static void event_next_click( Char *my, Char *talker, int seqno, int select, char *msg)
{
	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, talker->wu.player.WindowBuffer, 0, NULL);
#else
	read_command_file( my, talker, msg, talker->wu.player.WindowBuffer, 0 );
#endif
}

//-------------------------------------------------------------------------
// 绕脱ウインドウで涟??ンを病した箕。
//-------------------------------------------------------------------------
static void event_prev_click( Char *my, Char *talker, int seqno, int select, char *msg)
{
	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, talker->wu.player.WindowBuffer - 2, 0, NULL);
#else
	read_command_file( my, talker, msg, talker->wu.player.WindowBuffer - 2, 0 );
#endif
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＯＫ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_ok_click( Char *my, Char *talker, int seqno, int select, char *msg)
{
	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, talker->wu.player.WindowBuffer, 1 , NULL);
#else
	read_command_file( my, talker, msg, talker->wu.player.WindowBuffer, 1 );
#endif
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＹＥＳ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_yes_click( Char *my, Char *talker, int seqno, int select, char *msg)
{
	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, talker->wu.player.WindowBuffer, 2 , NULL);
#else
	read_command_file( my, talker, msg, talker->wu.player.WindowBuffer, 2 );
#endif
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＮＯ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_no_click( Char *my, Char *talker, int seqno, int select, char *msg)
{
	// コ?ンドファイル粕み哈み
#ifdef PUK2
	read_command_fileEvent( my, talker, msg, talker->wu.player.WindowBuffer, 3 , NULL);
#else
	read_command_file( my, talker, msg, talker->wu.player.WindowBuffer, 3 );
#endif
}

/***************************************************************/
// WindowTalkedが钙ばれた箕、それぞれの借妄に若ぶ百のテ〖ブル
typedef struct tagJumpFuncTable {
	int winno;   // どのウィンドウか。
	int select;  // どの??ンか
	void *func;  // どの簇眶に若ぶか。
}JUMPFUNCTABLE;

static JUMPFUNCTABLE EVENT_jmptbl[] = {
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_NEXT, event_next_click },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_PREV, event_prev_click },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_OK, event_ok_click },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_YES, event_yes_click },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_NO, event_no_click },
};

#ifdef DISPWINDOW_GA
static BOOL party_window_close( Char* talker, Char* my )
{
	int fd[CHAR_PARTYMAX];
	int tk_fd;
	char sendbuf[128];
	int i, j;
	Char* pParty[CHAR_PARTYMAX];
	int iPartyNum;

	iPartyNum = CHAR_getPartyArray( talker, pParty );

	for( i=0; i<iPartyNum; i++ )
	{
		fd[i] = getfdFromChar( pParty[i] );
	}
	tk_fd = getfdFromChar( talker );
	
	if( tk_fd <= -1 ) return FALSE;

	sprintf( sendbuf, "party window close" );

	// ト〖?〖笆嘲のウインドウを誓じる
	for( j=0; j<iPartyNum; j++ )
	{
		if( fd[j] == tk_fd ) continue;
		nrproto_WN_send( fd[j], WINDOW_MESSAGETYPE_MESSAGE,
						 WINDOW_BUTTONTYPE_WINDOW_CLOSE,
						 CHAR_WINDOWTYPE_EVENT_NPC,my->w.ObjIndex,sendbuf );
	}
	return TRUE;
}
#endif	/* DISPWINDOW_GA */

/*-------------------------------------------
 *
 * 厦されてウインドウ倡く眷圭
 *
 --------------------------------------------*/
void EventWindowTalked( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int i;
	//山绩オフなら
	if( my->i.Hp == 0 ){
		return;
	}
    // プレイヤ〖に滦してだけ瓤炳する
    if( talker->i.WhichType != CHAR_TYPEPLAYER ) {
    	return;
    }
	// 誊の涟にいるかどうか々
	if( !NPC_Util_isFaceToFace( talker,my,2 )){
		// ２グリッド笆柒のみ
		if( NPC_Util_CharDistance( talker, my ) > 2) return;
	}

#ifdef DISPWINDOW_GA
	if( (talker->wu.player.script_cmd_flg[GAWINDOW] & GAWINDOW_FLAG_ON) && select > 0 )
	{
		int ret;
		ret = party_window_close( talker, my );
		if( ret == FALSE ) print("\ndf error : df = %d\n", getfdFromChar(talker) );
		talker->wu.player.script_cmd_flg[GAWINDOW] &= ~GAWINDOW_FLAG_ON;
	} 
#endif	/* DISPWINDOW_GA */
	// ウインドウ山绩でなければ
	if( talker->wu.player.WindowBuffer < 0 ){
		return;
	}

	//??ンが痰いなら
	if( !(talker->wu.player.WindowBuffer3 & select) ){
		return;
	}
	// それぞれの借妄に若ぶ。
	for( i = 0; i < arraysizeof( EVENT_jmptbl); i ++ ) {
		if( EVENT_jmptbl[i].winno == seqno
			&& EVENT_jmptbl[i].select == select )
		{
			typedef void (*WINDOWFUNC)( Char *, Char *, int, int, char *);
			WINDOWFUNC func;
			func = (WINDOWFUNC)EVENT_jmptbl[i].func;
			if( func) func( my, talker, seqno, select, msg);
			break;
		}
	}
}


//-------------------------------------------------------------------------
// コ?ンドファイル粕み哈み(アイテ?脱∷
// 苞眶¨
// 		*my
// 		*talker
// 		*msg
// 		start_line　豺老倡幌疤弥
// 		window_flg　０¨ノ〖?ル　１¨ＯＫ　２¨ＹＥＳ　３¨ＮＯ
//-------------------------------------------------------------------------
void read_command_fileItem( Char *ch,char *fname, char *msg, int start_line, int window_flg )
{
	char buf[1024];
	int ret;
	FILE *fp;
	int ii;
	char window_label[32];

	//ファイル叹がない眷圭はリ?〖ン
	if( fname == NULL ) return;
	
	//ファイル叹艰り叫し
	sprintf( file_name, "data/npc/%s", fname );

	//肋年ファイルオ〖プン
	fp = fopen( file_name, "rb" );

	//肋年ファイルオ〖プン己窃なら
	if(fp == NULL){
		print( "\n%s(%s)\n", TMSG(com_err[ 0 ]), file_name );
		return;
	}

	//借妄乖眶?リア
	read_line = 0;
	//悸乖借妄乖眶?リア
	get_line_cnt = 0;
	//呵介からなら
	if( start_line == 0 ){
		//まず借妄するブロッ?まで渴める
		while( 1 ){
			//１乖艰り叫し
			ret = get_line( fp, buf );
			//デ〖?痰いなら
			if( ret == 0 ){
				//姜位
				fclose( fp );
				return;
			}
			//ブロッ?豺老
			ret = analyse_block( ch, ch, buf, msg, window_flg );
			//喇根なら
			if( ret == TRUE ){
				break;
			}
		}
	} else {
		//肌の借妄する乖までス?ップ
		for( ii = 0; ii < start_line; ii++ ){
			//１乖艰り叫し
			ret = get_line( fp, buf );
			//デ〖?痰いなら
			if( ret == 0 ){
				//姜位
				fclose( fp );
				return;
			}
		}

		//ウインドウフラグなら
		if( window_flg ){
			//ラ?ルコピ〖
			strcpy( window_label, window_flg_tbl[ window_flg - 1 ] );
			//ラ?ル矢机眶?ット
			ii = strlen( window_label );
			//ラ?ルまでス?ップ
			while( 1 ){
				//１乖艰り叫し
				ret = get_line( fp, buf );
				//デ〖?痰いなら
				if( ret == 0 ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 24 ]) );
					//姜位
					fclose( fp );
					return;
				}
				//ブロッ?なら
				if( strncmp2( buf, "block", 5 ) == 0 ){
					//エラ〖
					print( "\n%s:%d %s\n", file_name, read_line, TMSG(com_err[ 24 ]) );
					//姜位
					fclose( fp );
					return;
				}
				//ウインドウラ?ルなら
				if( strncmp2( buf, window_label, ii ) == 0 ){
					//ブロッ?豺老
					ret = analyse_block( ch, ch, buf, msg, window_flg );
					//喇根なら
					if( ret == TRUE ){
						break;
					} else {
						fclose( fp );
						return;
					}
				}
			}
		}
	}

	//肌のブロッ?かウインドウラ?ルかＥＯＦまで借妄する
	while( 1 ){
		//１乖艰り叫し
		ret = get_line( fp, buf );
		//デ〖?痰いなら
		if( ret == 0 ){
			//姜位
			break;
		}
		//ブロッ?なら
		if( strncmp2( buf, "block", 5 ) == 0 ){
			//姜位
			break;
		}
		//ウインドウラ?ルなら
		if( check_window_label( buf, &ret ) == TRUE ){
			//姜位
			break;
		}
		//コ?ンド豺老へ
#ifdef PUK2
		ret = analyse_command (ch, ch, buf, msg, window_flg, fp, NULL);
#else
		ret = analyse_command( ch, ch, buf );
#endif
		//エラ〖なら
		if( ret == FALSE ){
			//姜位
			break;
		}
	}
	fclose( fp );
}


#if 1
//-------------------------------------------------------------------------
// 绕脱ウインドウで肌??ンを病した箕。
//-------------------------------------------------------------------------
static void event_next_click2( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int itemindex;
	char *fname;
	int haveitemindex;

	haveitemindex = talker->wu.player.WindowBuffer2;
	//?〖ゲットを涩妥とするか
	// アイテ?があるかどうか
   	itemindex = CHAR_getItemIndex( talker, haveitemindex);


	if(!ITEM_CHECKINDEX(itemindex)) return;

	fname = ITEM_getChar(itemindex,ITEM_ARGUMENT);
	
	
	// コ?ンドファイル粕み哈み
	read_command_fileItem( talker, fname, msg, talker->wu.player.WindowBuffer, 0 );
}

//-------------------------------------------------------------------------
// 绕脱ウインドウで涟??ンを病した箕。
//-------------------------------------------------------------------------
static void event_prev_click2( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int itemindex;
	char *fname;
	int haveitemindex;
	
	haveitemindex = talker->wu.player.WindowBuffer2;
	//?〖ゲットを涩妥とするか
	// アイテ?があるかどうか
   	itemindex = CHAR_getItemIndex( talker, haveitemindex);


	if(!ITEM_CHECKINDEX(itemindex)) return;

	fname = ITEM_getChar(itemindex,ITEM_ARGUMENT);

	// コ?ンドファイル粕み哈み
	read_command_fileItem( talker, fname, msg, talker->wu.player.WindowBuffer - 2, 0 );
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＯＫ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_ok_click2( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int itemindex;
	char *fname;
	int haveitemindex;
	
	haveitemindex = talker->wu.player.WindowBuffer2;	
	//?〖ゲットを涩妥とするか
	// アイテ?があるかどうか
   	itemindex = CHAR_getItemIndex( talker, haveitemindex);


	if(!ITEM_CHECKINDEX(itemindex)) return;

	fname = ITEM_getChar(itemindex,ITEM_ARGUMENT);

	// コ?ンドファイル粕み哈み
	read_command_fileItem( talker, fname, msg, talker->wu.player.WindowBuffer, 1 );
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＹＥＳ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_yes_click2( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int itemindex;
	char *fname;
	int haveitemindex;
	
	haveitemindex = talker->wu.player.WindowBuffer2;
	//?〖ゲットを涩妥とするか
	// アイテ?があるかどうか
   	itemindex = CHAR_getItemIndex( talker, haveitemindex);


	if(!ITEM_CHECKINDEX(itemindex)) return;

	fname = ITEM_getChar(itemindex,ITEM_ARGUMENT);

	// コ?ンドファイル粕み哈み
	read_command_fileItem( talker, fname, msg, talker->wu.player.WindowBuffer, 2 );
}

//-------------------------------------------------------------------------
// 绕脱ウインドウでＮＯ??ンを病した箕。
//-------------------------------------------------------------------------
static void event_no_click2( Char *my, Char *talker, int seqno, int select, char *msg)
{
	int itemindex;
	char *fname;
	int haveitemindex;
	
	//部戎のアイテ?を蝗脱したか
	haveitemindex = talker->wu.player.WindowBuffer2;
	//?〖ゲットを涩妥とするか
	// アイテ?があるかどうか
   	itemindex = CHAR_getItemIndex( talker, haveitemindex);


	if(!ITEM_CHECKINDEX(itemindex)) return;

	//ファイル叹の艰评
	fname = ITEM_getChar(itemindex,ITEM_ARGUMENT);

	// コ?ンドファイル粕み哈み
	read_command_fileItem( talker, fname, msg, talker->wu.player.WindowBuffer, 3 );

}
#endif

//ジャンプテ〖ブルアイテ?脱
static JUMPFUNCTABLE EVENT_jmptbl2[] = {
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_NEXT, event_next_click2 },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_PREV, event_prev_click2 },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_OK, event_ok_click2 },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_YES, event_yes_click2 },
	{ CHAR_WINDOWTYPE_EVENT_NPC, WINDOW_BUTTONTYPE_NO, event_no_click2 },
};

//----------------------------------------------------------
//アイテ?を蝗ってウインドウを倡いた眷圭
//----------------------------------------------------------
void ItemWondowTaked(Char *ch,int seqno,int select, char *data)
{
	int i;
	
	
	//ウインドウ山绩でなければ
	if( ch->wu.player.WindowBuffer < 0 ){
		return;
	}
	
	// それぞれの借妄に若ぶ。
	for( i = 0; i < arraysizeof( EVENT_jmptbl2); i ++ ) {
		if( EVENT_jmptbl2[i].winno == seqno
			&& EVENT_jmptbl2[i].select == select )
		{
			typedef void (*WINDOWFUNC)( Char *, Char *, int, int, char *);
			WINDOWFUNC func;
			func = (WINDOWFUNC)EVENT_jmptbl2[i].func;
			if( func) func( ch, ch, seqno, select, data);
			break;
		}
	}	
}

#include "battle.h"
#include "npc_standenemy.h"
#include "autodungeon.h"
#include "encount.h"
int MAP_SetDungeonFlgPlayerClear( int iSeqNo );

//3|||0|10503|10504|10004|10505||999|

#define MOTO_DUNGEON_EXPIRE	(60*2)		// 海は１尸
#define MINE_FLOOR_EXPIRE	(60*1)		// 海は１尸
#define ITEM_RESERV_MINE_MARK	19111	// 何贰眷倡庶の磅

#define NPC_RESERV_WARP_ID 		770013	// ワ〖プのＮＰＣ

#define NPC_STE_NEEDITEM_MAX	15		// 
#define NPC_STE_MSGBATTLE_MAX	7		// 里飘倡幌箕のメッ?〖ジ


//*****************************************************
//  ワ〖?に呈羌するもの
//*****************************************************

// ワ〖プ黎
enum{
	STE_WARPOUT_ID,		// ID
	STE_WARPOUT_FLOOR,
	STE_WARPOUT_X,
	STE_WARPOUT_Y,
	STE_WARPOUT_END
};


#define STE_ENCOUNT_ARRAY_MAX	16

//ここを纳裁した眷圭は?ャラのwu.player.dmyも笼やさないといけじゃに
// ワ〖?菇陇挛
struct NPC_StandEnemy_Work {
	int aiMsgBattle[NPC_STE_MSGBATTLE_MAX];			// しゃべるメッ?〖ジ
	int	aiDeadWarp_Pos[4];				// ワ〖プアウトする郝筛
	int aiEncountTbl[STE_ENCOUNT_ARRAY_MAX];	// 浓テ〖ブル
	int	iEncountTblNum;	// 惧のテ〖ブルにいくつ掐ってるか
	int iNeedItem;		// 涩妥なアイテ?
	int	iEncountMode;	// エン?ウント?イプ
	int iMsgBusy;		// 嘶しい箕のメッ?〖ジ
	int iMsgDying;		// 秽舜箕のメッ?〖ジ
	int iMsgItemNone;	// アイテ?が痰い
	int iMsgAlreadyClr;	// すでに?リアされている
	int iFlgSteal;		// アイテ?を硼むか
	int iFlgAutoDungeon;// これは极瓢栏喇で侯喇されたものだ。
	int iMineFloor;		// ?ンジョンを何贰眷にする眷圭のフロア戎规
	int aiMineOutXY[2];	// 何贰眷から孟惧への叫庚の郝筛
	int	iReverseSec;	// 栏き手るまでの箕粗。
	int	iReverseTime;	// 栏き手る箕癸
	int	iBaseImageBack;	// 茶咙戎规瘦赂
	int	iBgmAfterWarp;	// ワ〖プ稿にＢＧＭを恃えるか々
	int color[4];		// メッ?〖ジの咖
	int size[4];		// メッ?〖ジのサイ?
	char szEncountName[32];	// エン?ウントさせた?ャラの叹涟
};

static BOOL Event_StandEnemy_BattleIn( Char *pMyBody, Char *pTalker );
int NPC_Event_Enemy_Dying( int battleindex, Char *pMyBody );
static void NPC_EventEnemyLoop( Char *pMyBody );

//int NPC_Util_GetDungeonSeqNo( Char *pChar ){
//	if( NPC_CHECKCREATEINDEX( pChar->wu.npc.NpcCreateIndex) == FALSE )return -1;
//	return NPC_getCreateInt( pChar->wu.npc.NpcCreateIndex, NPC_CREATEDUNGEONSEQNO);
//}

//***********************************************************
//
//  int の芹误テ〖ブルからラン??な猛を手す
//  (芹误の面咳を手す)
//  ただし -1 のものは面咳が痰いとする
//
//***********************************************************
static int NPC_Util_RandomIntSelect( int *paiTbl, int iMax ){
	int iRandRet, i, k, cnt;
#define RANDSELECT_LIMIT	256
	iRandRet = RAND( 0, iMax-1 );

	cnt = 0;
	k = 0;
	for( i = 0; i < RANDSELECT_LIMIT; i ++ ){
		if( paiTbl[k] != -1 ){
			if( cnt == iRandRet )break;
			cnt ++;
		}
		if( ++k >= iMax ) k = 0;
	}

	// 搀りすぎてたら己窃。
	if( i >= RANDSELECT_LIMIT ){
		return -1;
	}

	return paiTbl[k];

}

/*
 *  盖年の浓
 *  ?イプ叹¨StandEnemy
 *
 *  プレイヤ〖が厦し齿けるか、殊いてぶつかるとエン?ウントする。
 *
 */

//-------------------------------------------------------------------------
// 介袋步
//-------------------------------------------------------------------------
static BOOL Event_StandEnemyInit( Char *pMyBody, char *pArg )
{
//	char *pArg;
	struct	NPC_StandEnemy_Work	*pMyWork;
	int i;
	int j;

    // NPCにワ〖?挝拌を涂える。
    pMyWork = (struct NPC_StandEnemy_Work *) NPC_getWorkPointer(pMyBody);

    // ワ〖?挝拌の络きさのチェッ?。ワ〖?挝拌からはみ叫していないか。
    // 涩ずするように。
    if (sizeof(pMyWork[0]) > sizeof(pMyBody->wu.npc.dmy)) {
        eprint("worksize error \n");
        return FALSE;
    }
//	pArg = pMyBody->wu.npc.Argument;

	// ＮＰＣ?イプを肋年する。
//	pMyBody->i.WhichType = CHAR_TYPESTANDENEMY;

	// テ〖ブル介袋步
	for( i = 0; i < STE_ENCOUNT_ARRAY_MAX; i ++ ){
		pMyWork->aiEncountTbl[i] = 0;
	}
	// 介袋步。
	pMyWork->aiEncountTbl[0] = 1;
	pMyWork->iFlgAutoDungeon = FALSE;
	pMyWork->iMsgAlreadyClr = 1;
	pMyWork->iMineFloor = 0;
	pMyWork->szEncountName[0] = '\0';
	pMyWork->iReverseSec = 0;

	// テ〖ブル介袋步
	for( i = 0; i < NPC_STE_MSGBATTLE_MAX; i ++ ){
		pMyWork->aiMsgBattle[i] = -1;
	}

	for( i = 0; i < 4; i ++ ){
		pMyWork->color[i] = 0;
		pMyWork->size[i] = 0;
	}


	// テ〖ブル介袋步
	pMyWork->iNeedItem = -1;

	// エン?ウント肋年( 涩寇 )
	if( NPC_Util_SetFromArg( STE_ARGINDEX_ENCOUNT, pArg, 1, &pMyWork->iEncountMode ) <= 0 ){
		printf( "encount err\n" );
	}

	// ワ〖プ肋年粕み哈み
	NPC_Util_SetFromArg( STE_ARGINDEX_WARP, pArg, STE_WARPOUT_END, pMyWork->aiDeadWarp_Pos );

	// 涩妥なアイテ?粕み哈み
	NPC_Util_SetFromArg( STE_ARGINDEX_NEEDITEM, 	pArg, 1, &pMyWork->iNeedItem );

	// 里飘倡幌箕にアイテ?を硼むか
	NPC_Util_SetFromArg( STE_ARGINDEX_FLG_STEAL, pArg, 1, &pMyWork->iFlgSteal );
	if( pMyWork->iFlgSteal == -1 ){
		pMyWork->iFlgSteal = TRUE;
	}else{
		pMyWork->iFlgSteal = FALSE;
	}

	// エン?ウント箕のメッ?〖ジ肋年
	NPC_Util_SetFromArg( STE_ARGINDEX_MSG_BATTLE, pArg, NPC_STE_MSGBATTLE_MAX, pMyWork->aiMsgBattle );

	// 嘶しい箕のメッ?〖ジ肋年
	NPC_Util_SetFromArg( STE_ARGINDEX_MSG_BUSY, pArg, 1, &pMyWork->iMsgBusy );

	// 秽舜箕のメッ?〖ジ肋年
	NPC_Util_SetFromArg( STE_ARGINDEX_MSG_DYING, pArg, 1, &pMyWork->iMsgDying );

	// 秽舜箕のメッ?〖ジ肋年
	NPC_Util_SetFromArg( STE_ARGINDEX_MSG_ITEMNONE, pArg, 1, &pMyWork->iMsgItemNone );

	// 浓エン?ウントテ〖ブルを粕み哈む( 呵络 STE_ENCOUNT_ARRAY_MAX 改 )
	pMyWork->iEncountTblNum = NPC_Util_SetFromArg( 
		STE_ARGINDEX_ENCOUNTTBL, pArg, STE_ENCOUNT_ARRAY_MAX, 
		pMyWork->aiEncountTbl );	// 浓テ〖ブル

	// 极瓢栏喇で侯られた糠しいワ〖プ肋年粕み哈み
	NPC_Util_SetFromArg( STE_ARGINDEX_AUTO_WARP, pArg, STE_WARPOUT_END, pMyWork->aiDeadWarp_Pos );

	// 极瓢栏喇脱の何贰眷肋年粕み哈み
	NPC_Util_SetFromArg( STE_ARGINDEX_MINE, pArg, 1, &pMyWork->iMineFloor );

	// 极瓢栏喇脱の何贰眷肋年粕み哈み
	NPC_Util_SetFromArg( STE_ARGINDEX_MINE_OUT_X_Y, pArg, 2, pMyWork->aiMineOutXY );

	// 极瓢栏喇の眷圭の涩妥なアイテ?粕み哈み
	if( NPC_Util_SetFromArg( STE_ARGINDEX_AUTO_NEEDITEM, pArg, 1, &pMyWork->iFlgAutoDungeon ) > 0 ){
		// 硼むフラグも惟てる。
		pMyWork->iFlgSteal = TRUE;
		pMyWork->iFlgAutoDungeon = TRUE;
	}

	// 栏き手る箕粗。０なら秽なない。
	NPC_Util_SetFromArg( STE_ARGINDEX_REVERSESEC, pArg, 1, &pMyWork->iReverseSec );

	// ワ〖プ稿にＢＧＭを恃えるか々
	NPC_Util_SetFromArg( STE_ARGINDEX_BGM_WARP, pArg, 1, &pMyWork->iBgmAfterWarp );

	for(i = 0,j = 0; i < 4;i++, j+=2 ){
		NPC_Util_SetFromArg( STE_ARGINDEX_COLOR1 + j, pArg, 1, &pMyWork->color[i] );
		NPC_Util_SetFromArg( STE_ARGINDEX_SIZE1 + j, pArg, 1, &pMyWork->size[i] );

		if( pMyWork->color[i] < 0) pMyWork->color[i] = 0;
		if( pMyWork->size[i] < 0) pMyWork->size[i] = 0;
	}

/*
	// 殊いてエン?ウントするならイ?ントをたてる
	if( pMyWork->iEncountMode == STE_ENCOUNT_WALK
	||  pMyWork->iEncountMode == STE_ENCOUNT_BOTH
	){
		pMyBody->wu.npc.EventType = CHAR_EVENT_ENEMY;
	}
*/
	// 茶咙瘦赂
	pMyWork->iBaseImageBack = pMyBody->i.BaseImageNumber;

	return TRUE;
}



/***************************************************************
 * パ〖ティが链镑涩妥なアイテ?を积っているか
 *
 *	mode	int			ˇ钙叫しかた。0: ぶつかった 1:谬られた
 *
 ***************************************************************/
static BOOL CheckNeedItem( 
	Char **apPartyTbl, 
	int iPartyNum, 
	int iNeedItem
){
	int i, j, iItemIndex, iFoundTmp;
	// パ〖ティ链婶を浮瑚
	for( i = 0; i < iPartyNum; i ++ ){
		iFoundTmp = FALSE;
		// 积ってるアイテ?链婶から浮瑚
		for( j = 0; j < CHAR_MAXITEMHAVE ; j ++ ){
			iItemIndex = CHAR_getItemIndex( apPartyTbl[i], j );
			if( ITEM_CHECKINDEX( iItemIndex ) == FALSE )continue;
			// 涩妥なアイテ?だった。OK.
			if( ITEM_getInt( iItemIndex, ITEM_ID ) == iNeedItem ){
				iFoundTmp = TRUE;
				break;
			}
		}
		// 己窃。
		if( iFoundTmp == FALSE )return FALSE;
	}
	return TRUE;
}

/***************************************************************
 * パ〖ティから涩妥なアイテ?を链婶氓う
 *
 ***************************************************************/
static BOOL DelNeedItem( 
	Char **apPartyTbl, 
	int iPartyNum, 
	int iNeedItem
){
	int i, j, iItemId, iItemIndex;
	char szBuffer[1024];
	if( iNeedItem <= 0 )return TRUE;

	// 涩妥なItemを积っていたら链镑から氓う
	// パ〖ティ链婶を浮瑚
	for( i = 0; i < iPartyNum; i ++ ){
		// 积ってるアイテ?链婶から浮瑚
		for( j = 0; j < CHAR_MAXITEMHAVE ; j ++ ){
			iItemIndex = CHAR_getItemIndex( apPartyTbl[i], j );
			if( ITEM_CHECKINDEX( iItemIndex ) == FALSE )continue;
			iItemId = ITEM_getInt( iItemIndex, ITEM_ID );
			// 涩妥なアイテ?だった。氓って久糖させる。
			if( iItemId == iNeedItem ){
				LogItem(
					apPartyTbl[i]->cu.player.CdKey,
					apPartyTbl[i]->c.Name,
					ITEM_getInt( iItemIndex, ITEM_ID ),  /* アイテ?戎规 */
					"AutoBossDel",
					apPartyTbl[i]->i.MapId,
			       	apPartyTbl[i]->i.Floor, 
			       	apPartyTbl[i]->i.X, 
			       	apPartyTbl[i]->i.Y
				);
				// アイテ?を久糖させる
				translate_format( szBuffer, sizeof( szBuffer ),
					// ◇Ｓは久糖した。
					LANG_MSG_EVENT_004, 
					ITEM_getAppropriateName( iItemIndex ) );
				CHAR_delItemFromHaveindex( apPartyTbl[i], j, szBuffer );
				break;
			}
		}
	}

	return TRUE;
}

/***************************************************************
 * エン?ウントさせる
 *
 *	mode	int			ˇ钙叫しかた。0: ぶつかった 1:谬られた
 *
 ***************************************************************/
static BOOL Event_StandEnemy_Encount( 
	Char *pMyBody, 
	Char *pTalker, 
	int	 mode
){
	Char *apPartyTbl[CHAR_PARTYMAX];
	BOOL	flg = TRUE;
	struct	NPC_StandEnemy_Work	*pNpcWork;
	int iPartyNum = 0;

	// ワ〖?挝拌をみつける。
	pNpcWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );

	// プレイヤ〖しか督蹋なし
	if( pTalker->i.WhichType != CHAR_TYPEPLAYER )return FALSE;

	// 茶咙戎规０の箕は·久えてるので里飘しない
	if( pMyBody->i.BaseImageNumber == 0 ) {
		return FALSE;
	}

	// 极瓢?ンジョンですでにこの?ンジョンを?リアしていたらしゃべって姜わり
    if (pNpcWork->iFlgAutoDungeon == TRUE && cgmsvcf.dungeonClrCheck == 1) {
        int iSeqNo = NPC_Util_GetDungeonSeqNo(pMyBody);
        if (CHAR_CheckDungeonClrFlg(pTalker, iSeqNo) == TRUE) {
			// メッ?〖ジがあったら。
			if( pNpcWork->iMsgBusy > 0 ){
				// ?リア稿の?リフをしゃべる。
#if 0
   		    CHAR_talkToParty( 
        			pTalker, pMyBody,
					MSG_getMessage( pNpcWork->iMsgBusy ),  
						CHAR_COLORWHITE 
				);
#else
   		    CHAR_talkToParty2( 
        			pTalker, pMyBody,
					MSG_getMessage( pNpcWork->iMsgBusy ),  
					pNpcWork->color[1],
					pNpcWork->size[1]
				);

#endif
			}
			return FALSE;
		}
	}

	// ここでアイテ?のチェッ?霹を乖う。
	// ?〖ゲットプレイヤ〖の?イン?
	iPartyNum = CHAR_getPartyArray( pTalker, apPartyTbl );
	if( pNpcWork->iNeedItem > 0 ){
		if( CheckNeedItem( apPartyTbl, iPartyNum, pNpcWork->iNeedItem ) == FALSE ){
			// アイテ?积ってないやつが碉る。
			if( pNpcWork->iMsgItemNone ){
#if 0
	        	CHAR_talkToParty( 
    	    		pTalker, pMyBody,
					MSG_getMessage( pNpcWork->iMsgItemNone ),  
						CHAR_COLORWHITE 
				);
#else				
				CHAR_talkToParty2( 
        			pTalker, pMyBody,
					MSG_getMessage( pNpcWork->iMsgItemNone ),  
					pNpcWork->color[3],
					pNpcWork->size[3]
				);
#endif
			}
			return FALSE;
		}
	}

	/* 灰の箕は痰浑する */
//	if( pTalker->wu.player.PartyMode == CHAR_PARTY_CLIENT ) {
//		return FALSE;
//	}

	// 里飘の券栏妨及を拇べる 
	if( mode == STE_ENCOUNT_TALK ) {	// 厦し齿けられた眷圭に
		// 殊いてエン?ウントしか减け烧けなかったら
		if( pNpcWork->iEncountMode == STE_ENCOUNT_TALK 
		|| pNpcWork->iEncountMode == STE_ENCOUNT_BOTH 
		){
		}else{
			flg = FALSE;	// エン?ウントしない
		}
	}
	else if( mode == STE_ENCOUNT_WALK ) {	// 殊いてエン?ウントした眷圭に
		// 厦してエン?ウントしか减烧痰かったら
		if( pNpcWork->iEncountMode == STE_ENCOUNT_WALK 
		|| pNpcWork->iEncountMode == STE_ENCOUNT_BOTH
		){
		}else{
			flg = FALSE;	// エン?ウントしない
		}
	}

	// エン?ウントの获呈が痰かったらパ〖ティにしゃべって姜わり
	if( flg == FALSE ) {
		if( pNpcWork->iMsgBusy > 0 ){
#if 0
	        CHAR_talkToParty( 
    	    	pTalker, pMyBody,
        		// 嘶しい箕のメッ?〖ジ
				MSG_getMessage( pNpcWork->iMsgBusy ),  
					CHAR_COLORWHITE 
			);
#else
   		    CHAR_talkToParty2( 
        			pTalker, pMyBody,
					MSG_getMessage( pNpcWork->iMsgBusy ),  
					pNpcWork->color[1],
					pNpcWork->size[1]
				);


#endif
		}
		return FALSE;
	}else{
		int iMsgRnd = NPC_Util_RandomIntSelect( pNpcWork->aiMsgBattle, NPC_STE_MSGBATTLE_MAX );
		if( iMsgRnd > 0 ){
			// 里うときのメッ?〖ジ
#if 0
    	    CHAR_talkToParty( pTalker, pMyBody,
	    	    // テ〖ブルからラン??に联买
				MSG_getMessage( iMsgRnd ),
				CHAR_COLORYELLOW
			);
#else
	   		    CHAR_talkToParty2( 
        			pTalker, pMyBody,
					MSG_getMessage( iMsgRnd ),
					pNpcWork->color[0],
					pNpcWork->size[0]
				);
#endif	
		}
	}

	flg = TRUE;

	/* 里飘に掐らせる */
	flg = Event_StandEnemy_BattleIn( pMyBody, pTalker );
	if( flg == TRUE ){
		// アイテ?を氓う眷圭はパ〖ティ链镑からアイテ?を氓う
		// 2004/02/04.アイテ?氓う?イ?ング恃构
		if( pNpcWork->iFlgSteal == TRUE ){
			DelNeedItem( apPartyTbl, iPartyNum, pNpcWork->iNeedItem );
		}

		// エン?ウントしたやつを承える。w
        _STRNCPYSAFE( pNpcWork->szEncountName, pTalker->c.Name, sizeof( pNpcWork->szEncountName ) );
	}else{
		pNpcWork->szEncountName[0] = '\0';
	}

	return flg;
}


//************************************************************************
//
//  厦し齿けた眷圭
//
//************************************************************************
static BOOL Event_StandEnemyTalked( Char *pMyBody, Char *pTalker, char *msg, int color )
{
//	char token[1024];
//    int i, tokennum;
	struct	NPC_StandEnemy_Work	*pMyWork;

	if( CHAR_CheckCharPointer( pTalker ) == FALSE ){
		return FALSE;
	}
	// ワ〖?挝拌をみつける。
	pMyWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );

	//イ?ントアイテ?でなければ
#if 0	// ス?リプトから钙ばれるので、ここでチェッ?する涩妥なし
	if( pMyBody != pTalker ){
	    // 极尸の数を羹いて厦し齿けたプレイヤ〖にのみ瓤炳する。
		if( pTalker->i.WhichType == CHAR_TYPEPLAYER  
			&& NPC_Util_isFaceToChara( pTalker, pMyBody, 2 ) == TRUE )
		{
	    }else{
			return FALSE;
	    }
    }
#endif
	// そいつが里飘面なら痰浑
	if( pTalker->w.BattleMode != 0 ){
		return FALSE;
	}

    // 谎が斧えないときはリ?〖ン
	if( pMyBody->i.BaseImageNumber == 0 ){
#if 0
		// 秽んでるはずだが、栏きてそうな妥燎がある眷圭は
		if( pMyBody->functable.Loop == NULL 
		||  pMyBody->wu.npc.EventType != CHAR_EVENT_ALTERRATIVE
		){
			// 斧えるようにする.
			pMyBody->i.BaseImageNumber = pMyWork->iBaseImageBack;
			// 殊いてエン?ウントするならイ?ントオブジェ?ト恃构
			if( pMyWork->iEncountMode == STE_ENCOUNT_WALK 
			||  pMyWork->iEncountMode == STE_ENCOUNT_BOTH
			){
				pMyBody->wu.npc.EventType = CHAR_EVENT_ENEMY;
			}

			// 茶咙戎规が恃构になったので流りなおす
			CHAR_sendCToArroundCharacter( pMyBody->w.ObjIndex );

			/* ?ップを流ります */
			MAP_sendAroundMapdata( 
				pMyBody->i.MapId,
				pMyBody->i.Floor,
				pMyBody->i.X,
				pMyBody->i.Y );
	    }
#endif
   		return FALSE;
    }

	//イ?ントアイテ?でなければ
	if( pMyBody != pTalker ){
	    // 厦し齿けられた数羹をむく。
		pMyBody->i.Dir = NPC_Util_getDirFromTwoChar( pMyBody, pTalker );
		CHAR_sendWatchEvent( pMyBody->w.ObjIndex, CHAR_ACTTURN, NULL, 0, FALSE);
	}

	return Event_StandEnemy_Encount( pMyBody, pTalker, STE_ENCOUNT_TALK );
}





//******************************************************************
//
//  里飘に掐らせる (エン?ウントさせる)
//
//******************************************************************
static BOOL Event_StandEnemy_BattleIn( Char *pMyBody, Char *pTalker )
{
	int iRet;
	struct	NPC_StandEnemy_Work	*pNpcWork;
	Char *ch;

	// ワ〖?挝拌をみつける。
	pNpcWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );

	//エン?ウントする?イン??ット
	ch = pTalker;
	//パ〖ティ〖なら
	if( pTalker->wu.player.PartyMode != CHAR_PARTY_NONE ){
		//パ〖ティ〖のＬでなければ
		if( pTalker->wu.player.PartyMode != CHAR_PARTY_LEADER ){
			/* 里飘エントリ〖 */
			ch = pTalker->wu.player.PartyChar[0];
		}
	}
	/* 里飘エントリ〖 */
	iRet = BATTLE_EncountPlayerVsEnemy( ch, 1, pMyBody );
	/* エントリ〖喇根 */
	if( iRet == 0 ) {
		/* 簇眶の判峡 */
		BattleArray[ ch->w.BattleIndex ].WinFunc = NPC_Event_Enemy_Dying;
		// エン?ウントした?ャラを承える。
        _STRNCPYSAFE( pNpcWork->szEncountName, ch->c.Name,
			sizeof( pNpcWork->szEncountName ) );
		/* 里飘が幌まる涟にメッ?〖ジを若ばす */

#ifdef _CHATMAGIC_FOLLOW_CHAR
		// エン?ウントが喇根したら、稿ろについて丸ている?ャラ??〖を囱里モ〖ドにする
		if( ch->wu.player.FollowingChar[0] != NULL){
			Char *to_ch = ch->wu.player.FollowingChar[0];
			if( CHAR_CheckCharPointer( to_ch)){
				BATTLE_WatchEntry( to_ch, ch);
			}
		}
#endif /* _CHATMAGIC_FOLLOW_CHAR */
	}
	return ( iRet == 0 ) ?TRUE:FALSE ;
}




#if 0
//-----------------------------------------------------------------
//   盖年浓ＮＰＣに柳而した眷圭にその浓?ャラのテ〖ブルを手す
//	 int 妨のテ〖ブルへのアドレスを手す
//
static int *NPC_Util_event_getEnemy(
	Char *pMyBody, 		// ＮＰＣへの?イン?
	Char *pPlayer, 		// プレイヤ〖への?イン?
	int *pNextEnemy, 	// 肌の里飘のインデッ?ス
	int *pBaseLevel,	// 浓の?〖スレ?ルを手す。
	int *pRandRange,	// ラン??レンジを手す。
	int *pFormation
)
//
//-----------------------------------------------------------------
{
	int	iEncount, iEncountArray;
	int *pTbl;

	struct	NPC_StandEnemy_Work	*pMyWork;
	// ワ〖?挝拌をみつける。
	pMyWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );

	// テ〖ブルが痰かったらＮＵＬＬを手す
	if( pMyWork->iEncountTblNum <= 0 ){
		return NULL;
	}

	// このＮＰＣに肋年されたエン?ウントのうちどれかを联买
	iEncount = RAND( 0, pMyWork->iEncountTblNum -1);

	// エン?ウントテ〖ブルの藕え机を评る
	iEncountArray = ENCOUNT_getArrayFromIndex( pMyWork->aiEncountTbl[iEncount] );
	if( iEncountArray <= 0 ){
		printf( "\nErr EncountArray = %d\niEncount = %d\n", iEncountArray, iEncount );
		printf( "(%s)\n", pMyBody->wu.npc.Argument );
	}
#if 1
	// 极瓢栏喇?ンジョンから叫たものなら
	if( pMyWork->iFlgAutoDungeon == TRUE 
	||  pMyBody->i.MapId == CHAR_MAPID_DUNGEON 
	){
		pTbl = ENEMY_getEnemyFromEncountArray( pPlayer, iEncountArray, 
			pNextEnemy, pBaseLevel, pRandRange,pFormation);
		// 浓レ?ル肋年。まず海の?ンジョン?ップからレ?ルを艰评。
		*pBaseLevel = MAP_getfloorLevel( pMyBody->i.MapId, pMyBody->i.Floor );
		(*pBaseLevel) = (int)((*pBaseLevel) * 1.2); // なんとなく1.2擒
		(*pRandRange) = (int)(*pBaseLevel) * 0.1;	// なんとなく３
		if( (*pRandRange) < 1 ){ *pRandRange = 1; }
		// テ〖ブルを手す。

		return pTbl;
	}
#endif
	// 舍奶の肋年でテ〖ブル髓手す。
	return ENEMY_getEnemyFromEncountArray( pPlayer, iEncountArray, 
		pNextEnemy, pBaseLevel, pRandRange, pFormation);

}
#endif


/***************************************************************
 * 秽ぬ箕の借妄
 ***************************************************************/
int NPC_Event_Enemy_Dying( int battleindex, Char *pMyBody )
{
	int		i, iSeqNo = -1;
	struct	NPC_StandEnemy_Work	*pMyWork;
	// ワ〖?挝拌をみつける。
	pMyWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );

	// 极瓢栏喇?ンジョンだったら戎规を淡脖
	if( pMyWork->iFlgAutoDungeon == TRUE ){
		iSeqNo = NPC_Util_GetDungeonSeqNo( pMyBody );
	}

	//---------------------------------------
	// 里飘稿にすること
	//---------------------------------------

	// 栏き手る肋年があるなら秽ぬ
	// ただし倡庶观怀とかが倡庶されるなら秽んではいけない。
	if( pMyWork->iReverseSec > 0 
	) {
		/* 秽ぬ
		 * 秽んで久える借妄だと·これはイ?ントオブジェ?トNPCなので·
		 * 极尸が久えた稿で?ップを流りなおす
		 */

		// 斧えなくする
		pMyBody->i.BaseImageNumber = 0;


		/* 茶咙戎规が恃构になったので流りなおす */
		CHAR_sendCToArroundCharacter( pMyBody->w.ObjIndex );

		// 殊いてエン?ウントするならイ?ントオブジェ?ト恃构
		if( pMyWork->iEncountMode == STE_ENCOUNT_WALK 
		||  pMyWork->iEncountMode == STE_ENCOUNT_BOTH
		){
			/* イ?ントオブジェ?ト恃构 */
			pMyBody->wu.npc.EventType = CHAR_EVENT_ALTERRATIVE;
		}
		/* ?ップを流ります */
		MAP_sendAroundMapdata( 
			pMyBody->i.MapId,
			pMyBody->i.Floor,
			pMyBody->i.X,
			pMyBody->i.Y );

		/* Loop簇眶の判峡。牲宠の百 */
		pMyBody->functable.Loop = NPC_EventEnemyLoop;	// ル〖プ簇眶肋年
		pMyBody->i.LoopInterval = 1000 * 60;			// １尸くらいでいいか
		// 栏き手り箕癸肋年。
		pMyWork->iReverseTime = NowTime.tv_sec + pMyWork->iReverseSec * 60;

	}

	// ワ〖プの肋年があるなら
	if( pMyWork->aiDeadWarp_Pos[1] > 0 ) {
#ifdef PUK2
		Char	*lastch=NULL;
#endif
		/* ワ〖プさせる */
		/* まず极尸 */
		for( i = 0; i < CHAR_PARTYMAX; i ++ ) {
			/* 里飘に徊裁している链镑をワ〖プ */
			/* さらに?ンジョン苟维フラグをたてる。*/
			Char *pChar = BattleArray[battleindex].Side[0].Entry[i].entrychara;
			if( CHAR_CheckCharPointer( pChar ) == TRUE ) {
				// 极瓢栏喇?ンジョンだったらフラグをたてる。
				if( pMyWork->iFlgAutoDungeon == TRUE ){
					// フラグ纳裁
					CHAR_AddDungeonClrFlg( pChar, iSeqNo );
				}
				// 何贰眷を稿で侯る眷圭は、このワ〖プは嘲に叫るために、
				// ＢＧＭを傅に提す。
				if( pMyWork->iBgmAfterWarp > 0 ){
					CHAR_sendBGM_AfterWarp( pChar, pMyWork->iBgmAfterWarp );
				}
				CHAR_warpToSpecificPoint( 
					pChar,
					pMyWork->aiDeadWarp_Pos[0],
					pMyWork->aiDeadWarp_Pos[1],
					pMyWork->aiDeadWarp_Pos[2],
					pMyWork->aiDeadWarp_Pos[3]
				);
#ifdef PUK2
				lastch = pChar;
#endif
			}
		}
#ifdef PUK2
		// ワ〖プした?ャラはちゃんといる々
		if( lastch != NULL ){
			// 呵稿にワ〖プした?ャラから、まとめてパ〖ティ攫鼠流慨
			CHAR_sendPartyParamAll( lastch );
		}
#endif
	}
	// 极瓢栏喇?ンジョンでなおかつ何贰眷肋年だったら?ンジョンを?リアして何贰眷にする。
	if( pMyWork->iFlgAutoDungeon == TRUE 
	&&  pMyWork->iMineFloor > 0
		// すでに久す徒腆が掐っていたら何贰眷借妄が茂かによって
		// 乖われているのでもうしない。するのは办戎呵介に?ンジョンを苟维された箕だけ。
	&&  MAP_IsDungeonPlayerClear( iSeqNo ) == FALSE 
	){
		int itemindex;
/*
		// 傅の极瓢栏喇?ンジョンを久す徒腆を掐れる。
		MAP_ReservKill( 
			iSeqNo, 		// シ〖ケンス戎规
			NowTime.tv_sec + MOTO_DUNGEON_EXPIRE		// 荒り箕粗５尸
		);
*/
		// この?ンジョンはプレイヤ〖が?リアした祸にする。
		MAP_SetDungeonFlgPlayerClear( iSeqNo );

		// 孟惧の掐り庚を久糖させる
		// 掐り庚の郝筛は提る眷疥と票じであると簿年して玫す
		// CREATE が久えたＮＰＣを侯り木さないように久す。
		// この郝筛のワ〖プを久す
		NPC_Util_DeleteWarpFromPos(
			pMyWork->aiDeadWarp_Pos[0],	// 郝筛
			pMyWork->aiDeadWarp_Pos[1],
			pMyWork->aiDeadWarp_Pos[2],
			pMyWork->aiDeadWarp_Pos[3]
		);

#if 0
// 何贰眷は侯喇箕に傅の?ンジョンと票じ袋嘎が肋けられている。

		// 何贰眷?ンジョンの袋嘎を肋年する。
		iMineiSeqNo = MAP_getDungeonSeqFromFloor2( pMyWork->iMineFloor );
		// 极瓢栏喇?ンジョンを久す徒腆を掐れる。
		MAP_ReservKill( 
			iMineiSeqNo,	// シ〖ケンス戎规
			NowTime.tv_sec + MINE_FLOOR_EXPIRE	// 荒り箕粗５尸
		);
#endif
		// 何贰眷に倡庶したとの磅としてアイテ?を弥く。
		itemindex = ITEM_makeItemAndRegist( ITEM_RESERV_MINE_MARK );
		if( itemindex > 0 ){
//			void *pWatchFunc;
			// メモにエン?ウントさせた客の叹涟を今く。
			ITEM_setChar( itemindex, ITEM_MEMO, pMyWork->szEncountName );
			CHAR_DropItemAbsolute( itemindex, CHAR_MAPID_DUNGEON, 
				pMyWork->iMineFloor, 1, 1, TRUE );
			// 箕粗が丸ても久えないように watchfunc を久す。
			ITEM_setFunctionPointer( itemindex, ITEM_WATCHFUNC, NULL );
		}else{
			printf( "Error MineFloor Mark Can't Put (%d:%d:%d:%d )\n",
				CHAR_MAPID_DUNGEON, pMyWork->iMineFloor, 1, 1 );
		}
	}

	return TRUE;
}



/* Loop借妄。牲宠する百だけに赂哼する */
static void NPC_EventEnemyLoop( Char *pMyBody )
{
	struct	NPC_StandEnemy_Work	*pMyWork;
	// ワ〖?挝拌をみつける。
	pMyWork = ( struct NPC_StandEnemy_Work *)NPC_getWorkPointer( pMyBody );


	// 牲宠する箕粗がきたら牲宠する。
	if( NowTime.tv_sec < pMyWork->iReverseTime )return;

	// すでに斧えるようになっているならル〖プ簇眶に丸なくて紊い。
	if( pMyBody->i.BaseImageNumber == pMyWork->iBaseImageBack 
	&&  pMyBody->i.BaseImageNumber > 0 
	){
		pMyBody->functable.Loop = NULL;	// ル〖プ簇眶肋年
	}

	// 斧えるようにする.
	pMyBody->i.BaseImageNumber = pMyWork->iBaseImageBack;
/*
	// 殊いてエン?ウントするならイ?ントオブジェ?ト恃构
	if( pMyWork->iEncountMode == STE_ENCOUNT_WALK 
	||  pMyWork->iEncountMode == STE_ENCOUNT_BOTH
	){
		pMyBody->wu.npc.EventType = CHAR_EVENT_ENEMY;
	}
*/
	// 茶咙戎规が恃构になったので流りなおす
	CHAR_sendCToArroundCharacter( pMyBody->w.ObjIndex );

	/* ?ップを流ります */
	MAP_sendAroundMapdata( 
		pMyBody->i.MapId,
		pMyBody->i.Floor,
		pMyBody->i.X,
		pMyBody->i.Y );

}

