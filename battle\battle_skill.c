#include	<string.h>
#include	<math.h>
#include	"char.h"
#include	"char_event.h"
#include	"battle.h"
#include	"battle_event.h"
#include	"configfile.h"
#include	"log.h"
#include	"language.h"
#include	"battle_skill.h"
#include	"battle_magic.h"
#include	"battle_item.h"
#include	"battle_ai.h"
#include	"tech.h"
#include	"skill.h"
#include 	"item_event.h"
#include 	"jobs.h"
#include 	"injury.h"
#include	"char_suit.h"

#include	"version.h"

#ifdef PUK2
#include	"../puk2/puk2.h"
#include "../ext/battleExt.h"
#include "../ext/itemExt.h"

#endif
INLINE BOOL ITEM_IsShield(int itemIndex);

//***********************************************************
//
//	单固瘤 昏皑啦父阑 夸备茄促(绊沥魄)
//
//***********************************************************
int BATTLE_DamageModyfy(Char *ch)
{
	char *option;
	int a = 0;
	char *p;
	int array;

	if(BATTLE_SkillUseCheck(ch) == FALSE) return -1;

	array  = TECH_getTechIndex(ch->w.BattleCom3);
	option =  TECH_getChar(array, TECH_OPTION);

	if(option == NULL){
		return 1;
	}


	//单固瘤 版皑啦)
	p = strstr(option,"DD:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "DD:", a);
#endif
/*ADD END*/
	}

	return a;
}

//***********************************************************
//
//	单固瘤 昏皑啦父阑 夸备茄促(罚待魄)
//
//***********************************************************
int BATTLE_DamageModyfyWidth(Char *ch)
{
	char *option;
	int a = 0;
	char *p;
	int array;
	int work = 0;

	if(BATTLE_SkillUseCheck(ch) == FALSE) return -1;

	array  = TECH_getTechIndex(ch->w.BattleCom3);
	option =  TECH_getChar(array, TECH_OPTION);

	if(option == NULL){
		return 1;
	}

	//傍拜 雀荐(弥家)
	p = strstr(option,"RS:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "RS:", a);
#endif
/*ADD END*/
		work = a;
	}

	//傍拜 雀荐(弥措)
	p = strstr(option,"RE:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "RE:", a);
#endif
/*ADD END*/
		work = RAND(work , a);
	}

	return work;

}



//**********************************************************
//
//瓷仿 胶乔靛父阑 炼荤茄促
//
//馆券蔼：瓷仿 胶乔靛(-100~100)
//
//**********************************************************
int BATTLE_AbilitySpeed(Char *ch, int action)
{
	char *option;
	char *p;
	int array;
	int a, iret= 0;

	if(ch->w.BattleCom1 == BATTLE_COM_ITEM){
		iret = 50;
	}else if(ch->w.BattleCom1 == BATTLE_COM_ESCAPE){
		iret = 50;
	}else if(ch->w.BattleCom1 == BATTLE_COM_EQUIP){
		iret = -50;
	}else if(ch->w.BattleCom1 != BATTLE_COM_ATTACK){
		if(action == 0){
			array  = TECH_getTechIndex(ch->w.BattleCom3);
		}else{
			array  = TECH_getTechIndex(ch->w.Battle2Com3);
		}

		if( array == -1) return iret;

		option =  TECH_getChar(array, TECH_OPTION);

		if(option == NULL){
			return iret;
		}


		p = strstr(option,"AS:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AS:", a);
#endif
/*ADD END*/
			iret = a;
		}
	}
	return iret;

}



//'侥喊 扁龋
//("", "SR", "AN", "AM", "DG", "DR", "CR", "MR", "AR", "QR","HR", "RR", "FR", "CR")




//*********************************************************
//扁傍藕栏肺 儒飘矾瘤绊 筋酒 嘎洒绊 粱 加己 馆傈
//
//馆券蔼：藕(劝)狼 荐
//
//********************************************************
int BATTLE_Skill_SpiracleShot(Char *ch, int mode)
{
	char *option;
	int a, attack = 1;
	char *p;
	int array;

	array  = TECH_getTechIndex(ch->w.BattleCom3);
	option =  TECH_getChar(array, TECH_OPTION);

	//傍拜 雀荐(弥家)
	p = strstr(option,"AN:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AN:", a);
#endif
/*ADD END*/
		attack = a;
	}

	//傍拜 雀荐(弥措)
	p = strstr(option,"AM:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AM:", a);
#endif
/*ADD END*/
		attack = RAND(attack , a);
	}


	return attack;

}


//**************************************************************
//
//畔狼 弥檬肺 胶懦 版氰摹甫 罐阑 荐 乐绰 版快(肚绰 1 咀记 饶)
//
//**************************************************************
void BATTLE_SkillExpTurnStart(int battleindex, Char *ch)
{
	int array;
	int skillid;
	int skillindex;
	int fp;
	char szCommand[512];

	array  = TECH_getTechIndex(ch->w.BattleCom3);
	if(TECH_CHECKINDEX( array) == FALSE) return;

	skillid = TECH_getInt(array, TECH_SKILLID);
//	fp = TECH_getInt(array, TECH_FORCEPOINT);
	fp = BATTLE_SKILL_UseFp(ch);


	if(skillid == -1) return;

	skillindex = SKILL_getSkillIndex( skillid);
	if(SKILL_CHECKINDEX( skillindex) == FALSE) return;

	if(SKILL_getInt(skillindex, SKILL_EXPTABLE) == SKILL_BATTLEEXPTABLE2){
		//胶懦 版氰摹 掘绰促
		if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -2){
			//CFP|磊脚 ID｜家厚 FP|
			snprintf( szCommand, sizeof( szCommand ),
					"CFP|a%X|d%X|",
					BATTLE_Bid2No( battleindex, ch),
					fp);
			BATTLESTR_ADD( szCommand );
		}else{
			ch->w.BattleCom1 = BATTLE_COM_NONE;
			ch->w.BattleCom2 = -1;
			ch->w.BattleCom3 = -1;
		}
	}

	return;
}

//**************************************************************
//	胶懦阑 荤侩沁阑 锭俊 敲饭捞绢啊 啊瘤绊 乐绰 蔼捞 函拳茄促
//	拱扒阑 函拳矫挪促
//*************************************************************
int BATTLE_Skill_ModParam(Char *ch)
{
	int a , attack = 1;
	char *p;
	char *option;
	int array;
	int skillid;
	float work=0, work2=0;


//("", "SR", "AS", "AN", "AM", "TR", "DR", "QR", "MR", "RR", "CR", "CO", "AV", "HR", "D1", "D2", "DD", "AR", "FR", "CH", "CN", "ST", "SL", "PO", "AM", "YO", "RS", "RE")

	//胶懦 荤侩窍瘤 臼促搁 鸥格
	if(BATTLE_SkillUseCheck( ch) == FALSE) return attack;

	//胶懦阑 荤侩秦 (救)吝殿溜府畔
//	if(ch->w.BattleCom3 < 0) return attack;

	array  = TECH_getTechIndex( ch->w.BattleCom3);
	if(TECH_CHECKINDEX(array) == FALSE) return attack;

	option =  TECH_getChar(array, TECH_OPTION);

	//胶懦 ID
	skillid = TECH_getInt( array, TECH_SKILLID);

	if(option == NULL ){
		print("Name=%s SkillOptionError!=%d ", ch->c.Name, ch->w.BattleCom3);
		return attack;
	}

	//规绢 焊沥
	p = strstr(option,"DR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "DR:", a);
#endif
/*ADD END*/
		work = ch->w.DefencePower * ( (float) a * 0.01);
		ch->w.ModDefence = (int) work;	//荐沥摹 WORK甫 荤侩
	}

	//付贱 焊沥
	p = strstr(option,"MR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "MR:", a);
#endif
/*ADD END*/
		work = ch->w.MagicPower * ( (float) a * 0.01);
		ch->w.ModMagic = (int) work;
		ch->w.MagicPower = ch->w.MagicPower + work;
	}

	//傍拜仿 焊沥
	p = strstr(option,"TR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "TR:", a);
#endif
/*ADD END*/
		work = ch->w.AttackPower * ( (float) a * 0.01);
		ch->w.ModAttack = work;		//荐沥摹 WORK甫 荤侩
	}

	//刮酶仿 焊沥
	p = strstr(option,"QR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "QR:", a);
#endif
/*ADD END*/
		work = ch->w.Agility * ( (float) a * 0.01);
		ch->w.ModAgility = work;
		ch->w.Agility = ch->w.Agility + work;
	}

	//府力拌 捞寇绰 焊沥
	if( skillid < BATTLE_MAGIC_SKILL_REGENERATE_ONE
	 || skillid > BATTLE_MAGIC_SKILL_REGENERATE_ALL
	){
		//雀汗仿 焊沥
		p = strstr(option,"RR:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "RR:", a);
#endif
/*ADD END*/
			work = ch->w.Recovery * ( (float) a * 0.01);
			ch->w.Recovery = ch->w.Recovery + work;
		}
	}

	//墨款磐 荐沥 焊沥
	p = strstr(option,"CO:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "CO:", a);
#endif
/*ADD END*/
		work2 = a;
		if(ch->w.ModCounter < 1) ch->w.ModCounter = 0;

		p = strstr(option,"CM:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "CM:", a);
#endif
/*ADD END*/
			a = RAND(work2 , a);
			ch->w.ModCounterMagic  = a;

		}else{
			ch->w.ModCounterMagic = work2;
		}
	}

	p = strstr(option,"HR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "HR:", a);
#endif
/*ADD END*/
		ch->w.ModHitRateMagic  = a;		//荐沥摹 WORK甫 荤侩
	}

	//困扁 焊荐沥沥
	p = strstr(option,"CR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "CR:", a);
#endif
/*ADD END*/
		ch->w.ModCriticalMagic  = a;		//荐沥摹 WORK甫 荤侩
	}

	//雀乔啦 荐沥 焊沥
	p = strstr(option,"AV:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AV:", a);
#endif
/*ADD END*/
		ch->w.ModAvoidMagic  = a;		//荐沥摹 WORK甫 荤侩
	}

	//楷加 傍拜父 抗寇肺 秒鞭茄促
	if(ch->w.BattleCom1 == BATTLE_COM_P_RENZOKU){
		int min = 1, max = 1;
		int work= 2;
		int ran;
		int level;
		//楷加 傍拜篮 抗寇 秒鞭
		//傍拜 雀荐狼 MAX甫 夸备茄促


		//傍拜 雀荐(弥家)
		p = strstr(option,"AN:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AN:", a);
#endif
/*ADD END*/
			min = a;
		}


		//傍拜 雀荐(弥措)
		p = strstr(option,"AM:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AM:", a);
#endif
/*ADD END*/
			max = a;
		}

		array  = TECH_getTechIndex(ch->w.BattleCom3);
		if(TECH_CHECKINDEX(array) == FALSE) return attack;

		level =  TECH_getInt(array, TECH_NECESSARYLV);

		//胶懦 饭骇捞 5捞惑 10 捞窍绰 弥历 荐沥篮 绝澜
		if(level >= 5 && level <=10 ){
			attack = RAND(min , max);
//			print("attack=%d ", attack);
		}else{

			//弥措
			work  = max * 2;

			//罚待
			ran = RAND(min, work);


			if(ran == min){
	//			print("弥家甫 GET=%d", max);
				attack = max;
			}else{
				//圈荐扼搁 1歹茄促
				if(ran%2 == 1)
				{
	//				print("圈荐 = %d ", ran);
					ran  =ran + 1;
				}

				//傍拜 雀荐
				attack = ran / 2 ;

			}
		}

	}else{

		//傍拜 雀荐(弥家)
		p = strstr(option,"AN:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AN:", a);
#endif
/*ADD END*/
			attack = a;
		}

		//傍拜 雀荐(弥措)
		p = strstr(option,"AM:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &a);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "AM:", a);
#endif
/*ADD END*/
			attack = RAND(attack , a);
		}
	}

	return min(BATTLE_TARGET_NUM, attack);
}





//***************************************************
//
//规菩甫 厘厚 窍绊 乐阑鳖?
//
//馆券蔼：TRUE  厘厚 窍绊 乐促
//		  FALSE 厘厚 窍瘤 臼绰促
//
//***************************************************
BOOL BATTLE_SkillNeedEquipShield(Char *ch, int skillindex)
{
	int itemindex;
	int itemindex2;


	//鞠 1狼 眉农
	itemindex = CHAR_getItemIndex( ch, CHAR_ARM1 );
	itemindex2 = CHAR_getItemIndex( ch, CHAR_ARM2 );

	if( ITEM_CHECKINDEX( itemindex ) == FALSE
		&& ITEM_CHECKINDEX( itemindex2 ) == FALSE)
	{
		 return FALSE;
	}

    if (ITEM_CHECKINDEX(itemindex) == TRUE) {
        if (ITEM_IsShield(itemindex)) {
            return TRUE;
        }
    }

    if (ITEM_CHECKINDEX(itemindex2) == TRUE) {
        if (ITEM_IsShield(itemindex2)) {
            return TRUE;
        }
    }

	return FALSE;



}

//***************************************************
//
//	胶懦阑 荤侩窍绰单 鞘夸茄 厘厚甫 窍绊 乐阑鳖·
//
//馆券蔼：TRUE  厘厚 窍绊 乐促
//		  FALSE 厘厚 窍瘤 臼绰促
//***************************************************
BOOL BATTLE_SkillNeedEquip(Char *ch, int skillindex) {
    int equip;
    int itemtype;

    //厘厚 且 荐 乐绰 厚飘 敲贰弊
    equip = SKILL_getInt(skillindex, SKILL_NEEDEQUIP);
    if (equip == 0) return TRUE;

    //泅犁狼 厘厚 公扁
    itemtype = BATTLE_GetWepon(ch);

    //盖颊篮 抗寇
    if (itemtype <= -1) {
        itemtype = 7;
    }

    if ((equip & (1 << itemtype)) == (1 << itemtype)) {
        return TRUE;
    }

    if (ITEM_IsExtItemWithFlag(itemtype, equip)) {
        return TRUE;
    }

    return FALSE;
}

//**************************************************************
// 胶懦 饭骇 UP 茄促
// 惯悼矫俊 版氰蔼捞 甸绢棵 锭肺 窍磊
//
//**************************************************************
BOOL BATTLE_SkillUpCheck(int battleindex, Char *ch, int skillindex)
{
	int i , index;
	int skillexp_id;
	int skillexp_index;
	int maxlevel=0;
	int jobid = ch->iu.player.Job;
	int exp;

	//流诀俊 狼秦 扁撅惑茄 胶懦 饭骇捞 促福促
	maxlevel = SKILL_getMaxLevel(skillindex, jobid);

	//胶懦 版氰摹 抛捞喉狼 ID甫 炼荤茄促
	skillexp_id = SKILL_getInt( skillindex, SKILL_S_TABLE);
	skillexp_index = SKILLEXP_getSkillExpIndex( skillexp_id);

//	print("maxlevel=%d", maxlevel);

	//胶懦阑 茫绰促
	for(i = 0 ; i < CHAR_MAXSKILLHAVE ; i++)
	{
		//胶懦狼 眉农
		if(	ch->player_addon->Skill[i].SkillId == SKILL_getInt( skillindex, SKILL_ID))
		{
			//胶懦 饭骇
			index = ch->player_addon->Skill[i].SkillLevel;

//			print("index=%d \n", index);

			if(index >= maxlevel )
			{
				ch->player_addon->Skill[i].SkillLevel = maxlevel;
				ch->player_addon->Skill[i].SkillExp = SKILLEXP_getInt(skillexp_index, maxlevel-1 );
				return FALSE;
			}

			exp = 	SKILLEXP_getInt(skillexp_index, index );
//			print("exp=%d ", exp);
			//版氰摹 眉农
			if(exp <= ch->player_addon->Skill[i].SkillExp)
			{
				ch->player_addon->Skill[i].SkillLevel++;

				//概仿阑 棵赴促
				ch->iu.player.Charm += CH_FIX_PLAYERSKILLUP;
				if(ch->iu.player.Charm > 100 ) 	ch->iu.player.Charm = 100;

				//maxlevel 捞惑篮 粮犁窍瘤 臼绰促
				if(	ch->player_addon->Skill[i].SkillLevel >= maxlevel)
				{
					ch->player_addon->Skill[i].SkillLevel = maxlevel;
					ch->player_addon->Skill[i].SkillExp=SKILLEXP_getInt(skillexp_index, maxlevel-1);
				}
#ifdef ITEM_BOOSTHERB
				// 傈捧寇肺何磐狼 龋免 眉农
				if( battleindex != -1 ){
					//胶懦狼 饭骇捞 棵耳栏骨肺 殿废
					SkillLevelUp[SkillLevelCount] =  BATTLE_Bid2No( battleindex, ch );
					SkillLevelCount++;
				}
#else
				//胶懦狼 饭骇捞 棵耳栏骨肺 殿废
				SkillLevelUp[SkillLevelCount] =  BATTLE_Bid2No( battleindex, ch );
				SkillLevelCount++;
#endif

				//胶懦 单捞磐甫 焊辰促
				CHAR_sendSkillDataOne( ch, i);
				CHAR_complianceTech( ch, FALSE);
				//扁贱 单捞磐 焊辰促(CHAR_complianceTech俊辑绰 焊郴瘤瘤 臼绰促)
				CHAR_sendTechData( ch, i);

				//CHAR_complianceTech( ch, FALSE);

				return TRUE;
			}
			break;
		}
	}

	return FALSE;
}


//**************************************************************
//
//胶懦 版氰摹甫 掘绰促(郴己侩)
//
//**************************************************************
int BATTLE_SkillPointResistance(int battleindex, Char *ch, int skillhave)
{
	BATTLE *pBattle;
	BATTLE_ENTRY *pEntry;
	char szBuffer[256];
	int side;
	int skillindex;
	int techlevel;
	int index;
	int i;
	int skillexp_id;
	int maxlevel;
	int nowmaxlevel;
	int jobid = ch->iu.player.Job;
	float exp = 10.0;
	int iexp = 0;
	int population;
	int worldplayernum;
	int reasonablepopulation;
	int ratio;
	float correction;



	//某腐磐狼 眉农
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	//利捞扼搁 包拌绝扁 锭巩俊 府畔
	if(ch->i.WhichType == CHAR_TYPEENEMY) return -1;
	//局肯悼拱档 包拌绝促
	if(ch->i.WhichType == CHAR_TYPEPET) return -1;

	//掂郡吝篮 版氰摹绰 甸绢啊瘤 臼绰促
	if(BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P) return -1;

	// 叼滚弊 葛靛扼搁 冻绢瘤瘤 臼绰促.
	if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == FALSE ) {
		//焊胶傈苞 绊沥利篮 胶懦 版氰摹绰 秒垫且 荐 绝促
		if(BattleArray[battleindex].flg & BATTLE_FLG_NO_SKILLEXP) return -1;
	}

	//醚傈捧 胶懦 版氰摹啊 MAX甫 逞绊 乐栏搁(磊) 救 凳
//	if(ch->wu.player.BattleSkillExp >= 	BATTLE_MAXSKILLEXP ) return -1;

	//胶懦 版氰蔼档 涝陛矫虐绊 乾促

	//磊脚 荤捞靛甫 掘绰促
	side = ch->w.BattleSide;

	pBattle = &BattleArray[ battleindex];
	pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

	//某腐磐狼 器牢磐肺何磐 殿废 锅龋俊 函券
	index = BATTLE_Index2No( battleindex, ch);

	//版氰摹 罐阑 荐 乐阑瘤 绢冻瘤
	if(pEntry[ index].flg & BENT_FLG_SKILLEXP){
//		print("蜡皑, 傈捧 胶懦 版氰摹绰 罐阑 荐 绝促");
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_181), "");
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );
		return -1;
	}


	//胶懦 牢郸胶甫 掘绰促
	skillindex = SKILL_getSkillIndex( ch->player_addon->Skill[ skillhave].SkillId);

	//函荐甫 函版
	i = skillhave;


	//流诀俊 狼秦 扁撅惑茄 胶懦 饭骇捞 促福促
	maxlevel = SKILL_getMaxLevel( skillindex, jobid);


	if(maxlevel <= (ch->player_addon->Skill[i].SkillLevel))
	{
//		print("MAX 饭骇捞骨肺 版氰摹甫 临 荐 绝促");
//		"(%s) 胶懦 版氰摹(+%d)(total=%d)",
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_180), ""
		);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

//		CHAR_talkToCli( ch, NULL, szBuffer , CHAR_COLORCYAN );


		return -1;
	}

	techlevel = ch->player_addon->Skill[i].SkillLevel;
	nowmaxlevel = maxlevel = ch->player_addon->Skill[i].SkillLevel;

	if(nowmaxlevel <= 0 ) nowmaxlevel = 1;

	if(techlevel <= 0) techlevel = 1;
	//鞠混狼 EXP绰 绊沥
	if(SKILL_getInt(skillindex, SKILL_ID) == BATTLE_PARTICULAR_SKILL_ASSASSIN){
		exp = 10;
	}else{
		//EXP 拌魂(傈巩, 捞劳 力寇窍促)
		exp = exp * ( (float) techlevel / nowmaxlevel);
	}
	// 胶懦 厚啦 焊沥 拌荐狼 秒垫
	// 捞 胶懦阑 啊瘤绊 乐绰 傈技拌狼 牢盔荐
	population = SKILL_getPopulation( skillindex);
	if( population <= 0 ) population = 1;	// 1 捞窍绰 绝澜
	// 技拌狼 牢备
	worldplayernum = CHAR_getWorldPlayerMaxNum();
	if( worldplayernum <= 0 ) worldplayernum = 1;	// 1 捞窍绰 绝澜
	// 厚啦
	ratio = SKILL_getInt( skillindex, SKILL_RATIOPOPULATION);
	// 利沥 牢备
	reasonablepopulation = worldplayernum * (ratio/1000.0);
	// 焊沥 拌荐
	correction = reasonablepopulation / (float) population;
	if( correction < 0.1 ) correction = 0.1;
	if( correction >= 1) correction = 1;

	exp *= correction;
#ifdef CGMSV_CONFIG
	exp *= cgmsvcf.battleskillexprate;
#endif

	//版氰摹 抛捞喉狼 ID
	skillexp_id = SKILL_getInt( skillindex, SKILL_S_TABLE);

	//老窜 犬牢
	if(ch->player_addon->Skill[i].SkillId == SKILL_getInt(skillindex, SKILL_ID)) {

		iexp = SKILLEXP_AmplifySkillExp( ch, skillindex, exp);

//		print("荐沥 EXP=%f ", exp);


		//角力肺 敲饭捞绢俊 馆康矫挪促
		ch->player_addon->Skill[i].SkillExp += iexp;

		//醚傈捧 胶懦 EXP甫 疵赴促
		ch->wu.player.BattleSkillExp += iexp;

		//胶懦 版氰摹甫 疵赴促
		pEntry[ index].skillexp[i] += iexp;

//		print("荐沥 饶 EXP=%d ", iexp);

//		print("skillexp=%d have=%d \n", pEntry[index].skillexp[i], i);

//		"(%s) 胶懦 版氰摹(+%d)(total=%d)",
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_179),
					CHAR_getUseName( ch ),
					pEntry[index].skillexp[i],
					ch->player_addon->Skill[i].SkillExp
		);

		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		//咯扁辑 胶懦狼 饭骇 UP眉农
		if(	BATTLE_SkillUpCheck(battleindex, ch, skillindex) == FALSE)
		{
			//胶懦 单捞磐甫 焊辰促
			CHAR_sendSkillDataOne( ch, i);

			return -1;
		}else{
			pEntry[index].skilllevelup[i] = 1;
		}

	}else{
//		print(", 胶懦狼 厘家啊 促福摆绢! ");
		return -1;
	}

	return 1;

}




//**************************************************************
//
//胶懦 版氰摹 掘绰促(鸥涝栏肺 唱传促)
//
//**************************************************************
int BATTLE_SkillPointTypeSeveralty(int battleindex , Char *ch)
{

	if(ch->i.WhichType == CHAR_TYPEPLAYER){
		//敲饭捞绢侩
		BATTLE_SkillPointAndForcePoint(battleindex, ch);
	}else{
		//利捞唱 局肯悼拱
	//	BATTLE_SkillPointAndForcePoint(battleindex, ch);
	}
	return 1;
}

//**************************************************************
//
//胶懦 版氰摹甫 掘绰促
//器胶 器牢飘甫 临牢促
//
//**************************************************************
int BATTLE_SkillPointAndForcePoint(int battleindex, Char *ch)
{
	BATTLE *pBattle;
	BATTLE_ENTRY *pEntry;
	char szBuffer[256];
	int side;
	int skillindex;
	int skill_id;
	int techindex;
	int techlevel;
	int index;
	int i;
	int skillexp_id;
	int maxlevel = 1;
	int nowmaxlevel;
	int jobid = ch->iu.player.Job;
	int iexp = 0;
	float exp = 10.0;
	int population;
	int worldplayernum;
	int reasonablepopulation;
	int ratio;
	float correction;

//	int skilllv_tbl[10]={10,20,30,40,50,60,70,80,90,100};


	//某腐磐狼 眉农
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	//利捞扼搁 包拌绝扁 锭巩俊 府畔
	if(ch->i.WhichType == CHAR_TYPEENEMY) return -1;
#ifdef PUK2
	// 扁福阁档 包拌绝扁 锭巩俊 府畔
	if(ch->i.WhichType == CHAR_TYPEGUILDMONSTER ) return -1;
#endif

	//胶懦固荤侩栏肺, 茄祈 噶阿栏肺 镭瘤 臼疽促
//	if(ch->w.BattleCom3 == -1 && ch->w.ModAmnesia == 0) return -1;
	if(ch->w.BattleCom3 == -1) return -1;

	//器胶 器牢飘啊 面盒且鳖 狼 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		return -2;
	}

	//器胶 器牢飘 家厚
	ch->i.ForcePoint = ch->i.ForcePoint - BATTLE_SKILL_UseFp(ch);

	//0 捞窍啊 瞪 啊瓷己篮 绝瘤父 老窜 眉农
	if(ch->i.ForcePoint < 0) ch->i.ForcePoint = 0;
#ifdef PET_RIDE_BATTLE
#if 1
	// 局肯悼拱?
	if( ch->i.WhichType == CHAR_TYPEPET ){
		// 愁捞 扁备矫狼 FP盒硅 贸府
		BATTLE_rideFpDivision( ch );
		// 绢路电, 咯扁辑 贸府甫 倒妨霖促
		return -1;
	}
#else
	if( ch->i.WhichType == CHAR_TYPEPET ){
		// 愁捞 扁备吝?
		if( CHAR_CheckCharPointer( ch->wu.pet.riderChar ) ){
			Char	*player = (Char *) ch->wu.pet.riderChar;
			int		fp, plfp, petfp;

			// 敲饭捞绢, 局肯悼拱 阿阿狼 荤侩 FP甫 夸备茄促
			fp = BATTLE_SKILL_UseFp( ch );
			plfp = fp / 2;
			petfp = fp - plfp;
			// 场荐绰 FP啊 腹篮 (盒)祈栏肺何磐 临牢促
			if( plfp != petfp ){
				if( player->i.ForcePoint > ch->wu.pet.petForcePoint ){
					int		tmp = petfp;

					petfp = plfp;
					plfp = tmp;
				}
			}
			// FP甫 临牢促
			player->i.ForcePoint -= plfp;
			ch->wu.pet.petForcePoint -= petfp;
			// FP啊 0阑 唱穿菌阑 版快绰, 捞力(国结) 茄祈栏肺何磐 弊 父怒阑 临牢促
			if( player->i.ForcePoint < 0 ){
				ch->wu.pet.petForcePoint = ch->i.ForcePoint;
				player->i.ForcePoint = 0;
			}
			if( ch->wu.pet.petForcePoint < 0 ){
				player->i.ForcePoint = ch->i.ForcePoint;
				ch->wu.pet.petForcePoint = 0;
			}
		}
		// 绢路电, 咯扁辑 贸府甫 倒妨霖促
		return -1;
	}
#endif
#else
	if(ch->i.WhichType == CHAR_TYPEPET) return -1;
#endif

	//掂郡吝篮 版氰摹绰 甸绢啊瘤 臼绰促
	if(BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P) return -1;

	// 叼滚弊 葛靛 罐阑 荐 乐促
	if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == FALSE ){
		//焊胶傈苞 绊沥利篮 胶懦 版氰摹绰 秒垫且 荐 绝促
		if(BattleArray[battleindex].flg & BATTLE_FLG_NO_SKILLEXP) return -1;
	}

	//醚傈捧 胶懦 版氰摹啊 MAX甫 逞绊 乐栏搁(磊) 救 凳
//	if(ch->wu.player.BattleSkillExp >= BATTLE_MAXSKILLEXP ) return -1;

	//胶懦 版氰蔼档 涝陛矫虐绊 乾促
	//磊脚 荤捞靛甫 掘绰促
	side = ch->w.BattleSide;

	pBattle = &BattleArray[ battleindex];
	pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

	//某腐磐狼 器牢磐肺何磐 殿废 锅龋俊 函券
	index = BATTLE_Index2No( battleindex, ch);

	//版氰摹 罐阑 荐 乐阑瘤 绢冻瘤
	if( pEntry[ index].flg & BENT_FLG_SKILLEXP ){
//		print("蜡皑, 傈捧 胶懦 版氰摹绰 罐阑 荐 绝促");
		snprintf( szBuffer, sizeof(szBuffer),
				TMSG(LANG_MSG_CHAR_BATTLE_C_181), ""
		);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );
		return -1;
	}

	//荤侩茄 胶懦篮 绢叼俊 乐促
	skillindex = TECH_getSkillIndex( TECH_getTechIndex( ch->w.BattleCom3) );
	skill_id = SKILL_getInt( skillindex, SKILL_ID);

	//胶懦 版氰摹涝.
	if(SKILL_getInt(skillindex, SKILL_EXPFLG) == 1) return -1;

	//胶懦狼 厘家甫 夯促
	for(i = 0; i < CHAR_MAXSKILLHAVE;i++){
		if(ch->player_addon->Skill[i].SkillId == skill_id){
			break;
		}
	}

	//绢蠢 巴俊档 甸绢嘎瘤 臼栏搁 捞惑窍促
	if( i == CHAR_MAXSKILLHAVE ){
	//	print("绢蠢 巴俊档 甸绢嘎瘤 臼绰促");
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_182), ""
		);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		return -1;
	}

#if 0
	print("JobId=%d ", jobid);
	print("ELAION=%d ", JOBS_getInt( JOBS_getJobsIndex(  jobid), JOBS_ELATIONSKILL) );
	print("RATE=%d ", JOBS_getInt( JOBS_getJobsIndex(  jobid), JOBS_RATE) );
	print("JOBS_CRYSTAL=%d ", JOBS_getEquipLevel( JOBS_getJobsIndex(  jobid), ITEM_CRYSTAL) );
	print("JOBS_SWORD=%d ", JOBS_getEquipLevel( JOBS_getJobsIndex(  jobid), ITEM_SWORD) );
	print("JOBS_STAFF=%d ", JOBS_getEquipLevel( JOBS_getJobsIndex(  jobid), ITEM_STAFF) );
#endif

	//流诀俊 狼秦 扁撅惑茄 胶懦 饭骇捞 促福促
	maxlevel = SKILL_getMaxLevel( skillindex, jobid);

//	print("MaxLevel=%d ", maxlevel);
//	print("PlayerLevel=%d \n", ch->player_addon->Skill[i].SkillLevel);

	if(maxlevel <= (ch->player_addon->Skill[i].SkillLevel)){
//		print("MAX 饭骇捞骨肺 版氰摹甫 临 荐 绝促");
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_180), ""
		);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		return -1;
	}

	techindex = TECH_getTechIndex( ch->w.BattleCom3);
	techlevel = TECH_getInt(techindex, TECH_NECESSARYLV);

	nowmaxlevel = ch->player_addon->Skill[i].SkillLevel;

	if(nowmaxlevel <= 0) nowmaxlevel = 1;
	if(techlevel <= 0) techlevel = 1;

	//EXP 拌魂(傈巩, 捞劳 力寇窍促)
	exp = exp * ( (float) techlevel / nowmaxlevel);

	// 胶懦 厚啦 焊沥 拌荐狼 秒垫
	// 捞 胶懦阑 啊瘤绊 乐绰 傈技拌狼 牢盔荐
	population = SKILL_getPopulation( skillindex);
	if( population <= 0 ) population = 1;	// 1 捞窍绰 绝澜
	// 技拌狼 牢备
	worldplayernum = CHAR_getWorldPlayerMaxNum();
	if( worldplayernum <= 0 ) worldplayernum = 1;	// 1 捞窍绰 绝澜
	// 厚啦
	ratio = SKILL_getInt( skillindex, SKILL_RATIOPOPULATION);
	// 利沥 牢备
	reasonablepopulation = worldplayernum * (ratio/1000.0);
	// 焊沥 拌荐
	correction = reasonablepopulation / (float) population;
	if( correction < 0.1 ) correction = 0.1;
	if( correction >= 1) correction = 1;

	exp *= correction;
#ifdef CGMSV_CONFIG
	exp *= cgmsvcf.battleskillexprate;
#endif

	//版氰摹 抛捞喉狼 ID
	skillexp_id = SKILL_getInt( skillindex, SKILL_S_TABLE);

	//老窜 犬牢
	if(ch->player_addon->Skill[i].SkillId == SKILL_getInt(skillindex, SKILL_ID)){

//		print("荐沥 傈EXP=%d \n", exp);
		iexp = SKILLEXP_AmplifySkillExp( ch , skillindex, exp);

//		print("荐沥 饶 EXP=%d \n", exp);

		//角力肺 敲饭捞绢俊 馆康矫挪促
		ch->player_addon->Skill[i].SkillExp += iexp;

		//醚傈捧 胶懦 EXP甫 疵赴促
		ch->wu.player.BattleSkillExp += iexp;

		//胶懦 版氰摹甫 疵赴促
		pEntry[ index].skillexp[i] += iexp;


//		print("skillexp=%d have=%d \n", pEntry[index].skillexp[i], i);

//		"(%s) 胶懦 版氰摹(+%d)(total=%d)",
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_179),
					CHAR_getUseName( ch ),
					pEntry[index].skillexp[i],
					ch->player_addon->Skill[i].SkillExp
		);

		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );
//		SYSTEMMSG( ch, szBuffer);
		//咯扁辑 胶懦狼 饭骇 UP眉农
		if(	BATTLE_SkillUpCheck(battleindex, ch, skillindex) == FALSE)
		{
			//胶懦 单捞磐甫 焊辰促
			CHAR_sendSkillDataOne( ch, i);
//			CHAR_sendTechData( ch, i);

			return -1;
		}else{
			pEntry[index].skilllevelup[i] = 1;
		}

	}else{
//		print("skillHaveErr");
		return -1;
	}

	return 1;
}

#if 0
//**************************************************************
//
//胶懦 版氰摹甫 掘绰促(利苞 局肯悼拱侩)
//器胶 器牢飘甫 临咯?
//
//**************************************************************
int BATTLE_SkillPointAndForcePointPetEnemy(int battleindex, Char *ch)
{
	BATTLE *pBattle;
	BATTLE_ENTRY *pEntry;
	char szBuffer[256];
	int side;
	int skillindex;
	int skill_id;
	int index;
	int i;
	int skillexp_id;
	int maxlevel;
	int jobid = ch->iu.player.Job;


	//某腐磐狼 眉农
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	//利捞扼搁 包拌绝扁 锭巩俊 府畔
	if(ch->i.WhichType == CHAR_TYPEENEMY) return -1;

	//器胶 器牢飘啊 面盒且鳖 狼 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch) ){
//	if(ch->i.ForcePoint <TECH_getInt(TECH_getTechIndex(ch->w.BattleCom3), TECH_FORCEPOINT)){
//		print("器胶 器牢飘啊 何练窍促");
		return -1;
	}

	//器胶 器牢飘 家厚
	ch->i.ForcePoint = ch->i.ForcePoint - BATTLE_SKILL_UseFp(ch);

	//0 捞窍啊 瞪 啊瓷己篮 绝瘤父 老窜 眉农
	if(ch->i.ForcePoint < 0) ch->i.ForcePoint = 0;

	//掂郡吝篮 版氰摹绰 甸绢啊瘤 臼绰促
	if(BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P) return -1;

	// 叼滚弊 葛靛扼搁 冻绢瘤瘤 臼绰促.
	if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) == FALSE ){
		//焊胶傈苞 绊沥利篮 胶懦 版氰摹绰 秒垫且 荐 绝促
		if(BattleArray[battleindex].flg & BATTLE_FLG_NO_SKILLEXP) return -1;
	}
	//醚傈捧 胶懦 版氰摹啊 MAX甫 逞绊 乐栏搁(磊) 救 凳
//	if(ch->wu.player.BattleSkillExp >= BATTLE_MAXSKILLEXP ) return -1;


	//胶懦 版氰蔼档 涝陛矫虐绊 乾促

	//磊脚 荤捞靛甫 掘绰促
	side = ch->w.BattleSide;

	pBattle = &BattleArray[ battleindex];
	pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

	//某腐磐狼 器牢磐肺何磐 殿废 锅龋俊 函券
	index = BATTLE_Index2No( battleindex, ch);

	//版氰摹 罐阑 荐 乐阑瘤 绢冻瘤
	if(pEntry[ index].flg & BENT_FLG_SKILLEXP){
//		print("蜡皑, 傈捧 胶懦 版氰摹绰 罐阑 荐 绝促");
		snprintf( szBuffer, sizeof(szBuffer),
				TMSG(LANG_MSG_CHAR_BATTLE_C_181)
		);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		return -1;
	}

	//荤侩茄 胶懦篮 绢叼俊 乐促
	skillindex = TECH_getSkillIndex( TECH_getTechIndex( ch->w.BattleCom3) );
	skill_id = SKILL_getInt( skillindex, SKILL_ID);

	//胶懦狼 厘家甫 夯促
	for(i = 0; i < CHAR_MAXSKILLHAVE;i++){
		if(ch->player_addon->Skill[i].SkillId == skill_id){
			break;
		}
	}

	//绢蠢 巴俊档 甸绢嘎瘤 臼栏搁 捞惑窍促
	if( i == CHAR_MAXSKILLHAVE)
	{
//		print("绢蠢 巴俊档 甸绢嘎瘤 臼绰促");
		snprintf( szBuffer, sizeof(szBuffer),
				TMSG(LANG_MSG_CHAR_BATTLE_C_182));

		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		return -1;
	}


	//流诀俊 狼秦 扁撅惑茄 胶懦 饭骇捞 促福促
	maxlevel = SKILL_getMaxLevel( skillindex, jobid);


//	print("MaxLevel=%d ", maxlevel);
//	print("PlayerLevel=%d \n", ch->player_addon->Skill[i].SkillLevel);

	if(maxlevel <= (ch->player_addon->Skill[i].SkillLevel)){
//		print("MAX 饭骇捞骨肺 版氰摹甫 临 荐 绝促");
		snprintf( szBuffer, sizeof(szBuffer),
				TMSG(LANG_MSG_CHAR_BATTLE_C_180)
				);
		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );

		return -1;
	}

	//版氰摹 抛捞喉狼 ID
	skillexp_id = SKILL_getInt( skillindex, SKILL_S_TABLE);

	//老窜 犬牢
	if(ch->player_addon->Skill[i].SkillId == SKILL_getInt(skillindex, SKILL_ID)) {
		//角力肺 敲饭捞绢俊 馆康矫挪促
		ch->player_addon->Skill[i].SkillExp++;

		//醚傈捧 胶懦 EXP甫 疵赴促
		ch->wu.player.BattleSkillExp++;

		//胶懦 版氰摹甫 疵赴促
		pEntry[ index].skillexp[i] += 1;

//		print("skillexp=%d have=%d \n", pEntry[index].skillexp[i], i);

//		"(%s) 胶懦 版氰摹(+%d)(total=%d)",
		snprintf( szBuffer, sizeof(szBuffer),
					TMSG(LANG_MSG_CHAR_BATTLE_C_179),
					CHAR_getUseName( ch ),
					pEntry[index].skillexp[i],
					ch->player_addon->Skill[i].SkillExp
		);

		BATTLE_talkToCli( ch, szBuffer, CHAR_COLORCYAN );
//		SYSTEMMSG( ch, szBuffer);

		//咯扁辑 胶懦狼 饭骇 UP眉农
		if(	BATTLE_SkillUpCheck(battleindex, ch, skillindex) == FALSE)
		{
			return -1;
		}else{
			pEntry[index].skilllevelup[i] = 1;
		}

	}else{
//		print("skillHaveErr");
		return -1;
	}

	return 1;
}
#endif

//*****************************************************
//bleed狼 胶懦阑 爱绊 乐阑鳖
//
//馆券蔼：己傍： 割锅掳狼 厘家牢啊
//		  角菩:  -1
//*****************************************************
int BATTLE_Breed_check(Char *ch, Char *pch)
{
	int i , j;
	int techId, array;
	int techindex;
	int work = 1;
	char *option , *p;
	float exp;
#ifdef NEW_JOB_CHANGE
	int		skillindex;
	int		usablelv;
#endif


	//局肯悼拱篮 版氰摹甫 秒垫窍绊 乐促绰 巴捞 炼扒
	if(pch->w.GetExp <= 0) return -1;

	//胶懦狼 厘家甫 夯促
	for(i = 0; i < CHAR_MAXSKILLHAVE; i++ ) {
		if(ch->player_addon->Skill[i].SkillId == BATTLE_PARTICULAR_SKILL_BREED)
		{
			break;
		}
	}


	//啊瘤瘤 臼疽促
	if(i == CHAR_MAXSKILLHAVE) return -1;

#ifdef NEW_JOB_CHANGE
	// 瘤陛狼 老磊府肺 绢蠢 饭骇鳖瘤 荤侩且 荐 乐阑鳖?
	skillindex = SKILL_getSkillIndex( BATTLE_PARTICULAR_SKILL_BREED );
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
#endif

	//啊瘤绊 乐带, 扁贱狼 饭骇篮 倔付牢啊?
    for (j = CHAR_MAXTECHHAVE - 1; j > -1; j--) {
		techId = CHAR_getTechId( ch, i, j);
		if( techId == -1 ) continue;

		/* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
		techindex = TECH_getTechIndex( techId);
//		print("teckId=%d \n", techId );

		if(TECH_TechLevelCheck(ch, TECH_getInt( techindex, TECH_NECESSARYLV)) == 0) continue;
#ifdef NEW_JOB_CHANGE
		// 荤侩且 荐 乐绰 饭骇狼 扁贱捞唱 眉农
		if( TECH_getInt( techindex, TECH_NECESSARYLV ) > usablelv ){
			// 荤侩且 荐 绝促
			continue;
		}
#endif

		//格窍 乐绊 犬牢
		if(TECH_getInt(techindex , TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_BREED) continue;

		array  = TECH_getTechIndex( techId);
		option =  TECH_getChar( array, TECH_OPTION);

		if(option == NULL) return -1;

		//饭捞飘
		p = strstr(option, "AR:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &work);
/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		work = CHARSUIT_getOptionValue(ch, array, "AR:", work);
#endif
/*ADD END*/
		}
//	print("BREED : j =%d  Level=%d \n", j, TECH_getInt( techindex, TECH_NECESSARYLV) );

		//绢蠢 版氰摹甫 函版矫难 霖促
//		print("work=%d old_EXP=%d ", work, pch->w.GetExp);
		exp = (float) work * 0.01 * pch->w.GetExp ;
		pch->w.GetExp = pch->w.GetExp + exp;
//		print("new_EXP=%d \n", pch->w.GetExp);


		return i;
	}

	return -1;
}

//*****************************************
//炼背狼 胶懦阑 爱绊 乐阑鳖
//
// flg: 0: SKILL位置
//      1: TECH地点
//
//馆券蔼；己傍:	割锅掳狼 厘家牢啊
//		  角菩:	-1
//*****************************************
int BATTLE_Train_check(Char *ch, Char *pch, int flg) {
    int i;
    int j;
    int techId;
    int techindex;
#ifdef NEW_JOB_CHANGE
    int skillindex;
    int usablelv;
#endif


    //局肯悼拱捞 版氰摹甫 秒垫窍绊 乐促绰 巴捞 炼扒
    if (pch->w.GetExp <= 0 && flg == 0) return -1;

    //胶懦狼 厘家甫 夯促
    for (i = 0; i < CHAR_MAXSKILLHAVE; i++) {
        if (ch->player_addon->Skill[i].SkillId == BATTLE_PARTICULAR_SKILL_TRAIN) {
            break;
        }
    }

    //啊瘤瘤 臼疽促
    if (i == CHAR_MAXSKILLHAVE) return -1;
#ifdef NEW_JOB_CHANGE
    // 瘤陛狼 老磊府肺 绢蠢 饭骇鳖瘤 荤侩且 荐 乐阑鳖?
    skillindex = SKILL_getSkillIndex(BATTLE_PARTICULAR_SKILL_BREED);
    usablelv = SKILL_getMaxLevel(skillindex, ch->iu.player.Job);
#endif

#if 1
    //啊瘤绊 乐带, 扁贱狼 饭骇篮 倔付牢啊?
    for (j = CHAR_MAXTECHHAVE - 1; j > -1; j--) {
        techId = CHAR_getTechId(ch, i, j);
        if (techId == -1) continue;

        /* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
        techindex = TECH_getTechIndex(techId);
//		print("teckId=%d \n", techId );

        int techLv = TECH_getInt(techindex, TECH_NECESSARYLV);
        if (TECH_TechLevelCheck(ch, techLv) == 0) continue;
#ifdef NEW_JOB_CHANGE
        // 荤侩且 荐 乐绰 饭骇狼 扁贱捞唱 眉农
        if (techLv > usablelv) {
            // 荤侩且 荐 绝促
            continue;
        }
#endif

        //格窍 乐绊 犬牢
        if (TECH_getInt(techindex, TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_TRAIN) continue;

//		print("TRAIN : j =%d  Level=%d \n", j, TECH_getInt( techindex, TECH_NECESSARYLV) );
        if (flg == 0) {
            return i;
        } else {
            int job = SKILL_getInt(skillindex, SKILL_JOBS);
            if ((job == ch->iu.player.JobAncestry || job == ch->iu.player.Job) && cgmsvcf.trainJobRate != 1) {
                return cgmsvcf.trainJobRate * techLv;
            }
            return techLv;
        }
    }
#endif
    return -1;
}

//*****************************************
//鞠混狼 胶懦阑 爱绊 乐阑鳖
//
//flg : 0:胶懦狼 厘家
//      1:扁贱狼 厘家
//
//馆券蔼；己傍:	割锅掳狼 厘家牢啊
//		  角菩:	-1
//*****************************************
int BATTLE_Assasin_check(Char *ch, int flg)
{
	int i;
	int j;
	int techId;
	int techindex;
#ifdef NEW_JOB_CHANGE
	int		skillindex;
	int		usablelv;
#endif

    //囱磊客 捍荤扼绊 窍绰 巴捞 炼扒
    if ((hashseti_has(cgmsvcf.assassinJobs, ch->iu.player.JobAncestry) ||
         hashseti_has(cgmsvcf.assassinJobs, ch->iu.player.Job))) {
    } else {
        return -1;
    }

	//利阑 锭啡促绰 巴捞 炼扒
	if(	ch->w.BattleFlg & CHAR_BATTLEFLG_ATTACKFLG){
//		print("炼扒阑 盲奎促.");
	}else{
		return -1;
	}

	//胶懦狼 厘家甫 夯促
	for(i = 0; i < CHAR_MAXSKILLHAVE; i++ ) {
		if(ch->player_addon->Skill[i].SkillId == BATTLE_PARTICULAR_SKILL_ASSASSIN)
		{
			break;
		}
	}

	//啊瘤瘤 臼疽促
	if(i == CHAR_MAXSKILLHAVE) return -1;
#ifdef NEW_JOB_CHANGE
	// 瘤陛狼 老磊府肺 绢蠢 饭骇鳖瘤 荤侩且 荐 乐阑鳖?
	skillindex = SKILL_getSkillIndex( BATTLE_PARTICULAR_SKILL_BREED );
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
#endif

#if 1
	//啊瘤绊 乐带, 扁贱狼 饭骇篮 倔付牢啊?
    for (j = CHAR_MAXTECHHAVE - 1; j > -1; j--) {
		techId = CHAR_getTechId( ch, i, j);
		if( techId == -1 ) continue;

		/* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
		techindex = TECH_getTechIndex( techId);
//		print("teckId=%d \n", techId );

		if(TECH_TechLevelCheck(ch, TECH_getInt( techindex, TECH_NECESSARYLV)) == 0) continue;
#ifdef NEW_JOB_CHANGE
		// 荤侩且 荐 乐绰 饭骇狼 扁贱捞唱 眉农
		if( TECH_getInt( techindex, TECH_NECESSARYLV ) > usablelv ){
			// 荤侩且 荐 绝促
			continue;
		}
#endif

		//格窍 乐绊 犬牢
		if(TECH_getInt(techindex , TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_ASSASSIN) continue;

//		print("TRAIN : j =%d  Level=%d \n", j, TECH_getInt( techindex, TECH_NECESSARYLV) );
		if(flg == 0){
			return i;
		}else{
			return j;
		}
	}

#endif

	return -1;


}


//*************************************************
//付过 豪窍绊 吧府绊 乐绢 眉农
//
//馆券蔼：
//		TRUE:	吧府绊 乐促
//		FALSE:	吧府瘤 臼促
//*************************************************
BOOL BATTLE_Skill_SilenceCheck(int battleindex , int attackerBid)
{
	char szBuffer[256];
	Char *ch;

	//器牢磐 秒垫
	ch = BATTLE_No2Bid(battleindex, attackerBid);

	if( CHAR_CheckCharPointer( ch ) == FALSE) return 0;

	//厘家俊 付过 豪窍绊啊 吧妨 乐阑鳖?
	if(BattleArray[battleindex].field_att & BC_FIELD_FLAG_SILENCE) {

		//厘家俊 付过 豪窍绊啊 吧妨 乐阑鳖?
		if(BattleArray[battleindex].att_count <= 0){
			return TRUE;
		}

		BATTLESTR_ADD( "FF|");
		snprintf( szBuffer, sizeof(szBuffer),
				 "CHT|14|%X|3|0|", CHAR_COLORYELLOW
				 );
		BATTLESTR_ADD( szBuffer);
	//	BATTLESTR_TAIL(szBuffer);

//	 "%s 狼 付过篮 捞惑茄 塞栏肺 结 瘤况脸促.|",
		snprintf( szBuffer, sizeof(szBuffer),
				 TMSG(LANG_MSG_CHAR_BATTLE_C_195),
				 ch->c.Name
				 );
		BATTLESTR_ADD( szBuffer);
	}else{
		return TRUE;
	}

	return FALSE;

}


//*************************************************
//磊气
//*************************************************
int BATTLE_Bomb(int battleindex, int attackNo)
{
	Char *ch;
	int toNo;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackNo );
		//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	toNo = ch->w.BattleCom2;

	//荤捞靛绰 绢蠢 率?
	if(attackNo < SIDE_OFFSET){
		toNo = TARGET_SIDE_1;
	}else{
		toNo = TARGET_SIDE_0;
	}


	//磊气
	BATTLE_MultiBomb( battleindex, attackNo, toNo, 0, 0 );

	return 1;

}

//*************************************************
//酒胶农俊捞农
//*************************************************
int BATTLE_EarthQuake(int battleindex, int attackNo)
{
	Char *ch;
	int toNo;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackNo );

	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	toNo = ch->w.BattleCom2;

	//荤捞靛绰 绢蠢 率?
	if(attackNo < SIDE_OFFSET){
		toNo = TARGET_SIDE_1;
	}else{
		toNo = TARGET_SIDE_0;
	}


	//酒胶农俊捞农
	BATTLE_MultiEarthQuake( battleindex, attackNo, toNo, 0, 0 );

	return 1;


}

//*************************************************
//荤农府其捞胶
//*************************************************
int BATTLE_Sacrifice(int battleindex, int attackNo)
{
	Char *ch;
	int toNo;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackNo );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	toNo = ch->w.BattleCom2;

	//荤捞靛绰 绢蠢 率?
	if(attackNo < SIDE_OFFSET){
		toNo = TARGET_SIDE_0;
	}else{
		toNo = TARGET_SIDE_1;
	}

	// 魂 力拱阑 官魔
	BATTLE_MultiSacrifice( battleindex, attackNo, toNo, 0, 0 );

	return 1;

}


//*************************************************
//溜荤
//*************************************************
int BATTLE_Death(int battleindex, int attackNo)
{
	Char *ch;
	int toNo;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackNo );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	toNo = ch->w.BattleCom2;

	// 溜荤
//	BATTLE_MultiDeath( battleindex, attackNo, toNo,
//		kind, power, per, SPR_heal, HealedEffect );

	BATTLE_MultiDeath( battleindex, attackNo, toNo, 0, 0 );

	return 1;

}

#if 0
//*************************************************
//俊呈瘤 靛饭牢
//*************************************************
int BATTLE_EnergyDrain(int battleindex, int attackNo)
{
	Char *ch;
	int toNo;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackNo );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	toNo = ch->w.BattleCom2;

	// 溜荤
//	BATTLE_MultiDeath( battleindex, attackNo, toNo,
//		kind, power, per, SPR_heal, HealedEffect );

	BATTLE_MultiEnergyDrain( battleindex, attackNo, toNo, 0, 0 );

	return 1;

}
#endif

//----------------------------------------------------------------
//胶抛捞磐胶 捞惑 傍拜
//----------------------------------------------------------------
BOOL BATTLE_StatusChange(int battleindex, int attackerBid)
{
#if 1
	int		i;
	int		flg = 0;
	int		toNo;
	int		ToList[SIDE_OFFSET*4+1];
	int		damage = 0, success = 0, array , a;
	char	szCommand[256] = {0};
	char	*p;
	char	*option;
	int		skillid;
	Char	*ch;
	Char	*toch;
	int		SuccessBack = 0;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if( CHAR_CheckCharPointer( ch ) == FALSE ) return FALSE;

	//鸥百
	toNo = ch->w.BattleCom2;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch) ){
		//器胶 何练
		return FALSE;
	}

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if( BATTLE_SkillPointAndForcePoint( battleindex, ch ) != -1 ){
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	if(ToList[0] != -1){
		//某腐磐 扼牢 累己
		snprintf( szCommand, sizeof(szCommand),
#ifdef	VERSION_TW
			"SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
			"SKL|s%X|f%X|%X|a%X|",
#endif
				ch->w.BattleCom3,
				BATTLE_SKILL_UseFp(ch),
				weponNo,
				attackerBid
		);
		BATTLESTR_ADD( szCommand );
		//付豪矫啊 甸绊 乐绢?
		if(BATTLE_Skill_SilenceCheck(battleindex, attackerBid) == FALSE){
			return FALSE;
		}
#ifdef PUK2_SKILL
		// 厘寒 乐绢?
		if( BATTLE_Skill_BarrierCheck( battleindex, attackerBid, ToList[0] ) ){
			return FALSE;
		}
#endif
		//疙己狼 眉农
		if( ch->i.WhichType == CHAR_TYPEPLAYER ){
			if( ch->iu.player.JobAncestry == 80 ){
				//林贱荤啊 胶抛 付过阑 荤侩窍搁 疙己 坷弗促
				BATTLE_FameCheck( battleindex, attackerBid, attackerBid , 7 );
			}
		}
	}else{
		return FALSE;
	}

	/* 胶懦 包访狼 沥焊 秒垫 */
	// 胶懦 ID
	skillid = TECH_getInt( TECH_getTechIndex( ch->w.BattleCom3 ), TECH_SKILLID );
	// 己傍啦苞 瘤加 矫埃
	array  = TECH_getTechIndex(ch->w.BattleCom3);
	option =  TECH_getChar(array, TECH_OPTION);
	if( option != NULL ){
		p = strstr(option,"SR:");
		if( p != NULL ){
			sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "SR:", a);
#endif
/*ADD END*/
			success = a;
		}
		// 雀荐
		p = strstr( option, "CH:" );
		if( p != NULL ){
			sscanf( (p+3), "%d", &a );
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "CH:", a);
#endif
/*ADD END*/
			damage = a;
		}
	}
	// 汗荐 鸥百侩栏肺 扁夯 己傍啦阑 焊粮
	SuccessBack = success;

    int md = Tech_getOptionByIndex(array, "MD:", -1);
    md = CHARSUIT_getOptionValue(ch, array, "MD:", md);
    if (md == 255) {
        md = RAND(0, 5);
    }
    // 胶懦 ID喊肺 贸府甫 盒扁
    skillid = Tech_getOptionByIndex(array, "SK:", -1);
    skillid = CHARSUIT_getOptionValue(ch, array, "SK:", skillid);
    if (skillid < 0) {
        skillid = TECH_getInt(TECH_getTechIndex(ch->w.BattleCom3), TECH_SKILLID);
    }
	// 傈鸥百阑 急琶
	for( i = 0 ; ToList[i] != -1 ; i++ ){
		success = SuccessBack;
		flg = 0;

		// 措惑狼 某腐磐 器牢磐 秒垫
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
#ifdef PUK2
		// 府滚胶 公利?
		if( toch->w.RebirthFlag == 2 ){
			// 府滚胶 惯悼 公利阑 家侥
			flg |= BM_FLAG_REBIRTH_GUARD;
		}else
#endif
		//国结 惑怕 捞惑窍霸 吧妨乐绰 版快绰 救蹬
		if( toch->w.ModPoison <= 0
		 && toch->w.ModSleep <= 0
		 && toch->w.ModStone <= 0
		 && toch->w.ModDrunk <= 0
		 && toch->w.ModConfusion <= 0
		 && toch->w.ModAmnesia <= 0 )
		{
			switch( skillid ){
				// 刀
				case 32:
				case 38:
				case 44:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 0, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 泪
				case 33:
				case 39:
				case 45:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 1, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 籍蜡拳切
				case 34:
				case 40:
				case 46:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 2, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 秒扁
				case 35:
				case 41:
				case 47:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 3, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 去鄂
				case 36:
				case 42:
				case 48:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 4, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 噶阿
				case 37:
				case 43:
				case 49:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, md >= 0 ? md : 5, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				default:
                    if (md >= 0 && BATTLE_checkStatusChange(toch, md, success, damage)) {
                        flg |= BM_FLAG_SUCCESS;
                    }
					break;
			}
		}

		//酒公巴档 吧府瘤 臼促.磊 吧媚扼
		snprintf( szCommand, sizeof(szCommand),
		"%X|f%X|", ToList[i], flg);
		BATTLESTR_ADD( szCommand );
	}
#else
	int i , j, k = 1, amn = 0;
	int flg = 0;
	int toNo;
	int ToList[SIDE_OFFSET*2+1];
	int damage = 0, success = 0, resist = 0, array , a;
	int point= 0 , action = 0;
	char szCommand[256] = {0};
	char szBuffer[256] = {0};
	int Swork = 0;
	char *p;
	char *option;
	int skillid;
	float work = 0;
	Char *ch;
	Char *toch;
	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;
	int SuccessBack = 0;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//鸥百
	toNo = ch->w.BattleCom2;

	skillid = TECH_getInt(TECH_getTechIndex(ch->w.BattleCom3), TECH_SKILLID);

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch) ){
		//器胶 何练
		return FALSE;
	}

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if( BATTLE_SkillPointAndForcePoint( battleindex, ch ) != -1 ){
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);

	if(ToList[0] != -1){
		//某腐磐 扼牢 累己
		snprintf( szCommand, sizeof(szCommand),
#ifdef	VERSION_TW
			"SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
			"SKL|s%X|f%X|%X|a%X|",
#endif
				ch->w.BattleCom3,
				BATTLE_SKILL_UseFp(ch),
				weponNo,
				attackerBid
		);
		BATTLESTR_ADD( szCommand );
		//付豪矫啊 甸绊 乐绢?
		if(BATTLE_Skill_SilenceCheck(battleindex, attackerBid) == FALSE){
			return FALSE;
		}
#ifdef PUK2_SKILL
		// 厘寒 乐绢?
		if( BATTLE_Skill_BarrierCheck( battleindex, attackerBid, ToList[0] ) ){
			return FALSE;
		}
#endif
		//疙己狼 眉农
		if( ch->i.WhichType == CHAR_TYPEPLAYER ){
			if( ch->iu.player.JobAncestry == 80 ){
				//林贱荤啊 胶抛 付过阑 荤侩窍搁 疙己 坷弗促
				BATTLE_FameCheck( battleindex, attackerBid, attackerBid , 7 );
			}
		}
	}else{
		return FALSE;
	}

	/* 己傍啦阑 夸备茄促 */
	array  = TECH_getTechIndex(ch->w.BattleCom3);
	option =  TECH_getChar(array, TECH_OPTION);
	p = strstr(option,"SR:");
	if( p != NULL ){
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "SR:", a);
#endif
/*ADD END*/
		success = a;
	}
	//汗荐 鸥百侩栏肺 扁夯 己傍啦阑 焊粮
	SuccessBack = success;

	// 傈鸥百阑 急琶
	for( i = 0 ; ToList[i] != -1 ; i++ ){
		success = SuccessBack;

		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		flg = 0;
#ifdef PUK2
		// 府滚胶 公利?
		if( toch->w.RebirthFlag == 2 ){
			// 府滚胶 惯悼 公利阑 家侥
			flg |= BM_FLAG_REBIRTH_GUARD;
		}else
#endif
		//国结 惑怕 捞惑窍霸 吧妨乐绰 版快绰 救蹬
		if( toch->w.ModPoison <= 0
		 && toch->w.ModSleep <= 0
		 && toch->w.ModStone <= 0
		 && toch->w.ModDrunk <= 0
		 && toch->w.ModConfusion <= 0
		 && toch->w.ModAmnesia <= 0 )
		{
			//雀荐
			p = strstr( option, "CH:" );
			if( p != NULL ){
				sscanf( (p+3), "%d", &a );
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, array, "CH:", a);
#endif
/*ADD END*/
				damage = a;
			}
#if 1
			switch( skillid ){
				// 刀
				case 32:
				case 38:
				case 44:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 0, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 泪
				case 33:
				case 39:
				case 45:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 1, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 籍蜡拳切
				case 34:
				case 40:
				case 46:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 2, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 秒扁
				case 35:
				case 41:
				case 47:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 3, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 去鄂
				case 36:
				case 42:
				case 48:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 4, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				// 噶阿
				case 37:
				case 43:
				case 49:
					// 惑怕 捞惑 眉农
					if( BATTLE_checkStatusChange( toch, 5, success, damage ) ){
						flg |= BM_FLAG_SUCCESS;
					}
					break;

				default:
					break;
			}
#else
			switch( skillid ){
				case 32:
				case 38:
				case 44:
					//刀包拌
					Swork = success * (toch->w.Poison * 0.01);
					success -= Swork;
					if(success < 0 ) success = 0;

//					print("%s(%d)绰 刀(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage)
					//郴己 胶懦阑 爱绊 乐阑鳖
					resist = BATTLE_StatusResistance( toch, 0, &point, &action );

					if( resist == 0 ){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModPoison = damage;
							//"%s(%d)绰 刀(%d)俊 吧赴 \n"
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_156),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );
						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("PO_success=%d ", success);
						success = success - resist;
						if(success < 0) success = 0;

						// 刀阑 侥牢啊?
						if( RAND( 0, 99 ) < success ){
							// 侥
							flg |= BM_FLAG_SUCCESS;
							// 郴己俊 狼茄 瘤加 矫埃狼 焊沥
							work = (float)(100 + action) * 0.01;
							damage = (float) damage * work;
							// 瓤苞 矫埃捞 0 捞窍狼 版快绰, 瓤苞啊 绝菌带 秒鞭栏肺
							if( damage > 0 ){
								// 刀狼 瘤加 矫埃 汲沥
								toch->w.ModPoison = damage;

								// 傈捧侩狼 皋矫瘤
								snprintf( szBuffer, sizeof(szBuffer),
									  	TMSG(LANG_MSG_CHAR_BATTLE_C_156),
									  	toch->c.Name, ToList[i], damage );
								BATTLE_BroadCast( battleindex,
											  	szBuffer,
											  	CHAR_COLORYELLOW );
								//"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 刀(%d)栏肺 官诧 \n
								snprintf( szBuffer, sizeof(szBuffer),
									  	TMSG(LANG_MSG_CHAR_BATTLE_C_157),
									  	toch->c.Name, ToList[i], damage );
								BATTLE_BroadCast( battleindex,
											  	szBuffer,
											  	CHAR_COLORYELLOW );
							}
						}
					}

				break;

				case 33:
				case 39:
				case 45:
					//泪包拌
//					print("%s(%d)绰 泪(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

					Swork = success * (toch->w.Sleep * 0.01);
					success -= Swork;

					if(success < 0 ) success = 0;


					//郴己 胶懦阑 爱绊 乐阑鳖
					resist = BATTLE_StatusResistance( toch , 1 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//泪己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModSleep = damage;
							//"%s(%d)绰 泪(%d)俊 吧赴 \n",
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_158),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 100) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_158),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

							damage = (float) damage * work;
							toch->w.ModSleep = damage;

							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 泪(%d)栏肺 官诧 \n"
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_159),
								toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

						}
					}

				break;

				case 34:
				case 40:
				case 46:
					//籍蜡拳切 包拌
//					print("%s(%d)绰 籍蜡拳切(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

					Swork = success * (toch->w.Stone * 0.01);
					success -= Swork;

					if(success < 0 ) success = 0;

					//郴己 胶懦阑 爱绊 乐阑鳖
					resist = BATTLE_StatusResistance( toch , 2 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModStone = damage;

							//"%s(%d)绰 籍蜡拳切(%d)俊 吧赴 \n",
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_160),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );
						}

					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 100) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_160),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

							damage = (float) damage * work;
							toch->w.ModStone = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 籍蜡拳切(%d)栏肺 官诧 \n",
							snprintf( szBuffer, sizeof(szBuffer),

								toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );


						}
					}
				break;

				case 35:
				case 41:
				case 47:
					//秒扁 包拌
//					print("%s(%d)绰 秒扁(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

					Swork = success * (toch->w.Drunk * 0.01);
					success -= Swork;

					if(success < 0 ) success = 0;



					//郴己 胶懦阑 爱绊 乐阑鳖
					resist = BATTLE_StatusResistance( toch , 3 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModDrunk = damage;
							//"%s(%d)绰 秒扁(%d)俊 吧赴 \n",
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_162),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );


						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 100) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_162),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );


							damage = (float) damage * work;
							toch->w.ModDrunk = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


							snprintf( szBuffer, sizeof(szBuffer),
								TMSG(LANG_MSG_CHAR_BATTLE_C_163),
								toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

						}
					}
				break;

				case 36:
				case 42:
				case 48:
					//去鄂 包拌
//					print("%s(%d)绰 去鄂(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);
					resist = BATTLE_StatusResistance( toch , 4, &point, &action);

					Swork = success * (toch->w.Confusion * 0.01);
					success -= Swork;
					if(success < 0 ) success = 0;


					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModConfusion = damage;

//							"%s(%d)绰 去鄂(%d)俊 吧赴 \n",
							snprintf( szBuffer, sizeof(szBuffer),

							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 100) < success) {
						//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_164),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );


							damage = (float) damage * work;
							toch->w.ModConfusion = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);

							//"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 去鄂(%d)栏肺 官诧 \n",
							snprintf( szBuffer, sizeof(szBuffer),
								TMSG(LANG_MSG_CHAR_BATTLE_C_165),
								toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );
						}
					}

				break;

				case 37:
				case 43:
				case 49:
					//噶阿 包拌
//					print("%s(%d)绰 噶阿(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);
					pBattle = &BattleArray[battleindex];


					//荤捞靛甫 掘绰促
					if(ToList[i]  < 10){k = 0;}
					else if(ToList[i] > 10){k = 1;}


					pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯

					j = BATTLE_Index2No(battleindex, toch);

					//鞍篮 荤恩牢啊
					if(pEntry[j].entrychara != toch) break;
					//镭绰 厘家甫 茫绰促
					amn = BATTLE_AmnesiaPoint( toch);
//					print("name=%s amn = %d \n", toch->c.Name, amn);

					if(amn == 0){//角菩
						break;
					}

					Swork = success * (toch->w.Amnesia * 0.01);
					success -= Swork;
					if(success < 0 ) success = 0;


					//郴己阑 爱绊 乐阑鳖?
					resist = BATTLE_StatusResistance( toch , 5 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 100) < success) {
							//镭绰 厘家甫 扁撅
							toch->w.ModAmnesia = amn;

							//雀荐甫 扁撅
							pEntry[j].Amnesia = damage;
							flg |= BM_FLAG_SUCCESS;

//							"%s(%d)绰 噶阿(%d)俊 吧赴 \n",
							snprintf( szBuffer, sizeof(szBuffer),
							TMSG(LANG_MSG_CHAR_BATTLE_C_166),
							toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

							//噶阿阑 吧磨 荐 乐绢 目膏靛甫 荤侩且 荐 绝霸 登菌阑 锭
							BATTLE_AmnesiaCommandInit(battleindex, toch , amn , 0);

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 100) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

							snprintf( szBuffer, sizeof(szBuffer),
								TMSG(LANG_MSG_CHAR_BATTLE_C_166),
								toch->c.Name, ToList[i], damage);
								BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

							damage = (float) damage * work;

							//镭绰 厘家甫 扁撅
							toch->w.ModAmnesia = amn;
							//雀荐甫 扁撅
							pEntry[j].Amnesia = damage;
							flg |= BM_FLAG_SUCCESS;

							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 噶阿(%d)栏肺 官诧 \n",
							snprintf( szBuffer, sizeof(szBuffer),
								TMSG(LANG_MSG_CHAR_BATTLE_C_167),
								toch->c.Name, ToList[i], damage);
							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

							//噶阿阑 吧磨 荐 乐绢 目膏靛甫 荤侩且 荐 绝霸 登菌阑 锭
							BATTLE_AmnesiaCommandInit(battleindex, toch , amn , 0);

						}
					}
				break;

			}
#endif
		}

		//酒公巴档 吧府瘤 臼促.磊 吧媚扼
		snprintf( szCommand, sizeof(szCommand),
		"%X|f%X|", ToList[i], flg);
		BATTLESTR_ADD( szCommand );
	}
#endif
	BATTLESTR_ADD("FF|" );

	return TRUE;
}

//****************************************************************
//加己 馆傈
//****************************************************************
int	BATTLE_Reverse_Type( int battleindex, int attackerBid )
{
	Char *ch;
	int toNo;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch) ){
		//器胶 何练
		return FALSE;
	}
	toNo = ch->w.BattleCom2;

	// 傈盔
	BATTLE_MultiAttReverse( battleindex, attackerBid, toNo, -1, -1 );

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1){
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	return TRUE;
}

//**************************************************************
//沥脚 烹老
//**************************************************************
BOOL BATTLE_Consentration(int battleindex, int attackerBid)
{

	int power = 1, per = 0, HealedEffect ;
	int attackNo, toNo, kind = BD_KIND_HP;
	Char *ch, *toch; 	// 荤侩登绰 荤恩狼 锅龋
	int tech_index;
	char *p;
	char *option;
	int a;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );

	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//窜眉扼搁
	if( ch->w.BattleCom2 <= 19){
		//某腐磐狼 器牢磐甫 秒垫
		toch = BATTLE_No2Bid( battleindex, ch->w.BattleCom2 );
	}

	//扁贱狼 牢郸胶甫 秒垫
	tech_index = TECH_getTechIndex(ch->w.BattleCom3);

	//可记 某腐磐 扼牢阑 秒垫
	option = TECH_getChar( tech_index, TECH_OPTION);

	if(option == NULL) return FALSE;

	//傍拜 雀荐(弥家)
	p = strstr(option,"D1:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, tech_index, "D1:", a);
#endif
/*ADD END*/
		per = a;
	}
	//傍拜 雀荐(弥措)
	p = strstr(option,"D2:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, tech_index, "D2:", a);
#endif
/*ADD END*/
		per = RAND(per , a);
	}

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	// 瓤苞狼 农扁
	if( power <= 100 ){
		HealedEffect = SPR_heal;	// 荤捞令家
	}else
	if( power <= 300 ){
		HealedEffect = SPR_heal2;	// 荤捞令吝
	}else{
		HealedEffect = SPR_heal3;	// 荤捞令措
	}


	//------- 咯扁肺何磐 雀汗 贸府 -----------
	attackNo = BATTLE_Bid2No( battleindex, ch );

	toNo = attackNo;

	if( attackNo < 0 ) return FALSE;	// 	绢驴 荐 绝捞 倒酒柯促

	// 傈盔 雀汗
	BATTLE_RecoveryConsentration( battleindex, attackNo, toNo,
		kind, power, per, SPR_heal, HealedEffect );


	return TRUE;
}

//****************************************************************
//	家积
//
//****************************************************************
int BATTLE_Anabiosis(int battleindex, int attackerBid)
{
	int attackNo, toNo;
	Char *ch, *toch; 	// 荤侩登绰 荤恩狼 锅龋
	int tech_index;
	char *moji;
	int per = 0;
	int power = -1;

	char *p;
//	int a;

	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//窜眉扼搁
	if( ch->w.BattleCom2 <= 19){
		//某腐磐狼 器牢磐甫 秒垫
		toch = BATTLE_No2Bid( battleindex, ch->w.BattleCom2 );
	}

	//扁贱狼 牢郸胶甫 秒垫
	tech_index = TECH_getTechIndex(ch->w.BattleCom3);

		//可记 某腐磐 扼牢阑 秒垫
	moji = TECH_getChar( tech_index, TECH_OPTION);

	if(moji == NULL) return FALSE;

	//傍拜 雀荐(弥家)
	p = strstr(moji,"AR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &power);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		power = CHARSUIT_getOptionValue(ch, tech_index, "AR:", power);
#endif
/*ADD END*/
	}


	// 雀汗樊档 绝菌促
//	if( sscanf( moji+3, "%d", &per ) != 1 ){
//		per = 0;	// 绢驴 荐 绝绊 0
//	}


	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	//------- 咯扁肺何磐 雀汗 贸府 -----------
	attackNo = BATTLE_Bid2No( battleindex, ch );
	toNo = ch->w.BattleCom2;

	if( attackNo < 0 ) return FALSE;	// 	绢驴 荐 绝捞 倒酒柯促

	// 傈盔
	BATTLE_MultiRessurect(battleindex, attackNo, toNo,
		power, per, -1,-1);

/*
	BATTLE_MultiMagicAttack( battleindex, attackNo, toNo,
		kind, power, per, SPR_heal, HealedEffect );
*/
	return TRUE;


}



//**************************************************************
//加己 快措
//**************************************************************
int BATTLE_Treat_Type(int battleindex, int attackerBid)
{

	BATTLE *pBattle;
	int 	power = 0;
	int count = 1;
	char 	*p;
	char 	*moji;
	Char *ch;
	int 	d_min=1 , d_max=1;
	int tech_index;
	char szCommand[256] = {0};
	int skillid;


	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;


	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	//扁贱狼 牢郸胶甫 秒垫
	tech_index = TECH_getTechIndex(ch->w.BattleCom3);

	//可记 某腐磐 扼牢阑 秒垫
	moji = TECH_getChar( tech_index, TECH_OPTION);

	if(moji == NULL) return FALSE;


	//单固瘤(弥家)
	p = strstr(moji,"AN:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &d_min);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		d_min = CHARSUIT_getOptionValue(ch, tech_index, "AN:", d_min);
#endif
/*ADD END*/
		count = d_min;
	}

	//单固瘤(弥措)
	p = strstr(moji,"AM:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &d_max);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		d_max = CHARSUIT_getOptionValue(ch, tech_index, "AM:", d_max);
#endif
/*ADD END*/
		count = RAND(d_min , d_max);
	}

	if(count == 0) count= -1;
	//颇况
	p = strstr(moji,"AR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &power);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		power = CHARSUIT_getOptionValue(ch, tech_index, "AR:", power);
#endif
/*ADD END*/
	}



	snprintf( szCommand, sizeof(szCommand),
#ifdef	VERSION_TW
		"SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
		"SKL|a%X|f%X|%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			weponNo,
			attackerBid
	);

	BATTLESTR_ADD( szCommand );

	//付豪矫啊 甸绊 乐绢?
	if(BATTLE_Skill_SilenceCheck(battleindex, attackerBid) == FALSE){
		return FALSE;
	}else{
		BATTLESTR_ADD( "0|" );
	}

	pBattle = &BattleArray[battleindex];

	//公均 畔 且鳖
	pBattle->att_count = count+1;

	//颇况
	pBattle->att_pow = power;
    int array = TECH_getTechIndex(ch->w.BattleCom3);
    int md = Tech_getOptionByIndex(array, "MD:", -1);
    md = CHARSUIT_getOptionValue(ch, array, "MD:", md);
    // 胶懦 ID喊肺 贸府甫 盒扁
    skillid = Tech_getOptionByIndex(array, "SK:", -1);
    skillid = CHARSUIT_getOptionValue(ch, array, "SK:", skillid);
    if (skillid < 0) {
        skillid = TECH_getInt(TECH_getTechIndex(ch->w.BattleCom3), TECH_SKILLID);
    }
	switch(skillid){
		case BATTLE_MAGIC_SKILL_EARTH:	//瘤
			//公郊 加己牢啊
            pBattle->field_att = md >= 0 ? md : BC_FIELD_FLAG_EARTH;
		break;

		case BATTLE_MAGIC_SKILL_WATER:	//荐
			pBattle->field_att = md >= 0 ? md : BC_FIELD_FLAG_WATER;
		break;

		case BATTLE_MAGIC_SKILL_FIRE:	//阂
			pBattle->field_att = md >= 0 ? md : BC_FIELD_FLAG_FIRE;
		break;

		case BATTLE_MAGIC_SKILL_WIND:	//浅
			pBattle->field_att = md >= 0 ? md : BC_FIELD_FLAG_WIND;
		break;

		case BATTLE_MAGIC_SKILL_SILENCE:	//付过 豪窍绊
			pBattle->field_att = md >= 0 ? md : BC_FIELD_FLAG_SILENCE;
		break;

		default:	//档 绝澜
			pBattle->field_att = md >= 0 ? md : 0;
		break;
	}

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}
	return 1;
}
/*
//壬摹绰 犬伏
int stealRate[10][10]={
	{48, 0, 0, 0, 0, 0, 0, 0, 0, 0},
	{56,48, 0, 0, 0, 0, 0, 0, 0, 0},
	{64,56,48, 0, 0, 0, 0, 0, 0, 0},
	{72,64,56,48, 0, 0, 0, 0, 0, 0},
	{80,72,64,56,48, 0, 0, 0, 0, 0},
	{88,80,72,64,56,48, 0, 0, 0, 0},
	{96,88,80,72,64,56,48, 0, 0, 0},
	{96,96,88,80,72,64,56,48, 0 , 0},
	{96,96,96,88,80,72,64,56,48, 0},
	{96,96,96,96,88,80,72,64,56,48}
};
*/
//壬摹绰 犬伏(包祈)
//int stealRateItem[10][10]={
//	{56,14, 6, 4, 2, 2, 1, 1, 1, 1},
//	{64,56, 7, 4, 3, 2, 1, 1, 1, 1},
//	{72,64,56, 5, 3, 2, 1, 1, 1, 1},
//	{80,72,64,56, 3, 2, 1, 1, 1, 1},
//	{88,80,72,64,56, 2, 2, 1, 1, 1},
//	{96,88,80,72,64,56, 2, 2, 1, 1},
//	{96,96,88,80,72,64,56, 2, 1, 1},
//	{96,96,96,88,80,72,64,56, 1 , 1},
//	{96,96,96,96,88,80,72,64,56, 1},
//	{96,96,96,96,96,88,80,72,64,56}
//};


//************************************************************
//壬摹绰 犬伏阑 抛捞喉焊促 曼炼
//************************************************************
static int BATTLE_StealSuccessRate(Char *ch, int itemrank, int skilllv, int *itemflg)
{
	int itemindex;
	int i;
	//酒捞袍 珐农啊 捞惑茄 版快绰 犬伏 0
	if(itemrank < 1) return 0;
	//胶懦 饭骇篮 捞惑茄 版快绰 犬伏 0
	if(skilllv < 1) return 0;
    int rate = skilllv >= itemrank ? 48 + 8 * min(skilllv, max(0, skilllv - itemrank)) : 0;
    if (rate > 96) rate = 96;
	//壬摹绰 犬伏 UP酒捞袍阑 厘厚 窍绊 乐绢
	for(i = 0 ;i < CHAR_STARTITEMARRAY ; i++){
		itemindex = CHAR_getItemIndex( ch, i );
		if(ITEM_CHECKINDEX( itemindex ) == FALSE ) continue;
        if (ITEM_getWorkInt(itemindex, ITEM_WORKVALIDEQUIP) == 0) continue;
        //漂荐摹肺 汲沥捞 乐阑鳖
		if(ITEM_getInt(itemindex, ITEM_SPECIALEFFECT) != ITEM_SPECIALEFFECT_STEAL) continue;
        if (ITEM_getWorkInt(itemindex, ITEM_DURABILITY) <= 0) continue;

		//啊瘤绊 乐菌栏骨肺 漂喊 抛捞喉
		*itemflg = 1;
        if (rate > 0) {
            rate = min(96, rate + 8);
        } else {
            rate = 14 / max(1, itemrank - skilllv) / max(1, itemrank - 1);
            if (rate <= 0) {
                rate = 1;
            }
        }
		return rate;
	}

	//啊瘤瘤 臼疽扁 锭巩俊 烹惑 抛捞喉
	return rate;
}

//************************************************************
//	壬模促
//
//************************************************************
int BATTLE_ItemSteal(int battleindex, int attackerBid)
{

	Char *ch;
	int toNo = -1;
	char szBuffer[256];
	int flg = 0;
	int ToList[SIDE_OFFSET*4+1];
	Char *defch;
	int success = 0;
	int itemindex;
	int techindex;
	int skilllevel;
	char szCommand[256] = {0};
	int itemflg;
#ifdef EQUIPMENT_EFFECT
	int		bit;
#endif

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	//捞 胶懦阑 荤侩且 荐 乐绰 巴篮 敲饭捞绢父
	if(ch->i.WhichType !=  CHAR_TYPEPLAYER) return -1;

	//鸥百 沥焊
	toNo = ch->w.BattleCom2;

	//鸥百 秒垫
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
    EmitBattleActionTargetEvent(ch, ToList);

	//鸥百狼 器牢磐
	defch = BATTLE_No2Bid( battleindex, ToList[0] );

	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( defch ) == FALSE ) return -1;

	//利 捞寇俊 档迪龙阑 窍搁 救蹬
	if( defch->i.WhichType !=  CHAR_TYPEENEMY ) return -1;

	//扁贱狼 牢郸胶甫 秒垫
	techindex = TECH_getTechIndex( ch->w.BattleCom3 );
	if( ! TECH_CHECKINDEX( techindex) ) return -1;
	skilllevel = TECH_getInt( techindex, TECH_NECESSARYLV );

	//胶懦 饭骇篮 捞惑窍瘤 臼篮啊
	if( skilllevel  < 1 || skilllevel > CHAR_MAXTECHHAVE ) return -1;


	//己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	//胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
//			TECH_getInt(TECH_getTechIndex(ch->w.BattleCom3), TECH_FORCEPOINT),
			weponNo,
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );


	//傈捧 盲泼 橇肺配妮
	snprintf( szCommand, sizeof( szCommand),
			 "CHT|14|%X|3|0|", CHAR_COLORYELLOW
	 );
	//某腐磐 扼牢侩
	BATTLESTR_TAIL( szCommand);

	//角力肺 酒捞袍阑 爱绊 乐阑鳖狼 眉农
	//酒捞袍阑 历绵茄促(利捞 啊瘤绊 乐菌阑 版快)
	itemindex = CHAR_getItemIndex( defch, CHAR_STEALITEMHAVE );

	if(ITEM_CHECKINDEX( itemindex ) == TRUE ){
		// 敲饭捞绢啊 啊瘤绊 乐绰 巴捞扼搁 肋给秦
		if( CHAR_CheckPlayerPointer( ITEM_getCharPointer( itemindex ) ) == TRUE ){
			print_t(LANG_MSG_CHAR_BATTLE_C_140,
				__FILE__, __LINE__ );
		}else{
			//敲饭捞绢狼 凯覆鄂眉农
			// 敲饭捞绢狼 酒捞袍狼 后鄂阑 茫绰促
			if( CHAR_findEmptyItemBox( ch ) >= 0 ){
				int	ret;
				int ran;

				//己傍啦阑 夯促(抛捞喉 曼炼)
				ran = BATTLE_StealSuccessRate( ch, ITEM_getInt(itemindex, ITEM_LEVEL), skilllevel, &itemflg);

//				print("犬伏 =%d 酒捞袍 珐农 =%d 胶懦 饭骇 =%d ", ran, ITEM_getInt(itemindex, ITEM_LEVEL), skilllevel);

				if( ran > RAND( 0 , 99 ) ){
					//皑沥捞 场抄 惑怕肺 茄促
//					ITEM_setInt( itemindex, ITEM_LEAKLEVEL, 1);
//					print("档迪龙 己傍");

					// 利栏肺何磐 酒捞袍阑 哈变促.
					CHAR_unsetItem( defch, CHAR_STEALITEMHAVE );
					/*酒捞袍狼 眠啊(流立 酒捞袍鄂俊 持绢 滚赴促)*/
					ret = CHAR_addItemSpecificItemIndex(ch, itemindex);

					if( 0 <= ret && ret < CHAR_EQUIPPLACENUM ){
						print_t(LANG_MSG_CHAR_BATTLE_C_129, CHAR_getUseName( ch ), ret,
							ITEM_getAppropriateName(itemindex)
						);
					}else{
						//s绰%s肺何磐%s甫 壬闷促
						snprintf( szCommand, sizeof( szCommand),
							 TMSG(LANG_MSG_CHAR_BATTLE_C_201),
							 ch->c.Name,
							 defch->c.Name,
		 					 ITEM_getIncuseName( itemindex)
						);
						BATTLESTR_TAIL( szCommand);
					}

					/* 酒捞袍 单捞磐甫 促矫 焊辰促 */
	        		CHAR_sendItemDataOne( ch, ret);
					success = 1;
				}else{
					if( ITEM_getInt( itemindex, ITEM_LEVEL ) > skilllevel ){
						//档迪龙 角菩
						if(itemflg == 1){
							//%s绰,%s肺何磐甫 壬磨 荐 绝菌促
							snprintf( szCommand, sizeof( szCommand),
								 TMSG(LANG_MSG_CHAR_BATTLE_C_202),
								 ch->c.Name,
								 defch->c.Name
							);
							BATTLESTR_TAIL( szCommand);
						}else{
							//漂荐 酒捞袍 绝澜
							//"%s狼 扁樊俊辑绰 壬磨 荐 乐阑 巴 鞍瘤 臼促.|"
							snprintf( szCommand, sizeof( szCommand),
								 TMSG(LANG_MSG_CHAR_BATTLE_C_203),
								 ch->c.Name
							);
							BATTLESTR_TAIL( szCommand);
						}
					}else{
						//%s绰,%s肺何磐甫 壬磨 荐 绝菌促
						snprintf( szCommand, sizeof( szCommand),
								TMSG(LANG_MSG_CHAR_BATTLE_C_202),
								ch->c.Name,
								defch->c.Name
						);
						BATTLESTR_TAIL( szCommand);
					}
				}
			}else{
				//后 镑捞 绝促
				snprintf( szCommand, sizeof( szCommand),
					TMSG(LANG_MSG_CHAR_BATTLE_C_205), ""
				);
				BATTLESTR_TAIL( szCommand);
			}
			// 咯扁辑 酒捞袍阑 利栏肺何磐 哈变促
//			CHAR_unsetItem( defch, CHAR_STEALITEMHAVE );
		}
	}else{
		//%s绰 酒公巴档 啊瘤绊 乐瘤 臼篮 巴 鞍促
		snprintf( szCommand, sizeof( szCommand),
				 TMSG(LANG_MSG_CHAR_BATTLE_C_204),
				 defch->c.Name
		);
		BATTLESTR_TAIL( szCommand);
		success = 2;
	}

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if( BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1 ){
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	if(success == 1){
		flg |= BM_FLAG_SUCCESS;

		//疙己狼 眉农
		if(ch->i.WhichType == CHAR_TYPEPLAYER){
			if(ch->iu.player.JobAncestry == 120){
				// 档利捞 「壬模促」甫 荤侩窍搁 疙己捞 坷弗促
				BATTLE_FameCheck(battleindex, attackerBid, ToList[0], 10 );
			}
		}
	}
#ifdef EQUIPMENT_EFFECT
	// 厘厚俊 狼茄 漂荐 瓤苞狼 眉农
	bit = BATTLE_checkEquipmentEffect( ch );
	// 「壬模促」甫 「碍呕」肺 窍绰 漂荐 瓤苞 啊瘤绊 乐阑鳖?
	if( bit & ( 1 << ITEM_BATTLEEFFECT_ROBBER ) ){
		// 傍拜 汲沥
		flg |= BM_FLAG_NORMAL;
	}
#endif
	sprintf(szBuffer, "d%X|f%X|A|", ToList[0], flg);
	BATTLESTR_ADD( szBuffer );

#ifdef EQUIPMENT_EFFECT
	// 「碍呕」?
	if( flg & BM_FLAG_NORMAL ){
		int		moveflg;
		int		ret;

		/* 烹惑 傍拜 贸府甫 眠啊肺 角矫茄促 */
		// 公扁绰 馆靛矫 八秒鞭(漂荐茄 焊沥捞 绝扁 锭巩俊)
		gWeponType = ITEM_SWORD;
		// 胶懦肺, 弊 寇狼 傍拜 秒鞭
		moveflg = 6;
		// 烹惑 傍拜 贸府甫 荤侩
		ret = BATTLE_Attack( battleindex, attackerBid, ToList[0], moveflg );
		// 「碍呕」且 荐 乐菌绢?
		if( ret >= 0 ){
			// 「碍呕」沁阑 版快绰, 己傍 角菩 棺 公扁肺 包访登瘤 臼绊 墨款磐 乐绢
			success = 0;
			// 龋困 登菌绢?
			if( ret == 2 ) {
				// 墨款磐 绝澜
				success = 1;
			}
		}
	}
#endif

	// 角菩?
	if( success == 0 ){
#ifdef EQUIPMENT_EFFECT
		// 角菩矫俊绰 1雀 墨款磐 瞪 挥
		int		max = 1;
		int		i;
		int		attacker = attackerBid;
		int		defender = ToList[0];

		// 「碍呕」?
		if( flg & BM_FLAG_NORMAL ){
			// 「碍呕」矫绰 烹惑 傍拜苞 鞍捞 弥措 墨款磐 雀荐 10雀
			max = 10;
		}
		// 弥措 墨款磐 雀荐鳖瘤 墨款磐 傈捧
		for( i = 0 ; i < max ; i++ ){
			int		tmp;

			// 墨款磐 贸府
			if( BATTLE_Counter( battleindex, defender, attacker ) == FALSE ){
				// 墨款磐 登瘤 臼疽栏搁 芭扁辑 场
				break;
			}
			// 傍拜螟苞 规绢螟阑 官层 持绰促
			tmp = attacker;
			attacker = defender;
			defender = tmp;
		}
#else
		// 角菩扼搁 墨款磐 贸府
		BATTLE_Counter(battleindex , ToList[0] , attackerBid);
#endif
		BATTLESTR_ADD( "FF|" );
		return -1;
	}

	BATTLESTR_ADD( "FF|" );
	return ToList[0];

}

//************************************************************
//鞠混
//
//************************************************************
int BATTLE_Assassin(int battleindex, int attackerBid)
{

	Char *ch;
	int toNo = -1;
	char szBuffer[256];
	int flg = 0;
	Char *defch;
	int success = 0;
	int pAttackList[10];
	int attackBid, targetBid;


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;


	//捞 胶懦阑 荤侩且 荐 乐绰 巴篮 敲饭捞绢父
//	if(ch->i.WhichType !=  CHAR_TYPEPLAYER) return -1;

	//鸥百 沥焊
	toNo = ch->w.BattleCom2;
	defch = BATTLE_No2Bid( battleindex, toNo );

	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( defch ) == FALSE) return -1;
	// 鸥百捞 磊脚锭绰 惯悼窍瘤 臼绰促
	if( attackerBid == toNo ) return -1;

	//瘤陛篮 壬摹绰阑 持绢 敌促
//	ch->w.BattleCom3 = 7200;

	//己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	//胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|%X|a%X|",
#endif
			ch->w.BattleCom3,
			0,
//			BATTLE_SKILL_UseFp(ch),
			weponNo,
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );

	//己傍
	defch->i.Hp  = 0;
	flg |= BM_FLAG_DEATH;
#ifdef PET_RIDE_BATTLE
	// 鞠混等 巴篮, 局肯悼拱?
	if( defch->i.WhichType == CHAR_TYPEPET ){
		Char	*player = (Char *) defch->wu.pet.riderChar;
		// 愁捞 扁备吝?
		if( CHAR_CheckCharPointer( player ) ){
			// 愁捞 扁备矫绰, 剧率 葛滴 悼矫俊 荤噶
			player->i.Hp = 0;
			defch->wu.pet.petHp = 0;
		}
	}
#endif

	pAttackList[0] = BATTLE_Bid2No(battleindex, ch);
	pAttackList[1] = -1;


	//版氰瘤, 捣, 酒捞袍殿阑 GET
	BATTLE_AddProfit(battleindex, ch, defch, pAttackList);
	//疙己

	attackBid = BATTLE_Bid2No(battleindex, ch);
	targetBid = BATTLE_Bid2No(battleindex, defch);
	BATTLE_FameCheck(battleindex, attackBid, targetBid, 2);

	//荤噶 敲贰弊甫 技款促
	CHAR_setFlg( defch, CHAR_ISDIE, TRUE);

#ifdef PUK2_SKILL
	if( defch->i.WhichType == CHAR_TYPEPLAYER ){
		// 府滚胶 窍绊 乐绢?
		if( defch->w.RebirthFlag ){
			// 府滚胶 秦力
			defch->w.RebirthFlag = 0;
			defch->w.RebirthView = 0;
		}
		// 剧滴 俺绊扁档 秦力
		defch->wu.player.copyChar = NULL;
		defch->wu.player.copyCount = 0;
		// 弊贰侨阑 盔狼 葛嚼俊
		CHAR_updateGraphic( defch );
	}
#else
#ifdef PUK2
	// 府滚胶 窍绊 乐绢?
	if( defch->w.RebirthFlag ){
		// 府滚胶 秦力
		defch->w.RebirthFlag = 0;
		defch->w.RebirthView = 0;
		CHAR_updateGraphic( defch );
	}
#endif
#endif

	//己傍 敲贰弊 ON
	success = 1;

	//疙己




	//畴钢 荤噶 贸府
	BATTLE_NormalDeadExtra(battleindex, ch , defch);

	if(success == 1){
		flg |= BM_FLAG_SUCCESS;
	}
	//穿备俊霸｜敲贰弊｜单固瘤
	sprintf(szBuffer, "d%X|f%X|A|",
			toNo,
			flg);
	BATTLESTR_ADD( szBuffer );


	BATTLESTR_ADD( "FF|" );
	return toNo;

}





//********************************************************************
//厘厚 公扁 颇鲍
//*********************************************************************
int BATTLE_BreakAttack(int battleindex, int attackerBid)
{
	Char *ch;
	int toNo;
	char szBuffer[256];
	char szBuffer2[256];
	int i, j, k, itemindex = -1;
	int ran;
	int flg = 0, shieldflg = 0;
	int ToList[SIDE_OFFSET*4+1];
	int damage = 10;
	int work, wepon = 0;
	Char *toch;

#ifdef ITEM_NEW
	BOOL  breakflg = FALSE;
#endif
	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	ran = RAND(1,20);
	//鸥百 沥焊
	toNo = ch->w.BattleCom2;

	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
    EmitBattleActionTargetEvent(ch, ToList);


	toch = BATTLE_No2Bid( battleindex, ToList[0] );
	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( toch ) == FALSE) return -1;
#ifdef PET_RIDE_BATTLE
	// 局肯悼拱?
	if( toch->i.WhichType == CHAR_TYPEPET ){
		Char	*player = (Char *) toch->wu.pet.riderChar;

		// 愁捞 扁备吝?
		if( CHAR_CheckCharPointer( player ) ){
			// 愁捞 扁备吝捞扼搁, 扼捞歹狼 厘厚甫 眉农
			toch = player;
		}
	}
#endif

	//己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	//胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			weponNo,
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );


	//厘厚前阑 盒疙窍霸 厘厚 窍绊 乐阑鳖?
	for(j = 0, i = 0 ; ran >= 0 ; i++, j++){
		if(i == CHAR_EQUIPPLACENUM) i = 0;
		if(j >= 200) break;
		itemindex = CHAR_getItemIndex( toch, i );
		if(itemindex == -1) continue;

		ran--;
	}
	i--;
	//快急 傍拜捞扼绊 窍绰 巴栏肺
	flg |= BM_FLAG_NORMAL;

	//被捞 厘厚 窍臼疽绢
	if(j == 200){
		 damage = 0;
	}else{
		//己傍啦阑 夯促
		work = BATTLE_Aves( battleindex, ch);
		//己傍牢啊 角菩牢啊?
		if(RAND(0,99) < work){
			//己傍
            if (ITEM_IsShield(itemindex)) {
                shieldflg = 1;
            }

            if (ITEM_IsExtItemWeapon(ITEM_getInt(itemindex, ITEM_TYPE))) {
                wepon = 1;
                flg |= BM_FLAG_WEAPON_BROKEN;
            } else {
				flg |= BM_FLAG_EQUIP_BROKEN;
#ifdef HEAD_COVER
				// 噶啊柳 巴篮 墨宏府葛畴?
				if( ITEM_getInt( itemindex, ITEM_TYPE ) == ITEM_HEADGEAR ){
					// 弊贰侨捞 官差骨肺, 公扁 噶啊瘤绊客 悼老 秒鞭俊
					wepon = 1;
				}
#endif
			}

            if (!ITEM_hasFlag(itemindex, ITEM_OTHERFLG_IMMUTABLE) && EmitItemDurabilityChangedEvent(itemindex, ITEM_getInt(itemindex, ITEM_DURABILITY), -1, 4) == 4) {
#ifdef ITEM_NEW
                if (!BATTLE_TrySaveBrokenItem(toch, itemindex)) {

                    breakflg = TRUE;
                    snprintf(szBuffer2, sizeof(szBuffer2),
                             "CHT|14|%X|3|0|%s",
                             CHAR_COLORYELLOW,
                             TRMSG(LANG_MSG_BATTLE_SKILL_C_0001,
                                   toch->c.Name,
                                   ITEM_getIncuseName(itemindex)
                             )
                    );

                    if (ITEM_hasFlag(itemindex, ITEM_OTHERFLG_UNBREAK)) {
                        ITEM_setInt(itemindex, ITEM_DURABILITY, 0);
                    } else {
                        BATTLE_BreakItemDelete(toch, itemindex, i);
                    }

                    //瘤陛 窜拌 啊瓷己捞 乐绰 胶其既 啊捞靛 捞寇绰 弊措肺 傍拜 加青
                    if (shieldflg == 1 && toch->w.BattleCom1 == BATTLE_COM_P_SPECIALGARD) {
                        toch->w.BattleCom1 = BATTLE_COM_NONE;
                        toch->w.BattleCom3 = -1;
                    }
                    //2 咀记
                    if (shieldflg == 1 && toch->w.Battle2Com1 == BATTLE_COM_P_SPECIALGARD) {
                        toch->w.Battle2Com1 = BATTLE_COM_NONE;
                        toch->w.Battle2Com3 = -1;
                    }

                }
#else
                snprintf( szBuffer2, sizeof( szBuffer2),
                     "CHT|14|%X|3|0|"LANG_MSG_CHAR_BATTLE_C_189,
                     CHAR_COLORYELLOW,
                     toch->c.Name,
                     ITEM_getIncuseName( itemindex)
    //				 ITEM_getChar( itemindex, ITEM_TRUENAME)
                     );

                //厘厚 颇鲍
                BATTLE_BreakItemDelete(toch, itemindex, i);

                //瘤陛 窜拌 啊瓷己捞 乐绰 胶其既 啊捞靛 捞寇绰 弊措肺 傍拜 加青
                if(shieldflg == 1 && toch->w.BattleCom1 == BATTLE_COM_P_SPECIALGARD){
                    toch->w.BattleCom1 = BATTLE_COM_NONE;
                    toch->w.BattleCom3 = -1;
                }
                //2 咀记
                if(shieldflg == 1 && toch->w.Battle2Com1 == BATTLE_COM_P_SPECIALGARD){
                    toch->w.Battle2Com1 = BATTLE_COM_NONE;
                    toch->w.Battle2Com3 = -1;
                }

#endif
            }
        } else {
            //角菩
            damage = 0;
        }
	}

//	print("Break_i=%d j=%d \n", i, j);

	sprintf(szBuffer, "d%X|f%X|%X|",
			ToList[0],
			flg,
			damage
			);
	BATTLESTR_ADD( szBuffer );


	//公扁啊 绊厘车阑 版快绰 弊贰侨阑 官槽促
	if(wepon == 1){
		if(damage != 0){
#ifdef PUK2
			int		baseimage = toch->i.BaseBaseImageNumber;
#ifdef PET_RIDE_BATTLE
			int		ridepetimage = 0;
			int		headgearimage = 0;

			// 府滚胶吝?
			if( toch->w.RebirthView ){
				baseimage = toch->w.RebirthBaseImage;
			}

			// 愁捞 扁备吝?
			if( toch->w.walk.ridePet != -1 ){
				Char	*pet = CHAR_getHavePetCharPointer( toch, toch->w.walk.ridePet );

				if( CHAR_CheckCharPointer( pet ) ){
					// 愁捞 扁备 窍绊 乐绰 局肯悼拱狼 弊贰侨 锅龋
					ridepetimage = pet->i.BaseImageNumber;
				}
			}
#ifdef HEAD_COVER
			// 丹绢 静绰 巴狼 巴阑 窍绊 乐阑鳖?
			if( toch->wu.player.HeadGraNo > 0 ){
				// 丹绢 静绰 巴狼 巴狼 捞固瘤 锅龋甫 焊辰促
				headgearimage = toch->wu.player.HeadGraNo;
			}
#endif
			// 弊贰侨 锅龋 价脚
			snprintf( szBuffer, sizeof( szBuffer ), "%X|%X|%X|", baseimage, ridepetimage, headgearimage );
#else

			// 府滚胶吝?
			if( toch->w.RebirthView ){
				baseimage = toch->w.RebirthBaseImage;
			}
			snprintf( szBuffer, sizeof( szBuffer ), "%X|", baseimage );
#endif
			BATTLESTR_ADD( szBuffer );
#else
			//公扁啊 绊厘车阑 版快绰 弊贰侨阑 官槽促
			snprintf( szBuffer, sizeof( szBuffer),
				 "%X|",
			 	toch->i.BaseBaseImageNumber
			 	);
			BATTLESTR_ADD( szBuffer );
#endif
		}
	}



	//墨款磐 且瘤 绢冻瘤? (弥措狼 墨款磐 筏府绰 10)
	for(k = 0 ; k < 10 ; k++)
	{
		//扁傍藕
		if( ch->w.BattleCom1 == BATTLE_COM_P_SPIRACLESHOT) break;

#ifdef	TECH_BLASTWAVE
		//追月
		if( ch->w.BattleCom1 == BATTLE_COM_BLASTWAVE) break;
#endif
		//墨款磐 贸府
		if(BATTLE_Counter(battleindex, ToList[0] , attackerBid)
		 == FALSE) break;
			//傍荐 函券
		work = ToList[0];
		ToList[0] = attackerBid;
		attackerBid = work;

	}

	BATTLESTR_ADD( "FF|" );

	if(damage != 0){
#ifdef ITEM_NEW
		if( breakflg == TRUE )
			BATTLESTR_ADD( szBuffer2 );
#else
		BATTLESTR_ADD( szBuffer2 );
#endif
	}

//	CBC｜磊脚 ID｜脚弊贰侨 锅龋｜HP｜MaxHP｜FP｜MaxFP｜
	//HP唱 FP啊 函拳窍绊 乐绰 酒捞袍阑 何荐绢脸阑 版快啊 乐栏骨肺
	if(damage != 0){
		if(! (toch->w.BattleFlg & CHAR_BATTLEFLG_ULTIMATE)){
#ifdef PET_RIDE_BATTLE
			int		ridepetimage = 0;
			int		headgearimage = 0;
			int		hp = toch->i.Hp;
			int		maxhp = toch->w.MaxHp;
			int		fp = toch->i.ForcePoint;
			int		maxfp = toch->w.MaxForcePoint;

			// 愁捞 扁备吝?
			if( toch->w.walk.ridePet != -1 ){
				Char	*pet = CHAR_getHavePetCharPointer( toch, toch->w.walk.ridePet );

				if( CHAR_CheckCharPointer( pet ) ){
					// 愁捞 扁备 窍绊 乐绰 局肯悼拱狼 弊贰侨 锅龋
					ridepetimage = pet->i.BaseImageNumber;
					// LP, FP甫 愁捞 扁备 惑怕狼 巴俊
					hp = pet->i.Hp;
					maxhp = pet->w.MaxHp;
					fp = pet->i.ForcePoint;
					maxfp = pet->w.MaxForcePoint;
				}
			}
#ifdef HEAD_COVER
			// 丹绢 静绰 巴狼 巴阑 窍绊 乐阑鳖?
			if( toch->wu.player.HeadGraNo > 0 ){
				// 丹绢 静绰 巴狼 巴狼 捞固瘤 锅龋甫 焊辰促
				headgearimage = toch->wu.player.HeadGraNo;
			}
#endif

			sprintf(szBuffer, "CBC|%X|%X|%X|%X|%X|%X|%X|%X|",
				// 傍拜磊狼 殿废 锅龋 秒垫
				BATTLE_Bid2No( toch->w.BattleIndex, toch),
				toch->i.BaseImageNumber,
				hp, maxhp, fp, maxfp,
				ridepetimage,
				headgearimage
			);
#else
			sprintf(szBuffer, "CBC|%X|%X|%X|%X|%X|%X|",
				ToList[0],
				toch->i.BaseImageNumber,
				toch->i.Hp,
				toch->w.MaxHp,
				toch->i.ForcePoint,
				toch->w.MaxForcePoint
			);
#endif

			BATTLESTR_ADD( szBuffer );
		}
	}


	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	return 1;

}
//********************************
//抄公甫 且瘤 绢冻瘤
//茄促搁 目膏靛甫 官槽促
//********************************
int BATTLE_CheckRandomDance(int battleindex, Char *ch)
{
	return 1;
}

//********************************
//鞠混阑 且瘤 绢冻瘤
//茄促搁 目膏靛甫 官槽促
//********************************
int BATTLE_CheckAssassin(int battleindex, int attackerBid)
{
	int i, j;
	int techId;
	int techindex;
	int array;
	char *option;
	int work=0;
	char *p;
	Char *defch;
	Char *ch;
	int toNo;
	int ToList[SIDE_OFFSET*2+1];
	int s_level = 1, level = 0;
//	int normal_tbl[10][12]={
//				{24,16, 8, 4, 2, 1, 1, 1, 1, 1, 1, 1},
//				{32,24,16, 8, 4, 2, 1, 1, 1, 1, 1, 1},
//				{40,32,24,16, 8, 4, 2, 1, 1, 1, 1, 1},
//				{48,40,32,24,16, 8, 4, 2, 1, 1, 1, 1},
//				{56,48,40,32,24,16, 8, 4, 2, 1, 1, 1},
//				{64,56,48,40,32,24,16, 8, 4, 2, 1, 1},
//				{72,64,56,48,40,32,24,16, 8, 4, 2, 1},
//				{80,72,64,56,48,40,32,24,16, 8, 4, 2},
//				{88,80,72,64,56,48,40,32,24,16, 8, 4},
//				{96,88,80,72,64,56,48,40,32,24,16, 8}};
//	int duel_tbl[10]={2,4,6,8,10,12,14,16,18,20};
#ifdef NEW_JOB_CHANGE
	int skillindex;
	int usablelv;
#endif

	//某腐磐狼 器牢磐 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( ch ) == FALSE) return 0;

	//鸥百 沥焊
	toNo = ch->w.BattleCom2;
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
	//鸥百阑 秒垫
	defch = BATTLE_No2Bid( battleindex, ToList[0] );
	if( CHAR_CheckCharPointer( defch ) == FALSE) return -1;

	if(ch->i.WhichType == CHAR_TYPEENEMY){
		//利侩
		//带瘤扁拌狼 公扁档 惯悼窍瘤 臼绰促
		if( BATTLE_IsThrowWepon( ch ) == TRUE ) return -1;

		techId = ch->w.BattleCom3;
		techindex = TECH_getTechIndex( techId );
		if( TECH_CHECKINDEX(techindex) == FALSE ) return -1;
//		print("techid=%d \n", techId );

		//格窍 乐绊 犬牢
		if(TECH_getInt(techindex , TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_ASSASSIN) return -1;

		//牢荐甫 夯促
		array  = TECH_getTechIndex( techId);
		option = TECH_getChar( array, TECH_OPTION);

		if(option == NULL) return -1;

		//犬伏阑 夯促
		//己傍啦阑 夯促
		//可记 某腐磐 扼牢阑 秒垫
		//咯扁 犬伏阑 持绰促
		p = strstr(option,"SR:");
		if(p !=NULL) {
			sscanf(p +3,"%d", &work);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		work = CHARSUIT_getOptionValue(ch, array, "SR:", work);
#endif
/*ADD END*/
		}

//		print("work=%d", work);
//		print("ASS : j =%d  Level=%d \n", j, TECH_getInt( techindex, TECH_NECESSARYLV) );

		//犬伏篮 绢冻鳖
		if(work > RAND(0,999)){
			//目膏靛甫 鞠混俊 函版
			ch->w.BattleCom1 = BATTLE_COM_P_ASSASSIN;
			ch->w.BattleCom2 = ToList[0];
			ch->w.BattleCom3 = techId;
		}else{
			ch->w.BattleCom1 = BATTLE_COM_ATTACK;
			ch->w.BattleCom2 = ToList[0];
			ch->w.BattleCom3 = 7300;

			return -1;
		}

		return 1;
	}else{
        if (BattleArray[battleindex].type != BATTLE_TYPE_P_vs_P) {
            //敲饭捞绢＆脐侩
            //焊胶(EVIL)绰 鞠混且 荐 绝促.
            if (defch->i.Tribe == CHAR_TRIBE_EVIL) return -1;
        }
#ifdef PUK2
		// 磊脚篮 鞠混且 荐 绝促
		if( attackerBid == ToList[0] ) return -1;
#endif
		//利苞狼 傈捧肺
		if( BattleArray[battleindex].type != BATTLE_TYPE_P_vs_P ){
			//敲饭捞绢档 救蹬
			if(defch->i.WhichType == CHAR_TYPEPLAYER) return -1;
			//局肯悼拱档 救蹬
			if(defch->i.WhichType == CHAR_TYPEPET) return -1;
		}
		//胶拍靛 俊呈固档 救蹬(胶懦 版氰摹啊 甸绢啊瘤 臼绰 傈捧绰 救蹬)
		if( BattleArray[battleindex].flg & BATTLE_FLG_NO_SKILLEXP ) return -1;
		//带瘤扁拌狼 公扁档 惯悼窍瘤 臼绰促
		if( BATTLE_IsThrowWepon( ch ) == TRUE ) return -1;
	}

	/* 鞠混 胶懦阑 啊瘤绊 乐阑瘤 绢冻瘤甫 眉农 */
	// 胶懦狼 厘家甫 夯促
	for(i = 0; i < CHAR_MAXSKILLHAVE; i++ ) {
		if(ch->player_addon->Skill[i].SkillId == BATTLE_PARTICULAR_SKILL_ASSASSIN)
		{
			break;
		}
	}
	// 绢蠢?
	if(i == CHAR_MAXSKILLHAVE) return -1;

#ifdef NEW_JOB_CHANGE
	// 瘤陛狼 老磊府肺 绢蠢 饭骇鳖瘤 荤侩且 荐 乐阑鳖甫 夸备茄促
	skillindex = SKILL_getSkillIndex( BATTLE_PARTICULAR_SKILL_ASSASSIN );
	usablelv = SKILL_getMaxLevel( skillindex, ch->iu.player.Job );
#endif

	//啊瘤绊 乐带, 扁贱狼 饭骇篮 倔付牢啊?
    for (j = CHAR_MAXTECHHAVE - 1; j > -1; j--) {
		techId = CHAR_getTechId( ch, i, j);
		if( techId == -1 ) continue;

		/* techid 肺何磐 TECH_tech狼 梅磊甫 掘绰促 */
		techindex = TECH_getTechIndex( techId);
//		print("teckId=%d \n", techId );

		if(TECH_TechLevelCheck(ch, TECH_getInt( techindex, TECH_NECESSARYLV)) == 0) continue;
#ifdef NEW_JOB_CHANGE
		// 荤侩且 荐 乐绰 饭骇狼 巴牢啊?
		if( TECH_getInt( techindex, TECH_NECESSARYLV ) > usablelv ){
			// 荤侩且 荐 绝促
			continue;
		}
#endif

		//格窍 乐绊 犬牢
		if(TECH_getInt(techindex , TECH_SKILLID) != BATTLE_PARTICULAR_SKILL_ASSASSIN) continue;

		//胶懦 饭骇
		s_level = TECH_getInt( techindex, TECH_NECESSARYLV);
		if(s_level < 1 || s_level > cgmsvcf.maxskilllevel){
			print("SkillLevelErr:ASSASIIN");
            s_level = s_level < 1 ? 1 : cgmsvcf.maxskilllevel;
		}
//		print("s_level=%d \n", s_level);

		//鞠混 抛捞喉阑 曼炼
		if( BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P ){
			//DUEL矫
            work = CHARSUIT_getOptionValue(ch, techindex, "PR:", s_level * 2);
            // work = duel_tbl[s_level-1];
		}else{
			//烹惑矫
//			if(defch->i.Lv <=  10) level= 0;
//			else if(defch->i.Lv <=  20) level= 1;
//			else if(defch->i.Lv <=  30) level= 2;
//			else if(defch->i.Lv <=  40) level= 3;
//			else if(defch->i.Lv <=  50) level= 4;
//			else if(defch->i.Lv <=  60) level= 5;
//			else if(defch->i.Lv <=  70) level= 6;
//			else if(defch->i.Lv <=  80) level= 7;
//			else if(defch->i.Lv <=  90) level= 8;
//			else if(defch->i.Lv <=  100) level= 9;
//			else if(defch->i.Lv <=  110) level= 10;
//			else if(defch->i.Lv <=  120) level= 11;
            level = (defch->i.Lv -1) /10;
            work = (s_level + 2) > level ? (8 * (s_level + 2 - level)) : (max(1, (8 / (2 << (level - s_level - 2)))));
            work = CHARSUIT_getOptionValue(ch, techindex, "SR:", work);
//			work = normal_tbl[s_level-1][level];
//			print("level=%d \n", level);
		}
		work = work * 10;
        if (ch->i.WhichType == CHAR_TYPEPLAYER && SKILL_getInt(skillindex, SKILL_JOBS) != ch->iu.player.JobAncestry) {
            work = max(1, work * cgmsvcf.assassinNormalRate);
        }
//		print("work=%d \n", work);

//		print("maxlevel(%d)= Lv(%d) /waru(%d) + Level(%d) * 3 ",
//				maxlevel, ch->i.Lv, waru,
//				TECH_getInt( techindex, TECH_NECESSARYLV));

		//犬伏篮 绢冻鳖
		if(work > RAND(0,999)){
			//目膏靛甫 鞠混俊 函版
			ch->w.BattleCom1 = BATTLE_COM_P_ASSASSIN;
			ch->w.BattleCom2 = ToList[0];
			ch->w.BattleCom3 = techId;
		}else{
			return -1;
		}

		return i;
	}

	return 1;
}

//********************************************************
//勉冕促(胶抛捞磐胶 捞惑)
//********************************************************
BOOL BATTLE_DanceStatus(int battleindex, int attackerBid, int toNo, int type, int damage)
{
	int i , j, k = 1, amn = 0;
	int flg = 0;
	int ToList[SIDE_OFFSET*4+1];
	int success = 0, resist = 0;
	char szCommand[256] = {0};
	char szBuffer[256] = {0};
	float work = 0;
	int action =0;
	Char *ch;
	Char *toch;
	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	//鸥百捞 绝促
	if(ToList[0] == -1) return FALSE;

	for(i=0; ToList[i] != -1;i++)
	{
		//己傍啦篮 泅犁 100%
		success = 100;

		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		flg = 0;
		//国结 盔龋拌啊 吧妨乐绰 版快绰 救蹬
		if(toch->w.ModPoison <= 0
			&& toch->w.ModSleep <= 0
			&& toch->w.ModStone <= 0
			&& toch->w.ModDrunk<= 0
			&& toch->w.ModConfusion <= 0
			&& toch->w.ModAmnesia<= 0
		)
		{
			switch( type){

				case B_DANCE_POISON:
					//刀包拌
//					print("%s(%d)绰 刀(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage)
//					Swork = success * (toch->w.Poison * 0.01);
//					success -= Swork;

//					if(success < 0 ) success = 0;


					//郴己 胶懦阑 爱绊 乐阑鳖
//					resist = BATTLE_StatusResistance( toch , 0 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModPoison = damage;

							//"%s(%d)绰 刀(%d)俊 吧赴 \n"
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_156,
                                            toch->c.Name, ToList[i], damage);
						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("PO_success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_156,
                                            toch->c.Name, ToList[i], damage);

							damage = (float) damage * work;
							toch->w.ModPoison = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


							//"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 刀(%d)栏肺 官诧 \n
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_157,
								toch->c.Name, ToList[i], damage);
						}
					}

				break;

				case B_DANCE_SLEEP:
					//泪包拌
//					print("%s(%d)绰 泪(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

//					Swork = success * (toch->w.Sleep * 0.01);
//					success -= Swork;

//					if(success < 0 ) success = 0;


					//郴己 胶懦阑 爱绊 乐阑鳖
//					resist = BATTLE_StatusResistance( toch , 1 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//泪己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModSleep = damage;
							//"%s(%d)绰 泪(%d)俊 吧赴 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_158,
                                            toch->c.Name, ToList[i], damage);

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_158,
                                            toch->c.Name, ToList[i], damage);

							damage = (float) damage * work;
							toch->w.ModSleep = damage;

							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 泪(%d)栏肺 官诧 \n"
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_159,
                                            toch->c.Name, ToList[i], damage);
						}
					}

				break;

				case B_DANCE_STONE:
					//籍蜡拳切 包拌
//					print("%s(%d)绰 籍蜡拳切(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

//					Swork = success * (toch->w.Stone * 0.01);
//					success -= Swork;

//					if(success < 0 ) success = 0;

					//郴己 胶懦阑 爱绊 乐阑鳖
//					resist = BATTLE_StatusResistance( toch , 2 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModStone = damage;

							//"%s(%d)绰 籍蜡拳切(%d)俊 吧赴 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_160,
                                            toch->c.Name, ToList[i], damage);
						}

					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_160,
                                            toch->c.Name, ToList[i], damage);

							damage = (float) damage * work;
							toch->w.ModStone = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 籍蜡拳切(%d)栏肺 官诧 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_161,
                                            toch->c.Name, ToList[i], damage);


						}
					}
				break;

				case B_DANCE_INEBRIETY:
					//秒扁 包拌
//					print("%s(%d)绰 秒扁(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);

//					Swork = success * (toch->w.Drunk * 0.01);
//					success -= Swork;

//					if(success < 0 ) success = 0;

					//郴己 胶懦阑 爱绊 乐阑鳖
//					resist = BATTLE_StatusResistance( toch , 3 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModDrunk = damage;
							//"%s(%d)绰 秒扁(%d)俊 吧赴 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_162,
                                            toch->c.Name, ToList[i], damage);
						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_162,
                                            toch->c.Name, ToList[i], damage);


							damage = (float) damage * work;
							toch->w.ModDrunk = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_163,
                                            toch->c.Name, ToList[i], damage);
						}
					}
				break;

				case B_DANCE_CONFUSION:
					//去鄂 包拌
//					print("%s(%d)绰 去鄂(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);
//					resist = BATTLE_StatusResistance( toch , 4, &point, &action);

//					Swork = success * (toch->w.Confusion * 0.01);
//					success -= Swork;
//					if(success < 0 ) success = 0;


					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//刀己傍
							flg |= BM_FLAG_SUCCESS;
							toch->w.ModConfusion = damage;

//							"%s(%d)绰 去鄂(%d)俊 吧赴 \n",
//							snprintf( szBuffer, sizeof(szBuffer),
//
//							toch->c.Name, ToList[i], damage);
//							BATTLE_BroadCast( battleindex, szBuffer, CHAR_COLORYELLOW );

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
//						print("success=%d ", success);

						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
						//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_164,
                                            toch->c.Name, ToList[i], damage);


							damage = (float) damage * work;
							toch->w.ModConfusion = damage;
							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);

							//"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 去鄂(%d)栏肺 官诧 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_165,
                                            toch->c.Name, ToList[i], damage);
						}
					}

				break;

				case B_DANCE_FORGET:
					//噶阿 包拌
//					print("%s(%d)绰 噶阿(%d)俊 吧赴 \n", toch->c.Name, ToList[i], damage);
					pBattle = &BattleArray[battleindex];


					//荤捞靛甫 掘绰促
					if(ToList[i]  < 10){k = 0;}
					else if(ToList[i] > 10){k = 1;}


					pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯

					j = BATTLE_Index2No(battleindex, toch);

					//鞍篮 荤恩牢啊
					if(pEntry[j].entrychara != toch) break;
					//镭绰 厘家甫 茫绰促
					amn = BATTLE_AmnesiaPoint( toch);
//					print("name=%s amn = %d \n", toch->c.Name, amn);

					if(amn == 0){//角菩
						break;
					}

//					Swork = success * (toch->w.Amnesia * 0.01);
//					success -= Swork;
//					if(success < 0 ) success = 0;


					//郴己阑 爱绊 乐阑鳖?
//					resist = BATTLE_StatusResistance( toch , 5 , &point, &action);

					if(resist == 0){
						//郴己阑 啊瘤瘤 臼疽促
						if( RAND(0, 99) < success) {
							//镭绰 厘家甫 扁撅
							toch->w.ModAmnesia = amn;

							//雀荐甫 扁撅
							pEntry[j].Amnesia = damage;
							flg |= BM_FLAG_SUCCESS;

//							"%s(%d)绰 噶阿(%d)俊 吧赴 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_166,
                                            toch->c.Name, ToList[i], damage);

							//噶阿阑 吧磨 荐 乐绢 目膏靛甫 荤侩且 荐 绝霸 登菌阑 锭
							BATTLE_AmnesiaCommandInit(battleindex, toch , amn , 1);

						}
					}else{
						//郴己阑 爱绊 乐菌促
						//point绰 厘家
						//action 理拱 角付府览焊沥
						//resist绰 郴己 焊沥(犬伏阑 临牢促)
						 success = success - resist;
						if(success < 0) success = 0;

						if( RAND(0, 99) < success) {
							//郴己 己傍
							//郴己阑 啊瘤瘤 臼疽促
							flg |= BM_FLAG_SUCCESS;
							work = (float)(100 + action) * 0.01;

                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_166,
                                            toch->c.Name, ToList[i], damage);

							damage = (float) damage * work;

							//镭绰 厘家甫 扁撅
							toch->w.ModAmnesia = amn;
							//雀荐甫 扁撅
							pEntry[j].Amnesia = damage;
							flg |= BM_FLAG_SUCCESS;

							//郴己 胶懦狼 版氰蔼阑 涝陛矫挪促
//							BATTLE_SkillPointResistance(battleindex, toch, point);


//							"弊矾唱 郴己捞 乐菌栏骨肺 %s(%d)绰 噶阿(%d)栏肺 官诧 \n",
                            BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_167,
                                            toch->c.Name, ToList[i], damage);

							//噶阿阑 吧磨 荐 乐绢 目膏靛甫 荤侩且 荐 绝霸 登菌阑 锭
							BATTLE_AmnesiaCommandInit(battleindex, toch , amn, 1);

						}
					}
				break;

			}
		}
		//酒公巴档 吧府瘤 臼促.磊 吧媚扼
		snprintf( szCommand, sizeof(szCommand),
		"%X|f%X|", ToList[i], flg);
		BATTLESTR_ADD( szCommand );
	}

//	BATTLESTR_ADD("FF|" );

	return TRUE;
}

#if 1
//*******************************************************
//鸥百 急琶(俺牢)
//*******************************************************
int  BATTLE_DanceTargetSelect( int battleindex, int attackerBid , int side, int type)
{
	int ret;
	int i;
	int cnt = 0;
	int ran;
	int ToList[10]={-1,-1,-1,-1,-1,-1,-1,-1,-1,-1};
	int BackToList[10]={-1,-1,-1,-1,-1,-1,-1,-1,-1,-1};
	Char *toch;

	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;

	switch( type){
		case	B_DANCE_POISON:			//刀
		case	B_DANCE_SLEEP:			//泪
		case	B_DANCE_STONE:			//籍
		case	B_DANCE_INEBRIETY:		//秒扁
		case	B_DANCE_CONFUSION:		//去鄂
		case	B_DANCE_FORGET:			//噶阿
				//怠静扁 OK 焊绊 酵促
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;

					//国结 盔龋拌啊 吧妨乐绰 荤恩篮 救蹬
//					if(	toch->w.ModPoison > 0 || toch->w.ModSleep > 0|| toch->w.ModStone > 0
//					 || toch->w.ModDrunk > 0 || toch->w.ModConfusion > 0|| toch->w.ModAmnesia > 0
//					){
						BackToList[cnt] = BATTLE_Bid2No( battleindex, toch );
//						continue;
//					}
					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}
		break;

		case 	B_DANCE_POWER:			//颇况诀
				//颇况诀牢阑 急琶
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					if(pEntry[i].damageUp[0] > 0){
						//国结 颇况诀 窍绊 乐绰 赤籍篮 救蹬
						BackToList[cnt] = BATTLE_Bid2No( battleindex, toch );
						continue;
					}

					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}

		break;

		case 	B_DANCE_POSITION:			//器瘤记 眉牢瘤
				//器瘤记 眉牢瘤 窍绰 荤恩阑 急琶(局肯悼拱阑 力寇窍促)
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					//局肯悼拱篮 救 凳
//					if(toch->i.WhichType == CHAR_TYPEPET) continue;


					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}

		break;

		case 	B_DANCE_PET_RETURN:			//脐牢淬绊
				//牢淬绰 局肯悼拱阑 茫绰促
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					//局肯悼拱 捞寇绰 救蹬
					if(toch->i.WhichType != CHAR_TYPEPET) continue;

					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}

		break;

		case B_DANCE_DEATH:			//溜荤
				//牢淬绰 局肯悼拱阑 茫绰促
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;

					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}
		break;
		case B_DANCE_STATUS_RECOVER:			//胶抛捞磐胶 捞惑 雀汗
				//牢淬绰 局肯悼拱阑 茫绰促
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					//国结 盔龋拌啊 吧妨乐绰 荤恩篮 救蹬
					if(	toch->w.ModPoison > 0 || toch->w.ModSleep > 0|| toch->w.ModStone > 0
					 || toch->w.ModDrunk > 0 || toch->w.ModConfusion > 0|| toch->w.ModAmnesia > 0
					){
						BackToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					}
					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}
		break;
		case	B_DANCE_BOMB:			//磊气
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					BackToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}
		break;
		case	B_DANCE_SACRIFICE:			//魂 力拱阑 官魔
				pBattle = &BattleArray[battleindex];
				pEntry = pBattle->Side[ side].Entry;	// 浚飘府 硅凯

				for(i = 0; i < 10 ; i++){
					toch =  pEntry[i].entrychara;
					//某腐磐狼 器牢磐绰 沥惑利牢啊?
					if(CHAR_CheckCharPointer( toch) == FALSE) continue;
					//磷绢乐绰 荤恩篮 救蹬
					if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
					BackToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					ToList[cnt] = BATTLE_Bid2No( battleindex, toch );
					cnt++;
				}
		break;


	}
	cnt--;

	//罚待栏肺 急琶茄促
	ran = RAND(0, cnt);
	ret = ToList[ran];
	//酒公档 绝菌栏搁 父老 府胶飘肺何磐
	if(ret == -1) {
		ret = BackToList[ran];
	}

	return ret;


}
#endif

//*******************************************************
//单固瘤 UP(颇况诀)
//*******************************************************
static BOOL BATTLE_DanceDamageUp(int battleindex, int attackerBid, int toNo, int  nWork, int nWork2)
{
	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;
	Char *ch, *toch;
	int k, j, i;
	int ToList[SIDE_OFFSET*4+1];
	char szCommand[256] = {0};


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;
	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	//鸥百捞 绝促
	if(ToList[0] == -1) return FALSE;

	//硅撇 怕胶农
	pBattle = &BattleArray[battleindex];


	for(i=0; ToList[i] != -1;i++)
	{
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		//沥惑利牢啊
		if(CHAR_CheckCharPointer( toch) == FALSE) continue;
		//磷绢乐绰 荤恩篮 救蹬
		if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;

		k = ToList[i] < 10 ?  0:1;
		//荤捞靛甫 掘绰促
		pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯
		j = BATTLE_Index2No(battleindex, toch);
		//酒流 傈捧俊 曼啊窍绊 乐绰 芭具
		if(CHAR_CheckCharPointer( pEntry[j].entrychara) == FALSE) continue;

		//国结 颇况诀 惑怕牢啊?
		if(pEntry[j].damageUp[0] > 0) {
			//国结 吧妨 乐绰 版快
			continue;
		}else{
			//汲沥
			pEntry[j].damageUp[1] = nWork2;	//单固瘤 N硅
			pEntry[j].damageUp[0] = nWork; //拌加 咀记荐
		}

		//酒公巴档 吧府瘤 臼促.磊 吧媚扼
		snprintf( szCommand, sizeof(szCommand),
		"%X|", ToList[i]);
		BATTLESTR_ADD( szCommand );
	}

	return TRUE;

}

//*******************************************************
//器瘤记 眉牢瘤
//*******************************************************
static BOOL BATTLE_DancePosition(
	BATTLE_CHARLIST *bEntryList,
	int battleindex,
	int attackerBid,
	int toNo,
	int entrycnt,
	int opt
)
{
	BATTLE_ENTRY  *pEntry;
	BATTLE_ENTRY  *pEntry2[2];
	BATTLE *pBattle;
	Char *ch, *toch;
	int k, j, i, l, m;
	int atkj;
	int ToList[SIDE_OFFSET*4+1];
	char szCommand[256] = {0};
	char szBuffer[512];
	int poslist[10]={-1,-1,-1,-1,-1,-1,-1,-1,-1,-1};
	int Dlist[10]={-1,-1,-1,-1,-1,-1,-1,-1,-1,-1};
	int poser;
	int Back;
	int pl_pos[2] ={-1,-1} , pe_pos = -1;
	int pl_j = 0, pe_j = 0;


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;
	atkj = BATTLE_Index2No(battleindex, ch);
	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	//鸥百捞 绝促
	if(ToList[0] == -1) return FALSE;

	//硅撇 怕胶农
	pBattle = &BattleArray[battleindex];

	//DUEL矫牢啊
	if( BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P ){
		for( i = 0 ; ToList[i] != -1 ; i++ ){
			toch = BATTLE_No2Bid( battleindex, ToList[i] );
			//沥惑利牢啊
			if(CHAR_CheckCharPointer( toch) == FALSE) continue;
			//磷绢乐绰 荤恩篮 救蹬
			if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
#ifdef PET_RIDE_BATTLE
			// 局肯悼拱?
			if( toch->i.WhichType == CHAR_TYPEPET ){
				Char	*player = (Char *) toch->wu.pet.riderChar;

				// 愁捞 扁备吝?
				if( ! CHAR_CheckCharPointer( player ) ){
					// 愁捞 扁备吝狼 局肯悼拱篮 OK
					continue;
				}
			}
#else
			//局肯悼拱档 救蹬
			if(toch->i.WhichType == CHAR_TYPEPET) continue;
#endif

			k = ToList[i] < 10 ?  0:1;
			//荤捞靛甫 掘绰促
			pEntry = pBattle->Side[k].Entry;	// 浚飘府 硅凯
			j = BATTLE_Index2No( battleindex, toch );
			//酒流 傈捧俊 曼啊窍绊 乐绰 芭具
			if(CHAR_CheckCharPointer( pEntry[j].entrychara ) == FALSE) continue;

			//措惑捞 敲饭捞绢
			if( toch->i.WhichType == CHAR_TYPEPLAYER ){
				// 局肯悼拱捞 林芒沁阑 版快绰
				// 弊 局肯悼拱 磊脚捞 锅龋俊 绝栏搁 咐捞 绝促
				if( j == atkj - 5 ){
					//器瘤记 眉牢瘤 窍绰 荤恩阑 殿废
					poslist[j] = attackerBid;
					Dlist[j] = attackerBid;
					continue;
				}
			}

			//器瘤记 眉牢瘤 窍绰 荤恩阑 殿废
			poslist[j]=ToList[i];
		}
	}else{
		//烹惑 傈捧矫
		int z;

		for( i = 0 ; ToList[i] != -1 ; i++ ){
			z = 0;
			toch = BATTLE_No2Bid( battleindex, ToList[i] );
			//沥惑利牢啊
			if(CHAR_CheckCharPointer( toch) == FALSE) continue;
			//磷绢乐绰 荤恩篮 救蹬
			if(CHAR_getFlg( toch, CHAR_ISDIE) == TRUE) continue;
#ifdef PET_RIDE_BATTLE
			// 局肯悼拱?
			if( toch->i.WhichType == CHAR_TYPEPET ){
				Char	*player = (Char *) toch->wu.pet.riderChar;

				// 愁捞 扁备吝?
				if( ! CHAR_CheckCharPointer( player ) ){
					// 愁捞 扁备吝狼 局肯悼拱篮 OK
					continue;
				}
			}
#else
			//局肯悼拱档 救蹬
			if(toch->i.WhichType == CHAR_TYPEPET) continue;
#endif


			k = ToList[i] < 10 ?  0:1;
			//荤捞靛甫 掘绰促
			pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯
			j = BATTLE_Index2No(battleindex, toch);
			//酒流 傈捧俊 曼啊窍绊 乐绰 芭具
			if(CHAR_CheckCharPointer( pEntry[j].entrychara) == FALSE) continue;

			//措惑捞 敲饭捞绢
			if(toch->i.WhichType == CHAR_TYPEPLAYER){
				//局肯悼拱捞 林芒沁阑 版快绰
				//弊 局肯悼拱 磊脚捞 锅龋俊 绝栏搁 咐捞 绝促
				if( j == atkj - 5 ){
					//器瘤记 眉牢瘤 窍绰 荤恩阑 殿废
					poslist[j] = attackerBid;
					Dlist[j] = attackerBid;
//					print("j=%d Atkj=%d attaker=%d", j , atkj, attackerBid);
					continue;
				}
			}
			//利篮 炼陛 抗寇
			if(pEntry[j].entrychara->i.WhichType == CHAR_TYPEENEMY){
				if(j >= 5){
					 z = j - 5;
				}else{
					 z = j + 5;
				}
				//国结 器瘤记 眉牢瘤 场抄 包拌牢啊
				if( poslist[ z] != -1 ) continue;
			}
			//器瘤记 眉牢瘤 窍绰 荤恩阑 殿废
			poslist[j] = ToList[i];
		}
	}

	k = 0;


	//咯扁辑 器瘤记 眉牢瘤狼 贸府
	// 硅撇 牢郸胶甫 眉农
	for(m = 0; m < 10 ; m++){
		l = 0;
		pl_pos[0] = -1;
		pl_pos[1] = -1;
		pe_pos = -1;
		pl_j = 0;
		pe_j = 0;

//		print("m=%d, %d) \n", m, poslist[m]);
		if(poslist[m] < 0) continue;
		poser = poslist[m];
		ch = BATTLE_No2Bid(battleindex, poser);
		if(CHAR_CheckCharPointer( ch) == FALSE) continue;

		if(ch->i.WhichType == CHAR_TYPEPET){
			Char *toch;
#ifdef PET_RIDE_BATTLE
			toch = (Char *) ch->wu.pet.riderChar;

			// 愁捞 扁备吝?
			if( ! CHAR_CheckCharPointer( toch ) ){
				toch = (Char *) ch->wu.pet.PlayerChar;
				ch = toch;
				if( poser < 5 ){
					poser = poser + 5;
				}else if( poser >= 5 && poser < 10 ){
					poser = poser - 5;
				}if( poser >= 15 ){
					poser = poser - 5;
				}else if( poser >= 10 && poser <15 ){
					poser = poser + 5;
				}
			}
#else
			toch = (Char *) ch->wu.pet.PlayerChar;
			ch = toch;
			if( poser < 5 ){
				poser = poser + 5;
			}else if( poser >= 5 && poser < 10 ){
				poser = poser - 5;
			}if( poser >= 15 ){
				poser = poser - 5;
			}else if( poser >= 10 && poser <15 ){
				poser = poser + 5;
			}
#endif
		}

		//傍拜磊狼 泅犁狼 困摹甫 毫 局肯悼拱狼 厘家甫 茫绰促
		if(poser < 5 || (poser >=10 && poser <15) ){
			k = 5;	//脐篮 敲饭捞绢狼 傈捞促
		}else{
			k = -5;	//脐篮 敲饭捞绢狼 第促
		}

		//浚飘府 府胶飘(1 畔)绰 包府 锅龋父 背眉 2 咀记侩
		for( i = 0 ; i < entrycnt ; i++ ){
			if((bEntryList[i].side * SIDE_OFFSET+ bEntryList[i].num) != poser) continue;
			//磊脚狼 entrycnt甫 掘绰促
			pl_pos[l] = i;
			l = 1;
		}

		//促澜篮 局肯悼拱狼 entrycnt甫 茫绰促
		//绝栏搁—1
		for( j = 0 ; j < entrycnt ; j++ ){
			if((bEntryList[j].side * SIDE_OFFSET + (bEntryList[j].num) ) != (poser + k)) continue;
			//局肯悼拱狼 entrycnt甫 掘绰促
			pe_pos = j;
		}

		//局肯悼拱捞 绝菌促
		if(pe_pos == -1) {
			if(pl_pos[1] == -1){
				//敲饭捞绢父 器瘤记阑 官槽促(包府 锅龋)
				bEntryList[ pl_pos[0] ].num = bEntryList[ pl_pos[0] ].num + k;
				bEntryList[ pl_pos[0] ].position = bEntryList[pl_pos[0]].position == 0?  1 :0 ;

			}else{
				for(l = 0; l< 2; l++){
					//目膏靛 2雀 葛滴 器瘤记 眉牢瘤甫 急琶沁阑 版快
					bEntryList[pl_pos[l]].num =
						 (poser - bEntryList[pl_pos[0]].side * SIDE_OFFSET)  + k;
				}
			}
		}else{
			int work;
//			print("甸绢埃 =%d 其 =%d", pl_pos[0], pe_pos);
			//局肯悼拱苞狼 背眉
//			print("傈=%d \n", bEntryList[pl_pos[0]].num);
//			print("傈=%d \n", bEntryList[pe_pos].num);

			Back = bEntryList[pe_pos].num;
			bEntryList[pe_pos].num = bEntryList[pl_pos[0]].num;
			bEntryList[pl_pos[0]].num = Back;

//			print("饶 =%d \n", bEntryList[pl_pos[0]].num);
//			print("饶 =%d \n", bEntryList[pe_pos].num);


			work = bEntryList[pe_pos].position;
			bEntryList[pe_pos].position = bEntryList[pl_pos[0]].position;
			bEntryList[pl_pos[0]].position = work;

		}

		pe_pos = -1;
		pe_j= 0;
		pl_j = 0;
		pl_pos[0] = -1;
		pl_pos[1] = -1;


		//皋牢 浚飘府绰 葛滴 背眉
		//快急, 敲饭捞绢啊 绢叼俊 殿废登绢 乐阑鳖
		for( j = 0 ;j  < 2 ; j++){
			pEntry2[j] = pBattle->Side[j].Entry;

			for( i = 0; i < BATTLE_ENTRY_MAX; i ++ ){
				// 浚飘府 登绊 乐阑鳖?
				if(CHAR_CheckCharPointer( pEntry2[j][i].entrychara) ==FALSE)	continue;
				if(pEntry2[j][i].side * SIDE_OFFSET + pEntry2[j][i].bid != poser) continue;
				pl_j = j;
				pl_pos[0] = i;
			}
		}

			//敲饭捞绢啊 蝶福绊 乐绰 局肯悼拱篮 乐阑鳖
			//乐绰 版快绰 绢叼俊 殿废登绢 乐阑鳖
			for( j = 0 ;j  < 2 ; j++){
				pEntry2[j] = pBattle->Side[j].Entry;

				for( i = 0; i < BATTLE_ENTRY_MAX; i ++){
					// 浚飘府 登绊 乐阑鳖?
					if(CHAR_CheckCharPointer( pEntry2[j][i].entrychara) ==FALSE)	continue;
					if(pEntry2[j][i].side * SIDE_OFFSET + pEntry2[j][i].bid != poser + k) continue;
					pe_pos = i;
					pe_j = j;
				}
			}


			if(pe_pos == -1) {
#ifdef PUK2
				// 傍拜罐篮 雀荐甫 官层 持绰促
				Targetlist[pEntry2[pl_j][pl_pos[0]].bid + k] = Targetlist[pEntry2[pl_j][pl_pos[0]].bid];
#endif
				//包府 锅龋狼 函版
				pEntry2[pl_j][pl_pos[0]].bid = pEntry2[pl_j][pl_pos[0]].bid + k;

				//器瘤记 锅龋 背眉
				pEntry2[pl_j][pl_pos[0]].position =
					(pEntry2[pl_j][pl_pos[0]].position ==0) ?  1:0;

			}else{
#ifdef PUK2
				// 傍拜罐篮 雀荐甫 官层 持绰促
				Back = Targetlist[pEntry2[pe_j][pe_pos].bid];
				Targetlist[pEntry2[pe_j][pe_pos].bid] = Targetlist[pEntry2[pl_j][pl_pos[0]].bid];
				Targetlist[pEntry2[pl_j][pl_pos[0]].bid] = Back;
#endif
				//浚飘府郴狼 沥焊甫 背眉
				Back = pEntry2[pe_j][pe_pos].bid;
				pEntry2[pe_j][pe_pos].bid = pEntry2[pl_j][pl_pos[0]].bid;
				pEntry2[pl_j][pl_pos[0]].bid = Back;
				//器瘤记 锅龋 背眉
				Back = pEntry2[pe_j][pe_pos].position;
				pEntry2[pe_j][pe_pos].position = pEntry2[pl_j][pl_pos[0]].position;
				pEntry2[pl_j][pl_pos[0]].position = Back;

			}



		BATTLE_DEBUGLOG(battleindex, szBuffer, LANG_MSG_CHAR_BATTLE_C_112, ch->c.Name);

		if(Dlist[m] == poslist[m]){
			// 弊 寇 傍拜 某腐磐 扼牢 汲沥
			sprintf( szCommand, "%X|", poslist[m]);
			BATTLESTR_ADD( szCommand );
		}else{
			// 弊 寇 傍拜 某腐磐 扼牢 汲沥
			sprintf( szCommand, "%X|", poser);
			BATTLESTR_ADD( szCommand );
		}
	}

	return TRUE;

}

//********************************************************
//局肯悼拱牢淬绰促
//********************************************************
static BOOL BATTLE_DancePetReturn(int battleindex, int attackerBid, int toNo, int type)
{

	BATTLE *pBattle;
	Char *ch, *och, *pch, *tch;
	int i;
	int pet;
	int ToList[SIDE_OFFSET*4+1];
	char szCommand[256] = {0};


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;
	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	//鸥百捞 绝促
	if(ToList[0] == -1) return FALSE;

	//硅撇 怕胶农
	pBattle = &BattleArray[battleindex];

	//府胶飘甫 风橇
	for(i=0; ToList[i] != -1;i++){
		Char *toch;
		tch = BATTLE_No2Bid( battleindex, ToList[i] );
		//沥惑利牢啊
		if(CHAR_CheckCharPointer( tch) == FALSE) continue;
		//磷绢乐绰 荤恩篮 救蹬
		if(CHAR_getFlg( tch, CHAR_ISDIE) == TRUE) continue;

		if(tch->i.WhichType == CHAR_TYPEPLAYER ){
			pet = tch->iu.player.DefaultPet;
			if(pet == -1 ) continue;
			toch = (Char *) tch->player_addon->PetPointer[pet];
			if(CHAR_CheckCharPointer( toch) == FALSE) continue;
			ToList[i] =  BATTLE_Bid2No(battleindex, toch);
		}else
		if(tch->i.WhichType != CHAR_TYPEPET ) {
			continue;
		}else{
			toch = tch;
		}
#ifdef PET_RIDE_BATTLE
		// 局肯悼拱?
		if( toch->i.WhichType == CHAR_TYPEPET ){
			Char	*player = (Char *) toch->wu.pet.riderChar;

			// 愁捞 扁备吝?
			if( CHAR_CheckCharPointer( player ) ){
				// 愁捞 扁备吝狼 版快绰 牢赫厚瘤 臼绰促
				continue;
			}
		}
#endif

		//坷呈狼 器牢磐
		och = toch->wu.pet.PlayerChar;
		if(CHAR_CheckCharPointer( och) == FALSE) continue;
		//磷绢乐绰 荤恩篮 救蹬
		if(CHAR_getFlg( och, CHAR_ISDIE) == TRUE) continue;

		//局肯悼拱捞 盒疙窍霸 档噶牢啊.
		if(BATTLE_PetDefaultExit( och , battleindex )!= BATTLE_ERR_NONE) continue;

		pet = och->iu.player.DefaultPet;
		pch = (Char *) och->player_addon->PetPointer[pet];
		//沥惑利牢啊
		if( CHAR_CheckCharPointer( pch) == FALSE) continue;

		// 某腐磐 扼牢 汲沥(登倒赴促)
		sprintf( szCommand, "%X|", ToList[i]);
		BATTLESTR_ADD( szCommand );

		//局肯悼拱阑 措扁肺 函版
#ifdef PUK2
		// 悼馆 吧澜狼 敲贰弊绰 巢变盲肺
		pch->iu.pet.DepartureBattleStatus &= CHAR_PET_WALK;
		pch->iu.pet.DepartureBattleStatus |= CHAR_PET_BATTLE_STANDBY;
#else
		pch->iu.pet.DepartureBattleStatus = CHAR_PET_BATTLE_STANDBY;
#endif
		och->iu.player.DefaultPet = -1;

		//局肯悼拱 盎脚
		CHAR_send_KP_String( och, pet, CHAR_KP_DEPARTUREBATTLESTATUS|CHAR_KP_INJURY);

	}

	return TRUE;
}

//******************************************************
//溜荤
//******************************************************
static BOOL BATTLE_DanceDeath(int  battleindex, int attackerBid, int  toNo, int  type)
{
	Char *ch;
	Char *toch;

	int i;
	int ToList[SIDE_OFFSET*4+1];
	int flg;
	int aves;
	char szBuffer[256];
	char szCommand[256];
	int pAttackList[10];

	BATTLE_MultiList( battleindex, attackerBid, toNo, ToList, 0);

	//某腐磐 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );

	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( ch ) == FALSE);
    EmitBattleActionTargetEvent(ch, ToList);


	//犬伏阑 夸备茄促
//	aves =  BATTLE_Aves( battleindex, ch);
	//犬伏篮 100%牢啊
	aves = 100;

	// 绢蠢 颇扼固磐肺 且鳖
	for( i = 0; ToList[i] != -1; i ++ ){
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );

		//某腐磐狼 器牢磐 眉农
		if( CHAR_CheckCharPointer( toch ) == FALSE) continue;

		flg = 0;

		//己傍牢啊 角菩牢啊
		if(RAND(0,99) < aves){
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊

			toch->i.Hp =0;
			flg |= BM_FLAG_DEATH;
#ifdef PET_RIDE_BATTLE
			// 局肯悼拱?
			if( toch->i.WhichType == CHAR_TYPEPET ){
				Char	*player = (Char *) toch->wu.pet.riderChar;

				// 愁捞 扁备吝?
				if( CHAR_CheckCharPointer( player ) ){
					// 愁捞 扁备矫绰, 剧率 葛滴 悼矫俊 荤噶
					player->i.Hp = 0;
					toch->wu.pet.petHp = 0;
				}
			}
#endif

			//荤噶 敲贰弊甫 技款促
//			CHAR_setFlg( toch, CHAR_ISDIE, TRUE);

			pAttackList[0] = BATTLE_Bid2No(battleindex, ch);
			pAttackList[1] = -1;


			//版氰瘤, 捣, 酒捞袍殿阑 GET
			BATTLE_AddProfit(battleindex, ch, toch, pAttackList);

			//荤噶 敲贰弊甫 技款促
			CHAR_setFlg( toch, CHAR_ISDIE, TRUE);
#ifdef PUK2_SKILL
			// 敲饭捞绢?
			if( toch->i.WhichType == CHAR_TYPEPLAYER ){
				// 府滚胶 窍绊 乐绢?
				if( toch->w.RebirthFlag ){
					// 府滚胶 秦力
					toch->w.RebirthFlag = 0;
					toch->w.RebirthView = 0;
				}
				// 剧滴 俺绊扁档 秦力
				toch->wu.player.copyChar = NULL;
				toch->wu.player.copyCount = 0;
				// 弊贰侨阑 盔狼 葛嚼俊
				CHAR_updateGraphic( toch );
			}
#else
#ifdef PUK2
			// 府滚胶 窍绊 乐绢?
			if( toch->w.RebirthFlag ){
				// 府滚胶 秦力
				toch->w.RebirthFlag = 0;
				toch->w.RebirthView = 0;
				CHAR_updateGraphic( toch );
			}
#endif
#endif

			//惑贸 器牢飘 啊魂
//			if(BATTLE_InjurySet( toch, ch, 1, 0) != -1){
//				flg |= BM_FLAG_INJURY;	// 惑贸 敲贰弊
//			}

			//勉眠绰 漂喊 扁例 贸府
			BATTLE_DanceNormalDeadExtra(battleindex, ch , toch);


		}else{
//			"(%s)(篮)绰 溜荤甫 荤捞 乔茄",
            BATTLE_DEBUGLOG2(battleindex, szBuffer,
                             (attackerBid >= 10) ? CHAR_COLORGRAY : CHAR_COLORPURPLE,
                             LANG_MSG_CHAR_BATTLE_C_188,
                             toch->c.Name,
                             aves
            );
		}

		snprintf( szCommand, sizeof(szCommand),
			"%X|f%X|", ToList[i], flg);
		BATTLESTR_ADD( szCommand );

	}
	return TRUE;

}
//*******************************************************
//胶抛捞磐胶 捞惑 雀汗
//*******************************************************
static BOOL BATTLE_DanceStatusRecover(int battleindex, int atackerBid, int toNo, int type)
{

	int i, tostatus = 0;
	int ToList[SIDE_OFFSET*4+1];
	int flg,  successrate = 0;
	char szCommand[256] = {0};
//	char szBuffer[256];
	Char *ch;
	Char *toch;


	//雀汗窍绰 荤恩阑 府胶飘肺 茄促
	BATTLE_MultiList( battleindex, atackerBid, toNo, ToList, 0 );

	ch = BATTLE_No2Bid( battleindex, atackerBid );

	if(CHAR_CheckCharPointer( ch ) == FALSE) return FALSE;
    EmitBattleActionTargetEvent(ch, ToList);

	//己傍啦(泅犁绰 100%)
	successrate = 100;

	// 胶抛捞磐胶 捞惑 雀汗矫挪促
	for( i = 0; ToList[i] != -1; i ++ ){
		flg = 0;
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		if(CHAR_CheckCharPointer( toch) == FALSE) continue;

		// 惑措啊 绢蠢 胶抛捞磐胶牢啊?
		tostatus = 0;

		//己傍啦
		if(RAND(0,99) < successrate){
			//己傍
		}else{
			//角菩
			tostatus  = 2;
		}

		//刀
		if(toch->w.ModPoison > 0){
			toch->w.ModPoison = 0;
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}
		//泪
		if(toch->w.ModSleep > 0){
			toch->w.ModSleep = 0;
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}
		//籍蜡拳切
		if(toch->w.ModStone > 0){
			toch->w.ModStone = 0;
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}
		//秒扁
		if(toch->w.ModDrunk > 0){
			toch->w.ModDrunk = 0;
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}
		//去鄂
		if(toch->w.ModConfusion > 0){
			toch->w.ModConfusion = 0;
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}
		//噶阿
		if(toch->w.ModAmnesia > 0){
			toch->w.ModAmnesia = 0;
			// 己傍矫
			flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊
		}

		//
		if(flg & BM_FLAG_SUCCESS) {
			//======== 咯扁辑 己傍牢啊 绢恫啊甫 魄沥矫挪促 ==========
			snprintf( szCommand, sizeof(szCommand),
				"%X|f%X|", ToList[i], flg);
			BATTLESTR_ADD( szCommand );
		}
	}
	return TRUE;

//	BATTLESTR_ADD("FF|" );




}

//*******************************************************
//扁例 何劝
//*******************************************************
static BOOL BATTLE_DanceRevive(int  battleindex, int attackerBid, int toNo, int type)
{
	int i;
	int ToList[SIDE_OFFSET*4+1];
	int flg = 0,  successrate = 0;
	char szCommand[256] = {0};
//	char szBuffer[256];
	Char *ch;
	Char *toch;
	int workhp = 0;
	int per, power;
	float work;
	int UpPoint;

	//雀汗窍绰 荤恩阑 府胶飘肺 茄促
	BATTLE_MultiListDead( battleindex, attackerBid,  toNo, ToList, flg );

	ch = BATTLE_No2Bid( battleindex, attackerBid );
	if(CHAR_CheckCharPointer( ch ) == FALSE) return FALSE;
    EmitBattleActionTargetEvent(ch, ToList);

	//己傍啦(泅犁绰 100%)
	successrate = 100;

	// 扁例 何劝矫挪促
	for( i = 0; ToList[i] != -1; i ++ ){

		// 措惑狼 某腐磐 器牢磐
		toch = BATTLE_No2Bid( battleindex, ToList[i]);
		flg = 0;
		workhp = 0;
        if (!CHAR_CheckCharPointer(toch)) {
            eprint("ERROR: toch is null %d %d\n", battleindex, ToList[i]);
            continue;
        }
		//勉冕 荤恩阑 鞍栏搁 救 凳
		if(toch == ch) continue;

		// 扁例窍绊 乐瘤 臼栏搁 何劝矫虐瘤 臼促
		if( CHAR_getFlg( toch, CHAR_ISDIE ) == FALSE ){

			snprintf( szCommand, sizeof(szCommand),
				"%X|f%X|%X|", ToList[i], flg, workhp);
			BATTLESTR_ADD( szCommand );
			continue;
		}

		//雀汗樊(MAX狼 20~30%肺 且鳖)
		per = RAND(20,30);

		// 弊措肺狼 拌魂
		work = per * 0.01;
		power  =  (float) toch->w.MaxHp * work;
		UpPoint = power;
		// 雀汗樊俊 气阑 爱霸茄促(瘤陛篮 利寸)
//		UpPoint = RAND( (power*0.0), (power*1.0) );
		// 弥厩捞绢档 1
        UpPoint = EmitBattleHealCalculateEvent(ch, toch, UpPoint,
                                                 HEAL_DMG_FLG_Heal,
                                                 HEAL_DMG_FLG_EXT_HP);
		if(UpPoint <= 0) UpPoint = 1;
		UpPoint = max( 1, UpPoint );

		workhp = toch->i.Hp + UpPoint;
		toch->i.Hp = min( workhp, toch->w.MaxHp );

		// 何劝
		CHAR_setFlg( toch, CHAR_ISDIE, 0 );

		// (何劝) 目膏靛甫 持绰促
		flg |= BM_FLAG_SUCCESS;	// 己傍 敲贰弊

		//扁例 雀汗 敲贰弊
		if(toch->w.BattleFlg & CHAR_BATTLEFLG_DEATHHEAL){
			//茄 锅 扁例 雀汗沁促
		}else{
//			toch->w.BattleFlg |= CHAR_BATTLEFLG_DEATHHEAL;
			//霉扁例 雀汗
			if(toch->i.WhichType == CHAR_TYPEPLAYER){
				if(toch->iu.player.Penalty > 0
				&& (toch->w.BattleFlg & CHAR_BATTLEFLG_NOWDEATH)
				 ){
					if(toch->w.BattleFlg & CHAR_BATTLEFLG_FIVEDEATH){
						toch->iu.player.Penalty = 5;
					}else{
						toch->iu.player.Penalty--;
						if(toch->iu.player.Penalty < 0) toch->iu.player.Penalty = 0;
#if 1
						snprintf( szCommand, sizeof( szCommand),
						 "CHT|14|%X|3|0|", CHAR_COLORYELLOW
						 );
						//惑贸唱 公扁 颇鲍狼 某腐磐 扼牢侩
						BATTLESTR_TAIL( szCommand);

						snprintf( szCommand, sizeof( szCommand),
							 TMSG(LANG_MSG_CHAR_BATTLE_C_194),
							toch->c.Name
						 );
						BATTLESTR_TAIL( szCommand);
#endif
					}
				}

				toch->w.BattleFlg &= ~CHAR_BATTLEFLG_NOWDEATH;
			}
		}

		//某腐磐 扼牢 累己
		snprintf( szCommand, sizeof(szCommand),
			"%X|f%X|%X|", ToList[i], flg, workhp);
		BATTLESTR_ADD( szCommand );

		// 利苞狼 傈捧吝俊辑 府胶农啊 绝菌栏搁
		if( BattleArray[battleindex].norisk == 0
		 &&	BattleArray[battleindex].type == BATTLE_TYPE_P_vs_E
		 ){
			// 咯扁辑 局肯悼拱捞扼搁 面己摹甫 敲矾胶 茄促
			if( toch->i.WhichType == CHAR_TYPEPET ){
				//面己 函拳摹
//				CHAR_PetAddVariableAi( toch, AI_FIX_PETRESSURECT );
				//家积窍霸 秦 霖 荐
				ch->iu.pet.ResurrectedCount++;
			}


			//家积窍霸 秦 霖 荤恩捞 牢埃栏肺 惑措啊 局肯悼拱捞扼搁
			if( ch->i.WhichType == CHAR_TYPEPLAYER
			 && toch->i.WhichType == CHAR_TYPEPET )
			{
				ch->iu.player.ResurrectPetCount++;
//				print("ResurrectPetCount=%d \n", ch->iu.player.ResurrectPetCount);
			}
		}


	}
	return TRUE;

//	BATTLESTR_ADD("FF|" );

}
//*******************************************************
//勉眠绰 磊气
//*******************************************************
static BOOL BATTLE_DanceBomb(int  battleindex, int  attackerBid, int  toNo, int type)
{
	Char *ch;
	Char *toch=NULL;
	int hp, damage;
	int cnt2= 0;
	int i , j=0;
	int addpoint=0,
		maxhp,
		IsUltimate=0;
	int ref_list[20];
	int ToList[SIDE_OFFSET*4+1];
	int flg;
	int aves;
	int SubNo, side;
//	char szBuffer[256];
	char szCommand[256];

	//窜眉 捞寇甫 急琶窍绊 乐绰 版快绰 救蹬
	if(toNo >= 20) return FALSE;

	//磊气篮 抗寇肺 鳖促酚促
	attackerBid = toNo;
	//某腐磐 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( ch ) == FALSE) return FALSE;

	//绢蠢 率 荤捞靛牢啊
	toNo = attackerBid < 10 ?  41:40;
	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid, toNo, ToList, 0);
    EmitBattleActionTargetEvent(ch, ToList);

	if(ToList[0] == -1) return FALSE;

	//犬伏阑 夸备茄促(瘤陛篮 100%)
	aves =  100;

	//磊气磊狼 HP
	hp = ch->i.Hp;

	//割 荤恩 混酒 巢绊 乐绢?
	for( i = 0; ToList[i] != -1; i ++ ){
		cnt2++;
	}

	if(cnt2 == 0) return FALSE;

	//茄荤恩 寸狼 单固瘤
	damage = hp / cnt2;

	//单固瘤绰 0篮 1栏肺 茄促
	if(damage <= 0) damage = 1;


	//抗 SKL|2008|20|aB|d0|f61|100|d1|f1|100|FF|
	snprintf( szCommand, sizeof(szCommand),
		"%X|", attackerBid);
	BATTLESTR_ADD( szCommand );

	// 绢蠢 颇扼固磐肺 且鳖
	for( i = 0; ToList[i] != -1; i ++ ){
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );

		//某腐磐狼 器牢磐 眉农
		if( CHAR_CheckCharPointer( toch ) == FALSE) continue;
        damage = EmitDamageCalculateEvent(ch, toch, damage,
                                          BATTLE_DMG_FLG_Boom,
                                          BATTLE_DMG_FLG_EXT_NONE);
		flg = 0;

		hp = toch->i.Hp;
		hp -= damage;


		if( hp < 0 ){ // HP啊 0 捞窍啊 登搁
			addpoint = -hp;	// 倔萍皋捞飘摹肺 茄促
			hp = 0;
		}

		flg |= BM_FLAG_NORMAL;

		// 弥措
		maxhp = toch->w.MaxHp;
		// 技飘
		toch->i.Hp = min( hp, maxhp );
#ifdef PET_RIDE_BATTLE
#if 1
		// 愁捞 扁备矫狼 LP盒硅 贸府
		BATTLE_rideLpDivision( toch );
#else
		// 局肯悼拱?
		if( toch->i.WhichType == CHAR_TYPEPET ){
			// 愁捞 扁备吝?
			if( CHAR_CheckCharPointer( toch->wu.pet.riderChar ) ){
				Char	*player = toch->wu.pet.riderChar;

				// 剧率 葛滴 磷绢 乐绢?
				if( toch->i.Hp <= 0 ){
					player->i.Hp = 0;
					toch->wu.pet.petHp = 0;
				}else{
					int		alldam;
					int		pldam, petdam;

					// 罐篮 单固瘤甫 夸备茄促
					alldam = ( player->i.Hp + toch->wu.pet.petHp ) - toch->i.Hp;
					// 单固瘤甫 馆究
					pldam = alldam / 2;
					petdam = alldam - pldam;
					// 殿盒 且 荐 乐绢?
					if( pldam != petdam ){
						// HP狼 厚窖 (盒)祈俊霸 场荐狼 单固瘤甫 霖促
						if( player->i.Hp > toch->wu.pet.petHp ){
							int		tmp;

							// 场荐啊 唱坷绰 版快绰 局肯悼拱俊霸 唱坷骨肺,
							// 敲饭捞绢啊 HP啊 臭篮 版快绰 单固瘤 背眉
							tmp = pldam;
							pldam = petdam;
							petdam = tmp;
						}
					}
					// HP肺何磐 单固瘤甫 临牢促
					player->i.Hp -= pldam;
					toch->wu.pet.petHp -= petdam;
					// HP啊 0阑 唱穿菌阑 版快绰, 捞力(国结) 茄祈栏肺何磐 弊 父怒阑 临牢促
					if( player->i.Hp < 1 ){
						toch->wu.pet.petHp = toch->i.Hp - 1;
						player->i.Hp = 1;
					}
					if( toch->wu.pet.petHp < 0 ){
						player->i.Hp = toch->i.Hp;
						toch->wu.pet.petHp = 0;
					}
				}
			}
		}
#endif
#endif

		//单固瘤 墨款飘 UP
		toch->i.DamageCount++;

		if( (damage) >= maxhp * 1.2 + 20 ){
			// 捞 1惯捞 倔萍皋捞飘摹扼搁 溜倔萍皋捞飘 KO
			IsUltimate = 2;
		}else{
			if( addpoint > 0 ){
				// 倔萍皋捞飘啊 穿利窍搁(磊)
				addpoint += toch->w.Ultimate;
				toch->w.Ultimate = addpoint;
				if( addpoint >= maxhp * 1.2 + 20 ) IsUltimate = 1;
			}
		}

		if( IsUltimate ){
			// 倔萍皋捞飘扼搁 努府绢
			toch->w.Ultimate = 0;
		}

		//磊绊 乐栏搁(磊) 敌促
		if(toch->w.ModSleep > 0) {
			toch->w.ModSleep = 0;
			flg |= BM_FLAG_WAKE_UP;
		}

		//倔萍皋捞飘牢啊 绢恫啊?
		if(toch->i.Hp <= 0 && IsUltimate != 0)
		{
			toch->i.Hp = 0;
			flg |= BM_FLAG_AKO1;
			// 颇萍肺何磐档 狐柳促
	//		CHAR_DischargePartyNoMsg( defch );
			// 磊脚篮 傈捧肺何磐 狐柳促
			toch->w.BattleFlg |= CHAR_BATTLEFLG_ULTIMATE;
			// 狐柳 饶 某腐磐甫 傈捧 辆丰 惑怕肺 茄促.
			toch->w.BattleMode= BATTLE_CHARMODE_FINAL;

			//局肯悼拱 叼弃飘狼 傈捧惑怕甫 秦力
			if(toch->i.WhichType == CHAR_TYPEPET ){
				int pet = -1;
				Char *tch;

				tch  =(Char *) toch->wu.pet.PlayerChar;

				pet = tch->iu.player.DefaultPet;
				tch->iu.player.DefaultPet = -1;

				toch->iu.pet.DepartureBattleStatus = 0;
				CHAR_send_KP_String( tch, pet, CHAR_KP_DEPARTUREBATTLESTATUS);

			}

			//某腐磐 扼牢侩
			flg |= BM_FLAG_DEATH;

			//荤噶 敲贰弊甫 技款促
			CHAR_setFlg( toch, CHAR_ISDIE, TRUE);
#ifdef PUK2_SKILL
			// 敲饭捞绢?
			if( toch->i.WhichType == CHAR_TYPEPLAYER ){
				// 府滚胶 窍绊 乐绢?
				if( toch->w.RebirthFlag ){
					// 府滚胶 秦力
					toch->w.RebirthFlag = 0;
					toch->w.RebirthView = 0;
				}
				// 剧滴 俺绊扁档 秦力
				toch->wu.player.copyChar = NULL;
				toch->wu.player.copyCount = 0;
				// 弊贰侨阑 盔狼 葛嚼俊
				CHAR_updateGraphic( toch );
			}
#else
#ifdef PUK2
			// 府滚胶 窍绊 乐绢?
			if( toch->w.RebirthFlag ){
				// 府滚胶 秦力
				toch->w.RebirthFlag = 0;
				toch->w.RebirthView = 0;
				CHAR_updateGraphic( toch );
			}
#endif
#endif

			//版氰瘤, 捣, 酒捞袍殿阑 GET
//			BATTLE_GetEXPTmp(battleindex, ch, toch, 1);

			//扁例 墨款飘 UP
			toch->i.DeadCount++;

			//惑贸 器牢飘 啊魂
			if(BATTLE_InjurySet( toch, ch, 0, 0) != -1){
				flg |= BM_FLAG_INJURY;	// 惑贸 敲贰弊
			}

			//勉眠绰 倔萍皋捞飘 贸府
			BATTLE_UltimateExtra(battleindex, ch, toch);
//			BATTLE_DanceUltimateExtra(battleindex, ch, toch);

		}else if(toch->i.Hp <= 0){
			toch->i.Hp =0;
			flg |= BM_FLAG_DEATH;

			//荤噶 敲贰弊甫 技款促
			CHAR_setFlg( toch, CHAR_ISDIE, TRUE);
#ifdef PUK2_SKILL
			// 敲饭捞绢?
			if( toch->i.WhichType == CHAR_TYPEPLAYER ){
				// 府滚胶 窍绊 乐绢?
				if( toch->w.RebirthFlag ){
					// 府滚胶 秦力
					toch->w.RebirthFlag = 0;
					toch->w.RebirthView = 0;
				}
				// 剧滴 俺绊扁档 秦力
				toch->wu.player.copyChar = NULL;
				toch->wu.player.copyCount = 0;
				// 弊贰侨阑 盔狼 葛嚼俊
				CHAR_updateGraphic( toch );
			}
#else
#ifdef PUK2
			// 府滚胶 窍绊 乐绢?
			if( toch->w.RebirthFlag ){
				// 府滚胶 秦力
				toch->w.RebirthFlag = 0;
				toch->w.RebirthView = 0;
				CHAR_updateGraphic( toch );
			}
#endif
#endif

			//版氰瘤, 捣, 酒捞袍殿阑 GET
//			BATTLE_GetEXPTmp(battleindex, ch, toch , 1);

			//扁例 墨款飘 UP
			toch->i.DeadCount++;
			//惑贸 器牢飘 啊魂
			if(BATTLE_InjurySet( toch, ch, 1, 0) != -1){
				flg |= BM_FLAG_INJURY;	// 惑贸 敲贰弊
			}

			//勉眠绰 畴钢 荤噶 贸府
			BATTLE_NormalDeadExtra(battleindex, ch , toch);
//			BATTLE_DanceNormalDeadExtra(battleindex, ch , toch);

		}else{
			ref_list[j] = ToList[i];
			j++;
		}

		// 割俺(混) 雀汗沁绰瘤 角炔吝拌
//		BATTLE_BroadCast( battleindex, szBuffer,
//			(attackNo >= 10)?  CHAR_COLORGRAY : CHAR_COLORPURPLE ) ;

		//规绢侩 扁备 郴备档甫 临牢促
		BATTLE_ItemDamage( toch , BATTLE_BREAK_PROTECTIVE);

		//公扁, 规绢侩 扁备啊 绊厘唱绊 眉农
		flg += BATTLE_EquipBreakFlgCheck(ch, toch , 1);
		{
//			int Sign, Damage;
//			Sign = (UpPoint >= 0)? (1):(0);
//			Damage = ABS( UpPoint );
			// 咯扁辑 目膏靛甫 持绰促

			snprintf( szCommand, sizeof(szCommand),
				"%X|f%X|d%X|", ToList[i], flg, damage);
			BATTLESTR_ADD( szCommand );
		}

		//公扁 噶啊柳 某腐磐 扼牢阑 累己
		BATTLE_EquipBreakStringCheck( ch, toch , 1);

	}

	if( attackerBid >= SIDE_OFFSET ){
		// 荤捞靛
		side = 1; SubNo = attackerBid - SIDE_OFFSET;
	}else{
		side = 0; SubNo = attackerBid;
	}



	//磊脚捞 磊气
	ch->i.Hp = 0;
	flg |= BM_FLAG_AKO1;
	// 颇萍肺何磐档 狐柳促
	//		CHAR_DischargePartyNoMsg( defch );
	// 磊脚篮 傈捧肺何磐 狐柳促
	ch->w.BattleFlg |= CHAR_BATTLEFLG_ULTIMATE;
	// 狐柳 饶 某腐磐甫 傈捧 辆丰 惑怕肺 茄促.
	ch->w.BattleMode= BATTLE_CHARMODE_FINAL;

	//局肯悼拱 叼弃飘狼 傈捧惑怕甫 秦力
	if(ch->i.WhichType == CHAR_TYPEPET ){
		int pet = -1;
		Char *tch;

		tch  =(Char *) ch->wu.pet.PlayerChar;
		pet = tch->iu.player.DefaultPet;
		tch->iu.player.DefaultPet = -1;

		ch->iu.pet.DepartureBattleStatus = 0;
		CHAR_send_KP_String( tch, pet, CHAR_KP_DEPARTUREBATTLESTATUS);
	}

	//某腐磐 扼牢侩
	flg |= BM_FLAG_DEATH;

	//荤噶 敲贰弊甫 技款促
	CHAR_setFlg( ch, CHAR_ISDIE, TRUE);
#ifdef PUK2_SKILL
	// 敲饭捞绢?
	if( ch->i.WhichType == CHAR_TYPEPLAYER ){
		// 府滚胶 窍绊 乐绢?
		if( ch->w.RebirthFlag ){
			// 府滚胶 秦力
			ch->w.RebirthFlag = 0;
			ch->w.RebirthView = 0;
		}
		// 剧滴 俺绊扁档 秦力
		ch->wu.player.copyChar = NULL;
		ch->wu.player.copyCount = 0;
		// 弊贰侨阑 盔狼 葛嚼俊
		CHAR_updateGraphic( ch );
	}
#else
#ifdef PUK2
	// 府滚胶 窍绊 乐绢?
	if( ch->w.RebirthFlag ){
		// 府滚胶 秦力
		ch->w.RebirthFlag = 0;
		ch->w.RebirthView = 0;
		CHAR_updateGraphic( ch );
	}
#endif
#endif

	//惑贸 器牢飘 啊魂
	if(BATTLE_InjurySet( ch, ch, 0, 0) != -1){
		flg |= BM_FLAG_INJURY;	// 惑贸 敲贰弊
	}

	//DUEL扼搁 DP捞悼
	if( BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P ){
		BATTLE_DanceEscapeDpSend(battleindex, ch);
		BATTLE_GetDuelPoint( battleindex, side, SubNo );
	}
	//勉眠绰 傈侩 倔萍皋捞飘 贸府
	BATTLE_DanceUltimateExtra(battleindex, ch, ch);


//	BATTLESTR_ADD( "FF|" );

	return TRUE;




}

//*******************************************************
//勉眠绰 魂 力拱阑 官魔
//*******************************************************
static BOOL BATTLE_DanceSacrifice( int battleindex, int  attackerBid, int toNo, int type)
{
	Char *ch;
	Char *toch=NULL;
	int hp;
	int cnt2= 0;
	int i ;
	int maxhp;

	int ToList[SIDE_OFFSET*4+1];
	int flg;
	int aves;
	int SubNo, side;
	char szCommand[256];

	//窜眉 捞寇甫 急琶窍绊 乐绰 版快绰 救蹬
	if(toNo >= 20) return FALSE;

	attackerBid =  toNo;
	//荤农府 窍绰 荤捞靛甫 急琶茄促
	toNo = toNo < 10 ?  40:41;

	//鸥百阑 秒垫
	BATTLE_MultiList( battleindex, attackerBid, toNo, ToList, 0);

	//某腐磐 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );

	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( ch ) == FALSE) return FALSE;
    EmitBattleActionTargetEvent(ch, ToList);


	//犬伏篮 100%肺
	aves = 100;

	//荤农府磊狼 HP
	hp = ch->i.Hp;

	//割 荤恩 混酒 巢绊 乐绢?
	for( i = 0; ToList[i] != -1; i ++ ){
		if(ToList[i] == attackerBid) continue;
		cnt2++;
	}

	if(cnt2 == 0) cnt2 = 1;

	//抗 SKL|2008|20|aB|d0|f61|100|d1|f1|100|FF|
	snprintf( szCommand, sizeof(szCommand),
		"%X|", attackerBid);
	BATTLESTR_ADD( szCommand );


	// 绢蠢 颇扼固磐肺 且鳖
	for( i = 0; ToList[i] != -1; i ++ ){

		//磊气磊客 雀汗磊啊 窃膊狼 版快绰 鸥格
		if(ToList[i] == attackerBid) continue;
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		//某腐磐狼 器牢磐 眉农
		if( CHAR_CheckCharPointer( toch ) == FALSE) continue;

		flg = 0;
		//勉眠绰 魂 力拱阑 官魔篮 HP啊 钱 雀汗
		hp = toch->i.Hp;
		maxhp = toch->w.MaxHp;
		flg |= BM_FLAG_NORMAL;
		//瞒盒 雀汗樊
		hp = maxhp - hp;

		// 技飘
		toch->i.Hp =  maxhp ;
#ifdef PET_RIDE_BATTLE
		// 局肯悼拱?
		if( toch->i.WhichType == CHAR_TYPEPET ){
			Char *player = (Char *) toch->wu.pet.riderChar;

			// 愁捞 扁备吝?
			if( CHAR_CheckCharPointer( player ) ){
				// 肯蔫 窍绰 巴 鞍扁 锭巩俊, 剧磊 傍洒 肯蔫
				player->i.Hp = player->w.MaxHp;
				toch->wu.pet.petHp = toch->wu.pet.petMaxHp;
			}
		}
#endif

		snprintf( szCommand, sizeof(szCommand),
			"%X|f%X|d%X|", ToList[i], flg, hp);
		BATTLESTR_ADD( szCommand );
	}

	if( attackerBid >= SIDE_OFFSET ){
		// 荤捞靛
		side = 1; SubNo = attackerBid - SIDE_OFFSET;
	}else{
		side = 0; SubNo = attackerBid;
	}



	//磊脚捞 磊气
	ch->i.Hp = 0;
	flg |= BM_FLAG_AKO1;
	// 颇萍肺何磐档 狐柳促
	//	CHAR_DischargePartyNoMsg( defch );
	// 磊脚篮 傈捧肺何磐 狐柳促
	ch->w.BattleFlg |= CHAR_BATTLEFLG_ULTIMATE;
	// 狐柳 饶 某腐磐甫 傈捧 辆丰 惑怕肺 茄促.
	ch->w.BattleMode= BATTLE_CHARMODE_FINAL;

	//局肯悼拱 叼弃飘狼 傈捧惑怕甫 秦力
	if(ch->i.WhichType == CHAR_TYPEPET ){
		int pet = -1;
		Char *tch;

		tch  =(Char *) ch->wu.pet.PlayerChar;
		pet = tch->iu.player.DefaultPet;
		tch->iu.player.DefaultPet = -1;

		ch->iu.pet.DepartureBattleStatus = 0;
		CHAR_send_KP_String( tch, pet, CHAR_KP_DEPARTUREBATTLESTATUS);
	}

	//某腐磐 扼牢侩
	flg |= BM_FLAG_DEATH;

	//荤噶 敲贰弊甫 技款促
	CHAR_setFlg( ch, CHAR_ISDIE, TRUE);
#ifdef PUK2_SKILL
	// 敲饭捞绢?
	if( ch->i.WhichType == CHAR_TYPEPLAYER ){
		// 府滚胶 窍绊 乐绢?
		if( ch->w.RebirthFlag ){
			// 府滚胶 秦力
			ch->w.RebirthFlag = 0;
			ch->w.RebirthView = 0;
		}
		// 剧滴 俺绊扁档 秦力
		ch->wu.player.copyChar = NULL;
		ch->wu.player.copyCount = 0;
		// 弊贰侨阑 盔狼 葛嚼俊
		CHAR_updateGraphic( ch );
	}
#else
#ifdef PUK2
	// 府滚胶 窍绊 乐绢?
	if( ch->w.RebirthFlag ){
		// 府滚胶 秦力
		ch->w.RebirthFlag = 0;
		ch->w.RebirthView = 0;
		CHAR_updateGraphic( ch );
	}
#endif
#endif

	//版氰瘤, 捣, 酒捞袍殿阑 GET
//	BATTLE_GetEXPTmp(battleindex, ch, toch, 1);

	//惑贸 器牢飘 啊魂
	if(BATTLE_InjurySet( ch, ch, 0, 0) != -1){
		flg |= BM_FLAG_INJURY;	// 惑贸 敲贰弊
	}

	//DP捞悼
	if( BattleArray[battleindex].type == BATTLE_TYPE_P_vs_P ){
		BATTLE_DanceEscapeDpSend(battleindex, ch);
		BATTLE_GetDuelPoint( battleindex, side, SubNo );
	}
	//倔萍皋捞飘 贸府
	BATTLE_DanceUltimateExtra(battleindex, ch, ch);


//	BATTLESTR_ADD( "FF|" );

	return TRUE;

}


/******************************************************************************
*	鸥捞撇 勉眠绰 可记 秦籍
*	窃荐疙	BATTLE_DanceOption
*	馆券蔼	BOOL 沥惑：TRUE 捞惑 :FALSE
*	牢荐	BATTLE_CHARLIST *bEntryList
*	    	int battleindex
*	    	int attackerBid
*	    	int entrycnt
*	    	int opt
*	累己老	2002/10/01
******************************************************************************/
BOOL BATTLE_DanceOption(
	BATTLE_CHARLIST *bEntryList,
	int battleindex,
	int attackerBid,
	int entrycnt,
	int opt
)
{
	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;
	Char *ch;
	char szBuffer[512];
	char *option;
    char *moji[11] = {"P1:", "O1:", "A1:", "N1:", "P2:", "O2:", "A2:", "N2:", "TU:", "N3:", "N4:"};
	char *p;
	int techindex, array;
	int work[11] = {0,-1,-1, 0,0,-1,-1, 0,0,0,0};
	int weponNo = 0;
	int target=0 , type= 0;
	int ran , i , k , j;
	int nWork = 0, nWork2 = 0;
	int toNo = -1;
	int side = 0;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//咆捞 棵官弗啊
	techindex = TECH_getTechIndex( ch->w.BattleCom3 );
	if( TECH_CHECKINDEX(techindex) == FALSE )	return FALSE;

	//牢荐甫 夯促
	array  = TECH_getTechIndex( ch->w.BattleCom3);
	option = TECH_getChar( array, TECH_OPTION);

	//可记摹
	if(option == NULL) return FALSE;

	//  "P1", "O1", "A1", "N1", "P2", "O2", "A2", "N2", "TU","N3","N4")
	//可记狼 蔼阑 work俊
    for (i = 0; i < 11; i++) {
        p = strstr(option, moji[i]);
        if (p != NULL) {
            sscanf(p + 3, "%d", &work[i]);
            /*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
            work[i] = CHARSUIT_getOptionValue(ch, array, moji[i], work[i]);
#endif
/*ADD END*/
        }
    }
#ifdef EQUIPMENT_EFFECT
#if 0		// 捞 瓤苞绰 颇扼固磐 函悼狼 勉俊父 荤剧 函版 栋朝 荐 乐菌带
	// 厘厚俊 狼茄 漂荐 瓤苞狼 眉农
	int		bit;
	bit = BATTLE_checkEquipmentEffect( ch );
	// 「勉冕促」狼 己傍啦 诀狼 漂荐 瓤苞 乐绢?
	if( bit & ( 1 << ITEM_BATTLEEFFECT_DANCE ) ){
		int		rate, fail;
#ifdef _DEBUG
		int		before = work[0];
		char	msg[128];
#endif

		// 勉阑 角菩窍绰 犬伏阑 夸备茄促
		fail = 100 - work[0];
		// 己傍啦狼 荐沥摹甫 秒垫
		rate = BATTLE_getEquipmentEffectValue( ch, ITEM_BATTLEEFFECT_DANCE );
		// 角菩窍绰 犬伏阑 荐沥摹狼%盒 临牢 蔼捞, 荐沥 饶狼 己傍 犬伏
		work[0] = 100 - ( ( fail * ( 100 - rate ) ) / 100 );
#ifdef _DEBUG
		// 荐沥 饶狼 己傍啦阑 烹瘤
		snprintf( msg, sizeof( msg ),
				  TMSG(LANG_MSG_AUTOMATIC_56),
				  CHAR_COLORYELLOW, before, work[0] );
		BATTLESTR_TAIL( msg );
#endif
	}
#endif
#endif

	//己傍牢啊 角菩牢啊
	ran = RAND( 0, 99);

    //己傍牢啊 角菩牢啊
    if (ran < work[0]) {
        //己傍
//		print("己傍");
        target = work[1];    //鸥百 裹困(窜眉牢啊, 傈眉扼电瘤)
        type = work[2];        //勉狼 辆幅
        nWork = work[3];    //瘤加 畔(裹侩)
        nWork2 = work[9];    //裹侩 2

    } else if (ran >= work[0] && ran < work[0] + work[4]) {
        //角菩
//		print("角菩");
        target = work[5];    //鸥百
        type = work[6];     //勉狼 辆幅
        nWork = work[7];    //瘤加 畔(裹侩)
        nWork2 = work[10];  //裹侩 2
    } else {
//		print("阂惯");
        //阂惯
        type = B_DANCE_NONE;
    }

	//鸥百阑 急琶茄促
	if(target == B_DANCE_TARGET_MY){
		//磊脚
		side = attackerBid < 10 ?  0:1;
		toNo = attackerBid;
//		toNo = BATTLE_DanceTargetSelect( battleindex, attackerBid , side, type);
	}else if(target == B_DANCE_TARGET_PARTY_ONE){
		side = attackerBid < 10 ?  0:1;
		//酒焙 茄荤恩
		toNo = BATTLE_DanceTargetSelect( battleindex, attackerBid , side, type);
	}else if(target == B_DANCE_TARGET_PARTY_ALL){
		//酒焙 傈盔
		toNo = attackerBid < 10 ?  40:41;
	}else if(target == B_DANCE_TARGET_ENEMY_ONE){
		//利茄荤恩
		side = attackerBid < 10 ?  1:0;
		toNo = BATTLE_DanceTargetSelect( battleindex, attackerBid , side, type);
	}else if(target == B_DANCE_TARGET_ENEMY_ALL){
		//利傈盔
		toNo = attackerBid < 10 ?  41:40;
	}else if(target == B_DANCE_TARGET_ALL){
		//利酒焙 傈盔(42)
		toNo = TARGET_ALL;
	}

	if(toNo == -1) return FALSE;

	//公锐扼搁 疙己摹甫 UP
	//疙己狼 眉农
	if(ch->i.WhichType == CHAR_TYPEPLAYER){
		if(ch->iu.player.JobAncestry == 160){
			//公锐绰 勉眠搁 疙己捞 UP
			BATTLE_FameCheck(battleindex, attackerBid, attackerBid , 9);
		}
	}

	//己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	//胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|%X|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|%X|%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			weponNo,
			type,
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );


//	print("target=%d type=%d N=%d toNo%d \n", target, type , nWork, toNo);

	switch( type){
		case B_DANCE_POISON:
		case B_DANCE_SLEEP:				// 泪胶抛捞磐胶 捞惑
		case B_DANCE_STONE:				// 籍蜡拳切 胶抛捞磐胶 捞惑
		case B_DANCE_INEBRIETY:			// 秒扁 胶抛捞磐胶 捞惑
		case B_DANCE_CONFUSION:			// 去鄂 胶抛捞磐胶 捞惑
		case B_DANCE_FORGET:			// 噶阿 胶抛捞磐胶 捞惑
			//胶抛捞磐胶 捞惑 包拌
			BATTLE_DanceStatus( battleindex, attackerBid, toNo, type, nWork);
		break;

		case B_DANCE_POWER:				//单固瘤 UP
			BATTLE_DanceDamageUp(battleindex, attackerBid, toNo, nWork, nWork2);
		break;
		case B_DANCE_POSITION:			//器瘤记 捞悼
			BATTLE_DancePosition(bEntryList, battleindex, attackerBid, toNo, entrycnt, opt);
		break;
		case B_DANCE_PET_RETURN:		//脐阑 摹款促
			BATTLE_DancePetReturn( battleindex, attackerBid, toNo, type);
		break;

		case B_DANCE_DEATH:				//溜荤
			BATTLE_DanceDeath( battleindex, attackerBid, toNo, type);
		break;

		case B_DANCE_STATUS_RECOVER:	//胶抛捞磐胶 捞惑 雀汗
			BATTLE_DanceStatusRecover( battleindex, attackerBid, toNo, type);
		break;
		case B_DANCE_REVIVE:			//何劝
			BATTLE_DanceRevive( battleindex, attackerBid, toNo, type);
		break;
		case B_DANCE_BOMB:				//磊气
			BATTLE_DanceBomb( battleindex, attackerBid, toNo, type);
		break;
		case B_DANCE_SACRIFICE:			//魂 力拱阑 官魔
            BATTLE_DanceSacrifice(battleindex, attackerBid, toNo, type);
		break;
		case B_DANCE_NONE:				//阂惯
		break;

	}

	BATTLESTR_ADD( "FF|" );
    j = BATTLE_Index2No(battleindex, ch);
    if (j < 0) {
        return TRUE;
    }
	//勉眠绰阑 荤侩饶绰 荐畔 荤侩且 荐 绝霸 等促
	//荤侩且 荐 绝绰 畔 汲沥
	pBattle = &BattleArray[battleindex];
	//荤捞靛甫 掘绰促
	k = attackerBid < 10 ?  0:1;
	pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯
	//沥惑利牢啊
	if(CHAR_CheckCharPointer( pEntry[j].entrychara) == FALSE) return FALSE;
	//汲沥
	pEntry[j].danceturn = work[8];

	return TRUE;
}
/******************************************************************************
*	鸥捞撇 勉眠绰 皋牢
*	窃荐疙	BATTLE_Dance
*	馆券蔼	BOOL
*	牢荐	BATTLE_CHARLIST *bEntryList
*	    	int battleindex
*	    	int attackerBid
*	    	int entrycnt
*	    	int opt
*	累己老	2002/10/01
******************************************************************************/
BOOL BATTLE_Dance(
	BATTLE_CHARLIST *bEntryList,
	int battleindex,
	int attackerBid,
	int entrycnt,
	int opt
)
{
	Char *ch;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	// 促 勉眠菌阑 锭俊 公锐啊 绝绢瘤绊 乐阑 锭啊 乐栏骨肺,
	// 刚历 FP唱 胶懦 版氰摹甫 拌魂秦 敌促
#ifdef PUK2
	//胶懦 版氰蔼档 涝陛矫挪促(器胶 器牢飘甫 临牢促)
	BATTLE_SkillPointAndForcePoint(battleindex, ch);
#endif

	//可记 秦籍(芭狼 皋牢)
	BATTLE_DanceOption( bEntryList, battleindex, attackerBid, entrycnt, opt);

#ifndef PUK2
	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	BATTLE_SkillPointAndForcePoint(battleindex, ch);
#endif

	return 1;
}

/******************************************************************************
*	鸥捞撇:颇扼固磐 UPDOWN 胶懦狼 皋牢
*	窃荐疙	BATTLE_ParameterUpDownMain
*	馆券蔼	BOOL 沥惑：TRUE 捞惑：FALSE
*	牢荐	int battleindex
*	    	int attackerBid
*	累己老	2002/10/01
******************************************************************************/
BOOL BATTLE_ParameterUpDownMain( int battleindex, int attackerBid)
{
	BATTLE_ENTRY  *pEntry;
	BATTLE *pBattle;
	Char *ch;
	Char *toch;
	int toNo;
	int ToList[SIDE_OFFSET*4+1];
	char *option;
	int techindex;
	int skillid;
	char *p;
	int SuccessBack = 0, success = 0;
	int num = 0, damage = 0;
	int a, i, j , k;
	int flg = 0;
	char szCommand[256];
#ifdef EQUIPMENT_EFFECT
	int		bit;
#endif

	pBattle = &BattleArray[battleindex];
	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//咆捞 棵官弗啊
	techindex = TECH_getTechIndex( ch->w.BattleCom3 );
	if( TECH_CHECKINDEX( techindex) == FALSE )	return FALSE;

	option = TECH_getChar( techindex, TECH_OPTION);
	//可记摹
	if(option == NULL) return FALSE;

	//胶懦 ID
	skillid = TECH_getInt( techindex, TECH_SKILLID);
	//鸥百 沥焊
	toNo = ch->w.BattleCom2;

	//鸥百阑 急琶
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList , 1);
    EmitBattleActionTargetEvent(ch, ToList);

	if(ToList[0] != -1){
		//某腐磐 扼牢 累己
		snprintf( szCommand, sizeof(szCommand),
#ifdef	VERSION_TW
			"SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|",
#else
			"SKL|s%X|f%X|%X|a%X|",
#endif
				ch->w.BattleCom3,
				BATTLE_SKILL_UseFp(ch),
				weponNo,
				attackerBid
		);
		BATTLESTR_ADD( szCommand );
		//付豪矫啊 甸绊 乐绢?
//		if(BATTLE_Skill_SilenceCheck(battleindex, attackerBid) == FALSE){
//			BATTLESTR_ADD( "FF|" );
//			return FALSE;
//		}

//		//疙己狼 眉农
//		if(ch->i.WhichType == CHAR_TYPEPLAYER){
//			if(ch->iu.player.JobAncestry == 80){
//				//林贱荤啊 胶抛 付过阑 荤侩窍搁 疙己 坷弗促
//				BATTLE_FameCheck(battleindex, attackerBid, attackerBid , 7);
//			}
//		}

	}else{
		return FALSE;
	}

	//己傍 犬伏
	p = strstr(option,"SR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, techindex, "SR:", a);
#endif
/*ADD END*/
		success = a;
	}
#ifdef EQUIPMENT_EFFECT
	// 厘厚俊 狼茄 漂荐 瓤苞狼 眉农
	bit = BATTLE_checkEquipmentEffect( ch );
	// 「勉冕促」狼 己傍啦 诀狼 漂荐 瓤苞 乐绢?
	if( bit & ( 1 << ITEM_BATTLEEFFECT_DANCE ) ){
		int		rate, fail;
#ifdef _DEBUG
		int		before = success;
		char	msg[128];
#endif

		// 勉阑 角菩窍绰 犬伏阑 夸备茄促
		fail = 100 - success;
		// 己傍啦狼 荐沥摹甫 秒垫
		rate = BATTLE_getEquipmentEffectValue( ch, ITEM_BATTLEEFFECT_DANCE );
		// 角菩窍绰 犬伏阑 荐沥摹狼%盒 临牢 蔼捞, 荐沥 饶狼 己傍 犬伏
		success = 100 - ( ( fail * ( 100 - rate ) ) / 100 );
#ifdef _DEBUG
		// 叼滚弊 On锭绰 荐沥 饶狼 己傍啦阑 啊福模促
		if( CHAR_getWorkFlg( ch, CHAR_ISDEBUG ) ){
			// 荐沥 饶狼 己傍啦阑 烹瘤
			snprintf( msg, sizeof( msg ),
					  TMSG(LANG_MSG_AUTOMATIC_57),
					  CHAR_COLORYELLOW, before, success );
			BATTLESTR_TAIL( msg );
		}
#endif
	}
#endif

	SuccessBack = success;

	//雀荐
	p = strstr(option,"AR:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, techindex, "AR:", a);
#endif
/*ADD END*/
		num = a;
	}

	//鸥百 府胶飘甫 风橇
	for( i = 0 ; ToList[i] != -1 ; i++ ){
		success = SuccessBack;
		//鸥百狼 器牢磐
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		flg = 0;
		//郴己阑 啊瘤瘤 臼疽促
		if( RAND(0, 99) < success) {
			//DEF 己傍
			flg |= BM_FLAG_SUCCESS;
		}

		//汗荐 涵尔阑 OK肺 茄促搁 俺炼啊 鞘夸
		//国结 颇扼皋拌啊 吧妨乐绰 版快绰 救蹬
		if(! (toch->w.BattleFlg & CHAR_BATTLEFLG_UPDOWNFLG)
			&& flg & BM_FLAG_SUCCESS)
		{
			k = ToList[i] < 10 ?  0:1;
			pEntry = pBattle->Side[ k].Entry;	// 浚飘府 硅凯

			//牢郸胶
			j = BATTLE_Index2No(battleindex, toch);
			//某腐磐狼 器牢磐 眉农
			if( CHAR_CheckCharPointer( pEntry[j].entrychara ) == FALSE) continue;

			//傍拜 雀荐(弥家)
			p = strstr(option,"D1:");
			if(p !=NULL) {
				sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, techindex, "D1:", a);
#endif
/*ADD END*/
				damage = a;
			}
			//傍拜 雀荐(弥措)
			p = strstr(option,"D2:");
			if(p !=NULL) {
				sscanf(p +3,"%d", &a);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		a = CHARSUIT_getOptionValue(ch, techindex, "D2:", a);
#endif
/*ADD END*/
				damage = RAND(damage , a);
			}

			//敲贰弊甫 技款促
			toch->w.BattleFlg |=  CHAR_BATTLEFLG_UPDOWNFLG;

//			print("damage=%d num=%d ", damage, num);

			pEntry[j].parameterupdown[0] = damage;	//N硅
			//林芒茄 夯牢篮 1乘乘窍霸 茄促.(捞 促澜俊 促款 贸府啊 乐扁 (困秦)锭巩俊)
			if(ch == toch) {
				pEntry[j].parameterupdown[1] = num + 1;	//瘤加 畔
			}else{
				pEntry[j].parameterupdown[1] = num;		//瘤加 畔
			}
			switch( skillid){
				//诀拌
				//规绢仿
				case BATTLE_PARAMETER_SKILL_DEFUP:
					toch->w.DefUpDown = damage;
				break;
				//傍拜仿
				case BATTLE_PARAMETER_SKILL_ATKUP:
					toch->w.AtkUpDown = damage;
				break;
				//刮酶窃
				case BATTLE_PARAMETER_SKILL_AGLUP:
					toch->w.AglUpDown = damage;
				break;
#ifdef _TEST_TECH_YUK
				case BATTLE_PARAMETER_SKILL_RCVUP:
					toch->w.RcvUpDown = damage;
				break;
#endif /* _TEST_TECH_YUK */

				//促款拌
				//规绢仿
				case BATTLE_PARAMETER_SKILL_DEFDOWN:
					toch->w.DefUpDown = damage * -1;
				break;
				//傍拜仿
				case BATTLE_PARAMETER_SKILL_ATKDOWN:
					toch->w.AtkUpDown = damage * -1;
				break;
				//刮酶窃
				case BATTLE_PARAMETER_SKILL_AGLDOWN:
					toch->w.AglUpDown = damage * -1;
				break;


			}
		}

		//酒公巴档 吧府瘤 臼促.磊 吧媚扼
		snprintf( szCommand, sizeof(szCommand),
		"%X|f%X|", ToList[i], flg);
		BATTLESTR_ADD( szCommand );


	}

	BATTLESTR_ADD("FF|" );

	return TRUE;


}



//************************************************************
//颇扼固磐 UPDOWN 胶懦
//
//馆券蔼:	沥惑：TRUE
//			捞惑：FALSE
//
//************************************************************
BOOL BATTLE_ParameterUpDown(int battleindex, int attackerBid)
{
	Char *ch;


	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;

	//器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		//器胶 何练
		return FALSE;
	}

	//颇扼固磐 UPDOWN狼 皋牢 贸府
	BATTLE_ParameterUpDownMain( battleindex, attackerBid);

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	BATTLE_SkillPointAndForcePoint(battleindex, ch);

	return TRUE;

}
#ifdef PUK2_SKILL
//********************************************************************
// 剧滴 俺绊扁
//*********************************************************************
int BATTLE_ShapeChange(int battleindex, int attackerBid)
{
	Char *ch;
	int toNo = -1;
	char szBuffer[256];
	int ToList[SIDE_OFFSET*4+1];
	Char *toch;
	int techindex;
	char *option, *p;
	int turncount = 0;
	int level;

	//某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	//某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;
	//捞 胶懦阑 荤侩且 荐 乐绰 巴篮 敲饭捞绢父
	if(ch->i.WhichType !=  CHAR_TYPEPLAYER) return -1;
	//FP眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch) ){
		//FP何练
		return -1;
	}
	//国结 剧滴 俺绊扁甫 荤侩窍绊 乐绢?
	if( ch->wu.player.copyChar != NULL && ch->wu.player.copyCount > 0 ){
		// 剧滴 俺绊扁 荤侩吝篮 芭奠秦 荤侩且 荐 绝促
		return -1;
	}

	/* 鸥百狼 眉农 */
	//鸥百 沥焊
	toNo = ch->w.BattleCom2;
	//鸥百 秒垫
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
    EmitBattleActionTargetEvent(ch, ToList);
	//鸥百狼 器牢磐
	toch = BATTLE_No2Bid( battleindex, ToList[0] );
	//某腐磐狼 器牢磐 眉农
	if( CHAR_CheckCharPointer( toch ) == FALSE) return -1;
	//敲饭捞绢 捞寇俊绰 函且 荐 绝促
	if( toch->i.WhichType !=  CHAR_TYPEPLAYER) return -1;

	//扁贱狼 牢郸胶甫 秒垫
	techindex = TECH_getTechIndex( ch->w.BattleCom3 );
	if( ! TECH_CHECKINDEX( techindex ) ) return -1;
	option = TECH_getChar( techindex, TECH_OPTION );

	//瘤加 矫埃(弥家)
	p = strstr( option, "AN:" );
	if( p != NULL ){
		sscanf( (p+3), "%d", &toNo );
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		toNo = CHARSUIT_getOptionValue(ch, techindex, "AN:", toNo);
#endif
/*ADD END*/
		turncount = toNo;
	}
	//瘤加 矫埃(弥措)
	p = strstr( option, "AM:" );
	if( p != NULL ){
		sscanf( (p+3), "%d", &toNo );
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		toNo = CHARSUIT_getOptionValue(ch, techindex, "AM:", toNo);
#endif
/*ADD END*/
		turncount = RAND( turncount, toNo );
	}
	/* 饭骇瞒捞俊 狼茄 焊沥 */
	// 10 肋霸 戒扁俊辑狼 饭骇瞒捞
	level = ( (toch->i.Lv/10) - (ch->i.Lv/10) );
	// 惑措啊 臭酒?
	if( level > 0 ){
		// 瘤加 畔荐甫 临牢促
		turncount -= level;
	}
	// 弥历 捞 畔＋1 畔篮 拌加等促
	if( turncount < 2 ){
		turncount = 2;
	}

	/* 墨乔 某腐磐狼 汲沥 */
	// 器牢磐 技飘
	ch->wu.player.copyChar = toch;
	// 瘤加 矫埃阑 汲沥
	ch->wu.player.copyCount = turncount;
	// 颇扼固磐 函版
	CHAR_complianceParameter( ch );
	//胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|a%X|d%X|%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|a%X|d%X|%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			attackerBid,
			ToList[0],
			ch->i.BaseImageNumber
	);
	BATTLESTR_ADD( szBuffer );

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

#if 0
	if(success == 1){
		flg |= BM_FLAG_SUCCESS;

		//疙己狼 眉农
		if(ch->i.WhichType == CHAR_TYPEPLAYER){
			if(ch->iu.player.JobAncestry == 120){
				//林贱荤啊 胶抛 付过阑 荤侩窍搁 疙己 坷弗促
				BATTLE_FameCheck(battleindex, attackerBid, ToList[0], 10 );
			}
		}
	}

	sprintf(szBuffer, "d%X|f%X|A|",
			ToList[0],
			flg);
	BATTLESTR_ADD( szBuffer );
#endif

	BATTLESTR_ADD( "FF|" );
	return 1;
}
//********************************************************************
// 剧滴 俺绊扁, 辆丰 贸府
//*********************************************************************
void BATTLE_ShapeChange_Fin( int battleindex, int attackerBid )
{
	Char	*ch;
	char szBuffer[256];

	// 某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	// 某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return;

	// 剧滴 俺绊扁狼 汲沥阑 檬扁拳
	ch->wu.player.copyChar = NULL;
	ch->wu.player.copyCount = 0;
	// 颇扼固磐甫 弊傈措肺俊
	CHAR_complianceParameter( ch );
	// 府滚胶 窍瘤 臼阑 锭绰, 倒酒坷绰 橇肺配妮阑 价脚
	if( ! ch->w.RebirthFlag ) {
		// 剧滴 俺绊扁狼 橇肺配妮阑 固功窍霸 荤侩
#ifdef	VERSION_TW
		sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|a%X|d%X|%X|",
#else
		sprintf( szBuffer, "SKL|s%X|f%X|a%X|d%X|%X|",
#endif
				26300,
				0,
				attackerBid,
				-1,
				ch->i.BaseImageNumber
		);
		BATTLESTR_ADD( szBuffer );
		// 老窜, 辆丰 内靛甫 价脚
		BATTLESTR_ADD( "FF|" );
	}
}


//
// 捞 厘寒篮 荤侩 胶懦俊 措览茄 巴牢啊 眉农
//
#ifdef MAGIC_MISSILE
int	BarrierType0[]={  4,23,24,25,26,27,28,29,30,95,1011,-1 };
#else
int	BarrierType0[]={  4,23,24,25,26,27,28,29,30,95,-1 };
#endif
static BOOL checkSkillVsBarrier( int tech )
{
	int		i;
	int		techindex;
	int		skillID;

	// 咆 锅龋肺何磐 胶懦 ID甫 夸备茄促
	techindex = TECH_getTechIndex( tech );
	skillID = TECH_getInt( techindex, TECH_SKILLID );
	// 厘寒捞 阜绰 胶懦狼 辆幅甫 傈八祸
	for( i = 0 ; BarrierType0[i] > 0 ; i++ ){
		// 措览窍绊 乐绢?
		if( BarrierType0[i] == skillID ){
			// 措览窍绊 乐促
			return TRUE;
		}
	}
	// 措览窍绊 乐瘤 臼促
	return FALSE;
}

//*************************************************
// 检查敌人是否有屏障
//
//返回值：
//		TRUE:	有障碍
//		FALSE:	无障碍
//*************************************************
BOOL BATTLE_Skill_BarrierCheck( int battleindex, int attackerBid, int targetBid )
{
	BATTLE	*pBattle;
	Char	*ch, *defch;
	int		side;
	BOOL	ret = FALSE;

	pBattle = &BattleArray[battleindex];

	// 青悼窍绰 某腐磐狼 器牢磐甫 夸备茄促
	ch = BATTLE_No2Bid(battleindex, attackerBid);
	if( CHAR_CheckCharPointer( ch ) == FALSE){
		return FALSE;
	}
	// 利狼 青悼?
	if( ch->i.WhichType == CHAR_TYPEENEMY ){
		// 利篮 官府绢俊 吧府瘤 臼绰促
		return FALSE;
	}
	// 措惑 某腐磐狼 器牢磐甫 夸备茄促
	defch = BATTLE_No2Bid( battleindex, targetBid );
	if( CHAR_CheckCharPointer( defch ) == FALSE ){
		return FALSE;
	}
	// 利 荤捞靛甫 夸备茄促
	side = defch->w.BattleSide;
	if( side < 0 || side > 1 ){
		// 荤捞靛啊 捞惑窍扁 锭巩俊, 厘寒篮 绝菌带 巴栏肺 茄促
		return FALSE;
	}
	// 利俊霸 厘寒捞 乐阑鳖?
	if( pBattle->Side[side].barrierType ){
		int		type = -1;
		int		techindex;
		char	*option, *p;

		// 厘寒篮 酒流 蜡瓤?
		if( pBattle->Side[side].barrierCnt <= 0 ){
			// 厘寒篮 国结 绝绢瘤绊 乐促
			pBattle->Side[side].barrierType = 0;

			return FALSE;
		}

		// 官府绢狼 鸥涝阑, 可记 某腐磐 扼牢栏肺何磐 秒垫
		techindex = TECH_getTechIndex( pBattle->Side[side].barrierType );
		option =  TECH_getChar( techindex, TECH_OPTION );
		if(option == NULL){
			// 鸥涝阑 秒且 荐  绝菌阑 锭绰 厘寒 绝澜栏肺
			return FALSE;
		}
		p = strstr( option, "AR:" );
		if( p != NULL ){
			int		a;
			sscanf( p+3, "%d", &a );
			type = a;
		}

		// 官府绢狼 鸥涝付促 眉农
		switch( type ) {
			case 0:		// vs裹困 胶懦 官府绢
				// 荤侩窍绊 乐绰 胶懦篮 厘寒俊 措览茄 巴牢啊?
				if( checkSkillVsBarrier( ch->w.BattleCom3 ) ){
					// 厘寒捞 乐带 巴阑 敲贰弊俊 巢变促
					BarrierFlg = TRUE;
					// 厘寒 乐绢
					ret = TRUE;
				}
				break;
			case 1:		// 府滚胶 捞寇 公瓤 官府绢
				// 叼弃飘绰 吧府绰 汲沥栏肺
				BarrierFlg = TRUE;
				ret = TRUE;
				// 敲饭捞绢?
				if( ch->i.WhichType == CHAR_TYPEPLAYER ){
					// 府滚胶 荤侩吝?
					if( ch->w.RebirthFlag ){
						// 府滚胶 荤侩吝篮 厘寒 公矫
						BarrierFlg = FALSE;
						// 厘寒 绝澜
						ret = FALSE;
					}
				}
				break;
			case 2:		// 愁捞 扁备 捞寇 公瓤 官府绢
				// 叼弃飘绰 吧府绰 汲沥栏肺
				BarrierFlg = TRUE;
				ret = TRUE;
				// 局肯悼拱?
				if( ch->i.WhichType == CHAR_TYPEPET ){
					// 愁捞 扁备吝?
					if( CHAR_CheckCharPointer( ch->wu.pet.riderChar ) ){
						// 愁捞 扁备矫绰 厘寒 公矫
						BarrierFlg = FALSE;
						// 厘寒 绝澜
						ret = FALSE;
					}
				}
				break;
			case 3:		// 扁福阁 捞寇 公瓤 官府绢
				// 叼弃飘绰 吧府绰 汲沥栏肺
				BarrierFlg = TRUE;
				ret = TRUE;
				// 扁福阁?
				if( ch->i.WhichType == CHAR_TYPEGUILDMONSTER ){
					// 扁福阁篮 厘寒 公矫
					BarrierFlg = FALSE;
					// 厘寒 绝澜
					ret = FALSE;
				}
				break;

			default:
				break;
		}
	}

	// 搬苞 焊绊
	return ret;
}
//**************************************************************
// 厘寒
//**************************************************************
int BATTLE_Barrier_Type(int battleindex, int attackerBid)
{

	BATTLE	*pBattle;
	int		count = 1;
	char	*p;
	char	*moji;
	Char	*ch;
	int		d_min=1, d_max=1;
	int		techindex;
	char	szCommand[256] = {0};
	int		side = 0;
	int		type = 0;

	/* 阿辆 沥钦己狼 眉农 */
	// 硅撇 牢郸胶甫 眉农
	if( BATTLE_CHECKINDEX( battleindex ) == FALSE ) return FALSE;
	// 某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	// 某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return FALSE;
	// 器胶 眉农
	if( ch->i.ForcePoint < BATTLE_SKILL_UseFp(ch)){
		// 器胶 何练
		return FALSE;
	}

	// 扁贱狼 牢郸胶甫 秒垫
	techindex = TECH_getTechIndex(ch->w.BattleCom3);
	// 可记 某腐磐 扼牢阑 秒垫
	moji = TECH_getChar( techindex, TECH_OPTION);
	if(moji == NULL) return FALSE;
	// 单固瘤(弥家)
	p = strstr(moji,"AN:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &d_min);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		d_min = CHARSUIT_getOptionValue(ch, techindex, "AN:", d_min);
#endif
/*ADD END*/
		count = d_min;
	}
	// 单固瘤(弥措)
	p = strstr(moji,"AM:");
	if(p !=NULL) {
		sscanf(p +3,"%d", &d_max);
		/*ADD BY MOONBOY <SUIT>*/
#ifdef SUIT_SYSTEM
		d_max = CHARSUIT_getOptionValue(ch, techindex, "AM:", d_max);
#endif
/*ADD END*/
		count = RAND(d_min , d_max);
	}
	if(count == 0){
		count = -1;
	}
	// 措惑捞 登绰 柳康 秒垫
	if( ch->w.BattleCom2 == TARGET_SIDE_0 ){
		side = 0;
	}else{
		side = 1;
	}

	// 胶懦 某腐磐 扼牢狼 积己
	snprintf( szCommand, sizeof(szCommand),
#ifdef	VERSION_TW
		"SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|%X|",
#else
		"SKL|a%X|f%X|%X|a%X|%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			type,
			attackerBid,
			side
	);
	BATTLESTR_ADD( szCommand );

	// 厘寒捞 惯积茄 巴阑 况农肺 汲沥
	pBattle = &BattleArray[battleindex];
	// 厘寒狼 辆幅 汲沥
	pBattle->Side[side].barrierType = ch->w.BattleCom3;
	// 厘寒狼 瘤加 矫埃 汲沥
	pBattle->Side[side].barrierCnt = count+1;

	// 胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1){
		// 胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}
	return 1;
}
//********************************************************************
// 魂己厚
//*********************************************************************
int BATTLE_AcidRain(int battleindex, int attackerBid)
{
	Char	*ch, *toch;
	int		toNo;
	int		i, j;
	int		flg = 0;
	int		itemindex;
	int		damage = 10;
	int		success, targetNo = 0;
	char	szBuffer[256];
	int		ToList[SIDE_OFFSET*4+1];
	Char	*TargetList[SIDE_OFFSET*4+1];


	// 某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	// 某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	// 鸥百 沥焊
	toNo = ch->w.BattleCom2;
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
    EmitBattleActionTargetEvent(ch, ToList);
	// 己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	// 胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );

	// 傈 鸥百栏肺 措秦 眉农
	for( i = 0; ToList[i] != -1; i ++ ){
		/* 鸥百栏肺辑 蜡瓤茄啊 绢恫啊 炼荤茄促 */
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		//某腐磐狼 器牢磐 眉农
		if( CHAR_CheckCharPointer( toch ) == FALSE) continue;
		// 混酒乐绰 惑措父 蜡瓤
		if( CHAR_getFlg( toch, CHAR_ISDIE ) == TRUE ) continue;
		// 国结 曼傈 场抄 某腐磐父 蜡瓤
		if( toch->w.BattleMode == BATTLE_CHARMODE_RESCUE ) continue;
#ifdef PET_RIDE_BATTLE
		// 局肯悼拱?
		if( toch->i.WhichType == CHAR_TYPEPET ){
			Char	*player = (Char *) toch->wu.pet.riderChar;

			// 愁捞 扁备吝?
			if( CHAR_CheckCharPointer( player ) ){
				// 愁捞 扁备吝篮 敲饭捞绢螟俊辑 眉农
				toch = player;
			}
		}
#endif

		// 况农幅甫 檬扁拳
		flg = 0;
		// 快急 傍拜捞扼绊 窍绰 巴栏肺
		flg |= BM_FLAG_NORMAL;
		// 傍拜罐篮 秒鞭俊
		BATTLE_SetAttackedList( battleindex, toch );
		// 己傍/角菩 魄沥侩狼 蜡荤 风橇
		while( 1 ){
			// 敲饭捞绢 捞寇 瓤苞 绝澜
			if( toch->i.WhichType != CHAR_TYPEPLAYER ){
				break;
			}
			// 府滚胶 惑怕扼搁 瓤苞 绝澜
			if( toch->w.RebirthFlag ){
				flg |= BM_FLAG_REBIRTH_GUARD;
				break;
			}
			// 官府绢绰 眉农 规过 器窃秦 焊幅
//			if( toch->w.RebirthFlag == 2){
//				flg |= BM_FLAG_BARRIER;// 厘寒
//			}
			// 厘厚前阑 盒疙窍霸 厘厚 窍绊 乐阑鳖?
			for( j = 0 ; j < CHAR_EQUIPPLACENUM ; j++ ){
				// 厘厚鄂狼 酒捞袍 秒垫
				itemindex = CHAR_getItemIndex( toch, j );
				// 酒捞袍栏肺辑 蜡瓤茄啊?
				if( ! ITEM_CHECKINDEX( itemindex ) ) continue;

				// 厘厚 啊瓷茄 酒捞袍捞 厘厚鄂俊 乐阑鳖 眉农
				if( CHAR_checkCanEquipItem( toch, itemindex, FALSE ) ){
					// 绢蠢 OK
					break;
				}
			}
			// 酒公巴档 厘厚 窍绊 乐瘤 臼疽栏搁 公瓤
			if( j == CHAR_EQUIPPLACENUM ){
				break;
			}

			// 己傍啦阑 夯促
			success = BATTLE_Aves( battleindex, ch );

			// 己傍牢啊 角菩牢啊?
			if( RAND( 0, 99 ) < success ){
				/* 己傍沁栏骨肺, 傈厘厚俊 单固瘤 */
				// 己傍 敲贰弊甫 技款促
				flg |= BM_FLAG_SUCCESS;
				// 魂己厚甫 侥鸥百阑 滚欺俊 焊粮
				TargetList[targetNo++] = toch;
				// 傈厘厚俊 单固瘤
				for( j = 0 ; j < CHAR_EQUIPPLACENUM ; j++ ){
					// 厘厚鄂狼 酒捞袍 秒垫
					itemindex = CHAR_getItemIndex( toch, j );
					// 酒捞袍栏肺辑 蜡瓤茄啊?
					if( ! ITEM_CHECKINDEX( itemindex ) ) continue;
					// 厘厚 啊瓷茄 酒捞袍捞 厘厚鄂俊 乐阑鳖 眉农
					if( CHAR_checkCanEquipItem( toch, itemindex, FALSE ) ){
						int		dur;

						// 捞 酒捞袍狼 郴备档甫 临牢促
						dur = ITEM_getInt( itemindex, ITEM_DURABILITY );
						if( dur > 0 ){
							// 傈狼 郴备档甫 焊粮
							ITEM_setInt( itemindex, ITEM_VAR2, dur );
							dur -= damage;
							// 促父, 例措肺 噶啊瘤瘤 臼绰促
							if( dur <= 0 ){
								dur = 1;
							}
							// 临牢 郴备档甫 技飘
							ITEM_setInt( itemindex, ITEM_DURABILITY, dur );
                            EmitItemDurabilityChangedEvent(itemindex, ITEM_getInt(itemindex, ITEM_VAR2), dur, 3);
						}
					}
				}
			}
			// 绢路电 辆丰
			break;
		}
		// 某腐磐 扼牢 楷搬
		sprintf(szBuffer, "d%X|f%X|",
				ToList[i],
				flg );
		BATTLESTR_ADD( szBuffer );
	}
	// 胶懦 辆丰
	BATTLESTR_ADD( "FF|" );
	// 辆丰饶俊, 厘厚俊 单固瘤 罐篮 巴阑 沥府秦 烹瘤
	for( i = 0 ; i < targetNo ; i++ ){
		// 乔秦甫 罐篮 某腐磐
		toch = TargetList[i];
		// 老窜, 父距阑 困秦
		if( ! CHAR_CheckCharPointer( toch ) ) continue;
		snprintf( szBuffer, sizeof( szBuffer),
			 "CHT|14|%X|3|0|%s",
			 CHAR_COLORYELLOW,
			 TRMSG(LANG_MSG_CHAR_BATTLE_P_001, toch->c.Name)
			);
		BATTLESTR_ADD( szBuffer );
		// 公扁 噶啊瘤绊 某腐磐 扼牢阑 累己
		BATTLE_EquipBreakStringCheck( ch, toch, 0 );
	}
	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	return 1;
}
//********************************************************************
// 倒浅
//*********************************************************************
int BATTLE_Tornade(int battleindex, int attackerBid)
{
	Char	*ch, *toch;
	int		toNo;
	int		i;
	int		flg = 0;
	int		success, targetNo = 0;
	char	szBuffer[256];
	int		ToList[SIDE_OFFSET*4+1];
	int		TargetList[SIDE_OFFSET*4+1];


	// 某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	// 某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	// 鸥百 沥焊
	toNo = ch->w.BattleCom2;
	BATTLE_MultiList( battleindex, attackerBid , toNo, ToList, 1 );
    EmitBattleActionTargetEvent(ch, ToList);
	// 己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	// 胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|a%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|a%X|",
#endif
			ch->w.BattleCom3,
			BATTLE_SKILL_UseFp(ch),
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );

	// 傈 鸥百栏肺 措秦 眉农
	for( i = 0; ToList[i] != -1; i ++ ){
		/* 鸥百栏肺辑 蜡瓤茄啊 绢恫啊 炼荤茄促 */
		// 惑措狼 牢郸胶
		toch = BATTLE_No2Bid( battleindex, ToList[i] );
		//某腐磐狼 器牢磐 眉农
		if( CHAR_CheckCharPointer( toch ) == FALSE) continue;
		// 混酒乐绰 惑措父 蜡瓤
		if( CHAR_getFlg( toch, CHAR_ISDIE ) == TRUE ) continue;
		// 国结 曼傈 场抄 某腐磐父 蜡瓤
		if( toch->w.BattleMode == BATTLE_CHARMODE_RESCUE ) continue;

		// 况农幅甫 檬扁拳
		flg = 0;
		// 快急 傍拜捞扼绊 窍绰 巴栏肺
		flg |= BM_FLAG_NORMAL;
		// 傍拜罐篮 秒鞭俊
		BATTLE_SetAttackedList( battleindex, toch );
		// 己傍/角菩 魄沥侩狼 蜡荤 风橇
		while( 1 ){
			// 府滚胶 窍绊 乐绰 版快绰 瓤苞 绝澜
			if( toch->w.RebirthFlag != 0 ){
				// 老窜 府滚胶 公利篮 眉农
				if( toch->w.RebirthFlag == 2 ){
					flg |= BM_FLAG_REBIRTH_GUARD;
				}
				break;
			}
#ifdef PUK2_SKILL
			// 官府绢绰 眉农 规过 器窃秦 焊幅
//			if( toch->w.RebirthFlag == 2){
//				flg |= BM_FLAG_BARRIER;// 厘寒
//			}
#endif

			// 己傍啦阑 夯促
			success = BATTLE_Aves( battleindex, ch );
			// 己傍牢啊 角菩牢啊?
			if( RAND( 0, 99 ) < success ){
				/* 己傍沁栏骨肺, 狼荤 倔萍皋捞飘 茄促 */
				// 己傍 敲贰弊甫 技款促
				flg |= BM_FLAG_SUCCESS;
				// 倔萍皋捞飘肺 秒朝赴促
//				BATTLE_UltimateExtra( battleindex, ch, toch );
#ifdef PET_RIDE_BATTLE
				// 局肯悼拱捞扼搁, 愁捞 扁备吝牢啊 绢恫啊甫 眉农
				if( toch->i.WhichType == CHAR_TYPEPET ){
					Char	*player = (Char *) toch->wu.pet.riderChar;

					// 扼捞歹 乐绢?
					if( CHAR_CheckCharPointer( player ) ){
						// 敲饭捞绢啊 隔酒柳 巴栏肺辑 贸府
						toch = player;
					}
				}
#endif

				// 敲饭捞绢扼搁
				if(	toch->i.WhichType == CHAR_TYPEPLAYER){
					// 局肯悼拱捞 乐栏搁(磊) 傈捧肺何磐 狐瘤霸 茄促.
					BATTLE_PetDefaultExit( toch, battleindex );

					if( toch->wu.player.Metamo_Count > 0 ) {
						/* 父距 函脚吝捞扼搁 官帕栏肺 登倒赴促 */
						toch->i.BaseBaseImageNumber = toch->iu.player.OriginalImageNumber;
						toch->iu.player.OtherFlg &= ~CHAR_METAMO_NPC;
						toch->wu.player.Metamo_Count = 0;
						CHAR_complianceParameter( toch);
					}
#if 0	// 盔贰 府滚胶吝篮 朝瘤 臼绊
					// 府滚胶吝捞扼搁 官帕栏肺 登倒赴促
					if( toch->w.RebirthFlag != 0 ){
						toch->w.RebirthFlag = 0;
						toch->w.RebirthView = 0;
					}
#endif
					// 剧滴 俺绊扁档 秦力
					toch->wu.player.copyChar = NULL;
					toch->wu.player.copyCount = 0;
					// 弊贰侨阑 盔狼 葛嚼俊
					CHAR_updateGraphic( toch );

					// 肺弊牢 器牢飘俊 朝赴促
					if( CHAR_getLoginPoint( toch,
							&toch->i.MapId,
							&toch->i.Floor,
							&toch->i.X,
							&toch->i.Y) == TRUE )
					{
						CHAR_warpToSpecificPoint( toch,
								toch->i.MapId, toch->i.Floor, toch->i.X, toch->i.Y);
						CHAR_sendCToArroundCharacter( toch->w.ObjIndex);
					}

					// 颇萍 狐柳促
					CHAR_DischargePartyNoMsg( toch );
				}else if( toch->i.WhichType == CHAR_TYPEPET) {
					//局肯悼拱捞扼搁
					Char *player  =(Char *) toch->wu.pet.PlayerChar;

					//倔萍皋捞飘肺 版氰摹 啊瘤绊 乐栏搁(磊) 临牢促
					toch->w.GetExp = 0;
					//局肯悼拱 惑怕甫 REST肺 函版茄促
					toch->iu.pet.DepartureBattleStatus = CHAR_PET_BATTLE_REST;
					// 努扼捞攫飘 螟俊 价脚
					CHAR_send_KP_String( player, player->iu.player.DefaultPet, CHAR_KP_DEPARTUREBATTLESTATUS );
					// 林牢狼 叼弃飘肺何磐 毒促
					player->iu.player.DefaultPet = -1;
				}else{
					// 弊 捞寇肺 倔萍皋捞飘荤
					snprintf( szBuffer, sizeof(szBuffer),
						TMSG(LANG_MSG_CHAR_BATTLE_C_151),
						CHAR_getUseName( toch ) );

					// 倔萍皋捞飘 罐疽促
					toch->w.BattleFlg |= CHAR_BATTLEFLG_ULTIMATE;
					//皋技瘤
					BATTLE_EnemyMsg( battleindex, 25, BATTLE_Bid2No(battleindex, toch) );
				}
				// 傈捧肺何磐 狐瘤霸 茄促
				BATTLE_Exit( toch, battleindex );
			}
			// 绢路电 辆丰
			break;
		}
		// 某腐磐 扼牢 楷搬
		sprintf(szBuffer, "d%X|f%X|",
				ToList[i],
				flg );
		BATTLESTR_ADD( szBuffer );
	}
	// 胶懦 辆丰
	BATTLESTR_ADD( "FF|" );
	// 辆丰饶俊, 朝妨 滚妨柳 老阑 烹瘤
	for( i = 0 ; i < targetNo ; i++ ){
		// 乔秦甫 罐篮 某腐磐
		toch = BATTLE_No2Bid( battleindex, TargetList[i] );
		snprintf( szBuffer, sizeof( szBuffer),
			 "CHT|14|%X|3|0|%s",
			 CHAR_COLORYELLOW,
			 TRMSG(LANG_MSG_CHAR_BATTLE_P_002, toch->c.Name)
			);
		BATTLESTR_ADD( szBuffer );
	}
	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	return 1;
}
#endif
#ifdef LEVEL_DOWN_SKILL
//********************************************************************
// mapid 客 floorid 肺何磐, 鞘靛狼 饭骇 汲沥阑 夸备茄促
//********************************************************************
int BATTLE_getFieldLevelSet( int mapid, int floorid )
{
	int		level = -1;
	char	name[64];

	// 甘疙阑 夸备茄促
	if( MAP_getFloorName( mapid, floorid, name, sizeof( name ) ) != NULL ){
		int		ret;
		char	buf[32];

		// || 捞饶狼 某腐磐 扼牢阑 夸备茄促
		ret = getStringFromIndexWithDelim( name, "|", 3, buf, sizeof( buf ) );
		// 汲沥 某腐磐 扼牢捞 乐菌绰瘤?
		if( ret == TRUE ){
			// 芭扁狼 箭磊甫 饭骇 汲沥栏肺辑 荤侩茄促
			level = atoi( buf ) - 1;
		}
	}

	// 饭骇 汲沥阑 倒妨霖促
	return level;
}

//********************************************************************
// 饭骇 函悼 汲沥
//
//		BattleArray[battleindex].levelSet 狼 蔼栏肺何磐
//		ch->w.LevelDownLv 甫 汲沥茄促
//		捞 贸府狼 第俊 CHAR_complianceParameter 甫 何福绰 巴
//
//*********************************************************************
void BATTLE_setLevelVariant( int battleindex, Char *ch )
{
	int		side;
	int		level;
	BATTLE	*pBattle;

	// 器牢磐 眉农
	if( CHAR_CheckCharPointer( ch ) == FALSE ) return;

	pBattle = &BattleArray[ battleindex ];
	side = ch->w.BattleSide;

	// 函悼 饭骇阑 檬扁拳
	ch->w.LevelDownLv = -1;
	// 泅犁狼 饭骇阑 秒垫
	level = ch->i.Lv;
	if( ch->i.WhichType == CHAR_TYPEPLAYER ){
		// 敲饭捞绢狼 版快绰 瓤苞 饭骇肺 魄沥
		level = ch->wu.player.EffectiveLevel;
	}

	// 饭骇 函悼狼 鸥涝俊 狼秦, 贸府甫 盒扁
	switch ( pBattle->levelSet ){
		case 0:			// 敲饭捞绢, monster狼 饭骇阑 1俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				ch->w.LevelDownLv = 1;
			}
			break;
		case 1:			// 饭骇 10捞惑狼 某腐磐狼 饭骇阑 10俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				if( level > 10 ){
					ch->w.LevelDownLv = 10;
				}
			}
			break;
		case 2:			// 饭骇 20捞惑狼 某腐磐狼 饭骇阑 20俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				if( level > 20 ){
					ch->w.LevelDownLv = 20;
				}
			}
			break;
		case 3:			// 饭骇 30捞惑狼 某腐磐狼 饭骇阑 30俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				if( level > 30 ){
					ch->w.LevelDownLv = 30;
				}
			}
			break;
		case 4:			// 饭骇 40捞惑狼 某腐磐狼 饭骇阑 40俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				if( level > 40 ){
					ch->w.LevelDownLv = 40;
				}
			}
			break;

		case 5:			// 利螟狼 饭骇阑 敲饭捞绢狼 饭骇俊 嘎冕促
		case 6:			// 敲饭捞绢螟狼 饭骇阑 利狼 饭骇俊 嘎冕促
			break;

		case 7:			// 饭骇阑 10%俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				ch->w.LevelDownLv = ( level / 10 ) + 1;
			}
			break;
		case 8:			// 饭骇阑 1/4俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				ch->w.LevelDownLv = ( level + 3 ) / 4;
			}
			break;
		case 9:			// 饭骇阑 馆俊
			// 康氢 乐绰 巴篮 敲饭捞绢 柳康父
			if( pBattle->Side[side].type == BATTLE_S_TYPE_PLAYER ){
				ch->w.LevelDownLv = ( level + 1 ) / 2;
			}
			break;

		default:
			break;
	}
}

//********************************************************************
// 饭骇 函悼 胶懦
//*********************************************************************
int BATTLE_LevelDown( int battleindex, int attackerBid )
{
	Char	*ch, *toch;
	int		i, j;
	int		level;
	int		fieldno;
	char	szBuffer[256];
	BATTLE	*pBattle;
	BATTLE_ENTRY	*pEntry;

	// 某腐磐狼 器牢磐甫 秒垫
	ch = BATTLE_No2Bid( battleindex, attackerBid );
	// 某腐磐狼 器牢磐绰 沥惑利牢啊?
	if(CHAR_CheckCharPointer( ch) == FALSE) return -1;

	pBattle = &BattleArray[battleindex];

	/* 荤侩等 扁贱肺何磐, 饭骇 汲沥阑 角矫茄促 */
	// 扁贱狼 饭骇阑 夸备茄促
	level = ch->w.BattleCom3 % 10;
	// 扁贱狼 饭骇=饭骇 函悼狼 鸥涝(啊楼)
	pBattle->levelSet = level;

#if 1
	// 货肺款 鞘靛 锅龋甫 秒垫
	fieldno = BATTLE_GetFieldNo( battleindex, pBattle->type, ch );
	// 傈捧 包府 滚欺甫 盎脚
	pBattle->fieldno = fieldno;

	// 己傍 角菩狼 包拌绝捞 某腐磐 扼牢阑 焊辰促
	// 胶懦 傍拜 某腐磐 扼牢 汲沥(茄 锅俊 傍拜 Ver)
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|a%X|f%X|",
#else
	sprintf( szBuffer, "SKL|s%X|f%X|a%X|f%X|",
#endif
						ch->w.BattleCom3,
						BATTLE_SKILL_UseFp(ch),
						attackerBid,
						fieldno );
	BATTLESTR_ADD( szBuffer );
#else
	// 付过 豪牢栏肺 措侩
#ifdef	VERSION_TW
	sprintf( szBuffer, "SKL|s%X|f%X|i49|l1E|pFFFFFFFF|%X|a%X|0|",
#else
	sprintf( szBuffer, "SKL|a%X|f%X|%X|a%X|0|",
#endif
			8803,
			BATTLE_SKILL_UseFp(ch),
			0,
			attackerBid
	);
	BATTLESTR_ADD( szBuffer );
#endif

	/* 傈某腐磐狼 饭骇 汲沥 函版 */
	for( j = 0 ; j < 2 ; j++ ){
		// 捞 柳康俊 浚飘府 窍绊 乐绰 某腐磐 葛滴俊霸 康氢
		pEntry = pBattle->Side[j].Entry;
		for( i = 0 ; i < BATTLE_ENTRY_MAX ; i++ ){
			int		targetBid;
			int		baseimage;
			int		headgearimage = -1;
			int		ridepetimage = -1;

			toch = pEntry[i].entrychara;
			if( CHAR_CheckCharPointer( toch ) == FALSE ) continue;
			// ID甫 秒垫
			targetBid = BATTLE_Bid2No( battleindex, toch );

			// 饭骇 函悼 汲沥阑 角矫茄促
			BATTLE_setLevelVariant( battleindex, toch );
			// 泅犁狼 饭骇阑 夸备茄促
			level = toch->i.Lv;
			if( toch->w.LevelDownLv > 0 ){
				level = toch->w.LevelDownLv;
			}
			// 捞 鸥捞怪俊 颇扼固磐 犁拌魂
			CHAR_complianceParameter( toch );
			// 捞固瘤 锅龋殿阑 秒垫
			baseimage = toch->i.BaseImageNumber;

			// 敲饭捞绢?
			if( toch->i.WhichType == CHAR_TYPEPLAYER ){
#ifdef HEAD_COVER
				// 墨宏府葛畴 窍绊 乐绢?
				if( toch->wu.player.HeadGraNo != -1 ){
					headgearimage = toch->wu.player.HeadGraNo;
				}
#endif
#ifdef PET_RIDE_BATTLE
				// 愁捞 扁备吝?
				if( toch->w.walk.ridePet != -1 ){
					Char	*pet = CHAR_getHavePetCharPointer( toch, toch->w.walk.ridePet );

					if( CHAR_CheckCharPointer( pet ) ){
						int		ridelevel;

						// 愁捞 扁备 脐档 饭骇 函悼 汲沥阑 角矫茄促
						BATTLE_setLevelVariant( battleindex, pet );
						// 局肯悼拱档 颇扼固磐 犁拌魂
						CHAR_complianceParameter( pet );
						// 局肯悼拱狼 泅犁狼 饭骇阑 夸备茄促
						ridelevel = pet->i.Lv;
						if( pet->w.LevelDownLv > 0 ){
							ridelevel = pet->w.LevelDownLv;
						}
						// 敲饭捞绢客狼 乞闭 饭骇阑 夸备茄促
						ridelevel = ( level + pet->w.LevelDownLv + 1 ) / 2;
						// 弊府绊, 弊巴阑 饭骇俊
						level = ridelevel;
						// 愁捞 扁备 脐狼 弊贰侨 锅龋
						ridepetimage = pet->i.BaseImageNumber;
					}
				}
#endif
			}
			// 函悼茄 饭骇阑 价脚茄促
			sprintf( szBuffer, "d%X|l%X|%X|%X|%X|",
					 targetBid, level,
					 baseimage, ridepetimage, headgearimage );
			BATTLESTR_ADD( szBuffer );

		}
	}
	// 胶懦 辆丰
	BATTLESTR_ADD( "FF|" );

	//胶懦 版氰蔼档 涝陛矫挪促(器胶乐绰阑 临牢促)
	if(BATTLE_SkillPointAndForcePoint(battleindex, ch) != -1)
	{
		//胶懦 饭骇 UP
//		print("绵窍钦聪促 胶懦 饭骇 UP");
	}

	// 利饭骇狼 乞闭阑 犁拌魂秦具且 巴牢啊?

	return 1;
}
#endif
