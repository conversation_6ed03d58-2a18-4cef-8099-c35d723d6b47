<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator" content="HTML Tidy for HTML5 (experimental) for Windows https://github.com/w3c/tidy-html5/tree/c63cc39" />
    <meta content="text/html; charset=utf-8" http-equiv="content-type" />
    <link rel="stylesheet" type="text/css" href="style.css" />
    <title>
      SOCI - people
    </title>
  </head>
  <body>
    <table class="banner" cellpadding="0" cellspacing="0">
      <tr>
        <td class="banner_left">
          SOCI - The C++ Database Access Library
        </td>
      </tr>
    </table>
    <table class="main">
      <tr>
        <td class="main_navigator">
          <p>
            <a href="index.html">Home</a><br />
            <a href="http://sourceforge.net/project/showfiles.php?group_id=121480" target="_blank">Download</a><br />
            <a href="doc/index.html">Documentation</a><br />
            <a href="articles.html">Articles</a><br />
            People<br />
            <a href="events.html">Events</a><br />
            <a href="links.html">Links</a><br />
          </p><a href="http://sourceforge.net"><img src="http://sflogo.sourceforge.net/sflogo.php?group_id=121480&amp;type=1" width="88" height="31" border="0" alt="SourceForge.net Logo" /></a>
        </td>
        <td class="main_text">
          <p><span class="bold">SOCI People:</span></p>
          <p>Core team developers and contributors, current and previous, all people actively involved
          <div class="column-left">
            <span class="bold">Maciej Sobczak</span> (see <a target="_blank" href="http://www.msobczak.com/">homepage</a>)
            <div class="indent">
              Project admin<br />
              Core development<br />
              Initial Oracle Call Interface support (author)<br />
              Plug-in backend architecture (author)<br />
              PostgreSQL backend (author)<br />
              Documentation
            </div><br />
            <span class="bold">Mateusz Łoskot</span> (see <a target="_blank" href="http://mateusz.loskot.net">homepage</a>)
            <div class="indent">
              Project admin<br />
              Core development<br />
              Iterator support<br />
              Oracle backend (maintainer)<br />
              PostgreSQL backend (maintainer)<br />
              Build, test and release troublemaker<br />
              Documentation
            </div><br />
            <span class="bold">Paweł Aleksander Fedoryński</span> (see <a target="_blank" href="http://www.jfedor.org/pfedor/">homepage</a>)
            <div class="indent">
              MySQL backend (author, maintainer)<br />
              Documentation
            </div><br />
            <span class="bold">Steve Hutton</span> (see <a target="_blank" href="http://featurecomplete.com">homepage</a>)
            <div class="indent">
              Core development<br />
              Bulk (vector) operations<br />
              Dynamic result binding<br />
              Object-Relational mapping facilities<br />
              Common tests framework<br />
              Documentation
            </div><br />
            <span class="bold">David Courtney</span> (see <a target="_blank" href="http://www.davidcourtney.com">homepage</a>)
            <div class="indent">
              SQLite backend (author)<br />
              MS SQL Server (via ODBC) backend (author)
            </div><br />
            <span class="bold">Rafał Bobrowski</span>
            <div class="indent">
              Firebird backend (author)
            </div><br />
          </div>
          <div class="column-right">
            <span class="bold">Vadim Zeitlin</span> (see <a target="_blank" href="https://github.com/vadz">GitHub</a>)
            <div class="indent">
              ODBC backend (maintainer)<br />
              Core development<br />
            </div><br />
            <span class="bold">Viacheslav Naydenov</span> (see <a target="_blank" href="https://github.com/vnaydionov">GitHub</a>)
            <div class="indent">
              Firebird backend (maintainer)<br />
              Core development<br />
            </div><br />
            <span class="bold">Alex Ott</span> (see <a target="_blank" href="http://alexott.net/">homepage</a>)
            <div class="indent">
              Plug-in backend architecture<br />
              Core development<br />
            </div><br />
            <span class="bold">Denis Chapligin</span> (see <a target="_blank" href="https://github.com/akashihi">GitHub</a>)
            <div class="indent">
              IBM DB2 backend (author)
            </div><br />
            <span class="bold">Denis Arnaud</span> (see <a target="_blank" href="https://github.com/denisarnaud">GitHub</a>)
            <div class="indent">
              Fedora and Debian packaging<br />
              Contributor<br />
              Troubleshooter<br />
            </div><br />
            <span class="bold">Julian Taylor</span> (see <a target="_blank" href="https://github.com/juliantaylor">GitHub</a>)
            <div class="indent">
              Debian packaging<br />
              Contributor<br />
              Troubleshooter<br />
            </div><br />
            <span class="bold">Sergei Nikulov</span> (see <a target="_blank" href="https://github.com/snikulov">GitHub</a>)
            <div class="indent">
              SQLite3 contributor<br />
              Troubleshooter
            </div><br />
          </div>
        </td>
      </tr>
    </table><a href="http://github.com/SOCI"><img style="position: absolute; top: 0; right: 0; border: 0;" src="forkus_github.png" alt="Fork us on GitHub" /></a>
  </body>
</html>
