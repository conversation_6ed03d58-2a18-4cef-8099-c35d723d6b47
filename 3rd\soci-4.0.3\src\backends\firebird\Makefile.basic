# The following variable is specific to this backend and its correct
# values might depend on your environment - feel free to set it accordingly.

FIREBIRDINCLUDEDIR = -I/usr/local/firebird/include

# The rest of the Makefile is indepentent of the target environment.

COMPILER = g++
CXXFLAGS = -Wall -pedantic -Wno-long-long
CXXFLAGSSO = ${CXXFLAGS} -fPIC
INCLUDEDIRS = -I../../core ${FIREBIRDINCLUDEDIR}

OBJECTS = blob.o factory.o row-id.o session.o standard-into-type.o \
	standard-use-type.o statement.o vector-into-type.o vector-use-type.o \
	error-firebird.o common.o

OBJECTSSO = blob-s.o factory-s.o row-id-s.o session-s.o \
	standard-into-type-s.o standard-use-type-s.o statement-s.o \
	vector-into-type-s.o vector-use-type-s.o error-firebird-s.o common-s.o

FIREBIRDLIBS = -lfbclient -lpthread

libsoci_firebird.a : ${OBJECTS}
	ar rv $@ $?
	rm *.o

soci-firebird.o : soci-firebird.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

blob.o : blob.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

error-firebird.o : error-firebird.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

common.o : common.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

factory.o : factory.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

row-id.o : row-id.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

session.o : session.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

standard-into-type.o : standard-into-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

standard-use-type.o : standard-use-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

statement.o : statement.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

vector-into-type.o : vector-into-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

vector-use-type.o : vector-use-type.cpp
	${COMPILER} -c $? ${CXXFLAGS} ${INCLUDEDIRS}

shared : ${OBJECTSSO}
	${COMPILER} -shared -o libsoci_firebird.so ${OBJECTSSO} ${FIREBIRDLIBS}
	rm *.o

blob-s.o : blob.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

error-firebird-s.o : error-firebird.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

common-s.o : common.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

factory-s.o : factory.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

row-id-s.o : row-id.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

session-s.o : session.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

standard-into-type-s.o : standard-into-type.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

standard-use-type-s.o : standard-use-type.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

statement-s.o : statement.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

vector-into-type-s.o : vector-into-type.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

vector-use-type-s.o : vector-use-type.cpp
	${COMPILER} -c -o $@ $? ${CXXFLAGSSO} ${INCLUDEDIRS}

clean :
	rm -f *.o libsoci_firebird.a libsoci_firebird.so
