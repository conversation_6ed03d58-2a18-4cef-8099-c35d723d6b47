# Continuous integration workflow for SOCI.
name: GitHub CI

on:
  push:
    branches:
      - master
      - 'release/**'
    paths-ignore:
      - .circleci/**
      - .github/workflows/codeql.yml
      - appveyor.yml
  pull_request:
    branches:
      - master
    paths-ignore:
      - .circleci/**
      - .github/workflows/codeql.yml
      - appveyor.yml

jobs:
  build:
    runs-on: ${{ matrix.runner || 'ubuntu-18.04' }}
    name: ${{ matrix.name }}
    strategy:
      fail-fast: false
      matrix:
        include:
          # Note: the jobs are ordered in the order of decreasing running
          # time, as this should minimize the total run-time of all jobs.
          - backend: postgresql
            runner: macos-10.15
            name: PostgreSQL macOS
          - backend: oracle
            name: Oracle 11
            no_boost: true
          - backend: valgrind
            # This one dies under Xenial due to a bug in Valgrind and reports
            # errors under Focal in MySQL test, so run it under Bionic where
            # it just happens to work.
            runner: ubuntu-18.04
            name: Valgrind
          - backend: odbc
            name: ODBC
          - backend: firebird
            name: Firebird
          - backend: postgresql
            name: PostgreSQL Linux
          - backend: mysql
            name: MySQL
          - backend: sqlite3
            runner: macos-10.15
            name: SQLite3 macOS
          - backend: sqlite3
            name: SQLite3
          - backend: empty
            runner: macos-10.15
            cxx_std: 11
            name: Empty C++11 macOS
          - backend: empty
            name: Empty C++98
            cxx_std: 98
            test_release_package: true
          - backend: empty
            name: Empty C++11
            cxx_std: 11
          # Unsupported: db2exc package is only available in Ubuntu 14.04 not
          # supported by GitHub Actions any longer, we'd need to run it in
          # Docker container if we really need it.
          # backend: db2
          - backend: empty
            name: Examples
            build_examples: true

    env:
      SOCI_CI: true
      SOCI_CI_BACKEND: ${{ matrix.backend }}
      SOCI_MYSQL_ROOT_PASSWORD: root
      ASAN_OPTIONS: fast_unwind_on_malloc=0

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set environment variables
        run: |
          set_env_var() {
            echo "Setting environment variable $1=$2"
            echo $1=$2 >> $GITHUB_ENV
          }

          set_env_var SOCI_CI_BRANCH $GITHUB_REF
          set_env_var SOCI_SOURCE_DIR $GITHUB_WORKSPACE

          case "${{runner.os}}" in
            Linux)
              set_env_var PGHOST localhost
              set_env_var PGPORT 5432
              set_env_var PGUSER postgres
              set_env_var PGPASSWORD 'Password12!'
              ;;

            macOS)
              set_env_var PGDATA /usr/local/var/postgres
              ;;
          esac

          if [ "${{matrix.no_boost}}" = true ]; then
            set_env_var WITH_BOOST OFF
          fi
          if [ "${{matrix.cxx_std}}" != '' ]; then
            set_env_var CMAKE_CXX_STANDARD ${{matrix.cxx_std}}
          fi
          if [ "${{matrix.test_release_package}}" = true ]; then
            set_env_var TEST_RELEASE_PACKAGE YES
          fi
          if [ "${{matrix.build_examples}}" = true ]; then
            set_env_var BUILD_EXAMPLES YES
          fi

      - name: Install dependencies under Linux
        if: runner.os == 'Linux'
        env:
          SOCI_CI_PACKAGES: cmake
        run: ./scripts/ci/install.sh

      - name: Prepare for build
        run: ./scripts/ci/before_build.sh

      - name: Build
        run: ./scripts/ci/build.sh

      - name: Test
        run: ./scripts/ci/test.sh
