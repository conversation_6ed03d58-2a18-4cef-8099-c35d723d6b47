/*
 * Copyright (C) 1999-2002, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 15
 */

static const unsigned short cns11643_15_2uni_page21[7169] = {
  /* 0x21 */
  0x5302, 0x538c, 0x53d4, 0x54a5, 0x5392, 0x5393, 0x53d8, 0x53d9,
  0x54a7, 0x592b, 0x592c, 0x592d, 0x5930, 0x592e, 0x59ab, 0x1a01,
  0x5c2d, 0x5c6d, 0xfa34, 0x5d0f, 0x52fd, 0x5e9d, 0x64a2, 0x68d4,
  0x6e56, 0x6ec3, 0x7314, 0x52fd, 0x9a1a, 0x530f, 0x5310, 0x539a,
  0x539b, 0x52fd, 0x54ac, 0x0036, 0x5397, 0x5846, 0x0e4f, 0x5876,
  0x5877, 0x58ae, 0x593a, 0x597d, 0x6ec5, 0x5ea8, 0x52fd, 0x1afa,
  0x6541, 0x6542, 0x68d8, 0x68d9, 0x69aa, 0x69ab, 0x6c42, 0x6c41,
  0x5099, 0x6ec6, 0x52fd, 0x7035, 0x7036, 0x7037, 0xfa83, 0xb64d,
  0x52fd, 0x74b5, 0x7617, 0x7782, 0x86b6, 0x2d49, 0x8f72, 0x985c,
  0x98a8, 0x45b7, 0x68e3, 0x0006, 0x52fd, 0x53e6, 0x5444, 0x5445,
  0x1729, 0x003e, 0x54c4, 0x54c5, 0x54c6, 0x54c7, 0x54c8, 0x54c9,
  0x54ca, 0x54d4, 0x587d, 0xb572, 0x58b2, 0x58b4,
  /* 0x22 */
  0x5982, 0x59c7, 0x59c8, 0x52fd, 0x5b3c, 0x5b3d, 0x5b3e, 0x5b3f,
  0xfa24, 0x5bd3, 0x5447, 0x1a4e, 0x5d3d, 0x5de2, 0x5eb8, 0x5eb9,
  0x5ec2, 0x5eba, 0x5ec6, 0x5ebb, 0x64ad, 0x654c, 0x654d, 0x52fd,
  0x69b2, 0x69b3, 0x69b4, 0x69b5, 0x6cc3, 0x6cc4, 0x6cc5, 0x6cc6,
  0x6cc7, 0x52fd, 0x6ece, 0x6f30, 0x7047, 0x7048, 0x2281, 0x735c,
  0x52fd, 0x735f, 0x7618, 0x7619, 0x767e, 0x2410, 0x78b0, 0x7d75,
  0x2568, 0x7d76, 0x8341, 0x8442, 0x52fd, 0x86bd, 0x2842, 0x2840,
  0x18ea, 0x8e1e, 0x8e1f, 0x8f87, 0x2d62, 0x8f78, 0x9488, 0x9489,
  0x948a, 0x9917, 0x9915, 0x52fd, 0x52fd, 0x3291, 0xa3e5, 0xa909,
  0xaa3b, 0xaa4f, 0x52fd, 0xb92e, 0xb92f, 0xbdff, 0xbdfd, 0xbdfe,
  0x45bb, 0x45bc, 0x52fd, 0x5323, 0x53ea, 0x542a, 0x5449, 0x544a,
  0x544b, 0x54df, 0x54e0, 0x54e1, 0x54e2, 0x58c1,
  /* 0x23 */
  0x54e3, 0x54e4, 0x54e5, 0x58c0, 0x54e6, 0x54f7, 0x52fd, 0x54e7,
  0x54e8, 0x54e9, 0x54ea, 0x54eb, 0x580e, 0x5880, 0x5881, 0x58ba,
  0x58bb, 0x58bc, 0x58bd, 0x593f, 0x5940, 0x5988, 0xfa1f, 0x5b46,
  0x52fd, 0x5b47, 0x5b48, 0x5c36, 0x5c72, 0x5c96, 0x5d47, 0x5de7,
  0x5e34, 0x5e35, 0x5e36, 0x5e37, 0x5ee3, 0x5ee4, 0x5ee5, 0x5eeb,
  0x0175, 0x5ee6, 0x5ee7, 0x5ee8, 0x52fd, 0x52fd, 0x64b9, 0x1de8,
  0x64ba, 0x1e5b, 0x6563, 0x6564, 0x52fd, 0x6565, 0x52fd, 0x6566,
  0x657c, 0x6567, 0xfa5a, 0x6859, 0x68e9, 0x68ea, 0x68eb, 0x68ec,
  0x68ed, 0x68ee, 0x68ef, 0x69bf, 0x69cb, 0x69c3, 0x69d5, 0x69c4,
  0x69c5, 0x69d3, 0x69c6, 0x69c7, 0x69c8, 0x69c9, 0x69ca, 0x6c4a,
  0x52fd, 0x6cd1, 0x6e61, 0x6f37, 0x52fd, 0x7064, 0x7066, 0x2299,
  0x7067, 0x7068, 0x7069, 0x2297, 0x7073, 0x706a,
  /* 0x24 */
  0x706b, 0x2862, 0x52fd, 0x7372, 0x043c, 0x74b9, 0x761c, 0x7636,
  0x76bc, 0x76be, 0x76bf, 0x76c0, 0x7787, 0x78dc, 0x78dd, 0x78f5,
  0x78de, 0x78df, 0xfa9e, 0x78e0, 0x78e1, 0x78e2, 0x7d2c, 0x7d2d,
  0x25a4, 0x7da9, 0x7d9c, 0x7d9d, 0x7d9e, 0x7d9f, 0x7da0, 0x7da1,
  0x7da2, 0x52fd, 0x7da3, 0x7da4, 0x7da7, 0x8342, 0x8393, 0x8451,
  0x52fd, 0x8452, 0x8444, 0x8453, 0x8622, 0x86c6, 0x86c7, 0x86c9,
  0xfadd, 0x86ca, 0x2866, 0x86c8, 0x8e24, 0x8e25, 0x8e26, 0x52fd,
  0x8e27, 0x8e28, 0x52fd, 0x8f58, 0xfafb, 0x8fa7, 0x8f88, 0x8f89,
  0x8fa5, 0x8fa8, 0x8fa9, 0x8faa, 0x8fab, 0x8fac, 0x8fad, 0x2da3,
  0x8f79, 0x52fd, 0x9494, 0x9495, 0x9496, 0x09a5, 0x52fd, 0x9497,
  0x307f, 0x9498, 0x984e, 0x984f, 0x9919, 0x52fd, 0x9a41, 0xfb2a,
  0x9bf7, 0x9f04, 0x9f19, 0x9f17, 0xa340, 0xa3ec,
  /* 0x25 */
  0xa71b, 0xa71c, 0x36f6, 0xa90e, 0xaa52, 0xadd8, 0xb126, 0xb574,
  0x52fd, 0xb575, 0xbcc6, 0xbe0b, 0xbe0c, 0xbe0d, 0xbe0e, 0xbe0f,
  0xbe10, 0x45c8, 0xd759, 0xd75a, 0xd983, 0xd984, 0xd985, 0x52fd,
  0x4af5, 0xe120, 0xe121, 0xe122, 0xe123, 0xe124, 0xe129, 0x53f4,
  0x52fd, 0x688f, 0x5451, 0x17ab, 0x5517, 0x5518, 0x555b, 0x5519,
  0x551a, 0x0053, 0x551b, 0x551c, 0x551d, 0x551e, 0x551f, 0x5520,
  0x5521, 0x578b, 0x5788, 0x222d, 0x5885, 0xb587, 0x58c7, 0x594c,
  0x594d, 0x59fc, 0x59fd, 0x59fe, 0x59ff, 0x52fd, 0x52fd, 0x5b56,
  0x5b57, 0x5b58, 0x5b59, 0x5bdc, 0x5bdd, 0x5c73, 0x5c9d, 0x5ca1,
  0x5c9e, 0x5c9f, 0x5ca0, 0x5ce6, 0x5d51, 0x5d52, 0x5e3d, 0x5f22,
  0x1b9c, 0x5f23, 0x5f24, 0x5f25, 0x5f26, 0x5f27, 0x5f28, 0x5f29,
  0x5f2a, 0x5f2b, 0x52fd, 0x5f40, 0x5f2c, 0x5f2d,
  /* 0x26 */
  0x5f2e, 0x5f2f, 0x5f30, 0x64d0, 0x65a8, 0x6594, 0x6595, 0x6596,
  0x6597, 0x6598, 0x659c, 0x659b, 0x52fd, 0x65a1, 0x65a0, 0x6599,
  0x659a, 0x65a7, 0x1e88, 0x6864, 0x52fd, 0x6900, 0x6901, 0x52fd,
  0x52fd, 0x69e5, 0x69e9, 0x69fb, 0x69fc, 0x69ea, 0x69eb, 0x69ec,
  0x69ed, 0x69ee, 0x69ef, 0x69fa, 0x69f0, 0x69f1, 0x69f2, 0x69f3,
  0x69f4, 0x6c52, 0x6c53, 0x6c54, 0x6c55, 0x6c58, 0x6c56, 0x52fd,
  0x6cdd, 0x6cde, 0x6ce3, 0x6cdf, 0x6ce0, 0x6e22, 0x6e23, 0x6e68,
  0x6edf, 0x00ac, 0x6f44, 0x7094, 0x7095, 0x7096, 0x7097, 0x7098,
  0x7099, 0xfa79, 0x709a, 0x709b, 0x709d, 0x709e, 0x709f, 0x70a0,
  0x72f3, 0x72f2, 0x731f, 0x7388, 0x748f, 0x7490, 0x52fd, 0x74fd,
  0x74fe, 0x74ff, 0x7500, 0x7501, 0x791a, 0x52fd, 0x78e3, 0x78e4,
  0x78e5, 0x78e6, 0x78e7, 0x78eb, 0x78e8, 0x78e9,
  /* 0x27 */
  0x78ea, 0x791d, 0x7ca4, 0x7ca3, 0x7dd4, 0x7dc6, 0x7dc7, 0x7dc8,
  0x7dc9, 0x7dca, 0x7dcb, 0x7dcc, 0x7dcd, 0x05de, 0x7dce, 0x25c3,
  0x81b8, 0x81b9, 0x81ba, 0x836e, 0x83db, 0x83dc, 0x8468, 0x8469,
  0x846a, 0x846b, 0x846c, 0x846d, 0x846e, 0x2719, 0x8624, 0x8625,
  0x52fd, 0x8700, 0x86e5, 0x86f9, 0x86e7, 0x86e8, 0x86e9, 0x86fe,
  0x86ea, 0x86eb, 0x86ec, 0x0749, 0x86ed, 0x28a1, 0x86ee, 0x28a6,
  0x86ef, 0x52fd, 0x8717, 0x86f0, 0x86f1, 0x8b94, 0x8c5b, 0x8c5c,
  0x8c5d, 0x8e2f, 0x0891, 0x8e30, 0x8e31, 0x8e32, 0x8e33, 0x8fcc,
  0x8fcd, 0x8fce, 0x8fae, 0x8faf, 0x8fb0, 0x08d3, 0x8fcf, 0x8fd0,
  0x8fd1, 0x8fd2, 0x8fd3, 0x52fd, 0x8fd4, 0x09a9, 0x94a6, 0x94a7,
  0x94a8, 0x94b0, 0x94a9, 0x94aa, 0x94ab, 0x94ac, 0x94ad, 0x97fb,
  0x97fc, 0x52fd, 0x992d, 0x992e, 0x9950, 0x992f,
  /* 0x28 */
  0x9930, 0x9a65, 0x9be6, 0x9c0c, 0x9c02, 0x9c03, 0x9c04, 0x9c05,
  0x9c06, 0x52fd, 0x9ec0, 0x9f24, 0x9f25, 0xa016, 0xa032, 0xa720,
  0xa721, 0xa722, 0xa723, 0x36fe, 0xa725, 0xa919, 0xa924, 0xa91a,
  0xa91b, 0xa91c, 0xa91d, 0xfb52, 0xa91e, 0xaa5a, 0xaa5b, 0xaa5c,
  0xaa5d, 0xaa5e, 0xac31, 0x52fd, 0xb129, 0x52fd, 0xb288, 0xb289,
  0xb589, 0x3d57, 0xb656, 0xb7d1, 0xb7e6, 0xb832, 0xb833, 0xb952,
  0xb953, 0x0ed5, 0xb954, 0xb955, 0x52fd, 0x52fd, 0xbe29, 0xbe2a,
  0xbe2b, 0xbe2c, 0xbe2d, 0xfb92, 0xbe2e, 0xbe2f, 0xbe30, 0xbe31,
  0xbe32, 0xbe33, 0x3ec5, 0xbe34, 0xc49b, 0xc523, 0xc524, 0x52fd,
  0xc525, 0xc527, 0xc916, 0xcfa0, 0xd76f, 0x45df, 0xd770, 0xd771,
  0x45dc, 0x138c, 0xd772, 0xd773, 0xd774, 0xd99c, 0xd9aa, 0xd99d,
  0xd99e, 0x0c99, 0xd9ab, 0xd99f, 0xe135, 0xe138,
  /* 0x29 */
  0xf68d, 0x5335, 0x5336, 0xadee, 0x53b4, 0xf9c9, 0x5432, 0x5455,
  0x5544, 0x5545, 0x5546, 0x5547, 0x17e4, 0x5548, 0x5549, 0x52fd,
  0x554a, 0x554b, 0x554c, 0x554d, 0x554e, 0x554f, 0x5550, 0x5551,
  0xb599, 0x58d1, 0x52fd, 0x58d2, 0x58d3, 0x58d4, 0x58d5, 0x5a21,
  0x5a22, 0x5a23, 0x5b68, 0x5b69, 0x5b6a, 0x5be2, 0x5c45, 0x5cec,
  0x5ced, 0x5cee, 0x5cef, 0x52fd, 0x5d62, 0x5d63, 0x5df4, 0x536f,
  0x5e44, 0x5e45, 0x5f79, 0x1c1b, 0x5f7a, 0x5f7b, 0x5f7c, 0x5f7d,
  0x5f7e, 0x5f7f, 0x5f80, 0x0188, 0x52fd, 0x5f81, 0x5f82, 0x5f83,
  0x5f84, 0x5f85, 0x5f86, 0x5f87, 0x5f8f, 0x5f88, 0x5f89, 0x65c5,
  0x65c6, 0x1eaa, 0x65c7, 0x65c8, 0x65c9, 0x65ca, 0x65cb, 0x65cc,
  0x1eab, 0x65cd, 0x65ce, 0x65e3, 0x65cf, 0x65d0, 0x65d1, 0x65d2,
  0x65d3, 0x65c4, 0x65d4, 0x65d5, 0x65d6, 0x6820,
  /* 0x2a */
  0x6821, 0x691a, 0x6912, 0x6914, 0x6915, 0x6916, 0x6919, 0x6917,
  0x6918, 0x02c9, 0xfa61, 0x52fd, 0x6a12, 0x6a13, 0x6a14, 0x6a15,
  0x6a16, 0x6a17, 0x6a18, 0x6a19, 0x6c61, 0x6c62, 0x6c63, 0x6cf5,
  0x21a9, 0x6cf6, 0x6cf7, 0x6cf8, 0x6e29, 0x0517, 0x6f5d, 0x6f57,
  0x6f58, 0x6f59, 0x6f5a, 0x6f5b, 0x70bb, 0x70d1, 0x70bc, 0x70bd,
  0xbbdf, 0x70d0, 0x70be, 0x70bf, 0x70c0, 0x70c1, 0x70c2, 0x70c3,
  0x70c4, 0xbe6c, 0x73a1, 0x73a2, 0x73a3, 0x7493, 0x750d, 0x750f,
  0x750e, 0x7510, 0x7511, 0x7512, 0xfa95, 0x77e5, 0x792f, 0x52fd,
  0x7957, 0x7930, 0x7968, 0x792b, 0x7931, 0x7958, 0xfaa2, 0x7932,
  0x7959, 0x52fd, 0x795a, 0x7933, 0x795b, 0x795c, 0x795d, 0x791e,
  0x7cae, 0x7caf, 0x7d3b, 0x7d3c, 0x7d3d, 0x7e07, 0x7e08, 0x7e09,
  0x7e0a, 0x7e0b, 0x7e0c, 0x7e0d, 0x7e0e, 0x7e0f,
  /* 0x2b */
  0x7e10, 0x7e22, 0x7e11, 0x7e12, 0x7e13, 0x7e14, 0x7e15, 0x7e16,
  0x7e17, 0x7e18, 0xfab8, 0x52fd, 0x8346, 0x8347, 0x8348, 0x83e1,
  0x8481, 0x8483, 0x5f75, 0x52fd, 0x8485, 0x8486, 0x862a, 0xfad1,
  0x862b, 0x866b, 0x8718, 0x8719, 0x871a, 0x52fd, 0x871b, 0x871c,
  0x871d, 0x871e, 0x871f, 0x8720, 0x8721, 0x8722, 0x8723, 0x2911,
  0x8724, 0x8725, 0x8726, 0x8727, 0x8728, 0x8729, 0x872a, 0x8cef,
  0x8e49, 0x8e4a, 0x8e4b, 0x8e4c, 0x8e4d, 0x8e4e, 0x8e4f, 0x8e50,
  0x8e51, 0x8e52, 0x8e53, 0x8e54, 0x8e5a, 0x8e55, 0x8f5f, 0x9002,
  0x9003, 0x9004, 0x9005, 0x8fd5, 0x9006, 0x9007, 0x8fd6, 0x9008,
  0x9009, 0x900a, 0x900b, 0x8fd7, 0x900c, 0x900d, 0x94c8, 0x94c9,
  0x94ca, 0x94cb, 0x94cc, 0x94cd, 0x94ce, 0x94cf, 0x94d0, 0x94d1,
  0x94d2, 0x3451, 0x94d3, 0x94d4, 0x94d5, 0x94d6,
  /* 0x2c */
  0x94d7, 0x94e2, 0x94d8, 0x9804, 0x9805, 0x9806, 0x52fd, 0x9943,
  0x9944, 0x9a84, 0x9a8f, 0x9a85, 0x9a86, 0x9c2f, 0x9c1a, 0x9c1b,
  0x9c1c, 0x0abd, 0x9c1d, 0x9c1e, 0x9c1f, 0x9c20, 0x9c21, 0x9c22,
  0xfb2c, 0x9c23, 0x9c24, 0x9c0e, 0x9c25, 0x9c2b, 0x9c2c, 0x9c0b,
  0x9dee, 0x52fd, 0x9ec3, 0x9ed8, 0x9ed9, 0x9f38, 0x9f39, 0x9f3a,
  0x9f3b, 0x3453, 0x9f3c, 0x9f3d, 0x9f3e, 0x9f3f, 0x9f40, 0xa048,
  0xa04c, 0xa228, 0xa251, 0xa252, 0xa34c, 0xa423, 0xfb45, 0xa424,
  0xa731, 0xa732, 0xa733, 0x3718, 0xa734, 0xa735, 0xa736, 0xa740,
  0xa737, 0x52fd, 0xa738, 0xa73d, 0xa938, 0xa939, 0xa93a, 0xa93b,
  0xa93c, 0xa94c, 0xaa73, 0xaa74, 0x0d33, 0xaa75, 0xaa76, 0xaa79,
  0xac41, 0xac42, 0xac43, 0xad5b, 0x52fd, 0x39d7, 0x8ba6, 0xad5c,
  0xade5, 0xade6, 0xade7, 0xade8, 0xaded, 0xb130,
  /* 0x2d */
  0x3b7e, 0xb131, 0xb294, 0xb651, 0xb6fd, 0xb6fe, 0xb7e9, 0x52fd,
  0xb9b3, 0xb984, 0xb994, 0x52fd, 0xb99c, 0x52fd, 0xb985, 0xbc00,
  0xbc37, 0xbc57, 0xbe54, 0xbe68, 0xbe55, 0xbe5c, 0xbe56, 0xbe57,
  0xbe58, 0xbe59, 0xbe5a, 0xbe5b, 0xc534, 0x407e, 0xc535, 0x52fd,
  0xc539, 0xc536, 0xc537, 0xc538, 0xc8e5, 0xc959, 0x52fd, 0xc93a,
  0xc93b, 0xd028, 0xd189, 0x52fd, 0xd18a, 0xd18b, 0xd18c, 0xd18d,
  0xd2bc, 0xd5a3, 0x1344, 0xd5a4, 0xd793, 0xd794, 0xd795, 0xd796,
  0xd797, 0xd9b9, 0xe14f, 0xe150, 0xe151, 0xe376, 0x52fd, 0x53fb,
  0x5383, 0x5438, 0x545d, 0x5571, 0x52fd, 0x5577, 0x5578, 0xfa07,
  0x55c3, 0x5579, 0x557a, 0x557b, 0x557c, 0x5572, 0x557d, 0x55a0,
  0x557e, 0x557f, 0x5580, 0x5581, 0x5582, 0x5583, 0x559e, 0x5584,
  0x5585, 0x5586, 0x5587, 0x5588, 0x5793, 0x5794,
  /* 0x2e */
  0x5795, 0x57ef, 0x57f0, 0x52fd, 0x588d, 0x588f, 0x5890, 0x5891,
  0x5892, 0x58de, 0x58e1, 0x5953, 0x1966, 0x5a4a, 0x5a4b, 0x5a4c,
  0x5a51, 0x5a4d, 0x5a48, 0x5b74, 0x5b75, 0x5c20, 0x5c21, 0x5ca5,
  0x5ca6, 0x5d73, 0x5d74, 0x5e50, 0x5e51, 0x5e52, 0x5e53, 0x5fdb,
  0x5fdc, 0x1c20, 0x5fdd, 0x5fde, 0x5fff, 0x52fd, 0x52fd, 0x5fdf,
  0x5fe0, 0x1c21, 0x5fe1, 0x5fe2, 0x5fe3, 0x5fe4, 0x5fe5, 0x5fe6,
  0x5fe7, 0x5fe8, 0x5fe9, 0x5fea, 0x6607, 0x6608, 0x6609, 0x660a,
  0x660b, 0x660c, 0x660d, 0x1ed7, 0x661a, 0x660e, 0x660f, 0x6610,
  0x661c, 0x6827, 0x6866, 0x6898, 0x6899, 0x6933, 0x6924, 0x6925,
  0x6926, 0x52fd, 0x02e1, 0x6a3e, 0xfa64, 0x6a3f, 0x6a57, 0x6a40,
  0x6a41, 0x6a58, 0x6a42, 0x6a43, 0x6a44, 0x6a45, 0x6a46, 0x02e2,
  0x6a47, 0x6c6b, 0x6c6c, 0x6d10, 0x6d11, 0x21be,
  /* 0x2f */
  0x6e75, 0x6eef, 0x6f6a, 0x6f6b, 0x52fd, 0x6f6c, 0x6f6d, 0x6f6e,
  0x70ef, 0x70f0, 0x70f1, 0x70f2, 0x70f3, 0x70f4, 0x70f5, 0x70ff,
  0x70f6, 0x7102, 0x70f7, 0x7322, 0x73c4, 0x73c3, 0x7528, 0x047c,
  0x7620, 0x7625, 0x7622, 0x7623, 0x76eb, 0x04e3, 0x77f9, 0x77fa,
  0x7999, 0x799a, 0x799b, 0x7963, 0x52fd, 0x795e, 0x795f, 0x7960,
  0x799c, 0x7961, 0x799d, 0x7e54, 0x7e55, 0x7e56, 0x7e57, 0x7e58,
  0x7e59, 0x7e5a, 0x7e19, 0x7e6a, 0x7e5b, 0x7e5c, 0x7e5d, 0x7e66,
  0x52fd, 0x7e5e, 0x7e5f, 0x7e1a, 0x7e60, 0x7e61, 0x52fd, 0x7e62,
  0x7e1b, 0x7e63, 0xb710, 0xb711, 0x834c, 0x839b, 0x83eb, 0x83ec,
  0x83ed, 0x83ee, 0x84a3, 0x84a8, 0x84a6, 0x06ec, 0x862f, 0x8630,
  0x8631, 0x8632, 0x8633, 0x874f, 0x8751, 0x8752, 0x877d, 0x8753,
  0x8754, 0x8755, 0x8756, 0x8757, 0x8758, 0x8759,
  /* 0x30 */
  0x875a, 0x875b, 0x875c, 0x2957, 0x875d, 0x875e, 0x875f, 0x876f,
  0x8760, 0x8761, 0x8762, 0x8763, 0x8772, 0x8764, 0x52fd, 0x876e,
  0x8bb5, 0x8e65, 0x8e66, 0x8e67, 0x8e68, 0x8e69, 0x8e6a, 0x8e6b,
  0x8e6c, 0x900e, 0x9043, 0x52fd, 0x900f, 0x9044, 0x9045, 0x9046,
  0x9047, 0x9048, 0x9049, 0x9010, 0x904a, 0x904b, 0x904c, 0x904d,
  0x08df, 0x904e, 0x904f, 0x9050, 0x9051, 0x9052, 0x9053, 0x9054,
  0x9055, 0x9056, 0x9057, 0x9058, 0x9059, 0x905a, 0x901d, 0x905b,
  0x905c, 0x905d, 0xfb06, 0x52fd, 0x94fe, 0x94ff, 0x9500, 0x9501,
  0x9502, 0x9503, 0x9504, 0x9505, 0x9506, 0x9507, 0x9518, 0x9508,
  0x9509, 0x94f3, 0x950a, 0x950b, 0x951b, 0x950c, 0x950d, 0x950e,
  0x950f, 0x9510, 0x980f, 0x9861, 0x9879, 0x9ac1, 0x9aac, 0x9aad,
  0x9c43, 0x9c44, 0x9c45, 0x9c46, 0x9c47, 0x9c48,
  /* 0x31 */
  0x9c49, 0x9c64, 0x9c4a, 0x9c4b, 0x9c4c, 0x9c4d, 0x0ac7, 0xfb2d,
  0x9e39, 0x9f57, 0x9f58, 0x9f59, 0x9f5f, 0x9f5a, 0xa06a, 0xa22b,
  0xa25d, 0xa25e, 0xa25f, 0x52fd, 0xa260, 0xa261, 0xa358, 0xa359,
  0xa44a, 0xa44b, 0xa44c, 0xa44d, 0xa44e, 0xa757, 0xa6b3, 0xa6b4,
  0xa76e, 0xa75b, 0xa75c, 0x52fd, 0xa75d, 0xa75e, 0x52fd, 0xa76c,
  0xa93d, 0xa954, 0xa93e, 0xa955, 0xa956, 0xa93f, 0xa957, 0xa958,
  0xa959, 0xa95a, 0xa95b, 0xa95c, 0xaa88, 0xaa89, 0x52fd, 0xac58,
  0xac59, 0xac5a, 0x52fd, 0xad67, 0xad68, 0xad69, 0xad6a, 0x52fd,
  0xad6b, 0xad6c, 0xadfe, 0xadff, 0xae00, 0xae01, 0xae02, 0xae03,
  0xae04, 0xae05, 0xb139, 0xb13a, 0xb13b, 0xb13c, 0x52fd, 0x52fd,
  0xb2b2, 0xb2b3, 0xb2b4, 0xb2b5, 0xb2b6, 0xb2b7, 0xb2b8, 0x3d3c,
  0xb5a2, 0xb661, 0xb662, 0xb714, 0x52fd, 0xb7eb,
  /* 0x32 */
  0xb842, 0xb848, 0x52fd, 0xb843, 0xb84e, 0xb844, 0xb845, 0xb9b4,
  0xb9b5, 0x52fd, 0x52fd, 0x52fd, 0xb9b6, 0x52fd, 0xbbe1, 0xbc05,
  0x52fd, 0x3e73, 0xbe9a, 0x52fd, 0xbe9b, 0xbe9c, 0xbe9d, 0xbe9e,
  0xbe9f, 0xfb9a, 0xbea0, 0xbea1, 0xbea2, 0xbeb5, 0xbea3, 0xbea4,
  0x52fd, 0xbea5, 0xbea6, 0xbea7, 0xbea8, 0xbeaf, 0xbea9, 0xbeaa,
  0xbeab, 0xbeac, 0xbead, 0xbeb3, 0x52fd, 0xc4a0, 0xc556, 0xc934,
  0x1127, 0xc93c, 0xcb0c, 0x52fd, 0xcf3e, 0xcfa3, 0xd030, 0xd031,
  0xd197, 0xd198, 0xd199, 0xd19a, 0xd19b, 0xd5b2, 0xd5ab, 0xd5ac,
  0xd9fc, 0xd9e8, 0xd9e9, 0xd9ea, 0xdaf1, 0xdc3f, 0xdfce, 0xe16d,
  0xe16e, 0xe16f, 0xe170, 0xe171, 0xe172, 0xe173, 0x4bbe, 0xe378,
  0xeb3a, 0x5467, 0x27fa, 0x5464, 0x5465, 0x5607, 0x55c4, 0x55c5,
  0x55c6, 0x55c7, 0x55c8, 0x55c9, 0x55e5, 0x55ca,
  /* 0x33 */
  0x55cb, 0x52fd, 0x55cc, 0x55cd, 0x55ce, 0x55cf, 0x55d0, 0x5797,
  0x579a, 0x579b, 0x58ea, 0x58ec, 0x58ed, 0x58f7, 0x58ee, 0x58ef,
  0x595b, 0x595c, 0x595d, 0x5a6d, 0x5a6e, 0x52fd, 0x5a6f, 0x5b81,
  0x5b82, 0x5c4f, 0x5cad, 0xda0a, 0x5d7e, 0x5e69, 0x6054, 0x6055,
  0x6056, 0x6057, 0x01ab, 0x6058, 0x6059, 0x605a, 0x605b, 0x605c,
  0x605d, 0x52fd, 0x52fd, 0x605e, 0x605f, 0x663b, 0x6636, 0x663c,
  0x663d, 0x663e, 0x663f, 0x6640, 0x6641, 0x6642, 0x024b, 0x6643,
  0x6644, 0x6645, 0x6637, 0x52fd, 0x52fd, 0x6646, 0x6647, 0x6648,
  0x6649, 0x682d, 0x68a5, 0x693d, 0x693f, 0x6c6e, 0x6ae7, 0x6a75,
  0x6a76, 0x6a77, 0x6a78, 0x6a79, 0x6a7a, 0x6a7b, 0x6a7c, 0x6a8e,
  0x6a7d, 0x6a7e, 0x2072, 0x6a7f, 0x6a80, 0x6a81, 0x6a82, 0x6a83,
  0x6a84, 0x6a85, 0x6a86, 0x6a87, 0x6a88, 0x6a89,
  /* 0x34 */
  0x6a8a, 0x6a8b, 0x6a8c, 0x6c6f, 0x6c70, 0x6c71, 0x6c72, 0x6d2a,
  0x6d2b, 0x6d2c, 0x6d2d, 0x6d2e, 0x6d2f, 0x6d30, 0x6d31, 0x6e83,
  0x6f84, 0x6f85, 0x6f93, 0x52fd, 0x6f86, 0x6f87, 0x6f88, 0x6f89,
  0x6f8a, 0x6f8b, 0x7136, 0x7138, 0x7139, 0x713a, 0x03e2, 0x713b,
  0x713c, 0x713d, 0x713e, 0x713f, 0x52fd, 0x7140, 0x7141, 0x7142,
  0x73de, 0x73df, 0x73e0, 0x73e1, 0x754a, 0x754b, 0x754c, 0x754d,
  0x754e, 0x754f, 0x7550, 0x7627, 0x76f9, 0x76fa, 0x76fb, 0x7798,
  0x7799, 0x779a, 0x52fd, 0x781e, 0x799e, 0x79f7, 0x799f, 0x79a0,
  0x79f0, 0x79f8, 0x79f9, 0x79a1, 0x79a2, 0x79a3, 0x79a4, 0x79a5,
  0x79fa, 0x79fb, 0x79fc, 0x79fd, 0x79fe, 0x7a1f, 0x79ff, 0x7a00,
  0x7a8c, 0x7a01, 0x7cc2, 0x7cc3, 0x52fd, 0x7cd3, 0x7d4e, 0x7eaf,
  0x7eb0, 0x7eb1, 0x7eb2, 0x7eb3, 0x7ec6, 0x7eb4,
  /* 0x35 */
  0x52fd, 0x7eb5, 0x7eb6, 0x7eb7, 0x7eb8, 0x7eb9, 0x7eba, 0x7ebb,
  0x7ebc, 0x7ebd, 0x7ebe, 0x7ebf, 0x7ec1, 0x7ec0, 0x7ec2, 0x7ec3,
  0x8251, 0x8252, 0x8253, 0x8254, 0x8255, 0x835a, 0x8350, 0x8351,
  0x8378, 0x83f8, 0x83f9, 0x84c6, 0x84cb, 0x84cc, 0x84cd, 0x84ce,
  0x84cf, 0x84d0, 0x84d1, 0x84d2, 0x84d3, 0x84d4, 0x84d5, 0x52fd,
  0x8637, 0x8638, 0x8673, 0x8790, 0x8791, 0x87bf, 0x8792, 0x8793,
  0x8794, 0x8795, 0x8796, 0x8797, 0x8798, 0x8799, 0x29bd, 0x879a,
  0x879b, 0x879c, 0x879d, 0x879e, 0x879f, 0x87a0, 0x87a1, 0x87a2,
  0x87a3, 0x87a4, 0x87a5, 0x52fd, 0x87a6, 0x87a7, 0x8bbf, 0x8bc0,
  0x8bc1, 0x8bc2, 0x8bc3, 0x8bc4, 0x8bc5, 0x8bc6, 0x8c75, 0x8d43,
  0x8d12, 0x8d9f, 0x8d91, 0xfaf6, 0x8d92, 0x8d93, 0x8d94, 0x8e7e,
  0x8e7f, 0x8e80, 0x8e81, 0x8e82, 0x8f4c, 0x8f63,
  /* 0x36 */
  0x90c0, 0x90c1, 0x90c2, 0x953a, 0x90c3, 0x90c4, 0x90c5, 0x90c6,
  0x2e15, 0x90c7, 0x52fd, 0x90c8, 0x9073, 0x90c9, 0x90ca, 0x90cb,
  0x90cc, 0x90cd, 0x090e, 0x52fd, 0x52fd, 0x90ce, 0x90cf, 0x90d0,
  0x90d1, 0x90d2, 0x90d3, 0x90d4, 0x90d5, 0x953b, 0x953c, 0x953d,
  0x953e, 0x52fd, 0x953f, 0x9540, 0x9541, 0x9542, 0x9543, 0x09c1,
  0x9544, 0x9545, 0x9559, 0x9546, 0x9547, 0x9548, 0x52fd, 0x9549,
  0x9813, 0x9882, 0x9883, 0x9966, 0x9967, 0x9c65, 0x3313, 0x9c66,
  0x0ad9, 0x9c75, 0x9c74, 0x9c67, 0x9c6d, 0x9c76, 0x9c68, 0x9c69,
  0x9c6a, 0x9c6b, 0x9c4f, 0x9c6c, 0x9df8, 0x9e45, 0x33f8, 0x9e46,
  0x9ee4, 0x9f85, 0x9f6f, 0x9f70, 0xa082, 0xa083, 0xa084, 0xa08b,
  0xa22d, 0xa268, 0xa269, 0xa277, 0xa264, 0xa26a, 0x52fd, 0xa2f2,
  0xa2e2, 0xa363, 0xa364, 0xa365, 0xa366, 0x3a3d,
  /* 0x37 */
  0xa367, 0xa368, 0x35d8, 0x52fd, 0xa472, 0xa47d, 0xa669, 0xa784,
  0xa786, 0xa787, 0xa788, 0xa79e, 0x52fd, 0xa789, 0xa78a, 0xa796,
  0xa78b, 0xa78c, 0xa78d, 0xa979, 0xa983, 0xa97a, 0xa95d, 0xa97b,
  0x52fd, 0xaa9f, 0x0d48, 0xaaa0, 0xaaa1, 0xaaa2, 0x38fc, 0xac6a,
  0xac6b, 0xac6c, 0xac6d, 0xac6e, 0xad78, 0x52fd, 0xad79, 0xad7c,
  0xae21, 0xae22, 0xae23, 0xae24, 0x3a39, 0xae25, 0xae26, 0xb151,
  0xb152, 0xb156, 0x52fd, 0x3c4b, 0xb2e1, 0x52fd, 0x52fd, 0xb2d6,
  0xb2d7, 0xb2d8, 0xb2d9, 0xb5b5, 0xb66c, 0xb66f, 0xb670, 0xb7bc,
  0xb7bd, 0xb7ed, 0xb85b, 0xb85c, 0xb85d, 0xb85e, 0xb986, 0xb987,
  0xb9ed, 0x52fd, 0xb9ee, 0x52fd, 0xbc0e, 0xbc0f, 0x52fd, 0xbd2d,
  0xbd2a, 0xbd2e, 0xbdcc, 0xbef9, 0xbefa, 0xbefb, 0xbefc, 0x3fb9,
  0xbefd, 0xbf1b, 0xbefe, 0xbeff, 0xbf00, 0xbf01,
  /* 0x38 */
  0xbf02, 0xbf03, 0xbf21, 0xbf04, 0xbf05, 0xbf06, 0xbf07, 0xbf08,
  0xbf09, 0xbf0a, 0xbf0b, 0xbf0c, 0xbf0d, 0xbf0e, 0xbf0f, 0xbf10,
  0xbf11, 0xbf12, 0xbf13, 0xbf14, 0xbf15, 0xc57b, 0xc57c, 0xc57d,
  0xc57e, 0x40cf, 0xc57f, 0xc580, 0xc566, 0xc581, 0xc582, 0xc583,
  0xc8b2, 0x52fd, 0xc97f, 0xc972, 0xc95a, 0xcaeb, 0xcb15, 0x52fd,
  0xcb16, 0xfbca, 0xcb17, 0x52fd, 0xcbc5, 0xcbc6, 0xcbc7, 0xcc88,
  0xcc89, 0x52fd, 0xcc8a, 0xcc8b, 0xcc8c, 0xcc8d, 0xcedf, 0x52fd,
  0xd03b, 0xd03c, 0xd03d, 0xd03e, 0xd03f, 0xd040, 0xd041, 0xd042,
  0xd15c, 0xd1af, 0xd1a9, 0xd1aa, 0xd1ab, 0xd1ac, 0xd1b5, 0xd1ad,
  0xd1ae, 0xd2e2, 0xd524, 0x44ae, 0xd5c4, 0x52fd, 0xd7ea, 0xd7d3,
  0xd7d4, 0xd7d5, 0xd986, 0x52fd, 0xd987, 0xd9fd, 0xdb05, 0xdc49,
  0xdf5f, 0xdfd3, 0xdfd5, 0xe18a, 0xe18b, 0xe18c,
  /* 0x39 */
  0xe18d, 0xe18e, 0xe18f, 0xe190, 0xe191, 0xe192, 0xe193, 0xe37d,
  0xe37e, 0x4beb, 0x52fd, 0x52fd, 0xe4ec, 0xe55b, 0xef87, 0xef86,
  0x52fd, 0x5346, 0x5347, 0x5402, 0x546d, 0x546e, 0x546f, 0x560d,
  0x560e, 0x560f, 0x5610, 0x5611, 0x5612, 0x5613, 0x5614, 0x579f,
  0x57a0, 0x57a1, 0x57a3, 0x5821, 0x5822, 0x867c, 0x5895, 0x5896,
  0x5961, 0x5967, 0x5a91, 0x5a92, 0x5a93, 0x5a94, 0x5b8a, 0x5b8b,
  0x5bf7, 0x5c24, 0x5cb0, 0x5cb1, 0x5d8e, 0x5e04, 0x5e6a, 0x5e6b,
  0x5e6c, 0x5e6d, 0x60e8, 0x610c, 0x60e9, 0x60ea, 0x610d, 0x52fd,
  0x60eb, 0x60ec, 0x60ed, 0x60ee, 0x60ef, 0x60f0, 0x60f1, 0x60f2,
  0x6116, 0x60f3, 0x6104, 0x611d, 0x60f4, 0x60f5, 0x60f6, 0x60f7,
  0x1cb0, 0x6502, 0x6503, 0x6504, 0x669a, 0x667c, 0x66c5, 0x667d,
  0x667e, 0x667f, 0x6680, 0x6681, 0x1f40, 0x1f42,
  /* 0x3a */
  0x6682, 0x6683, 0x6684, 0x6685, 0x6686, 0x6687, 0x68ae, 0x694d,
  0x6ab0, 0x6adc, 0x6ab6, 0x6ab7, 0x6ab8, 0x6ab9, 0x6aba, 0x6abb,
  0x6adb, 0x6abc, 0x6abd, 0x6abe, 0x52fd, 0x6abf, 0x6ac0, 0x6ac1,
  0x6ac2, 0x6ac3, 0x6ace, 0x0301, 0x6ad9, 0x6ac4, 0x6ac5, 0x6ada,
  0x6ac6, 0x6ac7, 0x6ac8, 0x6ac9, 0x6aca, 0x6c7e, 0x6c7b, 0x6d4e,
  0x6d4f, 0x6d50, 0x6d51, 0x6d52, 0x6d53, 0x6d54, 0x6d55, 0x6e8f,
  0x6efc, 0x6fa6, 0x6fa7, 0x6fa8, 0x7023, 0x718b, 0x52fd, 0x718c,
  0x718d, 0x718e, 0x718f, 0x71a4, 0x5899, 0x7324, 0x7346, 0x7347,
  0x7348, 0x73fd, 0x73fe, 0x52fd, 0x756e, 0x757c, 0x756f, 0x7570,
  0x7571, 0x7572, 0x7629, 0x762a, 0x765f, 0x77a2, 0x7830, 0x782b,
  0x7a61, 0x7a02, 0x7a03, 0x7a04, 0x7a05, 0x7a06, 0x7a07, 0x52fd,
  0x7a08, 0x7a09, 0x7a62, 0x7a0a, 0x7a0b, 0x7a0c,
  /* 0x3b */
  0x7a0d, 0x7a0e, 0x7a63, 0x7a27, 0x7a0f, 0x52fd, 0x7a1b, 0x7a64,
  0x7a10, 0x7a11, 0x7a81, 0x7a12, 0x7a65, 0x7a13, 0x7cce, 0x7f29,
  0x7ec4, 0x7f2a, 0x52fd, 0x7f2b, 0x7f2c, 0x7f2d, 0x7f2e, 0x7f2f,
  0x7f30, 0x7f31, 0x7f32, 0x7f33, 0x7f34, 0x52fd, 0x7f35, 0x7f36,
  0x7ee3, 0x7f37, 0x7f38, 0x7f39, 0x7f3a, 0x7f3b, 0x7f4d, 0x7f3c,
  0x7f3d, 0x7f3e, 0x52fd, 0x81d0, 0x81d1, 0x8355, 0x8402, 0x8404,
  0x84fc, 0x8507, 0x84fd, 0x84fe, 0x52fd, 0x84ff, 0x8500, 0x8508,
  0x8501, 0x8502, 0x8535, 0x8503, 0x8504, 0x52fd, 0x863b, 0x8689,
  0x8681, 0x8682, 0xba33, 0x87eb, 0x87ec, 0x52fd, 0x87ed, 0x87ee,
  0x87ef, 0x52fd, 0x87f0, 0x87f1, 0x87f2, 0x2a2b, 0x87f3, 0x2a16,
  0x87f4, 0x87f5, 0x534b, 0x87f6, 0x87f7, 0x87f8, 0x87f9, 0x87fa,
  0x87fb, 0x87fc, 0x87fd, 0x87fe, 0x87ff, 0x8800,
  /* 0x3c */
  0x2a1b, 0x8801, 0x8802, 0x8803, 0x8804, 0x8805, 0x8806, 0x8807,
  0x2a27, 0x8808, 0x8809, 0x880a, 0x880b, 0x880c, 0x880d, 0x8bd7,
  0x8bd8, 0x8d29, 0x8d9c, 0x8df8, 0x8e98, 0x8ea4, 0x8e99, 0x8e9a,
  0x8e9b, 0x8e97, 0x8e9c, 0x2cf5, 0x8e9d, 0x52fd, 0x8e9e, 0x8e9f,
  0x8f65, 0x90d6, 0x0920, 0x9132, 0x9133, 0x9134, 0x2e82, 0x9135,
  0x9136, 0x9137, 0x90d7, 0x52fd, 0x90d8, 0x9138, 0x9139, 0x913a,
  0x913b, 0x913c, 0x913d, 0x913e, 0x913f, 0x9140, 0x90d9, 0x9141,
  0x9142, 0x0921, 0x9143, 0x9144, 0x2e7a, 0x957c, 0x957d, 0x957e,
  0x957f, 0x9580, 0x9581, 0x9582, 0x52fd, 0x9583, 0x9584, 0x9585,
  0x9586, 0x95a5, 0x9587, 0x9588, 0x9589, 0x958a, 0x958b, 0x958c,
  0x958d, 0x958e, 0x52fd, 0x52fd, 0x958f, 0x52fd, 0x9590, 0x9591,
  0x9592, 0x9593, 0x9594, 0x9595, 0x52fd, 0x3129,
  /* 0x3d */
  0x9596, 0x9856, 0x9857, 0x98e1, 0x990e, 0x990f, 0x997a, 0x997b,
  0x998b, 0x9af2, 0x9af3, 0x9c93, 0x9c94, 0x9c95, 0x9c96, 0x9c97,
  0x9cad, 0x9c98, 0x9cab, 0x9c7b, 0x9c99, 0x9c9a, 0x9c9b, 0x9c85,
  0x9c9c, 0x9c9d, 0x0ada, 0x52fd, 0x9cac, 0x9c79, 0x9c9e, 0x9ca4,
  0x9dfd, 0x9eeb, 0x9eea, 0x9f8f, 0x9f90, 0x9f86, 0x9f87, 0x9f88,
  0xa0ac, 0xa0ad, 0xa2ec, 0xa373, 0xa374, 0x52fd, 0xa4aa, 0xa4ab,
  0xa4ac, 0xa6d1, 0xa6d7, 0xa7b9, 0xa7ba, 0xa7bb, 0xa7bc, 0xa98e,
  0xa98f, 0xa990, 0xa97c, 0xa991, 0xa992, 0xa993, 0xa994, 0xfb54,
  0xa99a, 0xa995, 0xa996, 0xa997, 0xaabd, 0xaac7, 0xaabe, 0x52fd,
  0xaabf, 0x52fd, 0xac93, 0x6d5e, 0xad86, 0xad87, 0xad88, 0xad89,
  0xae41, 0xae42, 0xae43, 0xae44, 0xae45, 0xae46, 0xae4a, 0xae47,
  0xae48, 0xae49, 0xb16b, 0xb166, 0x52fd, 0xb167,
  /* 0x3e */
  0x3bab, 0xb168, 0x3bac, 0x52fd, 0xb2fd, 0xb2fe, 0xb2ff, 0xb300,
  0xb301, 0xb302, 0xb303, 0xb304, 0xb305, 0xb306, 0xb533, 0xb5c2,
  0xb5d0, 0xfb74, 0xb695, 0xb696, 0xb735, 0xb736, 0xb867, 0xb868,
  0xb869, 0xb86a, 0xb872, 0xb86b, 0xb86c, 0xb86d, 0xb86e, 0xb86f,
  0xba22, 0x52fd, 0x52fd, 0xba23, 0xba41, 0xba24, 0xba25, 0xba26,
  0xba27, 0xba28, 0xba29, 0xb9b7, 0x52fd, 0x52fd, 0x52fd, 0xbcda,
  0xbdcf, 0xbdda, 0xbf89, 0xbfb7, 0xbf8a, 0xbf8b, 0xc05e, 0xbf8c,
  0x52fd, 0xbf8d, 0xbf8e, 0xbf8f, 0xbf90, 0xbf91, 0xbf92, 0xbf93,
  0xbf94, 0xbfb6, 0xbf95, 0xbf96, 0xbf97, 0x3ff7, 0x52fd, 0xbf98,
  0xbf99, 0xbf9a, 0x0fea, 0x52fd, 0xbf9b, 0xbfbd, 0xbf9c, 0xbf9d,
  0xbf9e, 0xbf9f, 0xbfc0, 0xbfa0, 0xbfa1, 0xbfa2, 0xbfa3, 0xbfa4,
  0xbfa5, 0xbfa6, 0xbfa7, 0xbfa8, 0xbfa9, 0xbfaa,
  /* 0x3f */
  0x52fd, 0xc4b4, 0xc4b5, 0xc4b6, 0xc5b9, 0xc5ba, 0xc5bb, 0xc591,
  0xc5bc, 0xc8ee, 0xc8ef, 0xc996, 0xc997, 0xc973, 0xc998, 0xc999,
  0xcaed, 0xcaee, 0xcbde, 0xccb0, 0xccb1, 0xccb2, 0xccb3, 0xccb4,
  0xccb5, 0xccb6, 0xfbce, 0xccb7, 0xcee3, 0xcfb9, 0xd055, 0xd07e,
  0xd056, 0xd057, 0xd058, 0xd059, 0xd05a, 0x52fd, 0xd1c4, 0xd1c5,
  0xd1c6, 0xd1c7, 0xd1c8, 0xd1c9, 0xd1ee, 0xd1cf, 0xd1ca, 0xd1cb,
  0xd1cc, 0xd309, 0xfbda, 0xd303, 0xd52f, 0xd530, 0xd531, 0xd532,
  0xd80e, 0xd80f, 0xd810, 0xd811, 0xd812, 0x52fd, 0xd813, 0xd814,
  0xd815, 0xd7ff, 0xda1f, 0xda20, 0xda21, 0xda2d, 0x52fd, 0xdb1b,
  0xdb1c, 0xdb1d, 0xdb1e, 0xdc53, 0xdc54, 0xdc55, 0xdc56, 0xdc64,
  0xdc59, 0xdc57, 0x52fd, 0xdc58, 0xdf6b, 0x1489, 0x52fd, 0xdfe7,
  0xdfe8, 0xdfe9, 0xdfea, 0xdfeb, 0xdfec, 0x4a96,
  /* 0x40 */
  0xdfed, 0xe1b1, 0xe1b4, 0x52fd, 0xe38a, 0xe38d, 0xe38c, 0xe4f2,
  0xe4f3, 0x52fd, 0x7408, 0xe717, 0x15b2, 0xe89f, 0xe9a0, 0x52fd,
  0xeb6d, 0x837d, 0xf290, 0xf8c4, 0x534c, 0x5474, 0x5475, 0x5476,
  0x5649, 0x564a, 0x564b, 0x564c, 0x564d, 0x564e, 0x564f, 0x5650,
  0x5651, 0x5652, 0x5653, 0x5654, 0x5655, 0x5656, 0x57a4, 0x57a9,
  0x582a, 0x582b, 0x5ab3, 0x5ac1, 0x5b98, 0x5bfb, 0x5bfc, 0x5cbc,
  0x5d96, 0x5e0a, 0x52fd, 0x61a2, 0x52fd, 0x6181, 0x6182, 0x6183,
  0x6184, 0x6185, 0x6186, 0x6187, 0x6188, 0x6189, 0x618a, 0x618b,
  0x618c, 0x1cf1, 0x618d, 0x61d1, 0x6514, 0x66c9, 0x66ca, 0x66cb,
  0xedfd, 0x66cc, 0x66cd, 0x66ce, 0x66cf, 0x66d0, 0x66d1, 0x66d2,
  0x66d3, 0x66d4, 0x66d5, 0x6871, 0x534d, 0x6962, 0x6b0e, 0x6afe,
  0x6aff, 0x6b00, 0x52fd, 0x6b01, 0x6b02, 0x6b03,
  /* 0x41 */
  0x6b15, 0x6b04, 0x6b05, 0x52fd, 0x6c87, 0x6c89, 0x6d6e, 0x6d6f,
  0x6d70, 0x6d71, 0xfa6e, 0x6d72, 0x6d73, 0x6d74, 0x6e9d, 0x6fb8,
  0x6fb9, 0x6fba, 0x71c2, 0x71c3, 0x71d5, 0x71c4, 0x71c0, 0x71c5,
  0x71c6, 0x758a, 0x758b, 0x758c, 0x7664, 0xae8c, 0x2441, 0x770d,
  0x77a8, 0x783b, 0x783c, 0x783d, 0x783e, 0x783f, 0x7840, 0x7a66,
  0x7acb, 0x7ab7, 0x7ab8, 0x7ab9, 0x7aba, 0x7a67, 0x7abb, 0x7a68,
  0x7a69, 0x7a6a, 0x7abc, 0x52fd, 0x7cdb, 0x7f81, 0x7f82, 0x7f83,
  0x7f84, 0x7f85, 0x7f86, 0x7f87, 0x7f88, 0x7f89, 0xfabf, 0x7f8a,
  0x7f8b, 0x52fd, 0x7f8c, 0x7f8d, 0x7f8e, 0x7f8f, 0x7f90, 0x7fa1,
  0x7f91, 0x81d6, 0x829f, 0x8358, 0x8385, 0x840b, 0x840c, 0x8537,
  0x8538, 0xface, 0x8539, 0x853a, 0x853b, 0x853c, 0x853d, 0x853e,
  0x8540, 0x8541, 0x8542, 0x52fd, 0x8543, 0x868a,
  /* 0x42 */
  0x868b, 0x8890, 0x8859, 0x885b, 0x885c, 0x885d, 0x885e, 0x2a8a,
  0x885f, 0x8860, 0x8861, 0x8862, 0x8863, 0x8864, 0x8893, 0x8865,
  0x8866, 0x8867, 0x8868, 0x8869, 0x886a, 0x886b, 0x8895, 0x886c,
  0x886d, 0x886e, 0x886f, 0x2a8c, 0x8870, 0x8871, 0x8872, 0x8873,
  0x8874, 0x8875, 0x8876, 0x8877, 0x8878, 0x8879, 0x2a80, 0x887a,
  0x2a7f, 0x887b, 0x88a9, 0x887c, 0x8bf1, 0x8beb, 0x8c8a, 0x8d3d,
  0x8da6, 0x8da7, 0x8eba, 0x52fd, 0x8ebb, 0x8ebc, 0x8ebd, 0x9191,
  0x9192, 0x9193, 0x9194, 0x9195, 0x9196, 0x9197, 0x90da, 0x9198,
  0x9199, 0x919a, 0x52fd, 0x52fd, 0x919b, 0x919c, 0x919d, 0x919e,
  0x919f, 0x91a0, 0x91bf, 0x91a1, 0x91a2, 0x91a3, 0x91a4, 0x91a5,
  0x91a6, 0x91a7, 0x915b, 0xfb0d, 0x91a8, 0x95cb, 0x52fd, 0x09da,
  0x95cc, 0x964f, 0x95cd, 0x95ce, 0x95cf, 0x964e,
  /* 0x43 */
  0x95d0, 0x95d1, 0x95d2, 0x52fd, 0x95d3, 0x95d4, 0x95d5, 0x95d6,
  0x95d7, 0x95d8, 0x95d9, 0x95fa, 0x95da, 0x95db, 0xfb1c, 0x95dc,
  0x52fd, 0x52fd, 0x95dd, 0x95de, 0x95df, 0x95e0, 0x95e1, 0x95e2,
  0x95e3, 0x95e4, 0x95e5, 0x09db, 0x95e6, 0x95e7, 0x988e, 0x9998,
  0x9999, 0x52fd, 0x9b0a, 0x52fd, 0x9b26, 0x9b27, 0x9cbd, 0x9cdf,
  0x0ae2, 0x9cbe, 0x9cde, 0x9cbf, 0x9cc0, 0x9cc1, 0x9cc2, 0x9c9f,
  0x9cc3, 0x9cc4, 0x9cc5, 0x9cc6, 0x9cc7, 0x0ae3, 0x9cc8, 0x9cc9,
  0x9ce3, 0x9cca, 0x9ccb, 0x9ccc, 0x9ccd, 0x9cce, 0x9ccf, 0x9cd0,
  0x9cd1, 0x9cd2, 0x9cd3, 0xfb2f, 0x9cd4, 0x9e04, 0x9e65, 0x9eee,
  0x9fa0, 0x9fa1, 0xa0dd, 0xa0de, 0xa283, 0xa2fa, 0xa37f, 0xa380,
  0xa4e8, 0xa4e9, 0xa4ea, 0x88a6, 0xa6e1, 0x52fd, 0xa7db, 0xa7dc,
  0xa7dd, 0xa7de, 0xa7df, 0xa7e0, 0xa7e1, 0xa7e2,
  /* 0x44 */
  0xa7e3, 0xa7e4, 0xa7e5, 0xa7e6, 0x379c, 0xa7e7, 0xa9b0, 0xa998,
  0xa9b1, 0xa999, 0xa9b2, 0xa9b3, 0xa9b4, 0xa9b5, 0xa9b6, 0xa9b7,
  0xa9b8, 0xa9b9, 0xaae2, 0xaae3, 0xaae4, 0xaae5, 0xaae6, 0xaae7,
  0xaae8, 0xaae9, 0xacad, 0xaccf, 0xacae, 0xacaf, 0xacb0, 0xacb1,
  0xad92, 0xad93, 0xad94, 0xae79, 0x3a7b, 0xae7a, 0xae7b, 0xae7c,
  0xae7d, 0xae7e, 0xae7f, 0xae80, 0xae81, 0xae82, 0xae83, 0xae84,
  0xb197, 0xb184, 0xb185, 0xb189, 0xb186, 0xb187, 0xb32c, 0xb32d,
  0xb32e, 0xb32f, 0xb330, 0xb331, 0xb332, 0xb333, 0xb334, 0xb335,
  0xb336, 0xb337, 0xb5d2, 0xb744, 0xb800, 0xb883, 0xb884, 0xfb86,
  0xba7a, 0xba7b, 0xba7c, 0xba7d, 0xba7e, 0x52fd, 0xba7f, 0xba87,
  0xba80, 0xbd43, 0xc034, 0x1249, 0xc035, 0xc036, 0xc037, 0xc038,
  0xc039, 0xc03a, 0xc03b, 0xc03c, 0xc03d, 0xc03e,
  /* 0x45 */
  0xc03f, 0xc040, 0xc041, 0xc042, 0xc043, 0xc044, 0xc045, 0xc046,
  0xc047, 0xc048, 0xc049, 0xc04a, 0x52fd, 0xc04b, 0xc04c, 0xc04d,
  0xc04e, 0xc5e7, 0xc5e8, 0xc5e9, 0xc5ea, 0xc5eb, 0xc5ec, 0xc5ed,
  0xc5ee, 0xc8f7, 0xc99a, 0xc9cd, 0xfbc8, 0xc9a3, 0xc9ce, 0xcb31,
  0xcb32, 0xcb33, 0xcce4, 0x52fd, 0xcce5, 0xcce6, 0xcfc8, 0xd074,
  0xd075, 0xd076, 0xd077, 0xd078, 0xd079, 0xd07a, 0xd161, 0xd1df,
  0xd1e0, 0xd1e1, 0xd1e2, 0xd1e3, 0xd1e4, 0xd1e5, 0xd1e6, 0x12f3,
  0xd331, 0xd53d, 0xd53e, 0xd53f, 0xd5f4, 0xd5f5, 0xd5f6, 0x52fd,
  0xd717, 0xd841, 0xd842, 0xd843, 0xd844, 0xd845, 0xd846, 0xd847,
  0xd848, 0xd849, 0xd84a, 0xd84b, 0x46d2, 0xd9ba, 0xda44, 0xdc2a,
  0xdc6d, 0x4774, 0xfbe7, 0xdc7e, 0xdc6e, 0xdc6f, 0xdc70, 0xdc71,
  0xdc76, 0xdf79, 0x4aa0, 0x52fd, 0xe004, 0xdff8,
  /* 0x46 */
  0xe005, 0xe1da, 0xe1db, 0xe1dc, 0xe1dd, 0xe1de, 0xe1df, 0xe1e0,
  0xe1e1, 0xe3c5, 0xe39a, 0xe39b, 0xe39c, 0xe4d8, 0xe4d9, 0xe4da,
  0xe56e, 0x1557, 0xe76b, 0xd083, 0x52fd, 0xe9d6, 0xe9bd, 0x52fd,
  0x852e, 0xedfc, 0xf053, 0xf0b7, 0xf58c, 0xf68e, 0xf5ff, 0x5376,
  0x5406, 0x547e, 0x5684, 0x5685, 0x5686, 0x5687, 0x5688, 0x56a7,
  0x5689, 0x568a, 0x568b, 0x568c, 0x568d, 0x568e, 0x568f, 0x56a3,
  0x5690, 0x5691, 0x5692, 0x5693, 0x56ac, 0x5694, 0x5695, 0x5696,
  0x57aa, 0x57ab, 0x589e, 0x18a9, 0x5908, 0x5909, 0x5968, 0x5969,
  0x596a, 0x599a, 0x5ad9, 0x5ada, 0x5adb, 0x5ba8, 0x5c01, 0x5c28,
  0x5c77, 0x5cbd, 0x5cbe, 0x6260, 0x5da0, 0x5da2, 0x5e0f, 0x5e85,
  0x61fe, 0x61ff, 0x6200, 0x6201, 0x6216, 0x6202, 0x6203, 0x6204,
  0x6205, 0x6206, 0x6207, 0x6208, 0x6222, 0x6209,
  /* 0x47 */
  0x620a, 0x620b, 0x620c, 0x6214, 0x6229, 0x616e, 0x620d, 0x620e,
  0x620f, 0x6210, 0x6211, 0x6212, 0x6213, 0x651b, 0x651c, 0x651d,
  0x6713, 0x66ff, 0x6700, 0x6701, 0x6702, 0x6703, 0x6704, 0x670a,
  0x6705, 0x6706, 0x6707, 0x6708, 0x683a, 0x6873, 0x696f, 0x6970,
  0x6971, 0x5352, 0x52fd, 0x6d9a, 0x6b60, 0xfa69, 0x6b2b, 0x6b24,
  0x6b2c, 0x6b2d, 0x6b2e, 0x6b2f, 0x6b30, 0x6b31, 0x6b32, 0x6b33,
  0x6b34, 0x6b35, 0x6b45, 0x6b36, 0x6c92, 0x6c93, 0x6d88, 0x6d89,
  0x6d8a, 0x6d8b, 0x6e3f, 0x52fd, 0x6fca, 0x71f6, 0x71f7, 0x71f8,
  0x71f9, 0x742b, 0x52fd, 0x74a4, 0x74ce, 0x52fd, 0x759e, 0x759f,
  0x75a0, 0x75a1, 0x75a2, 0x75a3, 0x75a4, 0x75a5, 0x75a6, 0x7718,
  0x7860, 0x7861, 0x7862, 0x7863, 0x7864, 0x7abd, 0x7abe, 0x7b0b,
  0x7abf, 0x7ac0, 0x52fd, 0x7b0c, 0x7a8d, 0x7b0d,
  /* 0x48 */
  0x7b0e, 0x7b5e, 0x7b0f, 0x7b10, 0x7ac1, 0x7b11, 0x52fd, 0x7ac2,
  0x7cea, 0x7ceb, 0x7fe3, 0x7fe4, 0x7fe5, 0x7fe6, 0x7fe7, 0x7f92,
  0x804a, 0x52fd, 0x7fe8, 0x7fe9, 0x7fea, 0x8001, 0x7feb, 0x7fec,
  0x7fef, 0x7fed, 0x7fee, 0x7ff0, 0x7ff1, 0x7ff2, 0x7ff3, 0x7ff4,
  0x7ff5, 0x7ff6, 0x7ff7, 0x7ff8, 0x7ff9, 0x7ffe, 0x7ffa, 0x7ffb,
  0x7ffc, 0x8563, 0x8564, 0x8565, 0x8566, 0x8567, 0x8568, 0x890b,
  0x890d, 0x07bc, 0x890e, 0x890f, 0x07a4, 0x8914, 0x88d4, 0x88d5,
  0x88d6, 0x88d7, 0x88d8, 0x88d9, 0x88da, 0x88db, 0x88dc, 0x88dd,
  0x88de, 0x88df, 0x88e0, 0x88e1, 0x88e2, 0x88e3, 0x88e4, 0x88e5,
  0x88e6, 0x88e7, 0x88e8, 0x88e9, 0x88ea, 0x88eb, 0x88ec, 0x89ec,
  0x88ed, 0x88ee, 0x88ef, 0x8900, 0x88f0, 0x8c04, 0x52fd, 0x8c05,
  0xfaf3, 0x8db5, 0xfaf9, 0x8ed6, 0x8ed7, 0x8ed8,
  /* 0x49 */
  0x8ed9, 0x91a9, 0x9200, 0x9201, 0x9202, 0x9203, 0x9204, 0x9205,
  0x9206, 0x923f, 0x9207, 0x9208, 0x9209, 0x920a, 0x91b6, 0x920b,
  0x920c, 0x52fd, 0x52fd, 0x920d, 0x52fd, 0x920e, 0x920f, 0x9210,
  0x9211, 0x9212, 0x9235, 0x9213, 0x9214, 0x91aa, 0x52fd, 0x92c1,
  0x9215, 0x9216, 0x9217, 0x961e, 0x961f, 0x9620, 0x9621, 0x52fd,
  0x9622, 0x9623, 0x3195, 0x9624, 0x9625, 0x9626, 0x9627, 0x9628,
  0x9629, 0x962a, 0x962b, 0x9607, 0x962c, 0x962d, 0x962e, 0x09e8,
  0x52fd, 0x962f, 0x9630, 0x9648, 0x9631, 0x9632, 0x3198, 0x9633,
  0x52fd, 0x9634, 0x9635, 0x9636, 0x9859, 0x9891, 0x98e7, 0x99b8,
  0x99b9, 0x99ba, 0x99bb, 0x99c8, 0x9b4d, 0x9b89, 0x9b50, 0x9b28,
  0x9cfb, 0x9cfc, 0x9cfd, 0x9cfe, 0x9cff, 0x3378, 0x9d00, 0x9d01,
  0x9d02, 0x9ce4, 0x9ce5, 0x9d03, 0x9d04, 0x9d05,
  /* 0x4a */
  0x9d06, 0x9d07, 0x9d13, 0x9d08, 0x9d09, 0x9ce6, 0x9d0a, 0x9d0b,
  0x9d0c, 0x9e0b, 0x9ef4, 0x9f0a, 0x9fb1, 0xa11d, 0xfb3b, 0xa28b,
  0xa28c, 0xa28f, 0xa28d, 0xa28e, 0xa306, 0xa386, 0xa522, 0xa523,
  0xa524, 0xa525, 0xa526, 0xa527, 0xa528, 0xa6f1, 0x37b9, 0xa823,
  0xa812, 0x52fd, 0xa813, 0xa814, 0xa815, 0xa816, 0xa817, 0xa818,
  0xa819, 0xa84b, 0xa81a, 0xa81b, 0xa81c, 0x52fd, 0xa81d, 0xa81e,
  0xa81f, 0xa820, 0xa9ca, 0x75ac, 0xa9cb, 0xa9cc, 0xa9cd, 0xa9ce,
  0x52fd, 0xa9cf, 0xab10, 0xab11, 0xab12, 0xab13, 0xab14, 0xab0f,
  0xab15, 0xab16, 0xab17, 0x3933, 0xab18, 0xacc6, 0xad9f, 0xada0,
  0xada4, 0xada1, 0xaeb5, 0xaeb6, 0xaeb7, 0xaeb8, 0xaeb9, 0xaeba,
  0xaed0, 0xaec6, 0xaebb, 0xaebc, 0xaebd, 0xaebe, 0xaebf, 0xaec0,
  0xaec1, 0xaec2, 0xfb60, 0xb1a7, 0xb1a8, 0xb1a9,
  /* 0x4b */
  0xb1aa, 0xb1ab, 0xb1ac, 0xb1bc, 0xb1ad, 0xb1ae, 0x3bc0, 0xb1af,
  0xb1b0, 0xb1b1, 0x3bc1, 0xb1b4, 0xb1b2, 0xb366, 0xb367, 0xb368,
  0xb369, 0xb36a, 0xb36b, 0xb36c, 0xb36d, 0xb36e, 0xb36f, 0x52fd,
  0xb53e, 0xb53f, 0xb540, 0xb5e8, 0xb752, 0xb753, 0xb754, 0xb755,
  0xb891, 0xb892, 0xb893, 0xb894, 0xb895, 0xb896, 0xb897, 0xb898,
  0x52fd, 0xb899, 0xb89a, 0xb89b, 0xbab7, 0xbab8, 0xbab9, 0x52fd,
  0xbaba, 0xbabb, 0xbabd, 0x52fd, 0xbabc, 0xfb88, 0xfb87, 0xbbeb,
  0xbbec, 0xbc16, 0xbc44, 0xbce3, 0xbd58, 0xbd53, 0xbd54, 0xbde0,
  0xc0b1, 0xc0b2, 0xc0b3, 0xc0ea, 0x52fd, 0xc0b4, 0xc0f0, 0xc0b5,
  0xc0b6, 0xc0b7, 0xc0b8, 0xc0b9, 0xc0ba, 0xc0bb, 0xc0bc, 0xc0bd,
  0xc0be, 0xc0bf, 0xc0c0, 0xc0c1, 0xc0c2, 0xc0c3, 0x52fd, 0xc0c4,
  0xc0c5, 0xc0c6, 0xc0fc, 0xc0c7, 0xc0c8, 0xc0c9,
  /* 0x4c */
  0xc0ca, 0xc0cb, 0xc0cc, 0xc184, 0xc0cd, 0xc0ce, 0xc0cf, 0xc0d0,
  0xc0d1, 0xc0d2, 0xc4ca, 0xc4cb, 0xc636, 0xc637, 0xc69f, 0xc638,
  0xc639, 0xc63a, 0x4144, 0xc9be, 0xc9f0, 0xc9bf, 0xc9c0, 0xcaf7,
  0xcaf8, 0xcafa, 0xcb43, 0xcb44, 0xcd0f, 0xcd10, 0xcd11, 0xcd12,
  0x43ad, 0xcd13, 0xcd14, 0xcd15, 0xcd16, 0x52fd, 0x52fd, 0xcfd3,
  0xcfd7, 0x52fd, 0xd0b0, 0xd098, 0xd090, 0xd091, 0xd092, 0xd097,
  0xd093, 0xd164, 0xd201, 0xd202, 0xd203, 0xd204, 0xd205, 0xd206,
  0xd207, 0xd35a, 0xd35b, 0xd54e, 0xd54f, 0xd550, 0xd619, 0xd61a,
  0xd61b, 0x52fd, 0xd61c, 0x52fd, 0xd719, 0xd71a, 0x1386, 0xdc8a,
  0xd86e, 0xd86f, 0x4664, 0xd870, 0xd871, 0xd872, 0xd873, 0xd874,
  0xd875, 0xd876, 0xdb3a, 0xdb3b, 0xdb3c, 0xdb5a, 0xdb3d, 0xdb42,
  0xdc8b, 0xdca0, 0xdc8c, 0xdc8d, 0xdc8e, 0xdcab,
  /* 0x4d */
  0xdc8f, 0x4777, 0xdc90, 0xdc91, 0xdc92, 0xdcaa, 0x47af, 0xdc93,
  0xdc94, 0xdc9e, 0xdca8, 0x52fd, 0x52fd, 0xe200, 0xe201, 0xe202,
  0xe203, 0xe204, 0xe205, 0xe2c4, 0xe2c5, 0x52fd, 0xe3ae, 0xe3b2,
  0x52fd, 0x52fd, 0xe4f7, 0xe513, 0xe69e, 0xe785, 0xa680, 0xe7c8,
  0xeb41, 0xeb81, 0xeb82, 0xeb83, 0xeb84, 0xedc4, 0xedc5, 0xedc6,
  0xef95, 0xef96, 0xef97, 0xf058, 0xf059, 0x52fd, 0xf2a9, 0xf2aa,
  0xf2ab, 0xf58e, 0xf603, 0xf6b3, 0x5e82, 0x5353, 0x5483, 0x5484,
  0x5485, 0x56cc, 0x56cd, 0x56ce, 0x56cf, 0x56d0, 0x52fd, 0x56d1,
  0x56d2, 0x56da, 0x57b3, 0x57b4, 0x52fd, 0x5913, 0x590e, 0x596b,
  0x5c03, 0x5c61, 0x5cc1, 0x5cc2, 0x5db5, 0x5440, 0x6298, 0x6299,
  0x629a, 0x629b, 0x629c, 0x629d, 0x629e, 0x629f, 0x62ee, 0x62a0,
  0x62a1, 0x6286, 0x6525, 0x6734, 0x6735, 0x6751,
  /* 0x4e */
  0x672d, 0x9662, 0x6754, 0x6752, 0x026d, 0x6736, 0x6737, 0x6738,
  0x6739, 0x673a, 0x673b, 0x6768, 0x673c, 0x026e, 0x6877, 0x8596,
  0x697e, 0x697f, 0x6b69, 0x6b6a, 0x6b6b, 0x6b6c, 0x6b6d, 0x6b6e,
  0x6b6f, 0x6b5d, 0x6b70, 0x6b85, 0x6b71, 0x6b72, 0x6b73, 0x6b74,
  0x6b75, 0x6b76, 0x6b84, 0x6b77, 0x6b83, 0x6c9a, 0x6da7, 0x6da8,
  0x6da9, 0x6daa, 0x6dab, 0x6dac, 0x6db7, 0x6dad, 0x6dae, 0x52fd,
  0x6e45, 0x6fd3, 0x6fd4, 0x6fd5, 0x6fd6, 0x6fd7, 0x7226, 0x7227,
  0x7228, 0x52fd, 0x7229, 0x722a, 0x7305, 0x2364, 0x74d0, 0x75b7,
  0x52fd, 0x75b8, 0x75b9, 0x7722, 0x7723, 0x7873, 0x7874, 0x7b8f,
  0x7b12, 0x7b13, 0x7b14, 0x7b15, 0x7b16, 0x7b17, 0x7b18, 0x7b61,
  0x7b66, 0x7b67, 0x7b19, 0x7b68, 0x7b1a, 0x7b69, 0x7d04, 0x7d05,
  0x52fd, 0x7d06, 0x7cfd, 0x8050, 0x8051, 0x8052,
  /* 0x4f */
  0x8053, 0x8054, 0x8055, 0x8063, 0xfac4, 0x8056, 0x8057, 0x8058,
  0x8059, 0x805a, 0x805b, 0x52fd, 0x805c, 0x805d, 0x805e, 0x805f,
  0x82d7, 0x52fd, 0x8360, 0x8361, 0x8417, 0x8439, 0x858f, 0x85c8,
  0x8590, 0x8591, 0x8698, 0x8699, 0x8944, 0x8945, 0x8946, 0x8959,
  0x89ee, 0x2b2b, 0x8947, 0x8948, 0x52fd, 0x8949, 0x894a, 0x894b,
  0x894c, 0x894d, 0x894e, 0x894f, 0x8950, 0x8951, 0x8952, 0x8953,
  0x8954, 0x8955, 0x8956, 0x8c15, 0x8c9a, 0x8d58, 0x8dfc, 0x8dfd,
  0x9286, 0x9287, 0x9288, 0x9289, 0x928a, 0x928b, 0x928c, 0x52fd,
  0x931f, 0x928d, 0x928e, 0x928f, 0x9290, 0x9291, 0x9292, 0x9293,
  0xfb11, 0x9294, 0x9295, 0x91ca, 0x9218, 0x52fd, 0x9296, 0x9297,
  0x9298, 0x9299, 0x929a, 0x929b, 0x929c, 0x929d, 0x929e, 0x929f,
  0x2f46, 0x52fd, 0x92a0, 0x92a1, 0x2f9a, 0x92b7,
  /* 0x50 */
  0x52fd, 0x9696, 0x9668, 0x9669, 0x968c, 0x966a, 0x966b, 0x966c,
  0x966d, 0x966e, 0x966f, 0x967f, 0x9684, 0x9670, 0x9671, 0x9685,
  0x9672, 0x9673, 0x9674, 0x9675, 0x9676, 0x52fd, 0x9677, 0x9678,
  0x9679, 0x967a, 0x967b, 0x967c, 0xfb1e, 0x9894, 0x99d5, 0x9b29,
  0x9d2b, 0x9d2c, 0x9d2d, 0x9d2e, 0x9d2f, 0x9d30, 0x9d3e, 0x9d31,
  0x9d32, 0x9d33, 0x9d34, 0x9d35, 0x9e11, 0x0b53, 0x9fc0, 0xa142,
  0xa143, 0xa144, 0xa145, 0xa146, 0xa38c, 0xa38d, 0xa38e, 0xa38f,
  0xa390, 0xa391, 0xa563, 0xa564, 0xa565, 0x52fd, 0xfb4a, 0x52fd,
  0xa6f6, 0xa836, 0xa837, 0xa82c, 0xa838, 0xa839, 0x52fd, 0xa83a,
  0xa9e3, 0xa9e2, 0xa9f5, 0xab47, 0x52fd, 0xab48, 0xab49, 0xab4a,
  0xab4b, 0xab4c, 0xada8, 0xada9, 0xadaa, 0x52fd, 0xaf0b, 0xaf06,
  0xaf73, 0xaf0c, 0xaf0d, 0xaf0e, 0xaf0f, 0xaf10,
  /* 0x51 */
  0x52fd, 0xaf11, 0xfb62, 0xaf12, 0xaf14, 0xaf15, 0xaf16, 0x52fd,
  0xaf13, 0xaf17, 0xb1d9, 0xb1da, 0xb1db, 0x52fd, 0xb1dc, 0xb3a6,
  0xb3a7, 0xb3a8, 0xb3a9, 0xb3aa, 0xb3ab, 0x52fd, 0xfb6d, 0xb3ac,
  0xb3ad, 0xb3ae, 0x52fd, 0x52fd, 0xb3af, 0xb3b0, 0xb3b1, 0xb544,
  0xb545, 0xb5f7, 0xb5f8, 0xb76b, 0xb76c, 0xb761, 0xb812, 0xb8af,
  0xb8b0, 0xb8b1, 0xb8b2, 0xb8b3, 0xb8b4, 0xb8b5, 0xb8b6, 0xb922,
  0xbaea, 0xbaeb, 0xbaec, 0xbaed, 0x0f3d, 0xbbed, 0xbc1b, 0xbc47,
  0xbce8, 0xbdd0, 0xbde7, 0xbde8, 0xbde9, 0xc14e, 0xc14f, 0xc150,
  0xc172, 0xc151, 0xc152, 0xfba7, 0x1026, 0xc153, 0xc154, 0xc155,
  0xc156, 0xc157, 0xc177, 0xc158, 0xc159, 0xc15a, 0xc15b, 0xc15c,
  0xc16e, 0xc15d, 0x1027, 0x52fd, 0xc15e, 0x1028, 0xc15f, 0x52fd,
  0xc4d7, 0xc689, 0xc68a, 0x52fd, 0xfbbe, 0xc68b,
  /* 0x52 */
  0xc68c, 0xc68d, 0xc68e, 0xfbbc, 0xc68f, 0x52fd, 0xc69a, 0xc690,
  0xc691, 0xc692, 0xc693, 0xca12, 0xc9f1, 0x52fd, 0xcafe, 0xcafb,
  0xcb57, 0xcd49, 0xcd4a, 0xcd4b, 0xcd4c, 0x52fd, 0xcd4d, 0xcd59,
  0xcd4e, 0xcd4f, 0xcef4, 0xcf65, 0xd0a6, 0xd0a7, 0xd0a8, 0xd222,
  0xd223, 0xd38b, 0xd38c, 0xd38d, 0xd38e, 0xd558, 0x52fd, 0xd559,
  0xd63b, 0xd63c, 0xd63d, 0xd63e, 0xd748, 0xd894, 0xd895, 0xd896,
  0xd897, 0xd898, 0xda7d, 0xd9fe, 0xda7e, 0xda7f, 0x52fd, 0xdb5b,
  0xdb5c, 0xdce4, 0xdcc1, 0xdcfd, 0xdcc2, 0xdcd6, 0xdcc3, 0xdcc4,
  0x52fd, 0xdcdc, 0xdcc5, 0xdcc6, 0xdcc7, 0xdce1, 0xdcc8, 0xdcc9,
  0xdcca, 0xdccb, 0x47f2, 0xdccc, 0xdccd, 0xe035, 0xe036, 0xe047,
  0xe037, 0xe21b, 0xe21c, 0xe21d, 0xe21e, 0xe21f, 0xe220, 0xe27b,
  0xe27a, 0xe2fe, 0xe3c6, 0xe3c7, 0xe3c8, 0xe3c9,
  /* 0x53 */
  0xe3ca, 0x52fd, 0xe7a0, 0xe7a1, 0xe8cc, 0xe8cd, 0xe9f1, 0xe9d7,
  0xeb42, 0xeb46, 0xeba3, 0xeba5, 0xeba6, 0xedcb, 0xedcc, 0xee26,
  0xee27, 0xee28, 0xee29, 0xefae, 0xefaf, 0xefb0, 0xf073, 0x52fd,
  0xf07c, 0xf074, 0x52fd, 0xf280, 0xf075, 0x4c79, 0x52fd, 0xf2cf,
  0x52fd, 0xf68f, 0x5970, 0x5385, 0x5697, 0x56f6, 0x56f7, 0x56f8,
  0x56fa, 0x56fb, 0x57ba, 0x57bb, 0x57bc, 0x57bd, 0x5834, 0x5835,
  0x58a2, 0x5914, 0x5911, 0x596e, 0x596f, 0x5b08, 0x5bb8, 0x5d09,
  0x5dbd, 0x5dbe, 0x5e88, 0x62fe, 0x1d7a, 0x62ff, 0x6300, 0x6301,
  0x6302, 0x6303, 0x6304, 0x6305, 0x6306, 0x52fd, 0x6307, 0x6308,
  0x6309, 0x630a, 0x630b, 0x630c, 0x630d, 0x630e, 0x630f, 0x52fd,
  0x52fd, 0x52fd, 0x652c, 0x676b, 0x52fd, 0x676c, 0x6787, 0x676d,
  0x6878, 0x698b, 0x6ba4, 0x6ba5, 0x6ba6, 0x6ba7,
  /* 0x54 */
  0x6bbe, 0x6ba8, 0x6ba9, 0x6baa, 0x6b8d, 0x6bab, 0x6bac, 0x6bbf,
  0x52fd, 0x032c, 0x6bad, 0x6bae, 0x6ca4, 0x6ca5, 0x6dc7, 0x6dc8,
  0x6dc9, 0xd0bc, 0x6dca, 0x6dcb, 0x6dcc, 0x6dcd, 0x6dce, 0x6f19,
  0x6fe6, 0x724c, 0x724d, 0x724e, 0x7306, 0x52fd, 0x75cd, 0x75ce,
  0x75cf, 0x75d0, 0x245c, 0x7881, 0x7882, 0x5833, 0x7b6a, 0x7b6b,
  0x7bb2, 0x7b6c, 0x7b6d, 0x7bcc, 0x7bae, 0x7bb3, 0x7b91, 0x52fd,
  0x7b6e, 0x7bb4, 0x7b6f, 0x80c9, 0x80a7, 0x80a8, 0x80a9, 0x80aa,
  0x80ab, 0x80ac, 0x80ad, 0x80ae, 0x80af, 0x80b0, 0x8060, 0x80b1,
  0x81e1, 0x82e3, 0x82f6, 0x26d9, 0x85af, 0x85b0, 0x85b1, 0x85ba,
  0x85b2, 0x85b3, 0x864c, 0x52fd, 0x86a0, 0x86a1, 0x89b0, 0x89b1,
  0x89b2, 0x89b3, 0x89b4, 0xfaeb, 0x89b5, 0x89b6, 0x89b7, 0x89b8,
  0x89b9, 0x89ba, 0x2b72, 0x89bb, 0x89bc, 0x89bd,
  /* 0x55 */
  0x89be, 0x89bf, 0x89df, 0x89c0, 0x89c1, 0x89c2, 0x89c3, 0x89c4,
  0x2b78, 0x52fd, 0x89c5, 0x89c6, 0x89c7, 0x89c8, 0x89c9, 0x89ca,
  0x8a3c, 0x89cb, 0x89cc, 0x89cd, 0x89ce, 0x89cf, 0x89d0, 0x89d1,
  0x2c5a, 0x8c1d, 0x8c1e, 0x8d64, 0x8d65, 0x8dc5, 0x8dc6, 0x8dc7,
  0x8dff, 0x8eff, 0x8f00, 0x8f01, 0x8f02, 0x92f5, 0x935c, 0x92f6,
  0x92f7, 0x92b0, 0x92f8, 0x92f9, 0x935d, 0x9350, 0x92fa, 0x92fb,
  0x92a2, 0x92fc, 0x92fd, 0x92fe, 0x92ff, 0xfb13, 0x9300, 0xfb1f,
  0x96c1, 0x96c2, 0x96c3, 0x96c4, 0x96c5, 0x96c6, 0x96c7, 0x96c8,
  0x96c9, 0x96ca, 0x96cb, 0xe3ef, 0x96d7, 0x96cc, 0x96cd, 0x96ce,
  0x9722, 0x96cf, 0x96d0, 0x52fd, 0x96d1, 0x96d2, 0x96e9, 0x96d3,
  0x96d4, 0x96d5, 0x9838, 0x633b, 0x9b7d, 0x586f, 0x9ba1, 0x9d53,
  0x9d54, 0x0afb, 0x9d55, 0x9d3c, 0x9d56, 0x52fd,
  /* 0x56 */
  0x9d66, 0x0afc, 0x9d57, 0x9d58, 0x9d59, 0x9d5a, 0x9d5b, 0x9d67,
  0x9d5c, 0x52fd, 0x52fd, 0x9d6a, 0x9d5d, 0x9d3d, 0x9d5e, 0x9d65,
  0x9fe1, 0x9fcd, 0xf311, 0xa16f, 0xa170, 0xa31a, 0xa39a, 0xa39b,
  0xa39c, 0xa39e, 0xa39f, 0xfb3f, 0xa58e, 0x52fd, 0xa6fd, 0x52fd,
  0xa85e, 0xa85f, 0xa860, 0xa865, 0xa867, 0xa868, 0xa869, 0x52fd,
  0xa86a, 0xa88f, 0xa86b, 0xa86c, 0xa86d, 0xa9f7, 0xa9f8, 0xa9f9,
  0xa9e4, 0xa9fa, 0xabb1, 0xab75, 0xab76, 0xacff, 0xad00, 0xad01,
  0xad02, 0xad03, 0xadae, 0xadad, 0xaf51, 0xaf52, 0x52fd, 0xaf53,
  0xaf54, 0xaf55, 0xaf56, 0xaf57, 0xaf58, 0xaf59, 0xaf5a, 0xaf5b,
  0xaf5c, 0xaf5d, 0xaf5e, 0xaf5f, 0xaf60, 0xaf61, 0xaf62, 0xaf63,
  0xaf64, 0xaf4c, 0xaf66, 0xb1f1, 0xb1f2, 0xb3f2, 0xb3f3, 0xb3f4,
  0xb3f5, 0xb3f6, 0xb3f7, 0xb3f8, 0xb3f9, 0xb3fa,
  /* 0x57 */
  0xb54c, 0xb54b, 0xb61b, 0xb6ce, 0xb6b3, 0xb77f, 0xfb79, 0xfb7c,
  0xb81a, 0xb8c4, 0xb8c5, 0xb8c6, 0xb8c7, 0xb8c8, 0xb8c9, 0x52fd,
  0xbb19, 0xbb1c, 0xbb1a, 0xbc4c, 0xbc8e, 0xbd7e, 0xc1eb, 0xc1ec,
  0xc1ed, 0xc1ee, 0xc1ef, 0xc1f0, 0xc1f1, 0xc1d8, 0xc1d9, 0xc1da,
  0xc1db, 0xc1f2, 0xc1f3, 0xc1f4, 0xc207, 0x52fd, 0xfbac, 0x52fd,
  0xc1f5, 0xc215, 0x52fd, 0xc1f6, 0xc1f7, 0xc1f8, 0xc1f9, 0xc1fa,
  0xc1fb, 0xc1fc, 0xc1fd, 0xc1fe, 0xc2a7, 0xc1ff, 0xc200, 0xc201,
  0xc202, 0xc203, 0xc204, 0xc205, 0xc4e9, 0xc4ea, 0xc4eb, 0xc6de,
  0xc6df, 0x52fd, 0xc6e0, 0xc6e1, 0xc6e2, 0xc6e3, 0xca3e, 0xca3f,
  0xca40, 0xca41, 0xcb71, 0xcb72, 0xcb73, 0xcd88, 0xcd89, 0xcd8a,
  0xcd8b, 0xcd8c, 0xcd8d, 0xcd8e, 0xcd8f, 0xcd90, 0xcd91, 0xcd92,
  0x52fd, 0xcff1, 0xd0be, 0xd0bf, 0xd0c0, 0xd0c1,
  /* 0x58 */
  0xd0c2, 0xd0c3, 0xd16e, 0xd237, 0xd257, 0xd238, 0xd239, 0xd23a,
  0xd23b, 0xd23c, 0xd23d, 0xd23e, 0xd227, 0xd23f, 0x52fd, 0x52fd,
  0xd3c0, 0xd3c1, 0x44be, 0xd567, 0x52fd, 0xd568, 0xd658, 0xd665,
  0xd659, 0xd65a, 0xd724, 0xd8cd, 0xd8ce, 0xd8cf, 0xd8d2, 0xda9f,
  0xda22, 0xdb7d, 0xdb7e, 0xdcff, 0xdd25, 0xdd00, 0xdd01, 0x483b,
  0xdd02, 0xdd03, 0xdd04, 0x5128, 0xdd05, 0xdd06, 0xdd07, 0xdd16,
  0xdd08, 0xdd17, 0xdd09, 0xdd0a, 0x4840, 0xdd0b, 0xdd0c, 0xdd29,
  0x483a, 0xdd0d, 0x52fd, 0xdd0e, 0xdd10, 0xdd11, 0xdd1d, 0xdd12,
  0xdd13, 0x52fd, 0xdd14, 0xdd15, 0xdf8d, 0xe054, 0xe055, 0xe056,
  0xe233, 0xe234, 0xe235, 0xe236, 0xe2fa, 0xe3e3, 0xe3e6, 0xe3ed,
  0xe3e7, 0xe3e8, 0xe3e9, 0xe3ea, 0xe3eb, 0xe527, 0xe520, 0xfbf8,
  0xe72a, 0xe7c9, 0x52fd, 0xe7ca, 0xe7cb, 0xe7d9,
  /* 0x59 */
  0xe7cc, 0xe7cd, 0xe7ce, 0x52fd, 0xe8e1, 0xea1b, 0xea1c, 0xea1d,
  0xea1e, 0xeb47, 0xebd1, 0xebc9, 0xedd0, 0xedd1, 0xee3f, 0xee40,
  0xee41, 0xfc09, 0xee42, 0xefc4, 0xf09b, 0xf092, 0xf093, 0x4c96,
  0xf094, 0xf095, 0x52fd, 0x52fd, 0xf2f3, 0xf2f4, 0xf2f5, 0xf2f6,
  0xf560, 0xf59e, 0xf5a0, 0xf5a1, 0xf61c, 0xf69f, 0x5356, 0x5712,
  0x571f, 0x5715, 0x5716, 0x5717, 0xfa0c, 0x5718, 0x57c2, 0x57c3,
  0x57c4, 0x58a1, 0x591b, 0x591c, 0x5971, 0x5b13, 0x5e17, 0x5e8f,
  0x635f, 0x6360, 0x63ac, 0x52fd, 0x52fd, 0x6361, 0x6362, 0x6363,
  0x6364, 0x6365, 0x52fd, 0x6799, 0x679a, 0x679b, 0x679c, 0x679d,
  0x679e, 0x679f, 0x67a0, 0x6bda, 0x6bdb, 0x6bdc, 0x6bdd, 0x6bde,
  0x6bdf, 0x6dd5, 0x6dd6, 0x6dd7, 0x6dd8, 0x6de0, 0x6eb4, 0x52fd,
  0x726e, 0x726f, 0x7270, 0x7463, 0x52fd, 0x75d5,
  /* 0x5a */
  0x75d6, 0x75d7, 0x75d8, 0x7730, 0x7bb5, 0x7bb6, 0x7bc1, 0x7bf7,
  0x05a0, 0x7bcb, 0x7be8, 0x52fd, 0x7be9, 0x7bb7, 0x7d15, 0x80e6,
  0x80e7, 0x80e8, 0x80e9, 0x80ea, 0x80eb, 0xfaca, 0x830b, 0x830c,
  0x8364, 0x85d4, 0x85d5, 0x85d6, 0x85d8, 0x86a5, 0x535b, 0x8a0f,
  0x8a10, 0x52fd, 0x8a11, 0x8a12, 0x8a13, 0x8a14, 0x8a15, 0x8a16,
  0x8a17, 0x8a18, 0x8a19, 0x8a1a, 0x8a2f, 0x8a1b, 0x8a1c, 0x8a3f,
  0x8a1d, 0x8a1e, 0x8a2c, 0x8a1f, 0x8c24, 0x8ca4, 0x8ca5, 0xb551,
  0x8dfe, 0x8f11, 0x8f12, 0x8f13, 0x8f54, 0x935e, 0x9321, 0x933f,
  0x9340, 0x9341, 0x9342, 0x9343, 0x9344, 0x9320, 0x9345, 0x52fd,
  0x9346, 0x93b0, 0x9347, 0x9357, 0x9301, 0x93b1, 0x935a, 0x9348,
  0x9704, 0x9705, 0x31f5, 0x974f, 0x9750, 0x9706, 0x9707, 0x9708,
  0x9709, 0x970a, 0x970b, 0x970c, 0x970d, 0x970e,
  /* 0x5b */
  0x970f, 0x9710, 0x9711, 0x9833, 0x9834, 0x9835, 0x98fc, 0x98fd,
  0x9bb0, 0x9bb1, 0x9bb2, 0x9bb3, 0x0b01, 0x9d7b, 0x9d7c, 0x9d7d,
  0x9d7e, 0x9d7f, 0x9d80, 0x9d81, 0x9d82, 0x9ef9, 0x9fda, 0xa1a1,
  0x52fd, 0x52fd, 0xa3ad, 0xa3ae, 0x52fd, 0xa5c8, 0xa5c9, 0xa5ca,
  0xa882, 0xa883, 0xa884, 0xa885, 0xa886, 0xa887, 0xa888, 0xa889,
  0xa88a, 0x52fd, 0xaa07, 0xaa08, 0xaa01, 0xaa09, 0xaa02, 0xaa0a,
  0xaa0b, 0xaa0c, 0xaba7, 0xab98, 0xab99, 0xab9a, 0xab9b, 0xab9c,
  0x3950, 0xab9d, 0xad1a, 0xadb2, 0xafb0, 0xafb1, 0x3b14, 0xafb2,
  0xafb3, 0xafb4, 0xafb5, 0xafb6, 0xafc0, 0xafb7, 0xafb8, 0xafb9,
  0xafba, 0xb01d, 0xb20c, 0xb20d, 0xb20e, 0xb235, 0xb20f, 0xb210,
  0xb211, 0xb212, 0xb422, 0xb423, 0xb424, 0x52fd, 0xb425, 0xb426,
  0xb427, 0xb428, 0xb429, 0xb42a, 0xb42b, 0xb42c,
  /* 0x5c */
  0xb42d, 0xb42e, 0xb552, 0xb553, 0xb619, 0xb6bf, 0xb78b, 0xb78c,
  0xb823, 0xb824, 0xb8f1, 0xb8d8, 0xfb7f, 0xbb4e, 0xbb4f, 0xbb55,
  0xbb50, 0xbb51, 0xbb52, 0xbb53, 0xbb02, 0xbb54, 0xbbf1, 0xbc24,
  0xbdd1, 0xc27c, 0xc27d, 0xc27e, 0xc27f, 0xc280, 0xc29f, 0xc281,
  0xc282, 0xc283, 0xc284, 0xc285, 0xc286, 0xc287, 0xc288, 0xc29b,
  0xc289, 0xc28a, 0xc28b, 0xc28c, 0x52fd, 0xc2a1, 0xc28d, 0xc28e,
  0xc4f3, 0xc4f4, 0xc4f5, 0xc724, 0xc725, 0xc726, 0xc727, 0xc728,
  0xc729, 0xc72a, 0x52fd, 0xc72b, 0xc8cd, 0xc901, 0x788d, 0xca70,
  0xcb80, 0xcb81, 0xcdc0, 0xcdc1, 0xcdc2, 0xcdc3, 0xcdc4, 0xcdc5,
  0xcdc6, 0xcdc7, 0xcdc8, 0xcffe, 0x52fd, 0xd0d9, 0xd0dc, 0xd0da,
  0xd175, 0xd258, 0xd259, 0xd25a, 0xd25b, 0xd25c, 0xd3ec, 0x131a,
  0xd3f3, 0xd572, 0xd573, 0xd682, 0xd683, 0xd72a,
  /* 0x5d */
  0xd72b, 0xd72c, 0xd72d, 0xd8f6, 0xd900, 0xd8f7, 0xd8f8, 0xd8f9,
  0x52fd, 0xdb94, 0xdb95, 0xdb96, 0xdc1b, 0xdd86, 0xdd4c, 0xdd4d,
  0xdd83, 0x52fd, 0xdd82, 0xdd4e, 0xdd4f, 0xdd50, 0xdd51, 0xdd81,
  0x4887, 0xdd52, 0xdd53, 0xdd54, 0xdd55, 0x52fd, 0xdd56, 0xdd57,
  0x4885, 0xdd58, 0xdd5b, 0xdd5c, 0xdd5d, 0xdd5e, 0xdd5f, 0xdd60,
  0xdd61, 0xdd7c, 0xdd62, 0x143c, 0x52fd, 0xdd63, 0xdd64, 0xe073,
  0xe245, 0xe246, 0xe247, 0xe24c, 0xe312, 0x52fd, 0xe313, 0xe439,
  0xe403, 0xe40d, 0xe530, 0xe5d5, 0xe6c0, 0xe7f2, 0xe7f3, 0xe7f4,
  0xe7f5, 0xe7f6, 0xe7f7, 0xe8fa, 0xe8fb, 0xea45, 0xea46, 0xeb49,
  0xebe7, 0xebe8, 0xedd8, 0xedd9, 0xee55, 0xee56, 0xee57, 0xefda,
  0xefdb, 0xefdc, 0xf0b8, 0xf0b9, 0xf0ba, 0x4cb1, 0xf0bb, 0xf289,
  0xf0bc, 0xf330, 0xf331, 0xf332, 0xf333, 0x52fd,
  /* 0x5e */
  0xf334, 0xf335, 0xf336, 0x4e47, 0xf337, 0xf5b2, 0xf690, 0xf626,
  0xf6a2, 0xf738, 0x52fd, 0x572e, 0x52fd, 0x572f, 0x5730, 0x572a,
  0x572b, 0x57c9, 0x5837, 0x5920, 0x5972, 0x5b1f, 0x5c65, 0x5cca,
  0x5dc0, 0x5dc1, 0x5dc2, 0x63b7, 0x63b8, 0x63b9, 0x63ba, 0x63bb,
  0x52fd, 0x52fd, 0x63bc, 0x63bd, 0x63be, 0x63f2, 0x67b5, 0x67b7,
  0x67b8, 0x63df, 0x67b9, 0x67d5, 0x67ba, 0x67bb, 0x684a, 0x6bf2,
  0x6bf3, 0x6bf4, 0x6bfa, 0x6bf5, 0x6c02, 0x6de2, 0x6de3, 0x728e,
  0x728b, 0x75e4, 0x75eb, 0x75e5, 0x7735, 0x7893, 0x7bea, 0x7beb,
  0x52fd, 0x7bec, 0x7bed, 0x7c17, 0x7bee, 0x7bef, 0x7bf0, 0x810e,
  0x810f, 0x8110, 0x8119, 0x8118, 0x8111, 0x8112, 0x8113, 0xfac7,
  0x8114, 0x8115, 0x066b, 0x8116, 0x8365, 0x85ea, 0x85eb, 0x85ec,
  0x85ed, 0x52fd, 0x85ee, 0x86ac, 0x8ac2, 0x8a69,
  /* 0x5f */
  0x8a81, 0x8a6a, 0x8a6b, 0x8a6c, 0x8a6d, 0x8a6e, 0x8a6f, 0x8a70,
  0x07f4, 0x8a71, 0x8a72, 0x8a73, 0x8a8c, 0x8a74, 0x8a75, 0x8a76,
  0x8f24, 0x8f2a, 0x938e, 0x93af, 0x938f, 0x9390, 0x9391, 0x52fd,
  0x9392, 0x9393, 0x9394, 0x9395, 0x9396, 0x9397, 0x9398, 0x9399,
  0x939a, 0x939b, 0x939c, 0x9739, 0x973b, 0x973a, 0x973c, 0x973d,
  0x973e, 0x973f, 0x9770, 0x9740, 0x9741, 0x9bc0, 0x9bc1, 0x9da4,
  0x9d97, 0x9da5, 0x9d98, 0x9d99, 0x9d9a, 0x52fd, 0x9d9b, 0x9da6,
  0x9d9c, 0x9d9d, 0x9d9e, 0x9d9f, 0xddce, 0x9dc8, 0x9da0, 0x9e19,
  0x9f0d, 0xa3bf, 0xa5e9, 0xa5ea, 0xa8a2, 0xa8a3, 0xa8a4, 0xa8a5,
  0xa8a6, 0xaa15, 0xaa17, 0xabbf, 0xabc0, 0xabc1, 0xabc2, 0xabc3,
  0xabc4, 0xabc5, 0x6c0d, 0xad23, 0xadbe, 0xb003, 0xb004, 0xb005,
  0xb006, 0xb007, 0xb008, 0xb009, 0xb00a, 0xb00b,
  /* 0x60 */
  0xb00c, 0xb020, 0xb00d, 0xb00e, 0xb00f, 0x52fd, 0xb226, 0xb227,
  0xb228, 0xb45d, 0xb45e, 0xb45f, 0xb460, 0xb461, 0xb462, 0xb463,
  0xb464, 0xb465, 0xb557, 0xb6cf, 0xb6d0, 0xb799, 0xb79a, 0xb8ea,
  0xb8eb, 0xb8ec, 0xbb7e, 0xbb7f, 0xbb80, 0x52fd, 0x52fd, 0xbb81,
  0xbb82, 0xbbf3, 0xbc25, 0xbc9f, 0xbca0, 0xbcf3, 0xbd00, 0xc2eb,
  0xc2ec, 0xc2ed, 0xc2ee, 0xc2ef, 0x52fd, 0x105c, 0xc2f0, 0xc2f1,
  0xc2f2, 0xc2f3, 0xc2f4, 0xc2f5, 0xc2f9, 0xc2fa, 0xc2fb, 0xc2fc,
  0xc2fd, 0xc2fe, 0xc2ff, 0xc300, 0xfbae, 0xc301, 0xc302, 0xc503,
  0xc786, 0xc779, 0x52fd, 0xc77a, 0xc77b, 0xc77c, 0xc784, 0xc77d,
  0xc77e, 0xc77f, 0xca86, 0xca87, 0xcb00, 0xcb01, 0xcb88, 0xcb89,
  0xcb8a, 0xcdf5, 0x52fd, 0xce25, 0xcdf6, 0xd0eb, 0xd0ec, 0xd0ed,
  0xd0ee, 0x1287, 0xd0ef, 0xd0f0, 0xd26c, 0xd421,
  /* 0x61 */
  0xd422, 0xd423, 0xd424, 0xd425, 0xd426, 0xd57b, 0xd57c, 0xd691,
  0xd692, 0xd693, 0xd90e, 0xd90f, 0xd910, 0xdbb3, 0xdc36, 0xddc6,
  0xdda1, 0xdda2, 0x48b9, 0xdda3, 0xddcc, 0xdda4, 0xdda5, 0xdda6,
  0xddcb, 0xdda7, 0x52fd, 0xdda8, 0xdda9, 0xddaa, 0x52fd, 0xddab,
  0xddac, 0xddad, 0x48bf, 0xddae, 0xddaf, 0xddb0, 0xe08c, 0xe08d,
  0xe08e, 0xfbf1, 0xe092, 0xe08f, 0xe252, 0xe253, 0xe254, 0xe255,
  0xe429, 0x52fd, 0xe5f3, 0xe5f4, 0xe80d, 0xe80e, 0xe80f, 0xe810,
  0xe914, 0xea71, 0xea72, 0xec0f, 0xec04, 0xec06, 0x52fd, 0xec07,
  0xeddc, 0xeddd, 0xee6d, 0xee79, 0xee6e, 0x52fd, 0xefec, 0xf0dd,
  0xf0de, 0xf0df, 0xf0e0, 0xf0e1, 0xf0e2, 0xf0e3, 0xf0e4, 0xf13a,
  0xf0e5, 0x4ccf, 0xf0e6, 0xf36b, 0xf36c, 0x974e, 0x4e64, 0xf5bd,
  0xf5be, 0xf5bf, 0xf634, 0x4fbf, 0xf812, 0x5740,
  /* 0x62 */
  0x5741, 0x6402, 0x6403, 0x6404, 0x6405, 0x6406, 0x67d7, 0x67cc,
  0x6998, 0x6c06, 0x6c07, 0x6caf, 0x6cb0, 0x6dea, 0x6df2, 0x72a2,
  0x72a3, 0x72a4, 0x7476, 0x75f5, 0x75f6, 0x75f7, 0x52fd, 0x7c3b,
  0x7c25, 0x7c3a, 0x7c3c, 0x7c3d, 0x7c3e, 0x8137, 0x8138, 0x8139,
  0x813a, 0x813b, 0x813c, 0x813d, 0x813e, 0x813f, 0x814b, 0x52fd,
  0x8140, 0x8322, 0x8367, 0x83d3, 0x85fa, 0x52fd, 0x8abc, 0x52fd,
  0x52fd, 0x8aa5, 0x0807, 0x8ab6, 0x52fd, 0x8aa6, 0x8abb, 0x8aa7,
  0x8aa8, 0x8aa9, 0x8aaa, 0x8aab, 0x8aac, 0x8abf, 0x8aad, 0x8aae,
  0x8aaf, 0x8c2e, 0x8e00, 0x939d, 0x939e, 0x93cb, 0x93cc, 0x93cd,
  0x93ce, 0x93cf, 0x52fd, 0x939f, 0x93d0, 0x93d1, 0x93d2, 0x93d3,
  0x93d4, 0x52fd, 0x52fd, 0x975d, 0x975e, 0x975f, 0x9760, 0x0b05,
  0x9dba, 0x9dbc, 0x0b06, 0x9db5, 0x9dbd, 0x9db6,
  /* 0x63 */
  0x9db7, 0x9db8, 0x9da8, 0xfb32, 0x9efe, 0x9efc, 0xa1e1, 0xa3c4,
  0xa3c5, 0xa614, 0xa8ae, 0xa8af, 0xa8b0, 0xa8b1, 0xa8b9, 0xabe7,
  0xb046, 0x52fd, 0xb047, 0xb048, 0xb049, 0xb01e, 0xb01f, 0xb04a,
  0xb04b, 0xb04c, 0xb04d, 0xb04e, 0x52fd, 0xb246, 0xb24b, 0xb490,
  0xb491, 0x52fd, 0xb492, 0xb493, 0xb494, 0xb55b, 0xb8f2, 0xb8f8,
  0xb8f9, 0xbb94, 0xbba4, 0xbb97, 0xbb98, 0xbc29, 0xbcae, 0xbdaf,
  0xc359, 0xc35a, 0x52fd, 0xc35b, 0xc35c, 0xc35d, 0xc35e, 0xc35f,
  0xc360, 0xc361, 0xc2aa, 0xc362, 0xc363, 0xc364, 0xc365, 0xc366,
  0xc367, 0xc368, 0xc369, 0xc36a, 0xc36b, 0xc36c, 0xc36d, 0xc36e,
  0xc36f, 0x52fd, 0xc7b2, 0xc7b3, 0xc7b4, 0x10f9, 0xc7b5, 0xc7b6,
  0xc7b7, 0xc7b8, 0xc7b9, 0xc7ba, 0xc7bb, 0xca88, 0x42b8, 0xce19,
  0xce1a, 0xce1b, 0xcf16, 0xd10c, 0xd10d, 0xd10e,
  /* 0x64 */
  0xd10f, 0x52fd, 0xd283, 0xd284, 0xd285, 0xd583, 0xd584, 0xd6b0,
  0x52fd, 0xd926, 0xd927, 0xd936, 0xd928, 0xd929, 0x52fd, 0x52fd,
  0xdde7, 0xdde8, 0xdde9, 0x1453, 0xddea, 0xddeb, 0x52fd, 0xddec,
  0xdded, 0xddee, 0xddef, 0xddf0, 0xddf1, 0xde0c, 0xddf2, 0xddf3,
  0xddf4, 0xddf5, 0xddf6, 0xddf7, 0xddf8, 0xe0a3, 0xe0a4, 0xe0a5,
  0xe0a6, 0xe0a7, 0x52fd, 0xe25c, 0x52fd, 0xe44b, 0xe4e1, 0xe615,
  0x52fd, 0xe616, 0xe617, 0xe82c, 0xe82d, 0xe922, 0xe923, 0xea96,
  0xea97, 0x52fd, 0xec2e, 0xec30, 0xec31, 0x52fd, 0xeddf, 0xee8b,
  0xee8c, 0x52fd, 0xee8d, 0xeffc, 0xeffd, 0xeffe, 0xefff, 0xf117,
  0xf12d, 0xf118, 0x4cf3, 0xf119, 0x52fd, 0xf112, 0xf11a, 0xf11b,
  0xf11c, 0x1669, 0x52fd, 0xf397, 0x16e5, 0xf398, 0xf399, 0x52fd,
  0xf6c4, 0x574f, 0x57cd, 0x5dc7, 0x641c, 0x641d,
  /* 0x65 */
  0x641e, 0x67e7, 0x67e9, 0x67ea, 0x67eb, 0x6c15, 0x6ff1, 0x6ff2,
  0x72b6, 0x78a0, 0x7c4a, 0x7d20, 0x8160, 0x8161, 0x8141, 0x8162,
  0x8603, 0x8604, 0x8ad9, 0x8ada, 0x8adb, 0x8adc, 0x8add, 0x52fd,
  0x8ade, 0x8adf, 0x8ae0, 0x8ae1, 0x8ae2, 0x0812, 0x8ae3, 0x52fd,
  0x8f31, 0x9405, 0x9406, 0x9407, 0x9772, 0x978a, 0x978b, 0x9788,
  0x52fd, 0x978c, 0x978d, 0x978e, 0x978f, 0x9790, 0x9842, 0x9843,
  0x52fd, 0x9dc1, 0x9dc2, 0x9dc3, 0x9dc4, 0xa3cb, 0xa622, 0x52fd,
  0x52fd, 0xa8c3, 0xa8c4, 0xa8c5, 0xa8c6, 0xaa1e, 0xaa25, 0xabf3,
  0xabf4, 0x52fd, 0xabf5, 0xad38, 0xad48, 0xadca, 0x3b4f, 0xb07a,
  0xb07b, 0xb255, 0xb256, 0xb4b1, 0xb4b2, 0xb4b3, 0x52fd, 0xb4b4,
  0xb4b5, 0xb7a4, 0xb900, 0xb901, 0xb902, 0xbbac, 0x52fd, 0xbcb3,
  0xc3b6, 0xc3b7, 0xc3b8, 0xc3b9, 0xc3ba, 0xc3bb,
  /* 0x66 */
  0xc3bc, 0xc3bd, 0xc3be, 0xc3bf, 0xc3c0, 0xc3c1, 0xc3c2, 0xc3c3,
  0xc3c4, 0xc3c5, 0xc3c6, 0xc3c7, 0xc7e3, 0xc7e4, 0xc7e5, 0xc7e6,
  0xc7e7, 0x52fd, 0xcab5, 0xcaa1, 0xcb95, 0xce3b, 0xce3c, 0xce3d,
  0xce3e, 0xcf14, 0xd120, 0xd121, 0xd122, 0xd123, 0xd124, 0xd296,
  0xd297, 0xd298, 0xd494, 0xd495, 0xd496, 0xd497, 0xd498, 0xd58f,
  0xd590, 0xd6bc, 0xd73b, 0xd93d, 0x52fd, 0xdbdf, 0xdc1e, 0xde23,
  0xde24, 0xde49, 0xde25, 0xde26, 0xde27, 0xde28, 0x4925, 0xde29,
  0xde2a, 0xde2e, 0xde2f, 0xde4e, 0xde30, 0xfbeb, 0xde31, 0xde32,
  0xde33, 0xde34, 0xde35, 0xde36, 0x52fd, 0xde37, 0xde50, 0xde38,
  0x52fd, 0xde39, 0xde3a, 0xde3b, 0xde3c, 0xde3d, 0xde3e, 0xe0b8,
  0xe0b9, 0xe262, 0xe263, 0xe341, 0xe46d, 0x52fd, 0xe652, 0xe637,
  0xe6db, 0xe83f, 0xe934, 0xeb5b, 0xec46, 0xec5a,
  /* 0x67 */
  0xec5b, 0xede1, 0xede2, 0xede3, 0xede4, 0xeea7, 0xeea8, 0xeea9,
  0xf00f, 0xf02b, 0xf156, 0xf157, 0xf158, 0xf15e, 0xf159, 0xf15a,
  0xf15b, 0xf15c, 0x52fd, 0xf168, 0x52fd, 0xf42c, 0xf3e2, 0xf3f3,
  0xf5d2, 0xf656, 0xf6ce, 0xf761, 0xf99b, 0xf9b4, 0x575c, 0xfa10,
  0x57d2, 0x5871, 0x5973, 0x5b2c, 0x5e94, 0x52fd, 0x643f, 0x6440,
  0x67f9, 0x67fa, 0x67fd, 0x67fb, 0x6c1c, 0x6c1d, 0x6dff, 0x6e00,
  0x6ff7, 0x9ffe, 0x52fd, 0x77b6, 0x7c54, 0x8177, 0x8179, 0x817a,
  0x817b, 0x8369, 0x8b02, 0x8b03, 0x8b04, 0x8b05, 0x8b06, 0x8b07,
  0x8b08, 0x8b09, 0x8b0a, 0x8b0b, 0x8b35, 0x52fd, 0x8f39, 0x941e,
  0x941f, 0x9420, 0x9421, 0x52fd, 0x9422, 0x9423, 0x9424, 0x942c,
  0x97b3, 0x97b5, 0x97b6, 0x97b7, 0x97b8, 0x9dd0, 0x9ffa, 0xa207,
  0xa2bd, 0xa3d7, 0xa3d2, 0xa8db, 0xa8d6, 0x575d,
  /* 0x68 */
  0xad43, 0xadcf, 0xb0a3, 0xb0a4, 0xb0a5, 0xb0a6, 0xb0a7, 0xb0a8,
  0xb0a9, 0xb25d, 0xb25e, 0xb4c9, 0xb4ca, 0xb4cb, 0xb4cc, 0xb906,
  0xb907, 0x52fd, 0xbbc5, 0xbbf5, 0xbcb4, 0xbcb8, 0xbcb5, 0xc3fd,
  0x1077, 0xc3fe, 0xc3ff, 0xc400, 0xc401, 0xc402, 0xc403, 0xc409,
  0xc404, 0xc405, 0xc406, 0xc407, 0xc811, 0xc812, 0xc813, 0xc814,
  0xc815, 0x4277, 0xcb98, 0xcb99, 0xd13c, 0xd12d, 0xd12e, 0x52fd,
  0xd4bb, 0xd4bc, 0x1378, 0xd73d, 0xd947, 0xd948, 0xd949, 0xd94a,
  0xdae1, 0xdae7, 0xdbeb, 0xdc20, 0xdc38, 0xde62, 0xde67, 0xde68,
  0xde69, 0xde6a, 0xde6b, 0xde6d, 0xde6e, 0xde6f, 0xde70, 0xde71,
  0xde72, 0xde8f, 0xde73, 0xde74, 0xde75, 0xde76, 0xde77, 0xe0c9,
  0xe0ca, 0x4ae6, 0xe0cb, 0xe0cc, 0xe0cd, 0xe351, 0x52fd, 0x52fd,
  0xe545, 0xe859, 0xe85a, 0xe85b, 0xe940, 0xeacf,
  /* 0x69 */
  0xeb5e, 0xec7b, 0xed93, 0xeebc, 0xf016, 0xf030, 0x52fd, 0xf18d,
  0xf18e, 0x4d2f, 0xf18f, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194,
  0xf195, 0xf196, 0xf423, 0xf414, 0xf5db, 0xf70a, 0xf8ce, 0x5766,
  0x5768, 0x5767, 0x57d3, 0x6463, 0x6804, 0x699e, 0x6c26, 0x6c2a,
  0x7c6c, 0x7c64, 0x818f, 0x8190, 0x8334, 0x860e, 0x8b25, 0x8b26,
  0x8b27, 0x52fd, 0x8b28, 0x2c0c, 0x52fd, 0x8b29, 0x8b2a, 0x8b2b,
  0x8b2c, 0x8f3e, 0x9442, 0x9443, 0x9433, 0x97c1, 0x97c2, 0x97c3,
  0x9dd6, 0x9dd7, 0xa8e1, 0xa8e2, 0xaa2b, 0xadd1, 0xb0cc, 0xb0cd,
  0xb0ce, 0xb0cf, 0xb4de, 0xb4df, 0xb4e0, 0xb4e1, 0xbbf6, 0xc42c,
  0x52fd, 0xc454, 0xc42d, 0xc42e, 0xc841, 0xc842, 0xcac7, 0xcba0,
  0xcba1, 0xce70, 0xce71, 0xce72, 0x52fd, 0xd134, 0xd135, 0xd136,
  0xd2a6, 0xd4cc, 0x52fd, 0xd599, 0xdbf4, 0xde9b,
  /* 0x6a */
  0xde9e, 0xde9f, 0xdea0, 0xdea1, 0xdea2, 0xdea3, 0xdea4, 0xdea5,
  0xdea6, 0xdea7, 0xdea8, 0xe0db, 0xe0dc, 0xe0dd, 0xe0d7, 0xe0de,
  0xe0df, 0xe359, 0xe746, 0xe865, 0xe949, 0x52fd, 0xeadd, 0xec95,
  0xec96, 0xec97, 0xeed4, 0xeed6, 0xf026, 0x4d47, 0x1688, 0xf1c8,
  0xf1c5, 0xf1c6, 0xf1c7, 0xf451, 0xf452, 0xf453, 0xf454, 0xf455,
  0xf5de, 0x52fd, 0x576d, 0x57d5, 0x6472, 0x74ae, 0x760f, 0x7c6b,
  0x7c6d, 0x7c6e, 0x7c78, 0x8198, 0x8199, 0x819a, 0x8b44, 0x8b5c,
  0x8b45, 0x8e01, 0x9458, 0x9459, 0x945a, 0x52fd, 0x97d9, 0x97d6,
  0x9dda, 0x9ddf, 0x9ddb, 0xa003, 0xa8ef, 0x3836, 0xaa33, 0xadd2,
  0xb0e1, 0xb26f, 0xb564, 0xb90e, 0xc450, 0xc451, 0xc452, 0xc856,
  0xc857, 0xc858, 0x52fd, 0xce81, 0xce82, 0xcf1d, 0xcf1e, 0xd143,
  0xd2aa, 0xdbfd, 0x52fd, 0xdec6, 0xdec7, 0xded9,
  /* 0x6b */
  0xded7, 0x52fd, 0xdec8, 0xdec9, 0xdeca, 0xdecb, 0xdeda, 0x52fd,
  0xdecc, 0xdecd, 0xdece, 0xdecf, 0xded0, 0xded1, 0xdebc, 0xe0e1,
  0xe0e2, 0xe0e3, 0xe4a9, 0xe54e, 0xe877, 0x52fd, 0x52fd, 0xecac,
  0xecad, 0xedb6, 0xeee5, 0xf1f8, 0xf1ea, 0xf28d, 0xf1eb, 0xf495,
  0x576e, 0x57d8, 0x6483, 0x69a3, 0x6c30, 0x6e0f, 0x7c79, 0x7c7a,
  0x81a7, 0x81a8, 0x81a9, 0x81aa, 0x8619, 0x8b50, 0x8b51, 0x8b52,
  0x2c15, 0x8b68, 0x8b53, 0x8b54, 0x9be2, 0x9ddc, 0xac15, 0xac16,
  0xad4f, 0xb0f5, 0xb0f6, 0xb0f7, 0xb4f4, 0xb7cc, 0xb90f, 0xc466,
  0xc467, 0x52fd, 0xc468, 0xcba4, 0xce8b, 0xce8c, 0xd4fa, 0xdee6,
  0xdeed, 0xdeee, 0xdeef, 0xdef0, 0xdefe, 0xe0e7, 0xe0e8, 0xe4b3,
  0xe4e4, 0xe883, 0xedec, 0x52fd, 0xf216, 0x52fd, 0x52fd, 0xf217,
  0xf4c3, 0xf4ac, 0xf5ed, 0x52fd, 0x52fd, 0xf8b4,
  /* 0x6c */
  0x52fd, 0x648a, 0x648b, 0x6c34, 0x6e14, 0x72df, 0x77bb, 0x7c7f,
  0x7c81, 0x81ad, 0x81ae, 0x8b62, 0x8b6e, 0x52fd, 0x946e, 0x52fd,
  0x9a17, 0x9de6, 0x9f10, 0xac19, 0x52fd, 0xb910, 0xbdf2, 0xc476,
  0xc477, 0xce95, 0xce96, 0xdf00, 0xdf01, 0xdf31, 0xfbed, 0xdf1c,
  0xe36d, 0xeef4, 0xeef5, 0xf03d, 0xf232, 0xf233, 0xf5f4, 0x52fd,
  0x5773, 0x6c37, 0x52fd, 0x8b71, 0xb10f, 0x52fd, 0xb10e, 0xb911,
  0xc485, 0xdf14, 0xdf15, 0xdf16, 0xdf17, 0xdf18, 0xdf19, 0xdf1a,
  0xe4c3, 0xf242, 0xf243, 0xf244, 0xf248, 0xf4fb, 0x69a5, 0x6cb8,
  0x81b2, 0x833d, 0x8b72, 0x97e7, 0x97e8, 0xa659, 0xaa3a, 0xb114,
  0xb4ff, 0xbcc0, 0xc48d, 0x1084, 0xc48e, 0x52fd, 0xdf22, 0x52fd,
  0x52fd, 0xef04, 0xef29, 0xf251, 0xf266, 0xf50e, 0xfc13, 0x52fd,
  0x649b, 0x649c, 0x7616, 0x7c86, 0x7c87, 0x52fd,
  /* 0x6d */
  0xdf2c, 0xdf30, 0xdf28, 0xe95f, 0x52fd, 0x947e, 0xb119, 0xd50a,
  0xdf36, 0xe0fc, 0xf26d, 0xf26e, 0xf9c6, 0x1ab5, 0x8b7b, 0xb506,
  0xc89f, 0xf532, 0xd50d, 0x7c8b, 0xdf3b, 0x52fd, 0xf53d, 0xdf3c,
  0xdf3d,
};

static const ucs4_t cns11643_15_2uni_upages[253] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04300, 0x04400,
  0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04c00, 0x04f00,
  0x05100, 0x05200, 0x05300, 0x05400, 0x05500, 0x05600, 0x05700, 0x05800,
  0x05a00, 0x05b00, 0x05c00, 0x05e00, 0x05f00, 0x06200, 0x06500, 0x06600,
  0x06700, 0x06800, 0x06900, 0x06a00, 0x06b00, 0x06c00, 0x06e00, 0x06f00,
  0x07000, 0x07100, 0x07300, 0x07400, 0x07500, 0x07600, 0x07700, 0x07800,
  0x07900, 0x07a00, 0x07b00, 0x07c00, 0x07d00, 0x07f00, 0x08200, 0x08300,
  0x08600, 0x08700, 0x08900, 0x08a00, 0x08e00, 0x08f00, 0x09000, 0x09200,
  0x09300, 0x09400, 0x09500, 0x09600, 0x09b00, 0x09c00, 0x09d00, 0x09e00,
  0x09f00, 0x0fa00, 0x0ff00, 0x20000, 0x20100, 0x20200, 0x20300, 0x20400,
  0x20500, 0x20600, 0x20700, 0x20800, 0x20900, 0x20a00, 0x20b00, 0x20c00,
  0x20d00, 0x20e00, 0x20f00, 0x21000, 0x21100, 0x21200, 0x21300, 0x21400,
  0x21500, 0x21600, 0x21700, 0x21800, 0x21900, 0x21a00, 0x21b00, 0x21c00,
  0x21d00, 0x21e00, 0x21f00, 0x22000, 0x22100, 0x22200, 0x22300, 0x22400,
  0x22500, 0x22600, 0x22700, 0x22800, 0x22900, 0x22a00, 0x22b00, 0x22c00,
  0x22d00, 0x22e00, 0x22f00, 0x23000, 0x23100, 0x23200, 0x23300, 0x23400,
  0x23500, 0x23600, 0x23700, 0x23800, 0x23900, 0x23a00, 0x23b00, 0x23c00,
  0x23d00, 0x23e00, 0x23f00, 0x24000, 0x24100, 0x24200, 0x24300, 0x24400,
  0x24500, 0x24600, 0x24700, 0x24800, 0x24900, 0x24a00, 0x24b00, 0x24c00,
  0x24d00, 0x24e00, 0x24f00, 0x25000, 0x25100, 0x25200, 0x25300, 0x25400,
  0x25500, 0x25600, 0x25700, 0x25800, 0x25900, 0x25a00, 0x25b00, 0x25c00,
  0x25d00, 0x25e00, 0x25f00, 0x26000, 0x26100, 0x26200, 0x26300, 0x26400,
  0x26500, 0x26600, 0x26700, 0x26800, 0x26900, 0x26a00, 0x26b00, 0x26c00,
  0x26d00, 0x26e00, 0x26f00, 0x27000, 0x27100, 0x27200, 0x27300, 0x27400,
  0x27500, 0x27600, 0x27700, 0x27800, 0x27900, 0x27a00, 0x27b00, 0x27c00,
  0x27d00, 0x27e00, 0x27f00, 0x28000, 0x28100, 0x28200, 0x28300, 0x28400,
  0x28500, 0x28600, 0x28700, 0x28800, 0x28900, 0x28a00, 0x28b00, 0x28c00,
  0x28d00, 0x28e00, 0x28f00, 0x29000, 0x29100, 0x29200, 0x29300, 0x29400,
  0x29500, 0x29600, 0x29700, 0x29800, 0x29900, 0x29a00, 0x29b00, 0x29c00,
  0x29d00, 0x29e00, 0x29f00, 0x2a000, 0x2a100, 0x2a200, 0x2a300, 0x2a400,
  0x2a500, 0x2a600, 0x2f800, 0x2f900, 0x2fa00,
};

static int
cns11643_15_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x6d)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        {
          if (i < 7169)
            swc = cns11643_15_2uni_page21[i],
            wc = cns11643_15_2uni_upages[swc>>8] | (swc & 0xff);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

