This file contains the history of changes in the SOCI library.

Latest version is v4.0.3 released on 2022-02-14.

---
Version 4.0.3 differs from 4.0.2 in the following ways:

Changes affecting all or multiple backends:
 - Fix opening sessions from pool (#891).
 - Fix default backend search path (#928).
 - Fix build with latest glibc versions where SIGSTKSZ is not constant (#886).
 - Document using SOCI as a CMake subdirectory (#925).
 - Document using SOCI with <PERSON> (#877).

Backend-specific changes:

- MySQL
 - Implement get_table_names() for MySQL (#927).
 - Fix MySQL backend build with MySQL >= 8 (#884).
 - Fix mysql_library_end() in multithreaded environment (#909).
 - Make mysql_soci_error::get_error_category() more useful (#902).

- ODBC
 - Fix several bugs in vector into code (#888, #892).
 - Fix memory leaks in case of SQLNumResultCols() failure.
 - Export odbc_session_backend::get_database_product() from DLL.

- Oracle
 - Add support for detecting Oracle 20 and 21 to <PERSON><PERSON>ake (#896).

- PostgreSQL
 - Fix compilation when "free" is redefined somewhere else.

- SQLite3
 - Fix bulk operations with vectors of size 1 in SQLite3 backend (#908).

- Firebird
 - Suppress FB memory leaks


---
Version 4.0.2 differs from 4.0.1 in the following ways:

Changes affecting all or multiple backends:

 - Fix using SOCI from CMake projects using find_package() (#854, #867, #868).
 - Always set indicator to i_null when no data is read from the database (#28).
 - Add support for building SOCI with LTO (#846).
 - Enable ELF visibility support (#848).
 - Fix building tests under macOS on ARM 64 (#852).
 - Fix using custom types with an explicitly null indicator (#807).
 - Fix using vectors of custom types for DB/2, ODBC and Oracle (#856).
 - Fix vectors of XML and CLOB values for DB/2, Firebird, ODBC and Oracle (#869).
 - Don't return i_truncated for empty strings in a vector for DB/2, ODBC.
 - Make unloading dynamic backends safer by delaying it if necessary (#862).
 - Migrate most Linux CI builds to GitHub Actions from Travis CI (#863, #865).
 - Fix rare deadlock in session::reconnect() (#630).
 - Fix all warnings in MSVS build (#858).

Backend-specific changes:

- MySQL
 - Add "reconnect" option (#692).

- ODBC
 - Don't show interactive ODBC dialogs when reconnecting.
 - Fix session::get_last_insert_id() for empty tables with SQL Server.

- Oracle
 - Implement session::get_next_sequence_value().
 - Fix using default NUMBER type with rowset API (#872).
 - Handle reading from CLOBs that can't be read all at once.
 - Fix another memory leak in CLOB handling code.

- PostgreSQL
 - Fix use of quoted identifiers with colons (#840).
 - Fix tests compilation under macOS (#841, #864).
 - Fix tests with PostgreSQL 12 or later.

- SQLite3
 - Fix getting doubles from non-numeric columns.
 - Fix using uninitialized sqlite3_column member (#677).


---
Version 4.0.1 differs from 4.0.0 in the following ways:

- Add session::is_connected() (#828).
- Fix DST adjustment when reading dates from the database (#723, #799).
- Make dynamic_backends::search_paths() actually available (#829).
- Fix using std::vector<boost::optional<>> in query arguments (#797).
- Allow using dt_blob and dt_xml with rowset API too (#786).
- Fix or work around many compilation warnings with newer compilers.

- DB2
 - Fix memory leak.

- ODBC
 - Fix bug with handling of strings of exactly ODBC_MAX_COL_SIZE length (#819).
 - Fix opening connections when using connection pooling.

- Oracle
 - Improve detection for newer Oracle versions (#788).
 - Use bigger buffer for Oracle connection string length (#666).
 - Fix memory leak in Oracle backend when using CLOBs (XML or long strings).

- PostgreSQL
 - Fix reconnect() to use the correct connection parameters (#793).
 - Improve error message returned on connection loss (#828).
 - Fix use of uninitialized connection variable on connection failure (#822).
 - Fix inadvertently broken use of single row mode (#571).
 - Fix handling of identifiers with colons (#782).

- SQLite
 - Add support for "vfs" and "readonly" connection options (#816, #784).
 - Fix std::tm handling (#770).
 - Fix "big int" detection (#783, #785).

---
Version 4.0.0 differs from 3.2.3 in the following ways:

NOTICE: This is probably the last release compatible with C++98.

- Added support for C++11 and C++17 compilation modes.
- Added RELEASING.md how-to and scripts/release.sh helper for release managers.
- Added context of the failure in soci_error::what() which now returns a
  longer and more useful message. Use the new get_error_message() method to get
  just the brief error message which used to be returned by what().
- Added logger class to allow customizing SOCI logging operations (#245).
- Added helper for generating portable DDL and DML statements (#484).
- Added portable column info and other metadata queries (#480).
- Added helper exchange_type_cast<>() template function as better static_cast (#301).
- Added values::get_number_of_columns() as convenient accessor.
- Added public macro SOCI_NORETURN and use it in declaration.
- Added handling of dt_unsigned_long_long to the simple interface.
- Added new data type dt_blob with accompanying simple-interface support (#361).
- Added basic support for error categories.
- Added failover_callback interface (#486).
- Added bulk iterators interface (#487).
- Added basic package exporting to CMake configuration (#503).
- Added bigstring (XML and CLOB) support (#509).
- Added CMake option SOCI_POSTGRESQL_NOSINLGEROWMODE with default value OFF (#594).
- Adopted new layout of the source tree (#151).
- Although the build configuration is based on CMake 2.8,
  numerous improvements have been applied to the CMake scripts.
- Converted all tests to use the Catch framework.
- Fixed issues with throwing from soci_error copy constructor and assignment operator (#302).
- Fixed backends loading in case SOCI is built with CMAKE_DEBUG_POSTFIX set (#368).
- Fixed deadlock in soci::connection_pool::try_lease function.
- Fixed numerous compilation warnings using various compilers.
- Fixed non-copyability of connection_pool (#478).
- Fixed uniform offset for BLOB read/write operations (#508).
- Fixed memory leak when reusing into and use elements.
- Fixed deduction of reference in boost::fusion::for_each (#728).
- Added empty_blob() and nvl() to portable utilities.
- Improved handling and conversions of time and timezone information values (#316).
- Improved diagnostics with included query parameter names in error messages (#318).
- Improve CMake configuration for integrating SOCI as subproject (#380).
- Improved locale-independent conversions of floating-point numbers to string.
- Include all public headers using soci/ prefix inside SOCI itself (#239).
- Migrated documentation content from HTML to Markdown (#344).
- Provided error context in exceptions and richer diagnostics (#302).
- Replaced assertion with raising exception in case of connection_pool::lease() failure.
- Remove use of std::unary_function and std::ptr_fun deprecated in C++11/17 (#650, #751).
- Split statement::clean_up into two operations bind_clean_up and clean_up (#358).
- Updated the backend documentation.
- Use 64-bit integer for next sequence and last insert ID values (#720).

- DB2
-- Fixed ambiguous error handling during statement execution (#431).
-- Fixed handling of NULL for strings during bulk querying (#581).
-- Replaced SQLConnect with SQLDriverConnect to establish database session (#438).

- Firebird
-- Added SOCI_FIREBIRD_EMBEDDED option to allow building with embedded library.
-- Added possibility to build the backend using embedded library (libfbembed).
-- Added CLOB and XML support (#578).
-- Fixed too eager start of implicit transaction (#292).
-- Fixed bug with writing BLOB values (#524).
-- Replaced truncation of too long VARCHAR columns values with throwing exception.

- MySQL
-- Added MySQL 8 to tested versions.
-- Added get_last_insert_id function (#216).
-- Added timeout support (#691).
-- Fixed bug whe nusing get_affected_rows() and user defined types (#221).
-- Replace throwing generic soci_error with mysql_soci_error (#613).

- ODBC
-- Added support for ODBC driver for DB2 which is not compliant to ODBC spec (#663).
-- Fixed inserting strings of length greater than 8000 bytes into database (#383, #681).
-- Fixed get_affected_rows() when using FreeTDS driver.
-- Fixed reading from unallocated memory (driver bug?) in ODBC with MySQL (#555).
-- Fixed handling of NULL for strings during bulk querying (#581).
-- Fixed memory leak of internal odbc_standard_use_type_backend buffer (#627).

- Oracle
-- Added oraocci12 name to Oracle client look-up by CMake.
-- Added NLS support for connection parameters.
-- Added Oracle wallet authentication.
-- Added (partial) handling of OCI_SUCCESS_WITH_INFO.
-- Added handling of more error codes for error categories.
-- Added failover_callback interface implementation (#486).
-- Added bulk iterators interface implementation (#487).
-- Added bigstring (XML and CLOB) support (#509).
-- Added lazy initialization of the temporary LOB objects for Oracle.
-- Fixed uniform offset for BLOB read/write operations (#508).
-- Fixed connection parameters parsing to allow spaces in values (#213).
-- Fixed handling of BINARY_DOUBLE in dynamic row.
-- Use SQLT_BDOUBLE for floating point values instead of SQLT_FLT.

- PostgreSQL
-- Added singlerows mode for PostgreSQL (#482).
-- Added failover_callback interface implementation (#486).
-- Added bulk iterators interface implementation (#487).
-- Added test for the uuid data type (#420).
-- Added bigstring (XML and CLOB) support (#509).
-- Dropped support for PostgreSQL 7.x (#623).
-- Fixed defining SOCI_POSTGRESQL_NOSINLGEROWMODE for PostgreSQL < 9 (#571).
-- Fixed string to floating-point number conversions assuming "C" locale (#238).
-- Fixed support for bytea across PostgreSQL versions older than 9 (#242).
-- Fixed timestamp handling in UTC (#190).
-- Fixed uniform offset for BLOB read/write operations (#508).
-- Explicitly set extra_float_digits to 3 when using PostgreSQL >=9 in ODBC for consistency.
-- Improve string to floating-point number conversion to be exact.

- SQLite3
-- Added get_last_insert_id function (#216).
-- Fixed clean up on error to enable getting detailed diagnostics (#318).
-- Fixed issue numerous calls to fetch by better control when backend calls sqlite3_step (#375).
-- Fixed memory leak in sqlite3_session_backend (#378).
-- Fixed closing connection after obtaining error diagnostics (#381).
-- Fixed affected rows count when reusing prepared statements (#428).

---
Version 3.2.3 differs from 3.2.2 in the following ways:

- Improved Boost Tuple & Fusion integration by using boost::fusion::foreach
  to reference each member of a sequence. Breaks compatibility with Boost 1.35 (2008).
- Fixed linker error when building 64-bit target with Visual Studio.
- Fixed several issues with building using Cygwin and MinGW.
- Clarified documentation and examples on bulk operations.

- MySQL
-- Fixed building against MySQL 3.23.

- ODBC
- Improve readability of ODBC error messages.
- Fixed CMake configuration of ODBC backend for Visual Studio 64-bit targets.

- Oracle
-- We've had to disable Oracle target in the Travis CI configuration until we
   figure out how to setup Oracle on Travis CI directly.
   Therefore, this release hasn't been extensively tested against Oracle.

- PostgreSQL
-- Added support for UUID column type (tests and docs updated).

- SQLite3
-- Added sqlite3_soci_error exception as subclass of soci_error to provide useful
   exposure of specific SQLite3 error codes (tests and docs updated).

---
Version 3.2.2 differs from 3.2.1 in the following ways:

- Fixed once_temp_type destructor with noexcept(false) specifier for C++11
- Fix uninitialized indicators in conversion_into_type and conversion_use_type specialisations
- Fixed placeholder matching for PostgreSQL-style casts with ORM
- Fixed memory leaking in use binding in case of bind/unbind sequence
- Fixed sscanf formatter for MinGW/MSVC in backends
- Fixed partial placeholder name matching for ORM cases
- Added test for use of indicators with rowset<row>
- Added test for get_affected_rows after bulk operations

- DB2
-- Enable build and testing on Travis CI
-- Fixed use type size calculation during bind/pre_use

- Firebird
-- Enable memory cleanup to avoid leaks after binding
-- Added missing C++ library headers
-- Added get_affected_rows() support for bulk operations
-- Fixed compilation with VS2008
-- Fixed building tests with the backend built as DLL.

- MySQL
-- Added get_affected_rows() support for bulk operations

- ODBC
-- Always call ASCII version of ODBC function SQLGetDiagRec
-- Fixed invalid condition test in assertions
-- Added get_affected_rows() support for bulk operations
-- Added support for (unsigned) long long vectors
-- Changed mapping of SQL_BIGINT to dt_long_long in ODBC backend
-- Added some trivial optimizations in statement preparation code

- Oracle
-- Fixed missing noData_ reported in particular case when OCIStmtExecute returns success
-- Improved Oracle testing setup on Travis CI

- PostgreSQL
-- Added session::get_next_sequence_value() for PostgreSQL backend
-- Added get_affected_rows() support for bulk operations
-- Applied RAII and simplified error handling logic
-- Fixed issue with exceptions escaping postgresql_statement_backend destructor

- SQLite3
-- Replaced sqlite3_prepare with sqlite3_prepare_v2
-- Added missing <cstdarg> header
-- Fixed wrong size used in memcpy() during bulk processing
-- Added get_affected_rows() support for bulk operations
-- Added shared_cache=true propery to control SQLITE_OPEN_SHAREDCACHE flag

- Documentation
-- Corrected usage examples

- Building
-- Updated CMake for Oracle 12g
-- Restructured and improved Travis CI setup to do single build per backend
-- Fixed wrong GCC_VERSION used in CMake config for commandline-overriden GCC
-- Allows CMake to find 32-bit DB2 on Windows x64
-- Removed redundant Oracle libclntsh library lookup
-- Fixed SQLITE3_LIBRARIES handling by find_package_handle_standard_args

---
Version 3.2.1 differs from 3.2.0 in the following ways:

- Fixed binding of Oracle type NUMBER(9,0) to C++ int, by Tomasz Olszewski
- Fixed Oracle client library detection on Windows, by Tomasz Olszewski
- Fixed PostgreSQL issue with deallocate_prepared_statement called for non-existing statement
- Fixed prepared insert statements with custom data types support, by Tomasz Olszewski
- Fixed recent improvements in x:=y name binding, by Poul Bondo
- Fixed query transformation assignment in pooled sessions, by Stefan Chrobot
- Cleaned up SOCI_POSTGRESQL_NOPARAMS and related options in code and documentation
- Dropped patch (micro) version number from documentation URLs on the website

---
Version 3.2.0 differs from 3.1.0 in the following ways:

- SOCI is now organization at GitHub
-- Git repository moved to https://github.com/SOCI/soci
-- Opened new bug tracker (SF.net tracker is read-only)
-- Opened Wiki for FAQ and development articles
-- Website, mailing lists and downloads remain on SourceForge.net
-- Applied GitFlow branching model
-- Added package maintenance repository https://github.com/SOCI/soci-pkg

- Core
-- Added connection_parameters to enable support for session options used by core, backends or low-level API.
-- Added user-defined query transformation support based on function, functor and lambda
-- Fixed missing connection check and backend setup ensured for session
-- Fixed backend initialization when used with connection pool (Core)
-- Fixed memory leaks on exception thrown during statement preparation stage

- DB2
-- Added new backend for IBM DB2

- Firebird
-- Fixed bug in repeated statement execution
-- Fixed issues with input parameter binding
-- Fixed connection string parsing to handle possible white-spaces
-- Fixed issues with C++ type unsigned long
-- Fixed reading negative values into C++ type double
-- Added option to fetch numeric/decimal data into string of characters
-- Added CMake configuration
-- Updated tests
-- Continued regular testing on Windows and Unix platforms

- MySQL
-- Replaced use of type=InnoDB with engine=InnoDB
-- Improved CMake configuration

- ODBC
-- Added connection_parameters option ODBC_OPTION_DRIVER_COMPLETE
-- Fixed issues in handling C++ types int and long on 64-bit architectures
-- Added missing CMake configuration for tests
-- Continued regular testing on Windows and Unix platforms

- Oracle
-- Implemented statement::get_affected_rows() operation
-- Use OCI_THREADED and OCI_ENV_NO_MUTEX with OCIEnvCreate
-- Added numerous fixes and improvements in tests
-- Added option to fetch numeric/decimal data into string of characters
-- Fixed issues in binding procedure IN/OUT parameters

- PostgreSQL
-- Add reading of BYTEA data into std::string (not fully-featured binary data support yet)
-- Add JSON data type support available in PostgreSQL 9.2+
-- Fixed incorrect assertion in postgresql::get_error_details
-- Fixed premature deallocation of prepared statements
-- Fixed servers-side memory leak in prepared statements
-- Fixed memory leak of PGresult on exception thrown
-- Fixed isues in parsing complex PL/PSQL functions

- SQLite3
-- Implemented statement::get_affected_rows() operation
-- Fixed handling of database file path with spaces

- Building, testing and installation
-- Marked Makefile.basic as deprecated (maintainer wanted)
-- Cleaned numerous compilation warnings reported by various compilers
-- Improved compilation using latest version of clang
-- Added numerous improvements in CMake configuration
-- Added SOCI_STATIC option to enable/disable static libraries build
-- Fixed issues with ignored -DWITH_<dependency> options
-- Fixed FindMySQL.cmake to allow use of mysqlclient from custom location
-- Added support of SQLITE_ROOT_DIR variable to FindSQLite3.cmake module
-- Fixed and tested CMake configuration on OSX
-- Fixed and tested CMake configuration on FreeBSD
-- Fixed and tested CMake configuration to use with Ninja
-- Fixed building using Visual Studio 2010
-- Added support for building using Visual Studio 2012
-- Set up SOCI continuous integration at travis-ci.org
-- Added Debian packaging support to soci-pkg repository
-- Added Fedora/CentOS/RedHat packaging support to soci-pkg repository

- Documentation
-- Review and update to catch up with current status quo
-- Updated code examples
-- Start maintenance and hosting of SOCI documentation per released version

---
Version 3.1.0 differs from 3.0.0 in the following ways:

- Added Ada language binding

- Migraded build system from GNU Autotools and Visual Studio projects to CMake

- CMake build tested with Visual Studio, GCC and clang

- Incorporated a compromise for naming versioned shared libraries

- Enhanced and improved integration with Boost libraries:
-- Boost.DateTime
-- Boost.Fusion
-- Boost.Optional
-- Boost.Tuple

- Bug fixes and improvements in core and backends:
-- Added soci::values::get_properties accessor useful for composing soci::type_conversion
-- Export advanced API of backend loader from DLL.
-- Added static factory registration functions for backends
-- Added get_affected_rows operation
-- Fixed thread-safety of connection pool under Windows
-- Fixed bug with droping const qualifiers when binding to std::vector<soci::indicator>
-- Fixed bug in default initialization of an object of const backend_factory wihch requires user-provided default constructor (see C++03/C++0x)
-- Fixes for 64-bit builds
-- Removed redundant exchange_traits breaking ODR on LP64
-- Better ODBC support
-- Type conversion support for unsigned integer types
-- Bug ID:1971436 - incorrect rowset copy operations
-- Bug ID:2010367 - memory leak (ODBC)
-- Bug ID:2010409 - invalid memory allocaton in define by position (ODBC)
-- Bug ID:2021243 - long long type support in Visual C++
-- Patch ID:2483066 - 64bit Linux and 64bit integer submitted
-- Patch ID:2809809 - Fix build with GCC 4.4
-- Patch ID:2809810 - Fix SQLite3 build with GCC 4.3
-- Patch ID:2581206 - Windows unicode application
-- Patch ID:3058275 - install target for cmake build submitted
-- Patch ID:3069375 - use $(prefix)/lib64 on AMD64 platforms.
-- Improved performance while accessing query results (MySQL)
-- Bug fixes for PROCEDURE support (MySQL)
-- Removed throw statements from mysql_rowid_backend and mysql_blob_backend destructors (MySQL)
-- Verify that prepared statements survive session::reconnect() operatoin (MySQL)
-- Improved support for time and date (MySQL, PostgreSQL)
-- Fixed bug with strings of length exceeding 255 characters (ODBC)
-- Improved interpretation of the connect string (Oracle)
-- Added handling of unsigned long long (Oracle, SQLite3, PostgreSQL)
-- Fixes in integral types support (PostgreSQL)
-- Support for colon-casts (PostgreSQL)
-- Added possibility for use BLOB (PostgreSQL)
-- Added support for connection property "synchronous=on|off" (SQLite3)
-- Improved BLOB data handling (SQLite3)
-- Improved boolean type suppport (SQLite3)
-- Session timeout support (SQLite3)
-- Improved tests clean-up (SQLite3)
-- Added missing typedef of sqlite3_destructor_type which has been defined in sqlite3.h but since 3.3.10 (see comment for reference to SQLite ticket)

- Updated tests for various backends and SQL data types

- Migrated from CVS to Git repository

- Changed naming convensions and style across all the source code

- Firebird backend removed from official release as not actively maintained. Available in the Git repository.

---
Version 3.0.0 differs from 2.2.0 in the following ways:

- Exposed the session's locale object.

- Moved the "no data" flag from indicators to statement.

- Allowed const objects as "use" elements.

- Added connection mode for Oracle.

- Added RAII support for transactions.

- Added the open/close/reconnect functionality.

- Added support for long long as a fundamental data type.

- Unified column names for dynamic rowset description, to overcome
  differences between database servers.

- Added the "simple" interface for interfacing from other languages.

- Added thread-safe connection pool.

- Added integrated support for Boost data types: gregorian_date,
  fusion and tuple.

- Added dynamic backend loading.

- Changed the naming convention to comply with Boost recommendations.

---
Version 2.2.0 differs from 2.1.0 in the following ways:

- Added true support for statement preparation with PostgreSQL.

- Added support for the stream-like extraction from Row.

- Added STL-compatible iterator interface for select statements.

- Refactored the set of common tests to validate core library functionality
  independently on the target database server.

- Introduced new backends for MS SQL Server (via ODBC) and Firebird.

- Provided complete build system for both Unix (autotools) and
  Windows (solution and project files for MSVC++).

---
Version 2.1.0 differs from 2.0.1 in the following ways:

- Added two additional backends: MySQL and SQLite3

- Restructured the source code layout so that the whole library was broken
  into the "core" part and independent "backends", each in its own
  directory together with appropriate tests.

- Provided basic Makefiles for static and shared libraries on
  Linux-compatible systems.

- Added the general class and function reference to the documentation.

---
Version 2.0.1 differs from 2.0.0 in the following ways:

- Corrected some typos in documentation.

- Corrected handling of dynamic rowset description for those backends
  which do not have dedicated description functionality.

- A bug fix to correctly handle std::tm in the Oracle backend.

- A bug fix to correctly handle object relational mapping when
  Values::set<T>() and Values::get<T>() are called where T is a
  TypeConversion-based type.

---
Version 2.0.0 differs from 1.2.1 in the following ways:

- The whole library was internally re-architectured to allow operation
  with many different backends. The top-level part of the library
  (the syntax layer) provides essentially the same interface as in previous
  versions of the library, but it can work with independent (and dynamically
  selected) backends, possibly targeting different database engines.
  As a prove of concept (and to encourage developments of new backends),
  the PostgreSQL backend was provided in addition to the Oracle one.
  During this re-architecturing, some minor bugs were fixed as well.

- The old Boost-style license was changed to the new (v. 1.0) Boost license.

---
The version 1.2.1 differs from 1.2.0 in the following ways:

- A bug was fixed that caused compile errors on MS VC++ compiler.

---
The version 1.2.0 differs from 1.1.0 in the following ways:

- A memory leak when reading into Row objects was fixed.

- Bulk (array) operations were introduced for high-performance
  applications, where the number of network round-trips can be
  significantly reduced by operating on whole arrays (vectors).

---
The version 1.1.0 differs from 1.0.1 in the following ways:

- Explicit support for calling stored procedures was added.

- Dynamic row recognition (type discovery) was added.

- Support for user-defined data types was added.
