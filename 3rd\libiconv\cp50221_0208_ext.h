/*
 * Copyright (C) 1999-2012, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CP50221 JISX0208 extensions
 */

static const unsigned short cp50221_0208_ext_2uni[96] = {
  /* 0x00 */
  0xfffd, 0x2460, 0x2461, 0x2462, 0x2463, 0x2464, 0x2465, 0x2466,
  0x2467, 0x2468, 0x2469, 0x246a, 0x246b, 0x246c, 0x246d, 0x246e,
  /* 0x10 */
  0x246f, 0x2470, 0x2471, 0x2472, 0x2473, 0x2160, 0x2161, 0x2162,
  0x2163, 0x2164, 0x2165, 0x2166, 0x2167, 0x2168, 0x2169, 0xfffd,
  /* 0x20 */
  0x3349, 0x3314, 0x3322, 0x334d, 0x3318, 0x3327, 0x3303, 0x3336,
  0x3351, 0x3357, 0x330d, 0x3326, 0x3323, 0x332b, 0x334a, 0x333b,
  /* 0x30 */
  0x339c, 0x339d, 0x339e, 0x338e, 0x338f, 0x33c4, 0x33a1, 0xfffd,
  0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0x337b,
  /* 0x40 */
  0x301e, 0x301f, 0x2116, 0x33cd, 0x2121, 0x32a4, 0x32a5, 0x32a6,
  0x32a7, 0x32a8, 0x3231, 0x3232, 0x3239, 0x337e, 0x337d, 0x337c,
  /* 0x50 */
  0xfffd, 0xfffd, 0xfffd, 0x222e, 0x2211, 0xfffd, 0xfffd, 0xfffd,
  0x221f, 0x22bf, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd, 0xfffd,
};

static int
cp50221_0208_ext_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c = *s;
  if (c < 0x60) {
    unsigned short wc = cp50221_0208_ext_2uni[c];
    if (wc != 0xfffd) {
      *pwc = (ucs4_t) wc;
      return 1;
    }
  }
  return RET_ILSEQ;
}

static const unsigned char cp50221_0208_ext_page21[96] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x00, /* 0x10-0x17 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x18-0x1f */
  0x00, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x20-0x27 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x28-0x2f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x30-0x37 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x38-0x3f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x40-0x47 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x48-0x4f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x50-0x57 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x58-0x5f */
  0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, /* 0x60-0x67 */
  0x1d, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x68-0x6f */
};
static const unsigned char cp50221_0208_ext_page22[32] = {
  0x00, 0x54, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x10-0x17 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, /* 0x18-0x1f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x20-0x27 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x53, 0x00, /* 0x28-0x2f */
};
static const unsigned char cp50221_0208_ext_page24[24] = {
  0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, /* 0x60-0x67 */
  0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10, /* 0x68-0x6f */
  0x11, 0x12, 0x13, 0x14, 0x00, 0x00, 0x00, 0x00, /* 0x70-0x77 */
};
static const unsigned char cp50221_0208_ext_page30[8] = {
  0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x40, 0x41, /* 0x18-0x1f */
};
static const unsigned char cp50221_0208_ext_page32[16] = {
  0x00, 0x4a, 0x4b, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x30-0x37 */
  0x00, 0x4c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x38-0x3f */
};
static const unsigned char cp50221_0208_ext_page32_1[16] = {
  0x00, 0x00, 0x00, 0x00, 0x45, 0x46, 0x47, 0x48, /* 0xa0-0xa7 */
  0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0xa8-0xaf */
};
static const unsigned char cp50221_0208_ext_page33[208] = {
  0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x00, /* 0x00-0x07 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x2a, 0x00, 0x00, /* 0x08-0x0f */
  0x00, 0x00, 0x00, 0x00, 0x21, 0x00, 0x00, 0x00, /* 0x10-0x17 */
  0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x18-0x1f */
  0x00, 0x00, 0x22, 0x2c, 0x00, 0x00, 0x2b, 0x25, /* 0x20-0x27 */
  0x00, 0x00, 0x00, 0x2d, 0x00, 0x00, 0x00, 0x00, /* 0x28-0x2f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x00, /* 0x30-0x37 */
  0x00, 0x00, 0x00, 0x2f, 0x00, 0x00, 0x00, 0x00, /* 0x38-0x3f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x40-0x47 */
  0x00, 0x20, 0x2e, 0x00, 0x00, 0x23, 0x00, 0x00, /* 0x48-0x4f */
  0x00, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, /* 0x50-0x57 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x58-0x5f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x60-0x67 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x68-0x6f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x70-0x77 */
  0x00, 0x00, 0x00, 0x3f, 0x4f, 0x4e, 0x4d, 0x00, /* 0x78-0x7f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x80-0x87 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x34, /* 0x88-0x8f */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0x90-0x97 */
  0x00, 0x00, 0x00, 0x00, 0x30, 0x31, 0x32, 0x00, /* 0x98-0x9f */
  0x00, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0xa0-0xa7 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0xa8-0xaf */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0xb0-0xb7 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* 0xb8-0xbf */
  0x00, 0x00, 0x00, 0x00, 0x35, 0x00, 0x00, 0x00, /* 0xc0-0xc7 */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x43, 0x00, 0x00, /* 0xc8-0xcf */
};

static int
cp50221_0208_ext_wctomb (conv_t conv, unsigned char *r, ucs4_t wc, size_t n)
{
  unsigned char c = 0;
  if (wc >= 0x2110 && wc < 0x2170)
    c = cp50221_0208_ext_page21[wc-0x2110];
  else if (wc >= 0x2210 && wc < 0x2230)
    c = cp50221_0208_ext_page22[wc-0x2210];
  else if (wc == 0x22bf)
    c = 0x59;
  else if (wc >= 0x2460 && wc < 0x2478)
    c = cp50221_0208_ext_page24[wc-0x2460];
  else if (wc >= 0x3018 && wc < 0x3020)
    c = cp50221_0208_ext_page30[wc-0x3018];
  else if (wc >= 0x3230 && wc < 0x3240)
    c = cp50221_0208_ext_page32[wc-0x3230];
  else if (wc >= 0x32a0 && wc < 0x32b0)
    c = cp50221_0208_ext_page32_1[wc-0x32a0];
  else if (wc >= 0x3300 && wc < 0x33d0)
    c = cp50221_0208_ext_page33[wc-0x3300];
  if (c != 0) {
    *r = c;
    return 1;
  }
  return RET_ILUNI;
}
