
/*
 * Include file constants (processed in LibmysqlIncludeFiles.txt 1
 */
#cmakedefine HAVE_OPENSSL_APPLINK_C 1
#cmakedefine HAVE_ALLOCA_H 1
#cmakedefine HAVE_BIGENDIAN 1
#cmakedefine HAVE_SETLOCALE 1
#cmakedefine HAVE_NL_LANGINFO 1
#cmakedefine HAVE_DLFCN_H 1
#cmakedefine HAVE_FCNTL_H 1
#cmakedefine HAVE_FLOAT_H 1
#cmakedefine HAVE_LIMITS_H 1
#cmakedefine HAVE_LINUX_LIMITS_H 1
#cmakedefine HAVE_PWD_H 1
#cmakedefine HAVE_SELECT_H 1
#cmakedefine HAVE_STDDEF_H 1
#cmakedefine HAVE_STDINT_H 1
#cmakedefine HAVE_STDLIB_H 1
#cmakedefine HAVE_STRING_H 1
#cmakedefine HAVE_SYS_IOCTL_H 1
#cmakedefine HAVE_SYS_SELECT_H 1
#cmakedefine HAVE_SYS_SOCKET_H 1
#cmakedefine HAVE_SYS_STREAM_H 1
#cmakedefine HAVE_SYS_STAT_H 1
#cmakedefine HAVE_SYS_SYSCTL_H 1
#cmakedefine HAVE_SYS_TYPES_H 1
#cmakedefine HAVE_SYS_UN_H 1
#cmakedefine HAVE_UNISTD_H 1
#cmakedefine HAVE_UCONTEXT_H 1

/*
 * function definitions - processed in LibmysqlFunctions.txt 
 */

#cmakedefine HAVE_DLERROR 1
#cmakedefine HAVE_DLOPEN 1
#cmakedefine HAVE_GETPWUID 1
#cmakedefine HAVE_MEMCPY 1
#cmakedefine HAVE_POLL 1
#cmakedefine HAVE_STRTOK_R 1
#cmakedefine HAVE_STRTOL 1
#cmakedefine HAVE_STRTOLL 1
#cmakedefine HAVE_STRTOUL 1
#cmakedefine HAVE_STRTOULL 1
#cmakedefine HAVE_TELL 1
#cmakedefine HAVE_THR_SETCONCURRENCY 1
#cmakedefine HAVE_THR_YIELD 1
#cmakedefine HAVE_VASPRINTF 1
#cmakedefine HAVE_VSNPRINTF 1
#cmakedefine HAVE_CUSERID 1

/*
 * types and sizes
 */


#cmakedefine SIZEOF_CHARP @SIZEOF_CHARP@
#if defined(SIZEOF_CHARP)
# define HAVE_CHARP 1
#endif


#cmakedefine SIZEOF_INT @SIZEOF_INT@
#if defined(SIZEOF_INT)
# define HAVE_INT 1
#endif

#cmakedefine SIZEOF_LONG @SIZEOF_LONG@
#if defined(SIZEOF_LONG)
# define HAVE_LONG 1
#endif

#cmakedefine SIZEOF_LONG_LONG @SIZEOF_LONG_LONG@
#if defined(SIZEOF_LONG_LONG)
# define HAVE_LONG_LONG 1
#endif


#cmakedefine SIZEOF_SIZE_T @SIZEOF_SIZE_T@
#if defined(SIZEOF_SIZE_T)
# define HAVE_SIZE_T 1
#endif


#cmakedefine SIZEOF_UINT @SIZEOF_UINT@
#if defined(SIZEOF_UINT)
# define HAVE_UINT 1
#endif

#cmakedefine SIZEOF_USHORT @SIZEOF_USHORT@
#if defined(SIZEOF_USHORT)
# define HAVE_USHORT 1
#endif

#cmakedefine SIZEOF_ULONG @SIZEOF_ULONG@
#if defined(SIZEOF_ULONG)
# define HAVE_ULONG 1
#endif

#cmakedefine SIZEOF_INT8 @SIZEOF_INT8@
#if defined(SIZEOF_INT8)
# define HAVE_INT8 1
#endif
#cmakedefine SIZEOF_UINT8 @SIZEOF_UINT8@
#if defined(SIZEOF_UINT8)
# define HAVE_UINT8 1
#endif

#cmakedefine SIZEOF_INT16 @SIZEOF_INT16@
#if defined(SIZEOF_INT16)
# define HAVE_INT16 1
#endif
#cmakedefine SIZEOF_UINT16 @SIZEOF_UINT16@
#if defined(SIZEOF_UINT16)
# define HAVE_UINT16 1
#endif

#cmakedefine SIZEOF_INT32 @SIZEOF_INT32@
#if defined(SIZEOF_INT32)
# define HAVE_INT32 1
#endif
#cmakedefine SIZEOF_UINT32 @SIZEOF_UINT32@
#if defined(SIZEOF_UINT32)
# define HAVE_UINT32 1
#endif

#cmakedefine SIZEOF_INT64 @SIZEOF_INT64@
#if defined(SIZEOF_INT64)
# define HAVE_INT64 1
#endif
#cmakedefine SIZEOF_UINT64 @SIZEOF_UINT64@
#if defined(SIZEOF_UINT64)
# define HAVE_UINT64 1
#endif

#cmakedefine SIZEOF_SOCKLEN_T @SIZEOF_SOCKLEN_T@
#if defined(SIZEOF_SOCKLEN_T)
# define HAVE_SOCKLEN_T 1
#endif

#cmakedefine SOCKET_SIZE_TYPE @SOCKET_SIZE_TYPE@

#define LOCAL_INFILE_MODE_OFF  0
#define LOCAL_INFILE_MODE_ON   1
#define LOCAL_INFILE_MODE_AUTO 2
#define ENABLED_LOCAL_INFILE LOCAL_INFILE_MODE_@ENABLED_LOCAL_INFILE@

#define MARIADB_DEFAULT_CHARSET "@DEFAULT_CHARSET@"

