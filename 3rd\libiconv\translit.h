/*
 * Copyright (C) 1999-2003 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * Transliteration table
 */

static const unsigned int translit_data[9116] = {
   1, ' ',
   1, '!',
   1, 'c',
   2, 'l', 'b',
   3, 'y', 'e', 'n',
   1, '|',
   2, 'S', 'S',
   1, '"',
   3, '(', 'c', ')',
   1, 'a',
   2, '<', '<',
   3, 'n', 'o', 't',
   1, '-',
   3, '(', 'R', ')',
   2, '^', '0',
   3, '+', '/', '-',
   2, '^', '2',
   2, '^', '3',
   1,'\'',
   1, 'u',
   1, 'P',
   1, '.',
   1, ',',
   2, '^', '1',
   1, 'o',
   2, '>', '>',
   5, ' ', '1',0x2044, '4', ' ',
   5, ' ', '1',0x2044, '2', ' ',
   5, ' ', '3',0x2044, '4', ' ',
   1, '?',
   2, '`', 'A',
   2,0xB4, 'A',
   2, '^', 'A',
   2, '~', 'A',
   2, '"', 'A',
   1, 'A',
   2, 'A', 'E',
   1, 'C',
   2, '`', 'E',
   2,0xB4, 'E',
   2, '^', 'E',
   2, '"', 'E',
   2, '`', 'I',
   2,0xB4, 'I',
   2, '^', 'I',
   2, '"', 'I',
   1, 'D',
   2, '~', 'N',
   2, '`', 'O',
   2,0xB4, 'O',
   2, '^', 'O',
   2, '~', 'O',
   2, '"', 'O',
   1, 'x',
   1, 'O',
   2, '`', 'U',
   2,0xB4, 'U',
   2, '^', 'U',
   2, '"', 'U',
   2,0xB4, 'Y',
   2, 'T', 'h',
   2, 's', 's',
   2, '`', 'a',
   2,0xB4, 'a',
   2, '^', 'a',
   2, '~', 'a',
   2, '"', 'a',
   1, 'a',
   2, 'a', 'e',
   1, 'c',
   2, '`', 'e',
   2,0xB4, 'e',
   2, '^', 'e',
   2, '"', 'e',
   2, '`', 'i',
   2,0xB4, 'i',
   2, '^', 'i',
   2, '"', 'i',
   1, 'd',
   2, '~', 'n',
   2, '`', 'o',
   2,0xB4, 'o',
   2, '^', 'o',
   2, '~', 'o',
   2, '"', 'o',
   1, ':',
   1, 'o',
   2, '`', 'u',
   2,0xB4, 'u',
   2, '^', 'u',
   2, '"', 'u',
   2,0xB4, 'y',
   2, 't', 'h',
   2, '"', 'y',
   1, 'A',
   1, 'a',
   1, 'A',
   1, 'a',
   1, 'A',
   1, 'a',
   2,0xB4, 'C',
   2,0xB4, 'c',
   2, '^', 'C',
   2, '^', 'c',
   1, 'C',
   1, 'c',
   1, 'C',
   1, 'c',
   1, 'D',
   1, 'd',
   1, 'D',
   1, 'd',
   1, 'E',
   1, 'e',
   1, 'E',
   1, 'e',
   1, 'E',
   1, 'e',
   1, 'E',
   1, 'e',
   1, 'E',
   1, 'e',
   2, '^', 'G',
   2, '^', 'g',
   1, 'G',
   1, 'g',
   1, 'G',
   1, 'g',
   1, 'G',
   1, 'g',
   2, '^', 'H',
   2, '^', 'h',
   1, 'H',
   1, 'h',
   2, '~', 'I',
   2, '~', 'i',
   1, 'I',
   1, 'i',
   1, 'I',
   1, 'i',
   1, 'I',
   1, 'i',
   1, 'I',
   1, 'i',
   2, 'I', 'J',
   2, 'i', 'j',
   2, '^', 'J',
   2, '^', 'j',
   1, 'K',
   1, 'k',
   1, 'L',
   1, 'l',
   1, 'L',
   1, 'l',
   1, 'L',
   1, 'l',
   1, 'L',
   1, 'l',
   1, 'L',
   1, 'l',
   2,0xB4, 'N',
   2,0xB4, 'n',
   1, 'N',
   1, 'n',
   1, 'N',
   1, 'n',
   2,'\'', 'n',
   1, 'O',
   1, 'o',
   1, 'O',
   1, 'o',
   2, '"', 'O',
   2, '"', 'o',
   2, 'O', 'E',
   2, 'o', 'e',
   2,0xB4, 'R',
   2,0xB4, 'r',
   1, 'R',
   1, 'r',
   1, 'R',
   1, 'r',
   2,0xB4, 'S',
   2,0xB4, 's',
   2, '^', 'S',
   2, '^', 's',
   1, 'S',
   1, 's',
   1, 'S',
   1, 's',
   1, 'T',
   1, 't',
   1, 'T',
   1, 't',
   1, 'T',
   1, 't',
   2, '~', 'U',
   2, '~', 'u',
   1, 'U',
   1, 'u',
   1, 'U',
   1, 'u',
   1, 'U',
   1, 'u',
   2, '"', 'U',
   2, '"', 'u',
   1, 'U',
   1, 'u',
   2, '^', 'W',
   2, '^', 'w',
   2, '^', 'Y',
   2, '^', 'y',
   2, '"', 'Y',
   2,0xB4, 'Z',
   2,0xB4, 'z',
   1, 'Z',
   1, 'z',
   1, 'Z',
   1, 'z',
   1, 's',
   1, 'f',
   2, 'D',0x017D,
   2, 'D',0x017E,
   2, 'd',0x017E,
   2, 'L', 'J',
   2, 'L', 'j',
   2, 'l', 'j',
   2, 'N', 'J',
   2, 'N', 'j',
   2, 'n', 'j',
   2, 'D', 'Z',
   2, 'D', 'z',
   2, 'd', 'z',
   1, 'S',
   1, 's',
   1, 'T',
   1, 't',
   1,0x2032,
   1,0x2033,
   1,0x2018,
   1,0x2019,
   1,0x201B,
   1, '^',
   1,'\'',
   1,0xAF,
   1,0xB4,
   1, '`',
   1, '_',
   1, '~',
   1, '"',
   1,0x03B2,
   1,0x03B8,
   1,0x03A5,
   1,0x03C6,
   1,0x03C0,
   1,0x03BA,
   1,0x03C1,
   1,0x03C2,
   1,0x0398,
   1,0x03B5,
   1,0x03A3,
   2,0x0565,0x0582,
   2,0x05D5,0x05D5,
   2,0x05D5,0x05D9,
   2,0x05D9,0x05D9,
   2,0x0627,0x0674,
   2,0x0648,0x0674,
   2,0x06C7,0x0674,
   2,0x064A,0x0674,
   2,0x0E4D,0x0E32,
   2,0x0ECD,0x0EB2,
   2,0x0EAB,0x0E99,
   2,0x0EAB,0x0EA1,
   2,0x0FB2,0x0F81,
   2,0x0FB3,0x0F81,
   1, 'B',
   1, 'b',
   1, 'D',
   1, 'd',
   1, 'F',
   1, 'f',
   1, 'M',
   1, 'm',
   1, 'P',
   1, 'p',
   1, 'S',
   1, 's',
   1, 'T',
   1, 't',
   2, '`', 'W',
   2, '`', 'w',
   2,0xB4, 'W',
   2,0xB4, 'w',
   2, '"', 'W',
   2, '"', 'w',
   2, 'a',0x02BE,
   2, '`', 'Y',
   2, '`', 'y',
   1, ' ',
   1, ' ',
   1, ' ',
   1, ' ',
   1, ' ',
   1, ' ',
   1, ' ',
   1, ' ',
   1, '-',
   1, '-',
   1, '-',
   1, '-',
   1, '-',
   1, '-',
   1,'\'',
   1,'\'',
   1, ',',
   1,'\'',
   1, '"',
   1, '"',
   1, '"',
   1, '"',
   1, '+',
   1, 'o',
   1, '.',
   2, '.', '.',
   3, '.', '.', '.',
   4, 'o', '/', 'o', 'o',
   1,0xB4,
   2,0xB4,0xB4,
   3,0xB4,0xB4,0xB4,
   2,0x2035,0x2035,
   3,0x2035,0x2035,0x2035,
   1, '<',
   1, '>',
   2, '!', '!',
   1, '/',
   2, '?', '?',
   2, '?', '!',
   2, '!', '?',
   4,0xB4,0xB4,0xB4,0xB4,
   2, 'R', 's',
   4,0x0110,0x1ED3, 'n', 'g',
   3, 'E', 'U', 'R',
   3, 'a', '/', 'c',
   3, 'a', '/', 's',
   1, 'C',
   2,0xB0, 'C',
   3, 'c', '/', 'o',
   3, 'c', '/', 'u',
   1,0x0190,
   2,0xB0, 'F',
   1, 'g',
   1, 'H',
   1, 'H',
   1, 'H',
   1, 'h',
   1,0x0127,
   1, 'I',
   1, 'I',
   1, 'L',
   1, 'l',
   1, 'N',
   2, 'N', 'o',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'R',
   1, 'R',
   3, 'T', 'E', 'L',
   2, 'T', 'M',
   1, 'Z',
   3, 'O', 'h', 'm',
   1, 'Z',
   1, 'B',
   1, 'C',
   1, 'e',
   1, 'e',
   1, 'E',
   1, 'F',
   1, 'M',
   1, 'o',
   1,0x05D0,
   1,0x05D1,
   1,0x05D2,
   1,0x05D3,
   1, 'i',
   3, 'F', 'A', 'X',
   1,0x03B3,
   1,0x0393,
   1,0x03A0,
   1,0x2211,
   1, 'D',
   1, 'd',
   1, 'e',
   1, 'i',
   1, 'j',
   5, ' ', '1',0x2044, '3', ' ',
   5, ' ', '2',0x2044, '3', ' ',
   5, ' ', '1',0x2044, '5', ' ',
   5, ' ', '2',0x2044, '5', ' ',
   5, ' ', '3',0x2044, '5', ' ',
   5, ' ', '4',0x2044, '5', ' ',
   5, ' ', '1',0x2044, '6', ' ',
   5, ' ', '5',0x2044, '6', ' ',
   5, ' ', '1',0x2044, '8', ' ',
   5, ' ', '3',0x2044, '8', ' ',
   5, ' ', '5',0x2044, '8', ' ',
   5, ' ', '7',0x2044, '8', ' ',
   3, ' ', '1',0x2044,
   1, 'I',
   2, 'I', 'I',
   3, 'I', 'I', 'I',
   2, 'I', 'V',
   1, 'V',
   2, 'V', 'I',
   3, 'V', 'I', 'I',
   4, 'V', 'I', 'I', 'I',
   2, 'I', 'X',
   1, 'X',
   2, 'X', 'I',
   3, 'X', 'I', 'I',
   1, 'L',
   1, 'C',
   1, 'D',
   1, 'M',
   1, 'i',
   2, 'i', 'i',
   3, 'i', 'i', 'i',
   2, 'i', 'v',
   1, 'v',
   2, 'v', 'i',
   3, 'v', 'i', 'i',
   4, 'v', 'i', 'i', 'i',
   2, 'i', 'x',
   1, 'x',
   2, 'x', 'i',
   3, 'x', 'i', 'i',
   1, 'l',
   1, 'c',
   1, 'd',
   1, 'm',
   2, '<', '-',
   1, '^',
   2, '-', '>',
   1, 'V',
   3, '<', '-', '>',
   2, '<', '=',
   2, '=', '>',
   3, '<', '=', '>',
   1, '-',
   1, '/',
   1,'\\',
   1, '*',
   1,0x2022,
   1, '|',
   2,0x222B,0x222B,
   3,0x222B,0x222B,0x222B,
   2,0x222E,0x222E,
   3,0x222E,0x222E,0x222E,
   1, ':',
   1, '~',
   2, '/', '=',
   2, '<', '=',
   2, '>', '=',
   2, '<', '<',
   2, '>', '>',
   1,0xB7,
   3, '<', '<', '<',
   3, '>', '>', '>',
   3,0xB7,0xB7,0xB7,
   5, '[', 'N', 'U', 'L', ']',
   5, '[', 'S', 'O', 'H', ']',
   5, '[', 'S', 'T', 'X', ']',
   5, '[', 'E', 'T', 'X', ']',
   5, '[', 'E', 'O', 'T', ']',
   5, '[', 'E', 'N', 'Q', ']',
   5, '[', 'A', 'C', 'K', ']',
   5, '[', 'B', 'E', 'L', ']',
   4, '[', 'B', 'S', ']',
   4, '[', 'H', 'T', ']',
   4, '[', 'L', 'F', ']',
   4, '[', 'V', 'T', ']',
   4, '[', 'F', 'F', ']',
   4, '[', 'C', 'R', ']',
   4, '[', 'S', 'O', ']',
   4, '[', 'S', 'I', ']',
   5, '[', 'D', 'L', 'E', ']',
   5, '[', 'D', 'C', '1', ']',
   5, '[', 'D', 'C', '2', ']',
   5, '[', 'D', 'C', '3', ']',
   5, '[', 'D', 'C', '4', ']',
   5, '[', 'N', 'A', 'K', ']',
   5, '[', 'S', 'Y', 'N', ']',
   5, '[', 'E', 'T', 'B', ']',
   5, '[', 'C', 'A', 'N', ']',
   4, '[', 'E', 'M', ']',
   5, '[', 'S', 'U', 'B', ']',
   5, '[', 'E', 'S', 'C', ']',
   4, '[', 'F', 'S', ']',
   4, '[', 'G', 'S', ']',
   4, '[', 'R', 'S', ']',
   4, '[', 'U', 'S', ']',
   4, '[', 'S', 'P', ']',
   5, '[', 'D', 'E', 'L', ']',
   4, '[', 'N', 'L', ']',
   3, '(', '1', ')',
   3, '(', '2', ')',
   3, '(', '3', ')',
   3, '(', '4', ')',
   3, '(', '5', ')',
   3, '(', '6', ')',
   3, '(', '7', ')',
   3, '(', '8', ')',
   3, '(', '9', ')',
   4, '(', '1', '0', ')',
   4, '(', '1', '1', ')',
   4, '(', '1', '2', ')',
   4, '(', '1', '3', ')',
   4, '(', '1', '4', ')',
   4, '(', '1', '5', ')',
   4, '(', '1', '6', ')',
   4, '(', '1', '7', ')',
   4, '(', '1', '8', ')',
   4, '(', '1', '9', ')',
   4, '(', '2', '0', ')',
   3, '(', '1', ')',
   3, '(', '2', ')',
   3, '(', '3', ')',
   3, '(', '4', ')',
   3, '(', '5', ')',
   3, '(', '6', ')',
   3, '(', '7', ')',
   3, '(', '8', ')',
   3, '(', '9', ')',
   4, '(', '1', '0', ')',
   4, '(', '1', '1', ')',
   4, '(', '1', '2', ')',
   4, '(', '1', '3', ')',
   4, '(', '1', '4', ')',
   4, '(', '1', '5', ')',
   4, '(', '1', '6', ')',
   4, '(', '1', '7', ')',
   4, '(', '1', '8', ')',
   4, '(', '1', '9', ')',
   4, '(', '2', '0', ')',
   2, '1', '.',
   2, '2', '.',
   2, '3', '.',
   2, '4', '.',
   2, '5', '.',
   2, '6', '.',
   2, '7', '.',
   2, '8', '.',
   2, '9', '.',
   3, '1', '0', '.',
   3, '1', '1', '.',
   3, '1', '2', '.',
   3, '1', '3', '.',
   3, '1', '4', '.',
   3, '1', '5', '.',
   3, '1', '6', '.',
   3, '1', '7', '.',
   3, '1', '8', '.',
   3, '1', '9', '.',
   3, '2', '0', '.',
   3, '(', 'a', ')',
   3, '(', 'b', ')',
   3, '(', 'c', ')',
   3, '(', 'd', ')',
   3, '(', 'e', ')',
   3, '(', 'f', ')',
   3, '(', 'g', ')',
   3, '(', 'h', ')',
   3, '(', 'i', ')',
   3, '(', 'j', ')',
   3, '(', 'k', ')',
   3, '(', 'l', ')',
   3, '(', 'm', ')',
   3, '(', 'n', ')',
   3, '(', 'o', ')',
   3, '(', 'p', ')',
   3, '(', 'q', ')',
   3, '(', 'r', ')',
   3, '(', 's', ')',
   3, '(', 't', ')',
   3, '(', 'u', ')',
   3, '(', 'v', ')',
   3, '(', 'w', ')',
   3, '(', 'x', ')',
   3, '(', 'y', ')',
   3, '(', 'z', ')',
   3, '(', 'A', ')',
   3, '(', 'B', ')',
   3, '(', 'C', ')',
   3, '(', 'D', ')',
   3, '(', 'E', ')',
   3, '(', 'F', ')',
   3, '(', 'G', ')',
   3, '(', 'H', ')',
   3, '(', 'I', ')',
   3, '(', 'J', ')',
   3, '(', 'K', ')',
   3, '(', 'L', ')',
   3, '(', 'M', ')',
   3, '(', 'N', ')',
   3, '(', 'O', ')',
   3, '(', 'P', ')',
   3, '(', 'Q', ')',
   3, '(', 'R', ')',
   3, '(', 'S', ')',
   3, '(', 'T', ')',
   3, '(', 'U', ')',
   3, '(', 'V', ')',
   3, '(', 'W', ')',
   3, '(', 'X', ')',
   3, '(', 'Y', ')',
   3, '(', 'Z', ')',
   3, '(', 'a', ')',
   3, '(', 'b', ')',
   3, '(', 'c', ')',
   3, '(', 'd', ')',
   3, '(', 'e', ')',
   3, '(', 'f', ')',
   3, '(', 'g', ')',
   3, '(', 'h', ')',
   3, '(', 'i', ')',
   3, '(', 'j', ')',
   3, '(', 'k', ')',
   3, '(', 'l', ')',
   3, '(', 'm', ')',
   3, '(', 'n', ')',
   3, '(', 'o', ')',
   3, '(', 'p', ')',
   3, '(', 'q', ')',
   3, '(', 'r', ')',
   3, '(', 's', ')',
   3, '(', 't', ')',
   3, '(', 'u', ')',
   3, '(', 'v', ')',
   3, '(', 'w', ')',
   3, '(', 'x', ')',
   3, '(', 'y', ')',
   3, '(', 'z', ')',
   3, '(', '0', ')',
   1, '-',
   1, '|',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, '+',
   1, 'o',
   4,0x222B,0x222B,0x222B,0x222B,
   3, ':', ':', '=',
   2, '=', '=',
   3, '=', '=', '=',
   1,0x6BCD,
   1,0x9F9F,
   1,0x4E00,
   1,0x4E28,
   1,0x4E36,
   1,0x4E3F,
   1,0x4E59,
   1,0x4E85,
   1,0x4E8C,
   1,0x4EA0,
   1,0x4EBA,
   1,0x513F,
   1,0x5165,
   1,0x516B,
   1,0x5182,
   1,0x5196,
   1,0x51AB,
   1,0x51E0,
   1,0x51F5,
   1,0x5200,
   1,0x529B,
   1,0x52F9,
   1,0x5315,
   1,0x531A,
   1,0x5338,
   1,0x5341,
   1,0x535C,
   1,0x5369,
   1,0x5382,
   1,0x53B6,
   1,0x53C8,
   1,0x53E3,
   1,0x56D7,
   1,0x571F,
   1,0x58EB,
   1,0x5902,
   1,0x590A,
   1,0x5915,
   1,0x5927,
   1,0x5973,
   1,0x5B50,
   1,0x5B80,
   1,0x5BF8,
   1,0x5C0F,
   1,0x5C22,
   1,0x5C38,
   1,0x5C6E,
   1,0x5C71,
   1,0x5DDB,
   1,0x5DE5,
   1,0x5DF1,
   1,0x5DFE,
   1,0x5E72,
   1,0x5E7A,
   1,0x5E7F,
   1,0x5EF4,
   1,0x5EFE,
   1,0x5F0B,
   1,0x5F13,
   1,0x5F50,
   1,0x5F61,
   1,0x5F73,
   1,0x5FC3,
   1,0x6208,
   1,0x6236,
   1,0x624B,
   1,0x652F,
   1,0x6534,
   1,0x6587,
   1,0x6597,
   1,0x65A4,
   1,0x65B9,
   1,0x65E0,
   1,0x65E5,
   1,0x66F0,
   1,0x6708,
   1,0x6728,
   1,0x6B20,
   1,0x6B62,
   1,0x6B79,
   1,0x6BB3,
   1,0x6BCB,
   1,0x6BD4,
   1,0x6BDB,
   1,0x6C0F,
   1,0x6C14,
   1,0x6C34,
   1,0x706B,
   1,0x722A,
   1,0x7236,
   1,0x723B,
   1,0x723F,
   1,0x7247,
   1,0x7259,
   1,0x725B,
   1,0x72AC,
   1,0x7384,
   1,0x7389,
   1,0x74DC,
   1,0x74E6,
   1,0x7518,
   1,0x751F,
   1,0x7528,
   1,0x7530,
   1,0x758B,
   1,0x7592,
   1,0x7676,
   1,0x767D,
   1,0x76AE,
   1,0x76BF,
   1,0x76EE,
   1,0x77DB,
   1,0x77E2,
   1,0x77F3,
   1,0x793A,
   1,0x79B8,
   1,0x79BE,
   1,0x7A74,
   1,0x7ACB,
   1,0x7AF9,
   1,0x7C73,
   1,0x7CF8,
   1,0x7F36,
   1,0x7F51,
   1,0x7F8A,
   1,0x7FBD,
   1,0x8001,
   1,0x800C,
   1,0x8012,
   1,0x8033,
   1,0x807F,
   1,0x8089,
   1,0x81E3,
   1,0x81EA,
   1,0x81F3,
   1,0x81FC,
   1,0x820C,
   1,0x821B,
   1,0x821F,
   1,0x826E,
   1,0x8272,
   1,0x8278,
   1,0x864D,
   1,0x866B,
   1,0x8840,
   1,0x884C,
   1,0x8863,
   1,0x897E,
   1,0x898B,
   1,0x89D2,
   1,0x8A00,
   1,0x8C37,
   1,0x8C46,
   1,0x8C55,
   1,0x8C78,
   1,0x8C9D,
   1,0x8D64,
   1,0x8D70,
   1,0x8DB3,
   1,0x8EAB,
   1,0x8ECA,
   1,0x8F9B,
   1,0x8FB0,
   1,0x8FB5,
   1,0x9091,
   1,0x9149,
   1,0x91C6,
   1,0x91CC,
   1,0x91D1,
   1,0x9577,
   1,0x9580,
   1,0x961C,
   1,0x96B6,
   1,0x96B9,
   1,0x96E8,
   1,0x9751,
   1,0x975E,
   1,0x9762,
   1,0x9769,
   1,0x97CB,
   1,0x97ED,
   1,0x97F3,
   1,0x9801,
   1,0x98A8,
   1,0x98DB,
   1,0x98DF,
   1,0x9996,
   1,0x9999,
   1,0x99AC,
   1,0x9AA8,
   1,0x9AD8,
   1,0x9ADF,
   1,0x9B25,
   1,0x9B2F,
   1,0x9B32,
   1,0x9B3C,
   1,0x9B5A,
   1,0x9CE5,
   1,0x9E75,
   1,0x9E7F,
   1,0x9EA5,
   1,0x9EBB,
   1,0x9EC3,
   1,0x9ECD,
   1,0x9ED1,
   1,0x9EF9,
   1,0x9EFD,
   1,0x9F0E,
   1,0x9F13,
   1,0x9F20,
   1,0x9F3B,
   1,0x9F4A,
   1,0x9F52,
   1,0x9F8D,
   1,0x9F9C,
   1,0x9FA0,
   1, ' ',
   1,0x3012,
   1,0x5341,
   1,0x5344,
   1,0x5345,
   1,0x3042,
   1,0x3044,
   1,0x3046,
   1,0x3048,
   1,0x304A,
   1,0x3064,
   1,0x3084,
   1,0x3086,
   1,0x3088,
   1,0x308F,
   1,0x304B,
   1,0x3051,
   2, ' ',0x3099,
   2, ' ',0x309A,
   1, '=',
   1,0x30A2,
   1,0x30A4,
   1,0x30A6,
   1,0x30A8,
   1,0x30AA,
   1,0x30C4,
   1,0x30E4,
   1,0x30E6,
   1,0x30E8,
   1,0x30EF,
   1,0x30AB,
   1,0x30B1,
   1,0x1100,
   1,0x1101,
   1,0x11AA,
   1,0x1102,
   1,0x11AC,
   1,0x11AD,
   1,0x1103,
   1,0x1104,
   1,0x1105,
   1,0x11B0,
   1,0x11B1,
   1,0x11B2,
   1,0x11B3,
   1,0x11B4,
   1,0x11B5,
   1,0x111A,
   1,0x1106,
   1,0x1107,
   1,0x1108,
   1,0x1121,
   1,0x1109,
   1,0x110A,
   1,0x110B,
   1,0x110C,
   1,0x110D,
   1,0x110E,
   1,0x110F,
   1,0x1110,
   1,0x1111,
   1,0x1112,
   1,0x1161,
   1,0x1162,
   1,0x1163,
   1,0x1164,
   1,0x1165,
   1,0x1166,
   1,0x1167,
   1,0x1168,
   1,0x1169,
   1,0x116A,
   1,0x116B,
   1,0x116C,
   1,0x116D,
   1,0x116E,
   1,0x116F,
   1,0x1170,
   1,0x1171,
   1,0x1172,
   1,0x1173,
   1,0x1174,
   1,0x1175,
   1,0x1160,
   1,0x1114,
   1,0x1115,
   1,0x11C7,
   1,0x11C8,
   1,0x11CC,
   1,0x11CE,
   1,0x11D3,
   1,0x11D7,
   1,0x11D9,
   1,0x111C,
   1,0x11DD,
   1,0x11DF,
   1,0x111D,
   1,0x111E,
   1,0x1120,
   1,0x1122,
   1,0x1123,
   1,0x1127,
   1,0x1129,
   1,0x112B,
   1,0x112C,
   1,0x112D,
   1,0x112E,
   1,0x112F,
   1,0x1132,
   1,0x1136,
   1,0x1140,
   1,0x1147,
   1,0x114C,
   1,0x11F1,
   1,0x11F2,
   1,0x1157,
   1,0x1158,
   1,0x1159,
   1,0x1184,
   1,0x1185,
   1,0x1188,
   1,0x1191,
   1,0x1192,
   1,0x1194,
   1,0x119E,
   1,0x11A1,
   1,0x30AF,
   1,0x30B7,
   1,0x30B9,
   1,0x30C8,
   1,0x30CC,
   1,0x30CF,
   1,0x30D2,
   1,0x30D5,
   1,0x30D8,
   1,0x30DB,
   1,0x30E0,
   1,0x30E9,
   1,0x30EA,
   1,0x30EB,
   1,0x30EC,
   1,0x30ED,
   3, '(',0x1100, ')',
   3, '(',0x1102, ')',
   3, '(',0x1103, ')',
   3, '(',0x1105, ')',
   3, '(',0x1106, ')',
   3, '(',0x1107, ')',
   3, '(',0x1109, ')',
   3, '(',0x110B, ')',
   3, '(',0x110C, ')',
   3, '(',0x110E, ')',
   3, '(',0x110F, ')',
   3, '(',0x1110, ')',
   3, '(',0x1111, ')',
   3, '(',0x1112, ')',
   4, '(',0x1100,0x1161, ')',
   4, '(',0x1102,0x1161, ')',
   4, '(',0x1103,0x1161, ')',
   4, '(',0x1105,0x1161, ')',
   4, '(',0x1106,0x1161, ')',
   4, '(',0x1107,0x1161, ')',
   4, '(',0x1109,0x1161, ')',
   4, '(',0x110B,0x1161, ')',
   4, '(',0x110C,0x1161, ')',
   4, '(',0x110E,0x1161, ')',
   4, '(',0x110F,0x1161, ')',
   4, '(',0x1110,0x1161, ')',
   4, '(',0x1111,0x1161, ')',
   4, '(',0x1112,0x1161, ')',
   4, '(',0x110C,0x116E, ')',
   7, '(',0x110B,0x1169,0x110C,0x1165,0x11AB, ')',
   6, '(',0x110B,0x1169,0x1112,0x116E, ')',
   3, '(',0x4E00, ')',
   3, '(',0x4E8C, ')',
   3, '(',0x4E09, ')',
   3, '(',0x56DB, ')',
   3, '(',0x4E94, ')',
   3, '(',0x516D, ')',
   3, '(',0x4E03, ')',
   3, '(',0x516B, ')',
   3, '(',0x4E5D, ')',
   3, '(',0x5341, ')',
   3, '(',0x6708, ')',
   3, '(',0x706B, ')',
   3, '(',0x6C34, ')',
   3, '(',0x6728, ')',
   3, '(',0x91D1, ')',
   3, '(',0x571F, ')',
   3, '(',0x65E5, ')',
   3, '(',0x682A, ')',
   3, '(',0x6709, ')',
   3, '(',0x793E, ')',
   3, '(',0x540D, ')',
   3, '(',0x7279, ')',
   3, '(',0x8CA1, ')',
   3, '(',0x795D, ')',
   3, '(',0x52B4, ')',
   3, '(',0x4EE3, ')',
   3, '(',0x547C, ')',
   3, '(',0x5B66, ')',
   3, '(',0x76E3, ')',
   3, '(',0x4F01, ')',
   3, '(',0x8CC7, ')',
   3, '(',0x5354, ')',
   3, '(',0x796D, ')',
   3, '(',0x4F11, ')',
   3, '(',0x81EA, ')',
   3, '(',0x81F3, ')',
   3, 'P', 'T', 'E',
   4, '(', '2', '1', ')',
   4, '(', '2', '2', ')',
   4, '(', '2', '3', ')',
   4, '(', '2', '4', ')',
   4, '(', '2', '5', ')',
   4, '(', '2', '6', ')',
   4, '(', '2', '7', ')',
   4, '(', '2', '8', ')',
   4, '(', '2', '9', ')',
   4, '(', '3', '0', ')',
   4, '(', '3', '1', ')',
   4, '(', '3', '2', ')',
   4, '(', '3', '3', ')',
   4, '(', '3', '4', ')',
   4, '(', '3', '5', ')',
   3, '(',0x1100, ')',
   3, '(',0x1102, ')',
   3, '(',0x1103, ')',
   3, '(',0x1105, ')',
   3, '(',0x1106, ')',
   3, '(',0x1107, ')',
   3, '(',0x1109, ')',
   3, '(',0x110B, ')',
   3, '(',0x110C, ')',
   3, '(',0x110E, ')',
   3, '(',0x110F, ')',
   3, '(',0x1110, ')',
   3, '(',0x1111, ')',
   3, '(',0x1112, ')',
   4, '(',0x1100,0x1161, ')',
   4, '(',0x1102,0x1161, ')',
   4, '(',0x1103,0x1161, ')',
   4, '(',0x1105,0x1161, ')',
   4, '(',0x1106,0x1161, ')',
   4, '(',0x1107,0x1161, ')',
   4, '(',0x1109,0x1161, ')',
   4, '(',0x110B,0x1161, ')',
   4, '(',0x110C,0x1161, ')',
   4, '(',0x110E,0x1161, ')',
   4, '(',0x110F,0x1161, ')',
   4, '(',0x1110,0x1161, ')',
   4, '(',0x1111,0x1161, ')',
   4, '(',0x1112,0x1161, ')',
   7, '(',0x110E,0x1161,0x11B7,0x1100,0x1169, ')',
   6, '(',0x110C,0x116E,0x110B,0x1174, ')',
   3, '(',0x4E00, ')',
   3, '(',0x4E8C, ')',
   3, '(',0x4E09, ')',
   3, '(',0x56DB, ')',
   3, '(',0x4E94, ')',
   3, '(',0x516D, ')',
   3, '(',0x4E03, ')',
   3, '(',0x516B, ')',
   3, '(',0x4E5D, ')',
   3, '(',0x5341, ')',
   3, '(',0x6708, ')',
   3, '(',0x706B, ')',
   3, '(',0x6C34, ')',
   3, '(',0x6728, ')',
   3, '(',0x91D1, ')',
   3, '(',0x571F, ')',
   3, '(',0x65E5, ')',
   3, '(',0x682A, ')',
   3, '(',0x6709, ')',
   3, '(',0x793E, ')',
   3, '(',0x540D, ')',
   3, '(',0x7279, ')',
   3, '(',0x8CA1, ')',
   3, '(',0x795D, ')',
   3, '(',0x52B4, ')',
   3, '(',0x79D8, ')',
   3, '(',0x7537, ')',
   3, '(',0x5973, ')',
   3, '(',0x9069, ')',
   3, '(',0x512A, ')',
   3, '(',0x5370, ')',
   3, '(',0x6CE8, ')',
   3, '(',0x9805, ')',
   3, '(',0x4F11, ')',
   3, '(',0x5199, ')',
   3, '(',0x6B63, ')',
   3, '(',0x4E0A, ')',
   3, '(',0x4E2D, ')',
   3, '(',0x4E0B, ')',
   3, '(',0x5DE6, ')',
   3, '(',0x53F3, ')',
   3, '(',0x533B, ')',
   3, '(',0x5B97, ')',
   3, '(',0x5B66, ')',
   3, '(',0x76E3, ')',
   3, '(',0x4F01, ')',
   3, '(',0x8CC7, ')',
   3, '(',0x5354, ')',
   3, '(',0x591C, ')',
   4, '(', '3', '6', ')',
   4, '(', '3', '7', ')',
   4, '(', '3', '8', ')',
   4, '(', '3', '9', ')',
   4, '(', '4', '0', ')',
   4, '(', '4', '1', ')',
   4, '(', '4', '2', ')',
   4, '(', '4', '3', ')',
   4, '(', '4', '4', ')',
   4, '(', '4', '5', ')',
   4, '(', '4', '6', ')',
   4, '(', '4', '7', ')',
   4, '(', '4', '8', ')',
   4, '(', '4', '9', ')',
   4, '(', '5', '0', ')',
   2, '1',0x6708,
   2, '2',0x6708,
   2, '3',0x6708,
   2, '4',0x6708,
   2, '5',0x6708,
   2, '6',0x6708,
   2, '7',0x6708,
   2, '8',0x6708,
   2, '9',0x6708,
   3, '1', '0',0x6708,
   3, '1', '1',0x6708,
   3, '1', '2',0x6708,
   2, 'H', 'g',
   3, 'e', 'r', 'g',
   2, 'e', 'V',
   3, 'L', 'T', 'D',
   3, '(',0x30A2, ')',
   3, '(',0x30A4, ')',
   3, '(',0x30A6, ')',
   3, '(',0x30A8, ')',
   3, '(',0x30AA, ')',
   3, '(',0x30AB, ')',
   3, '(',0x30AD, ')',
   3, '(',0x30AF, ')',
   3, '(',0x30B1, ')',
   3, '(',0x30B3, ')',
   3, '(',0x30B5, ')',
   3, '(',0x30B7, ')',
   3, '(',0x30B9, ')',
   3, '(',0x30BB, ')',
   3, '(',0x30BD, ')',
   3, '(',0x30BF, ')',
   3, '(',0x30C1, ')',
   3, '(',0x30C4, ')',
   3, '(',0x30C6, ')',
   3, '(',0x30C8, ')',
   3, '(',0x30CA, ')',
   3, '(',0x30CB, ')',
   3, '(',0x30CC, ')',
   3, '(',0x30CD, ')',
   3, '(',0x30CE, ')',
   3, '(',0x30CF, ')',
   3, '(',0x30D2, ')',
   3, '(',0x30D5, ')',
   3, '(',0x30D8, ')',
   3, '(',0x30DB, ')',
   3, '(',0x30DE, ')',
   3, '(',0x30DF, ')',
   3, '(',0x30E0, ')',
   3, '(',0x30E1, ')',
   3, '(',0x30E2, ')',
   3, '(',0x30E4, ')',
   3, '(',0x30E6, ')',
   3, '(',0x30E8, ')',
   3, '(',0x30E9, ')',
   3, '(',0x30EA, ')',
   3, '(',0x30EB, ')',
   3, '(',0x30EC, ')',
   3, '(',0x30ED, ')',
   3, '(',0x30EF, ')',
   3, '(',0x30F0, ')',
   3, '(',0x30F1, ')',
   3, '(',0x30F2, ')',
   4,0x30A2,0x30D1,0x30FC,0x30C8,
   4,0x30A2,0x30EB,0x30D5,0x30A1,
   4,0x30A2,0x30F3,0x30DA,0x30A2,
   3,0x30A2,0x30FC,0x30EB,
   4,0x30A4,0x30CB,0x30F3,0x30B0,
   3,0x30A4,0x30F3,0x30C1,
   3,0x30A6,0x30A9,0x30F3,
   5,0x30A8,0x30B9,0x30AF,0x30FC,0x30C9,
   4,0x30A8,0x30FC,0x30AB,0x30FC,
   3,0x30AA,0x30F3,0x30B9,
   3,0x30AA,0x30FC,0x30E0,
   3,0x30AB,0x30A4,0x30EA,
   4,0x30AB,0x30E9,0x30C3,0x30C8,
   4,0x30AB,0x30ED,0x30EA,0x30FC,
   3,0x30AC,0x30ED,0x30F3,
   3,0x30AC,0x30F3,0x30DE,
   2,0x30AE,0x30AC,
   3,0x30AE,0x30CB,0x30FC,
   4,0x30AD,0x30E5,0x30EA,0x30FC,
   4,0x30AE,0x30EB,0x30C0,0x30FC,
   2,0x30AD,0x30ED,
   5,0x30AD,0x30ED,0x30B0,0x30E9,0x30E0,
   6,0x30AD,0x30ED,0x30E1,0x30FC,0x30C8,0x30EB,
   5,0x30AD,0x30ED,0x30EF,0x30C3,0x30C8,
   3,0x30B0,0x30E9,0x30E0,
   5,0x30B0,0x30E9,0x30E0,0x30C8,0x30F3,
   5,0x30AF,0x30EB,0x30BC,0x30A4,0x30ED,
   4,0x30AF,0x30ED,0x30FC,0x30CD,
   3,0x30B1,0x30FC,0x30B9,
   3,0x30B3,0x30EB,0x30CA,
   3,0x30B3,0x30FC,0x30DD,
   4,0x30B5,0x30A4,0x30AF,0x30EB,
   5,0x30B5,0x30F3,0x30C1,0x30FC,0x30E0,
   4,0x30B7,0x30EA,0x30F3,0x30B0,
   3,0x30BB,0x30F3,0x30C1,
   3,0x30BB,0x30F3,0x30C8,
   3,0x30C0,0x30FC,0x30B9,
   2,0x30C7,0x30B7,
   2,0x30C9,0x30EB,
   2,0x30C8,0x30F3,
   2,0x30CA,0x30CE,
   3,0x30CE,0x30C3,0x30C8,
   3,0x30CF,0x30A4,0x30C4,
   5,0x30D1,0x30FC,0x30BB,0x30F3,0x30C8,
   3,0x30D1,0x30FC,0x30C4,
   4,0x30D0,0x30FC,0x30EC,0x30EB,
   5,0x30D4,0x30A2,0x30B9,0x30C8,0x30EB,
   3,0x30D4,0x30AF,0x30EB,
   2,0x30D4,0x30B3,
   2,0x30D3,0x30EB,
   5,0x30D5,0x30A1,0x30E9,0x30C3,0x30C9,
   4,0x30D5,0x30A3,0x30FC,0x30C8,
   5,0x30D6,0x30C3,0x30B7,0x30A7,0x30EB,
   3,0x30D5,0x30E9,0x30F3,
   5,0x30D8,0x30AF,0x30BF,0x30FC,0x30EB,
   2,0x30DA,0x30BD,
   3,0x30DA,0x30CB,0x30D2,
   3,0x30D8,0x30EB,0x30C4,
   3,0x30DA,0x30F3,0x30B9,
   3,0x30DA,0x30FC,0x30B8,
   3,0x30D9,0x30FC,0x30BF,
   4,0x30DD,0x30A4,0x30F3,0x30C8,
   3,0x30DC,0x30EB,0x30C8,
   2,0x30DB,0x30F3,
   3,0x30DD,0x30F3,0x30C9,
   3,0x30DB,0x30FC,0x30EB,
   3,0x30DB,0x30FC,0x30F3,
   4,0x30DE,0x30A4,0x30AF,0x30ED,
   3,0x30DE,0x30A4,0x30EB,
   3,0x30DE,0x30C3,0x30CF,
   3,0x30DE,0x30EB,0x30AF,
   5,0x30DE,0x30F3,0x30B7,0x30E7,0x30F3,
   4,0x30DF,0x30AF,0x30ED,0x30F3,
   2,0x30DF,0x30EA,
   5,0x30DF,0x30EA,0x30D0,0x30FC,0x30EB,
   2,0x30E1,0x30AC,
   4,0x30E1,0x30AC,0x30C8,0x30F3,
   4,0x30E1,0x30FC,0x30C8,0x30EB,
   3,0x30E4,0x30FC,0x30C9,
   3,0x30E4,0x30FC,0x30EB,
   3,0x30E6,0x30A2,0x30F3,
   4,0x30EA,0x30C3,0x30C8,0x30EB,
   2,0x30EA,0x30E9,
   3,0x30EB,0x30D4,0x30FC,
   4,0x30EB,0x30FC,0x30D6,0x30EB,
   2,0x30EC,0x30E0,
   5,0x30EC,0x30F3,0x30C8,0x30B2,0x30F3,
   3,0x30EF,0x30C3,0x30C8,
   2, '0',0x70B9,
   2, '1',0x70B9,
   2, '2',0x70B9,
   2, '3',0x70B9,
   2, '4',0x70B9,
   2, '5',0x70B9,
   2, '6',0x70B9,
   2, '7',0x70B9,
   2, '8',0x70B9,
   2, '9',0x70B9,
   3, '1', '0',0x70B9,
   3, '1', '1',0x70B9,
   3, '1', '2',0x70B9,
   3, '1', '3',0x70B9,
   3, '1', '4',0x70B9,
   3, '1', '5',0x70B9,
   3, '1', '6',0x70B9,
   3, '1', '7',0x70B9,
   3, '1', '8',0x70B9,
   3, '1', '9',0x70B9,
   3, '2', '0',0x70B9,
   3, '2', '1',0x70B9,
   3, '2', '2',0x70B9,
   3, '2', '3',0x70B9,
   3, '2', '4',0x70B9,
   3, 'h', 'P', 'a',
   2, 'd', 'a',
   2, 'A', 'U',
   3, 'b', 'a', 'r',
   2, 'o', 'V',
   2, 'p', 'c',
   2, 'd', 'm',
   4, 'd', 'm', '^', '2',
   4, 'd', 'm', '^', '3',
   2, 'I', 'U',
   2,0x5E73,0x6210,
   2,0x662D,0x548C,
   2,0x5927,0x6B63,
   2,0x660E,0x6CBB,
   4,0x682A,0x5F0F,0x4F1A,0x793E,
   2, 'p', 'A',
   2, 'n', 'A',
   2,0x03BC, 'A',
   2, 'm', 'A',
   2, 'k', 'A',
   2, 'K', 'B',
   2, 'M', 'B',
   2, 'G', 'B',
   3, 'c', 'a', 'l',
   4, 'k', 'c', 'a', 'l',
   2, 'p', 'F',
   2, 'n', 'F',
   2,0x03BC, 'F',
   2,0x03BC, 'g',
   2, 'm', 'g',
   2, 'k', 'g',
   2, 'H', 'z',
   3, 'k', 'H', 'z',
   3, 'M', 'H', 'z',
   3, 'G', 'H', 'z',
   3, 'T', 'H', 'z',
   2,0x03BC, 'l',
   2, 'm', 'l',
   2, 'd', 'l',
   2, 'k', 'l',
   2, 'f', 'm',
   2, 'n', 'm',
   2,0x03BC, 'm',
   2, 'm', 'm',
   2, 'c', 'm',
   2, 'k', 'm',
   4, 'm', 'm', '^', '2',
   4, 'c', 'm', '^', '2',
   3, 'm', '^', '2',
   4, 'k', 'm', '^', '2',
   4, 'm', 'm', '^', '3',
   4, 'c', 'm', '^', '3',
   3, 'm', '^', '3',
   4, 'k', 'm', '^', '3',
   3, 'm', '/', 's',
   5, 'm', '/', 's', '^', '2',
   2, 'P', 'a',
   3, 'k', 'P', 'a',
   3, 'M', 'P', 'a',
   3, 'G', 'P', 'a',
   3, 'r', 'a', 'd',
   5, 'r', 'a', 'd', '/', 's',
   7, 'r', 'a', 'd', '/', 's', '^', '2',
   2, 'p', 's',
   2, 'n', 's',
   2,0x03BC, 's',
   2, 'm', 's',
   2, 'p', 'V',
   2, 'n', 'V',
   2,0x03BC, 'V',
   2, 'm', 'V',
   2, 'k', 'V',
   2, 'M', 'V',
   2, 'p', 'W',
   2, 'n', 'W',
   2,0x03BC, 'W',
   2, 'm', 'W',
   2, 'k', 'W',
   2, 'M', 'W',
   2, 'k',0x03A9,
   2, 'M',0x03A9,
   4, 'a', '.', 'm', '.',
   2, 'B', 'q',
   2, 'c', 'c',
   2, 'c', 'd',
   4, 'C', '/', 'k', 'g',
   3, 'C', 'o', '.',
   2, 'd', 'B',
   2, 'G', 'y',
   2, 'h', 'a',
   2, 'H', 'P',
   2, 'i', 'n',
   2, 'K', 'K',
   2, 'K', 'M',
   2, 'k', 't',
   2, 'l', 'm',
   2, 'l', 'n',
   3, 'l', 'o', 'g',
   2, 'l', 'x',
   2, 'm', 'b',
   3, 'm', 'i', 'l',
   3, 'm', 'o', 'l',
   2, 'P', 'H',
   4, 'p', '.', 'm', '.',
   3, 'P', 'P', 'M',
   2, 'P', 'R',
   2, 's', 'r',
   2, 'S', 'v',
   2, 'W', 'b',
   3, 'V', '/', 'm',
   3, 'A', '/', 'm',
   3, 'g', 'a', 'l',
   2, '1',0x65E5,
   2, '2',0x65E5,
   2, '3',0x65E5,
   2, '4',0x65E5,
   2, '5',0x65E5,
   2, '6',0x65E5,
   2, '7',0x65E5,
   2, '8',0x65E5,
   2, '9',0x65E5,
   3, '1', '0',0x65E5,
   3, '1', '1',0x65E5,
   3, '1', '2',0x65E5,
   3, '1', '3',0x65E5,
   3, '1', '4',0x65E5,
   3, '1', '5',0x65E5,
   3, '1', '6',0x65E5,
   3, '1', '7',0x65E5,
   3, '1', '8',0x65E5,
   3, '1', '9',0x65E5,
   3, '2', '0',0x65E5,
   3, '2', '1',0x65E5,
   3, '2', '2',0x65E5,
   3, '2', '3',0x65E5,
   3, '2', '4',0x65E5,
   3, '2', '5',0x65E5,
   3, '2', '6',0x65E5,
   3, '2', '7',0x65E5,
   3, '2', '8',0x65E5,
   3, '2', '9',0x65E5,
   3, '3', '0',0x65E5,
   3, '3', '1',0x65E5,
   1,0x8C48,
   1,0x66F4,
   1,0x8ECA,
   1,0x8CC8,
   1,0x6ED1,
   1,0x4E32,
   1,0x53E5,
   1,0x9F9C,
   1,0x9F9C,
   1,0x5951,
   1,0x91D1,
   1,0x5587,
   1,0x5948,
   1,0x61F6,
   1,0x7669,
   1,0x7F85,
   1,0x863F,
   1,0x87BA,
   1,0x88F8,
   1,0x908F,
   1,0x6A02,
   1,0x6D1B,
   1,0x70D9,
   1,0x73DE,
   1,0x843D,
   1,0x916A,
   1,0x99F1,
   1,0x4E82,
   1,0x5375,
   1,0x6B04,
   1,0x721B,
   1,0x862D,
   1,0x9E1E,
   1,0x5D50,
   1,0x6FEB,
   1,0x85CD,
   1,0x8964,
   1,0x62C9,
   1,0x81D8,
   1,0x881F,
   1,0x5ECA,
   1,0x6717,
   1,0x6D6A,
   1,0x72FC,
   1,0x90CE,
   1,0x4F86,
   1,0x51B7,
   1,0x52DE,
   1,0x64C4,
   1,0x6AD3,
   1,0x7210,
   1,0x76E7,
   1,0x8001,
   1,0x8606,
   1,0x865C,
   1,0x8DEF,
   1,0x9732,
   1,0x9B6F,
   1,0x9DFA,
   1,0x788C,
   1,0x797F,
   1,0x7DA0,
   1,0x83C9,
   1,0x9304,
   1,0x9E7F,
   1,0x8AD6,
   1,0x58DF,
   1,0x5F04,
   1,0x7C60,
   1,0x807E,
   1,0x7262,
   1,0x78CA,
   1,0x8CC2,
   1,0x96F7,
   1,0x58D8,
   1,0x5C62,
   1,0x6A13,
   1,0x6DDA,
   1,0x6F0F,
   1,0x7D2F,
   1,0x7E37,
   1,0x964B,
   1,0x52D2,
   1,0x808B,
   1,0x51DC,
   1,0x51CC,
   1,0x7A1C,
   1,0x7DBE,
   1,0x83F1,
   1,0x9675,
   1,0x8B80,
   1,0x62CF,
   1,0x6A02,
   1,0x8AFE,
   1,0x4E39,
   1,0x5BE7,
   1,0x6012,
   1,0x7387,
   1,0x7570,
   1,0x5317,
   1,0x78FB,
   1,0x4FBF,
   1,0x5FA9,
   1,0x4E0D,
   1,0x6CCC,
   1,0x6578,
   1,0x7D22,
   1,0x53C3,
   1,0x585E,
   1,0x7701,
   1,0x8449,
   1,0x8AAA,
   1,0x6BBA,
   1,0x8FB0,
   1,0x6C88,
   1,0x62FE,
   1,0x82E5,
   1,0x63A0,
   1,0x7565,
   1,0x4EAE,
   1,0x5169,
   1,0x51C9,
   1,0x6881,
   1,0x7CE7,
   1,0x826F,
   1,0x8AD2,
   1,0x91CF,
   1,0x52F5,
   1,0x5442,
   1,0x5973,
   1,0x5EEC,
   1,0x65C5,
   1,0x6FFE,
   1,0x792A,
   1,0x95AD,
   1,0x9A6A,
   1,0x9E97,
   1,0x9ECE,
   1,0x529B,
   1,0x66C6,
   1,0x6B77,
   1,0x8F62,
   1,0x5E74,
   1,0x6190,
   1,0x6200,
   1,0x649A,
   1,0x6F23,
   1,0x7149,
   1,0x7489,
   1,0x79CA,
   1,0x7DF4,
   1,0x806F,
   1,0x8F26,
   1,0x84EE,
   1,0x9023,
   1,0x934A,
   1,0x5217,
   1,0x52A3,
   1,0x54BD,
   1,0x70C8,
   1,0x88C2,
   1,0x8AAA,
   1,0x5EC9,
   1,0x5FF5,
   1,0x637B,
   1,0x6BAE,
   1,0x7C3E,
   1,0x7375,
   1,0x4EE4,
   1,0x56F9,
   1,0x5BE7,
   1,0x5DBA,
   1,0x601C,
   1,0x73B2,
   1,0x7469,
   1,0x7F9A,
   1,0x8046,
   1,0x9234,
   1,0x96F6,
   1,0x9748,
   1,0x9818,
   1,0x4F8B,
   1,0x79AE,
   1,0x91B4,
   1,0x96B8,
   1,0x60E1,
   1,0x4E86,
   1,0x50DA,
   1,0x5BEE,
   1,0x5C3F,
   1,0x6599,
   1,0x6A02,
   1,0x71CE,
   1,0x7642,
   1,0x84FC,
   1,0x907C,
   1,0x9F8D,
   1,0x6688,
   1,0x962E,
   1,0x5289,
   1,0x677B,
   1,0x67F3,
   1,0x6D41,
   1,0x6E9C,
   1,0x7409,
   1,0x7559,
   1,0x786B,
   1,0x7D10,
   1,0x985E,
   1,0x516D,
   1,0x622E,
   1,0x9678,
   1,0x502B,
   1,0x5D19,
   1,0x6DEA,
   1,0x8F2A,
   1,0x5F8B,
   1,0x6144,
   1,0x6817,
   1,0x7387,
   1,0x9686,
   1,0x5229,
   1,0x540F,
   1,0x5C65,
   1,0x6613,
   1,0x674E,
   1,0x68A8,
   1,0x6CE5,
   1,0x7406,
   1,0x75E2,
   1,0x7F79,
   1,0x88CF,
   1,0x88E1,
   1,0x91CC,
   1,0x96E2,
   1,0x533F,
   1,0x6EBA,
   1,0x541D,
   1,0x71D0,
   1,0x7498,
   1,0x85FA,
   1,0x96A3,
   1,0x9C57,
   1,0x9E9F,
   1,0x6797,
   1,0x6DCB,
   1,0x81E8,
   1,0x7ACB,
   1,0x7B20,
   1,0x7C92,
   1,0x72C0,
   1,0x7099,
   1,0x8B58,
   1,0x4EC0,
   1,0x8336,
   1,0x523A,
   1,0x5207,
   1,0x5EA6,
   1,0x62D3,
   1,0x7CD6,
   1,0x5B85,
   1,0x6D1E,
   1,0x66B4,
   1,0x8F3B,
   1,0x884C,
   1,0x964D,
   1,0x898B,
   1,0x5ED3,
   1,0x5140,
   1,0x55C0,
   1,0x585A,
   1,0x6674,
   1,0x51DE,
   1,0x732A,
   1,0x76CA,
   1,0x793C,
   1,0x795E,
   1,0x7965,
   1,0x798F,
   1,0x9756,
   1,0x7CBE,
   1,0x7FBD,
   1,0x8612,
   1,0x8AF8,
   1,0x9038,
   1,0x90FD,
   1,0x98EF,
   1,0x98FC,
   1,0x9928,
   1,0x9DB4,
   1,0x4FAE,
   1,0x50E7,
   1,0x514D,
   1,0x52C9,
   1,0x52E4,
   1,0x5351,
   1,0x559D,
   1,0x5606,
   1,0x5668,
   1,0x5840,
   1,0x58A8,
   1,0x5C64,
   1,0x5C6E,
   1,0x6094,
   1,0x6168,
   1,0x618E,
   1,0x61F2,
   1,0x654F,
   1,0x65E2,
   1,0x6691,
   1,0x6885,
   1,0x6D77,
   1,0x6E1A,
   1,0x6F22,
   1,0x716E,
   1,0x722B,
   1,0x7422,
   1,0x7891,
   1,0x793E,
   1,0x7949,
   1,0x7948,
   1,0x7950,
   1,0x7956,
   1,0x795D,
   1,0x798D,
   1,0x798E,
   1,0x7A40,
   1,0x7A81,
   1,0x7BC0,
   1,0x7DF4,
   1,0x7E09,
   1,0x7E41,
   1,0x7F72,
   1,0x8005,
   1,0x81ED,
   1,0x8279,
   1,0x8279,
   1,0x8457,
   1,0x8910,
   1,0x8996,
   1,0x8B01,
   1,0x8B39,
   1,0x8CD3,
   1,0x8D08,
   1,0x8FB6,
   1,0x9038,
   1,0x96E3,
   1,0x97FF,
   1,0x983B,
   2, 'f', 'f',
   2, 'f', 'i',
   2, 'f', 'l',
   3, 'f', 'f', 'i',
   3, 'f', 'f', 'l',
   2,0x017F, 't',
   2, 's', 't',
   2,0x0574,0x0576,
   2,0x0574,0x0565,
   2,0x0574,0x056B,
   2,0x057E,0x0576,
   2,0x0574,0x056D,
   1,0x05E2,
   1,0x05D0,
   1,0x05D3,
   1,0x05D4,
   1,0x05DB,
   1,0x05DC,
   1,0x05DD,
   1,0x05E8,
   1,0x05EA,
   1, '+',
   2,0x05D0,0x05DC,
   1,0x203E,
   1,0x203E,
   1,0x203E,
   1,0x203E,
   1, '_',
   1, '_',
   1, '_',
   1, ',',
   1,0x3001,
   1, '.',
   1, ';',
   1, ':',
   1, '?',
   1, '!',
   1,0x2014,
   1, '(',
   1, ')',
   1, '{',
   1, '}',
   1,0x3014,
   1,0x3015,
   1, '#',
   1, '&',
   1, '*',
   1, '+',
   1, '-',
   1, '<',
   1, '>',
   1, '=',
   1,'\\',
   1, '$',
   1, '%',
   1, '@',
   1, '!',
   1, '"',
   1, '#',
   1, '$',
   1, '%',
   1, '&',
   1,'\'',
   1, '(',
   1, ')',
   1, '*',
   1, '+',
   1, ',',
   1, '-',
   1, '.',
   1, '/',
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1, ':',
   1, ';',
   1, '<',
   1, '=',
   1, '>',
   1, '?',
   1, '@',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, '[',
   1,'\\',
   1, ']',
   1, '^',
   1, '_',
   1, '`',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, '{',
   1, '|',
   1, '}',
   1, '~',
   1,0x2985,
   1,0x2986,
   1,0x3002,
   1,0x300C,
   1,0x300D,
   1,0x3001,
   1,0x30FB,
   1,0x30F2,
   1,0x30A1,
   1,0x30A3,
   1,0x30A5,
   1,0x30A7,
   1,0x30A9,
   1,0x30E3,
   1,0x30E5,
   1,0x30E7,
   1,0x30C3,
   1,0x30FC,
   1,0x30A2,
   1,0x30A4,
   1,0x30A6,
   1,0x30A8,
   1,0x30AA,
   1,0x30AB,
   1,0x30AD,
   1,0x30AF,
   1,0x30B1,
   1,0x30B3,
   1,0x30B5,
   1,0x30B7,
   1,0x30B9,
   1,0x30BB,
   1,0x30BD,
   1,0x30BF,
   1,0x30C1,
   1,0x30C4,
   1,0x30C6,
   1,0x30C8,
   1,0x30CA,
   1,0x30CB,
   1,0x30CC,
   1,0x30CD,
   1,0x30CE,
   1,0x30CF,
   1,0x30D2,
   1,0x30D5,
   1,0x30D8,
   1,0x30DB,
   1,0x30DE,
   1,0x30DF,
   1,0x30E0,
   1,0x30E1,
   1,0x30E2,
   1,0x30E4,
   1,0x30E6,
   1,0x30E8,
   1,0x30E9,
   1,0x30EA,
   1,0x30EB,
   1,0x30EC,
   1,0x30ED,
   1,0x30EF,
   1,0x30F3,
   1,0x3099,
   1,0x309A,
   1,0x3164,
   1,0x3131,
   1,0x3132,
   1,0x3133,
   1,0x3134,
   1,0x3135,
   1,0x3136,
   1,0x3137,
   1,0x3138,
   1,0x3139,
   1,0x313A,
   1,0x313B,
   1,0x313C,
   1,0x313D,
   1,0x313E,
   1,0x313F,
   1,0x3140,
   1,0x3141,
   1,0x3142,
   1,0x3143,
   1,0x3144,
   1,0x3145,
   1,0x3146,
   1,0x3147,
   1,0x3148,
   1,0x3149,
   1,0x314A,
   1,0x314B,
   1,0x314C,
   1,0x314D,
   1,0x314E,
   1,0x314F,
   1,0x3150,
   1,0x3151,
   1,0x3152,
   1,0x3153,
   1,0x3154,
   1,0x3155,
   1,0x3156,
   1,0x3157,
   1,0x3158,
   1,0x3159,
   1,0x315A,
   1,0x315B,
   1,0x315C,
   1,0x315D,
   1,0x315E,
   1,0x315F,
   1,0x3160,
   1,0x3161,
   1,0x3162,
   1,0x3163,
   1,0xA2,
   1,0xA3,
   1,0xAC,
   1,0xAF,
   1,0xA6,
   1,0xA5,
   1,0x20A9,
   1,0x2502,
   1,0x2190,
   1,0x2191,
   1,0x2192,
   1,0x2193,
   1,0x25A0,
   1,0x25CB,
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'C',
   1, 'D',
   1, 'G',
   1, 'J',
   1, 'K',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'f',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'O',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1, 'A',
   1, 'B',
   1, 'C',
   1, 'D',
   1, 'E',
   1, 'F',
   1, 'G',
   1, 'H',
   1, 'I',
   1, 'J',
   1, 'K',
   1, 'L',
   1, 'M',
   1, 'N',
   1, 'O',
   1, 'P',
   1, 'Q',
   1, 'R',
   1, 'S',
   1, 'T',
   1, 'U',
   1, 'V',
   1, 'W',
   1, 'X',
   1, 'Y',
   1, 'Z',
   1, 'a',
   1, 'b',
   1, 'c',
   1, 'd',
   1, 'e',
   1, 'f',
   1, 'g',
   1, 'h',
   1, 'i',
   1, 'j',
   1, 'k',
   1, 'l',
   1, 'm',
   1, 'n',
   1, 'o',
   1, 'p',
   1, 'q',
   1, 'r',
   1, 's',
   1, 't',
   1, 'u',
   1, 'v',
   1, 'w',
   1, 'x',
   1, 'y',
   1, 'z',
   1,0x0391,
   1,0x0392,
   1,0x0393,
   1,0x0394,
   1,0x0395,
   1,0x0396,
   1,0x0397,
   1,0x0398,
   1,0x0399,
   1,0x039A,
   1,0x039B,
   1,0x039C,
   1,0x039D,
   1,0x039E,
   1,0x039F,
   1,0x03A0,
   1,0x03A1,
   1,0x03F4,
   1,0x03A3,
   1,0x03A4,
   1,0x03A5,
   1,0x03A6,
   1,0x03A7,
   1,0x03A8,
   1,0x03A9,
   1,0x2207,
   1,0x03B1,
   1,0x03B2,
   1,0x03B3,
   1,0x03B4,
   1,0x03B5,
   1,0x03B6,
   1,0x03B7,
   1,0x03B8,
   1,0x03B9,
   1,0x03BA,
   1,0x03BB,
   1,0x03BC,
   1,0x03BD,
   1,0x03BE,
   1,0x03BF,
   1,0x03C0,
   1,0x03C1,
   1,0x03C2,
   1,0x03C3,
   1,0x03C4,
   1,0x03C5,
   1,0x03C6,
   1,0x03C7,
   1,0x03C8,
   1,0x03C9,
   1,0x2202,
   1,0x03F5,
   1,0x03D1,
   1,0x03F0,
   1,0x03D5,
   1,0x03F1,
   1,0x03D6,
   1,0x0391,
   1,0x0392,
   1,0x0393,
   1,0x0394,
   1,0x0395,
   1,0x0396,
   1,0x0397,
   1,0x0398,
   1,0x0399,
   1,0x039A,
   1,0x039B,
   1,0x039C,
   1,0x039D,
   1,0x039E,
   1,0x039F,
   1,0x03A0,
   1,0x03A1,
   1,0x03F4,
   1,0x03A3,
   1,0x03A4,
   1,0x03A5,
   1,0x03A6,
   1,0x03A7,
   1,0x03A8,
   1,0x03A9,
   1,0x2207,
   1,0x03B1,
   1,0x03B2,
   1,0x03B3,
   1,0x03B4,
   1,0x03B5,
   1,0x03B6,
   1,0x03B7,
   1,0x03B8,
   1,0x03B9,
   1,0x03BA,
   1,0x03BB,
   1,0x03BC,
   1,0x03BD,
   1,0x03BE,
   1,0x03BF,
   1,0x03C0,
   1,0x03C1,
   1,0x03C2,
   1,0x03C3,
   1,0x03C4,
   1,0x03C5,
   1,0x03C6,
   1,0x03C7,
   1,0x03C8,
   1,0x03C9,
   1,0x2202,
   1,0x03F5,
   1,0x03D1,
   1,0x03F0,
   1,0x03D5,
   1,0x03F1,
   1,0x03D6,
   1,0x0391,
   1,0x0392,
   1,0x0393,
   1,0x0394,
   1,0x0395,
   1,0x0396,
   1,0x0397,
   1,0x0398,
   1,0x0399,
   1,0x039A,
   1,0x039B,
   1,0x039C,
   1,0x039D,
   1,0x039E,
   1,0x039F,
   1,0x03A0,
   1,0x03A1,
   1,0x03F4,
   1,0x03A3,
   1,0x03A4,
   1,0x03A5,
   1,0x03A6,
   1,0x03A7,
   1,0x03A8,
   1,0x03A9,
   1,0x2207,
   1,0x03B1,
   1,0x03B2,
   1,0x03B3,
   1,0x03B4,
   1,0x03B5,
   1,0x03B6,
   1,0x03B7,
   1,0x03B8,
   1,0x03B9,
   1,0x03BA,
   1,0x03BB,
   1,0x03BC,
   1,0x03BD,
   1,0x03BE,
   1,0x03BF,
   1,0x03C0,
   1,0x03C1,
   1,0x03C2,
   1,0x03C3,
   1,0x03C4,
   1,0x03C5,
   1,0x03C6,
   1,0x03C7,
   1,0x03C8,
   1,0x03C9,
   1,0x2202,
   1,0x03F5,
   1,0x03D1,
   1,0x03F0,
   1,0x03D5,
   1,0x03F1,
   1,0x03D6,
   1,0x0391,
   1,0x0392,
   1,0x0393,
   1,0x0394,
   1,0x0395,
   1,0x0396,
   1,0x0397,
   1,0x0398,
   1,0x0399,
   1,0x039A,
   1,0x039B,
   1,0x039C,
   1,0x039D,
   1,0x039E,
   1,0x039F,
   1,0x03A0,
   1,0x03A1,
   1,0x03F4,
   1,0x03A3,
   1,0x03A4,
   1,0x03A5,
   1,0x03A6,
   1,0x03A7,
   1,0x03A8,
   1,0x03A9,
   1,0x2207,
   1,0x03B1,
   1,0x03B2,
   1,0x03B3,
   1,0x03B4,
   1,0x03B5,
   1,0x03B6,
   1,0x03B7,
   1,0x03B8,
   1,0x03B9,
   1,0x03BA,
   1,0x03BB,
   1,0x03BC,
   1,0x03BD,
   1,0x03BE,
   1,0x03BF,
   1,0x03C0,
   1,0x03C1,
   1,0x03C2,
   1,0x03C3,
   1,0x03C4,
   1,0x03C5,
   1,0x03C6,
   1,0x03C7,
   1,0x03C8,
   1,0x03C9,
   1,0x2202,
   1,0x03F5,
   1,0x03D1,
   1,0x03F0,
   1,0x03D5,
   1,0x03F1,
   1,0x03D6,
   1,0x0391,
   1,0x0392,
   1,0x0393,
   1,0x0394,
   1,0x0395,
   1,0x0396,
   1,0x0397,
   1,0x0398,
   1,0x0399,
   1,0x039A,
   1,0x039B,
   1,0x039C,
   1,0x039D,
   1,0x039E,
   1,0x039F,
   1,0x03A0,
   1,0x03A1,
   1,0x03F4,
   1,0x03A3,
   1,0x03A4,
   1,0x03A5,
   1,0x03A6,
   1,0x03A7,
   1,0x03A8,
   1,0x03A9,
   1,0x2207,
   1,0x03B1,
   1,0x03B2,
   1,0x03B3,
   1,0x03B4,
   1,0x03B5,
   1,0x03B6,
   1,0x03B7,
   1,0x03B8,
   1,0x03B9,
   1,0x03BA,
   1,0x03BB,
   1,0x03BC,
   1,0x03BD,
   1,0x03BE,
   1,0x03BF,
   1,0x03C0,
   1,0x03C1,
   1,0x03C2,
   1,0x03C3,
   1,0x03C4,
   1,0x03C5,
   1,0x03C6,
   1,0x03C7,
   1,0x03C8,
   1,0x03C9,
   1,0x2202,
   1,0x03F5,
   1,0x03D1,
   1,0x03F0,
   1,0x03D5,
   1,0x03F1,
   1,0x03D6,
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1, '0',
   1, '1',
   1, '2',
   1, '3',
   1, '4',
   1, '5',
   1, '6',
   1, '7',
   1, '8',
   1, '9',
   1,0x4E3D,
   1,0x4E38,
   1,0x4E41,
   1,0x20122,
   1,0x4F60,
   1,0x4FAE,
   1,0x4FBB,
   1,0x5002,
   1,0x507A,
   1,0x5099,
   1,0x50E7,
   1,0x50CF,
   1,0x349E,
   1,0x2063A,
   1,0x514D,
   1,0x5154,
   1,0x5164,
   1,0x5177,
   1,0x2051C,
   1,0x34B9,
   1,0x5167,
   1,0x518D,
   1,0x2054B,
   1,0x5197,
   1,0x51A4,
   1,0x4ECC,
   1,0x51AC,
   1,0x51B5,
   1,0x291DF,
   1,0x51F5,
   1,0x5203,
   1,0x34DF,
   1,0x523B,
   1,0x5246,
   1,0x5272,
   1,0x5277,
   1,0x3515,
   1,0x52C7,
   1,0x52C9,
   1,0x52E4,
   1,0x52FA,
   1,0x5305,
   1,0x5306,
   1,0x5317,
   1,0x5349,
   1,0x5351,
   1,0x535A,
   1,0x5373,
   1,0x537D,
   1,0x537F,
   1,0x537F,
   1,0x537F,
   1,0x20A2C,
   1,0x7070,
   1,0x53CA,
   1,0x53DF,
   1,0x20B63,
   1,0x53EB,
   1,0x53F1,
   1,0x5406,
   1,0x549E,
   1,0x5438,
   1,0x5448,
   1,0x5468,
   1,0x54A2,
   1,0x54F6,
   1,0x5510,
   1,0x5553,
   1,0x5563,
   1,0x5584,
   1,0x5584,
   1,0x5599,
   1,0x55AB,
   1,0x55B3,
   1,0x55C2,
   1,0x5716,
   1,0x5606,
   1,0x5717,
   1,0x5651,
   1,0x5674,
   1,0x5207,
   1,0x58EE,
   1,0x57CE,
   1,0x57F4,
   1,0x580D,
   1,0x578B,
   1,0x5832,
   1,0x5831,
   1,0x58AC,
   1,0x214E4,
   1,0x58F2,
   1,0x58F7,
   1,0x5906,
   1,0x591A,
   1,0x5922,
   1,0x5962,
   1,0x216A8,
   1,0x216EA,
   1,0x59EC,
   1,0x5A1B,
   1,0x5A27,
   1,0x59D8,
   1,0x5A66,
   1,0x36EE,
   1,0x36FC,
   1,0x5B08,
   1,0x5B3E,
   1,0x5B3E,
   1,0x219C8,
   1,0x5BC3,
   1,0x5BD8,
   1,0x5BE7,
   1,0x5BF3,
   1,0x21B18,
   1,0x5BFF,
   1,0x5C06,
   1,0x5F53,
   1,0x5C22,
   1,0x3781,
   1,0x5C60,
   1,0x5C6E,
   1,0x5CC0,
   1,0x5C8D,
   1,0x21DE4,
   1,0x5D43,
   1,0x21DE6,
   1,0x5D6E,
   1,0x5D6B,
   1,0x5D7C,
   1,0x5DE1,
   1,0x5DE2,
   1,0x382F,
   1,0x5DFD,
   1,0x5E28,
   1,0x5E3D,
   1,0x5E69,
   1,0x3862,
   1,0x22183,
   1,0x387C,
   1,0x5EB0,
   1,0x5EB3,
   1,0x5EB6,
   1,0x5ECA,
   1,0x2A392,
   1,0x5EFE,
   1,0x22331,
   1,0x22331,
   1,0x8201,
   1,0x5F22,
   1,0x5F22,
   1,0x38C7,
   1,0x232B8,
   1,0x261DA,
   1,0x5F62,
   1,0x5F6B,
   1,0x38E3,
   1,0x5F9A,
   1,0x5FCD,
   1,0x5FD7,
   1,0x5FF9,
   1,0x6081,
   1,0x393A,
   1,0x391C,
   1,0x6094,
   1,0x226D4,
   1,0x60C7,
   1,0x6148,
   1,0x614C,
   1,0x614E,
   1,0x614C,
   1,0x617A,
   1,0x618E,
   1,0x61B2,
   1,0x61A4,
   1,0x61AF,
   1,0x61DE,
   1,0x61F2,
   1,0x61F6,
   1,0x6210,
   1,0x621B,
   1,0x625D,
   1,0x62B1,
   1,0x62D4,
   1,0x6350,
   1,0x22B0C,
   1,0x633D,
   1,0x62FC,
   1,0x6368,
   1,0x6383,
   1,0x63E4,
   1,0x22BF1,
   1,0x6422,
   1,0x63C5,
   1,0x63A9,
   1,0x3A2E,
   1,0x6469,
   1,0x647E,
   1,0x649D,
   1,0x6477,
   1,0x3A6C,
   1,0x654F,
   1,0x656C,
   1,0x2300A,
   1,0x65E3,
   1,0x66F8,
   1,0x6649,
   1,0x3B19,
   1,0x6691,
   1,0x3B08,
   1,0x3AE4,
   1,0x5192,
   1,0x5195,
   1,0x6700,
   1,0x669C,
   1,0x80AD,
   1,0x43D9,
   1,0x6717,
   1,0x671B,
   1,0x6721,
   1,0x675E,
   1,0x6753,
   1,0x233C3,
   1,0x3B49,
   1,0x67FA,
   1,0x6785,
   1,0x6852,
   1,0x6885,
   1,0x2346D,
   1,0x688E,
   1,0x681F,
   1,0x6914,
   1,0x3B9D,
   1,0x6942,
   1,0x69A3,
   1,0x69EA,
   1,0x6AA8,
   1,0x236A3,
   1,0x6ADB,
   1,0x3C18,
   1,0x6B21,
   1,0x238A7,
   1,0x6B54,
   1,0x3C4E,
   1,0x6B72,
   1,0x6B9F,
   1,0x6BBA,
   1,0x6BBB,
   1,0x23A8D,
   1,0x21D0B,
   1,0x23AFA,
   1,0x6C4E,
   1,0x23CBC,
   1,0x6CBF,
   1,0x6CCD,
   1,0x6C67,
   1,0x6D16,
   1,0x6D3E,
   1,0x6D77,
   1,0x6D41,
   1,0x6D69,
   1,0x6D78,
   1,0x6D85,
   1,0x23D1E,
   1,0x6D34,
   1,0x6E2F,
   1,0x6E6E,
   1,0x3D33,
   1,0x6ECB,
   1,0x6EC7,
   1,0x23ED1,
   1,0x6DF9,
   1,0x6F6E,
   1,0x23F5E,
   1,0x23F8E,
   1,0x6FC6,
   1,0x7039,
   1,0x701E,
   1,0x701B,
   1,0x3D96,
   1,0x704A,
   1,0x707D,
   1,0x7077,
   1,0x70AD,
   1,0x20525,
   1,0x7145,
   1,0x24263,
   1,0x719C,
   1,0x243AB,
   1,0x7228,
   1,0x7235,
   1,0x7250,
   1,0x24608,
   1,0x7280,
   1,0x7295,
   1,0x24735,
   1,0x24814,
   1,0x737A,
   1,0x738B,
   1,0x3EAC,
   1,0x73A5,
   1,0x3EB8,
   1,0x3EB8,
   1,0x7447,
   1,0x745C,
   1,0x7471,
   1,0x7485,
   1,0x74CA,
   1,0x3F1B,
   1,0x7524,
   1,0x24C36,
   1,0x753E,
   1,0x24C92,
   1,0x7570,
   1,0x2219F,
   1,0x7610,
   1,0x24FA1,
   1,0x24FB8,
   1,0x25044,
   1,0x3FFC,
   1,0x4008,
   1,0x76F4,
   1,0x250F3,
   1,0x250F2,
   1,0x25119,
   1,0x25133,
   1,0x771E,
   1,0x771F,
   1,0x771F,
   1,0x774A,
   1,0x4039,
   1,0x778B,
   1,0x4046,
   1,0x4096,
   1,0x2541D,
   1,0x784E,
   1,0x788C,
   1,0x78CC,
   1,0x40E3,
   1,0x25626,
   1,0x7956,
   1,0x2569A,
   1,0x256C5,
   1,0x798F,
   1,0x79EB,
   1,0x412F,
   1,0x7A40,
   1,0x7A4A,
   1,0x7A4F,
   1,0x2597C,
   1,0x25AA7,
   1,0x25AA7,
   1,0x7AEE,
   1,0x4202,
   1,0x25BAB,
   1,0x7BC6,
   1,0x7BC9,
   1,0x4227,
   1,0x25C80,
   1,0x7CD2,
   1,0x42A0,
   1,0x7CE8,
   1,0x7CE3,
   1,0x7D00,
   1,0x25F86,
   1,0x7D63,
   1,0x4301,
   1,0x7DC7,
   1,0x7E02,
   1,0x7E45,
   1,0x4334,
   1,0x26228,
   1,0x26247,
   1,0x4359,
   1,0x262D9,
   1,0x7F7A,
   1,0x2633E,
   1,0x7F95,
   1,0x7FFA,
   1,0x8005,
   1,0x264DA,
   1,0x26523,
   1,0x8060,
   1,0x265A8,
   1,0x8070,
   1,0x2335F,
   1,0x43D5,
   1,0x80B2,
   1,0x8103,
   1,0x440B,
   1,0x813E,
   1,0x5AB5,
   1,0x267A7,
   1,0x267B5,
   1,0x23393,
   1,0x2339C,
   1,0x8201,
   1,0x8204,
   1,0x8F9E,
   1,0x446B,
   1,0x8291,
   1,0x828B,
   1,0x829D,
   1,0x52B3,
   1,0x82B1,
   1,0x82B3,
   1,0x82BD,
   1,0x82E6,
   1,0x26B3C,
   1,0x82E5,
   1,0x831D,
   1,0x8363,
   1,0x83AD,
   1,0x8323,
   1,0x83BD,
   1,0x83E7,
   1,0x8457,
   1,0x8353,
   1,0x83CA,
   1,0x83CC,
   1,0x83DC,
   1,0x26C36,
   1,0x26D6B,
   1,0x26CD5,
   1,0x452B,
   1,0x84F1,
   1,0x84F3,
   1,0x8516,
   1,0x273CA,
   1,0x8564,
   1,0x26F2C,
   1,0x455D,
   1,0x4561,
   1,0x26FB1,
   1,0x270D2,
   1,0x456B,
   1,0x8650,
   1,0x865C,
   1,0x8667,
   1,0x8669,
   1,0x86A9,
   1,0x8688,
   1,0x870E,
   1,0x86E2,
   1,0x8779,
   1,0x8728,
   1,0x876B,
   1,0x8786,
   1,0x45D7,
   1,0x87E1,
   1,0x8801,
   1,0x45F9,
   1,0x8860,
   1,0x8863,
   1,0x27667,
   1,0x88D7,
   1,0x88DE,
   1,0x4635,
   1,0x88FA,
   1,0x34BB,
   1,0x278AE,
   1,0x27966,
   1,0x46BE,
   1,0x46C7,
   1,0x8AA0,
   1,0x8AED,
   1,0x8B8A,
   1,0x8C55,
   1,0x27CA8,
   1,0x8CAB,
   1,0x8CC1,
   1,0x8D1B,
   1,0x8D77,
   1,0x27F2F,
   1,0x20804,
   1,0x8DCB,
   1,0x8DBC,
   1,0x8DF0,
   1,0x208DE,
   1,0x8ED4,
   1,0x8F38,
   1,0x285D2,
   1,0x285ED,
   1,0x9094,
   1,0x90F1,
   1,0x9111,
   1,0x2872E,
   1,0x911B,
   1,0x9238,
   1,0x92D7,
   1,0x92D8,
   1,0x927C,
   1,0x93F9,
   1,0x9415,
   1,0x28BFA,
   1,0x958B,
   1,0x4995,
   1,0x95B7,
   1,0x28D77,
   1,0x49E6,
   1,0x96C3,
   1,0x5DB2,
   1,0x9723,
   1,0x29145,
   1,0x2921A,
   1,0x4A6E,
   1,0x4A76,
   1,0x97E0,
   1,0x2940A,
   1,0x4AB2,
   1,0x29496,
   1,0x980B,
   1,0x980B,
   1,0x9829,
   1,0x295B6,
   1,0x98E2,
   1,0x4B33,
   1,0x9929,
   1,0x99A7,
   1,0x99C2,
   1,0x99FE,
   1,0x4BCE,
   1,0x29B30,
   1,0x9B12,
   1,0x9C40,
   1,0x9CFD,
   1,0x4CCE,
   1,0x4CED,
   1,0x9D67,
   1,0x2A0CE,
   1,0x4CF8,
   1,0x2A105,
   1,0x2A20E,
   1,0x2A291,
   1,0x9EBB,
   1,0x4D56,
   1,0x9EF9,
   1,0x9EFE,
   1,0x9F05,
   1,0x9F0F,
   1,0x9F16,
   1,0x9F3B,
   1,0x2A600,
};

static const short translit_page00[344] = {
     0,    2,    4,    6,   -1,    9,   13,   15, /* 0xa0-0xa7 */
    18,   20,   24,   26,   29,   33,   35,   -1, /* 0xa8-0xaf */
    39,   42,   46,   49,   52,   54,   56,   58, /* 0xb0-0xb7 */
    60,   62,   65,   67,   70,   76,   82,   88, /* 0xb8-0xbf */
    90,   93,   96,   99,  102,  105,  107,  110, /* 0xc0-0xc7 */
   112,  115,  118,  121,  124,  127,  130,  133, /* 0xc8-0xcf */
   136,  138,  141,  144,  147,  150,  153,  156, /* 0xd0-0xd7 */
   158,  160,  163,  166,  169,  172,  175,  178, /* 0xd8-0xdf */
   181,  184,  187,  190,  193,  196,  198,  201, /* 0xe0-0xe7 */
   203,  206,  209,  212,  215,  218,  221,  224, /* 0xe8-0xef */
   227,  229,  232,  235,  238,  241,  244,  247, /* 0xf0-0xf7 */
   249,  251,  254,  257,  260,  263,  266,  269, /* 0xf8-0xff */
  /* 0x0100 */
   272,  274,  276,  278,  280,  282,  284,  287, /* 0x00-0x07 */
   290,  293,  296,  298,  300,  302,  304,  306, /* 0x08-0x0f */
   308,  310,  312,  314,  316,  318,  320,  322, /* 0x10-0x17 */
   324,  326,  328,  330,  332,  335,  338,  340, /* 0x18-0x1f */
   342,  344,  346,  348,  350,  353,  356,  358, /* 0x20-0x27 */
   360,  363,  366,  368,  370,  372,  374,  376, /* 0x28-0x2f */
   378,  380,  382,  385,  388,  391,  394,  396, /* 0x30-0x37 */
    -1,  398,  400,  402,  404,  406,  408,  410, /* 0x38-0x3f */
   412,  414,  416,  418,  421,  424,  426,  428, /* 0x40-0x47 */
   430,  432,   -1,   -1,  435,  437,  439,  441, /* 0x48-0x4f */
   443,  446,  449,  452,  455,  458,  461,  463, /* 0x50-0x57 */
   465,  467,  469,  472,  475,  478,  481,  483, /* 0x58-0x5f */
   485,  487,  489,  491,  493,  495,  497,  499, /* 0x60-0x67 */
   501,  504,  507,  509,  511,  513,  515,  517, /* 0x68-0x6f */
   519,  522,  525,  527,  529,  532,  535,  538, /* 0x70-0x77 */
   541,  544,  547,  550,  552,  554,  556,  558, /* 0x78-0x7f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x80-0x87 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x88-0x8f */
    -1,   -1,  560,   -1,   -1,   -1,   -1,   -1, /* 0x90-0x97 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x98-0x9f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xa0-0xa7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xa8-0xaf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb0-0xb7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb8-0xbf */
    -1,   -1,   -1,   -1,  562,  565,  568,  571, /* 0xc0-0xc7 */
   574,  577,  580,  583,  586,   -1,   -1,   -1, /* 0xc8-0xcf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd0-0xd7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd8-0xdf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xe0-0xe7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xe8-0xef */
    -1,  589,  592,  595,   -1,   -1,   -1,   -1, /* 0xf0-0xf7 */
};
static const short translit_page02[8] = {
   598,  600,  602,  604,   -1,   -1,   -1,   -1, /* 0x18-0x1f */
};
static const short translit_page02_1[40] = {
    -1,  606,  608,  610,  612,  614,   -1,   -1, /* 0xb8-0xbf */
    -1,   -1,   -1,   -1,   -1,   -1,  616,   -1, /* 0xc0-0xc7 */
   618,  620,  622,  624,   -1,  626,   -1,   -1, /* 0xc8-0xcf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd0-0xd7 */
    -1,   -1,   -1,   -1,  628,  630,   -1,   -1, /* 0xd8-0xdf */
};
static const short translit_page03[48] = {
   632,  634,  636,   -1,   -1,  638,  640,   -1, /* 0xd0-0xd7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd8-0xdf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xe0-0xe7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xe8-0xef */
   642,  644,  646,   -1,  648,  650,   -1,   -1, /* 0xf0-0xf7 */
    -1,  652,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xf8-0xff */
};
static const short translit_page05[8] = {
   657,  660,  663,   -1,   -1,   -1,   -1,   -1, /* 0xf0-0xf7 */
};
static const short translit_page06[16] = {
    -1,   -1,   -1,   -1,   -1,  666,  669,  672, /* 0x70-0x77 */
   675,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x78-0x7f */
};
static const short translit_page0e[48] = {
    -1,   -1,   -1,  681,   -1,   -1,   -1,   -1, /* 0xb0-0xb7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb8-0xbf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc0-0xc7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc8-0xcf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd0-0xd7 */
    -1,   -1,   -1,   -1,  684,  687,   -1,   -1, /* 0xd8-0xdf */
};
static const short translit_page0f[16] = {
    -1,   -1,   -1,   -1,   -1,   -1,   -1,  690, /* 0x70-0x77 */
    -1,  693,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x78-0x7f */
};
static const short translit_page1e[160] = {
    -1,   -1,  696,  698,   -1,   -1,   -1,   -1, /* 0x00-0x07 */
    -1,   -1,  700,  702,   -1,   -1,   -1,   -1, /* 0x08-0x0f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x10-0x17 */
    -1,   -1,   -1,   -1,   -1,   -1,  704,  706, /* 0x18-0x1f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x28-0x2f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x30-0x37 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x38-0x3f */
   708,  710,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x40-0x47 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,   -1,   -1,   -1,  712,  714, /* 0x50-0x57 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x58-0x5f */
   716,  718,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x60-0x67 */
    -1,   -1,  720,  722,   -1,   -1,   -1,   -1, /* 0x68-0x6f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x70-0x77 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x78-0x7f */
   724,  727,  730,  733,  736,  739,   -1,   -1, /* 0x80-0x87 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x88-0x8f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x90-0x97 */
    -1,   -1,  742,   -1,   -1,   -1,   -1,   -1, /* 0x98-0x9f */
};
static const short translit_page1e_1[8] = {
    -1,   -1,  745,  748,   -1,   -1,   -1,   -1, /* 0xf0-0xf7 */
};
static const short translit_page20[88] = {
    -1,   -1,  751,  753,  755,  757,  759,   -1, /* 0x00-0x07 */
   761,  763,  765,   -1,   -1,   -1,   -1,   -1, /* 0x08-0x0f */
   767,  769,  771,  773,  775,  777,   -1,   -1, /* 0x10-0x17 */
   779,  781,  783,  785,  787,  789,  791,  793, /* 0x18-0x1f */
   795,   -1,  797,   -1,  799,  801,  804,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x28-0x2f */
   808,   -1,  813,  815,  818,   -1,  822,  825, /* 0x30-0x37 */
    -1,  829,  831,   -1,  833,   -1,   -1,   -1, /* 0x38-0x3f */
    -1,   -1,   -1,   -1,  836,   -1,   -1,  838, /* 0x40-0x47 */
   841,  844,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,  847, /* 0x50-0x57 */
};
static const short translit_page20_1[8] = {
   852,   -1,   -1,  855,  860,   -1,   -1,   -1, /* 0xa8-0xaf */
};
static const short translit_page21[216] = {
   864,  868,  872,  874,   -1,  877,  881,  885, /* 0x00-0x07 */
    -1,  887,  890,  892,  894,  896,  898,  900, /* 0x08-0x0f */
   902,  904,  906,  908,   -1,  910,  912,   -1, /* 0x10-0x17 */
    -1,  915,  917,  919,  921,  923,   -1,   -1, /* 0x18-0x1f */
    -1,  925,  929,   -1,  932,   -1,  934,   -1, /* 0x20-0x27 */
   938,   -1,   -1,   -1,  940,  942,  944,  946, /* 0x28-0x2f */
   948,  950,   -1,  952,  954,  956,  958,  960, /* 0x30-0x37 */
   962,  964,   -1,  966,   -1,  970,  972,  974, /* 0x38-0x3f */
   976,   -1,   -1,   -1,   -1,  978,  980,  982, /* 0x40-0x47 */
   984,  986,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,  988,  994, 1000, 1006, 1012, /* 0x50-0x57 */
  1018, 1024, 1030, 1036, 1042, 1048, 1054, 1060, /* 0x58-0x5f */
  1064, 1066, 1069, 1073, 1076, 1078, 1081, 1085, /* 0x60-0x67 */
  1090, 1093, 1095, 1098, 1102, 1104, 1106, 1108, /* 0x68-0x6f */
  1110, 1112, 1115, 1119, 1122, 1124, 1127, 1131, /* 0x70-0x77 */
  1136, 1139, 1141, 1144, 1148, 1150, 1152, 1154, /* 0x78-0x7f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x80-0x87 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x88-0x8f */
  1156, 1159, 1161, 1164, 1166,   -1,   -1,   -1, /* 0x90-0x97 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x98-0x9f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xa0-0xa7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xa8-0xaf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb0-0xb7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb8-0xbf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc0-0xc7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc8-0xcf */
  1170,   -1, 1173,   -1, 1176,   -1,   -1,   -1, /* 0xd0-0xd7 */
};
static const short translit_page22[96] = {
    -1,   -1, 1180,   -1,   -1, 1182, 1184, 1186, /* 0x10-0x17 */
    -1, 1188,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x18-0x1f */
    -1,   -1,   -1, 1190,   -1,   -1,   -1,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1, 1192, 1195,   -1, 1199, /* 0x28-0x2f */
  1202,   -1,   -1,   -1,   -1,   -1, 1206,   -1, /* 0x30-0x37 */
    -1,   -1,   -1,   -1, 1208,   -1,   -1,   -1, /* 0x38-0x3f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x40-0x47 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x50-0x57 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x58-0x5f */
  1210,   -1,   -1,   -1, 1213, 1216,   -1,   -1, /* 0x60-0x67 */
    -1,   -1, 1219, 1222,   -1,   -1,   -1,   -1, /* 0x68-0x6f */
};
static const short translit_page22_1[48] = {
    -1,   -1,   -1,   -1,   -1, 1225,   -1,   -1, /* 0xc0-0xc7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc8-0xcf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd0-0xd7 */
  1227, 1231,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd8-0xdf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xe0-0xe7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1, 1235, /* 0xe8-0xef */
};
static const short translit_page24[240] = {
  1239, 1245, 1251, 1257, 1263, 1269, 1275, 1281, /* 0x00-0x07 */
  1287, 1292, 1297, 1302, 1307, 1312, 1317, 1322, /* 0x08-0x0f */
  1327, 1333, 1339, 1345, 1351, 1357, 1363, 1369, /* 0x10-0x17 */
  1375, 1381, 1386, 1392, 1398, 1403, 1408, 1413, /* 0x18-0x1f */
  1418, 1423,   -1,   -1, 1429,   -1,   -1,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x28-0x2f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x30-0x37 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x38-0x3f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x40-0x47 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x50-0x57 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x58-0x5f */
  1434, 1438, 1442, 1446, 1450, 1454, 1458, 1462, /* 0x60-0x67 */
  1466, 1470, 1475, 1480, 1485, 1490, 1495, 1500, /* 0x68-0x6f */
  1505, 1510, 1515, 1520, 1525, 1529, 1533, 1537, /* 0x70-0x77 */
  1541, 1545, 1549, 1553, 1557, 1561, 1566, 1571, /* 0x78-0x7f */
  1576, 1581, 1586, 1591, 1596, 1601, 1606, 1611, /* 0x80-0x87 */
  1616, 1619, 1622, 1625, 1628, 1631, 1634, 1637, /* 0x88-0x8f */
  1640, 1643, 1647, 1651, 1655, 1659, 1663, 1667, /* 0x90-0x97 */
  1671, 1675, 1679, 1683, 1687, 1691, 1695, 1699, /* 0x98-0x9f */
  1703, 1707, 1711, 1715, 1719, 1723, 1727, 1731, /* 0xa0-0xa7 */
  1735, 1739, 1743, 1747, 1751, 1755, 1759, 1763, /* 0xa8-0xaf */
  1767, 1771, 1775, 1779, 1783, 1787, 1791, 1795, /* 0xb0-0xb7 */
  1799, 1803, 1807, 1811, 1815, 1819, 1823, 1827, /* 0xb8-0xbf */
  1831, 1835, 1839, 1843, 1847, 1851, 1855, 1859, /* 0xc0-0xc7 */
  1863, 1867, 1871, 1875, 1879, 1883, 1887, 1891, /* 0xc8-0xcf */
  1895, 1899, 1903, 1907, 1911, 1915, 1919, 1923, /* 0xd0-0xd7 */
  1927, 1931, 1935, 1939, 1943, 1947, 1951, 1955, /* 0xd8-0xdf */
  1959, 1963, 1967, 1971, 1975, 1979, 1983, 1987, /* 0xe0-0xe7 */
  1991, 1995, 1999,   -1,   -1,   -1,   -1,   -1, /* 0xe8-0xef */
};
static const short translit_page25[64] = {
  2003,   -1, 2005,   -1,   -1,   -1,   -1,   -1, /* 0x00-0x07 */
    -1,   -1,   -1,   -1, 2007,   -1,   -1,   -1, /* 0x08-0x0f */
  2009,   -1,   -1,   -1, 2011,   -1,   -1,   -1, /* 0x10-0x17 */
  2013,   -1,   -1,   -1, 2015,   -1,   -1,   -1, /* 0x18-0x1f */
    -1,   -1,   -1,   -1, 2017,   -1,   -1,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1, 2019,   -1,   -1,   -1, /* 0x28-0x2f */
    -1,   -1,   -1,   -1, 2021,   -1,   -1,   -1, /* 0x30-0x37 */
    -1,   -1,   -1,   -1, 2023,   -1,   -1,   -1, /* 0x38-0x3f */
};
static const short translit_page2a[8] = {
    -1,   -1,   -1,   -1, 2032, 2036, 2039,   -1, /* 0x70-0x77 */
};
static const short translit_page2f[216] = {
  2047, 2049, 2051, 2053, 2055, 2057, 2059, 2061, /* 0x00-0x07 */
  2063, 2065, 2067, 2069, 2071, 2073, 2075, 2077, /* 0x08-0x0f */
  2079, 2081, 2083, 2085, 2087, 2089, 2091, 2093, /* 0x10-0x17 */
  2095, 2097, 2099, 2101, 2103, 2105, 2107, 2109, /* 0x18-0x1f */
  2111, 2113, 2115, 2117, 2119, 2121, 2123, 2125, /* 0x20-0x27 */
  2127, 2129, 2131, 2133, 2135, 2137, 2139, 2141, /* 0x28-0x2f */
  2143, 2145, 2147, 2149, 2151, 2153, 2155, 2157, /* 0x30-0x37 */
  2159, 2161, 2163, 2165, 2167, 2169, 2171, 2173, /* 0x38-0x3f */
  2175, 2177, 2179, 2181, 2183, 2185, 2187, 2189, /* 0x40-0x47 */
  2191, 2193, 2195, 2197, 2199, 2201, 2203, 2205, /* 0x48-0x4f */
  2207, 2209, 2211, 2213, 2215, 2217, 2219, 2221, /* 0x50-0x57 */
  2223, 2225, 2227, 2229, 2231, 2233, 2235, 2237, /* 0x58-0x5f */
  2239, 2241, 2243, 2245, 2247, 2249, 2251, 2253, /* 0x60-0x67 */
  2255, 2257, 2259, 2261, 2263, 2265, 2267, 2269, /* 0x68-0x6f */
  2271, 2273, 2275, 2277, 2279, 2281, 2283, 2285, /* 0x70-0x77 */
  2287, 2289, 2291, 2293, 2295, 2297, 2299, 2301, /* 0x78-0x7f */
  2303, 2305, 2307, 2309, 2311, 2313, 2315, 2317, /* 0x80-0x87 */
  2319, 2321, 2323, 2325, 2327, 2329, 2331, 2333, /* 0x88-0x8f */
  2335, 2337, 2339, 2341, 2343, 2345, 2347, 2349, /* 0x90-0x97 */
  2351, 2353, 2355, 2357, 2359, 2361, 2363, 2365, /* 0x98-0x9f */
  2367, 2369, 2371, 2373, 2375, 2377, 2379, 2381, /* 0xa0-0xa7 */
  2383, 2385, 2387, 2389, 2391, 2393, 2395, 2397, /* 0xa8-0xaf */
  2399, 2401, 2403, 2405, 2407, 2409, 2411, 2413, /* 0xb0-0xb7 */
  2415, 2417, 2419, 2421, 2423, 2425, 2427, 2429, /* 0xb8-0xbf */
  2431, 2433, 2435, 2437, 2439, 2441, 2443, 2445, /* 0xc0-0xc7 */
  2447, 2449, 2451, 2453, 2455, 2457, 2459, 2461, /* 0xc8-0xcf */
  2463, 2465, 2467, 2469, 2471, 2473,   -1,   -1, /* 0xd0-0xd7 */
};
static const short translit_page30[248] = {
  2475,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x00-0x07 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x08-0x0f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x10-0x17 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x18-0x1f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x20-0x27 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x28-0x2f */
    -1,   -1,   -1,   -1,   -1,   -1, 2477,   -1, /* 0x30-0x37 */
  2479, 2481, 2483,   -1,   -1,   -1,   -1,   -1, /* 0x38-0x3f */
    -1, 2485,   -1, 2487,   -1, 2489,   -1, 2491, /* 0x40-0x47 */
    -1, 2493,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x50-0x57 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x58-0x5f */
    -1,   -1,   -1, 2495,   -1,   -1,   -1,   -1, /* 0x60-0x67 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x68-0x6f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x70-0x77 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x78-0x7f */
    -1,   -1,   -1, 2497,   -1, 2499,   -1, 2501, /* 0x80-0x87 */
    -1,   -1,   -1,   -1,   -1,   -1, 2503,   -1, /* 0x88-0x8f */
    -1,   -1,   -1,   -1,   -1, 2505, 2507,   -1, /* 0x90-0x97 */
    -1,   -1,   -1, 2509, 2512,   -1,   -1,   -1, /* 0x98-0x9f */
  2515, 2517,   -1, 2519,   -1, 2521,   -1, 2523, /* 0xa0-0xa7 */
    -1, 2525,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xa8-0xaf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb0-0xb7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xb8-0xbf */
    -1,   -1,   -1, 2527,   -1,   -1,   -1,   -1, /* 0xc0-0xc7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xc8-0xcf */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd0-0xd7 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0xd8-0xdf */
    -1,   -1,   -1, 2529,   -1, 2531,   -1, 2533, /* 0xe0-0xe7 */
    -1,   -1,   -1,   -1,   -1,   -1, 2535,   -1, /* 0xe8-0xef */
    -1,   -1,   -1,   -1,   -1, 2537, 2539,   -1, /* 0xf0-0xf7 */
};
static const short translit_page31[96] = {
    -1, 2541, 2543, 2545, 2547, 2549, 2551, 2553, /* 0x30-0x37 */
  2555, 2557, 2559, 2561, 2563, 2565, 2567, 2569, /* 0x38-0x3f */
  2571, 2573, 2575, 2577, 2579, 2581, 2583, 2585, /* 0x40-0x47 */
  2587, 2589, 2591, 2593, 2595, 2597, 2599, 2601, /* 0x48-0x4f */
  2603, 2605, 2607, 2609, 2611, 2613, 2615, 2617, /* 0x50-0x57 */
  2619, 2621, 2623, 2625, 2627, 2629, 2631, 2633, /* 0x58-0x5f */
  2635, 2637, 2639, 2641, 2643, 2645, 2647, 2649, /* 0x60-0x67 */
  2651, 2653, 2655, 2657, 2659, 2661, 2663, 2665, /* 0x68-0x6f */
  2667, 2669, 2671, 2673, 2675, 2677, 2679, 2681, /* 0x70-0x77 */
  2683, 2685, 2687, 2689, 2691, 2693, 2695, 2697, /* 0x78-0x7f */
  2699, 2701, 2703, 2705, 2707, 2709, 2711, 2713, /* 0x80-0x87 */
  2715, 2717, 2719, 2721, 2723, 2725, 2727,   -1, /* 0x88-0x8f */
};
static const short translit_page31_1[528] = {
  2729, 2731, 2733, 2735, 2737, 2739, 2741, 2743, /* 0xf0-0xf7 */
  2745, 2747, 2749, 2751, 2753, 2755, 2757, 2759, /* 0xf8-0xff */
  /* 0x3200 */
  2761, 2765, 2769, 2773, 2777, 2781, 2785, 2789, /* 0x00-0x07 */
  2793, 2797, 2801, 2805, 2809, 2813, 2817, 2822, /* 0x08-0x0f */
  2827, 2832, 2837, 2842, 2847, 2852, 2857, 2862, /* 0x10-0x17 */
  2867, 2872, 2877, 2882, 2887, 2892, 2900,   -1, /* 0x18-0x1f */
  2907, 2911, 2915, 2919, 2923, 2927, 2931, 2935, /* 0x20-0x27 */
  2939, 2943, 2947, 2951, 2955, 2959, 2963, 2967, /* 0x28-0x2f */
  2971, 2975, 2979, 2983, 2987, 2991, 2995, 2999, /* 0x30-0x37 */
  3003, 3007, 3011, 3015, 3019, 3023, 3027, 3031, /* 0x38-0x3f */
  3035, 3039, 3043, 3047,   -1,   -1,   -1,   -1, /* 0x40-0x47 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x48-0x4f */
  3051, 3055, 3060, 3065, 3070, 3075, 3080, 3085, /* 0x50-0x57 */
  3090, 3095, 3100, 3105, 3110, 3115, 3120, 3125, /* 0x58-0x5f */
  3130, 3134, 3138, 3142, 3146, 3150, 3154, 3158, /* 0x60-0x67 */
  3162, 3166, 3170, 3174, 3178, 3182, 3186, 3191, /* 0x68-0x6f */
  3196, 3201, 3206, 3211, 3216, 3221, 3226, 3231, /* 0x70-0x77 */
  3236, 3241, 3246, 3251, 3256, 3264,   -1,   -1, /* 0x78-0x7f */
  3271, 3275, 3279, 3283, 3287, 3291, 3295, 3299, /* 0x80-0x87 */
  3303, 3307, 3311, 3315, 3319, 3323, 3327, 3331, /* 0x88-0x8f */
  3335, 3339, 3343, 3347, 3351, 3355, 3359, 3363, /* 0x90-0x97 */
  3367, 3371, 3375, 3379, 3383, 3387, 3391, 3395, /* 0x98-0x9f */
  3399, 3403, 3407, 3411, 3415, 3419, 3423, 3427, /* 0xa0-0xa7 */
  3431, 3435, 3439, 3443, 3447, 3451, 3455, 3459, /* 0xa8-0xaf */
  3463, 3467, 3472, 3477, 3482, 3487, 3492, 3497, /* 0xb0-0xb7 */
  3502, 3507, 3512, 3517, 3522, 3527, 3532, 3537, /* 0xb8-0xbf */
  3542, 3545, 3548, 3551, 3554, 3557, 3560, 3563, /* 0xc0-0xc7 */
  3566, 3569, 3573, 3577, 3581, 3584, 3588, 3591, /* 0xc8-0xcf */
  3595, 3599, 3603, 3607, 3611, 3615, 3619, 3623, /* 0xd0-0xd7 */
  3627, 3631, 3635, 3639, 3643, 3647, 3651, 3655, /* 0xd8-0xdf */
  3659, 3663, 3667, 3671, 3675, 3679, 3683, 3687, /* 0xe0-0xe7 */
  3691, 3695, 3699, 3703, 3707, 3711, 3715, 3719, /* 0xe8-0xef */
  3723, 3727, 3731, 3735, 3739, 3743, 3747, 3751, /* 0xf0-0xf7 */
  3755, 3759, 3763, 3767, 3771, 3775, 3779,   -1, /* 0xf8-0xff */
  /* 0x3300 */
  3783, 3788, 3793, 3798, 3802, 3807, 3811, 3815, /* 0x00-0x07 */
  3821, 3826, 3830, 3834, 3838, 3843, 3848, 3852, /* 0x08-0x0f */
  3856, 3859, 3863, 3868, 3873, 3876, 3882, 3889, /* 0x10-0x17 */
  3895, 3899, 3905, 3911, 3916, 3920, 3924, 3928, /* 0x18-0x1f */
  3933, 3939, 3944, 3948, 3952, 3956, 3959, 3962, /* 0x20-0x27 */
  3965, 3968, 3972, 3976, 3982, 3986, 3991, 3997, /* 0x28-0x2f */
  4001, 4004, 4007, 4013, 4018, 4024, 4028, 4034, /* 0x30-0x37 */
  4037, 4041, 4045, 4049, 4053, 4057, 4062, 4066, /* 0x38-0x3f */
  4069, 4073, 4077, 4081, 4086, 4090, 4094, 4098, /* 0x40-0x47 */
  4104, 4109, 4112, 4118, 4121, 4126, 4131, 4135, /* 0x48-0x4f */
  4139, 4143, 4148, 4151, 4155, 4160, 4163, 4169, /* 0x50-0x57 */
  4173, 4176, 4179, 4182, 4185, 4188, 4191, 4194, /* 0x58-0x5f */
  4197, 4200, 4203, 4207, 4211, 4215, 4219, 4223, /* 0x60-0x67 */
  4227, 4231, 4235, 4239, 4243, 4247, 4251, 4255, /* 0x68-0x6f */
  4259, 4263, 4267, 4270, 4273, 4277, 4280, 4283, /* 0x70-0x77 */
  4286, 4291, 4296, 4299, 4302, 4305, 4308, 4311, /* 0x78-0x7f */
  4316, 4319, 4322, 4325, 4328, 4331, 4334, 4337, /* 0x80-0x87 */
  4340, 4344, 4349, 4352, 4355, 4358, 4361, 4364, /* 0x88-0x8f */
  4367, 4370, 4374, 4378, 4382, 4386, 4389, 4392, /* 0x90-0x97 */
  4395, 4398, 4401, 4404, 4407, 4410, 4413, 4416, /* 0x98-0x9f */
  4421, 4426, 4430, 4435, 4440, 4445, 4449, 4454, /* 0xa0-0xa7 */
  4458, 4464, 4467, 4471, 4475, 4479, 4483, 4489, /* 0xa8-0xaf */
  4497, 4500, 4503, 4506, 4509, 4512, 4515, 4518, /* 0xb0-0xb7 */
  4521, 4524, 4527, 4530, 4533, 4536, 4539, 4542, /* 0xb8-0xbf */
  4545, 4548, 4551, 4556, 4559, 4562, 4565, 4570, /* 0xc0-0xc7 */
  4574, 4577, 4580, 4583, 4586, 4589, 4592, 4595, /* 0xc8-0xcf */
  4598, 4601, 4604, 4608, 4611, 4614, 4618, 4622, /* 0xd0-0xd7 */
  4625, 4630, 4634, 4637, 4640, 4643, 4646, 4650, /* 0xd8-0xdf */
  4658, 4661, 4664, 4667, 4670, 4673, 4676, 4679, /* 0xe0-0xe7 */
  4682, 4685, 4689, 4693, 4697, 4701, 4705, 4709, /* 0xe8-0xef */
  4713, 4717, 4721, 4725, 4729, 4733, 4737, 4741, /* 0xf0-0xf7 */
  4745, 4749, 4753, 4757, 4761, 4765, 4769, 4654, /* 0xf8-0xff */
};
static const short translit_pagef9[368] = {
  4773, 4775, 4777, 4779, 4781, 4783, 4785, 4787, /* 0x00-0x07 */
  4789, 4791, 4793, 4795, 4797, 4799, 4801, 4803, /* 0x08-0x0f */
  4805, 4807, 4809, 4811, 4813, 4815, 4817, 4819, /* 0x10-0x17 */
  4821, 4823, 4825, 4827, 4829, 4831, 4833, 4835, /* 0x18-0x1f */
  4837, 4839, 4841, 4843, 4845, 4847, 4849, 4851, /* 0x20-0x27 */
  4853, 4855, 4857, 4859, 4861, 4863, 4865, 4867, /* 0x28-0x2f */
  4869, 4871, 4873, 4875, 4877, 4879, 4881, 4883, /* 0x30-0x37 */
  4885, 4887, 4889, 4891, 4893, 4895, 4897, 4899, /* 0x38-0x3f */
  4901, 4903, 4905, 4907, 4909, 4911, 4913, 4915, /* 0x40-0x47 */
  4917, 4919, 4921, 4923, 4925, 4927, 4929, 4931, /* 0x48-0x4f */
  4933, 4935, 4937, 4939, 4941, 4943, 4945, 4947, /* 0x50-0x57 */
  4949, 4951, 4953, 4955, 4957, 4959, 4961, 4963, /* 0x58-0x5f */
  4965, 4967, 4969, 4971, 4973, 4975, 4977, 4979, /* 0x60-0x67 */
  4981, 4983, 4985, 4987, 4989, 4991, 4993, 4995, /* 0x68-0x6f */
  4997, 4999, 5001, 5003, 5005, 5007, 5009, 5011, /* 0x70-0x77 */
  5013, 5015, 5017, 5019, 5021, 5023, 5025, 5027, /* 0x78-0x7f */
  5029, 5031, 5033, 5035, 5037, 5039, 5041, 5043, /* 0x80-0x87 */
  5045, 5047, 5049, 5051, 5053, 5055, 5057, 5059, /* 0x88-0x8f */
  5061, 5063, 5065, 5067, 5069, 5071, 5073, 5075, /* 0x90-0x97 */
  5077, 5079, 5081, 5083, 5085, 5087, 5089, 5091, /* 0x98-0x9f */
  5093, 5095, 5097, 5099, 5101, 5103, 5105, 5107, /* 0xa0-0xa7 */
  5109, 5111, 5113, 5115, 5117, 5119, 5121, 5123, /* 0xa8-0xaf */
  5125, 5127, 5129, 5131, 5133, 5135, 5137, 5139, /* 0xb0-0xb7 */
  5141, 5143, 5145, 5147, 5149, 5151, 5153, 5155, /* 0xb8-0xbf */
  5157, 5159, 5161, 5163, 5165, 5167, 5169, 5171, /* 0xc0-0xc7 */
  5173, 5175, 5177, 5179, 5181, 5183, 5185, 5187, /* 0xc8-0xcf */
  5189, 5191, 5193, 5195, 5197, 5199, 5201, 5203, /* 0xd0-0xd7 */
  5205, 5207, 5209, 5211, 5213, 5215, 5217, 5219, /* 0xd8-0xdf */
  5221, 5223, 5225, 5227, 5229, 5231, 5233, 5235, /* 0xe0-0xe7 */
  5237, 5239, 5241, 5243, 5245, 5247, 5249, 5251, /* 0xe8-0xef */
  5253, 5255, 5257, 5259, 5261, 5263, 5265, 5267, /* 0xf0-0xf7 */
  5269, 5271, 5273, 5275, 5277, 5279, 5281, 5283, /* 0xf8-0xff */
  /* 0xfa00 */
  5285, 5287, 5289, 5291, 5293, 5295, 5297, 5299, /* 0x00-0x07 */
  5301, 5303, 5305, 5307, 5309, 5311,   -1,   -1, /* 0x08-0x0f */
  5313,   -1, 5315,   -1,   -1, 5317, 5319, 5321, /* 0x10-0x17 */
  5323, 5325, 5327, 5329, 5331, 5333, 5335,   -1, /* 0x18-0x1f */
  5337,   -1, 5339,   -1,   -1, 5341, 5343,   -1, /* 0x20-0x27 */
    -1,   -1, 5345, 5347, 5349, 5351,   -1,   -1, /* 0x28-0x2f */
  5353, 5355, 5357, 5359, 5361, 5363, 5365, 5367, /* 0x30-0x37 */
  5369, 5371, 5373, 5375, 5377, 5379, 5381, 5383, /* 0x38-0x3f */
  5385, 5387, 5389, 5391, 5393, 5395, 5397, 5399, /* 0x40-0x47 */
  5401, 5403, 5405, 5407, 5409, 5411, 5413, 5415, /* 0x48-0x4f */
  5417, 5419, 5421, 5423, 5425, 5427, 5429, 5431, /* 0x50-0x57 */
  5433, 5435, 5437, 5439, 5441, 5443, 5445, 5447, /* 0x58-0x5f */
  5449, 5451, 5453, 5455, 5457, 5459, 5461, 5463, /* 0x60-0x67 */
  5465, 5467, 5469,   -1,   -1,   -1,   -1,   -1, /* 0x68-0x6f */
};
static const short translit_pagefb[80] = {
  5471, 5474, 5477, 5480, 5484, 5488, 5491,   -1, /* 0x00-0x07 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x08-0x0f */
    -1,   -1,   -1, 5494, 5497, 5500, 5503, 5506, /* 0x10-0x17 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x18-0x1f */
  5509, 5511, 5513, 5515, 5517, 5519, 5521, 5523, /* 0x20-0x27 */
  5525, 5527,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x28-0x2f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x30-0x37 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x38-0x3f */
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1, /* 0x40-0x47 */
    -1,   -1,   -1,   -1,   -1,   -1,   -1, 5529, /* 0x48-0x4f */
};
static const short translit_pagefe[40] = {
    -1, 5532, 5534, 5536, 5538, 5540, 5542, 5544, /* 0x48-0x4f */
  5546, 5548, 5550,   -1, 5552, 5554, 5556, 5558, /* 0x50-0x57 */
  5560, 5562, 5564, 5566, 5568, 5570, 5572, 5574, /* 0x58-0x5f */
  5576, 5578, 5580, 5582, 5584, 5586, 5588,   -1, /* 0x60-0x67 */
  5590, 5592, 5594, 5596,   -1,   -1,   -1,   -1, /* 0x68-0x6f */
};
static const short translit_pageff[240] = {
    -1, 5598, 5600, 5602, 5604, 5606, 5608, 5610, /* 0x00-0x07 */
  5612, 5614, 5616, 5618, 5620, 5622, 5624, 5626, /* 0x08-0x0f */
  5628, 5630, 5632, 5634, 5636, 5638, 5640, 5642, /* 0x10-0x17 */
  5644, 5646, 5648, 5650, 5652, 5654, 5656, 5658, /* 0x18-0x1f */
  5660, 5662, 5664, 5666, 5668, 5670, 5672, 5674, /* 0x20-0x27 */
  5676, 5678, 5680, 5682, 5684, 5686, 5688, 5690, /* 0x28-0x2f */
  5692, 5694, 5696, 5698, 5700, 5702, 5704, 5706, /* 0x30-0x37 */
  5708, 5710, 5712, 5714, 5716, 5718, 5720, 5722, /* 0x38-0x3f */
  5724, 5726, 5728, 5730, 5732, 5734, 5736, 5738, /* 0x40-0x47 */
  5740, 5742, 5744, 5746, 5748, 5750, 5752, 5754, /* 0x48-0x4f */
  5756, 5758, 5760, 5762, 5764, 5766, 5768, 5770, /* 0x50-0x57 */
  5772, 5774, 5776, 5778, 5780, 5782, 5784, 5786, /* 0x58-0x5f */
  5788, 5790, 5792, 5794, 5796, 5798, 5800, 5802, /* 0x60-0x67 */
  5804, 5806, 5808, 5810, 5812, 5814, 5816, 5818, /* 0x68-0x6f */
  5820, 5822, 5824, 5826, 5828, 5830, 5832, 5834, /* 0x70-0x77 */
  5836, 5838, 5840, 5842, 5844, 5846, 5848, 5850, /* 0x78-0x7f */
  5852, 5854, 5856, 5858, 5860, 5862, 5864, 5866, /* 0x80-0x87 */
  5868, 5870, 5872, 5874, 5876, 5878, 5880, 5882, /* 0x88-0x8f */
  5884, 5886, 5888, 5890, 5892, 5894, 5896, 5898, /* 0x90-0x97 */
  5900, 5902, 5904, 5906, 5908, 5910, 5912, 5914, /* 0x98-0x9f */
  5916, 5918, 5920, 5922, 5924, 5926, 5928, 5930, /* 0xa0-0xa7 */
  5932, 5934, 5936, 5938, 5940, 5942, 5944, 5946, /* 0xa8-0xaf */
  5948, 5950, 5952, 5954, 5956, 5958, 5960, 5962, /* 0xb0-0xb7 */
  5964, 5966, 5968, 5970, 5972, 5974, 5976,   -1, /* 0xb8-0xbf */
    -1,   -1, 5978, 5980, 5982, 5984, 5986, 5988, /* 0xc0-0xc7 */
    -1,   -1, 5990, 5992, 5994, 5996, 5998, 6000, /* 0xc8-0xcf */
    -1,   -1, 6002, 6004, 6006, 6008, 6010, 6012, /* 0xd0-0xd7 */
    -1,   -1, 6014, 6016, 6018,   -1,   -1,   -1, /* 0xd8-0xdf */
  6020, 6022, 6024, 6026, 6028, 6030, 6032,   -1, /* 0xe0-0xe7 */
  6034, 6036, 6038, 6040, 6042, 6044, 6046,   -1, /* 0xe8-0xef */
};
static const short translit_page1d4[1024] = {
  6048, 6050, 6052, 6054, 6056, 6058, 6060, 6062, /* 0x00-0x07 */
  6064, 6066, 6068, 6070, 6072, 6074, 6076, 6078, /* 0x08-0x0f */
  6080, 6082, 6084, 6086, 6088, 6090, 6092, 6094, /* 0x10-0x17 */
  6096, 6098, 6100, 6102, 6104, 6106, 6108, 6110, /* 0x18-0x1f */
  6112, 6114, 6116, 6118, 6120, 6122, 6124, 6126, /* 0x20-0x27 */
  6128, 6130, 6132, 6134, 6136, 6138, 6140, 6142, /* 0x28-0x2f */
  6144, 6146, 6148, 6150, 6152, 6154, 6156, 6158, /* 0x30-0x37 */
  6160, 6162, 6164, 6166, 6168, 6170, 6172, 6174, /* 0x38-0x3f */
  6176, 6178, 6180, 6182, 6184, 6186, 6188, 6190, /* 0x40-0x47 */
  6192, 6194, 6196, 6198, 6200, 6202, 6204, 6206, /* 0x48-0x4f */
  6208, 6210, 6212, 6214, 6216,   -1, 6218, 6220, /* 0x50-0x57 */
  6222, 6224, 6226, 6228, 6230, 6232, 6234, 6236, /* 0x58-0x5f */
  6238, 6240, 6242, 6244, 6246, 6248, 6250, 6252, /* 0x60-0x67 */
  6254, 6256, 6258, 6260, 6262, 6264, 6266, 6268, /* 0x68-0x6f */
  6270, 6272, 6274, 6276, 6278, 6280, 6282, 6284, /* 0x70-0x77 */
  6286, 6288, 6290, 6292, 6294, 6296, 6298, 6300, /* 0x78-0x7f */
  6302, 6304, 6306, 6308, 6310, 6312, 6314, 6316, /* 0x80-0x87 */
  6318, 6320, 6322, 6324, 6326, 6328, 6330, 6332, /* 0x88-0x8f */
  6334, 6336, 6338, 6340, 6342, 6344, 6346, 6348, /* 0x90-0x97 */
  6350, 6352, 6354, 6356, 6358,   -1, 6360, 6362, /* 0x98-0x9f */
    -1,   -1, 6364,   -1,   -1, 6366, 6368,   -1, /* 0xa0-0xa7 */
    -1, 6370, 6372, 6374, 6376,   -1, 6378, 6380, /* 0xa8-0xaf */
  6382, 6384, 6386, 6388, 6390, 6392, 6394, 6396, /* 0xb0-0xb7 */
  6398, 6400,   -1, 6402,   -1, 6404, 6406, 6408, /* 0xb8-0xbf */
  6410, 6412, 6414, 6416,   -1, 6418, 6420, 6422, /* 0xc0-0xc7 */
  6424, 6426, 6428, 6430, 6432, 6434, 6436, 6438, /* 0xc8-0xcf */
  6440, 6442, 6444, 6446, 6448, 6450, 6452, 6454, /* 0xd0-0xd7 */
  6456, 6458, 6460, 6462, 6464, 6466, 6468, 6470, /* 0xd8-0xdf */
  6472, 6474, 6476, 6478, 6480, 6482, 6484, 6486, /* 0xe0-0xe7 */
  6488, 6490, 6492, 6494, 6496, 6498, 6500, 6502, /* 0xe8-0xef */
  6504, 6506, 6508, 6510, 6512, 6514, 6516, 6518, /* 0xf0-0xf7 */
  6520, 6522, 6524, 6526, 6528, 6530, 6532, 6534, /* 0xf8-0xff */
  /* 0x1d500 */
  6536, 6538, 6540, 6542, 6544, 6546,   -1, 6548, /* 0x00-0x07 */
  6550, 6552, 6554,   -1,   -1, 6556, 6558, 6560, /* 0x08-0x0f */
  6562, 6564, 6566, 6568, 6570,   -1, 6572, 6574, /* 0x10-0x17 */
  6576, 6578, 6580, 6582, 6584,   -1, 6586, 6588, /* 0x18-0x1f */
  6590, 6592, 6594, 6596, 6598, 6600, 6602, 6604, /* 0x20-0x27 */
  6606, 6608, 6610, 6612, 6614, 6616, 6618, 6620, /* 0x28-0x2f */
  6622, 6624, 6626, 6628, 6630, 6632, 6634, 6636, /* 0x30-0x37 */
  6638, 6640,   -1, 6642, 6644, 6646, 6648,   -1, /* 0x38-0x3f */
  6650, 6652, 6654, 6656, 6658,   -1, 6660,   -1, /* 0x40-0x47 */
    -1,   -1, 6662, 6664, 6666, 6668, 6670, 6672, /* 0x48-0x4f */
  6674,   -1, 6676, 6678, 6680, 6682, 6684, 6686, /* 0x50-0x57 */
  6688, 6690, 6692, 6694, 6696, 6698, 6700, 6702, /* 0x58-0x5f */
  6704, 6706, 6708, 6710, 6712, 6714, 6716, 6718, /* 0x60-0x67 */
  6720, 6722, 6724, 6726, 6728, 6730, 6732, 6734, /* 0x68-0x6f */
  6736, 6738, 6740, 6742, 6744, 6746, 6748, 6750, /* 0x70-0x77 */
  6752, 6754, 6756, 6758, 6760, 6762, 6764, 6766, /* 0x78-0x7f */
  6768, 6770, 6772, 6774, 6776, 6778, 6780, 6782, /* 0x80-0x87 */
  6784, 6786, 6788, 6790, 6792, 6794, 6796, 6798, /* 0x88-0x8f */
  6800, 6802, 6804, 6806, 6808, 6810, 6812, 6814, /* 0x90-0x97 */
  6816, 6818, 6820, 6822, 6824, 6826, 6828, 6830, /* 0x98-0x9f */
  6832, 6834, 6836, 6838, 6840, 6842, 6844, 6846, /* 0xa0-0xa7 */
  6848, 6850, 6852, 6854, 6856, 6858, 6860, 6862, /* 0xa8-0xaf */
  6864, 6866, 6868, 6870, 6872, 6874, 6876, 6878, /* 0xb0-0xb7 */
  6880, 6882, 6884, 6886, 6888, 6890, 6892, 6894, /* 0xb8-0xbf */
  6896, 6898, 6900, 6902, 6904, 6906, 6908, 6910, /* 0xc0-0xc7 */
  6912, 6914, 6916, 6918, 6920, 6922, 6924, 6926, /* 0xc8-0xcf */
  6928, 6930, 6932, 6934, 6936, 6938, 6940, 6942, /* 0xd0-0xd7 */
  6944, 6946, 6948, 6950, 6952, 6954, 6956, 6958, /* 0xd8-0xdf */
  6960, 6962, 6964, 6966, 6968, 6970, 6972, 6974, /* 0xe0-0xe7 */
  6976, 6978, 6980, 6982, 6984, 6986, 6988, 6990, /* 0xe8-0xef */
  6992, 6994, 6996, 6998, 7000, 7002, 7004, 7006, /* 0xf0-0xf7 */
  7008, 7010, 7012, 7014, 7016, 7018, 7020, 7022, /* 0xf8-0xff */
  /* 0x1d600 */
  7024, 7026, 7028, 7030, 7032, 7034, 7036, 7038, /* 0x00-0x07 */
  7040, 7042, 7044, 7046, 7048, 7050, 7052, 7054, /* 0x08-0x0f */
  7056, 7058, 7060, 7062, 7064, 7066, 7068, 7070, /* 0x10-0x17 */
  7072, 7074, 7076, 7078, 7080, 7082, 7084, 7086, /* 0x18-0x1f */
  7088, 7090, 7092, 7094, 7096, 7098, 7100, 7102, /* 0x20-0x27 */
  7104, 7106, 7108, 7110, 7112, 7114, 7116, 7118, /* 0x28-0x2f */
  7120, 7122, 7124, 7126, 7128, 7130, 7132, 7134, /* 0x30-0x37 */
  7136, 7138, 7140, 7142, 7144, 7146, 7148, 7150, /* 0x38-0x3f */
  7152, 7154, 7156, 7158, 7160, 7162, 7164, 7166, /* 0x40-0x47 */
  7168, 7170, 7172, 7174, 7176, 7178, 7180, 7182, /* 0x48-0x4f */
  7184, 7186, 7188, 7190, 7192, 7194, 7196, 7198, /* 0x50-0x57 */
  7200, 7202, 7204, 7206, 7208, 7210, 7212, 7214, /* 0x58-0x5f */
  7216, 7218, 7220, 7222, 7224, 7226, 7228, 7230, /* 0x60-0x67 */
  7232, 7234, 7236, 7238, 7240, 7242, 7244, 7246, /* 0x68-0x6f */
  7248, 7250, 7252, 7254, 7256, 7258, 7260, 7262, /* 0x70-0x77 */
  7264, 7266, 7268, 7270, 7272, 7274, 7276, 7278, /* 0x78-0x7f */
  7280, 7282, 7284, 7286, 7288, 7290, 7292, 7294, /* 0x80-0x87 */
  7296, 7298, 7300, 7302, 7304, 7306, 7308, 7310, /* 0x88-0x8f */
  7312, 7314, 7316, 7318, 7320, 7322, 7324, 7326, /* 0x90-0x97 */
  7328, 7330, 7332, 7334, 7336, 7338, 7340, 7342, /* 0x98-0x9f */
  7344, 7346, 7348, 7350,   -1,   -1,   -1,   -1, /* 0xa0-0xa7 */
  7352, 7354, 7356, 7358, 7360, 7362, 7364, 7366, /* 0xa8-0xaf */
  7368, 7370, 7372, 7374, 7376, 7378, 7380, 7382, /* 0xb0-0xb7 */
  7384, 7386, 7388, 7390, 7392, 7394, 7396, 7398, /* 0xb8-0xbf */
  7400, 7402, 7404, 7406, 7408, 7410, 7412, 7414, /* 0xc0-0xc7 */
  7416, 7418, 7420, 7422, 7424, 7426, 7428, 7430, /* 0xc8-0xcf */
  7432, 7434, 7436, 7438, 7440, 7442, 7444, 7446, /* 0xd0-0xd7 */
  7448, 7450, 7452, 7454, 7456, 7458, 7460, 7462, /* 0xd8-0xdf */
  7464, 7466, 7468, 7470, 7472, 7474, 7476, 7478, /* 0xe0-0xe7 */
  7480, 7482, 7484, 7486, 7488, 7490, 7492, 7494, /* 0xe8-0xef */
  7496, 7498, 7500, 7502, 7504, 7506, 7508, 7510, /* 0xf0-0xf7 */
  7512, 7514, 7516, 7518, 7520, 7522, 7524, 7526, /* 0xf8-0xff */
  /* 0x1d700 */
  7528, 7530, 7532, 7534, 7536, 7538, 7540, 7542, /* 0x00-0x07 */
  7544, 7546, 7548, 7550, 7552, 7554, 7556, 7558, /* 0x08-0x0f */
  7560, 7562, 7564, 7566, 7568, 7570, 7572, 7574, /* 0x10-0x17 */
  7576, 7578, 7580, 7582, 7584, 7586, 7588, 7590, /* 0x18-0x1f */
  7592, 7594, 7596, 7598, 7600, 7602, 7604, 7606, /* 0x20-0x27 */
  7608, 7610, 7612, 7614, 7616, 7618, 7620, 7622, /* 0x28-0x2f */
  7624, 7626, 7628, 7630, 7632, 7634, 7636, 7638, /* 0x30-0x37 */
  7640, 7642, 7644, 7646, 7648, 7650, 7652, 7654, /* 0x38-0x3f */
  7656, 7658, 7660, 7662, 7664, 7666, 7668, 7670, /* 0x40-0x47 */
  7672, 7674, 7676, 7678, 7680, 7682, 7684, 7686, /* 0x48-0x4f */
  7688, 7690, 7692, 7694, 7696, 7698, 7700, 7702, /* 0x50-0x57 */
  7704, 7706, 7708, 7710, 7712, 7714, 7716, 7718, /* 0x58-0x5f */
  7720, 7722, 7724, 7726, 7728, 7730, 7732, 7734, /* 0x60-0x67 */
  7736, 7738, 7740, 7742, 7744, 7746, 7748, 7750, /* 0x68-0x6f */
  7752, 7754, 7756, 7758, 7760, 7762, 7764, 7766, /* 0x70-0x77 */
  7768, 7770, 7772, 7774, 7776, 7778, 7780, 7782, /* 0x78-0x7f */
  7784, 7786, 7788, 7790, 7792, 7794, 7796, 7798, /* 0x80-0x87 */
  7800, 7802, 7804, 7806, 7808, 7810, 7812, 7814, /* 0x88-0x8f */
  7816, 7818, 7820, 7822, 7824, 7826, 7828, 7830, /* 0x90-0x97 */
  7832, 7834, 7836, 7838, 7840, 7842, 7844, 7846, /* 0x98-0x9f */
  7848, 7850, 7852, 7854, 7856, 7858, 7860, 7862, /* 0xa0-0xa7 */
  7864, 7866, 7868, 7870, 7872, 7874, 7876, 7878, /* 0xa8-0xaf */
  7880, 7882, 7884, 7886, 7888, 7890, 7892, 7894, /* 0xb0-0xb7 */
  7896, 7898, 7900, 7902, 7904, 7906, 7908, 7910, /* 0xb8-0xbf */
  7912, 7914, 7916, 7918, 7920, 7922, 7924, 7926, /* 0xc0-0xc7 */
  7928, 7930,   -1,   -1,   -1,   -1, 7932, 7934, /* 0xc8-0xcf */
  7936, 7938, 7940, 7942, 7944, 7946, 7948, 7950, /* 0xd0-0xd7 */
  7952, 7954, 7956, 7958, 7960, 7962, 7964, 7966, /* 0xd8-0xdf */
  7968, 7970, 7972, 7974, 7976, 7978, 7980, 7982, /* 0xe0-0xe7 */
  7984, 7986, 7988, 7990, 7992, 7994, 7996, 7998, /* 0xe8-0xef */
  8000, 8002, 8004, 8006, 8008, 8010, 8012, 8014, /* 0xf0-0xf7 */
  8016, 8018, 8020, 8022, 8024, 8026, 8028, 8030, /* 0xf8-0xff */
};
static const short translit_page2f8[544] = {
  8032, 8034, 8036, 8038, 8040, 8042, 8044, 8046, /* 0x00-0x07 */
  8048, 8050, 8052, 8054, 8056, 8058, 8060, 8062, /* 0x08-0x0f */
  8064, 8066, 8068, 8070, 8072, 8074, 8076, 8078, /* 0x10-0x17 */
  8080, 8082, 8084, 8086, 8088, 8090, 8092, 8094, /* 0x18-0x1f */
  8096, 8098, 8100, 8102, 8104, 8106, 8108, 8110, /* 0x20-0x27 */
  8112, 8114, 8116, 8118, 8120, 8122, 8124, 8126, /* 0x28-0x2f */
  8128, 8130, 8132, 8134, 8136, 8138, 8140, 8142, /* 0x30-0x37 */
  8144, 8146, 8148, 8150, 8152, 8154, 8156, 8158, /* 0x38-0x3f */
  8160, 8162, 8164, 8166, 8168, 8170, 8172, 8174, /* 0x40-0x47 */
  8176, 8178, 8180, 8182, 8184, 8186, 8188, 8190, /* 0x48-0x4f */
  8192, 8194, 8196, 8198, 8200, 8202, 8204, 8206, /* 0x50-0x57 */
  8208, 8210, 8212, 8214, 8216, 8218, 8220, 8222, /* 0x58-0x5f */
  8224, 8226, 8228, 8230, 8232, 8234, 8236, 8238, /* 0x60-0x67 */
  8240, 8242, 8244, 8246, 8248, 8250, 8252, 8254, /* 0x68-0x6f */
  8256, 8258, 8260, 8262, 8264, 8266, 8268, 8270, /* 0x70-0x77 */
  8272, 8274, 8276, 8278, 8280, 8282, 8284, 8286, /* 0x78-0x7f */
  8288, 8290, 8292, 8294, 8296, 8298, 8300, 8302, /* 0x80-0x87 */
  8304, 8306, 8308, 8310, 8312, 8314, 8316, 8318, /* 0x88-0x8f */
  8320, 8322, 8324, 8326, 8328, 8330, 8332, 8334, /* 0x90-0x97 */
  8336, 8338, 8340, 8342, 8344, 8346, 8348, 8350, /* 0x98-0x9f */
  8352, 8354, 8356, 8358, 8360, 8362, 8364, 8366, /* 0xa0-0xa7 */
  8368, 8370, 8372, 8374, 8376, 8378, 8380, 8382, /* 0xa8-0xaf */
  8384, 8386, 8388, 8390, 8392, 8394, 8396, 8398, /* 0xb0-0xb7 */
  8400, 8402, 8404, 8406, 8408, 8410, 8412, 8414, /* 0xb8-0xbf */
  8416, 8418, 8420, 8422, 8424, 8426, 8428, 8430, /* 0xc0-0xc7 */
  8432, 8434, 8436, 8438, 8440, 8442, 8444, 8446, /* 0xc8-0xcf */
  8448, 8450, 8452, 8454, 8456, 8458, 8460, 8462, /* 0xd0-0xd7 */
  8464, 8466, 8468, 8470, 8472, 8474, 8476, 8478, /* 0xd8-0xdf */
  8480, 8482, 8484, 8486, 8488, 8490, 8492, 8494, /* 0xe0-0xe7 */
  8496, 8498, 8500, 8502, 8504, 8506, 8508, 8510, /* 0xe8-0xef */
  8512, 8514, 8516, 8518, 8520, 8522, 8524, 8526, /* 0xf0-0xf7 */
  8528, 8530, 8532, 8534, 8536, 8538, 8540, 8542, /* 0xf8-0xff */
  /* 0x2f900 */
  8544, 8546, 8548, 8550, 8552, 8554, 8556, 8558, /* 0x00-0x07 */
  8560, 8562, 8564, 8566, 8568, 8570, 8572, 8574, /* 0x08-0x0f */
  8576, 8578, 8580, 8582, 8584, 8586, 8588, 8590, /* 0x10-0x17 */
  8592, 8594, 8596, 8598, 8600, 8602, 8604, 8606, /* 0x18-0x1f */
  8608, 8610, 8612, 8614, 8616, 8618, 8620, 8622, /* 0x20-0x27 */
  8624, 8626, 8628, 8630, 8632, 8634, 8636, 8638, /* 0x28-0x2f */
  8640, 8642, 8644, 8646, 8648, 8650, 8652, 8654, /* 0x30-0x37 */
  8656, 8658, 8660, 8662, 8664, 8666, 8668, 8670, /* 0x38-0x3f */
  8672, 8674, 8676, 8678, 8680, 8682, 8684, 8686, /* 0x40-0x47 */
  8688, 8690, 8692, 8694, 8696, 8698, 8700, 8702, /* 0x48-0x4f */
  8704, 8706, 8708, 8710, 8712, 8714, 8716, 8718, /* 0x50-0x57 */
  8720, 8722, 8724, 8726, 8728, 8730, 8732, 8734, /* 0x58-0x5f */
  8736, 8738, 8740, 8742, 8744, 8746, 8748, 8750, /* 0x60-0x67 */
  8752, 8754, 8756, 8758, 8760, 8762, 8764, 8766, /* 0x68-0x6f */
  8768, 8770, 8772, 8774, 8776, 8778, 8780, 8782, /* 0x70-0x77 */
  8784, 8786, 8788, 8790, 8792, 8794, 8796, 8798, /* 0x78-0x7f */
  8800, 8802, 8804, 8806, 8808, 8810, 8812, 8814, /* 0x80-0x87 */
  8816, 8818, 8820, 8822, 8824, 8826, 8828, 8830, /* 0x88-0x8f */
  8832, 8834, 8836, 8838, 8840, 8842, 8844, 8846, /* 0x90-0x97 */
  8848, 8850, 8852, 8854, 8856, 8858, 8860, 8862, /* 0x98-0x9f */
  8864, 8866, 8868, 8870, 8872, 8874, 8876, 8878, /* 0xa0-0xa7 */
  8880, 8882, 8884, 8886, 8888, 8890, 8892, 8894, /* 0xa8-0xaf */
  8896, 8898, 8900, 8902, 8904, 8906, 8908, 8910, /* 0xb0-0xb7 */
  8912, 8914, 8916, 8918, 8920, 8922, 8924, 8926, /* 0xb8-0xbf */
  8928, 8930, 8932, 8934, 8936, 8938, 8940, 8942, /* 0xc0-0xc7 */
  8944, 8946, 8948, 8950, 8952, 8954, 8956, 8958, /* 0xc8-0xcf */
  8960, 8962, 8964, 8966, 8968, 8970, 8972, 8974, /* 0xd0-0xd7 */
  8976, 8978, 8980, 8982, 8984, 8986, 8988, 8990, /* 0xd8-0xdf */
  8992, 8994, 8996, 8998, 9000, 9002, 9004, 9006, /* 0xe0-0xe7 */
  9008, 9010, 9012, 9014, 9016, 9018, 9020, 9022, /* 0xe8-0xef */
  9024, 9026, 9028, 9030, 9032, 9034, 9036, 9038, /* 0xf0-0xf7 */
  9040, 9042, 9044, 9046, 9048, 9050, 9052, 9054, /* 0xf8-0xff */
  /* 0x2fa00 */
  9056, 9058, 9060, 9062, 9064, 9066, 9068, 9070, /* 0x00-0x07 */
  9072, 9074, 9076, 9078, 9080, 9082, 9084, 9086, /* 0x08-0x0f */
  9088, 9090, 9092, 9094, 9096, 9098, 9100, 9102, /* 0x10-0x17 */
  9104, 9106, 9108, 9110, 9112, 9114,   -1,   -1, /* 0x18-0x1f */
};

#define translit_index(wc) \
  (wc >= 0x00a0 && wc < 0x01f8 ? translit_page00[wc-0x00a0] : \
   wc >= 0x0218 && wc < 0x0220 ? translit_page02[wc-0x0218] : \
   wc >= 0x02b8 && wc < 0x02e0 ? translit_page02_1[wc-0x02b8] : \
   wc >= 0x03d0 && wc < 0x0400 ? translit_page03[wc-0x03d0] : \
   wc == 0x0587 ? 654 : \
   wc >= 0x05f0 && wc < 0x05f8 ? translit_page05[wc-0x05f0] : \
   wc >= 0x0670 && wc < 0x0680 ? translit_page06[wc-0x0670] : \
   wc == 0x0e33 ? 678 : \
   wc >= 0x0eb0 && wc < 0x0ee0 ? translit_page0e[wc-0x0eb0] : \
   wc >= 0x0f70 && wc < 0x0f80 ? translit_page0f[wc-0x0f70] : \
   wc >= 0x1e00 && wc < 0x1ea0 ? translit_page1e[wc-0x1e00] : \
   wc >= 0x1ef0 && wc < 0x1ef8 ? translit_page1e_1[wc-0x1ef0] : \
   wc >= 0x2000 && wc < 0x2058 ? translit_page20[wc-0x2000] : \
   wc >= 0x20a8 && wc < 0x20b0 ? translit_page20_1[wc-0x20a8] : \
   wc >= 0x2100 && wc < 0x21d8 ? translit_page21[wc-0x2100] : \
   wc >= 0x2210 && wc < 0x2270 ? translit_page22[wc-0x2210] : \
   wc >= 0x22c0 && wc < 0x22f0 ? translit_page22_1[wc-0x22c0] : \
   wc >= 0x2400 && wc < 0x24f0 ? translit_page24[wc-0x2400] : \
   wc >= 0x2500 && wc < 0x2540 ? translit_page25[wc-0x2500] : \
   wc == 0x25e6 ? 2025 : \
   wc == 0x2a0c ? 2027 : \
   wc >= 0x2a70 && wc < 0x2a78 ? translit_page2a[wc-0x2a70] : \
   wc == 0x2e9f ? 2043 : \
   wc == 0x2ef3 ? 2045 : \
   wc >= 0x2f00 && wc < 0x2fd8 ? translit_page2f[wc-0x2f00] : \
   wc >= 0x3000 && wc < 0x30f8 ? translit_page30[wc-0x3000] : \
   wc >= 0x3130 && wc < 0x3190 ? translit_page31[wc-0x3130] : \
   wc >= 0x31f0 && wc < 0x3400 ? translit_page31_1[wc-0x31f0] : \
   wc >= 0xf900 && wc < 0xfa70 ? translit_pagef9[wc-0xf900] : \
   wc >= 0xfb00 && wc < 0xfb50 ? translit_pagefb[wc-0xfb00] : \
   wc >= 0xfe48 && wc < 0xfe70 ? translit_pagefe[wc-0xfe48] : \
   wc >= 0xff00 && wc < 0xfff0 ? translit_pageff[wc-0xff00] : \
   wc >= 0x1d400 && wc < 0x1d800 ? translit_page1d4[wc-0x1d400] : \
   wc >= 0x2f800 && wc < 0x2fa20 ? translit_page2f8[wc-0x2f800] : \
   -1)
