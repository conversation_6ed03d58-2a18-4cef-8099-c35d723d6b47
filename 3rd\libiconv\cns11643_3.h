/*
 * Copyright (C) 1999-2002, 2016 Free Software Foundation, Inc.
 * This file is part of the GNU LIBICONV Library.
 *
 * The GNU LIBICONV Library is free software; you can redistribute it
 * and/or modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either version 2.1
 * of the License, or (at your option) any later version.
 *
 * The GNU LIBICONV Library is distributed in the hope that it will be
 * useful, but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with the GNU LIBICONV Library; see the file COPYING.LIB.
 * If not, see <https://www.gnu.org/licenses/>.
 */

/*
 * CNS 11643-1992 plane 3
 */

static const unsigned short cns11643_3_2uni_page21[6148] = {
  /* 0x21 */
  0x1a28, 0x1a36, 0x1a3f, 0x1a85, 0x1a05, 0x1a04, 0x1d82, 0x1d96,
  0x1f38, 0x1f69, 0x1fb6, 0x1a2a, 0x1a87, 0x1a49, 0x1de2, 0x1a46,
  0x1a8f, 0x1abc, 0x1abe, 0x1d66, 0x1de3, 0x1e04, 0x1e9c, 0x1f44,
  0x2502, 0x250a, 0x2780, 0x29db, 0x2a7a, 0x2a7f, 0x2af4, 0x2b50,
  0x2b51, 0x2b61, 0x621d, 0x6d0b, 0x1a63, 0x1a62, 0x1aa3, 0x1d85,
  0x1ac5, 0x1acf, 0x1ace, 0x1acc, 0x1d84, 0x1d86, 0x8517, 0x00c5,
  0x1de4, 0x1e05, 0x1e9e, 0x1e9d, 0x1efd, 0x1f00, 0x1f3a, 0x0139,
  0x1f46, 0x1f5d, 0x1f86, 0x1fb7, 0x0155, 0x1fcc, 0x015b, 0x1fce,
  0x2321, 0x03a2, 0x2a00, 0x2b0c, 0x2e37, 0x2e38, 0x3134, 0x3135,
  0x31e0, 0x0a26, 0x3f8d, 0x1a97, 0x1ae0, 0x0032, 0x6ea9, 0x1ae7,
  0x0033, 0x1ae6, 0x0034, 0x02a2, 0x0031, 0x00b0, 0x22d8, 0x1d8b,
  0x1d8c, 0x1d99, 0x1de5, 0x8550, 0x1e0b, 0x00dc,
  /* 0x22 */
  0x021e, 0x1f04, 0x1f03, 0x1f07, 0x852a, 0x1f1e, 0x1f5f, 0x1f6d,
  0x1f89, 0x1fba, 0x1fd0, 0x0165, 0x1ff6, 0x1ff7, 0x1ff9, 0x0164,
  0x1ff4, 0x021d, 0x0226, 0x2324, 0x2504, 0x2518, 0x2532, 0x2530,
  0x2534, 0x028e, 0x2575, 0x034a, 0x2782, 0x27f9, 0x2814, 0x038b,
  0x03a6, 0x03a4, 0x03a5, 0x03a7, 0x042f, 0x0432, 0x2a81, 0x2a83,
  0x2b0d, 0x2b52, 0x04d4, 0x2bca, 0x2bc7, 0x2e39, 0x05c5, 0x2e4f,
  0x31e7, 0x332f, 0x377a, 0x3839, 0x08ba, 0x08b9, 0x3837, 0x3844,
  0x3845, 0x3f8c, 0x4192, 0x4276, 0x5c93, 0x5c92, 0x14b3, 0x15ba,
  0x1a21, 0x1a20, 0x1a22, 0x1a68, 0x1a89, 0x1a98, 0x1af9, 0x1aef,
  0x003b, 0x003c, 0x1af8, 0x1b06, 0x1b03, 0x1afc, 0x1aee, 0x1b16,
  0x0039, 0x1b28, 0x1b1c, 0x1b07, 0x1b1a, 0x1afa, 0x1b17, 0x1d4a,
  0x00b2, 0x1d72, 0x8515, 0x1db4, 0x1db3, 0x1db2,
  /* 0x23 */
  0x00c7, 0x1de8, 0x002b, 0x1e14, 0x1e0f, 0x1e15, 0x1e18, 0x1ea8,
  0x852c, 0x1f4b, 0x1f4f, 0x013b, 0x1f50, 0x0144, 0x1f8b, 0x0142,
  0x1fbe, 0x015c, 0x1fd2, 0x2016, 0x1fff, 0x0167, 0x2000, 0x0166,
  0x2005, 0x2013, 0x2015, 0x853b, 0x021f, 0x22e3, 0x2335, 0x2336,
  0x2331, 0x2332, 0x24ee, 0x2505, 0x1a54, 0x028f, 0x2536, 0x0290,
  0x02a8, 0x02a4, 0x257a, 0x02a3, 0x2586, 0x033d, 0x034c, 0x2786,
  0x2b53, 0x2818, 0x038c, 0x283d, 0x2878, 0x03a8, 0x03ad, 0x03af,
  0x7746, 0x2880, 0x0429, 0x2a08, 0x0436, 0x0471, 0x0470, 0x046f,
  0x2af5, 0x2b0e, 0x04a9, 0x04aa, 0x04fb, 0x2bd3, 0x2bda, 0x04fc,
  0x2bdb, 0x05ae, 0x2e0f, 0x2e5d, 0x2e5f, 0x2e67, 0x2e57, 0x6b50,
  0x06c3, 0x31eb, 0x31ea, 0x0730, 0x3337, 0x0741, 0x3332, 0x3336,
  0x3722, 0x37ce, 0x088c, 0x3858, 0x3851, 0x3877,
  /* 0x24 */
  0x383c, 0x08bb, 0x385a, 0x7c86, 0x3853, 0x3c6f, 0x3c72, 0x3c6e,
  0x8535, 0x09a1, 0x3c73, 0x3eb1, 0x3eb2, 0x0aa8, 0x3f8f, 0x0aaa,
  0x0aab, 0x0c96, 0x453c, 0x0dc2, 0x4c8d, 0x4c8e, 0x1093, 0x4e7b,
  0x1094, 0x5971, 0x5bb9, 0x5c96, 0x5c9a, 0x15bb, 0x1a24, 0x1a71,
  0x851b, 0x1a9c, 0x1b45, 0x1b4a, 0x1b39, 0x1b37, 0x0043, 0x1b32,
  0x1b42, 0x0042, 0x1b44, 0x1b4b, 0x0044, 0x1b40, 0x1b35, 0x1b31,
  0x1d51, 0x850e, 0x1d50, 0x1d4e, 0x00b3, 0x00b7, 0x1d9d, 0x00c8,
  0x1db5, 0x1db8, 0x1dec, 0x1e23, 0x1e27, 0x1e26, 0x1e1f, 0x1e2b,
  0x1e20, 0x1eb4, 0x1eb3, 0x0118, 0x1f25, 0x1f3b, 0x1f74, 0x0147,
  0x0146, 0x0145, 0x016b, 0x0169, 0x204d, 0x0172, 0x0171, 0x203a,
  0x016c, 0x016f, 0x2044, 0x204c, 0x2023, 0x201a, 0x2032, 0x204b,
  0x2021, 0x0173, 0x2034, 0x2049, 0x2050, 0x2022,
  /* 0x25 */
  0x203f, 0x2051, 0x205a, 0x202f, 0x0176, 0x22e9, 0x22f2, 0x22f3,
  0x22ef, 0x22ed, 0x22ec, 0x22e6, 0x2348, 0x0227, 0x2344, 0x233f,
  0x233c, 0x2353, 0x2356, 0x0230, 0x235f, 0x2343, 0x2358, 0x2357,
  0x0229, 0x022a, 0x022f, 0x2346, 0x022c, 0x233d, 0x022d, 0x2342,
  0x2354, 0x2355, 0x24f1, 0x24f2, 0x24f0, 0x250b, 0x6aa6, 0x22f1,
  0x253d, 0x0293, 0x2594, 0x258c, 0x02ad, 0x259c, 0x02ac, 0x02ab,
  0x259f, 0x02a9, 0x259b, 0x02ae, 0x2589, 0x259a, 0x02aa, 0x3188,
  0x034e, 0x278d, 0x0350, 0x27fe, 0x27ff, 0x27fd, 0x282b, 0x03b2,
  0x2884, 0x288e, 0x289c, 0x03b5, 0x03b6, 0x2885, 0x29f5, 0x2a09,
  0x0439, 0x043b, 0x2a0b, 0x0472, 0x2a92, 0x2a90, 0x2b03, 0x04ac,
  0x2b1e, 0x2b63, 0x0508, 0x2be7, 0x2bfe, 0x2be6, 0x2bdc, 0x2bce,
  0x0503, 0x2bfc, 0x2bdf, 0x2bec, 0x2bf6, 0x79d7,
  /* 0x26 */
  0x2bf2, 0x2bf0, 0x2bf9, 0x050b, 0x2e13, 0x05af, 0x85b2, 0x2e3b,
  0x2e3c, 0x2e82, 0x05ce, 0x05cb, 0x05cc, 0x2e78, 0x2e8b, 0x05cd,
  0x2e9e, 0x2ea5, 0x2e9b, 0x2e9c, 0x2e99, 0x2e8d, 0x2e85, 0x2e9d,
  0x2e75, 0x0680, 0x06af, 0x06d3, 0x31f6, 0x06d5, 0x06d4, 0x06d7,
  0x32f5, 0x335b, 0x0742, 0x3354, 0x3352, 0x0744, 0x3358, 0x3344,
  0x334a, 0x3361, 0x08c6, 0x387f, 0x3891, 0x389e, 0x08c0, 0x386e,
  0x387c, 0x389f, 0x3875, 0x08be, 0x3856, 0x38a2, 0x3879, 0x08ca,
  0x38a1, 0x08c4, 0x38aa, 0x38a0, 0x08c2, 0x3c79, 0x3c77, 0x3c7e,
  0x09a4, 0x3c75, 0x3c7b, 0x3e64, 0x0a29, 0x3ebb, 0x3ebc, 0x3ec7,
  0x3eb9, 0x3ebe, 0x3eb6, 0x0a60, 0x0a5e, 0x3f98, 0x0aad, 0x0aae,
  0x0aac, 0x0b57, 0x4193, 0x4280, 0x0bdd, 0x4283, 0x42c0, 0x42c1,
  0x0c0e, 0x0c97, 0x43f4, 0x43f5, 0x0d27, 0x46cc,
  /* 0x27 */
  0x46cd, 0x48fa, 0x4c9f, 0x4c91, 0x4c97, 0x4c94, 0x1095, 0x4e86,
  0x4e8c, 0x868f, 0x4e95, 0x1098, 0x526c, 0x119d, 0x5bb5, 0x5bbe,
  0x5bc7, 0x148a, 0x5bc1, 0x5ca9, 0x5ca4, 0x14b5, 0x14b6, 0x14b7,
  0x5ca8, 0x6227, 0x6226, 0x622b, 0x6233, 0x6234, 0x6229, 0x1a3d,
  0x0028, 0x1a9d, 0x1b93, 0x1b8a, 0x004d, 0x0049, 0x1b6d, 0x1b8e,
  0x1ba0, 0x1ba2, 0x1ba1, 0x1b9f, 0x1ba3, 0x6f09, 0x1b72, 0x0051,
  0x1b8c, 0x1d56, 0x850f, 0x8511, 0x1d90, 0x00cb, 0x00ca, 0x00cc,
  0x1ded, 0x1dfe, 0x1e2f, 0x71ec, 0x1e3c, 0x1e34, 0x1e39, 0x1eb9,
  0x1eb5, 0x1ebf, 0x1f55, 0x013d, 0x1f76, 0x1f7a, 0x1f93, 0x0148,
  0x1fc1, 0x1fc2, 0x1fd5, 0x2085, 0x0178, 0x205f, 0x2093, 0x2089,
  0x2079, 0x6afe, 0x208f, 0x2069, 0x206d, 0x017a, 0x2094, 0x206a,
  0x208a, 0x0177, 0x22fd, 0x22fb, 0x22f8, 0x0221,
  /* 0x28 */
  0x22fc, 0x22f6, 0x2365, 0x2381, 0x2363, 0x2367, 0x0231, 0x236e,
  0x2378, 0x237f, 0x0233, 0x0234, 0x24f3, 0x254b, 0x254c, 0x02c1,
  0x02b0, 0x02b4, 0x25ad, 0x02b8, 0x25c4, 0x02bc, 0x25c2, 0x25b0,
  0x02bf, 0x02b5, 0x02b1, 0x02bd, 0x25bf, 0x02bb, 0x25c9, 0x25b8,
  0x25ac, 0x02b3, 0x02b6, 0x02ba, 0x25b7, 0x25d7, 0x02b7, 0x2760,
  0x0340, 0x2796, 0x279e, 0x2794, 0x279f, 0x279d, 0x0352, 0x2800,
  0x2819, 0x0390, 0x0391, 0x2849, 0x284a, 0x03be, 0x28bb, 0x28c1,
  0x03c0, 0x03c1, 0x03b9, 0x28b9, 0x289e, 0x28b4, 0x28ba, 0x29f6,
  0x2a13, 0x2a12, 0x2a77, 0x0479, 0x2a98, 0x047b, 0x2a99, 0x2a9d,
  0x2af8, 0x04a0, 0x2af9, 0x0029, 0x2b06, 0x2b21, 0x04ae, 0x2b25,
  0x2b55, 0x04cd, 0x04cb, 0x04d9, 0x2b84, 0x2b83, 0x2c30, 0x2c07,
  0x050c, 0x2c36, 0x0501, 0x0505, 0x0502, 0x2be9,
  /* 0x29 */
  0x2c3d, 0x2c08, 0x0513, 0x0511, 0x2eba, 0x2eb2, 0x05e4, 0x2eb7,
  0x2ee4, 0x2ea7, 0x05da, 0x05d5, 0x05d3, 0x2ed5, 0x2ee1, 0x2edd,
  0x2ea6, 0x2ec1, 0x2ec5, 0x2ec0, 0x2edf, 0x2ee0, 0x2ede, 0x05d6,
  0x3189, 0x06b4, 0x31a6, 0x31ba, 0x06d9, 0x31ff, 0x06d8, 0x3217,
  0x3218, 0x3201, 0x31fe, 0x0733, 0x330c, 0x0748, 0x336b, 0x3396,
  0x3382, 0x338a, 0x0747, 0x33a3, 0x074b, 0x33a2, 0x338f, 0x074a,
  0x33f9, 0x3380, 0x3726, 0x3727, 0x3768, 0x3769, 0x085a, 0x3781,
  0x37b4, 0x37d1, 0x088e, 0x08b4, 0x381c, 0x08cd, 0x08cc, 0x08cf,
  0x08cb, 0x08ce, 0x3897, 0x386c, 0x38df, 0x08d2, 0x38ea, 0x08d1,
  0x38e4, 0x38d8, 0x38b2, 0x38ce, 0x38c8, 0x09a6, 0x3c8b, 0x3c88,
  0x3c90, 0x3c8f, 0x09aa, 0x3c87, 0x3c89, 0x3c8d, 0x3c81, 0x09a8,
  0x3c8c, 0x0a13, 0x0a1a, 0x3e40, 0x0a1d, 0x0a1e,
  /* 0x2a */
  0x3e65, 0x3e66, 0x3e68, 0x0a65, 0x0a66, 0x3ecd, 0x3ed3, 0x3edb,
  0x0a64, 0x3ecf, 0x3fa7, 0x3fa3, 0x3f9e, 0x0ab0, 0x3faf, 0x0ab3,
  0x0ab5, 0x3faa, 0x3f9c, 0x0b19, 0x4142, 0x4144, 0x413b, 0x4141,
  0x783f, 0x419b, 0x419e, 0x0b75, 0x45c4, 0x45c3, 0x45c6, 0x0d2b,
  0x0d2c, 0x45c7, 0x0d2d, 0x45ca, 0x802e, 0x0dc3, 0x46cf, 0x4876,
  0x4874, 0x48ff, 0x48fc, 0x00ba, 0x0f50, 0x4b59, 0x4ca8, 0x0fd3,
  0x0fd0, 0x4cb0, 0x0fdc, 0x4cb3, 0x0fd2, 0x4ca4, 0x4cb6, 0x4ca7,
  0x4cac, 0x0fdb, 0x4ca6, 0x1f67, 0x4e0e, 0x4ec4, 0x4f3e, 0x4e9c,
  0x10a5, 0x109f, 0x109a, 0x109c, 0x10a2, 0x4eaa, 0x109b, 0x4ec9,
  0x10a3, 0x109d, 0x4ea6, 0x4eb2, 0x1188, 0x121a, 0x148d, 0x5bcc,
  0x5bd9, 0x5bca, 0x5bd8, 0x5bcf, 0x5cb7, 0x14b8, 0x5cad, 0x5cb9,
  0x6237, 0x15c3, 0x6241, 0x623e, 0x62b6, 0x6351,
  /* 0x2b */
  0x6363, 0x1a57, 0x1a79, 0x1ab2, 0x1ab0, 0x1aaf, 0x1ab1, 0x1bd2,
  0x1bd5, 0x005d, 0x1bbe, 0x1bb8, 0x1bb0, 0x1bb1, 0x1bc8, 0x005a,
  0x0057, 0x1bc6, 0x1bcc, 0x1be5, 0x1be3, 0x1bb4, 0x1d6a, 0x00b8,
  0x1d9f, 0x00c2, 0x1dc1, 0x00cf, 0x1dc2, 0x1dc3, 0x1e45, 0x1e48,
  0x00e7, 0x00e9, 0x1e4f, 0x1052, 0x00e8, 0x1ec5, 0x1eca, 0x1ec4,
  0x1f27, 0x1f58, 0x1f7d, 0x014a, 0x1fdd, 0x1fdc, 0x1fda, 0x1fd9,
  0x20b9, 0x0180, 0x20d0, 0x20b4, 0x20ca, 0x0187, 0x20a3, 0x20da,
  0x20a4, 0x0184, 0x20b2, 0x209e, 0x209f, 0x20b5, 0x0182, 0x0181,
  0x20cd, 0x0183, 0x20cc, 0x0222, 0x2300, 0x23ac, 0x2391, 0x238e,
  0x238d, 0x2392, 0x23a1, 0x2390, 0x23a6, 0x23a8, 0x023b, 0x239c,
  0x2396, 0x23a7, 0x023a, 0x0238, 0x0239, 0x0236, 0x24f5, 0x0285,
  0x2509, 0x2508, 0x0854, 0x2552, 0x029a, 0x02c4,
  /* 0x2c */
  0x25df, 0x02c5, 0x25eb, 0x25ef, 0x25f0, 0x25d5, 0x260d, 0x2604,
  0x25f9, 0x2602, 0x25f8, 0x25e2, 0x25d9, 0x25e7, 0x276a, 0x0354,
  0x0355, 0x27ab, 0x0356, 0x281b, 0x282f, 0x0396, 0x323c, 0x0395,
  0x0394, 0x03c4, 0x28d1, 0x28dc, 0x28e6, 0x28e1, 0x28cd, 0x857a,
  0x28e2, 0x28dd, 0x28e5, 0x29fb, 0x29fa, 0x2a1e, 0x0444, 0x2aa1,
  0x047d, 0x047e, 0x2afc, 0x2afb, 0x2b2f, 0x04b2, 0x04b6, 0x2b66,
  0x8599, 0x04dc, 0x04df, 0x2c5c, 0x0528, 0x2c4e, 0x2c51, 0x0519,
  0x0510, 0x2c23, 0x2c31, 0x2c7c, 0x2c52, 0x052c, 0x2c60, 0x2c4a,
  0x2c61, 0x051b, 0x2e18, 0x05c2, 0x05ef, 0x05e3, 0x05e5, 0x05ea,
  0x05e6, 0x05ee, 0x2f1f, 0x2f17, 0x2eea, 0x2f21, 0x2f04, 0x2f05,
  0x05e8, 0x3131, 0x3144, 0x3140, 0x0685, 0x3142, 0x31be, 0x06e0,
  0x3229, 0x321b, 0x06dd, 0x3223, 0x322c, 0x321a,
  /* 0x2d */
  0x3230, 0x323b, 0x321e, 0x3237, 0x3238, 0x06e1, 0x330e, 0x0751,
  0x0755, 0x33e8, 0x33d6, 0x0752, 0x33c7, 0x33bc, 0x3452, 0x33bf,
  0x33d5, 0x33fe, 0x4f63, 0x33fb, 0x85df, 0x33b1, 0x3401, 0x3405,
  0x3400, 0x33d7, 0x0c9e, 0x372a, 0x376b, 0x0852, 0x085e, 0x0860,
  0x085f, 0x37e1, 0x0892, 0x08d6, 0x3923, 0x38ff, 0x3914, 0x3905,
  0x3913, 0x3906, 0x3921, 0x08de, 0x3915, 0x38af, 0x38f4, 0x3902,
  0x3945, 0x85fe, 0x3926, 0x08d9, 0x3944, 0x08dd, 0x3924, 0x3ca5,
  0x09ac, 0x3ca3, 0x09b0, 0x3ca2, 0x3cbb, 0x3ca0, 0x3caa, 0x09af,
  0x09ae, 0x3ca8, 0x3cb6, 0x3cb2, 0x3ca7, 0x09ad, 0x09ab, 0x3cb9,
  0x3e2e, 0x0a16, 0x3e3c, 0x0a30, 0x3e6d, 0x0a33, 0x0a31, 0x3ee7,
  0x3eed, 0x0a6e, 0x3eec, 0x3ee5, 0x3ee2, 0x0ab1, 0x3fc4, 0x3fbd,
  0x3fcf, 0x3fc9, 0x3fc1, 0x3fd0, 0x0ab7, 0x3fce,
  /* 0x2e */
  0x40ed, 0x40eb, 0x0b1a, 0x40ef, 0x4149, 0x4150, 0x4146, 0x414a,
  0x0b59, 0x414d, 0x41a6, 0x0b7a, 0x0b78, 0x0b7b, 0x41a8, 0x0bde,
  0x0bec, 0x42c7, 0x42ff, 0x0c1e, 0x42fd, 0x43e6, 0x440a, 0x0c9b,
  0x4404, 0x440b, 0x4407, 0x0c9d, 0x4415, 0x4408, 0x0cfd, 0x45d3,
  0x45d4, 0x45d0, 0x45d7, 0x467c, 0x0d94, 0x0d93, 0x467d, 0x4683,
  0x4682, 0x0dc6, 0x46d4, 0x46d5, 0x46d3, 0x46d0, 0x46d2, 0x46fe,
  0x46fc, 0x4877, 0x487c, 0x487b, 0x0eb8, 0x866a, 0x0eb7, 0x0eb9,
  0x0f53, 0x7f33, 0x0f52, 0x0f51, 0x4b8f, 0x4cd3, 0x0fe3, 0x4ccb,
  0x4cd2, 0x0fe2, 0x4d09, 0x4ce2, 0x4cdf, 0x4cc6, 0x1063, 0x4e24,
  0x4ef7, 0x4ed8, 0x4edd, 0x10aa, 0x10a6, 0x4ef8, 0x4efc, 0x10a8,
  0x10a9, 0x4ee9, 0x10ab, 0x4eee, 0x10ac, 0x4ed0, 0x4f0e, 0x4ee2,
  0x4f0b, 0x4efd, 0x1d79, 0x5276, 0x119e, 0x5278,
  /* 0x2f */
  0x119f, 0x11a0, 0x5275, 0x527d, 0x120f, 0x5442, 0x5466, 0x121c,
  0x558c, 0x5605, 0x12ae, 0x5606, 0x12b0, 0x589f, 0x13d4, 0x5bf1,
  0x5be7, 0x5be9, 0x5bef, 0x5cc2, 0x5cbc, 0x14bb, 0x5cc6, 0x5cc0,
  0x14c1, 0x14c2, 0x5ccd, 0x5cc9, 0x14be, 0x5cc4, 0x14e5, 0x6181,
  0x15c6, 0x68ec, 0x1c32, 0x1bf9, 0x1c1d, 0x1bff, 0x1c04, 0x1bf0,
  0x1c03, 0x122e, 0x1c02, 0x1bfc, 0x1bf2, 0x1c24, 0x1c08, 0x1c36,
  0x1c2e, 0x0065, 0x1c10, 0x1c38, 0x1c39, 0x1bfd, 0x1c56, 0x1bfb,
  0x1da3, 0x1da6, 0x1da1, 0x00d1, 0x00d0, 0x1dc7, 0x1dc9, 0x1e60,
  0x1e64, 0x1e59, 0x1e65, 0x1e67, 0x1e57, 0x1e63, 0x00ee, 0x1e53,
  0x00ef, 0x1ecf, 0x011e, 0x1ece, 0x1ed0, 0x1ed1, 0x1ecc, 0x014b,
  0x014d, 0x0156, 0x210d, 0x20f4, 0x0192, 0x2113, 0x20ef, 0x20f5,
  0x20f9, 0x2102, 0x2100, 0x0193, 0x0190, 0x2118,
  /* 0x30 */
  0x20f0, 0x20f6, 0x8541, 0x0197, 0x2119, 0x0223, 0x2305, 0x23c9,
  0x023f, 0x23b7, 0x23cd, 0x0243, 0x0242, 0x0244, 0x23be, 0x23bb,
  0x0245, 0x23db, 0x23c8, 0x23c4, 0x23c5, 0x23d1, 0x23ca, 0x23c0,
  0x02d9, 0x02de, 0x2621, 0x262a, 0x02cf, 0x261d, 0x02cd, 0x260b,
  0x02dd, 0x02ce, 0x02d3, 0x02d6, 0x2622, 0x02dc, 0x02d1, 0x2624,
  0x02d0, 0x2614, 0x2631, 0x02d5, 0x262f, 0x261a, 0x2612, 0x02d4,
  0x02db, 0x2626, 0x762e, 0x0343, 0x27bc, 0x27bb, 0x27b7, 0x2805,
  0x2806, 0x2852, 0x2853, 0x03cd, 0x03d1, 0x28fa, 0x28eb, 0x03ca,
  0x28f3, 0x28f5, 0x28e9, 0x28ef, 0x03d4, 0x2a2a, 0x2a30, 0x2a2e,
  0x2a2c, 0x2a2f, 0x2aaf, 0x2aa9, 0x0486, 0x2afd, 0x2b32, 0x2b8e,
  0x2b93, 0x2b8f, 0x2c4f, 0x2c99, 0x0533, 0x2c7e, 0x0537, 0x2c74,
  0x2c4b, 0x2c73, 0x2c75, 0x052a, 0x051f, 0x2c56,
  /* 0x31 */
  0x2ca9, 0x2c8b, 0x2ca6, 0x0539, 0x2c93, 0x2cae, 0x2c9e, 0x2ca7,
  0x2e45, 0x05f2, 0x05f8, 0x2f2e, 0x05f7, 0x2f52, 0x2f30, 0x2f5b,
  0x05f4, 0x2f19, 0x2f1b, 0x05f1, 0x2f31, 0x2f5d, 0x2f37, 0x2f35,
  0x2f53, 0x05f5, 0x2f5c, 0x2f3f, 0x314b, 0x0687, 0x0f69, 0x318b,
  0x06b6, 0x319a, 0x3250, 0x3246, 0x324e, 0x3240, 0x06e9, 0x324b,
  0x3248, 0x06eb, 0x3260, 0x3244, 0x324d, 0x0734, 0x3437, 0x3424,
  0x0762, 0x075c, 0x341b, 0x3436, 0x0760, 0x342c, 0x3419, 0x3456,
  0x3447, 0x343e, 0x341e, 0x85e1, 0x3415, 0x3422, 0x3427, 0x3459,
  0x3458, 0x3455, 0x3430, 0x3423, 0x372e, 0x372b, 0x3730, 0x376c,
  0x0861, 0x378b, 0x087f, 0x37e9, 0x37ea, 0x37e5, 0x396b, 0x08e5,
  0x08e6, 0x3973, 0x3957, 0x08e9, 0x08f3, 0x395d, 0x3956, 0x398f,
  0x395b, 0x391c, 0x399a, 0x399b, 0x3999, 0x08ee,
  /* 0x32 */
  0x3981, 0x3971, 0x08ed, 0x08ec, 0x3972, 0x395c, 0x3996, 0x3cc4,
  0x3cdb, 0x3ccc, 0x3cd0, 0x3ce3, 0x3cdf, 0x09b3, 0x3cd6, 0x3cee,
  0x3cd5, 0x09b5, 0x0a27, 0x0a35, 0x0a36, 0x3e7a, 0x0a71, 0x3ef5,
  0x3f02, 0x0ab8, 0x0ac2, 0x3fe2, 0x3fec, 0x3fd5, 0x3ff9, 0x3fdf,
  0x3fe6, 0x0ac8, 0x0ac0, 0x0ac1, 0x0ac4, 0x3fe4, 0x3fe1, 0x40f3,
  0x0b1f, 0x0b1c, 0x0b1d, 0x0b4d, 0x4156, 0x4155, 0x4158, 0x4157,
  0x415e, 0x41c3, 0x0b87, 0x0b82, 0x41b4, 0x0b7d, 0x41b1, 0x0bdf,
  0x0c00, 0x42cb, 0x42cc, 0x432a, 0x0c20, 0x4316, 0x430f, 0x0c22,
  0x0c24, 0x433f, 0x432b, 0x430e, 0x4324, 0x0c21, 0x4321, 0x4318,
  0x43dd, 0x0ca4, 0x0ca5, 0x4424, 0x4436, 0x0d01, 0x4558, 0x4559,
  0x0d03, 0x4562, 0x45da, 0x45d9, 0x0d37, 0x45e1, 0x45e5, 0x45e8,
  0x45db, 0x0d38, 0x45e2, 0x45f0, 0x0d99, 0x0d98,
  /* 0x33 */
  0x0d97, 0x0dc9, 0x46da, 0x46dd, 0x0dc7, 0x46db, 0x46dc, 0x0dd9,
  0x0ddb, 0x470d, 0x470b, 0x4714, 0x488e, 0x4886, 0x0e7b, 0x4887,
  0x4883, 0x488b, 0x0e7c, 0x0ebd, 0x0ebc, 0x0ec3, 0x4924, 0x0ec1,
  0x0ebf, 0x0ec4, 0x4925, 0x4b62, 0x4b93, 0x4b99, 0x4b97, 0x0f7e,
  0x0f7f, 0x4bc4, 0x4bc6, 0x4c0a, 0x0fb4, 0x0fb3, 0x4c40, 0x4c3c,
  0x4c3b, 0x4cf6, 0x4cff, 0x4cee, 0x4d04, 0x4d03, 0x4d07, 0x8683,
  0x0fe6, 0x4cf7, 0x1059, 0x105a, 0x4e2d, 0x1064, 0x4e27, 0x4e29,
  0x4f1f, 0x4f57, 0x10b4, 0x10b9, 0x10b7, 0x10b5, 0x4f21, 0x10c1,
  0x10b1, 0x4f18, 0x4f58, 0x10b3, 0x10ba, 0x118c, 0x118b, 0x118d,
  0x5284, 0x529f, 0x529b, 0x5289, 0x52a6, 0x5292, 0x528f, 0x52a0,
  0x544f, 0x5478, 0x547a, 0x546e, 0x547b, 0x5484, 0x5473, 0x1278,
  0x1277, 0x560d, 0x560b, 0x5619, 0x12b2, 0x13d6,
  /* 0x34 */
  0x5ad0, 0x1445, 0x1492, 0x1495, 0x5bf9, 0x5c09, 0x5c08, 0x14c6,
  0x5cde, 0x5d51, 0x14e7, 0x14e8, 0x5ddb, 0x5ddf, 0x5dde, 0x5dd6,
  0x5de0, 0x6185, 0x6260, 0x6259, 0x15cb, 0x6256, 0x15cd, 0x15f1,
  0x62bd, 0x1722, 0x0021, 0x1c42, 0x1c59, 0x006f, 0x1c44, 0x1c66,
  0x1c52, 0x1c54, 0x1c71, 0x1c50, 0x1c7b, 0x1c7c, 0x1c58, 0x0070,
  0x0064, 0x1c79, 0x1c6c, 0x1c78, 0x1da8, 0x1dd1, 0x1dcf, 0x1e68,
  0x1e76, 0x1ed4, 0x012d, 0x1fa0, 0x1fc4, 0x0158, 0x2158, 0x214c,
  0x2168, 0x01a6, 0x2149, 0x01a4, 0x019f, 0x215d, 0x2129, 0x73ae,
  0x2154, 0x2153, 0x01a3, 0x215a, 0x01a0, 0x213a, 0x213f, 0x212b,
  0x23ea, 0x024a, 0x23ef, 0x0247, 0x0248, 0x23dd, 0x23fe, 0x8555,
  0x23de, 0x23e6, 0x0249, 0x23e8, 0x23ff, 0x2403, 0x24f7, 0x34a6,
  0x251f, 0x029e, 0x255b, 0x255d, 0x255e, 0x7537,
  /* 0x35 */
  0x02e8, 0x262b, 0x02ec, 0x263b, 0x02ed, 0x02e6, 0x2661, 0x263a,
  0x266e, 0x264b, 0x266b, 0x02eb, 0x02e7, 0x2645, 0x264e, 0x2668,
  0x263d, 0x2671, 0x263f, 0x266f, 0x2675, 0x02e9, 0x2673, 0x262c,
  0x2659, 0x2654, 0x264f, 0x2663, 0x035c, 0x035d, 0x27c8, 0x0360,
  0x27c3, 0x035b, 0x285b, 0x2861, 0x0399, 0x2921, 0x290a, 0x2909,
  0x03d8, 0x292c, 0x2908, 0x03da, 0x03dd, 0x292a, 0x2915, 0x03e0,
  0x2910, 0x2913, 0x03e5, 0x292f, 0x2918, 0x03d7, 0x29e3, 0x2a39,
  0x2a35, 0x2a3a, 0x2a32, 0x044e, 0x048c, 0x0488, 0x858d, 0x2abb,
  0x2aba, 0x2b34, 0x2b39, 0x04ce, 0x859c, 0x04e5, 0x04e6, 0x2c98,
  0x0532, 0x2cd0, 0x0540, 0x0547, 0x054c, 0x2cd7, 0x2caa, 0x0535,
  0x2ca1, 0x2ca4, 0x0530, 0x2cee, 0x0543, 0x2ce7, 0x054d, 0x2ce8,
  0x2cde, 0x05b7, 0x05f3, 0x2f7e, 0x2f8b, 0x0602,
  /* 0x36 */
  0x060b, 0x2f79, 0x2f86, 0x2f93, 0x0604, 0x2f73, 0x2f6a, 0x85ba,
  0x2f6c, 0x0608, 0x2f7f, 0x05fc, 0x2fb2, 0x2fba, 0x05ff, 0x0600,
  0x2f66, 0x2f74, 0x068b, 0x315a, 0x068d, 0x314e, 0x314d, 0x318d,
  0x318e, 0x31ad, 0x06ca, 0x31c7, 0x31ca, 0x06cb, 0x31c9, 0x85cb,
  0x31e3, 0x3257, 0x06f3, 0x3263, 0x3267, 0x331a, 0x3319, 0x3316,
  0x0736, 0x076a, 0x349e, 0x34b6, 0x3498, 0x3473, 0x076b, 0x349a,
  0x348e, 0x34b7, 0x34db, 0x34a5, 0x346c, 0x34c1, 0x3484, 0x0771,
  0x0768, 0x3495, 0x347a, 0x3499, 0x0772, 0x34b8, 0x34b9, 0x3470,
  0x082e, 0x3735, 0x0862, 0x3790, 0x37bb, 0x37ed, 0x0898, 0x08b5,
  0x08eb, 0x39c1, 0x39c3, 0x39ce, 0x08fb, 0x08f8, 0x39ad, 0x3a04,
  0x08f5, 0x39b9, 0x0908, 0x39e7, 0x8607, 0x3a08, 0x3a06, 0x090a,
  0x3a0a, 0x39b0, 0x0906, 0x39f8, 0x3a0c, 0x08fd,
  /* 0x37 */
  0x39b1, 0x08fa, 0x3a02, 0x3a07, 0x3a09, 0x3a01, 0x3a17, 0x39ff,
  0x3a12, 0x09ba, 0x09b9, 0x3d03, 0x3d07, 0x3d01, 0x3cf5, 0x3cf1,
  0x3d08, 0x3cf2, 0x3d0f, 0x09bb, 0x3cfe, 0x0a18, 0x0a40, 0x0a3d,
  0x3f1a, 0x3f10, 0x3f0e, 0x4002, 0x3ff3, 0x0acd, 0x0ac9, 0x3ffb,
  0x0acb, 0x0aca, 0x0ace, 0x411b, 0x4123, 0x4161, 0x4168, 0x0b5e,
  0x4167, 0x41d3, 0x0b91, 0x0b8c, 0x4290, 0x0be1, 0x0c02, 0x42d5,
  0x42d7, 0x42d6, 0x4330, 0x0c2b, 0x4326, 0x0c2a, 0x4340, 0x0a14,
  0x431e, 0x0cad, 0x0ca3, 0x0cab, 0x4447, 0x0caf, 0x444b, 0x4451,
  0x444f, 0x4442, 0x4446, 0x0d04, 0x456e, 0x456c, 0x45f2, 0x0d44,
  0x45f1, 0x45f5, 0x45f3, 0x45f9, 0x0d3d, 0x0d47, 0x0d9c, 0x469a,
  0x4693, 0x4691, 0x46e1, 0x0de0, 0x0de4, 0x4721, 0x471c, 0x4716,
  0x4717, 0x4736, 0x471f, 0x0e80, 0x4893, 0x4899,
  /* 0x38 */
  0x489a, 0x489c, 0x0eca, 0x4949, 0x0ed4, 0x4934, 0x4937, 0x0ed2,
  0x492d, 0x0ecb, 0x494c, 0x0ece, 0x0ed3, 0x4948, 0x0f44, 0x0f48,
  0x4b3b, 0x0f45, 0x0f81, 0x0f86, 0x0f85, 0x4c08, 0x4c1a, 0x0fa3,
  0x4c1d, 0x0fb5, 0x4c49, 0x4c45, 0x4c44, 0x489b, 0x0ffa, 0x0ff9,
  0x4d2a, 0x4d2e, 0x0ffb, 0x0ff2, 0x4d31, 0x0fef, 0x4d1a, 0x4d34,
  0x4d17, 0x105b, 0x1066, 0x10ce, 0x4f1d, 0x4f71, 0x4f84, 0x4f80,
  0x4f72, 0x4fa1, 0x01b4, 0x4f79, 0x4f91, 0x10c8, 0x4f9f, 0x4fad,
  0x10d1, 0x10c5, 0x4f23, 0x10d2, 0x4f85, 0x4f9c, 0x4fb7, 0x5258,
  0x525a, 0x118f, 0x5257, 0x52b2, 0x11a7, 0x52ae, 0x11a5, 0x11a4,
  0x1211, 0x5445, 0x549c, 0x5494, 0x54a3, 0x548f, 0x54a5, 0x54a9,
  0x54a6, 0x548a, 0x54a0, 0x5490, 0x5592, 0x5591, 0x5594, 0x12b5,
  0x5626, 0x5632, 0x5628, 0x12b4, 0x12bd, 0x561c,
  /* 0x39 */
  0x12bb, 0x562b, 0x5620, 0x12b9, 0x5629, 0x12c2, 0x12be, 0x12ba,
  0x5621, 0x583a, 0x06b7, 0x585b, 0x5858, 0x587c, 0x1358, 0x58a6,
  0x58ae, 0x58ad, 0x5965, 0x139b, 0x597e, 0x139c, 0x597c, 0x597f,
  0x597a, 0x59bd, 0x13da, 0x13de, 0x59c0, 0x59bb, 0x5aad, 0x5aaf,
  0x5ad6, 0x144d, 0x1446, 0x1447, 0x144b, 0x144c, 0x5ad9, 0x1448,
  0x1499, 0x5c12, 0x5c0e, 0x5c25, 0x149b, 0x5c13, 0x5cee, 0x14ce,
  0x5cab, 0x5cf7, 0x14eb, 0x5d59, 0x5d54, 0x5df2, 0x5df0, 0x5de5,
  0x5df6, 0x151c, 0x158c, 0x6187, 0x15d1, 0x625a, 0x15d6, 0x15d3,
  0x626e, 0x15d4, 0x15d0, 0x15d5, 0x6279, 0x160b, 0x64e1, 0x64e6,
  0x17c6, 0x6ac4, 0x6ad2, 0x1a80, 0x0024, 0x1a81, 0x1c8f, 0x1c97,
  0x1c88, 0x1c89, 0x0074, 0x007a, 0x1c81, 0x1d60, 0x7064, 0x00c3,
  0x2a42, 0x1dd3, 0x00d4, 0x00d5, 0x1dd2, 0x1dd6,
  /* 0x3a */
  0x1e73, 0x00fb, 0x1e70, 0x00f7, 0x0132, 0x8533, 0x1fa8, 0x1fa6,
  0x1fc5, 0x2197, 0x21de, 0x01ba, 0x01bf, 0x2196, 0x21b4, 0x01c7,
  0x2185, 0x01b7, 0x219b, 0x21a0, 0x01b9, 0x2159, 0x01c3, 0x2186,
  0x01bd, 0x01d0, 0x21af, 0x217a, 0x01c1, 0x01be, 0x01cd, 0x219e,
  0x01cb, 0x21a9, 0x230f, 0x230e, 0x241a, 0x024f, 0x241f, 0x0253,
  0x243c, 0x2418, 0x243e, 0x2426, 0x0255, 0x243a, 0x7464, 0x2422,
  0x0251, 0x24fb, 0x2563, 0x2564, 0x029f, 0x26a8, 0x26a3, 0x2682,
  0x2688, 0x26a1, 0x2685, 0x2698, 0x02fe, 0x2699, 0x02fb, 0x2689,
  0x2681, 0x2696, 0x2680, 0x02f1, 0x02f5, 0x2691, 0x02ef, 0x0304,
  0x0303, 0x02f4, 0x26cf, 0x02f3, 0x0302, 0x02f7, 0x02fa, 0x02fd,
  0x02ee, 0x2687, 0x26a0, 0x02f0, 0x2679, 0x02f2, 0x2686, 0x26ab,
  0x26aa, 0x26a4, 0x268d, 0x267e, 0x0344, 0x27d5,
  /* 0x3b */
  0x0362, 0x0377, 0x09c9, 0x281e, 0x285f, 0x285e, 0x2944, 0x293e,
  0x03e8, 0x2948, 0x291c, 0x03ef, 0x295b, 0x294d, 0x03e6, 0x03ed,
  0x2957, 0x03e7, 0x2953, 0x294f, 0x03eb, 0x293b, 0x2946, 0x042d,
  0x0455, 0x2a46, 0x2a47, 0x0453, 0x2a48, 0x2ac0, 0x2abd, 0x2abf,
  0x0490, 0x2b11, 0x04be, 0x2b3e, 0x2b3b, 0x04bd, 0x2b3a, 0x04cf,
  0x04d0, 0x04ec, 0x2ba7, 0x054b, 0x2cea, 0x0548, 0x2d07, 0x2d22,
  0x2d0c, 0x0555, 0x0551, 0x2cb3, 0x2cd6, 0x2cd2, 0x054e, 0x2ce3,
  0x2ce5, 0x2ce9, 0x056b, 0x055e, 0x2d11, 0x2cfd, 0x0560, 0x0567,
  0x2d1e, 0x2d20, 0x2d21, 0x2e1e, 0x05b8, 0x2fe2, 0x2fde, 0x2fe6,
  0x0614, 0x060f, 0x0607, 0x0613, 0x2ff8, 0x0617, 0x2ffe, 0x2fc1,
  0x2fbf, 0x2ff7, 0x2fd1, 0x315f, 0x3160, 0x3161, 0x069a, 0x06b8,
  0x31d1, 0x06f7, 0x06f8, 0x327d, 0x326b, 0x327f,
  /* 0x3c */
  0x06fd, 0x06f5, 0x3273, 0x3281, 0x326d, 0x3269, 0x06fa, 0x0738,
  0x331e, 0x34ed, 0x0787, 0x0780, 0x0788, 0x0779, 0x3503, 0x077c,
  0x34fe, 0x34e5, 0x351e, 0x3502, 0x0783, 0x0785, 0x3509, 0x34ca,
  0x3500, 0x85e5, 0x3501, 0x3518, 0x34e2, 0x34cf, 0x077b, 0x352e,
  0x34c5, 0x34ff, 0x0786, 0x351c, 0x34c3, 0x0834, 0x376f, 0x0855,
  0x376e, 0x0868, 0x37be, 0x089c, 0x37f4, 0x382d, 0x08fc, 0x39b6,
  0x3a75, 0x3a1e, 0x091a, 0x3a18, 0x0917, 0x3a48, 0x091b, 0x3a4f,
  0x0913, 0x3a42, 0x3a6a, 0x3a70, 0x39fe, 0x0905, 0x0907, 0x3a6d,
  0x091c, 0x3a7b, 0x3a7e, 0x3a59, 0x0911, 0x3a57, 0x0916, 0x3a80,
  0x3a50, 0x0915, 0x3a29, 0x3a76, 0x3a2a, 0x3a4c, 0x3d2a, 0x09cb,
  0x3d35, 0x3d2c, 0x3d37, 0x3d1d, 0x09c5, 0x09c2, 0x3d38, 0x09cd,
  0x3d34, 0x3d2b, 0x3d33, 0x3d27, 0x3d24, 0x09ca,
  /* 0x3d */
  0x3d2d, 0x3e32, 0x3e83, 0x3e82, 0x3e87, 0x3f06, 0x3f24, 0x3f38,
  0x3f2a, 0x3f2c, 0x3f2b, 0x0a83, 0x3f2f, 0x3f28, 0x4017, 0x0ad6,
  0x0ad5, 0x4019, 0x4038, 0x0ad1, 0x401f, 0x4014, 0x403c, 0x3ff7,
  0x401c, 0x4015, 0x4018, 0x4039, 0x40f9, 0x4124, 0x8634, 0x0b52,
  0x0b5f, 0x416e, 0x416d, 0x4171, 0x418e, 0x0b95, 0x41e5, 0x0b9d,
  0x0b98, 0x0b9e, 0x0b96, 0x4294, 0x42b3, 0x0c03, 0x42d9, 0x0c2f,
  0x4348, 0x4349, 0x4343, 0x0c31, 0x0c33, 0x4342, 0x43df, 0x0cb4,
  0x4463, 0x4476, 0x0cb0, 0x445f, 0x4466, 0x4566, 0x4571, 0x0d08,
  0x0d07, 0x4576, 0x4584, 0x4575, 0x45ff, 0x4607, 0x0d4e, 0x460e,
  0x4609, 0x0d50, 0x0d52, 0x0da1, 0x0da3, 0x0da5, 0x0dcc, 0x46e7,
  0x46e2, 0x4755, 0x0def, 0x0dea, 0x4743, 0x4757, 0x476c, 0x4742,
  0x4753, 0x0ded, 0x4741, 0x0e85, 0x0e84, 0x48a7,
  /* 0x3e */
  0x48a0, 0x48a6, 0x48a4, 0x4974, 0x0edb, 0x4959, 0x0ed9, 0x4960,
  0x4957, 0x496c, 0x497e, 0x4964, 0x0ed7, 0x495a, 0x495d, 0x0eda,
  0x0ede, 0x0ed8, 0x4976, 0x494d, 0x4975, 0x0ed5, 0x4bd3, 0x4bd6,
  0x0f9c, 0x0f9d, 0x4c60, 0x4c4e, 0x4d45, 0x4d3b, 0x0ffe, 0x4d48,
  0x4d42, 0x4d49, 0x4d40, 0x4d14, 0x4d41, 0x1007, 0x4def, 0x4df6,
  0x4e03, 0x106a, 0x4fed, 0x10e7, 0x4fda, 0x5018, 0x4fd2, 0x5008,
  0x10e2, 0x5000, 0x10df, 0x10e1, 0x10e5, 0x5017, 0x4f46, 0x5014,
  0x4fd3, 0x5005, 0x501f, 0x5002, 0x5016, 0x4fcd, 0x4fe6, 0x1191,
  0x525d, 0x52d5, 0x52e1, 0x11b4, 0x11b0, 0x11b5, 0x11ae, 0x52ee,
  0x5447, 0x5446, 0x122d, 0x122c, 0x54bb, 0x122b, 0x54bf, 0x54b4,
  0x1229, 0x54b5, 0x127f, 0x559a, 0x5643, 0x12c9, 0x12cb, 0x565a,
  0x12c5, 0x12c6, 0x12ca, 0x5635, 0x5638, 0x5642,
  /* 0x3f */
  0x5649, 0x565d, 0x564b, 0x563d, 0x12d2, 0x12d0, 0x132d, 0x1335,
  0x5860, 0x585e, 0x587f, 0x587e, 0x5883, 0x136c, 0x58b1, 0x5987,
  0x139d, 0x13a0, 0x5988, 0x5983, 0x13a2, 0x139f, 0x5986, 0x598b,
  0x5982, 0x59ca, 0x59d2, 0x13eb, 0x13e2, 0x59d4, 0x59c9, 0x5ab0,
  0x1436, 0x1432, 0x1450, 0x5af2, 0x5ae4, 0x5af3, 0x5aea, 0x144f,
  0x5afd, 0x1452, 0x5b9d, 0x5c2b, 0x5c2a, 0x149e, 0x5c28, 0x5c29,
  0x5c2c, 0x14a0, 0x149c, 0x5c3a, 0x5c30, 0x5c37, 0x5c3b, 0x14d1,
  0x5d0a, 0x14ef, 0x14f0, 0x14f1, 0x5dfe, 0x5e20, 0x151d, 0x5e0b,
  0x151f, 0x5e18, 0x5e22, 0x151e, 0x5e1b, 0x5e08, 0x1520, 0x5e0e,
  0x5e13, 0x158e, 0x1591, 0x6195, 0x83dd, 0x1590, 0x15d7, 0x628c,
  0x627b, 0x627f, 0x6281, 0x15d9, 0x6282, 0x15f4, 0x15f6, 0x0160,
  0x15f5, 0x15f3, 0x62ee, 0x62ed, 0x160c, 0x62ec,
  /* 0x40 */
  0x635f, 0x636f, 0x1651, 0x636d, 0x16a6, 0x16a7, 0x16a8, 0x1727,
  0x1724, 0x1725, 0x64f0, 0x172a, 0x1774, 0x17c7, 0x66a9, 0x17e7,
  0x17ed, 0x66e0, 0x1ab7, 0x002e, 0x007b, 0x1ccc, 0x1cbc, 0x007c,
  0x1caa, 0x1cb9, 0x007d, 0x1cab, 0x1cc3, 0x1ccd, 0x1d7e, 0x1e7e,
  0x1e79, 0x00fd, 0x8523, 0x1ee1, 0x1ee0, 0x1ee7, 0x1f80, 0x1fab,
  0x1faa, 0x1fa9, 0x1fe0, 0x21ea, 0x01da, 0x21d7, 0x01d6, 0x01db,
  0x21c1, 0x2315, 0x025b, 0x246c, 0x025c, 0x245c, 0x2450, 0x2461,
  0x246a, 0x2469, 0x2456, 0x2460, 0x2466, 0x245f, 0x2523, 0x2566,
  0x2568, 0x0306, 0x030b, 0x26ce, 0x030d, 0x26c5, 0x26c3, 0x030a,
  0x0313, 0x26d0, 0x0310, 0x0312, 0x0309, 0x0308, 0x0311, 0x030f,
  0x2774, 0x2776, 0x27dc, 0x27d7, 0x27da, 0x27db, 0x0367, 0x2820,
  0x296d, 0x2966, 0x03f6, 0x2964, 0x296e, 0x857e,
  /* 0x41 */
  0x2960, 0x2b42, 0x2b5a, 0x2b6e, 0x0564, 0x056c, 0x2d30, 0x2d3a,
  0x2d2a, 0x2d43, 0x2d19, 0x2d31, 0x056d, 0x2d3d, 0x057a, 0x0575,
  0x060d, 0x3008, 0x3032, 0x3038, 0x061e, 0x3031, 0x061b, 0x3019,
  0x062a, 0x3011, 0x061f, 0x0622, 0x3029, 0x301d, 0x0625, 0x0627,
  0x0629, 0x303c, 0x0624, 0x3046, 0x3047, 0x0628, 0x0626, 0x303a,
  0x3007, 0x0623, 0x316b, 0x069f, 0x3170, 0x316d, 0x06b1, 0x31e4,
  0x3293, 0x0703, 0x0707, 0x070c, 0x0706, 0x328f, 0x0704, 0x0709,
  0x3292, 0x0705, 0x328e, 0x0708, 0x3546, 0x0796, 0x079c, 0x079f,
  0x079b, 0x0798, 0x0799, 0x0794, 0x3531, 0x078d, 0x07a3, 0x353e,
  0x0793, 0x357c, 0x3543, 0x0792, 0x3573, 0x85e8, 0x3555, 0x078e,
  0x078c, 0x3585, 0x354d, 0x3550, 0x3547, 0x3567, 0x3536, 0x3564,
  0x3561, 0x079a, 0x357d, 0x3744, 0x3740, 0x3771,
  /* 0x42 */
  0x3773, 0x379c, 0x086a, 0x086d, 0x0884, 0x37c1, 0x08a0, 0x37fa,
  0x3831, 0x3832, 0x091d, 0x0926, 0x3ab8, 0x3aa8, 0x0933, 0x3a91,
  0x3abb, 0x0938, 0x3a9a, 0x0930, 0x0928, 0x3aa9, 0x0927, 0x092a,
  0x3ab5, 0x3a6c, 0x3ae8, 0x0931, 0x3add, 0x3ada, 0x3ae6, 0x3aac,
  0x0934, 0x092e, 0x093b, 0x3ad9, 0x3ae3, 0x3ae9, 0x3adb, 0x0929,
  0x3d6f, 0x09d2, 0x09d8, 0x3d48, 0x09cf, 0x3d4a, 0x3d6b, 0x09d9,
  0x3d4f, 0x3d57, 0x3d74, 0x09ce, 0x09d3, 0x09d0, 0x3d45, 0x3d51,
  0x3d6d, 0x07a1, 0x3e51, 0x3e50, 0x3e4e, 0x0a47, 0x3f41, 0x0a8b,
  0x3f2e, 0x3f46, 0x0ad4, 0x4027, 0x0ade, 0x4048, 0x4053, 0x403d,
  0x0adf, 0x405d, 0x4056, 0x0ad7, 0x401e, 0x4047, 0x4043, 0x4058,
  0x4049, 0x0ae1, 0x404c, 0x4045, 0x403e, 0x0b2f, 0x4101, 0x411e,
  0x0b62, 0x0b63, 0x417a, 0x41ee, 0x4202, 0x4297,
  /* 0x43 */
  0x4298, 0x0be2, 0x0c04, 0x0c43, 0x435d, 0x4364, 0x4353, 0x4358,
  0x4482, 0x4490, 0x448a, 0x0cbe, 0x447a, 0x447d, 0x0cba, 0x448b,
  0x4478, 0x0cbc, 0x864e, 0x448d, 0x4488, 0x4492, 0x4481, 0x457e,
  0x4583, 0x0d0d, 0x0d0e, 0x0d11, 0x4580, 0x0d0f, 0x0d12, 0x0d55,
  0x460f, 0x0d59, 0x0d5b, 0x461d, 0x0d57, 0x46a1, 0x46a4, 0x0dce,
  0x46e9, 0x46ea, 0x0dfe, 0x4762, 0x476b, 0x0dfc, 0x475e, 0x0df5,
  0x4779, 0x0df9, 0x0dfa, 0x476f, 0x4768, 0x0e88, 0x0e89, 0x48ae,
  0x0e8a, 0x0e87, 0x0e8b, 0x48b0, 0x0ee6, 0x4990, 0x0eed, 0x498a,
  0x0ee5, 0x498b, 0x4999, 0x4995, 0x0ee0, 0x4987, 0x4978, 0x4997,
  0x4989, 0x4998, 0x0ee1, 0x0f5b, 0x0f5c, 0x4ba3, 0x0f8f, 0x0f8b,
  0x0f8d, 0x4bdd, 0x4c57, 0x0fb9, 0x4d63, 0x4d6a, 0x4d6c, 0x100f,
  0x1019, 0x1013, 0x4d5d, 0x4d75, 0x1018, 0x4d5f,
  /* 0x44 */
  0x1016, 0x4d7d, 0x4d6d, 0x1053, 0x868d, 0x4e41, 0x504f, 0x5084,
  0x10f6, 0x507f, 0x10f5, 0x5048, 0x502a, 0x507b, 0x5072, 0x5064,
  0x502e, 0x505c, 0x5053, 0x10f7, 0x5041, 0x50c8, 0x10f0, 0x5062,
  0x5080, 0x503e, 0x5083, 0x5071, 0x10f9, 0x504a, 0x5055, 0x5058,
  0x1192, 0x1195, 0x1196, 0x52fc, 0x52fd, 0x5315, 0x11b9, 0x5316,
  0x52ff, 0x11bd, 0x11b8, 0x1212, 0x5458, 0x54cf, 0x54e0, 0x1280,
  0x1281, 0x129a, 0x1298, 0x55e7, 0x566a, 0x5680, 0x12d4, 0x566f,
  0x5665, 0x12da, 0x5678, 0x567d, 0x5688, 0x12d6, 0x12db, 0x5664,
  0x567e, 0x12dc, 0x5667, 0x5863, 0x5888, 0x1371, 0x58cd, 0x1372,
  0x58c9, 0x13a8, 0x59ed, 0x13f0, 0x86db, 0x13f1, 0x13fd, 0x1438,
  0x1437, 0x1439, 0x5ab1, 0x1455, 0x1453, 0x5b04, 0x5b9e, 0x5ba0,
  0x5c43, 0x5c46, 0x5c48, 0x5c45, 0x5c40, 0x5c4c,
  /* 0x45 */
  0x14d5, 0x14bd, 0x5d0c, 0x5d13, 0x5d15, 0x14f5, 0x5d6b, 0x5d67,
  0x5e5d, 0x5e55, 0x5e35, 0x1521, 0x5e59, 0x5e2f, 0x5e3c, 0x5e8f,
  0x5e5c, 0x5e6a, 0x5e62, 0x5e5f, 0x5e6b, 0x5e6e, 0x5e3b, 0x5e44,
  0x5e41, 0x619a, 0x1592, 0x6199, 0x15de, 0x15db, 0x15da, 0x628f,
  0x15df, 0x6296, 0x15f9, 0x15f8, 0x15fa, 0x62f4, 0x62fc, 0x160e,
  0x6355, 0x1643, 0x6379, 0x1656, 0x1653, 0x169e, 0x63ee, 0x63f5,
  0x16a9, 0x640b, 0x16fa, 0x64f3, 0x1731, 0x1730, 0x64f7, 0x64ff,
  0x64f5, 0x1732, 0x64ec, 0x64f1, 0x1729, 0x172e, 0x659a, 0x1776,
  0x66e2, 0x673d, 0x675d, 0x68e8, 0x18a5, 0x68eb, 0x68ef, 0x68ee,
  0x6a81, 0x6b14, 0x1cd0, 0x1cd9, 0x1cdc, 0x1cd8, 0x008c, 0x1ce1,
  0x1ceb, 0x008b, 0x0089, 0x1cf4, 0x1ce2, 0x1cde, 0x008d, 0x0086,
  0x00d7, 0x1df4, 0x0104, 0x0107, 0x0103, 0x1eed,
  /* 0x46 */
  0x1eea, 0x0122, 0x1f32, 0x0151, 0x1fae, 0x1fb0, 0x0161, 0x21fb,
  0x2203, 0x220b, 0x01e9, 0x2207, 0x01e5, 0x21f8, 0x01e4, 0x2228,
  0x221e, 0x01e3, 0x2218, 0x2211, 0x2251, 0x2205, 0x2317, 0x2492,
  0x0265, 0x248c, 0x0263, 0x2478, 0x2484, 0x2473, 0x24ad, 0x2497,
  0x2495, 0x2477, 0x2472, 0x2496, 0x248d, 0x2510, 0x028c, 0x256c,
  0x031a, 0x26e7, 0x0315, 0x26e4, 0x0320, 0x0321, 0x26ef, 0x2226,
  0x031c, 0x031b, 0x26f0, 0x297b, 0x03fe, 0x2983, 0x0404, 0x0401,
  0x298b, 0x298c, 0x0400, 0x2978, 0x2a52, 0x046d, 0x0493, 0x2ad0,
  0x2acf, 0x04a1, 0x2bb3, 0x2bb4, 0x0576, 0x0579, 0x0572, 0x2d7b,
  0x0583, 0x2d6f, 0x2d81, 0x2d3c, 0x2d42, 0x2d38, 0x2d33, 0x85a6,
  0x2d60, 0x2d69, 0x2d7d, 0x2d86, 0x2e2c, 0x2e28, 0x0638, 0x304c,
  0x0630, 0x3057, 0x307c, 0x0634, 0x063a, 0x3055,
  /* 0x47 */
  0x3062, 0x3071, 0x306a, 0x3056, 0x303b, 0x3081, 0x0635, 0x304f,
  0x307e, 0x3064, 0x063f, 0x0640, 0x0632, 0x0631, 0x0636, 0x3171,
  0x7aba, 0x070f, 0x32a5, 0x329a, 0x329c, 0x0710, 0x32a6, 0x070d,
  0x32a4, 0x358f, 0x35c5, 0x35c8, 0x3592, 0x35b2, 0x07a9, 0x07b4,
  0x07ac, 0x35e3, 0x35c0, 0x35d6, 0x35d1, 0x359f, 0x35a2, 0x35d2,
  0x07b8, 0x07ae, 0x7bf3, 0x35e1, 0x35d5, 0x359d, 0x07b3, 0x07ba,
  0x3598, 0x083f, 0x3774, 0x37a1, 0x093c, 0x3af0, 0x3af3, 0x0942,
  0x0940, 0x3b1b, 0x3b0c, 0x3b1d, 0x3b34, 0x3b28, 0x3b17, 0x093e,
  0x3b44, 0x3b42, 0x3b04, 0x3b11, 0x3afa, 0x3b4a, 0x3d91, 0x3d8e,
  0x09e1, 0x3d8b, 0x3d8d, 0x3d7f, 0x3d8c, 0x3d7e, 0x3d7c, 0x3d83,
  0x09e6, 0x3d88, 0x09e0, 0x0a15, 0x3e94, 0x0a93, 0x3f55, 0x3f53,
  0x3f4f, 0x3f54, 0x406c, 0x4065, 0x4066, 0x4061,
  /* 0x48 */
  0x406b, 0x4068, 0x4076, 0x0ae7, 0x4060, 0x7e0f, 0x4074, 0x4106,
  0x420e, 0x0bad, 0x4207, 0x0bae, 0x0be3, 0x42b9, 0x0bf5, 0x42b7,
  0x42e2, 0x0c06, 0x4374, 0x4377, 0x4376, 0x4375, 0x0c4f, 0x4378,
  0x4371, 0x0c54, 0x437a, 0x3d5b, 0x437b, 0x44a6, 0x44ae, 0x44b8,
  0x0ccb, 0x0ce3, 0x0cc9, 0x44b1, 0x44af, 0x0d13, 0x4589, 0x4587,
  0x0d15, 0x0d61, 0x4629, 0x0d66, 0x462a, 0x0d64, 0x462d, 0x462c,
  0x0d60, 0x4632, 0x0d63, 0x46ec, 0x46f0, 0x4781, 0x479e, 0x4783,
  0x0e0a, 0x4792, 0x0e04, 0x47a3, 0x479f, 0x4793, 0x0e07, 0x4786,
  0x48b8, 0x48b7, 0x0e8d, 0x0e8f, 0x0e90, 0x0e92, 0x0eec, 0x49c8,
  0x49b6, 0x866c, 0x49d1, 0x0ee7, 0x49a8, 0x49ab, 0x0ef2, 0x49b3,
  0x49cd, 0x0eee, 0x49cf, 0x49a4, 0x0eef, 0x0f4c, 0x4b41, 0x4b6f,
  0x4b71, 0x0f5e, 0x0f5f, 0x0f76, 0x0f74, 0x0f72,
  /* 0x49 */
  0x0f90, 0x4c23, 0x4c5b, 0x0fbe, 0x4c61, 0x4c5f, 0x4d81, 0x1026,
  0x1025, 0x4d84, 0x4e13, 0x1074, 0x4e4a, 0x4e4c, 0x10fd, 0x1105,
  0x1101, 0x50bd, 0x5095, 0x1109, 0x5092, 0x50c3, 0x110c, 0x5096,
  0x50a5, 0x50b5, 0x50b3, 0x50a3, 0x50e4, 0x50d8, 0x50d5, 0x110d,
  0x50b7, 0x50ad, 0x50da, 0x5093, 0x5336, 0x11c0, 0x11c5, 0x11c9,
  0x533d, 0x532b, 0x5347, 0x5339, 0x11d5, 0x5345, 0x531d, 0x1241,
  0x54ff, 0x54ea, 0x1233, 0x54f5, 0x123a, 0x5500, 0x54ed, 0x5503,
  0x54e9, 0x1240, 0x1242, 0x55ea, 0x12e8, 0x569b, 0x568e, 0x56a2,
  0x12e4, 0x569c, 0x5694, 0x5690, 0x56a9, 0x56ac, 0x12e7, 0x569f,
  0x12e6, 0x12e1, 0x569d, 0x1339, 0x5867, 0x135c, 0x1375, 0x58d0,
  0x58d6, 0x58d4, 0x5998, 0x599a, 0x5997, 0x13ae, 0x13b0, 0x13fa,
  0x5a0b, 0x5a08, 0x5a01, 0x5ab4, 0x5ab3, 0x145b,
  /* 0x4a */
  0x5ba1, 0x5ba2, 0x14a5, 0x5c5a, 0x14a2, 0x5c61, 0x5c5f, 0x14db,
  0x14da, 0x5d25, 0x5d7b, 0x5d76, 0x5d7c, 0x1524, 0x5e89, 0x5ef6,
  0x5eb1, 0x5ead, 0x5e92, 0x5e81, 0x5e84, 0x1526, 0x5eae, 0x5e90,
  0x5e9e, 0x1598, 0x1596, 0x159a, 0x61a2, 0x61a7, 0x1597, 0x15e1,
  0x15e0, 0x15e3, 0x15e2, 0x62a0, 0x629d, 0x629f, 0x62d0, 0x15fb,
  0x62d1, 0x1612, 0x1614, 0x6359, 0x1645, 0x6364, 0x165c, 0x165d,
  0x16b8, 0x6419, 0x16ba, 0x6414, 0x6415, 0x641a, 0x1703, 0x1735,
  0x1736, 0x1739, 0x6506, 0x172d, 0x64f8, 0x6501, 0x177a, 0x65be,
  0x65bc, 0x65b7, 0x65b6, 0x65c0, 0x1778, 0x65b8, 0x177b, 0x177c,
  0x177e, 0x65c4, 0x177d, 0x65bf, 0x17c9, 0x66da, 0x66e4, 0x66e9,
  0x66e8, 0x66ea, 0x66e5, 0x17f3, 0x6726, 0x181a, 0x1819, 0x6740,
  0x181f, 0x18a6, 0x18a7, 0x18a8, 0x18ab, 0x18a9,
  /* 0x4b */
  0x192e, 0x6abd, 0x195e, 0x0095, 0x0093, 0x0092, 0x1d0e, 0x0096,
  0x1cf7, 0x0097, 0x1cfc, 0x1d0d, 0x1d01, 0x1dda, 0x1dd9, 0x1ddb,
  0x1e86, 0x1e8e, 0x1eee, 0x1f33, 0x1fb1, 0x01f5, 0x2247, 0x222d,
  0x2254, 0x01ea, 0x224b, 0x2252, 0x2231, 0x2244, 0x2256, 0x2250,
  0x222b, 0x01f3, 0x224d, 0x2237, 0x224f, 0x24a2, 0x24b7, 0x0269,
  0x24b2, 0x026b, 0x24aa, 0x24b5, 0x24b0, 0x026c, 0x24b4, 0x24a4,
  0x24a7, 0x0268, 0x2526, 0x26fe, 0x0328, 0x2704, 0x0326, 0x26fc,
  0x0325, 0x2706, 0x270a, 0x26fa, 0x270d, 0x2700, 0x270e, 0x036b,
  0x040f, 0x0408, 0x2991, 0x040c, 0x298f, 0x2990, 0x2998, 0x29a4,
  0x299b, 0x29a3, 0x2996, 0x29e4, 0x2a5a, 0x0460, 0x0462, 0x2a5e,
  0x0498, 0x2bb8, 0x2d57, 0x2d5c, 0x2da6, 0x2d95, 0x2d88, 0x058a,
  0x2da3, 0x2d8f, 0x0584, 0x2d64, 0x057f, 0x2d59,
  /* 0x4c */
  0x2d78, 0x0582, 0x2d85, 0x2d87, 0x2d9e, 0x0596, 0x0589, 0x2d98,
  0x2d9c, 0x058d, 0x05bc, 0x2e2f, 0x3080, 0x309b, 0x308e, 0x308d,
  0x3094, 0x30c6, 0x0644, 0x30a8, 0x3083, 0x063c, 0x30b9, 0x3086,
  0x30b4, 0x30af, 0x3091, 0x064e, 0x30aa, 0x30a1, 0x30a7, 0x32b6,
  0x32b3, 0x0714, 0x32bc, 0x32ac, 0x0715, 0x32ad, 0x360e, 0x07ce,
  0x361c, 0x361a, 0x07e0, 0x07c2, 0x360b, 0x07bf, 0x35ef, 0x360c,
  0x35f0, 0x3622, 0x07c4, 0x35d8, 0x07cf, 0x3612, 0x35fa, 0x07c8,
  0x362a, 0x07cc, 0x3610, 0x07cd, 0x07c7, 0x3629, 0x35f9, 0x35ea,
  0x362c, 0x3624, 0x18b7, 0x35e9, 0x3752, 0x374f, 0x3753, 0x0843,
  0x08b6, 0x3b10, 0x3b65, 0x3b75, 0x0951, 0x094a, 0x094d, 0x0956,
  0x3bd0, 0x0953, 0x3b5c, 0x3b3d, 0x3b71, 0x0959, 0x3b91, 0x3b0b,
  0x3b79, 0x3b81, 0x3b8f, 0x094e, 0x3b59, 0x3b74,
  /* 0x4d */
  0x09ee, 0x3dae, 0x09ec, 0x3da3, 0x3dad, 0x09eb, 0x09ef, 0x3dab,
  0x3da6, 0x3da2, 0x09ed, 0x1ef2, 0x3e57, 0x3e55, 0x3e99, 0x3f4b,
  0x407a, 0x0af2, 0x0aef, 0x0af1, 0x408c, 0x4084, 0x0aed, 0x0af0,
  0x4082, 0x4093, 0x407b, 0x0aee, 0x4109, 0x181b, 0x0b50, 0x0b66,
  0x0284, 0x0bb8, 0x0bf6, 0x438a, 0x0c57, 0x4390, 0x0c5e, 0x44c6,
  0x44d3, 0x44c0, 0x44d2, 0x44c7, 0x44c2, 0x0d19, 0x459f, 0x459d,
  0x459e, 0x0d70, 0x4641, 0x0d6e, 0x4638, 0x463a, 0x4642, 0x0d72,
  0x0d76, 0x463e, 0x46b0, 0x47ae, 0x47b3, 0x0e12, 0x0e1f, 0x47bf,
  0x0e11, 0x0e16, 0x47cd, 0x0e19, 0x47b2, 0x0e24, 0x0e14, 0x0e25,
  0x0e95, 0x0e96, 0x0e93, 0x0e94, 0x48c4, 0x48cd, 0x48c2, 0x48c6,
  0x48c3, 0x48c9, 0x48c7, 0x0ea0, 0x49f8, 0x0efb, 0x49ed, 0x49e2,
  0x0efc, 0x0f00, 0x0ef8, 0x49dc, 0x4a02, 0x4a01,
  /* 0x4e */
  0x0ef9, 0x49d6, 0x0f04, 0x49e4, 0x49fe, 0x0f03, 0x4a00, 0x49fc,
  0x49fd, 0x0ef3, 0x49f5, 0x49ff, 0x0efa, 0x49eb, 0x49e5, 0x4b78,
  0x4bae, 0x4be7, 0x0fbf, 0x4c65, 0x4c6a, 0x4c66, 0x4c68, 0x4c6b,
  0x4d94, 0x4da1, 0x4d92, 0x4d96, 0x4d93, 0x1079, 0x1110, 0x5101,
  0x1114, 0x50f8, 0x110e, 0x50f5, 0x111a, 0x5104, 0x1119, 0x1121,
  0x1123, 0x111f, 0x511b, 0x5103, 0x5133, 0x5134, 0x50ed, 0x1125,
  0x112b, 0x5135, 0x1116, 0x5105, 0x1122, 0x111b, 0x11ce, 0x11cf,
  0x537d, 0x11cb, 0x11d1, 0x11cc, 0x5371, 0x1217, 0x545c, 0x54e6,
  0x550f, 0x551b, 0x1251, 0x55a9, 0x55a5, 0x55ee, 0x56b1, 0x12ed,
  0x56cc, 0x56ce, 0x12f4, 0x56b7, 0x12f1, 0x56b5, 0x56e9, 0x56b4,
  0x12f8, 0x56b3, 0x56c1, 0x56af, 0x56ca, 0x56d0, 0x132f, 0x135e,
  0x135d, 0x588e, 0x1376, 0x1377, 0x58e9, 0x58db,
  /* 0x4f */
  0x137e, 0x58eb, 0x59a4, 0x13b6, 0x59a2, 0x599d, 0x13b3, 0x13fc,
  0x1403, 0x1400, 0x5a2a, 0x5a28, 0x140a, 0x1402, 0x5ab8, 0x5ab6,
  0x5ab9, 0x5ab7, 0x5b22, 0x5b2b, 0x5b27, 0x5b19, 0x5ba4, 0x1487,
  0x5bb3, 0x14a6, 0x5c71, 0x5c6a, 0x14a9, 0x14de, 0x5d88, 0x5d8c,
  0x5ebf, 0x5eb8, 0x5ebe, 0x5edc, 0x5ee5, 0x152e, 0x152d, 0x5ed4,
  0x5ed6, 0x1530, 0x5eda, 0x5eed, 0x5ef3, 0x5edb, 0x152b, 0x5eb9,
  0x5ee2, 0x5eeb, 0x61af, 0x159e, 0x61b2, 0x61b3, 0x159f, 0x15e5,
  0x15e4, 0x62a3, 0x62a5, 0x15fd, 0x15fc, 0x1617, 0x1619, 0x630a,
  0x1618, 0x6387, 0x6389, 0x638c, 0x63ef, 0x642a, 0x6422, 0x16bf,
  0x641f, 0x173c, 0x6519, 0x176b, 0x65ca, 0x65da, 0x1783, 0x1781,
  0x1780, 0x65de, 0x65c8, 0x65e0, 0x17ca, 0x66b6, 0x66b5, 0x17ce,
  0x66f4, 0x17f6, 0x676b, 0x6769, 0x6772, 0x6763,
  /* 0x50 */
  0x1839, 0x690d, 0x18ae, 0x6901, 0x690c, 0x18b5, 0x68f8, 0x18b3,
  0x18b4, 0x68fe, 0x6902, 0x6a84, 0x1922, 0x6aab, 0x6aaa, 0x1d1d,
  0x1d16, 0x0099, 0x1d2b, 0x1d1e, 0x1d1b, 0x1e90, 0x1e94, 0x1f14,
  0x7289, 0x0202, 0x2267, 0x0201, 0x227b, 0x02a1, 0x225f, 0x2261,
  0x01fd, 0x0273, 0x0274, 0x0270, 0x0276, 0x0275, 0x0272, 0x24c3,
  0x24ca, 0x24bb, 0x24c0, 0x24c4, 0x2501, 0x271f, 0x2718, 0x2711,
  0x2715, 0x0329, 0x2712, 0x271c, 0x032a, 0x2722, 0x2779, 0x29a6,
  0x0416, 0x29b3, 0x29ab, 0x2aea, 0x0499, 0x2b5b, 0x04d3, 0x04f5,
  0x2db7, 0x2dce, 0x2db9, 0x2dbd, 0x2dcf, 0x2dc0, 0x2d99, 0x2d97,
  0x0594, 0x2dbb, 0x2dd0, 0x2dc4, 0x2e31, 0x0656, 0x30d3, 0x30c0,
  0x0659, 0x0658, 0x0655, 0x0652, 0x30dc, 0x30d1, 0x30c8, 0x0657,
  0x30d5, 0x32c3, 0x071b, 0x071c, 0x32bf, 0x32c5,
  /* 0x51 */
  0x0719, 0x32cd, 0x32c1, 0x3306, 0x073f, 0x3324, 0x3663, 0x3642,
  0x3652, 0x07db, 0x3643, 0x3633, 0x07e2, 0x366c, 0x3657, 0x07d7,
  0x364c, 0x366e, 0x07de, 0x07e5, 0x07e4, 0x07e6, 0x07d6, 0x3637,
  0x07df, 0x3671, 0x364a, 0x3636, 0x07dc, 0x3653, 0x07da, 0x3645,
  0x3670, 0x07d3, 0x07d0, 0x365c, 0x3758, 0x3757, 0x0886, 0x0887,
  0x08ad, 0x08b7, 0x0958, 0x096a, 0x3bbb, 0x0962, 0x0961, 0x3bbe,
  0x0969, 0x096c, 0x0965, 0x3bb5, 0x3bd3, 0x3b9f, 0x0966, 0x3bb7,
  0x3bf5, 0x3db7, 0x09f5, 0x3dbb, 0x09f4, 0x3dd1, 0x09f7, 0x3dba,
  0x09f8, 0x3db6, 0x3dcc, 0x09fb, 0x09fc, 0x3dd3, 0x409b, 0x0af5,
  0x0af8, 0x4096, 0x40a2, 0x409d, 0x410a, 0x410e, 0x0b3c, 0x4181,
  0x422c, 0x4237, 0x4236, 0x423b, 0x0bc5, 0x42a1, 0x0c62, 0x0c63,
  0x4398, 0x0c67, 0x4396, 0x0c66, 0x0cd9, 0x0cdb,
  /* 0x52 */
  0x44d6, 0x44eb, 0x0cd8, 0x44dc, 0x0d1b, 0x45a5, 0x45a9, 0x6434,
  0x4653, 0x4645, 0x0d79, 0x464f, 0x0d7d, 0x46bd, 0x46bb, 0x46f1,
  0x0e2c, 0x0e37, 0x47ec, 0x47ed, 0x0e30, 0x0e9a, 0x48d3, 0x1600,
  0x48e1, 0x0f05, 0x4a19, 0x0f07, 0x0f09, 0x0f0a, 0x4a27, 0x4a26,
  0x0f79, 0x0fc2, 0x4c6e, 0x4daf, 0x1038, 0x1037, 0x4dad, 0x1021,
  0x4daa, 0x4e18, 0x105e, 0x113d, 0x1137, 0x1140, 0x516f, 0x514c,
  0x111d, 0x5142, 0x1133, 0x515c, 0x5170, 0x515f, 0x1135, 0x515a,
  0x514b, 0x513f, 0x538a, 0x11d8, 0x538b, 0x53a1, 0x538e, 0x11dc,
  0x11de, 0x5399, 0x545e, 0x545f, 0x5524, 0x55a7, 0x56ea, 0x56fd,
  0x56f9, 0x56e3, 0x56e5, 0x12fa, 0x12fb, 0x56ec, 0x133d, 0x133b,
  0x133f, 0x135f, 0x58f2, 0x137f, 0x58ef, 0x1384, 0x59a6, 0x13bc,
  0x1414, 0x140f, 0x5a3b, 0x5a43, 0x140e, 0x5a32,
  /* 0x53 */
  0x5b31, 0x5b30, 0x1460, 0x5b2d, 0x5b3c, 0x5ba7, 0x5ba5, 0x14ab,
  0x14ac, 0x14aa, 0x5d37, 0x5d95, 0x5d8e, 0x1504, 0x5d96, 0x1508,
  0x5f45, 0x5f0a, 0x1533, 0x1534, 0x5efd, 0x5f17, 0x5f1c, 0x5f07,
  0x5f31, 0x5f32, 0x5f2c, 0x5f30, 0x5f03, 0x5f05, 0x15a2, 0x61c2,
  0x15a4, 0x61b8, 0x15a5, 0x61c1, 0x15a7, 0x15a6, 0x15e7, 0x62ab,
  0x62b7, 0x15ff, 0x15fe, 0x6315, 0x6314, 0x161d, 0x161c, 0x630c,
  0x6317, 0x1667, 0x6393, 0x1694, 0x63d2, 0x16c5, 0x16c8, 0x6436,
  0x6431, 0x6433, 0x643c, 0x642e, 0x643a, 0x16c9, 0x643d, 0x16c7,
  0x64b5, 0x6522, 0x6523, 0x6520, 0x651c, 0x651d, 0x176c, 0x65a0,
  0x178a, 0x65ef, 0x65e8, 0x65eb, 0x1788, 0x1787, 0x1786, 0x65e1,
  0x65e6, 0x17cf, 0x17d0, 0x66f8, 0x66f5, 0x181c, 0x1823, 0x6783,
  0x6794, 0x6784, 0x1849, 0x678b, 0x678f, 0x1843,
  /* 0x54 */
  0x678c, 0x1848, 0x6789, 0x1847, 0x678e, 0x1846, 0x183f, 0x1844,
  0x6924, 0x690f, 0x18be, 0x6913, 0x690a, 0x18c2, 0x18ba, 0x18bc,
  0x18c6, 0x692a, 0x691a, 0x18c8, 0x6927, 0x6916, 0x6921, 0x1923,
  0x6a85, 0x6aac, 0x6ac6, 0x6ac5, 0x6ad7, 0x6b53, 0x009d, 0x1d28,
  0x1d27, 0x1ddf, 0x0124, 0x1f35, 0x1fb3, 0x0207, 0x228a, 0x227d,
  0x2289, 0x0279, 0x24cd, 0x24d0, 0x0278, 0x272b, 0x2733, 0x2729,
  0x2735, 0x2731, 0x2737, 0x2836, 0x29be, 0x0419, 0x29b9, 0x041c,
  0x29bb, 0x0418, 0x2de2, 0x2ddb, 0x2ddd, 0x2ddc, 0x2dda, 0x85af,
  0x2dd9, 0x05bd, 0x065d, 0x30df, 0x065a, 0x065e, 0x30e1, 0x065c,
  0x30ee, 0x065b, 0x31b5, 0x32d4, 0x32d5, 0x0721, 0x32d0, 0x32d1,
  0x32ce, 0x32d7, 0x0720, 0x0732, 0x367d, 0x368a, 0x07f2, 0x36a7,
  0x07f5, 0x3699, 0x3682, 0x3688, 0x07ee, 0x07ec,
  /* 0x55 */
  0x3686, 0x07ea, 0x3698, 0x369d, 0x07ed, 0x07f3, 0x368f, 0x07f6,
  0x36aa, 0x0848, 0x375d, 0x0849, 0x380a, 0x0975, 0x3bd7, 0x3bd6,
  0x3be5, 0x096f, 0x097b, 0x0973, 0x3bd9, 0x3bda, 0x3bea, 0x0970,
  0x3bf6, 0x7d39, 0x0978, 0x3de3, 0x09fe, 0x3de9, 0x0a00, 0x3deb,
  0x3def, 0x3df3, 0x3dea, 0x0a01, 0x8621, 0x0a55, 0x0a56, 0x0a9d,
  0x3f71, 0x0af9, 0x40ae, 0x0aff, 0x40b3, 0x0afd, 0x40ac, 0x0b43,
  0x0b41, 0x4183, 0x4245, 0x424e, 0x4244, 0x42a3, 0x42a5, 0x43a6,
  0x43a4, 0x0c6f, 0x43a9, 0x43af, 0x0c8a, 0x0ce5, 0x0ce6, 0x44f0,
  0x44f8, 0x44f1, 0x0d7f, 0x4649, 0x0db5, 0x0db6, 0x0dbb, 0x46c2,
  0x46f2, 0x46f3, 0x47fa, 0x0e40, 0x47f6, 0x47fc, 0x4818, 0x4808,
  0x4812, 0x0e9d, 0x0e9c, 0x48db, 0x48da, 0x0f0f, 0x0f11, 0x0f0d,
  0x4a2c, 0x4a4d, 0x0f14, 0x0f13, 0x4b46, 0x4bf6,
  /* 0x56 */
  0x4c2b, 0x4c74, 0x4db8, 0x4dc8, 0x1082, 0x1083, 0x114d, 0x5192,
  0x5193, 0x114f, 0x517f, 0x51ab, 0x5197, 0x114c, 0x1151, 0x51ac,
  0x11ee, 0x11e8, 0x18cb, 0x53ce, 0x11eb, 0x53cd, 0x11e2, 0x11e6,
  0x53c1, 0x53b1, 0x53c7, 0x11ec, 0x5540, 0x1259, 0x553f, 0x5539,
  0x125d, 0x5543, 0x1257, 0x125b, 0x1256, 0x55ab, 0x12fe, 0x571f,
  0x5709, 0x570c, 0x1300, 0x1301, 0x5840, 0x1342, 0x5896, 0x1360,
  0x58f6, 0x58f7, 0x141d, 0x5a46, 0x5a4f, 0x143e, 0x1469, 0x1465,
  0x5b3d, 0x5b41, 0x5f66, 0x5f78, 0x5f5d, 0x5f69, 0x5f74, 0x5f7d,
  0x5f6e, 0x5f72, 0x5f73, 0x5f62, 0x5f48, 0x5f53, 0x5f5f, 0x5f68,
  0x1538, 0x5f7f, 0x5f6b, 0x15ae, 0x61c4, 0x15ad, 0x62af, 0x62ad,
  0x62b2, 0x1602, 0x161f, 0x631a, 0x631b, 0x1622, 0x1620, 0x86f5,
  0x166c, 0x639b, 0x639f, 0x1668, 0x166d, 0x166e,
  /* 0x57 */
  0x16a0, 0x16ce, 0x16d0, 0x16d1, 0x16cb, 0x6440, 0x16d2, 0x6447,
  0x16d3, 0x64b7, 0x1720, 0x174e, 0x174b, 0x1772, 0x1770, 0x65a2,
  0x1792, 0x178f, 0x6600, 0x65f3, 0x1790, 0x8437, 0x65f5, 0x17d9,
  0x17d5, 0x66bd, 0x6700, 0x6702, 0x17fa, 0x6734, 0x6749, 0x679f,
  0x184b, 0x67a3, 0x67cd, 0x6799, 0x679d, 0x18d0, 0x18ce, 0x6939,
  0x18cf, 0x6944, 0x18c4, 0x18cc, 0x6935, 0x18d2, 0x1935, 0x6aaf,
  0x0a03, 0x1d2f, 0x009e, 0x00af, 0x6b8e, 0x020c, 0x229f, 0x229b,
  0x229e, 0x2296, 0x2294, 0x22a0, 0x027c, 0x273b, 0x0330, 0x0331,
  0x273a, 0x29c1, 0x2b4d, 0x2b5d, 0x2df3, 0x05a1, 0x059e, 0x0668,
  0x0661, 0x30f6, 0x30e5, 0x30ea, 0x30e7, 0x3105, 0x0665, 0x30f9,
  0x0666, 0x066a, 0x06ab, 0x36ab, 0x36ed, 0x36b2, 0x36b0, 0x36b5,
  0x36be, 0x36c1, 0x36c8, 0x07f9, 0x36c0, 0x36bc,
  /* 0x58 */
  0x36b1, 0x36c4, 0x36bf, 0x0858, 0x088a, 0x3c08, 0x3c03, 0x3bfd,
  0x3c10, 0x3c02, 0x3c13, 0x0a04, 0x3dfa, 0x3e00, 0x40b9, 0x40bc,
  0x0b02, 0x425b, 0x4251, 0x424f, 0x42eb, 0x43b8, 0x0c79, 0x43b9,
  0x43c1, 0x43c0, 0x43be, 0x450b, 0x0ceb, 0x4507, 0x450a, 0x4508,
  0x0ce9, 0x450d, 0x4506, 0x4515, 0x45af, 0x0d20, 0x0d21, 0x0d81,
  0x46f5, 0x0e4d, 0x0e59, 0x482e, 0x0e58, 0x481b, 0x81d1, 0x481a,
  0x4824, 0x0ea5, 0x0ea9, 0x48e6, 0x48e3, 0x0f1a, 0x0f19, 0x4a5d,
  0x4a4f, 0x4a66, 0x4a5b, 0x4b47, 0x4bb4, 0x0f96, 0x0f98, 0x0f97,
  0x4bfa, 0x4c2e, 0x82df, 0x0fc8, 0x4dce, 0x1043, 0x1045, 0x4e19,
  0x1152, 0x1157, 0x51cc, 0x51b2, 0x1155, 0x51bb, 0x51c1, 0x1156,
  0x1158, 0x11f2, 0x53e9, 0x53ee, 0x53f0, 0x53d6, 0x540e, 0x53da,
  0x5548, 0x554a, 0x554e, 0x554d, 0x55b1, 0x55b0,
  /* 0x59 */
  0x55b3, 0x1307, 0x5738, 0x5732, 0x1308, 0x572d, 0x130a, 0x5734,
  0x0f1b, 0x5729, 0x5874, 0x1361, 0x1362, 0x5903, 0x13c2, 0x13c6,
  0x59a9, 0x5a58, 0x141e, 0x1425, 0x5abf, 0x5ac1, 0x5b4a, 0x5bac,
  0x14b0, 0x5c89, 0x5d3d, 0x5d3c, 0x5da9, 0x5fa0, 0x153d, 0x5f90,
  0x153e, 0x5f93, 0x5f8b, 0x5fad, 0x5fbb, 0x5fb8, 0x1546, 0x1545,
  0x5f9c, 0x61d8, 0x61d7, 0x1603, 0x1626, 0x1627, 0x635d, 0x63a9,
  0x63da, 0x1698, 0x16ad, 0x16d5, 0x16da, 0x6454, 0x16d9, 0x6455,
  0x644b, 0x16dd, 0x643f, 0x64b9, 0x1715, 0x1716, 0x1717, 0x1721,
  0x6538, 0x6536, 0x6540, 0x174c, 0x653b, 0x6539, 0x65a4, 0x1796,
  0x1798, 0x6608, 0x660c, 0x179b, 0x6610, 0x17ff, 0x6707, 0x1825,
  0x67d2, 0x184f, 0x67c2, 0x67bb, 0x67cc, 0x67cb, 0x1856, 0x1854,
  0x694d, 0x6963, 0x694e, 0x18d8, 0x6950, 0x6955,
  /* 0x5a */
  0x18d7, 0x695e, 0x1926, 0x6a90, 0x6ab2, 0x6ab1, 0x1938, 0x6aca,
  0x6b02, 0x6b27, 0x6b26, 0x198a, 0x22af, 0x24e0, 0x24dc, 0x0334,
  0x2739, 0x0335, 0x856a, 0x277c, 0x27f3, 0x8570, 0x03a1, 0x286b,
  0x29c4, 0x310b, 0x3108, 0x310a, 0x066c, 0x066d, 0x31dc, 0x0729,
  0x072a, 0x32e1, 0x32df, 0x36ce, 0x36d4, 0x36e3, 0x36d7, 0x36e2,
  0x0800, 0x0808, 0x0806, 0x0805, 0x36d8, 0x36d5, 0x36d2, 0x08b1,
  0x0988, 0x3c1e, 0x3c2c, 0x3c25, 0x3bf3, 0x3e04, 0x3e08, 0x3e15,
  0x0a09, 0x40c4, 0x40c9, 0x40c7, 0x40c8, 0x42a9, 0x43c6, 0x43c5,
  0x4518, 0x451a, 0x4520, 0x0d22, 0x4666, 0x4664, 0x466a, 0x0dd5,
  0x0e61, 0x0e5d, 0x0e62, 0x0e4f, 0x0e60, 0x4835, 0x4834, 0x0eaa,
  0x0f22, 0x4a6c, 0x0f21, 0x4a6e, 0x4a71, 0x1046, 0x4dd4, 0x4dd6,
  0x4e1a, 0x4e62, 0x4e65, 0x4e76, 0x51db, 0x51d6,
  /* 0x5b */
  0x1162, 0x51e7, 0x1160, 0x1164, 0x51f4, 0x86b6, 0x53fd, 0x53d5,
  0x5407, 0x11f6, 0x540f, 0x53f8, 0x86c1, 0x1219, 0x5587, 0x1291,
  0x55b5, 0x55f5, 0x130d, 0x573f, 0x5743, 0x574c, 0x1365, 0x590b,
  0x5a6b, 0x5a68, 0x5a70, 0x5a75, 0x5a77, 0x143f, 0x5ac3, 0x154b,
  0x5fe9, 0x5fea, 0x5fcb, 0x5fc5, 0x5fc6, 0x1548, 0x5fed, 0x5fd3,
  0x1552, 0x5fe5, 0x154a, 0x1551, 0x5fdb, 0x5feb, 0x5fe0, 0x5fc1,
  0x1550, 0x154c, 0x61dd, 0x15ee, 0x1604, 0x1606, 0x162d, 0x162e,
  0x162f, 0x167b, 0x1678, 0x1677, 0x63b2, 0x63b4, 0x63b1, 0x63b5,
  0x63f2, 0x16a2, 0x16a1, 0x16e3, 0x6456, 0x171a, 0x1719, 0x1757,
  0x6544, 0x179e, 0x6626, 0x661f, 0x6618, 0x6621, 0x6617, 0x17dd,
  0x6709, 0x1805, 0x1828, 0x67c5, 0x67df, 0x1860, 0x67e3, 0x1866,
  0x67e9, 0x67ee, 0x1867, 0x1868, 0x6966, 0x697a,
  /* 0x5c */
  0x18de, 0x696e, 0x6991, 0x6983, 0x6976, 0x697e, 0x696d, 0x18e1,
  0x6a95, 0x6ae3, 0x1969, 0x1977, 0x6b03, 0x6b04, 0x8719, 0x6b17,
  0x00a6, 0x1d36, 0x00a5, 0x1f36, 0x0214, 0x2742, 0x0336, 0x0338,
  0x2744, 0x2746, 0x277e, 0x29ca, 0x29c8, 0x29cc, 0x2af0, 0x0670,
  0x3185, 0x32e5, 0x32e7, 0x072b, 0x0811, 0x080a, 0x36f4, 0x080d,
  0x36e9, 0x0816, 0x0810, 0x0809, 0x080e, 0x087a, 0x3c3d, 0x098c,
  0x3c36, 0x0991, 0x3e16, 0x0a0a, 0x3e12, 0x3e0f, 0x3e17, 0x3e11,
  0x3e0b, 0x0a08, 0x0a0b, 0x40cd, 0x40d0, 0x40cc, 0x40ce, 0x40d1,
  0x0b07, 0x4189, 0x0cf2, 0x466f, 0x484b, 0x4844, 0x4855, 0x0eae,
  0x0f24, 0x0f26, 0x0f27, 0x4a7f, 0x5771, 0x0f99, 0x4c2f, 0x4c7a,
  0x4c7b, 0x4c7c, 0x115f, 0x116a, 0x1171, 0x51fc, 0x5210, 0x5202,
  0x116c, 0x116f, 0x51ee, 0x5203, 0x1168, 0x520d,
  /* 0x5d */
  0x5213, 0x5208, 0x520f, 0x5418, 0x5412, 0x1201, 0x1268, 0x5567,
  0x5565, 0x55bb, 0x5769, 0x5762, 0x1313, 0x576e, 0x1316, 0x5761,
  0x1318, 0x5764, 0x574d, 0x5851, 0x1389, 0x13c8, 0x5a83, 0x5ac6,
  0x1484, 0x601f, 0x1554, 0x6004, 0x6017, 0x6008, 0x6005, 0x1556,
  0x5ff3, 0x601e, 0x6002, 0x601a, 0x601b, 0x6027, 0x601c, 0x155a,
  0x62b5, 0x1605, 0x1607, 0x6333, 0x1631, 0x6334, 0x6331, 0x63b8,
  0x63ba, 0x16a3, 0x63fc, 0x16eb, 0x171c, 0x64c3, 0x175a, 0x654d,
  0x175b, 0x662f, 0x17a6, 0x17aa, 0x17a5, 0x66c9, 0x17e1, 0x66c8,
  0x66c4, 0x672a, 0x6738, 0x6750, 0x182a, 0x680a, 0x67fb, 0x6804,
  0x67fc, 0x67fe, 0x1872, 0x186f, 0x1873, 0x6802, 0x67f6, 0x681b,
  0x67f9, 0x6815, 0x6810, 0x67ff, 0x6800, 0x680c, 0x186b, 0x18e6,
  0x6995, 0x69a5, 0x18e9, 0x18ec, 0x18e8, 0x18f0,
  /* 0x5e */
  0x6a98, 0x6ac1, 0x198c, 0x6b5a, 0x1d64, 0x22bb, 0x0215, 0x24e6,
  0x2749, 0x27f7, 0x0371, 0x0426, 0x29d0, 0x04c6, 0x2bc2, 0x05a8,
  0x3111, 0x0673, 0x36ff, 0x36fe, 0x36fd, 0x0815, 0x3701, 0x0998,
  0x0997, 0x3c4b, 0x3c4d, 0x3c47, 0x40d3, 0x4268, 0x4267, 0x0bd7,
  0x0c80, 0x43d1, 0x4530, 0x4532, 0x452e, 0x0d88, 0x6b9d, 0x46c9,
  0x46c8, 0x0e69, 0x4856, 0x4851, 0x0e6b, 0x0f29, 0x0f28, 0x4a85,
  0x4a89, 0x4a8e, 0x4a84, 0x105f, 0x4e6a, 0x522b, 0x522f, 0x5228,
  0x1174, 0x5216, 0x5215, 0x521d, 0x541a, 0x1202, 0x126a, 0x1294,
  0x55bc, 0x5775, 0x577c, 0x138a, 0x5911, 0x5912, 0x5b5c, 0x5dbb,
  0x1564, 0x5ff4, 0x155e, 0x1561, 0x602d, 0x1565, 0x1566, 0x62e4,
  0x6337, 0x6336, 0x6367, 0x63be, 0x63bd, 0x63e2, 0x6468, 0x6466,
  0x64c8, 0x64ca, 0x64c7, 0x64dc, 0x175f, 0x654f,
  /* 0x5f */
  0x65a9, 0x663c, 0x17af, 0x663b, 0x66ce, 0x180d, 0x6714, 0x6753,
  0x187c, 0x682e, 0x187a, 0x681f, 0x1876, 0x1879, 0x187d, 0x1877,
  0x69b0, 0x69bd, 0x18f6, 0x18f1, 0x69ae, 0x69c4, 0x6a7b, 0x0c0b,
  0x1929, 0x6a9e, 0x196f, 0x6b05, 0x199a, 0x6b69, 0x6ba1, 0x22c7,
  0x231d, 0x274a, 0x29d3, 0x0469, 0x2b72, 0x2e02, 0x05ab, 0x2e35,
  0x3127, 0x311e, 0x311f, 0x072c, 0x072d, 0x3707, 0x3706, 0x0817,
  0x099a, 0x3c54, 0x3e1c, 0x3e20, 0x46f8, 0x0e6e, 0x485d, 0x4858,
  0x0f2c, 0x4a92, 0x4b4e, 0x0fca, 0x1178, 0x1206, 0x5427, 0x1207,
  0x5781, 0x5783, 0x1320, 0x5844, 0x1353, 0x13ce, 0x147a, 0x1479,
  0x6042, 0x604d, 0x6054, 0x604e, 0x156b, 0x6043, 0x1567, 0x156d,
  0x633c, 0x6340, 0x63c0, 0x1685, 0x16b0, 0x16f3, 0x1763, 0x655a,
  0x6651, 0x17b6, 0x66dd, 0x1882, 0x187f, 0x6838,
  /* 0x60 */
  0x1886, 0x6845, 0x683a, 0x1884, 0x6835, 0x18fc, 0x18fd, 0x18fa,
  0x6af1, 0x1987, 0x6b93, 0x1e9a, 0x021a, 0x0219, 0x5241, 0x29d7,
  0x0675, 0x3128, 0x081a, 0x081b, 0x0819, 0x3c53, 0x3c59, 0x099c,
  0x3e21, 0x0a10, 0x426f, 0x4537, 0x45b5, 0x4862, 0x485e, 0x48f5,
  0x117b, 0x117c, 0x523d, 0x1208, 0x542d, 0x5589, 0x578d, 0x5787,
  0x5790, 0x591a, 0x5a99, 0x1441, 0x14e3, 0x1572, 0x605f, 0x1573,
  0x1568, 0x6056, 0x6061, 0x605b, 0x605a, 0x605c, 0x6065, 0x1635,
  0x6341, 0x1688, 0x169d, 0x646e, 0x646c, 0x646d, 0x0e75, 0x65aa,
  0x665c, 0x6658, 0x66de, 0x188f, 0x684f, 0x6851, 0x188e, 0x6853,
  0x1905, 0x1904, 0x18ff, 0x69fc, 0x6b39, 0x199e, 0x1d3e, 0x0154,
  0x22d2, 0x0281, 0x274f, 0x3714, 0x0cfa, 0x4672, 0x4673, 0x0f32,
  0x1270, 0x126e, 0x5791, 0x86d6, 0x147c, 0x5dbf,
  /* 0x61 */
  0x1575, 0x606c, 0x1574, 0x1577, 0x62e6, 0x6345, 0x1637, 0x63c8,
  0x63e4, 0x655d, 0x17ba, 0x6721, 0x1811, 0x672c, 0x6757, 0x1892,
  0x1899, 0x685d, 0x6861, 0x6865, 0x6a08, 0x190a, 0x192a, 0x192b,
  0x1944, 0x1979, 0x6b45, 0x00aa, 0x0348, 0x2e05, 0x32ef, 0x371b,
  0x371d, 0x3e25, 0x3e24, 0x486d, 0x0eb4, 0x5242, 0x5249, 0x120d,
  0x5578, 0x558a, 0x5797, 0x1354, 0x589b, 0x591c, 0x1430, 0x5aa2,
  0x1609, 0x1638, 0x1636, 0x168b, 0x16f7, 0x1766, 0x17bd, 0x181e,
  0x686c, 0x1896, 0x686f, 0x190d, 0x6a0e, 0x1973, 0x6b08, 0x6b1d,
  0x6ba3, 0x033b, 0x033c, 0x2b60, 0x371c, 0x09a0, 0x0cfb, 0x6cfd,
  0x48f3, 0x1181, 0x579b, 0x5aa7, 0x5dc4, 0x1578, 0x607a, 0x168d,
  0x1773, 0x6661, 0x6663, 0x66d7, 0x6876, 0x19a6, 0x6ba5, 0x05ad,
  0x3c67, 0x0a11, 0x3eab, 0x524a, 0x557d, 0x579d,
  /* 0x62 */
  0x5853, 0x5b65, 0x607b, 0x1639, 0x64cd, 0x64dd, 0x17bf, 0x6730,
  0x6a16, 0x190f, 0x19a7, 0x19b5, 0x0bdc, 0x1431, 0x62e7, 0x6a18,
  0x6aa2, 0x19a8, 0x6b7c, 0x0d25, 0x4a9e, 0x6084, 0x17c1, 0x6a1c,
  0x0d90, 0x4871, 0x63ca, 0x1296, 0x147f, 0x1910, 0x6aa3, 0x160a,
  0x687b, 0x6b97, 0x1912, 0x163a, 0x6350, 0x163b,
};
static const unsigned short cns11643_3_2uni_page64[292] = {
  /* 0x64 */
  0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd,
  0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd,
  0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd,
  0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd,
  0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x6cfd, 0x1b66,
  0x1b68, 0x1be7, 0x1c3f, 0x6cfd, 0x1ca6, 0x1d0f, 0x1e3e, 0x1f24,
  0x1f65, 0x1f9b, 0x1d7f, 0x20cb, 0x2173, 0x2171, 0x216b, 0x21f4,
  0x2222, 0x2220, 0x2292, 0x22ba, 0x2291, 0x22b0, 0x2359, 0x238a,
  0x240f, 0x2412, 0x2413, 0x2447, 0x249b, 0x2500, 0x254d, 0x26d1,
  0x26d3, 0x2767, 0x2857, 0x2877, 0x28d5, 0x2975, 0x298e, 0x29a5,
  0x29b6, 0x29bf, 0x2a65, 0x2acd, 0x2aed, 0x2b94, 0x2b9a, 0x2bba,
  0x2d25, 0x2d50, 0x2ea3, 0x2f60, 0x2f64, 0x2fb6,
  /* 0x65 */
  0x3003, 0x30b6, 0x311a, 0x4625, 0x2821, 0x32e2, 0x3302, 0x33a4,
  0x33ac, 0x3410, 0x3406, 0x345e, 0x345a, 0x352c, 0x3529, 0x362d,
  0x3677, 0x367a, 0x36ca, 0x36e6, 0x36f5, 0x370d, 0x370e, 0x37dc,
  0x37dd, 0x37f6, 0x381e, 0x3863, 0x39a5, 0x3a0f, 0x3a8a, 0x3a84,
  0x3a8b, 0x3a7c, 0x3b4c, 0x3b48, 0x3b49, 0x3b9d, 0x3b99, 0x3bf8,
  0x3c2e, 0x3c2d, 0x3c5c, 0x45cc, 0x3cbf, 0x3cea, 0x3ce5, 0x3d11,
  0x3d12, 0x3d3f, 0x3d39, 0x3d3b, 0x3d3d, 0x3d77, 0x3d75, 0x3d76,
  0x3d71, 0x3d96, 0x3d93, 0x3db4, 0x3ddd, 0x3dde, 0x3e0e, 0x2511,
  0x3e18, 0x3f47, 0x3f48, 0x3fef, 0x4012, 0x403b, 0x40a4, 0x408d,
  0x40b4, 0x4273, 0x4277, 0x42bc, 0x4419, 0x441b, 0x443d, 0x4453,
  0x4454, 0x4458, 0x44b7, 0x44d8, 0x44ee, 0x4522, 0x454d, 0x4586,
  0x4599, 0x45a3, 0x45bc, 0x46a7, 0x4737, 0x4759,
  /* 0x66 */
  0x47d0, 0x482f, 0x4832, 0x4842, 0x484e, 0x4868, 0x48a9, 0x48ed,
  0x49d0, 0x4a07, 0x49d3, 0x4a64, 0x4b40, 0x6cfd, 0x4c41, 0x4c63,
  0x4cbb, 0x3311, 0x3325, 0x4e48, 0x4f10, 0x4f62, 0x4f12, 0x5021,
  0x501e, 0x50e2, 0x50de, 0x50e1, 0x5173, 0x51d4, 0x51f5, 0x5237,
  0x5245, 0x5272, 0x534a, 0x53a9, 0x53a5, 0x53f5, 0x5434, 0x5450,
  0x5487, 0x5554, 0x5584, 0x5703, 0x5852, 0x58d8, 0x590c, 0x5918,
  0x59b0, 0x5abc, 0x5ad5, 0x5baa, 0x5c9c, 0x6cfd, 0x5d5c, 0x5e2b,
  0x5e21, 0x5e73, 0x5ef4, 0x5ef5, 0x5f3f, 0x5f42, 0x5f86, 0x5fbe,
  0x5fbc, 0x5fbd, 0x5ff1, 0x5ff2, 0x5fef, 0x6022, 0x6023, 0x6024,
  0x6067, 0x6066, 0x6197, 0x61ce, 0x61e7, 0x633b, 0x634d, 0x64e4,
  0x6542, 0x671d, 0x6798, 0x6cfd, 0x6949, 0x3049, 0x2a71, 0x2a85,
  0x2dd3, 0x650e, 0x4c02, 0x441e, 0x6cfd, 0x6cfd,
  /* 0x67 */
  0x2128, 0x2172, 0x21ba, 0x21f0, 0x21ee, 0x22b8, 0x22b9, 0x22c4,
  0x4c53, 0x5eb0,
};

static const ucs4_t cns11643_3_2uni_upages[136] = {
  0x03400, 0x03500, 0x03600, 0x03700, 0x03800, 0x03900, 0x03a00, 0x03b00,
  0x03c00, 0x03d00, 0x03e00, 0x03f00, 0x04000, 0x04100, 0x04200, 0x04300,
  0x04400, 0x04500, 0x04600, 0x04700, 0x04800, 0x04900, 0x04a00, 0x04b00,
  0x04c00, 0x04d00, 0x04e00, 0x04f00, 0x05000, 0x05100, 0x05200, 0x05300,
  0x05400, 0x05500, 0x05600, 0x05700, 0x05800, 0x05900, 0x05a00, 0x05b00,
  0x05c00, 0x05d00, 0x05e00, 0x05f00, 0x06000, 0x06100, 0x06200, 0x06300,
  0x06400, 0x06500, 0x06600, 0x06700, 0x06800, 0x06900, 0x06a00, 0x06b00,
  0x06c00, 0x06d00, 0x06e00, 0x06f00, 0x07000, 0x07100, 0x07200, 0x07300,
  0x07400, 0x07500, 0x07600, 0x07700, 0x07800, 0x07900, 0x07a00, 0x07b00,
  0x07c00, 0x07d00, 0x07e00, 0x07f00, 0x08000, 0x08100, 0x08200, 0x08300,
  0x08400, 0x08500, 0x08600, 0x08700, 0x08800, 0x08900, 0x08a00, 0x08b00,
  0x08c00, 0x08d00, 0x08e00, 0x08f00, 0x09000, 0x09100, 0x09200, 0x09300,
  0x09400, 0x09500, 0x09600, 0x09700, 0x09800, 0x09900, 0x09a00, 0x09b00,
  0x09c00, 0x09d00, 0x09e00, 0x09f00, 0x0ff00, 0x20000, 0x20100, 0x20200,
  0x20500, 0x20600, 0x20b00, 0x20d00, 0x21300, 0x21600, 0x21700, 0x21d00,
  0x22300, 0x22500, 0x23000, 0x23500, 0x23c00, 0x24000, 0x24a00, 0x25100,
  0x25900, 0x25c00, 0x26500, 0x28c00, 0x29900, 0x2f800, 0x2f900, 0x2fa00,
};

static int
cns11643_3_mbtowc (conv_t conv, ucs4_t *pwc, const unsigned char *s, size_t n)
{
  unsigned char c1 = s[0];
  if ((c1 >= 0x21 && c1 <= 0x62) || (c1 >= 0x64 && c1 <= 0x67)) {
    if (n >= 2) {
      unsigned char c2 = s[1];
      if (c2 >= 0x21 && c2 < 0x7f) {
        unsigned int i = 94 * (c1 - 0x21) + (c2 - 0x21);
        ucs4_t wc = 0xfffd;
        unsigned short swc;
        if (i < 6298) {
          if (i < 6148)
            swc = cns11643_3_2uni_page21[i],
            wc = cns11643_3_2uni_upages[swc>>8] | (swc & 0xff);
        } else {
          if (i < 6590)
            swc = cns11643_3_2uni_page64[i-6298],
            wc = cns11643_3_2uni_upages[swc>>8] | (swc & 0xff);
        }
        if (wc != 0xfffd) {
          *pwc = wc;
          return 2;
        }
      }
      return RET_ILSEQ;
    }
    return RET_TOOFEW(0);
  }
  return RET_ILSEQ;
}

