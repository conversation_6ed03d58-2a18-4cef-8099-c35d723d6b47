#!/bin/bash -e
# Builds SOCI for use with Valgrind in CI builds
#
# Copyright (c) 2013 <PERSON><PERSON><PERSON> <<EMAIL>>
# Copyright (c) 2015 <PERSON> <<EMAIL>>
# Copyright (c) 2021 V<PERSON><PERSON> <<EMAIL>>
#
source ${SOCI_SOURCE_DIR}/scripts/ci/common.sh

valgrind --leak-check=full --suppressions=${SOCI_SOURCE_DIR}/valgrind.suppress --error-exitcode=1 --trace-children=yes ctest -V --output-on-failure "$@" .
