  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_0,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_2,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_4,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_6,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_7,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_9,
  (int)(long)&((struct stringpool2_t *)0)->stringpool_extra_11,
