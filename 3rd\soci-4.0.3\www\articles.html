<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <meta content="text/html; charset=ISO-8859-1" http-equiv="content-type" />
    <link rel="stylesheet" type="text/css" href="style.css" />
    <title>SOCI - articles</title>
</head>
<body>
<table class="banner" cellpadding="0" cellspacing="0">
      <tr>
        <td class="banner_left">
          SOCI - The C++ Database Access Library
        </td>
      </tr>
</table>

<table class="main">
      <tr>
        <td class="main_navigator">
          <p><a href="index.html">Home</a><br />
          <a href="http://sourceforge.net/project/showfiles.php?group_id=121480" target="_blank">Download</a><br />
          <a href="doc/index.html">Documentation</a><br />
          Articles<br />
          <a href="people.html">People</a><br />
          <a href="events.html">Events</a><br />
          <a href="links.html">Links</a><br />
          </p>
          <a href="http://sourceforge.net"><img src="http://sflogo.sourceforge.net/sflogo.php?group_id=121480&amp;type=1" width="88" height="31" border="0" alt="SourceForge.net Logo" /></a>
        </td>
        <td class="main_text">
<p>The following articles were published about SOCI:</p>
<ul>
<li><a href="http://ddj.com/dept/cpp/188700800" target="_blank">Supporting Custom C++ Types</a> by Stephen Hutton, in Dr. Dobb's Journal (June 2006).</li>
<li><a href="http://www.ddj.com/184405930" target="_blank">A Simple Oracle Call Interface</a> by Maciej Sobczak, in Dr. Dobb's Journal (October 2004).</li>
</ul>

<p>as well as presentations:</p>
<ul>
    <li><a href="http://slid.es/mloskot/soci" target="_blank">Introduction To A Simple C++ Database Access Library</a> by Mateusz Loskot at London C++ Meeting (May 2013).</li>
</ul>
      </td>
    </tr>
</table>

<a href="http://github.com/SOCI"><img style="position: absolute; top: 0; right: 0; border: 0;" src="forkus_github.png" alt="Fork us on GitHub"></a>
</body>
</html>
