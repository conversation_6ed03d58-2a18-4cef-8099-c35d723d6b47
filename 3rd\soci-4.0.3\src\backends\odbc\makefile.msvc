# The following variable is specific to this backend and its correct
# values might depend on your environment - feel free to set it accordingly.

ODBCINCLUDEDIR="C:\Program Files\Microsoft Platform SDK\Include"

# The rest of the Makefile is indepentent of the target environment.

COMPILER = cl
CXXFLAGS = /nologo /EHsc /D_CRT_SECURE_NO_DEPRECATE
INCLUDEDIRS = /I..\..\core /I$(ODBCINCLUDEDIR)

OBJECTS = blob.obj factory.obj row-id.obj session.obj standard-into-type.obj \
	standard-use-type.obj statement.obj vector-into-type.obj \
	vector-use-type.obj

soci-odbc.lib : $(OBJECTS)
	lib /NOLOGO /OUT:$@ $?
	del *.obj

blob.obj : blob.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

factory.obj : factory.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

row-id.obj : row-id.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

session.obj : session.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

standard-into-type.obj : standard-into-type.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

standard-use-type.obj : standard-use-type.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

statement.obj : statement.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

vector-into-type.obj : vector-into-type.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

vector-use-type.obj : vector-use-type.cpp
	$(COMPILER) /c $? $(CXXFLAGS) $(INCLUDEDIRS)

clean :
	del soci-odbc.lib soci-odbc.dll
